# Blast-Radius Infrastructure

This directory contains all infrastructure-as-code (IaC) configurations for the Blast-Radius Security Tool, implementing the Phase 2 PRD requirements for robustness, deployment excellence, and security hardening.

## 🏗️ **Infrastructure Overview**

### **Directory Structure**
```
infrastructure/
├── terraform/                 # Infrastructure as Code
│   ├── modules/               # Reusable Terraform modules
│   │   ├── networking/        # VPC, subnets, security groups
│   │   ├── security/          # KMS, WAF, CloudTrail
│   │   ├── compute/           # EKS, EC2, Auto Scaling
│   │   └── storage/           # RDS, ElastiCache, S3
│   ├── environments/          # Environment-specific configs
│   │   ├── dev/
│   │   ├── staging/
│   │   └── production/
│   └── shared/                # Shared resources
├── kubernetes/                # Kubernetes manifests
│   ├── base/                  # Base configurations
│   ├── overlays/              # Environment overlays
│   └── operators/             # Custom operators
├── helm/                      # Helm charts
│   ├── blast-radius/          # Main application chart
│   ├── monitoring/            # Monitoring stack
│   └── security/              # Security tools
└── monitoring/                # Observability configs
    ├── prometheus/            # Prometheus rules
    ├── grafana/               # Grafana dashboards
    └── jaeger/                # Distributed tracing
```

## 🚀 **Quick Start**

### **Prerequisites**
- Terraform >= 1.0
- kubectl >= 1.28
- Helm >= 3.12
- AWS CLI configured
- Docker for local testing

### **Deploy Infrastructure**
```bash
# 1. Initialize Terraform
cd terraform/environments/dev
terraform init

# 2. Plan deployment
terraform plan -var-file="terraform.tfvars"

# 3. Apply infrastructure
terraform apply -var-file="terraform.tfvars"

# 4. Deploy application
cd ../../../helm
helm install blast-radius ./blast-radius \
  --namespace blast-radius \
  --create-namespace \
  --values values-dev.yaml
```

## 🛡️ **Security Features**

### **Zero-Trust Architecture**
- Network micro-segmentation
- Pod-to-pod encryption
- Identity-based access control
- Continuous security monitoring

### **Compliance Controls**
- SOC 2 Type II ready
- GDPR compliance features
- Audit logging and retention
- Data encryption at rest/transit

### **Security Scanning**
- Container vulnerability scanning
- Infrastructure security checks
- Secrets management
- Network policy enforcement

## 📊 **Monitoring & Observability**

### **Metrics Collection**
- Application performance metrics
- Infrastructure health monitoring
- Security event correlation
- Business logic monitoring

### **Alerting Framework**
- Multi-tier alerting (Critical/Warning/Info)
- Intelligent alert correlation
- Escalation policies
- Incident response automation

### **Dashboards**
- Executive KPI dashboards
- Operations monitoring
- Security posture tracking
- Performance analytics

## 🔧 **Deployment Strategies**

### **Blue-Green Deployment**
- Zero-downtime deployments
- Automatic rollback capabilities
- Traffic shifting controls
- Health check validation

### **Canary Releases**
- Gradual traffic migration
- A/B testing support
- Risk mitigation
- Performance validation

### **GitOps Workflow**
- Infrastructure as Code
- Automated deployments
- Configuration drift detection
- Audit trail maintenance

## 🏥 **Disaster Recovery**

### **Backup Strategy**
- Automated daily backups
- Cross-region replication
- Point-in-time recovery
- Backup validation testing

### **High Availability**
- Multi-AZ deployment
- Auto-scaling capabilities
- Load balancing
- Failover automation

### **Recovery Procedures**
- RTO: < 4 hours
- RPO: < 1 hour
- Automated recovery testing
- Documented runbooks

## 📋 **Environment Management**

### **Development**
- Single-node clusters
- Reduced resource allocation
- Debug-friendly configuration
- Fast iteration cycles

### **Staging**
- Production-like environment
- Full feature testing
- Performance validation
- Security testing

### **Production**
- High availability setup
- Enhanced monitoring
- Strict security controls
- Compliance enforcement

## 🔍 **Troubleshooting**

### **Common Issues**
1. **Pod startup failures**: Check resource limits and dependencies
2. **Network connectivity**: Verify security groups and network policies
3. **Storage issues**: Check PVC status and storage class
4. **Performance problems**: Review resource utilization and scaling

### **Debugging Tools**
- kubectl logs and describe
- Prometheus metrics
- Grafana dashboards
- Jaeger tracing

### **Support Contacts**
- Infrastructure Team: <EMAIL>
- Security Team: <EMAIL>
- On-call: +1-555-ONCALL

## 📚 **Documentation Links**

- [Terraform Modules Documentation](terraform/modules/README.md)
- [Kubernetes Deployment Guide](kubernetes/README.md)
- [Helm Chart Configuration](helm/blast-radius/README.md)
- [Monitoring Setup Guide](monitoring/README.md)
- [Security Hardening Guide](../docs/security-hardening.md)
- [Disaster Recovery Procedures](../docs/disaster-recovery.md)

## 🎯 **Performance Targets**

### **Availability**
- 99.9% uptime SLA
- < 30 seconds recovery time
- Zero data loss guarantee

### **Performance**
- < 500ms API response time (95th percentile)
- < 30 seconds attack path analysis
- 10,000+ concurrent users support

### **Security**
- < 24 hours vulnerability remediation
- 100% encrypted communications
- Real-time threat detection

---

**Last Updated**: 2025-06-13  
**Version**: 1.0.0  
**Maintained By**: Infrastructure & Security Teams
