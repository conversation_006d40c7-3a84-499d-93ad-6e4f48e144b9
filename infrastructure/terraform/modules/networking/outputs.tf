# Blast-Radius Security Tool - Networking Module Outputs

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.blast_radius.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.blast_radius.cidr_block
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.blast_radius.arn
}

# Internet Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.blast_radius.id
}

# Subnet Outputs
output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_cidrs" {
  description = "CIDR blocks of the public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "private_subnet_cidrs" {
  description = "CIDR blocks of the private subnets"
  value       = aws_subnet.private[*].cidr_block
}

output "database_subnet_ids" {
  description = "IDs of the database subnets"
  value       = aws_subnet.database[*].id
}

output "database_subnet_cidrs" {
  description = "CIDR blocks of the database subnets"
  value       = aws_subnet.database[*].cidr_block
}

# NAT Gateway Outputs
output "nat_gateway_ids" {
  description = "IDs of the NAT Gateways"
  value       = var.enable_nat_gateway ? aws_nat_gateway.blast_radius[*].id : []
}

output "nat_gateway_public_ips" {
  description = "Public IPs of the NAT Gateways"
  value       = var.enable_nat_gateway ? aws_eip.nat[*].public_ip : []
}

# Route Table Outputs
output "public_route_table_id" {
  description = "ID of the public route table"
  value       = aws_route_table.public.id
}

output "private_route_table_ids" {
  description = "IDs of the private route tables"
  value       = aws_route_table.private[*].id
}

output "database_route_table_id" {
  description = "ID of the database route table"
  value       = aws_route_table.database.id
}

# Security Group Outputs (for reference)
output "default_security_group_id" {
  description = "ID of the default security group"
  value       = aws_vpc.blast_radius.default_security_group_id
}

# Availability Zone Outputs
output "availability_zones" {
  description = "List of availability zones used"
  value       = data.aws_availability_zones.available.names
}

# Flow Logs Outputs
output "flow_log_id" {
  description = "ID of the VPC Flow Log"
  value       = var.enable_flow_logs ? aws_flow_log.blast_radius[0].id : null
}

output "flow_log_cloudwatch_log_group" {
  description = "CloudWatch Log Group for VPC Flow Logs"
  value       = var.enable_flow_logs ? aws_cloudwatch_log_group.vpc_flow_log[0].name : null
}

# Network ACL Outputs
output "private_network_acl_id" {
  description = "ID of the private network ACL"
  value       = aws_network_acl.private.id
}

output "database_network_acl_id" {
  description = "ID of the database network ACL"
  value       = aws_network_acl.database.id
}

# Subnet Group Outputs (for RDS)
output "database_subnet_group_name" {
  description = "Name for RDS subnet group"
  value       = "${var.environment}-blast-radius-db-subnet-group"
}

# EKS Cluster Outputs
output "eks_cluster_subnet_ids" {
  description = "Subnet IDs for EKS cluster (private subnets)"
  value       = aws_subnet.private[*].id
}

output "eks_nodegroup_subnet_ids" {
  description = "Subnet IDs for EKS node groups (private subnets)"
  value       = aws_subnet.private[*].id
}

# Load Balancer Outputs
output "alb_subnet_ids" {
  description = "Subnet IDs for Application Load Balancer (public subnets)"
  value       = aws_subnet.public[*].id
}

output "nlb_subnet_ids" {
  description = "Subnet IDs for Network Load Balancer (public subnets)"
  value       = aws_subnet.public[*].id
}

# DNS Outputs
output "vpc_dns_hostnames_enabled" {
  description = "Whether DNS hostnames are enabled in the VPC"
  value       = aws_vpc.blast_radius.enable_dns_hostnames
}

output "vpc_dns_support_enabled" {
  description = "Whether DNS support is enabled in the VPC"
  value       = aws_vpc.blast_radius.enable_dns_support
}

# Tagging Outputs
output "common_tags" {
  description = "Common tags applied to resources"
  value       = var.common_tags
}

# Regional Information
output "aws_region" {
  description = "AWS region"
  value       = data.aws_region.current.name
}

# CIDR Calculation Outputs
output "vpc_cidr_newbits" {
  description = "Number of additional bits for subnet CIDR calculation"
  value       = 8
}

output "max_subnets_per_type" {
  description = "Maximum number of subnets per type based on CIDR"
  value       = pow(2, 8) - (var.public_subnet_count + var.private_subnet_count + var.database_subnet_count)
}

# Security and Compliance Outputs
output "compliance_framework" {
  description = "Compliance framework applied"
  value       = var.compliance_framework
}

output "data_classification" {
  description = "Data classification level"
  value       = var.data_classification
}

# Cost Allocation Outputs
output "cost_center" {
  description = "Cost center for billing allocation"
  value       = var.cost_center
}

# Network Configuration Summary
output "network_configuration" {
  description = "Summary of network configuration"
  value = {
    vpc_cidr                = aws_vpc.blast_radius.cidr_block
    public_subnets         = length(aws_subnet.public)
    private_subnets        = length(aws_subnet.private)
    database_subnets       = length(aws_subnet.database)
    nat_gateways           = var.enable_nat_gateway ? length(aws_nat_gateway.blast_radius) : 0
    availability_zones     = length(data.aws_availability_zones.available.names)
    flow_logs_enabled      = var.enable_flow_logs
    network_acls_enabled   = var.enable_network_acls
  }
}
