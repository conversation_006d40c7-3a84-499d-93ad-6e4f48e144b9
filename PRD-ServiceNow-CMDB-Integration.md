# ServiceNow CMDB Integration PRD

**Product Requirements Document**
**Version:** 1.0.0
**Date:** December 13, 2025
**Priority:** P2 (Phase 4)
**Status:** Planning

## 🎯 Executive Summary

### Vision Statement
Establish seamless bi-directional integration between the Blast-Radius Security Tool and ServiceNow Configuration Management Database (CMDB) to provide unified asset lifecycle management, automated change tracking, and enhanced security context for enterprise IT operations.

### Business Objectives
- **Unified Asset Management**: Single source of truth for asset data across security and IT operations
- **Automated Change Tracking**: Real-time synchronization of asset changes and security implications
- **Enhanced Incident Response**: Leverage CMDB relationships for faster incident resolution
- **Compliance Automation**: Automated compliance reporting with CMDB data integration
- **Operational Efficiency**: Reduce manual data entry and synchronization overhead by 80%

### Success Metrics
- **Sync Accuracy**: 99.9% data consistency between systems
- **Sync Performance**: <1 minute for critical asset updates, <5 minutes for bulk operations
- **Data Coverage**: 95% of CMDB assets enriched with security context
- **Operational Impact**: 80% reduction in manual asset management tasks
- **Incident Response**: 50% faster mean time to resolution with enhanced asset context

## 🏗️ Technical Architecture

### Integration Patterns
```mermaid
graph TB
    subgraph "Blast-Radius Security Tool"
        A[Asset Service] --> B[CMDB Sync Service]
        B --> C[Graph Engine]
        C --> D[Attack Path Analyzer]
        E[Real-time Monitor] --> B
    end

    subgraph "ServiceNow Platform"
        F[CMDB API] --> G[Configuration Items]
        G --> H[Relationships]
        H --> I[Change Records]
        I --> J[Incident Management]
    end

    subgraph "Integration Layer"
        K[Bi-directional Sync Engine]
        L[Data Transformation]
        M[Conflict Resolution]
        N[Event Processing]
    end

    B <--> K
    F <--> K
    K --> L
    L --> M
    M --> N
```

### Core Components

#### 1. ServiceNow CMDB Sync Service
**File:** `backend/app/services/servicenow_cmdb_service.py`

```python
"""
ServiceNow CMDB Integration Service

Provides bi-directional synchronization between Blast-Radius assets and ServiceNow CMDB,
following PEP 8, PEP 257, and PEP 484 standards with comprehensive type hints and docstrings.
"""

from typing import Dict, List, Optional, Union, Any, Tuple, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import logging
import hashlib
import json

import networkx as nx
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator

from app.core.config import settings
from app.db.models.asset import Asset, AssetRelationship, RelationshipType
from app.services.graph_engine import GraphEngine


class SyncDirection(str, Enum):
    """Synchronization direction enumeration."""

    BIDIRECTIONAL = "bidirectional"
    TO_SERVICENOW = "to_servicenow"
    FROM_SERVICENOW = "from_servicenow"


class ConflictResolutionStrategy(str, Enum):
    """Conflict resolution strategy enumeration."""

    BLAST_RADIUS_WINS = "blast_radius_wins"
    SERVICENOW_WINS = "servicenow_wins"
    MANUAL_REVIEW = "manual_review"
    MERGE_FIELDS = "merge_fields"
    TIMESTAMP_BASED = "timestamp_based"


@dataclass
class CMDBSyncConfiguration:
    """Configuration for CMDB synchronization operations."""

    sync_direction: SyncDirection = SyncDirection.BIDIRECTIONAL
    conflict_resolution: ConflictResolutionStrategy = ConflictResolutionStrategy.TIMESTAMP_BASED
    sync_interval_minutes: int = 15
    batch_size: int = 100
    max_retries: int = 3
    timeout_seconds: int = 30

    # Field mappings
    asset_type_mappings: Dict[str, str] = field(default_factory=dict)
    relationship_type_mappings: Dict[str, str] = field(default_factory=dict)
    custom_field_mappings: Dict[str, str] = field(default_factory=dict)

    # Sync filters
    include_asset_types: Optional[List[str]] = None
    exclude_asset_types: Optional[List[str]] = None
    include_environments: Optional[List[str]] = None
    exclude_environments: Optional[List[str]] = None


class ServiceNowCMDBService:
    """
    ServiceNow CMDB integration service providing bi-directional synchronization.

    This service manages the synchronization of asset data between the Blast-Radius
    Security Tool and ServiceNow CMDB, ensuring data consistency and providing
    enhanced security context for IT operations.

    Attributes:
        db: Database session for local operations
        graph_engine: NetworkX-based graph engine for relationship analysis
        config: Synchronization configuration settings
        logger: Logging instance for operation tracking
    """

    def __init__(
        self,
        db: Session,
        graph_engine: GraphEngine,
        config: Optional[CMDBSyncConfiguration] = None
    ) -> None:
        """
        Initialize the ServiceNow CMDB service.

        Args:
            db: SQLAlchemy database session
            graph_engine: Graph engine for relationship processing
            config: Optional synchronization configuration

        Raises:
            ValueError: If required configuration is missing
            ConnectionError: If ServiceNow connection cannot be established
        """
        self.db = db
        self.graph_engine = graph_engine
        self.config = config or CMDBSyncConfiguration()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # ServiceNow client initialization
        self._servicenow_client: Optional[ServiceNowClient] = None
        self._sync_metrics: Dict[str, Any] = {
            "last_sync": None,
            "assets_synced": 0,
            "relationships_synced": 0,
            "conflicts_resolved": 0,
            "errors": 0
        }

        # NetworkX graph for CMDB relationship analysis
        self._cmdb_graph: nx.DiGraph = nx.DiGraph()

        self.logger.info("ServiceNow CMDB service initialized")
```

#### 2. Data Models and Schemas
**File:** `backend/app/schemas/servicenow_cmdb.py`

```python
"""
ServiceNow CMDB data models and schemas.

Defines Pydantic models for ServiceNow CMDB integration with comprehensive
validation and type safety following PEP 484 standards.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, validator
from enum import Enum


class CMDBAssetClass(str, Enum):
    """ServiceNow CMDB asset class enumeration."""

    COMPUTER = "cmdb_ci_computer"
    SERVER = "cmdb_ci_server"
    DATABASE = "cmdb_ci_database"
    APPLICATION = "cmdb_ci_appl"
    NETWORK_GEAR = "cmdb_ci_netgear"
    STORAGE = "cmdb_ci_storage"
    CLOUD_SERVICE = "cmdb_ci_cloud_service"
    CONTAINER = "cmdb_ci_container"


class CMDBRelationshipType(str, Enum):
    """ServiceNow CMDB relationship type enumeration."""

    DEPENDS_ON = "Depends on::Used by"
    CONNECTS_TO = "Connects to::Connected by"
    CONTAINS = "Contains::Contained by"
    RUNS_ON = "Runs on::Runs"
    MANAGES = "Manages::Managed by"
    MONITORS = "Monitors::Monitored by"


class ServiceNowAsset(BaseModel):
    """ServiceNow CMDB Configuration Item model."""

    sys_id: str = Field(..., description="ServiceNow system ID")
    name: str = Field(..., min_length=1, max_length=255, description="Asset name")
    sys_class_name: CMDBAssetClass = Field(..., description="CMDB asset class")

    # Basic properties
    operational_status: Optional[str] = Field(None, description="Operational status")
    install_status: Optional[str] = Field(None, description="Installation status")
    environment: Optional[str] = Field(None, description="Environment")
    location: Optional[str] = Field(None, description="Physical/logical location")

    # Technical details
    ip_address: Optional[str] = Field(None, description="Primary IP address")
    fqdn: Optional[str] = Field(None, description="Fully qualified domain name")
    serial_number: Optional[str] = Field(None, description="Serial number")
    asset_tag: Optional[str] = Field(None, description="Asset tag")

    # Ownership and management
    owned_by: Optional[str] = Field(None, description="Asset owner")
    managed_by: Optional[str] = Field(None, description="Managing entity")
    support_group: Optional[str] = Field(None, description="Support group")

    # Timestamps
    sys_created_on: Optional[datetime] = Field(None, description="Creation timestamp")
    sys_updated_on: Optional[datetime] = Field(None, description="Last update timestamp")

    # Custom attributes
    attributes: Dict[str, Any] = Field(default_factory=dict, description="Additional attributes")

    @validator('ip_address')
    def validate_ip_address(cls, v: Optional[str]) -> Optional[str]:
        """Validate IP address format."""
        if v is not None:
            import ipaddress
            try:
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError(f"Invalid IP address: {v}")
        return v


class ServiceNowRelationship(BaseModel):
    """ServiceNow CMDB relationship model."""

    sys_id: str = Field(..., description="Relationship system ID")
    parent: str = Field(..., description="Parent CI sys_id")
    child: str = Field(..., description="Child CI sys_id")
    type: CMDBRelationshipType = Field(..., description="Relationship type")

    # Relationship properties
    connection_strength: Optional[float] = Field(None, ge=0.0, le=1.0, description="Connection strength")
    port: Optional[int] = Field(None, ge=1, le=65535, description="Network port")
    protocol: Optional[str] = Field(None, description="Communication protocol")

    # Metadata
    sys_created_on: Optional[datetime] = Field(None, description="Creation timestamp")
    sys_updated_on: Optional[datetime] = Field(None, description="Last update timestamp")
```

### 3. Integration Workflows

#### Bi-directional Synchronization Flow
```python
async def perform_bidirectional_sync(self) -> SyncResult:
    """
    Perform comprehensive bi-directional synchronization.

    This method orchestrates the complete synchronization process between
    Blast-Radius and ServiceNow CMDB, handling conflicts and maintaining
    data integrity using NetworkX for relationship analysis.

    Returns:
        SyncResult: Comprehensive synchronization results with metrics

    Raises:
        SyncError: If synchronization fails critically
        NetworkError: If ServiceNow connection is lost
    """
    sync_start = datetime.utcnow()
    self.logger.info("Starting bi-directional CMDB synchronization")

    try:
        # Phase 1: Asset synchronization
        asset_sync_result = await self._sync_assets_bidirectional()

        # Phase 2: Relationship synchronization with NetworkX analysis
        relationship_sync_result = await self._sync_relationships_with_graph_analysis()

        # Phase 3: Conflict resolution
        conflict_resolution_result = await self._resolve_sync_conflicts()

        # Phase 4: Graph consistency validation
        graph_validation_result = await self._validate_graph_consistency()

        # Update metrics
        self._update_sync_metrics(sync_start, [
            asset_sync_result,
            relationship_sync_result,
            conflict_resolution_result,
            graph_validation_result
        ])

        return SyncResult(
            success=True,
            assets_synced=asset_sync_result.count,
            relationships_synced=relationship_sync_result.count,
            conflicts_resolved=conflict_resolution_result.count,
            duration=datetime.utcnow() - sync_start,
            graph_metrics=self._get_graph_metrics()
        )

    except Exception as e:
        self.logger.error(f"Bi-directional sync failed: {e}")
        raise SyncError(f"Synchronization failed: {e}") from e
```

## 📊 Data Mapping and Transformation

### Asset Type Mappings
```python
ASSET_TYPE_MAPPINGS = {
    # Blast-Radius -> ServiceNow
    "server": "cmdb_ci_server",
    "database": "cmdb_ci_database",
    "application": "cmdb_ci_appl",
    "network_device": "cmdb_ci_netgear",
    "storage": "cmdb_ci_storage",
    "container": "cmdb_ci_container",
    "cloud_service": "cmdb_ci_cloud_service",
    "workstation": "cmdb_ci_computer"
}

RELATIONSHIP_TYPE_MAPPINGS = {
    # Blast-Radius -> ServiceNow
    RelationshipType.DEPENDS_ON: "Depends on::Used by",
    RelationshipType.COMMUNICATES_WITH: "Connects to::Connected by",
    RelationshipType.CONTAINS: "Contains::Contained by",
    RelationshipType.MANAGES: "Manages::Managed by",
    RelationshipType.MONITORS: "Monitors::Monitored by"
}
```

### Field Transformation Logic
```python
def transform_blast_radius_to_servicenow(self, asset: Asset) -> ServiceNowAsset:
    """
    Transform Blast-Radius asset to ServiceNow CMDB format.

    Args:
        asset: Blast-Radius asset instance

    Returns:
        ServiceNowAsset: Transformed asset for ServiceNow

    Raises:
        TransformationError: If transformation fails
    """
    try:
        # Map asset type
        cmdb_class = ASSET_TYPE_MAPPINGS.get(
            asset.asset_type.value,
            "cmdb_ci_computer"
        )

        # Extract primary IP address
        primary_ip = None
        if asset.ip_addresses:
            primary_ip = asset.ip_addresses[0]

        # Build ServiceNow asset
        servicenow_asset = ServiceNowAsset(
            sys_id=self._get_or_create_servicenow_id(asset.id),
            name=asset.name,
            sys_class_name=CMDBAssetClass(cmdb_class),
            operational_status=self._map_asset_status(asset.status),
            environment=asset.environment,
            ip_address=primary_ip,
            fqdn=asset.hostname,
            attributes={
                "blast_radius_id": str(asset.id),
                "risk_score": asset.risk_score,
                "compliance_status": asset.compliance_status,
                "discovery_source": asset.discovery_source.value,
                "security_context": {
                    "vulnerabilities": asset.vulnerabilities or [],
                    "security_groups": asset.security_groups or [],
                    "last_security_scan": asset.last_health_check.isoformat() if asset.last_health_check else None
                }
            }
        )

        return servicenow_asset

    except Exception as e:
        raise TransformationError(f"Failed to transform asset {asset.id}: {e}") from e
```

## 🔄 NetworkX Graph Integration

### Graph-Based Relationship Analysis
```python
async def _sync_relationships_with_graph_analysis(self) -> RelationshipSyncResult:
    """
    Synchronize relationships using NetworkX graph analysis for enhanced accuracy.

    This method leverages NetworkX to analyze relationship patterns, detect
    inconsistencies, and optimize synchronization based on graph topology.

    Returns:
        RelationshipSyncResult: Results of relationship synchronization
    """
    self.logger.info("Starting graph-based relationship synchronization")

    # Build comprehensive graph from both sources
    blast_radius_graph = await self._build_blast_radius_graph()
    servicenow_graph = await self._build_servicenow_graph()

    # Analyze graph differences using NetworkX algorithms
    graph_diff = self._analyze_graph_differences(blast_radius_graph, servicenow_graph)

    # Synchronize based on analysis
    sync_operations = []

    # Add missing relationships
    for edge in graph_diff.missing_in_servicenow:
        operation = await self._create_servicenow_relationship(edge)
        sync_operations.append(operation)

    # Update existing relationships
    for edge in graph_diff.different_attributes:
        operation = await self._update_servicenow_relationship(edge)
        sync_operations.append(operation)

    # Remove obsolete relationships
    for edge in graph_diff.obsolete_in_servicenow:
        operation = await self._remove_servicenow_relationship(edge)
        sync_operations.append(operation)

    # Validate graph consistency using NetworkX
    consistency_check = self._validate_graph_consistency_with_networkx(
        blast_radius_graph,
        servicenow_graph
    )

    return RelationshipSyncResult(
        operations=sync_operations,
        consistency_score=consistency_check.score,
        graph_metrics=consistency_check.metrics
    )


def _analyze_graph_differences(
    self,
    graph_a: nx.DiGraph,
    graph_b: nx.DiGraph
) -> GraphDifferenceAnalysis:
    """
    Analyze differences between two NetworkX graphs.

    Args:
        graph_a: First graph (typically Blast-Radius)
        graph_b: Second graph (typically ServiceNow)

    Returns:
        GraphDifferenceAnalysis: Comprehensive difference analysis
    """
    # Find structural differences
    edges_a = set(graph_a.edges())
    edges_b = set(graph_b.edges())

    missing_in_b = edges_a - edges_b
    missing_in_a = edges_b - edges_a
    common_edges = edges_a & edges_b

    # Analyze attribute differences for common edges
    different_attributes = []
    for edge in common_edges:
        attrs_a = graph_a.get_edge_data(*edge)
        attrs_b = graph_b.get_edge_data(*edge)

        if attrs_a != attrs_b:
            different_attributes.append({
                'edge': edge,
                'attributes_a': attrs_a,
                'attributes_b': attrs_b,
                'differences': self._compare_edge_attributes(attrs_a, attrs_b)
            })

    # Calculate graph metrics using NetworkX
    metrics_a = self._calculate_graph_metrics(graph_a)
    metrics_b = self._calculate_graph_metrics(graph_b)

    return GraphDifferenceAnalysis(
        missing_in_servicenow=missing_in_b,
        missing_in_blast_radius=missing_in_a,
        different_attributes=different_attributes,
        graph_metrics_blast_radius=metrics_a,
        graph_metrics_servicenow=metrics_b,
        similarity_score=self._calculate_graph_similarity(graph_a, graph_b)
    )


def _calculate_graph_metrics(self, graph: nx.DiGraph) -> Dict[str, Any]:
    """
    Calculate comprehensive graph metrics using NetworkX.

    Args:
        graph: NetworkX directed graph

    Returns:
        Dict containing graph metrics
    """
    return {
        "node_count": graph.number_of_nodes(),
        "edge_count": graph.number_of_edges(),
        "density": nx.density(graph),
        "is_connected": nx.is_weakly_connected(graph),
        "components": nx.number_weakly_connected_components(graph),
        "average_clustering": nx.average_clustering(graph.to_undirected()),
        "diameter": self._safe_graph_diameter(graph),
        "centrality_metrics": {
            "degree_centrality": dict(nx.degree_centrality(graph)),
            "betweenness_centrality": dict(nx.betweenness_centrality(graph)),
            "closeness_centrality": dict(nx.closeness_centrality(graph))
        }
    }
```

## 🔧 Implementation Details

### Core Service Implementation
**File:** `backend/app/services/servicenow_client.py`

```python
"""
ServiceNow REST API client with comprehensive error handling and retry logic.

Implements robust ServiceNow API communication following PEP 8, PEP 257, and PEP 484
standards with full type annotations and detailed docstrings.
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, AsyncGenerator, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging
import json

from app.core.config import settings
from app.schemas.servicenow_cmdb import ServiceNowAsset, ServiceNowRelationship


@dataclass
class ServiceNowConfig:
    """ServiceNow connection configuration."""

    instance_url: str
    username: str
    password: str
    api_version: str = "v1"
    timeout_seconds: int = 30
    max_retries: int = 3
    retry_delay_seconds: float = 1.0


class ServiceNowAPIError(Exception):
    """ServiceNow API operation error."""

    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class ServiceNowClient:
    """
    Asynchronous ServiceNow REST API client.

    Provides comprehensive ServiceNow CMDB operations with robust error handling,
    automatic retries, and efficient batch processing capabilities.

    Attributes:
        config: ServiceNow connection configuration
        session: Async HTTP session for API calls
        logger: Logging instance for operation tracking
    """

    def __init__(self, config: ServiceNowConfig) -> None:
        """
        Initialize ServiceNow client.

        Args:
            config: ServiceNow connection configuration

        Raises:
            ValueError: If configuration is invalid
        """
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Validate configuration
        if not all([config.instance_url, config.username, config.password]):
            raise ValueError("ServiceNow configuration incomplete")

        self._base_url = f"{config.instance_url}/api/now/table"
        self._auth = aiohttp.BasicAuth(config.username, config.password)

    async def __aenter__(self) -> 'ServiceNowClient':
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout_seconds),
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def get_configuration_items(
        self,
        table: str = "cmdb_ci",
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> AsyncGenerator[ServiceNowAsset, None]:
        """
        Retrieve configuration items from ServiceNow CMDB.

        Args:
            table: CMDB table name
            filters: Query filters
            limit: Maximum number of items to retrieve

        Yields:
            ServiceNowAsset: Individual configuration items

        Raises:
            ServiceNowAPIError: If API request fails
        """
        offset = 0
        batch_size = 100
        total_retrieved = 0

        while True:
            # Build query parameters
            params = {
                'sysparm_offset': offset,
                'sysparm_limit': batch_size,
                'sysparm_display_value': 'true'
            }

            if filters:
                query_parts = []
                for key, value in filters.items():
                    query_parts.append(f"{key}={value}")
                params['sysparm_query'] = '^'.join(query_parts)

            # Make API request with retry logic
            response_data = await self._make_request('GET', f"{table}", params=params)

            items = response_data.get('result', [])
            if not items:
                break

            # Yield transformed items
            for item_data in items:
                try:
                    asset = self._transform_servicenow_item_to_asset(item_data)
                    yield asset
                    total_retrieved += 1

                    if limit and total_retrieved >= limit:
                        return

                except Exception as e:
                    self.logger.warning(f"Failed to transform item {item_data.get('sys_id')}: {e}")
                    continue

            offset += batch_size

            # Break if we got fewer items than requested (end of data)
            if len(items) < batch_size:
                break

    async def create_configuration_item(self, asset: ServiceNowAsset) -> str:
        """
        Create a new configuration item in ServiceNow.

        Args:
            asset: ServiceNow asset to create

        Returns:
            str: Created item's sys_id

        Raises:
            ServiceNowAPIError: If creation fails
        """
        table = asset.sys_class_name.value
        data = self._transform_asset_to_servicenow_data(asset)

        response_data = await self._make_request('POST', table, json_data=data)

        result = response_data.get('result', {})
        sys_id = result.get('sys_id')

        if not sys_id:
            raise ServiceNowAPIError("Failed to create configuration item: no sys_id returned")

        self.logger.info(f"Created ServiceNow CI: {sys_id}")
        return sys_id

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict] = None,
        json_data: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to ServiceNow API with retry logic.

        Args:
            method: HTTP method
            endpoint: API endpoint
            params: Query parameters
            json_data: JSON request body

        Returns:
            Dict containing response data

        Raises:
            ServiceNowAPIError: If request fails after retries
        """
        if not self.session:
            raise ServiceNowAPIError("Client session not initialized")

        url = f"{self._base_url}/{endpoint}"

        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_data,
                    auth=self._auth
                ) as response:

                    response_text = await response.text()

                    if response.status == 200:
                        return json.loads(response_text)
                    elif response.status == 429:  # Rate limited
                        if attempt < self.config.max_retries:
                            await asyncio.sleep(self.config.retry_delay_seconds * (2 ** attempt))
                            continue

                    # Handle other error status codes
                    error_data = None
                    try:
                        error_data = json.loads(response_text)
                    except json.JSONDecodeError:
                        pass

                    raise ServiceNowAPIError(
                        f"ServiceNow API error: {response.status} - {response_text}",
                        status_code=response.status,
                        response_data=error_data
                    )

            except aiohttp.ClientError as e:
                if attempt < self.config.max_retries:
                    self.logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                    await asyncio.sleep(self.config.retry_delay_seconds * (2 ** attempt))
                    continue
                else:
                    raise ServiceNowAPIError(f"Request failed after {self.config.max_retries} retries: {e}") from e

        raise ServiceNowAPIError("Maximum retry attempts exceeded")
```

## 📋 API Endpoints

### CMDB Sync Management API
**File:** `backend/app/api/v1/servicenow_cmdb.py`

```python
"""
ServiceNow CMDB integration API endpoints.

Provides REST API for managing ServiceNow CMDB synchronization operations
with comprehensive error handling and validation.
"""

import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.api.security import get_current_user
from app.models.user import User
from app.services.servicenow_cmdb_service import ServiceNowCMDBService, CMDBSyncConfiguration
from app.services.graph_engine import GraphEngine
from app.schemas.servicenow_cmdb import (
    ServiceNowAsset,
    ServiceNowRelationship,
    SyncConfigurationRequest,
    SyncStatusResponse,
    SyncResultResponse
)


logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/sync/start", response_model=SyncStatusResponse)
async def start_cmdb_sync(
    background_tasks: BackgroundTasks,
    sync_config: Optional[SyncConfigurationRequest] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Start ServiceNow CMDB synchronization process.

    Initiates bi-directional synchronization between Blast-Radius assets
    and ServiceNow CMDB with configurable options and conflict resolution.

    Args:
        background_tasks: FastAPI background task manager
        sync_config: Optional synchronization configuration
        db: Database session
        current_user: Authenticated user

    Returns:
        SyncStatusResponse: Synchronization job status

    Raises:
        HTTPException: If sync initiation fails
    """
    try:
        # Initialize services
        graph_engine = GraphEngine()
        config = CMDBSyncConfiguration()

        if sync_config:
            config = CMDBSyncConfiguration(**sync_config.dict())

        cmdb_service = ServiceNowCMDBService(db, graph_engine, config)

        # Start background synchronization
        sync_job_id = str(uuid.uuid4())
        background_tasks.add_task(
            _execute_sync_job,
            sync_job_id,
            cmdb_service,
            current_user.id
        )

        return SyncStatusResponse(
            job_id=sync_job_id,
            status="started",
            started_at=datetime.utcnow(),
            message="CMDB synchronization started successfully"
        )

    except Exception as e:
        logger.error(f"Failed to start CMDB sync: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start synchronization: {str(e)}"
        )


@router.get("/sync/status/{job_id}", response_model=SyncStatusResponse)
async def get_sync_status(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get synchronization job status.

    Args:
        job_id: Synchronization job identifier
        db: Database session
        current_user: Authenticated user

    Returns:
        SyncStatusResponse: Current job status and progress
    """
    try:
        # Retrieve job status from database or cache
        job_status = await _get_sync_job_status(job_id, db)

        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sync job {job_id} not found"
            )

        return job_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get sync status for job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sync status"
        )


async def _execute_sync_job(
    job_id: str,
    cmdb_service: ServiceNowCMDBService,
    user_id: uuid.UUID
) -> None:
    """
    Execute synchronization job in background.

    Args:
        job_id: Job identifier
        cmdb_service: CMDB service instance
        user_id: User who initiated the sync
    """
    try:
        logger.info(f"Starting CMDB sync job {job_id}")

        # Update job status to running
        await _update_sync_job_status(job_id, "running", "Synchronization in progress")

        # Perform synchronization
        sync_result = await cmdb_service.perform_bidirectional_sync()

        # Update job status to completed
        await _update_sync_job_status(
            job_id,
            "completed",
            f"Sync completed: {sync_result.assets_synced} assets, {sync_result.relationships_synced} relationships",
            sync_result
        )

        logger.info(f"CMDB sync job {job_id} completed successfully")

    except Exception as e:
        logger.error(f"CMDB sync job {job_id} failed: {e}")
        await _update_sync_job_status(job_id, "failed", f"Synchronization failed: {str(e)}")
```

## 🧪 Testing Strategy

### Comprehensive Test Suite
**File:** `backend/tests/test_servicenow_cmdb_integration.py`

```python
"""
Comprehensive test suite for ServiceNow CMDB integration.

Tests all aspects of CMDB synchronization including NetworkX graph operations,
data transformation, conflict resolution, and API endpoints following PEP 8,
PEP 257, and PEP 484 standards.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, List, Any
import uuid

import networkx as nx
from sqlalchemy.orm import Session

from app.services.servicenow_cmdb_service import (
    ServiceNowCMDBService,
    CMDBSyncConfiguration,
    SyncDirection,
    ConflictResolutionStrategy
)
from app.services.servicenow_client import ServiceNowClient, ServiceNowConfig
from app.schemas.servicenow_cmdb import ServiceNowAsset, ServiceNowRelationship, CMDBAssetClass
from app.db.models.asset import Asset, AssetRelationship, AssetType, AssetProvider
from app.services.graph_engine import GraphEngine


class TestServiceNowCMDBService:
    """Test suite for ServiceNow CMDB service functionality."""

    @pytest.fixture
    def mock_db_session(self) -> Mock:
        """Create mock database session."""
        session = Mock(spec=Session)
        session.query.return_value.filter.return_value.all.return_value = []
        session.commit = Mock()
        session.rollback = Mock()
        return session

    @pytest.fixture
    def mock_graph_engine(self) -> Mock:
        """Create mock graph engine with NetworkX capabilities."""
        engine = Mock(spec=GraphEngine)
        engine.graph = nx.DiGraph()
        engine.add_asset = Mock()
        engine.add_relationship = Mock()
        engine.get_graph_statistics.return_value = {
            "nodes": 0,
            "edges": 0,
            "density": 0.0,
            "is_connected": False
        }
        return engine

    @pytest.fixture
    def sync_config(self) -> CMDBSyncConfiguration:
        """Create test synchronization configuration."""
        return CMDBSyncConfiguration(
            sync_direction=SyncDirection.BIDIRECTIONAL,
            conflict_resolution=ConflictResolutionStrategy.TIMESTAMP_BASED,
            sync_interval_minutes=15,
            batch_size=50,
            asset_type_mappings={
                "server": "cmdb_ci_server",
                "database": "cmdb_ci_database"
            }
        )

    @pytest.fixture
    def cmdb_service(self, mock_db_session, mock_graph_engine, sync_config) -> ServiceNowCMDBService:
        """Create CMDB service instance for testing."""
        return ServiceNowCMDBService(mock_db_session, mock_graph_engine, sync_config)

    @pytest.fixture
    def sample_blast_radius_asset(self) -> Asset:
        """Create sample Blast-Radius asset for testing."""
        return Asset(
            id=uuid.uuid4(),
            name="test-server-01",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            environment="production",
            ip_addresses=["************"],
            hostname="test-server-01.company.com",
            risk_score=75,
            compliance_status="compliant",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    @pytest.fixture
    def sample_servicenow_asset(self) -> ServiceNowAsset:
        """Create sample ServiceNow asset for testing."""
        return ServiceNowAsset(
            sys_id="12345678-1234-1234-1234-123456789012",
            name="test-server-01",
            sys_class_name=CMDBAssetClass.SERVER,
            operational_status="Operational",
            environment="production",
            ip_address="************",
            fqdn="test-server-01.company.com",
            sys_created_on=datetime.utcnow(),
            sys_updated_on=datetime.utcnow(),
            attributes={
                "blast_radius_id": str(uuid.uuid4()),
                "risk_score": 75,
                "compliance_status": "compliant"
            }
        )

    def test_service_initialization(self, mock_db_session, mock_graph_engine, sync_config):
        """Test ServiceNow CMDB service initialization."""
        service = ServiceNowCMDBService(mock_db_session, mock_graph_engine, sync_config)

        assert service.db == mock_db_session
        assert service.graph_engine == mock_graph_engine
        assert service.config == sync_config
        assert service._sync_metrics["last_sync"] is None
        assert isinstance(service._cmdb_graph, nx.DiGraph)

    def test_asset_transformation_blast_radius_to_servicenow(
        self,
        cmdb_service,
        sample_blast_radius_asset
    ):
        """Test transformation from Blast-Radius asset to ServiceNow format."""
        with patch.object(cmdb_service, '_get_or_create_servicenow_id') as mock_get_id:
            mock_get_id.return_value = "test-sys-id"

            servicenow_asset = cmdb_service.transform_blast_radius_to_servicenow(sample_blast_radius_asset)

            assert servicenow_asset.sys_id == "test-sys-id"
            assert servicenow_asset.name == sample_blast_radius_asset.name
            assert servicenow_asset.sys_class_name == CMDBAssetClass.SERVER
            assert servicenow_asset.ip_address == "************"
            assert servicenow_asset.fqdn == sample_blast_radius_asset.hostname
            assert servicenow_asset.attributes["blast_radius_id"] == str(sample_blast_radius_asset.id)
            assert servicenow_asset.attributes["risk_score"] == sample_blast_radius_asset.risk_score

    def test_asset_transformation_servicenow_to_blast_radius(
        self,
        cmdb_service,
        sample_servicenow_asset
    ):
        """Test transformation from ServiceNow asset to Blast-Radius format."""
        blast_radius_asset = cmdb_service.transform_servicenow_to_blast_radius(sample_servicenow_asset)

        assert blast_radius_asset.name == sample_servicenow_asset.name
        assert blast_radius_asset.asset_type == AssetType.SERVER
        assert blast_radius_asset.ip_addresses == [sample_servicenow_asset.ip_address]
        assert blast_radius_asset.hostname == sample_servicenow_asset.fqdn
        assert blast_radius_asset.environment == sample_servicenow_asset.environment

    @pytest.mark.asyncio
    async def test_graph_based_relationship_analysis(self, cmdb_service):
        """Test NetworkX-based relationship analysis."""
        # Create test graphs
        blast_radius_graph = nx.DiGraph()
        blast_radius_graph.add_edge("asset1", "asset2", relationship_type="depends_on")
        blast_radius_graph.add_edge("asset2", "asset3", relationship_type="communicates_with")

        servicenow_graph = nx.DiGraph()
        servicenow_graph.add_edge("asset1", "asset2", relationship_type="depends_on")
        servicenow_graph.add_edge("asset1", "asset4", relationship_type="manages")

        # Analyze differences
        diff_analysis = cmdb_service._analyze_graph_differences(blast_radius_graph, servicenow_graph)

        assert len(diff_analysis.missing_in_servicenow) == 1
        assert ("asset2", "asset3") in diff_analysis.missing_in_servicenow
        assert len(diff_analysis.missing_in_blast_radius) == 1
        assert ("asset1", "asset4") in diff_analysis.missing_in_blast_radius
        assert diff_analysis.similarity_score < 1.0

    @pytest.mark.asyncio
    async def test_conflict_resolution_timestamp_based(self, cmdb_service):
        """Test timestamp-based conflict resolution."""
        # Create conflicting assets with different timestamps
        blast_radius_asset = Mock()
        blast_radius_asset.updated_at = datetime.utcnow()
        blast_radius_asset.name = "server-updated-name"

        servicenow_asset = Mock()
        servicenow_asset.sys_updated_on = datetime.utcnow() - timedelta(hours=1)
        servicenow_asset.name = "server-old-name"

        # Test conflict resolution
        resolved_asset = await cmdb_service._resolve_asset_conflict(
            blast_radius_asset,
            servicenow_asset,
            ConflictResolutionStrategy.TIMESTAMP_BASED
        )

        # Blast-Radius asset should win (newer timestamp)
        assert resolved_asset.name == "server-updated-name"

    @pytest.mark.asyncio
    async def test_bidirectional_sync_workflow(self, cmdb_service):
        """Test complete bi-directional synchronization workflow."""
        with patch.object(cmdb_service, '_sync_assets_bidirectional') as mock_sync_assets, \
             patch.object(cmdb_service, '_sync_relationships_with_graph_analysis') as mock_sync_rels, \
             patch.object(cmdb_service, '_resolve_sync_conflicts') as mock_resolve_conflicts, \
             patch.object(cmdb_service, '_validate_graph_consistency') as mock_validate:

            # Mock return values
            mock_sync_assets.return_value = Mock(count=10, success=True)
            mock_sync_rels.return_value = Mock(count=15, success=True)
            mock_resolve_conflicts.return_value = Mock(count=2, success=True)
            mock_validate.return_value = Mock(score=0.95, success=True)

            # Execute sync
            result = await cmdb_service.perform_bidirectional_sync()

            # Verify workflow execution
            assert result.success is True
            assert result.assets_synced == 10
            assert result.relationships_synced == 15
            assert result.conflicts_resolved == 2
            assert result.graph_metrics is not None

            # Verify all phases were called
            mock_sync_assets.assert_called_once()
            mock_sync_rels.assert_called_once()
            mock_resolve_conflicts.assert_called_once()
            mock_validate.assert_called_once()

    def test_graph_metrics_calculation(self, cmdb_service):
        """Test NetworkX graph metrics calculation."""
        # Create test graph
        test_graph = nx.DiGraph()
        test_graph.add_edges_from([
            ("A", "B"), ("B", "C"), ("C", "D"), ("A", "C")
        ])

        metrics = cmdb_service._calculate_graph_metrics(test_graph)

        assert metrics["node_count"] == 4
        assert metrics["edge_count"] == 4
        assert metrics["density"] > 0
        assert "centrality_metrics" in metrics
        assert "degree_centrality" in metrics["centrality_metrics"]
        assert "betweenness_centrality" in metrics["centrality_metrics"]

    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, cmdb_service):
        """Test error handling and recovery mechanisms."""
        with patch.object(cmdb_service, '_sync_assets_bidirectional') as mock_sync:
            # Simulate sync failure
            mock_sync.side_effect = Exception("Network error")

            # Test that sync handles errors gracefully
            with pytest.raises(Exception) as exc_info:
                await cmdb_service.perform_bidirectional_sync()

            assert "Network error" in str(exc_info.value)

    def test_configuration_validation(self):
        """Test synchronization configuration validation."""
        # Test valid configuration
        valid_config = CMDBSyncConfiguration(
            sync_direction=SyncDirection.BIDIRECTIONAL,
            batch_size=100,
            max_retries=3
        )
        assert valid_config.sync_direction == SyncDirection.BIDIRECTIONAL

        # Test invalid batch size
        with pytest.raises(ValueError):
            CMDBSyncConfiguration(batch_size=-1)


class TestServiceNowClient:
    """Test suite for ServiceNow API client."""

    @pytest.fixture
    def servicenow_config(self) -> ServiceNowConfig:
        """Create test ServiceNow configuration."""
        return ServiceNowConfig(
            instance_url="https://test.service-now.com",
            username="test_user",
            password="test_password",
            timeout_seconds=30,
            max_retries=3
        )

    @pytest.fixture
    def servicenow_client(self, servicenow_config) -> ServiceNowClient:
        """Create ServiceNow client for testing."""
        return ServiceNowClient(servicenow_config)

    @pytest.mark.asyncio
    async def test_client_initialization(self, servicenow_client):
        """Test ServiceNow client initialization."""
        assert servicenow_client.config.instance_url == "https://test.service-now.com"
        assert servicenow_client.config.username == "test_user"
        assert servicenow_client._base_url == "https://test.service-now.com/api/now/table"

    @pytest.mark.asyncio
    async def test_configuration_items_retrieval(self, servicenow_client):
        """Test configuration items retrieval with pagination."""
        mock_response_data = {
            "result": [
                {
                    "sys_id": "12345",
                    "name": "test-server",
                    "sys_class_name": "cmdb_ci_server",
                    "operational_status": "Operational"
                }
            ]
        }

        with patch.object(servicenow_client, '_make_request') as mock_request:
            mock_request.return_value = mock_response_data

            items = []
            async for item in servicenow_client.get_configuration_items():
                items.append(item)
                break  # Only test first item

            assert len(items) == 1
            assert items[0].sys_id == "12345"
            assert items[0].name == "test-server"

    @pytest.mark.asyncio
    async def test_api_error_handling(self, servicenow_client):
        """Test API error handling and retry logic."""
        with patch.object(servicenow_client, 'session') as mock_session:
            # Mock failed response
            mock_response = AsyncMock()
            mock_response.status = 500
            mock_response.text.return_value = "Internal Server Error"

            mock_session.request.return_value.__aenter__.return_value = mock_response

            # Test that API errors are properly handled
            with pytest.raises(Exception):
                await servicenow_client._make_request('GET', 'test_endpoint')


class TestCMDBAPIEndpoints:
    """Test suite for CMDB API endpoints."""

    @pytest.mark.asyncio
    async def test_start_sync_endpoint(self):
        """Test sync initiation endpoint."""
        # This would test the FastAPI endpoint
        # Implementation depends on your testing framework setup
        pass

    @pytest.mark.asyncio
    async def test_sync_status_endpoint(self):
        """Test sync status retrieval endpoint."""
        # This would test the sync status endpoint
        # Implementation depends on your testing framework setup
        pass


# Performance and load testing
class TestPerformanceAndScaling:
    """Performance and scaling tests for CMDB integration."""

    @pytest.mark.asyncio
    async def test_large_dataset_sync_performance(self):
        """Test synchronization performance with large datasets."""
        # Test with 10,000+ assets
        pass

    @pytest.mark.asyncio
    async def test_concurrent_sync_operations(self):
        """Test concurrent synchronization operations."""
        # Test multiple simultaneous sync jobs
        pass

    def test_networkx_graph_performance(self):
        """Test NetworkX graph operations performance."""
        # Test graph analysis with large numbers of nodes and edges
        large_graph = nx.DiGraph()

        # Add 1000 nodes and 5000 edges
        for i in range(1000):
            large_graph.add_node(f"node_{i}")

        for i in range(5000):
            source = f"node_{i % 1000}"
            target = f"node_{(i + 1) % 1000}"
            large_graph.add_edge(source, target)

        # Test performance of common operations
        start_time = datetime.utcnow()

        # Calculate metrics
        node_count = large_graph.number_of_nodes()
        edge_count = large_graph.number_of_edges()
        density = nx.density(large_graph)

        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()

        # Assert performance is acceptable (< 1 second for basic operations)
        assert duration < 1.0
        assert node_count == 1000
        assert edge_count == 5000
        assert density > 0
```

## 📊 Success Metrics and KPIs

### Operational Metrics
```python
@dataclass
class CMDBSyncMetrics:
    """Comprehensive CMDB synchronization metrics."""

    # Sync performance
    sync_duration_seconds: float
    assets_processed_per_second: float
    relationships_processed_per_second: float

    # Data quality
    data_consistency_score: float  # 0.0 to 1.0
    conflict_resolution_rate: float
    error_rate: float

    # Graph analysis
    graph_similarity_score: float
    relationship_accuracy: float
    topology_consistency: float

    # Business impact
    manual_effort_reduction_percentage: float
    incident_resolution_time_improvement: float
    compliance_coverage_percentage: float


def calculate_sync_success_score(metrics: CMDBSyncMetrics) -> float:
    """
    Calculate overall synchronization success score.

    Args:
        metrics: Synchronization metrics

    Returns:
        float: Success score from 0.0 to 100.0
    """
    weights = {
        'data_consistency': 0.3,
        'performance': 0.2,
        'graph_accuracy': 0.2,
        'error_handling': 0.15,
        'business_impact': 0.15
    }

    performance_score = min(metrics.assets_processed_per_second / 10.0, 1.0)
    error_score = max(1.0 - metrics.error_rate, 0.0)

    total_score = (
        metrics.data_consistency_score * weights['data_consistency'] +
        performance_score * weights['performance'] +
        metrics.graph_similarity_score * weights['graph_accuracy'] +
        error_score * weights['error_handling'] +
        (metrics.manual_effort_reduction_percentage / 100.0) * weights['business_impact']
    )

    return total_score * 100.0
```

## 🚀 Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
- [ ] ServiceNow client implementation with retry logic
- [ ] Basic data models and transformation logic
- [ ] Core CMDB service with NetworkX integration
- [ ] Unit tests for core functionality

### Phase 2: Synchronization Engine (Weeks 3-4)
- [ ] Bi-directional sync implementation
- [ ] Conflict resolution strategies
- [ ] Graph-based relationship analysis
- [ ] Performance optimization

### Phase 3: API and Integration (Weeks 5-6)
- [ ] REST API endpoints
- [ ] Background job processing
- [ ] Error handling and monitoring
- [ ] Integration tests

### Phase 4: Production Readiness (Weeks 7-8)
- [ ] Performance testing and optimization
- [ ] Security review and hardening
- [ ] Documentation and deployment guides
- [ ] Production deployment and monitoring

## 🔒 Security Considerations

### Authentication and Authorization
- ServiceNow OAuth 2.0 integration
- Role-based access control for sync operations
- Audit logging for all CMDB operations
- Encrypted credential storage

### Data Protection
- Field-level encryption for sensitive data
- Data masking for non-production environments
- GDPR compliance for personal data
- Secure data transmission (TLS 1.3)

## 📈 Monitoring and Alerting

### Key Metrics to Monitor
- Sync success rate and duration
- Data consistency scores
- API response times and error rates
- Graph analysis performance
- Resource utilization

### Alert Conditions
- Sync failure rate > 5%
- Data inconsistency > 1%
- API response time > 30 seconds
- Memory usage > 80%
- Disk space < 20%

## 🎯 Acceptance Criteria

### Functional Requirements
- [ ] Bi-directional asset synchronization with 99.9% accuracy
- [ ] Real-time conflict detection and resolution
- [ ] NetworkX-based relationship analysis
- [ ] Comprehensive API for sync management
- [ ] Automated error recovery and retry logic

### Performance Requirements
- [ ] Sync 1000+ assets in < 5 minutes
- [ ] Process 10,000+ relationships in < 10 minutes
- [ ] API response time < 2 seconds for 95% of requests
- [ ] Support 100+ concurrent sync operations
- [ ] Memory usage < 2GB for typical workloads

### Quality Requirements
- [ ] 95%+ test coverage
- [ ] PEP 8, PEP 257, PEP 484 compliance
- [ ] Comprehensive error handling
- [ ] Detailed logging and monitoring
- [ ] Security best practices implementation

---

**Document Status:** Draft v1.0
**Next Review:** December 20, 2025
**Stakeholders:** Security Team, IT Operations, Development Team
```
```