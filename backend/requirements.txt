# Core Framework
fastapi==0.115.6
uvicorn[standard]==0.32.1
pydantic==2.10.4
pydantic-settings==2.7.0

# Database
asyncpg==0.30.0
sqlalchemy[asyncio]==2.0.36
alembic==1.14.0
psycopg2-binary==2.9.10

# Graph Database
neo4j==5.27.0
py2neo==2021.2.4

# Caching and Sessions
redis==5.2.1
aioredis==2.0.1

# Graph Analysis
networkx==3.4.2
networkit==11.1.post1

# Authentication and Security
azure-identity==1.19.0
python-jose[cryptography]==3.5.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.20
email-validator==2.2.0
pyotp==2.9.0
qrcode[pil]==8.0
cryptography>=45.0.4
pyjwt>=2.10.1
oauthlib>=3.2.2

# HTTP Client
requests==2.32.4
stix2==3.0.1
mitreattack-python==2.0.0
taxii2-client==2.3.0
httpx==0.28.1

# Task Scheduling
APScheduler==3.11.0
celery==5.4.0

# Logging and Monitoring
structlog==25.4.0
prometheus-client==0.21.1

# Utilities
click==8.1.7
rich==13.9.4
typer==0.15.1

# Data Validation and Serialization
marshmallow==3.23.2
marshmallow-sqlalchemy==1.1.0

# Configuration Management
python-dotenv==1.0.1
dynaconf==3.3.2

# Date and Time
python-dateutil==2.9.0.post0
pytz==2024.2

# Discovery Dependencies (Optional)
# Cloud Discovery
boto3>=1.35.84  # AWS SDK
azure-mgmt-resource>=23.2.0  # Azure SDK
azure-mgmt-compute>=33.0.0
google-cloud-asset>=3.27.1  # GCP SDK
google-cloud-compute>=1.21.0

# Network Discovery
python-nmap>=0.7.1  # Python nmap wrapper
scapy>=2.6.1  # Network packet manipulation

# API Discovery
aiohttp>=3.11.10  # HTTP client for API discovery
beautifulsoup4>=4.12.3  # HTML parsing
lxml>=5.3.0  # XML parsing

# Additional Security Dependencies
urllib3>=2.2.3  # HTTP library with security fixes
certifi>=2024.8.30  # CA certificates
setuptools>=75.6.0  # Build tools with security fixes
