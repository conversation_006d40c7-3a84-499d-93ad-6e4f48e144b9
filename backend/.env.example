# Blast-Radius Security Tool - Backend Configuration Template
# Copy this file to .env and update with your actual values

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
PROJECT_NAME=Blast-Radius Security Tool
API_V1_STR=/api/v1
ENVIRONMENT=development

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Configuration
DATABASE_URL=postgresql://blastradius:blastradius_secure_password_2024@localhost:5432/blastradius
DATABASE_TEST_URL=postgresql://blastradius_test:blastradius_test_password_2024@localhost:5432/blastradius_test

# Redis Configuration
REDIS_URL=redis://:blastradius_redis_secure_password_2024@localhost:6379/0
REDIS_TEST_URL=redis://:blastradius_redis_secure_password_2024@localhost:6379/1

# Neo4j Configuration
NEO4J_URL=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=blastradius_neo4j_secure_password_2024

# Azure Identity Configuration
AZURE_CLIENT_ID=your-azure-client-id
AZURE_CLIENT_SECRET=your-azure-client-secret
AZURE_TENANT_ID=your-azure-tenant-id
AZURE_AUTHORITY=https://login.microsoftonline.com/your-tenant-id

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "https://localhost:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# API Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Celery Configuration (Background Tasks)
CELERY_BROKER_URL=redis://:blastradius_redis_secure_password_2024@localhost:6379/2
CELERY_RESULT_BACKEND=redis://:blastradius_redis_secure_password_2024@localhost:6379/3

# Monitoring and Observability
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
JAEGER_ENABLED=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["json", "csv", "xml", "yaml"]
UPLOAD_PATH=/tmp/uploads

# External API Configuration
THREAT_INTEL_API_KEY=your-threat-intel-api-key
SERVICENOW_INSTANCE_URL=https://your-instance.service-now.com
SERVICENOW_USERNAME=your-servicenow-username
SERVICENOW_PASSWORD=your-servicenow-password

# Cloud Provider Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1

AZURE_SUBSCRIPTION_ID=your-azure-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group

GCP_PROJECT_ID=your-gcp-project-id
GCP_SERVICE_ACCOUNT_KEY_PATH=/path/to/service-account-key.json

# Graph Analysis Configuration
MAX_GRAPH_NODES=********
MAX_ATTACK_PATH_DEPTH=5
GRAPH_ANALYSIS_TIMEOUT=300  # seconds
PARALLEL_PROCESSING_WORKERS=4

# Caching Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000
ENABLE_QUERY_CACHE=true

# Session Configuration
SESSION_TIMEOUT_MINUTES=60
SESSION_CLEANUP_INTERVAL_MINUTES=15
MAX_CONCURRENT_SESSIONS_PER_USER=5

# Audit and Compliance
AUDIT_LOG_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=365
COMPLIANCE_MODE=SOC2
GDPR_ENABLED=true

# Performance Tuning
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Development and Testing
TESTING=false
MOCK_EXTERNAL_APIS=false
ENABLE_DEBUG_TOOLBAR=false
PROFILING_ENABLED=false
