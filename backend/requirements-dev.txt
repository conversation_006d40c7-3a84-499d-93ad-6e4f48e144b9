# Development dependencies
-r requirements.txt

# Code Quality and Linting
ruff>=0.1.6
mypy>=1.7.1
bandit>=1.7.5
pre-commit>=3.6.0

# Security Updates
setuptools>=78.1.1
wheel>=0.38.1

# Testing
pytest>=7.4.3
pytest-cov>=4.1.0
pytest-asyncio>=0.21.1
pytest-mock>=3.12.0
pytest-xdist>=3.5.0
pytest-html>=4.1.1
factory-boy>=3.3.0
faker>=20.1.0
httpx>=0.25.2

# E2E Testing
playwright>=1.40.0
pytest-playwright>=0.4.3

# BDD Testing
behave>=1.2.6
behave-html-formatter>=0.9.10
parse>=1.20.0
parse-type>=0.6.2

# Property-Based Testing
hypothesis>=6.88.0

# Contract Testing
jsonschema>=4.19.0

# Performance Testing
psutil>=5.9.0
locust>=2.17.0

# Architecture Testing
networkx>=3.2.0

# Chaos Engineering
redis>=5.0.0

# Visual Regression Testing
Pillow>=10.0.0

# Mutation Testing
mutmut>=2.4.3

# Test Metrics and Reporting
matplotlib>=3.7.0
seaborn>=0.12.0

# Documentation
sphinx>=7.2.6
sphinx-rtd-theme>=1.3.0
sphinx-autodoc-typehints>=1.25.2
myst-parser>=2.0.0
sphinx-copybutton>=0.5.2
sphinxcontrib-openapi>=0.8.1

# Development Tools
ipython>=8.17.2
ipdb>=0.13.13
watchdog>=3.0.0

# Security Testing
safety>=2.3.0                    # Dependency vulnerability scanner
semgrep>=1.45.0                  # Static analysis security scanner
python-owasp-zap-v2.4>=0.0.21   # OWASP ZAP integration
requests-mock>=1.11.0            # Mock HTTP requests for security tests

# Type Stubs
types-python-dateutil>=2.8.19
types-redis>=4.6.0
types-requests>=2.31.0
