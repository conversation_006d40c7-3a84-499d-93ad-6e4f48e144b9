# Optional Discovery Dependencies for Blast-Radius Security Tool
# Install with: pip install -r requirements-discovery.txt

# Cloud Discovery Dependencies
# AWS
boto3>=1.35.84
botocore>=1.35.84

# Azure
azure-mgmt-resource>=23.2.0
azure-mgmt-compute>=33.0.0
azure-mgmt-storage>=21.2.0
azure-mgmt-sql>=3.0.1
azure-mgmt-web>=7.3.0
azure-mgmt-network>=27.0.0

# Google Cloud Platform
google-cloud-asset>=3.27.1
google-cloud-compute>=1.21.0
google-cloud-storage>=2.18.2
google-cloud-sql>=1.12.0
google-cloud-functions>=1.17.0

# Network Discovery Dependencies
python-nmap>=0.7.1
scapy>=2.6.1
netaddr>=1.3.0
ipaddress>=1.0.23  # Usually included in Python 3.3+

# API Discovery Dependencies
aiohttp>=3.11.10
beautifulsoup4>=4.12.3
lxml>=5.3.0
requests-oauthlib>=2.0.0
urllib3>=2.2.3

# Additional Network Tools (Optional)
# These require external tools to be installed
# masscan - Install separately: https://github.com/robertdavidgraham/masscan
# nmap - Install separately: https://nmap.org/download.html
# kiterunner - Install separately: https://github.com/assetnote/kiterunner

# Development and Testing
pytest-asyncio>=0.24.0  # For async testing
responses>=0.25.4  # Mock HTTP responses
moto>=5.0.23  # Mock AWS services

# Security Dependencies
certifi>=2024.8.30  # CA certificates
setuptools>=75.6.0  # Build tools with security fixes
