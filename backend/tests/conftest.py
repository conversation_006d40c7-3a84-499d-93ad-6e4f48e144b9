"""Test configuration and fixtures for pytest."""

import os
import pytest
import uuid
from typing import Generator, Dict, Any
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
from app.main import app
from app.db.base import Base
from app.db.session import get_db
from app.db.models.user import User, UserRole
from app.services.auth_service import AuthService
from app.services.user_service import UserService
from app.core.security import get_password_hash
from app.config import settings

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False}
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine) -> Generator[Session, None, None]:
    """Create test database session."""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create test client with database session override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def auth_service(db_session: Session) -> AuthService:
    """Create auth service instance."""
    return AuthService(db_session)


@pytest.fixture
def user_service(db_session: Session) -> UserService:
    """Create user service instance."""
    return UserService(db_session)


@pytest.fixture
def sample_user_data() -> Dict[str, Any]:
    """Sample user data for testing."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "password": "TestPassword123!",
        "phone_number": "+1234567890",
        "department": "Security",
        "job_title": "Analyst",
        "timezone": "UTC",
        "language": "en",
        "theme": "dark",
        "is_active": True,
        "require_password_change": False,
        "roles": ["analyst"]
    }


@pytest.fixture
def admin_user_data() -> Dict[str, Any]:
    """Admin user data for testing."""
    return {
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Admin User",
        "password": "AdminPassword123!",
        "phone_number": "+1234567891",
        "department": "Security",
        "job_title": "Administrator",
        "timezone": "UTC",
        "language": "en",
        "theme": "dark",
        "is_active": True,
        "require_password_change": False,
        "roles": ["admin"]
    }


@pytest.fixture
def system_roles(db_session: Session) -> None:
    """Create system roles in test database."""
    UserRole.create_system_roles(db_session)


@pytest.fixture
def test_user(db_session: Session, system_roles, sample_user_data) -> User:
    """Create a test user in the database."""
    from app.schemas.user import UserCreate
    from app.services.user_service import UserService
    
    user_service = UserService(db_session)
    user_create = UserCreate(**sample_user_data)
    user = user_service.create_user(user_create)
    
    return user


@pytest.fixture
def admin_user(db_session: Session, system_roles, admin_user_data) -> User:
    """Create an admin user in the database."""
    from app.schemas.user import UserCreate
    from app.services.user_service import UserService
    
    user_service = UserService(db_session)
    user_create = UserCreate(**admin_user_data)
    user = user_service.create_user(user_create)
    
    return user


@pytest.fixture
def authenticated_headers(client: TestClient, test_user: User) -> Dict[str, str]:
    """Get authentication headers for test user."""
    login_data = {
        "username": test_user.username,
        "password": "TestPassword123!",
        "remember_me": False
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token_data = response.json()
    access_token = token_data["tokens"]["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_headers(client: TestClient, admin_user: User) -> Dict[str, str]:
    """Get authentication headers for admin user."""
    login_data = {
        "username": admin_user.username,
        "password": "AdminPassword123!",
        "remember_me": False
    }
    
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token_data = response.json()
    access_token = token_data["tokens"]["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def mock_request_context():
    """Mock request context for testing."""
    class MockClient:
        host = "127.0.0.1"
    
    class MockRequest:
        client = MockClient()
        headers = {"user-agent": "test-client"}
        url = type('obj', (object,), {'path': '/test'})()
        method = "GET"
    
    return MockRequest()


# Test data factories
class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def create_user_data(
        username: str = None,
        email: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Create user data with optional overrides."""
        base_data = {
            "username": username or f"user_{uuid.uuid4().hex[:8]}",
            "email": email or f"user_{uuid.uuid4().hex[:8]}@example.com",
            "full_name": "Test User",
            "password": "TestPassword123!",
            "phone_number": "+1234567890",
            "department": "Security",
            "job_title": "Analyst",
            "timezone": "UTC",
            "language": "en",
            "theme": "dark",
            "is_active": True,
            "require_password_change": False,
            "roles": ["analyst"]
        }
        base_data.update(kwargs)
        return base_data


class LoginAttemptFactory:
    """Factory for creating login attempt data."""
    
    @staticmethod
    def create_attempt_data(
        username: str = "testuser",
        success: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Create login attempt data."""
        base_data = {
            "username": username,
            "success": success,
            "ip_address": "127.0.0.1",
            "user_agent": "test-client",
            "failure_reason": None if success else "invalid_credentials"
        }
        base_data.update(kwargs)
        return base_data


# Test utilities
def assert_user_response(user_data: Dict[str, Any], expected_data: Dict[str, Any]):
    """Assert user response matches expected data."""
    assert user_data["username"] == expected_data["username"]
    assert user_data["email"] == expected_data["email"]
    assert user_data["full_name"] == expected_data["full_name"]
    assert user_data["is_active"] == expected_data.get("is_active", True)


def assert_error_response(response_data: Dict[str, Any], expected_detail: str):
    """Assert error response format."""
    assert "detail" in response_data
    assert response_data["detail"] == expected_detail


# Pytest markers
pytest_plugins = []

# Configure test environment
os.environ["TESTING"] = "true"
os.environ["DATABASE_URL"] = TEST_DATABASE_URL
os.environ["SECRET_KEY"] = "test-secret-key-for-testing-only"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"
