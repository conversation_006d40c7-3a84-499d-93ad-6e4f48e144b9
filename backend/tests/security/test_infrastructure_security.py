"""
Infrastructure Security Tests for Blast-Radius Security Tool.

These tests validate security configurations at the infrastructure level
including Docker, database, Redis, and network security.
"""

import pytest
import subprocess
import json
import os
import socket
from pathlib import Path
from typing import Dict, Any
import docker
import redis
import psycopg2
from sqlalchemy import create_engine


class TestDockerSecurity:
    """Test Docker container security configurations."""
    
    def test_docker_image_vulnerabilities(self):
        """Test Docker images for known vulnerabilities."""
        try:
            client = docker.from_env()
            
            # Get list of images used by the application
            images_to_scan = [
                "postgres:15",
                "redis:7-alpine", 
                "neo4j:5.0",
                # Add your application image when built
            ]
            
            vulnerabilities_found = []
            
            for image_name in images_to_scan:
                try:
                    # Pull image if not present
                    client.images.pull(image_name)
                    
                    # Run Trivy scan (if available)
                    result = subprocess.run([
                        "docker", "run", "--rm", "-v", "/var/run/docker.sock:/var/run/docker.sock",
                        "aquasec/trivy", "image", "--format", "json", image_name
                    ], capture_output=True, text=True, timeout=300)
                    
                    if result.returncode == 0 and result.stdout:
                        scan_results = json.loads(result.stdout)
                        if scan_results.get("Results"):
                            for result_item in scan_results["Results"]:
                                if result_item.get("Vulnerabilities"):
                                    high_critical = [
                                        v for v in result_item["Vulnerabilities"]
                                        if v.get("Severity") in ["HIGH", "CRITICAL"]
                                    ]
                                    if high_critical:
                                        vulnerabilities_found.extend(high_critical)
                
                except Exception as e:
                    print(f"Warning: Could not scan image {image_name}: {e}")
            
            # Assert no critical vulnerabilities
            critical_vulns = [v for v in vulnerabilities_found if v.get("Severity") == "CRITICAL"]
            assert len(critical_vulns) == 0, f"Found {len(critical_vulns)} critical vulnerabilities"
            
        except docker.errors.DockerException:
            pytest.skip("Docker not available for testing")
    
    def test_docker_container_security_config(self):
        """Test Docker container security configurations."""
        try:
            client = docker.from_env()
            
            # Check running containers
            containers = client.containers.list()
            
            for container in containers:
                # Check if container is running as root
                exec_result = container.exec_run("whoami")
                if exec_result.exit_code == 0:
                    user = exec_result.output.decode().strip()
                    assert user != "root", f"Container {container.name} running as root"
                
                # Check for privileged mode
                container_info = client.api.inspect_container(container.id)
                assert not container_info["HostConfig"]["Privileged"], \
                    f"Container {container.name} running in privileged mode"
                
                # Check for excessive capabilities
                caps = container_info["HostConfig"].get("CapAdd", [])
                dangerous_caps = ["SYS_ADMIN", "NET_ADMIN", "SYS_PTRACE"]
                for cap in dangerous_caps:
                    assert cap not in caps, f"Container {container.name} has dangerous capability {cap}"
        
        except docker.errors.DockerException:
            pytest.skip("Docker not available for testing")


class TestDatabaseSecurity:
    """Test database security configurations."""
    
    def test_postgresql_security_config(self):
        """Test PostgreSQL security configuration."""
        try:
            # Test connection with default credentials
            db_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/blast_radius")
            engine = create_engine(db_url)
            
            with engine.connect() as conn:
                # Check for default/weak passwords
                result = conn.execute("SELECT current_user;")
                current_user = result.fetchone()[0]
                
                # Check SSL configuration
                result = conn.execute("SHOW ssl;")
                ssl_status = result.fetchone()[0]
                # In production, SSL should be enabled
                if os.getenv("ENVIRONMENT") == "production":
                    assert ssl_status == "on", "SSL should be enabled in production"
                
                # Check for dangerous extensions
                result = conn.execute("""
                    SELECT extname FROM pg_extension 
                    WHERE extname IN ('plpythonu', 'plperlu', 'plsh');
                """)
                dangerous_extensions = result.fetchall()
                assert len(dangerous_extensions) == 0, "Dangerous extensions found"
                
                # Check user privileges
                result = conn.execute("""
                    SELECT rolname, rolsuper, rolcreaterole, rolcreatedb 
                    FROM pg_roles WHERE rolname = current_user;
                """)
                user_info = result.fetchone()
                
                # Application user should not be superuser
                if current_user != "postgres":
                    assert not user_info[1], "Application user should not be superuser"
        
        except Exception as e:
            pytest.skip(f"Database not available for testing: {e}")
    
    def test_database_connection_security(self):
        """Test database connection security."""
        db_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/blast_radius")
        
        # Check if connection uses SSL in production
        if os.getenv("ENVIRONMENT") == "production":
            assert "sslmode=require" in db_url or "sslmode=verify" in db_url, \
                "Production database connections should use SSL"
        
        # Check for password in connection string (should use environment variables)
        if "://" in db_url:
            # Extract password part
            parts = db_url.split("://")[1].split("@")[0]
            if ":" in parts:
                password = parts.split(":")[1]
                # Password should not be a common weak password
                weak_passwords = ["password", "123456", "admin", "root", "postgres"]
                assert password not in weak_passwords, "Weak database password detected"


class TestRedisSecurity:
    """Test Redis security configurations."""
    
    def test_redis_authentication(self):
        """Test Redis authentication configuration."""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
            
            # Try to connect without authentication
            r = redis.from_url(redis_url)
            
            # Check if authentication is required
            try:
                r.ping()
                # If we can ping without auth, check if it's intentional for dev
                if os.getenv("ENVIRONMENT") == "production":
                    pytest.fail("Redis should require authentication in production")
            except redis.AuthenticationError:
                # Good - authentication is required
                pass
            
            # Test for default Redis port exposure
            if "localhost" not in redis_url and "127.0.0.1" not in redis_url:
                # External Redis should use non-default port or be properly secured
                assert ":6379" not in redis_url or os.getenv("ENVIRONMENT") != "production", \
                    "Production Redis should not use default port 6379"
        
        except Exception as e:
            pytest.skip(f"Redis not available for testing: {e}")
    
    def test_redis_dangerous_commands(self):
        """Test that dangerous Redis commands are disabled."""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
            r = redis.from_url(redis_url)
            
            # Test dangerous commands
            dangerous_commands = ["FLUSHALL", "FLUSHDB", "CONFIG", "EVAL", "DEBUG"]
            
            for cmd in dangerous_commands:
                try:
                    if cmd == "CONFIG":
                        r.execute_command("CONFIG", "GET", "*")
                    elif cmd == "EVAL":
                        r.eval("return 1", 0)
                    else:
                        r.execute_command(cmd)
                    
                    # If command succeeds in production, it's a security risk
                    if os.getenv("ENVIRONMENT") == "production":
                        pytest.fail(f"Dangerous Redis command {cmd} is enabled in production")
                
                except redis.ResponseError as e:
                    if "unknown command" in str(e).lower() or "disabled" in str(e).lower():
                        # Good - command is disabled
                        pass
                    else:
                        raise
        
        except Exception as e:
            pytest.skip(f"Redis not available for testing: {e}")


class TestNetworkSecurity:
    """Test network security configurations."""
    
    def test_port_exposure(self):
        """Test that only necessary ports are exposed."""
        # Define expected open ports for different environments
        expected_ports = {
            "development": [8000, 5432, 6379, 7474, 7687],  # API, PostgreSQL, Redis, Neo4j
            "production": [8000, 443]  # Only API and HTTPS
        }
        
        environment = os.getenv("ENVIRONMENT", "development")
        allowed_ports = expected_ports.get(environment, expected_ports["development"])
        
        # Scan for open ports on localhost
        open_ports = []
        for port in range(1, 65536):
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.1)
            result = sock.connect_ex(('localhost', port))
            if result == 0:
                open_ports.append(port)
            sock.close()
            
            # Limit scan to common ports for performance
            if port > 10000:
                break
        
        # Check for unexpected open ports
        unexpected_ports = [p for p in open_ports if p not in allowed_ports]
        
        # Allow some flexibility for development
        if environment == "development":
            # Filter out common development ports
            dev_ports = [3000, 3001, 5000, 5001, 8080, 8081, 9000]
            unexpected_ports = [p for p in unexpected_ports if p not in dev_ports]
        
        assert len(unexpected_ports) == 0, f"Unexpected open ports: {unexpected_ports}"
    
    def test_ssl_tls_configuration(self):
        """Test SSL/TLS configuration."""
        if os.getenv("ENVIRONMENT") == "production":
            # Test HTTPS redirect
            try:
                import requests
                
                # Test HTTP to HTTPS redirect
                response = requests.get("http://localhost:8000/health", 
                                      allow_redirects=False, timeout=5)
                
                # Should redirect to HTTPS
                assert response.status_code in [301, 302, 307, 308], \
                    "HTTP should redirect to HTTPS in production"
                
                # Test HTTPS endpoint
                response = requests.get("https://localhost:8000/health", 
                                      verify=False, timeout=5)
                assert response.status_code == 200, "HTTPS endpoint should be accessible"
                
            except Exception as e:
                pytest.skip(f"Cannot test HTTPS configuration: {e}")


class TestSecretsManagement:
    """Test secrets management security."""
    
    def test_environment_variables_security(self):
        """Test that sensitive data is not in environment variables."""
        # Get all environment variables
        env_vars = dict(os.environ)
        
        # Check for hardcoded secrets
        sensitive_patterns = [
            "password=",
            "secret=", 
            "key=",
            "token=",
            "api_key=",
        ]
        
        for var_name, var_value in env_vars.items():
            var_value_lower = var_value.lower()
            
            # Skip checking certain variables
            if var_name in ["PATH", "HOME", "USER", "SHELL"]:
                continue
            
            for pattern in sensitive_patterns:
                assert pattern not in var_value_lower, \
                    f"Potential hardcoded secret in {var_name}"
    
    def test_configuration_files_security(self):
        """Test that configuration files don't contain secrets."""
        config_files = [
            ".env",
            ".env.local", 
            ".env.production",
            "config.yaml",
            "config.json",
            "docker-compose.yml",
            "docker-compose.override.yml"
        ]
        
        for config_file in config_files:
            config_path = Path(config_file)
            if config_path.exists():
                content = config_path.read_text().lower()
                
                # Check for hardcoded secrets
                secret_indicators = [
                    "password=password",
                    "password=123456", 
                    "password=admin",
                    "secret=secret",
                    "key=key",
                    "token=token"
                ]
                
                for indicator in secret_indicators:
                    assert indicator not in content, \
                        f"Hardcoded secret found in {config_file}: {indicator}"


class TestComplianceSecurity:
    """Test compliance and regulatory security requirements."""
    
    def test_data_encryption_at_rest(self):
        """Test that sensitive data is encrypted at rest."""
        # This would typically involve checking database encryption,
        # file system encryption, etc.
        # Implementation depends on specific encryption setup
        pass
    
    def test_audit_logging_configuration(self):
        """Test that audit logging is properly configured."""
        # Check if audit logs are being generated
        log_files = [
            "logs/audit.log",
            "logs/security.log", 
            "logs/access.log"
        ]
        
        for log_file in log_files:
            log_path = Path(log_file)
            if log_path.exists():
                # Check if log file has recent entries
                stat = log_path.stat()
                # Log should have been modified recently (within last hour)
                import time
                assert time.time() - stat.st_mtime < 3600, \
                    f"Audit log {log_file} not recently updated"
    
    def test_backup_security(self):
        """Test backup security configurations."""
        # Check if backups are encrypted
        backup_dirs = [
            "backups/",
            "/var/backups/blast-radius/",
            "/backup/"
        ]
        
        for backup_dir in backup_dirs:
            backup_path = Path(backup_dir)
            if backup_path.exists():
                # Check for unencrypted backup files
                for backup_file in backup_path.glob("**/*"):
                    if backup_file.is_file():
                        # Backup files should be encrypted (have .gpg, .enc extension)
                        # or be in encrypted directory
                        assert backup_file.suffix in [".gpg", ".enc", ".encrypted"], \
                            f"Unencrypted backup file found: {backup_file}"
