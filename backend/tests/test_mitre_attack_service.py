"""Tests for MITRE ATT&CK integration service."""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from pathlib import Path

from app.services.mitre_attack_service import (
    MitreAttackService,
    AttackTechnique,
    AttackGroup,
    AttackSoftware,
    TechniqueCorrelation,
    AttackPattern,
    AttackDomain,
    ConfidenceLevel
)


class TestMitreAttackService:
    """Test MITRE ATT&CK service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def temp_data_dir(self, tmp_path):
        """Create temporary data directory."""
        return str(tmp_path / "mitre_test")
    
    @pytest.fixture
    def mitre_service(self, mock_db, temp_data_dir):
        """Create MITRE ATT&CK service."""
        return MitreAttackService(mock_db, temp_data_dir)
    
    @pytest.fixture
    def sample_technique(self):
        """Sample MITRE ATT&CK technique."""
        return AttackTechnique(
            technique_id="T1566",
            name="Phishing",
            description="Adversaries may send phishing messages to gain access to victim systems.",
            tactic="initial-access",
            domain=AttackDomain.ENTERPRISE,
            platforms=["Windows", "Linux", "macOS"],
            data_sources=["Email Gateway", "Network Traffic"],
            mitigations=["User Training", "Email Security"],
            detection_methods=["Email Analysis", "Network Monitoring"],
            sub_techniques=["T1566.001", "T1566.002"],
            kill_chain_phases=["initial-access"],
            external_references=[],
            created=datetime.utcnow(),
            modified=datetime.utcnow(),
            version="1.0"
        )
    
    @pytest.fixture
    def sample_group(self):
        """Sample MITRE ATT&CK group."""
        return AttackGroup(
            group_id="G0016",
            name="APT29",
            description="APT29 is a threat group that has been attributed to Russia's Foreign Intelligence Service.",
            aliases=["Cozy Bear", "The Dukes"],
            techniques=["T1566", "T1078", "T1055"],
            software=["S0154", "S0363"],
            associated_campaigns=["SolarWinds"],
            external_references=[],
            created=datetime.utcnow(),
            modified=datetime.utcnow(),
            version="1.0"
        )
    
    @pytest.fixture
    def sample_event_data(self):
        """Sample security event data."""
        return {
            "event_id": "evt_12345",
            "timestamp": datetime.utcnow().isoformat(),
            "source": "email_security",
            "message": "Suspicious phishing email detected with malicious attachment",
            "command_line": "powershell.exe -ExecutionPolicy Bypass -File malicious.ps1",
            "process_name": "powershell.exe",
            "host": "workstation-001",
            "user": "john.doe",
            "source_ip": "*************",
            "file_hash": "abc123def456"
        }
    
    def test_service_initialization(self, mitre_service):
        """Test service initialization."""
        assert mitre_service.data_dir.exists()
        assert len(mitre_service.correlation_rules) > 0
        assert "T1566" in mitre_service.correlation_rules  # Phishing
        assert "T1059" in mitre_service.correlation_rules  # Command and Scripting Interpreter
    
    def test_correlation_rules_loaded(self, mitre_service):
        """Test correlation rules are properly loaded."""
        # Check phishing rule
        phishing_rule = mitre_service.correlation_rules["T1566"]
        assert "phishing" in phishing_rule["keywords"]
        assert "email_security" in phishing_rule["log_sources"]
        assert phishing_rule["confidence_base"] > 0
        
        # Check PowerShell rule
        powershell_rule = mitre_service.correlation_rules["T1059"]
        assert "powershell" in powershell_rule["keywords"]
        assert "endpoint" in powershell_rule["log_sources"]
    
    @pytest.mark.asyncio
    async def test_event_correlation(self, mitre_service, sample_event_data):
        """Test security event correlation to techniques."""
        # Add sample technique to service
        sample_technique = AttackTechnique(
            technique_id="T1566",
            name="Phishing",
            description="Phishing attacks",
            tactic="initial-access",
            domain=AttackDomain.ENTERPRISE,
            platforms=["Windows"],
            data_sources=[],
            mitigations=[],
            detection_methods=[],
            sub_techniques=[],
            kill_chain_phases=[],
            external_references=[],
            created=datetime.utcnow(),
            modified=datetime.utcnow(),
            version="1.0"
        )
        mitre_service.techniques["T1566"] = sample_technique
        
        correlations = await mitre_service.correlate_event_to_techniques(sample_event_data)
        
        assert len(correlations) > 0
        
        # Check phishing correlation
        phishing_corr = next((c for c in correlations if c.technique_id == "T1566"), None)
        assert phishing_corr is not None
        assert phishing_corr.event_id == "evt_12345"
        assert phishing_corr.confidence_score > 0.5
        assert "phishing" in " ".join(phishing_corr.evidence).lower()
        
        # Check PowerShell correlation
        powershell_corr = next((c for c in correlations if c.technique_id == "T1059"), None)
        assert powershell_corr is not None
        assert powershell_corr.confidence_score > 0.5
    
    def test_extract_event_text(self, mitre_service, sample_event_data):
        """Test event text extraction."""
        event_text = mitre_service._extract_event_text(sample_event_data)
        
        assert "phishing" in event_text
        assert "powershell" in event_text
        assert "malicious" in event_text
        assert event_text == event_text.lower()  # Should be lowercase
    
    def test_calculate_correlation_confidence(self, mitre_service):
        """Test correlation confidence calculation."""
        event_text = "suspicious phishing email with malicious attachment"
        log_source = "email_security"
        rule = {
            "keywords": ["phishing", "malicious"],
            "log_sources": ["email_security", "web_proxy"],
            "confidence_base": 0.8
        }
        
        confidence = mitre_service._calculate_correlation_confidence(event_text, log_source, rule)
        
        assert confidence > 0.8  # Should be high due to keyword and source matches
        assert confidence <= 1.0
    
    def test_confidence_level_conversion(self, mitre_service):
        """Test confidence score to level conversion."""
        assert mitre_service._score_to_confidence_level(0.9) == ConfidenceLevel.VERY_HIGH
        assert mitre_service._score_to_confidence_level(0.7) == ConfidenceLevel.HIGH
        assert mitre_service._score_to_confidence_level(0.5) == ConfidenceLevel.MEDIUM
        assert mitre_service._score_to_confidence_level(0.3) == ConfidenceLevel.LOW
    
    def test_extract_evidence(self, mitre_service):
        """Test evidence extraction from event text."""
        event_text = "phishing email with malicious powershell script"
        rule = {
            "keywords": ["phishing", "malicious", "nonexistent"]
        }
        
        evidence = mitre_service._extract_evidence(event_text, rule)
        
        assert len(evidence) == 2  # phishing and malicious should match
        assert any("phishing" in ev for ev in evidence)
        assert any("malicious" in ev for ev in evidence)
        assert not any("nonexistent" in ev for ev in evidence)
    
    def test_extract_context(self, mitre_service, sample_event_data):
        """Test context extraction from event data."""
        context = mitre_service._extract_context(sample_event_data)
        
        assert "host" in context
        assert "user" in context
        assert "source_ip" in context
        assert context["host"] == "workstation-001"
        assert context["user"] == "john.doe"
    
    @pytest.mark.asyncio
    async def test_attack_pattern_identification(self, mitre_service):
        """Test attack pattern identification."""
        # Create sample correlations
        correlations = [
            TechniqueCorrelation(
                correlation_id="corr1",
                event_id="evt1",
                technique_id="T1566",  # Phishing
                confidence=ConfidenceLevel.HIGH,
                confidence_score=0.8,
                evidence=["phishing email"],
                context={"host": "workstation-001"},
                timestamp=datetime.utcnow() - timedelta(minutes=30)
            ),
            TechniqueCorrelation(
                correlation_id="corr2",
                event_id="evt2",
                technique_id="T1059",  # Command and Scripting
                confidence=ConfidenceLevel.HIGH,
                confidence_score=0.7,
                evidence=["powershell execution"],
                context={"host": "workstation-001"},
                timestamp=datetime.utcnow() - timedelta(minutes=20)
            ),
            TechniqueCorrelation(
                correlation_id="corr3",
                event_id="evt3",
                technique_id="T1055",  # Process Injection
                confidence=ConfidenceLevel.MEDIUM,
                confidence_score=0.6,
                evidence=["process injection"],
                context={"host": "workstation-001"},
                timestamp=datetime.utcnow() - timedelta(minutes=10)
            )
        ]
        
        # Add correlations to service
        for corr in correlations:
            mitre_service.correlations[corr.correlation_id] = corr
        
        # Add sample techniques
        mitre_service.techniques["T1566"] = AttackTechnique(
            technique_id="T1566", name="Phishing", description="", tactic="initial-access",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        mitre_service.techniques["T1059"] = AttackTechnique(
            technique_id="T1059", name="Command and Scripting", description="", tactic="execution",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        mitre_service.techniques["T1055"] = AttackTechnique(
            technique_id="T1055", name="Process Injection", description="", tactic="defense-evasion",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        
        patterns = await mitre_service.identify_attack_patterns(time_window_hours=1)
        
        assert len(patterns) > 0
        pattern = patterns[0]
        assert pattern.affected_assets == {"workstation-001"}
        assert len(pattern.techniques) == 3
        assert pattern.confidence > 0.5
        assert pattern.event_count == 3
    
    def test_determine_attack_sequence(self, mitre_service):
        """Test attack sequence determination."""
        # Add sample techniques with different tactics
        mitre_service.techniques["T1566"] = AttackTechnique(
            technique_id="T1566", name="Phishing", description="", tactic="initial-access",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        mitre_service.techniques["T1059"] = AttackTechnique(
            technique_id="T1059", name="Command and Scripting", description="", tactic="execution",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        mitre_service.techniques["T1055"] = AttackTechnique(
            technique_id="T1055", name="Process Injection", description="", tactic="defense-evasion",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        
        techniques = ["T1055", "T1566", "T1059"]  # Out of order
        sequence = mitre_service._determine_attack_sequence(techniques)
        
        # Should be ordered by tactic progression
        assert sequence.index("T1566") < sequence.index("T1059")  # initial-access before execution
        assert sequence.index("T1059") < sequence.index("T1055")  # execution before defense-evasion
    
    def test_validate_attack_sequence(self, mitre_service):
        """Test attack sequence validation."""
        # Add sample techniques
        mitre_service.techniques["T1566"] = AttackTechnique(
            technique_id="T1566", name="Phishing", description="", tactic="initial-access",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        mitre_service.techniques["T1059"] = AttackTechnique(
            technique_id="T1059", name="Command and Scripting", description="", tactic="execution",
            domain=AttackDomain.ENTERPRISE, platforms=[], data_sources=[], mitigations=[],
            detection_methods=[], sub_techniques=[], kill_chain_phases=[], external_references=[],
            created=datetime.utcnow(), modified=datetime.utcnow(), version="1.0"
        )
        
        # Valid sequence (initial-access -> execution)
        valid_sequence = ["T1566", "T1059"]
        valid_score = mitre_service._validate_attack_sequence(valid_sequence)
        assert valid_score > 0.5
        
        # Invalid sequence (execution -> initial-access)
        invalid_sequence = ["T1059", "T1566"]
        invalid_score = mitre_service._validate_attack_sequence(invalid_sequence)
        assert invalid_score < valid_score
    
    def test_attribute_to_groups(self, mitre_service, sample_group):
        """Test threat actor group attribution."""
        # Add sample group
        mitre_service.groups[sample_group.group_id] = sample_group
        
        # Test with techniques used by the group
        techniques = ["T1566", "T1078"]  # Techniques used by APT29
        attributed_groups = mitre_service._attribute_to_groups(techniques)
        
        assert sample_group.group_id in attributed_groups
        
        # Test with techniques not used by the group
        unrelated_techniques = ["T9999", "T8888"]
        unattributed_groups = mitre_service._attribute_to_groups(unrelated_techniques)
        
        assert sample_group.group_id not in unattributed_groups
    
    @pytest.mark.asyncio
    async def test_ioc_enrichment(self, mitre_service, sample_technique, sample_group):
        """Test IOC enrichment with ATT&CK context."""
        # Add sample data
        mitre_service.techniques[sample_technique.technique_id] = sample_technique
        mitre_service.groups[sample_group.group_id] = sample_group
        
        # Test IOC that should match
        ioc = "phishing"
        enrichment = await mitre_service.enrich_ioc_with_attack_context(ioc, "keyword")
        
        assert enrichment["ioc"] == ioc
        assert enrichment["ioc_type"] == "keyword"
        assert len(enrichment["attack_context"]["techniques"]) > 0
        assert enrichment["confidence"] > 0
        
        # Check technique match
        technique_match = enrichment["attack_context"]["techniques"][0]
        assert technique_match["id"] == "T1566"
        assert technique_match["name"] == "Phishing"
    
    def test_search_techniques(self, mitre_service, sample_technique):
        """Test technique search functionality."""
        mitre_service.techniques[sample_technique.technique_id] = sample_technique
        
        # Search by name
        results = mitre_service.search_techniques("phishing")
        assert len(results) == 1
        assert results[0].technique_id == "T1566"
        
        # Search by ID
        results = mitre_service.search_techniques("T1566")
        assert len(results) == 1
        
        # Search with no matches
        results = mitre_service.search_techniques("nonexistent")
        assert len(results) == 0
    
    def test_get_techniques_by_tactic(self, mitre_service, sample_technique):
        """Test getting techniques by tactic."""
        mitre_service.techniques[sample_technique.technique_id] = sample_technique
        
        results = mitre_service.get_techniques_by_tactic("initial-access")
        assert len(results) == 1
        assert results[0].technique_id == "T1566"
        
        # Test with non-existent tactic
        results = mitre_service.get_techniques_by_tactic("nonexistent-tactic")
        assert len(results) == 0
    
    def test_generate_navigator_layer(self, mitre_service, sample_technique):
        """Test ATT&CK Navigator layer generation."""
        mitre_service.techniques[sample_technique.technique_id] = sample_technique
        
        layer = mitre_service.generate_attack_navigator_layer(
            techniques=["T1566"],
            name="Test Layer",
            description="Test layer description"
        )
        
        assert layer["name"] == "Test Layer"
        assert layer["description"] == "Test layer description"
        assert len(layer["techniques"]) == 1
        assert layer["techniques"][0]["techniqueID"] == "T1566"
        assert layer["techniques"][0]["tactic"] == "initial-access"
    
    def test_get_statistics(self, mitre_service, sample_technique, sample_group):
        """Test statistics generation."""
        # Add sample data
        mitre_service.techniques[sample_technique.technique_id] = sample_technique
        mitre_service.groups[sample_group.group_id] = sample_group
        
        stats = mitre_service.get_statistics()
        
        assert stats["techniques"]["total"] == 1
        assert stats["groups"]["total"] == 1
        assert "enterprise" in stats["techniques"]["by_domain"]
        assert "initial-access" in stats["techniques"]["by_tactic"]
    
    @patch('aiohttp.ClientSession.get')
    @pytest.mark.asyncio
    async def test_download_attack_data(self, mock_get, mitre_service, temp_data_dir):
        """Test downloading ATT&CK data."""
        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = b'{"test": "data"}'
        mock_get.return_value.__aenter__.return_value = mock_response
        
        file_path = Path(temp_data_dir) / "test-attack.json"
        await mitre_service._download_attack_data("http://test.com/data.json", file_path)
        
        assert file_path.exists()
        with open(file_path, 'r') as f:
            data = json.load(f)
            assert data["test"] == "data"
    
    def test_is_data_current(self, mitre_service, temp_data_dir):
        """Test data currency check."""
        # Create test file
        data_file = Path(temp_data_dir) / "enterprise-attack.json"
        data_file.parent.mkdir(parents=True, exist_ok=True)
        data_file.write_text('{"test": "data"}')
        
        # File should be current (just created)
        assert mitre_service._is_data_current(AttackDomain.ENTERPRISE)
        
        # Test with non-existent file
        assert not mitre_service._is_data_current(AttackDomain.MOBILE)
    
    def test_technique_properties(self, sample_technique):
        """Test technique property methods."""
        # Test regular technique
        assert not sample_technique.is_sub_technique
        assert sample_technique.parent_technique_id is None
        
        # Test sub-technique
        sub_technique = AttackTechnique(
            technique_id="T1566.001",
            name="Spearphishing Attachment",
            description="",
            tactic="initial-access",
            domain=AttackDomain.ENTERPRISE,
            platforms=[],
            data_sources=[],
            mitigations=[],
            detection_methods=[],
            sub_techniques=[],
            kill_chain_phases=[],
            external_references=[],
            created=datetime.utcnow(),
            modified=datetime.utcnow(),
            version="1.0"
        )
        
        assert sub_technique.is_sub_technique
        assert sub_technique.parent_technique_id == "T1566"
