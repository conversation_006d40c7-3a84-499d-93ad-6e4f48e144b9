"""Simple test to isolate Pydantic schema issues."""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    
    try:
        from app.db.models.mitre import MitreDomain, MitreEntityStatus
        print("✅ Model imports successful")
        
        from pydantic import BaseModel, Field
        print("✅ Pydantic imports successful")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_simple_schema():
    """Test a simple schema creation."""
    print("Testing simple schema creation...")
    
    try:
        from pydantic import BaseModel, Field
        from app.db.models.mitre import MitreDomain, MitreEntityStatus
        
        class SimpleTechnique(BaseModel):
            technique_id: str
            name: str
            domain: MitreDomain
            status: MitreEntityStatus = MitreEntityStatus.ACTIVE
        
        # Test creation
        technique = SimpleTechnique(
            technique_id="T1234",
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE
        )
        
        print(f"✅ Simple schema created: {technique.technique_id}")
        return True
        
    except Exception as e:
        print(f"❌ Simple schema failed: {e}")
        return False

def test_schema_with_validation():
    """Test schema with field validation."""
    print("Testing schema with validation...")
    
    try:
        from pydantic import BaseModel, Field, field_validator
        from app.db.models.mitre import MitreDomain
        import re
        
        class ValidatedTechnique(BaseModel):
            technique_id: str = Field(pattern=r"^T\d{4}$")
            name: str = Field(min_length=1, max_length=255)
            domain: MitreDomain
            
            @field_validator('technique_id')
            @classmethod
            def validate_technique_id(cls, v: str) -> str:
                """Validate MITRE technique ID format."""
                if not re.match(r"^T\d{4}$", v):
                    raise ValueError("Technique ID must be in format T1234")
                return v.upper()
        
        # Test valid creation
        technique = ValidatedTechnique(
            technique_id="T1234",  # Use uppercase directly
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE
        )
        
        assert technique.technique_id == "T1234"
        print(f"✅ Validated schema created: {technique.technique_id}")
        
        # Test validation error
        try:
            ValidatedTechnique(
                technique_id="INVALID",
                name="Test",
                domain=MitreDomain.ENTERPRISE
            )
            print("❌ Should have failed validation")
            return False
        except Exception:
            print("✅ Validation error properly caught")
        
        return True
        
    except Exception as e:
        print(f"❌ Validated schema failed: {e}")
        return False

def test_mitre_schema_import():
    """Test importing our actual MITRE schemas."""
    print("Testing MITRE schema import...")
    
    try:
        from app.schemas.mitre import MitreTechniqueBase
        print("✅ MitreTechniqueBase imported successfully")
        
        from app.db.models.mitre import MitreDomain
        
        # Try to create an instance
        technique = MitreTechniqueBase(
            technique_id="T1234",
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE
        )
        
        print(f"✅ MitreTechniqueBase instance created: {technique.technique_id}")
        return True
        
    except Exception as e:
        print(f"❌ MITRE schema import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_tests():
    """Run all tests."""
    print("🚀 Running Simple MITRE Schema Tests")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_simple_schema,
        test_schema_with_validation,
        test_mitre_schema_import,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✅ {test.__name__} PASSED\n")
            else:
                failed += 1
                print(f"❌ {test.__name__} FAILED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED: {e}\n")
    
    print("=" * 50)
    print(f"Results: {passed} passed, {failed} failed")
    
    return failed == 0

if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
