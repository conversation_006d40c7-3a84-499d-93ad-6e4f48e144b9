"""Unit tests for MITRE ATT&CK database models."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models.mitre import (
    MitreTechnique,
    MitreTactic,
    MitreGroup,
    MitreSoftware,
    MitreMitigation,
    MitreDataSource,
    MitreCampaign,
    MitreMatrix,
    MitreDataSync,
    MitreDomain,
    MitreEntityStatus,
    MitreDataSource as MitreDataSourceEnum,
)


class TestMitreTechnique:
    """Test MITRE Technique model."""
    
    @pytest.mark.asyncio
    async def test_create_technique(self, async_db: AsyncSession):
        """Test creating a MITRE technique."""
        technique = MitreTechnique(
            technique_id="T1234",
            name="Test Technique",
            description="Test technique description",
            domain=MitreDomain.ENTERPRISE,
            status=MitreEntityStatus.ACTIVE,
            platforms=["Windows", "Linux"],
            data_sources=["Process monitoring", "File monitoring"]
        )
        
        async_db.add(technique)
        await async_db.commit()
        await async_db.refresh(technique)
        
        assert technique.id is not None
        assert technique.technique_id == "T1234"
        assert technique.name == "Test Technique"
        assert technique.domain == MitreDomain.ENTERPRISE
        assert technique.status == MitreEntityStatus.ACTIVE
        assert technique.platforms == ["Windows", "Linux"]
        assert technique.is_subtechnique is False
        assert technique.created_date is not None
        assert technique.modified_date is not None
    
    @pytest.mark.asyncio
    async def test_create_subtechnique(self, async_db: AsyncSession):
        """Test creating a MITRE sub-technique."""
        # Create parent technique first
        parent = MitreTechnique(
            technique_id="T1234",
            name="Parent Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(parent)
        
        # Create sub-technique
        subtechnique = MitreTechnique(
            technique_id="T1234.001",
            name="Sub-technique",
            domain=MitreDomain.ENTERPRISE,
            is_subtechnique=True,
            parent_technique_id="T1234"
        )
        async_db.add(subtechnique)
        
        await async_db.commit()
        await async_db.refresh(subtechnique)
        
        assert subtechnique.technique_id == "T1234.001"
        assert subtechnique.is_subtechnique is True
        assert subtechnique.parent_technique_id == "T1234"
    
    @pytest.mark.asyncio
    async def test_technique_unique_constraint(self, async_db: AsyncSession):
        """Test technique ID unique constraint."""
        technique1 = MitreTechnique(
            technique_id="T1234",
            name="First Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(technique1)
        await async_db.commit()
        
        # Try to create another technique with same ID
        technique2 = MitreTechnique(
            technique_id="T1234",
            name="Second Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(technique2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            await async_db.commit()
    
    @pytest.mark.asyncio
    async def test_technique_search_vector(self, async_db: AsyncSession):
        """Test technique search vector functionality."""
        technique = MitreTechnique(
            technique_id="T1234",
            name="Test Technique",
            description="This is a test technique for search",
            domain=MitreDomain.ENTERPRISE,
            search_vector="test technique search"
        )
        
        async_db.add(technique)
        await async_db.commit()
        await async_db.refresh(technique)
        
        assert technique.search_vector == "test technique search"


class TestMitreTactic:
    """Test MITRE Tactic model."""
    
    @pytest.mark.asyncio
    async def test_create_tactic(self, async_db: AsyncSession):
        """Test creating a MITRE tactic."""
        tactic = MitreTactic(
            tactic_id="TA0001",
            name="Initial Access",
            description="Initial access tactic",
            domain=MitreDomain.ENTERPRISE,
            short_name="initial-access"
        )
        
        async_db.add(tactic)
        await async_db.commit()
        await async_db.refresh(tactic)
        
        assert tactic.id is not None
        assert tactic.tactic_id == "TA0001"
        assert tactic.name == "Initial Access"
        assert tactic.domain == MitreDomain.ENTERPRISE
        assert tactic.short_name == "initial-access"
        assert tactic.status == MitreEntityStatus.ACTIVE
    
    @pytest.mark.asyncio
    async def test_tactic_references(self, async_db: AsyncSession):
        """Test tactic with references."""
        references = [
            {
                "source_name": "mitre-attack",
                "external_id": "TA0001",
                "url": "https://attack.mitre.org/tactics/TA0001"
            }
        ]
        
        tactic = MitreTactic(
            tactic_id="TA0001",
            name="Initial Access",
            domain=MitreDomain.ENTERPRISE,
            references=references
        )
        
        async_db.add(tactic)
        await async_db.commit()
        await async_db.refresh(tactic)
        
        assert tactic.references == references
        assert len(tactic.references) == 1
        assert tactic.references[0]["source_name"] == "mitre-attack"


class TestMitreGroup:
    """Test MITRE Group model."""
    
    @pytest.mark.asyncio
    async def test_create_group(self, async_db: AsyncSession):
        """Test creating a MITRE group."""
        group = MitreGroup(
            group_id="G0001",
            name="APT1",
            description="Advanced Persistent Threat 1",
            domain=MitreDomain.ENTERPRISE,
            aliases=["Comment Crew", "PLA Unit 61398"],
            country="China",
            sophistication="High"
        )
        
        async_db.add(group)
        await async_db.commit()
        await async_db.refresh(group)
        
        assert group.id is not None
        assert group.group_id == "G0001"
        assert group.name == "APT1"
        assert group.aliases == ["Comment Crew", "PLA Unit 61398"]
        assert group.country == "China"
        assert group.sophistication == "High"
    
    @pytest.mark.asyncio
    async def test_group_timeline(self, async_db: AsyncSession):
        """Test group with timeline information."""
        first_seen = datetime(2006, 1, 1)
        last_seen = datetime(2023, 12, 31)
        
        group = MitreGroup(
            group_id="G0001",
            name="APT1",
            domain=MitreDomain.ENTERPRISE,
            first_seen=first_seen,
            last_seen=last_seen
        )
        
        async_db.add(group)
        await async_db.commit()
        await async_db.refresh(group)
        
        assert group.first_seen == first_seen
        assert group.last_seen == last_seen


class TestMitreSoftware:
    """Test MITRE Software model."""
    
    @pytest.mark.asyncio
    async def test_create_malware(self, async_db: AsyncSession):
        """Test creating MITRE malware."""
        software = MitreSoftware(
            software_id="S0001",
            name="Poison Ivy",
            description="Remote access trojan",
            domain=MitreDomain.ENTERPRISE,
            software_type="malware",
            platforms=["Windows"],
            labels=["trojan"]
        )
        
        async_db.add(software)
        await async_db.commit()
        await async_db.refresh(software)
        
        assert software.software_id == "S0001"
        assert software.name == "Poison Ivy"
        assert software.software_type == "malware"
        assert software.platforms == ["Windows"]
        assert software.labels == ["trojan"]
    
    @pytest.mark.asyncio
    async def test_create_tool(self, async_db: AsyncSession):
        """Test creating MITRE tool."""
        software = MitreSoftware(
            software_id="S0002",
            name="Mimikatz",
            description="Credential dumping tool",
            domain=MitreDomain.ENTERPRISE,
            software_type="tool",
            platforms=["Windows"]
        )
        
        async_db.add(software)
        await async_db.commit()
        await async_db.refresh(software)
        
        assert software.software_type == "tool"
        assert software.name == "Mimikatz"


class TestMitreMitigation:
    """Test MITRE Mitigation model."""
    
    @pytest.mark.asyncio
    async def test_create_mitigation(self, async_db: AsyncSession):
        """Test creating a MITRE mitigation."""
        mitigation = MitreMitigation(
            mitigation_id="M1001",
            name="Multi-factor Authentication",
            description="Use multi-factor authentication",
            domain=MitreDomain.ENTERPRISE,
            mitigation_type="preventive"
        )
        
        async_db.add(mitigation)
        await async_db.commit()
        await async_db.refresh(mitigation)
        
        assert mitigation.mitigation_id == "M1001"
        assert mitigation.name == "Multi-factor Authentication"
        assert mitigation.mitigation_type == "preventive"


class TestMitreDataSync:
    """Test MITRE Data Sync model."""
    
    @pytest.mark.asyncio
    async def test_create_sync_record(self, async_db: AsyncSession):
        """Test creating a data sync record."""
        sync = MitreDataSync(
            sync_id="sync_enterprise_123456",
            domain=MitreDomain.ENTERPRISE,
            sync_type="manual",
            status="pending",
            source_url="https://example.com/data.json",
            triggered_by="test_user"
        )
        
        async_db.add(sync)
        await async_db.commit()
        await async_db.refresh(sync)
        
        assert sync.sync_id == "sync_enterprise_123456"
        assert sync.domain == MitreDomain.ENTERPRISE
        assert sync.sync_type == "manual"
        assert sync.status == "pending"
        assert sync.processed_entities == 0
        assert sync.failed_entities == 0
        assert sync.started_at is not None
    
    @pytest.mark.asyncio
    async def test_sync_progress_tracking(self, async_db: AsyncSession):
        """Test sync progress tracking."""
        sync = MitreDataSync(
            sync_id="sync_test_123",
            domain=MitreDomain.ENTERPRISE,
            sync_type="automatic",
            status="running",
            total_entities=100,
            processed_entities=50,
            failed_entities=5
        )
        
        async_db.add(sync)
        await async_db.commit()
        await async_db.refresh(sync)
        
        assert sync.total_entities == 100
        assert sync.processed_entities == 50
        assert sync.failed_entities == 5
        
        # Update progress
        sync.processed_entities = 75
        sync.failed_entities = 8
        await async_db.commit()
        
        assert sync.processed_entities == 75
        assert sync.failed_entities == 8
    
    @pytest.mark.asyncio
    async def test_sync_completion(self, async_db: AsyncSession):
        """Test sync completion with results."""
        sync = MitreDataSync(
            sync_id="sync_complete_123",
            domain=MitreDomain.ENTERPRISE,
            sync_type="manual",
            status="completed",
            total_entities=100,
            processed_entities=95,
            failed_entities=5,
            duration_seconds=120.5,
            sync_results={
                "techniques": 50,
                "tactics": 14,
                "groups": 20,
                "software": 11
            }
        )
        
        async_db.add(sync)
        await async_db.commit()
        await async_db.refresh(sync)
        
        assert sync.status == "completed"
        assert sync.duration_seconds == 120.5
        assert sync.sync_results["techniques"] == 50
        assert sync.sync_results["tactics"] == 14


class TestMitreRelationships:
    """Test MITRE model relationships."""
    
    @pytest.mark.asyncio
    async def test_technique_tactic_relationship(self, async_db: AsyncSession):
        """Test technique-tactic many-to-many relationship."""
        # Create tactic
        tactic = MitreTactic(
            tactic_id="TA0001",
            name="Initial Access",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(tactic)
        
        # Create technique
        technique = MitreTechnique(
            technique_id="T1234",
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(technique)
        
        # Associate technique with tactic
        technique.tactics.append(tactic)
        
        await async_db.commit()
        await async_db.refresh(technique)
        await async_db.refresh(tactic)
        
        assert len(technique.tactics) == 1
        assert technique.tactics[0].tactic_id == "TA0001"
        assert len(tactic.techniques) == 1
        assert tactic.techniques[0].technique_id == "T1234"
    
    @pytest.mark.asyncio
    async def test_group_technique_relationship(self, async_db: AsyncSession):
        """Test group-technique many-to-many relationship."""
        # Create group
        group = MitreGroup(
            group_id="G0001",
            name="APT1",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(group)
        
        # Create technique
        technique = MitreTechnique(
            technique_id="T1234",
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(technique)
        
        # Associate group with technique
        group.techniques.append(technique)
        
        await async_db.commit()
        await async_db.refresh(group)
        await async_db.refresh(technique)
        
        assert len(group.techniques) == 1
        assert group.techniques[0].technique_id == "T1234"
        assert len(technique.groups) == 1
        assert technique.groups[0].group_id == "G0001"


class TestMitreEnums:
    """Test MITRE enum values."""
    
    def test_mitre_domain_enum(self):
        """Test MITRE domain enum values."""
        assert MitreDomain.ENTERPRISE == "enterprise"
        assert MitreDomain.MOBILE == "mobile"
        assert MitreDomain.ICS == "ics"
    
    def test_mitre_entity_status_enum(self):
        """Test MITRE entity status enum values."""
        assert MitreEntityStatus.ACTIVE == "active"
        assert MitreEntityStatus.DEPRECATED == "deprecated"
        assert MitreEntityStatus.REVOKED == "revoked"
    
    def test_mitre_data_source_enum(self):
        """Test MITRE data source enum values."""
        assert MitreDataSourceEnum.OFFICIAL == "official"
        assert MitreDataSourceEnum.COMMUNITY == "community"
        assert MitreDataSourceEnum.CUSTOM == "custom"
