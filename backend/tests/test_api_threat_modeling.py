"""Tests for threat modeling API endpoints."""

import pytest
import json
from unittest.mock import Mock, patch
from datetime import datetime

from fastapi.testclient import TestClient
from app.main import app
from app.services.threat_modeling_service import (
    ThreatActorProfile,
    ThreatActorType,
    AttackMotivation,
    QuantitativeRiskAssessment,
    AttackSimulationResult
)


class TestThreatModelingAPI:
    """Test threat modeling API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test_token"}
    
    @pytest.fixture
    def mock_threat_actor(self):
        """Mock threat actor profile."""
        return ThreatActorProfile(
            actor_id="test_actor",
            name="Test Actor",
            actor_type=ThreatActorType.CYBERCRIMINAL,
            sophistication_level=0.7,
            motivation=AttackMotivation.FINANCIAL_GAIN,
            target_sectors=["financial"],
            preferred_techniques=[],
            resource_level=0.6,
            stealth_capability=0.5,
            persistence_level=0.6,
            known_campaigns=["Test Campaign"],
            attribution_confidence=0.8,
            last_activity=datetime.utcnow(),
            geographic_origin="Unknown"
        )
    
    @pytest.fixture
    def mock_risk_assessment(self):
        """Mock risk assessment."""
        return QuantitativeRiskAssessment(
            asset_id="test_asset",
            annual_loss_expectancy=500000.0,
            single_loss_expectancy=250000.0,
            annual_rate_of_occurrence=2.0,
            exposure_factor=0.7,
            asset_value=1000000.0,
            threat_frequency=12.0,
            vulnerability_score=0.6,
            control_effectiveness=0.7,
            residual_risk=0.08,
            risk_tolerance=0.1
        )
    
    @pytest.fixture
    def mock_simulation_result(self, mock_threat_actor):
        """Mock attack simulation result."""
        return AttackSimulationResult(
            simulation_id="test_sim_123",
            scenario_name="Test Attack Scenario",
            threat_actor=mock_threat_actor,
            success_probability=0.75,
            detection_probability=0.4,
            time_to_compromise=48.0,
            time_to_detection=72.0,
            impact_assessment={
                "confidentiality": 0.8,
                "integrity": 0.6,
                "availability": 0.7,
                "financial": 0.9,
                "reputation": 0.5,
                "regulatory": 0.7
            },
            affected_assets={"asset1", "asset2", "asset3"},
            data_exfiltrated=500.0,
            service_downtime=12.0,
            financial_impact=750000.0,
            reputation_impact=0.6,
            regulatory_violations=["GDPR", "PCI-DSS"],
            simulation_timestamp=datetime.utcnow()
        )
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_list_threat_actors(self, mock_threat_service, mock_user, client, auth_headers, mock_threat_actor):
        """Test listing threat actors."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.list_threat_actors.return_value = [mock_threat_actor]
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/threat-actors", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["actor_id"] == "test_actor"
        assert data[0]["name"] == "Test Actor"
        assert data[0]["actor_type"] == "cybercriminal"
        assert data[0]["sophistication_level"] == 0.7
        assert data[0]["threat_score"] > 0
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_get_threat_actor(self, mock_threat_service, mock_user, client, auth_headers, mock_threat_actor):
        """Test getting specific threat actor."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.get_threat_actor_profile.return_value = mock_threat_actor
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/threat-actors/test_actor", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["actor_id"] == "test_actor"
        assert data["name"] == "Test Actor"
        assert data["motivation"] == "financial_gain"
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_get_threat_actor_not_found(self, mock_threat_service, mock_user, client, auth_headers):
        """Test getting non-existent threat actor."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.get_threat_actor_profile.return_value = None
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/threat-actors/nonexistent", headers=auth_headers)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_perform_risk_assessment(self, mock_threat_service, mock_user, client, auth_headers, mock_risk_assessment):
        """Test performing risk assessment."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.perform_quantitative_risk_assessment.return_value = mock_risk_assessment
        mock_threat_service.return_value = mock_service
        
        # Make request
        request_data = {"asset_id": "test_asset"}
        response = client.post(
            "/api/v1/threat-modeling/risk-assessment",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["asset_id"] == "test_asset"
        assert data["annual_loss_expectancy"] == 500000.0
        assert data["risk_level"] == "MEDIUM"
        assert data["exceeds_tolerance"] == False
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_risk_assessment_asset_not_found(self, mock_threat_service, mock_user, client, auth_headers):
        """Test risk assessment for non-existent asset."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.perform_quantitative_risk_assessment.side_effect = ValueError("Asset not found")
        mock_threat_service.return_value = mock_service
        
        # Make request
        request_data = {"asset_id": "nonexistent_asset"}
        response = client.post(
            "/api/v1/threat-modeling/risk-assessment",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 404
        assert "Asset not found" in response.json()["detail"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_simulate_attack(self, mock_threat_service, mock_user, client, auth_headers, mock_simulation_result):
        """Test attack simulation."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.simulate_attack_scenario.return_value = mock_simulation_result
        mock_threat_service.return_value = mock_service
        
        # Make request
        request_data = {
            "threat_actor_id": "test_actor",
            "target_assets": ["asset1", "asset2"],
            "scenario_name": "Test Simulation"
        }
        response = client.post(
            "/api/v1/threat-modeling/simulate-attack",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["simulation_id"] == "test_sim_123"
        assert data["scenario_name"] == "Test Attack Scenario"
        assert data["threat_actor_id"] == "test_actor"
        assert data["success_probability"] == 0.75
        assert data["detection_probability"] == 0.4
        assert data["affected_assets_count"] == 3
        assert data["financial_impact"] == 750000.0
        assert "GDPR" in data["regulatory_violations"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_simulate_attack_invalid_actor(self, mock_threat_service, mock_user, client, auth_headers):
        """Test attack simulation with invalid threat actor."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.simulate_attack_scenario.side_effect = ValueError("Threat actor not found")
        mock_threat_service.return_value = mock_service
        
        # Make request
        request_data = {
            "threat_actor_id": "invalid_actor",
            "target_assets": ["asset1"],
            "scenario_name": "Test Simulation"
        }
        response = client.post(
            "/api/v1/threat-modeling/simulate-attack",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 404
        assert "Threat actor not found" in response.json()["detail"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_list_simulations(self, mock_threat_service, mock_user, client, auth_headers, mock_simulation_result):
        """Test listing simulations."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.list_simulations.return_value = [mock_simulation_result]
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/simulations", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["simulation_id"] == "test_sim_123"
        assert data[0]["scenario_name"] == "Test Attack Scenario"
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_get_simulation(self, mock_threat_service, mock_user, client, auth_headers, mock_simulation_result):
        """Test getting specific simulation."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.get_simulation_result.return_value = mock_simulation_result
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/simulations/test_sim_123", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["simulation_id"] == "test_sim_123"
        assert data["threat_actor_name"] == "Test Actor"
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_get_simulation_not_found(self, mock_threat_service, mock_user, client, auth_headers):
        """Test getting non-existent simulation."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.get_simulation_result.return_value = None
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/simulations/nonexistent", headers=auth_headers)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_export_threat_model(self, mock_threat_service, mock_user, client, auth_headers):
        """Test exporting threat model."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.export_threat_model.return_value = '{"test": "data"}'
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/export?format=json", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["format"] == "json"
        assert data["data"] == '{"test": "data"}'
        assert "timestamp" in data
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_export_invalid_format(self, mock_threat_service, mock_user, client, auth_headers):
        """Test exporting with invalid format."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_service.export_threat_model.side_effect = ValueError("Invalid format")
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/threat-modeling/export?format=invalid", headers=auth_headers)
        
        assert response.status_code == 400
        assert "Invalid format" in response.json()["detail"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.threat_modeling.get_threat_service')
    def test_clear_simulation_cache(self, mock_threat_service, mock_user, client, auth_headers):
        """Test clearing simulation cache."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock threat service
        mock_service = Mock()
        mock_threat_service.return_value = mock_service
        
        # Make request
        response = client.delete("/api/v1/threat-modeling/simulations/cache", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "cleared successfully" in data["message"]
        mock_service.clear_simulation_cache.assert_called_once()
    
    def test_request_validation(self, client, auth_headers):
        """Test request validation."""
        # Test missing required fields
        response = client.post(
            "/api/v1/threat-modeling/risk-assessment",
            json={},
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
        
        # Test invalid data types
        response = client.post(
            "/api/v1/threat-modeling/simulate-attack",
            json={
                "threat_actor_id": 123,  # Should be string
                "target_assets": "not_a_list",  # Should be list
            },
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
