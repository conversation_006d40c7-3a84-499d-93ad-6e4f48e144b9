"""Unit tests for asset service."""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from sqlalchemy.orm import Session

from app.db.models.asset import (
    Asset,
    AssetRelationship,
    AssetTag,
    DiscoveryJob,
    AssetProvider,
    AssetStatus,
    AssetType,
    DiscoveryJobStatus,
    DiscoverySource,
    RelationshipType,
    RiskLevel,
)
from app.schemas.asset import (
    AssetCreate,
    AssetUpdate,
    AssetSearchRequest,
    AssetRelationshipCreate,
    AssetRelationshipUpdate,
    DiscoveryJobCreate,
    DiscoveryJobUpdate,
    AssetTagCreate,
    AssetTagUpdate,
)
from app.services.asset_service import (
    AssetService,
    AssetNotFoundError,
    AssetAlreadyExistsError,
    RelationshipNotFoundError,
    DiscoveryJobNotFoundError,
)


class TestAssetService:
    """Test cases for AssetService."""

    def test_create_asset_success(self, db_session: Session):
        """Test successful asset creation."""
        service = AssetService(db_session)
        
        asset_data = AssetCreate(
            name="test-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            provider_id="i-1234567890abcdef0",
            provider_region="us-east-1",
            ip_addresses=["**********"],
            discovery_source=DiscoverySource.CLOUD_API,
            environment="production",
            owner="team-alpha",
            tags={"Environment": "prod", "Team": "alpha"}
        )
        
        asset = service.create_asset(asset_data)
        
        assert asset.id is not None
        assert asset.name == "test-server"
        assert asset.asset_type == AssetType.SERVER
        assert asset.provider == AssetProvider.AWS
        assert asset.provider_id == "i-1234567890abcdef0"
        assert asset.ip_addresses == ["**********"]
        assert asset.environment == "production"
        assert asset.owner == "team-alpha"
        assert asset.tags == {"Environment": "prod", "Team": "alpha"}
        assert asset.discovered_at is not None
        assert asset.last_seen is not None

    def test_create_asset_duplicate_provider_id(self, db_session: Session):
        """Test creating asset with duplicate provider ID."""
        service = AssetService(db_session)
        
        # Create first asset
        asset_data = AssetCreate(
            name="test-server-1",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            provider_id="i-1234567890abcdef0",
            discovery_source=DiscoverySource.CLOUD_API,
        )
        service.create_asset(asset_data)
        
        # Try to create duplicate
        duplicate_data = AssetCreate(
            name="test-server-2",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            provider_id="i-1234567890abcdef0",
            discovery_source=DiscoverySource.CLOUD_API,
        )
        
        with pytest.raises(AssetAlreadyExistsError):
            service.create_asset(duplicate_data)

    def test_get_asset_success(self, db_session: Session):
        """Test successful asset retrieval."""
        service = AssetService(db_session)
        
        # Create asset
        asset_data = AssetCreate(
            name="test-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        created_asset = service.create_asset(asset_data)
        
        # Retrieve asset
        retrieved_asset = service.get_asset(created_asset.id)
        
        assert retrieved_asset.id == created_asset.id
        assert retrieved_asset.name == "test-server"

    def test_get_asset_not_found(self, db_session: Session):
        """Test asset retrieval with non-existent ID."""
        service = AssetService(db_session)
        
        with pytest.raises(AssetNotFoundError):
            service.get_asset(uuid.uuid4())

    def test_update_asset_success(self, db_session: Session):
        """Test successful asset update."""
        service = AssetService(db_session)
        
        # Create asset
        asset_data = AssetCreate(
            name="test-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
            environment="staging",
        )
        asset = service.create_asset(asset_data)
        
        # Update asset
        update_data = AssetUpdate(
            environment="production",
            owner="team-beta",
            tags={"Environment": "prod"}
        )
        updated_asset = service.update_asset(asset.id, update_data)
        
        assert updated_asset.environment == "production"
        assert updated_asset.owner == "team-beta"
        assert updated_asset.tags == {"Environment": "prod"}

    def test_delete_asset_success(self, db_session: Session):
        """Test successful asset deletion."""
        service = AssetService(db_session)
        
        # Create asset
        asset_data = AssetCreate(
            name="test-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        asset = service.create_asset(asset_data)
        
        # Delete asset
        result = service.delete_asset(asset.id)
        
        assert result is True
        
        # Verify asset is deleted
        with pytest.raises(AssetNotFoundError):
            service.get_asset(asset.id)

    def test_search_assets_by_name(self, db_session: Session):
        """Test asset search by name."""
        service = AssetService(db_session)
        
        # Create test assets
        assets_data = [
            AssetCreate(
                name="web-server-1",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
            AssetCreate(
                name="db-server-1",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
            AssetCreate(
                name="web-server-2",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AZURE,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
        ]
        
        for asset_data in assets_data:
            service.create_asset(asset_data)
        
        # Search for web servers
        search_request = AssetSearchRequest(
            query="web-server",
            page=1,
            size=10
        )
        
        assets, total = service.search_assets(search_request)
        
        assert total == 2
        assert len(assets) == 2
        assert all("web-server" in asset.name for asset in assets)

    def test_search_assets_by_provider(self, db_session: Session):
        """Test asset search by provider."""
        service = AssetService(db_session)
        
        # Create test assets
        assets_data = [
            AssetCreate(
                name="aws-server",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
            AssetCreate(
                name="azure-server",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AZURE,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
        ]
        
        for asset_data in assets_data:
            service.create_asset(asset_data)
        
        # Search for AWS assets
        search_request = AssetSearchRequest(
            providers=[AssetProvider.AWS],
            page=1,
            size=10
        )
        
        assets, total = service.search_assets(search_request)
        
        assert total == 1
        assert len(assets) == 1
        assert assets[0].provider == AssetProvider.AWS

    def test_create_relationship_success(self, db_session: Session):
        """Test successful relationship creation."""
        service = AssetService(db_session)
        
        # Create source and target assets
        source_data = AssetCreate(
            name="web-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        source_asset = service.create_asset(source_data)
        
        target_data = AssetCreate(
            name="database",
            asset_type=AssetType.CLOUD_DATABASE,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        target_asset = service.create_asset(target_data)
        
        # Create relationship
        relationship_data = AssetRelationshipCreate(
            source_asset_id=source_asset.id,
            target_asset_id=target_asset.id,
            relationship_type=RelationshipType.DEPENDS_ON,
            protocol="TCP",
            port=5432,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        
        relationship = service.create_relationship(relationship_data)
        
        assert relationship.id is not None
        assert relationship.source_asset_id == source_asset.id
        assert relationship.target_asset_id == target_asset.id
        assert relationship.relationship_type == RelationshipType.DEPENDS_ON
        assert relationship.protocol == "TCP"
        assert relationship.port == 5432

    def test_get_asset_relationships(self, db_session: Session):
        """Test getting asset relationships."""
        service = AssetService(db_session)
        
        # Create assets
        source_data = AssetCreate(
            name="web-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        source_asset = service.create_asset(source_data)
        
        target_data = AssetCreate(
            name="database",
            asset_type=AssetType.CLOUD_DATABASE,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        target_asset = service.create_asset(target_data)
        
        # Create relationship
        relationship_data = AssetRelationshipCreate(
            source_asset_id=source_asset.id,
            target_asset_id=target_asset.id,
            relationship_type=RelationshipType.DEPENDS_ON,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        service.create_relationship(relationship_data)
        
        # Get relationships
        relationships = service.get_asset_relationships(source_asset.id)
        
        assert len(relationships) == 1
        assert relationships[0].source_asset_id == source_asset.id
        assert relationships[0].target_asset_id == target_asset.id

    def test_add_asset_tag_success(self, db_session: Session):
        """Test successful asset tag addition."""
        service = AssetService(db_session)
        
        # Create asset
        asset_data = AssetCreate(
            name="test-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        asset = service.create_asset(asset_data)
        
        # Add tag
        tag_data = AssetTagCreate(
            asset_id=asset.id,
            key="Environment",
            value="production",
            source="manual"
        )
        
        tag = service.add_asset_tag(tag_data)
        
        assert tag.id is not None
        assert tag.asset_id == asset.id
        assert tag.key == "Environment"
        assert tag.value == "production"
        assert tag.source == "manual"

    def test_create_discovery_job_success(self, db_session: Session):
        """Test successful discovery job creation."""
        service = AssetService(db_session)
        
        job_data = DiscoveryJobCreate(
            name="AWS Discovery",
            description="Discover AWS resources",
            job_type="aws_discovery",
            configuration={"region": "us-east-1"},
            parameters={"include_stopped": False}
        )
        
        job = service.create_discovery_job(job_data)
        
        assert job.id is not None
        assert job.name == "AWS Discovery"
        assert job.job_type == "aws_discovery"
        assert job.status == DiscoveryJobStatus.PENDING
        assert job.configuration == {"region": "us-east-1"}

    def test_start_discovery_job_success(self, db_session: Session):
        """Test successful discovery job start."""
        service = AssetService(db_session)
        
        # Create job
        job_data = DiscoveryJobCreate(
            name="AWS Discovery",
            job_type="aws_discovery"
        )
        job = service.create_discovery_job(job_data)
        
        # Start job
        started_job = service.start_discovery_job(job.id, executor="test-user")
        
        assert started_job.status == DiscoveryJobStatus.RUNNING
        assert started_job.started_at is not None
        assert started_job.executor == "test-user"

    def test_complete_discovery_job_success(self, db_session: Session):
        """Test successful discovery job completion."""
        service = AssetService(db_session)
        
        # Create and start job
        job_data = DiscoveryJobCreate(
            name="AWS Discovery",
            job_type="aws_discovery"
        )
        job = service.create_discovery_job(job_data)
        service.start_discovery_job(job.id)
        
        # Complete job
        completed_job = service.complete_discovery_job(
            job.id,
            assets_discovered=10,
            assets_updated=5,
            relationships_discovered=15,
            errors_count=0
        )
        
        assert completed_job.status == DiscoveryJobStatus.COMPLETED
        assert completed_job.completed_at is not None
        assert completed_job.assets_discovered == 10
        assert completed_job.assets_updated == 5
        assert completed_job.relationships_discovered == 15
        assert completed_job.errors_count == 0

    def test_get_asset_statistics(self, db_session: Session):
        """Test asset statistics generation."""
        service = AssetService(db_session)
        
        # Create test assets
        assets_data = [
            AssetCreate(
                name="aws-server",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="production",
                risk_level=RiskLevel.HIGH
            ),
            AssetCreate(
                name="azure-database",
                asset_type=AssetType.CLOUD_DATABASE,
                provider=AssetProvider.AZURE,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="staging",
                risk_level=RiskLevel.MEDIUM
            ),
        ]
        
        for asset_data in assets_data:
            service.create_asset(asset_data)
        
        # Get statistics
        stats = service.get_asset_statistics()
        
        assert stats['total_assets'] == 2
        assert stats['assets_by_type']['server'] == 1
        assert stats['assets_by_type']['cloud_database'] == 1
        assert stats['assets_by_provider']['aws'] == 1
        assert stats['assets_by_provider']['azure'] == 1
        assert stats['assets_by_environment']['production'] == 1
        assert stats['assets_by_environment']['staging'] == 1
