"""
Tests for Real-time Monitoring Service

Tests the real-time monitoring functionality including WebSocket connections,
event broadcasting, and dashboard data aggregation.
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from app.services.realtime_monitoring_service import (
    RealTimeMonitoringService,
    RealTimeEvent,
    ThreatLevel,
    EventType,
    ThreatMapData,
    RiskHeatmapData,
    AttackTimelineEvent
)
from app.models.asset import Asset


class TestRealTimeMonitoringService:
    """Test suite for RealTimeMonitoringService"""

    @pytest.fixture
    def monitoring_service(self):
        """Create a monitoring service instance for testing"""
        return RealTimeMonitoringService()

    @pytest.fixture
    def sample_event(self):
        """Create a sample real-time event"""
        return RealTimeEvent(
            id="test-event-1",
            event_type=EventType.THREAT_DETECTED,
            timestamp=datetime.utcnow(),
            severity=ThreatLevel.HIGH,
            title="Test Threat Detected",
            description="A test threat was detected on the system",
            asset_id=1,
            asset_name="test-server",
            mitre_technique_id="T1190",
            risk_score=8.5,
            metadata={"source": "test", "confidence": 0.95}
        )

    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket connection"""
        websocket = AsyncMock()
        websocket.send_text = AsyncMock()
        return websocket

    @pytest.mark.asyncio
    async def test_add_remove_connection(self, monitoring_service, mock_websocket):
        """Test adding and removing WebSocket connections"""
        # Initially no connections
        assert len(monitoring_service.active_connections) == 0
        
        # Add connection
        await monitoring_service.add_connection(mock_websocket)
        assert len(monitoring_service.active_connections) == 1
        assert mock_websocket in monitoring_service.active_connections
        
        # Remove connection
        await monitoring_service.remove_connection(mock_websocket)
        assert len(monitoring_service.active_connections) == 0
        assert mock_websocket not in monitoring_service.active_connections

    @pytest.mark.asyncio
    async def test_broadcast_event(self, monitoring_service, mock_websocket, sample_event):
        """Test broadcasting events to connected clients"""
        # Add connection
        await monitoring_service.add_connection(mock_websocket)
        
        # Broadcast event
        await monitoring_service.broadcast_event(sample_event)
        
        # Verify message was sent
        mock_websocket.send_text.assert_called_once()
        
        # Verify message content
        call_args = mock_websocket.send_text.call_args[0][0]
        message = json.loads(call_args)
        
        assert message["type"] == "realtime_event"
        assert message["data"]["id"] == sample_event.id
        assert message["data"]["event_type"] == sample_event.event_type.value
        assert message["data"]["severity"] == sample_event.severity.value
        
        # Verify event was added to buffer
        assert len(monitoring_service.event_buffer) == 1
        assert monitoring_service.event_buffer[0] == sample_event

    @pytest.mark.asyncio
    async def test_broadcast_event_no_connections(self, monitoring_service, sample_event):
        """Test broadcasting events with no active connections"""
        # Broadcast event with no connections
        await monitoring_service.broadcast_event(sample_event)
        
        # Event should still be added to buffer
        assert len(monitoring_service.event_buffer) == 1
        assert monitoring_service.event_buffer[0] == sample_event

    @pytest.mark.asyncio
    async def test_broadcast_event_failed_connection(self, monitoring_service, sample_event):
        """Test broadcasting events with failed connections"""
        # Create mock websocket that raises exception
        failed_websocket = AsyncMock()
        failed_websocket.send_text.side_effect = Exception("Connection failed")
        
        # Add connection
        await monitoring_service.add_connection(failed_websocket)
        assert len(monitoring_service.active_connections) == 1
        
        # Broadcast event
        await monitoring_service.broadcast_event(sample_event)
        
        # Failed connection should be removed
        assert len(monitoring_service.active_connections) == 0

    @pytest.mark.asyncio
    async def test_get_dashboard_overview(self, monitoring_service):
        """Test getting dashboard overview data"""
        # Mock database session
        mock_db = Mock()
        
        # Mock asset queries
        mock_query = Mock()
        mock_query.filter.return_value.count.return_value = 100  # total assets
        mock_db.query.return_value = mock_query
        
        # Add some events to buffer
        events = [
            RealTimeEvent(
                id=f"event-{i}",
                event_type=EventType.THREAT_DETECTED,
                timestamp=datetime.utcnow(),
                severity=ThreatLevel.HIGH if i < 5 else ThreatLevel.LOW,
                title=f"Event {i}",
                description=f"Test event {i}",
                mitre_technique_id=f"T{1000 + i}"
            )
            for i in range(10)
        ]
        
        for event in events:
            await monitoring_service.broadcast_event(event)
        
        # Get dashboard overview
        overview = await monitoring_service.get_dashboard_overview(mock_db)
        
        # Verify structure
        assert "overview" in overview
        assert "recent_events" in overview
        assert "mitre_distribution" in overview
        assert "threat_trends" in overview
        
        # Verify overview data
        assert overview["overview"]["total_assets"] == 100
        assert len(overview["recent_events"]) == 10

    @pytest.mark.asyncio
    async def test_get_threat_map_data(self, monitoring_service):
        """Test getting threat map data"""
        mock_db = Mock()
        
        threat_data = await monitoring_service.get_threat_map_data(mock_db)
        
        # Verify data structure
        assert isinstance(threat_data, list)
        assert len(threat_data) > 0
        
        # Verify first threat data
        first_threat = threat_data[0]
        assert hasattr(first_threat, 'country')
        assert hasattr(first_threat, 'country_code')
        assert hasattr(first_threat, 'threat_count')
        assert hasattr(first_threat, 'severity_distribution')
        assert hasattr(first_threat, 'top_techniques')
        assert hasattr(first_threat, 'coordinates')

    @pytest.mark.asyncio
    async def test_get_risk_heatmap_data(self, monitoring_service):
        """Test getting risk heatmap data"""
        # Mock database session and assets
        mock_db = Mock()
        
        # Create mock assets
        mock_assets = []
        for i in range(5):
            asset = Mock(spec=Asset)
            asset.id = i + 1
            asset.name = f"test-asset-{i + 1}"
            asset.asset_type = "server"
            asset.risk_score = 5.0 + i
            asset.updated_at = datetime.utcnow()
            mock_assets.append(asset)
        
        # Mock query chain
        mock_query = Mock()
        mock_query.filter.return_value.order_by.return_value.limit.return_value.all.return_value = mock_assets
        mock_db.query.return_value = mock_query
        
        # Get heatmap data
        heatmap_data = await monitoring_service.get_risk_heatmap_data(mock_db)
        
        # Verify data structure
        assert isinstance(heatmap_data, list)
        assert len(heatmap_data) == 5
        
        # Verify first heatmap item
        first_item = heatmap_data[0]
        assert hasattr(first_item, 'asset_id')
        assert hasattr(first_item, 'asset_name')
        assert hasattr(first_item, 'asset_type')
        assert hasattr(first_item, 'risk_score')
        assert hasattr(first_item, 'threat_count')
        assert hasattr(first_item, 'mitre_techniques')

    @pytest.mark.asyncio
    async def test_get_attack_timeline(self, monitoring_service):
        """Test getting attack timeline data"""
        mock_db = Mock()
        
        # Add events with MITRE techniques to buffer
        events = []
        for i in range(5):
            event = RealTimeEvent(
                id=f"timeline-event-{i}",
                event_type=EventType.MITRE_CORRELATION,
                timestamp=datetime.utcnow() - timedelta(hours=i),
                severity=ThreatLevel.MEDIUM,
                title=f"Timeline Event {i}",
                description=f"Timeline test event {i}",
                asset_name=f"asset-{i}",
                mitre_technique_id=f"T{1200 + i}"
            )
            events.append(event)
            await monitoring_service.broadcast_event(event)
        
        # Mock MITRE service
        with patch.object(monitoring_service.mitre_service, 'get_technique_by_id') as mock_get_technique:
            # Mock technique response
            mock_technique = Mock()
            mock_technique.name = "Test Technique"
            mock_technique.tactic = Mock()
            mock_technique.tactic.name = "Test Tactic"
            mock_get_technique.return_value = mock_technique
            
            # Get timeline data
            timeline_data = await monitoring_service.get_attack_timeline(mock_db, hours=24)
            
            # Verify data structure
            assert isinstance(timeline_data, list)
            assert len(timeline_data) == 5
            
            # Verify timeline events are sorted by timestamp (newest first)
            timestamps = [event.timestamp for event in timeline_data]
            assert timestamps == sorted(timestamps, reverse=True)

    @pytest.mark.asyncio
    async def test_event_buffer_size_limit(self, monitoring_service):
        """Test that event buffer respects size limit"""
        # Set small buffer size for testing
        monitoring_service.max_buffer_size = 5
        
        # Add more events than buffer size
        for i in range(10):
            event = RealTimeEvent(
                id=f"buffer-test-{i}",
                event_type=EventType.SYSTEM_ALERT,
                timestamp=datetime.utcnow(),
                severity=ThreatLevel.LOW,
                title=f"Buffer Test {i}",
                description=f"Buffer test event {i}"
            )
            await monitoring_service.broadcast_event(event)
        
        # Verify buffer size is limited
        assert len(monitoring_service.event_buffer) == 5
        
        # Verify newest events are kept (events 5-9)
        event_ids = [event.id for event in monitoring_service.event_buffer]
        assert "buffer-test-9" in event_ids
        assert "buffer-test-0" not in event_ids

    def test_real_time_event_to_dict(self, sample_event):
        """Test RealTimeEvent serialization to dictionary"""
        event_dict = sample_event.to_dict()
        
        # Verify all fields are present
        assert event_dict["id"] == sample_event.id
        assert event_dict["event_type"] == sample_event.event_type.value
        assert event_dict["severity"] == sample_event.severity.value
        assert event_dict["title"] == sample_event.title
        assert event_dict["description"] == sample_event.description
        assert event_dict["asset_id"] == sample_event.asset_id
        assert event_dict["asset_name"] == sample_event.asset_name
        assert event_dict["mitre_technique_id"] == sample_event.mitre_technique_id
        assert event_dict["risk_score"] == sample_event.risk_score
        assert event_dict["metadata"] == sample_event.metadata
        
        # Verify timestamp is ISO format string
        assert isinstance(event_dict["timestamp"], str)
        datetime.fromisoformat(event_dict["timestamp"].replace('Z', '+00:00'))

    @pytest.mark.asyncio
    async def test_simulate_real_time_events(self, monitoring_service):
        """Test event simulation functionality"""
        # Mock database with assets and techniques
        mock_db = Mock()
        
        # Mock assets
        mock_assets = []
        for i in range(3):
            asset = Mock(spec=Asset)
            asset.id = i + 1
            asset.name = f"sim-asset-{i + 1}"
            mock_assets.append(asset)
        
        mock_query = Mock()
        mock_query.filter.return_value.limit.return_value.all.return_value = mock_assets
        mock_db.query.return_value = mock_query
        
        # Mock MITRE techniques
        mock_techniques = []
        for i in range(3):
            technique = Mock()
            technique.external_id = f"T{1300 + i}"
            technique.name = f"Simulated Technique {i}"
            mock_techniques.append(technique)
        
        # Mock MITRE service
        with patch.object(monitoring_service.mitre_service, 'get_all_techniques') as mock_get_techniques:
            mock_get_techniques.return_value = mock_techniques
            
            # Simulate events
            await monitoring_service.simulate_real_time_events(mock_db)
            
            # Verify events were generated
            assert len(monitoring_service.event_buffer) > 0
            
            # Verify event structure
            for event in monitoring_service.event_buffer:
                assert event.id is not None
                assert event.event_type in EventType
                assert event.severity in ThreatLevel
                assert event.timestamp is not None
                assert event.title is not None
                assert event.description is not None
