"""Tests for authentication service."""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from app.services.auth_service import AuthService, AuthenticationError
from app.db.models.user import User, UserSession
from app.db.models.auth import LoginAttempt, MFADevice
from app.schemas.user import UserCreate
from app.services.user_service import UserService


class TestAuthService:
    """Test authentication service functionality."""
    
    def test_authenticate_user_success(self, db_session, auth_service, test_user):
        """Test successful user authentication."""
        username = test_user.username
        password = "TestPassword123!"
        
        user, auth_result = auth_service.authenticate_user(
            username=username,
            password=password,
            ip_address="127.0.0.1",
            user_agent="test-client"
        )
        
        assert user is not None
        assert user.id == test_user.id
        assert auth_result['success'] is True
        assert auth_result['user'] == user
        assert auth_result['session_id'] is not None
        assert auth_result['requires_mfa'] is False
        assert auth_result['error'] is None
    
    def test_authenticate_user_invalid_username(self, db_session, auth_service):
        """Test authentication with invalid username."""
        user, auth_result = auth_service.authenticate_user(
            username="nonexistent",
            password="password",
            ip_address="127.0.0.1"
        )
        
        assert user is None
        assert auth_result['success'] is False
        assert auth_result['error'] == "Invalid credentials"
        
        # Check login attempt was recorded
        login_attempt = db_session.query(LoginAttempt).filter(
            LoginAttempt.username == "nonexistent"
        ).first()
        assert login_attempt is not None
        assert login_attempt.success is False
        assert login_attempt.failure_reason == "invalid_username"
    
    def test_authenticate_user_invalid_password(self, db_session, auth_service, test_user):
        """Test authentication with invalid password."""
        user, auth_result = auth_service.authenticate_user(
            username=test_user.username,
            password="wrongpassword",
            ip_address="127.0.0.1"
        )
        
        assert user is None
        assert auth_result['success'] is False
        assert auth_result['error'] == "Invalid credentials"
        
        # Check login attempt was recorded
        login_attempt = db_session.query(LoginAttempt).filter(
            LoginAttempt.username == test_user.username
        ).first()
        assert login_attempt is not None
        assert login_attempt.success is False
        assert login_attempt.failure_reason == "invalid_password"
        
        # Check failed login attempts incremented
        db_session.refresh(test_user)
        assert test_user.failed_login_attempts > 0
    
    def test_authenticate_user_inactive_account(self, db_session, auth_service, test_user):
        """Test authentication with inactive account."""
        # Deactivate user
        test_user.is_active = False
        db_session.commit()
        
        user, auth_result = auth_service.authenticate_user(
            username=test_user.username,
            password="TestPassword123!",
            ip_address="127.0.0.1"
        )
        
        assert user is None
        assert auth_result['success'] is False
        assert auth_result['error'] == "Account is inactive"
    
    def test_authenticate_user_locked_account(self, db_session, auth_service, test_user):
        """Test authentication with locked account."""
        # Lock user account
        test_user.lock_account()
        db_session.commit()
        
        user, auth_result = auth_service.authenticate_user(
            username=test_user.username,
            password="TestPassword123!",
            ip_address="127.0.0.1"
        )
        
        assert user is None
        assert auth_result['success'] is False
        assert auth_result['error'] == "Account is locked"
    
    def test_authenticate_user_with_email(self, db_session, auth_service, test_user):
        """Test authentication using email instead of username."""
        user, auth_result = auth_service.authenticate_user(
            username=test_user.email,
            password="TestPassword123!",
            ip_address="127.0.0.1"
        )
        
        assert user is not None
        assert user.id == test_user.id
        assert auth_result['success'] is True
    
    def test_create_user_session(self, db_session, auth_service, test_user):
        """Test user session creation."""
        session = auth_service._create_user_session(
            user=test_user,
            ip_address="127.0.0.1",
            user_agent="test-client"
        )
        
        assert session is not None
        assert session.user_id == test_user.id
        assert session.ip_address == "127.0.0.1"
        assert session.user_agent == "test-client"
        assert session.is_active is True
        assert session.expires_at > datetime.utcnow()
        assert session.session_token is not None
        assert session.refresh_token is not None
    
    def test_create_user_session_remember_me(self, db_session, auth_service, test_user):
        """Test user session creation with remember me option."""
        session = auth_service._create_user_session(
            user=test_user,
            ip_address="127.0.0.1",
            user_agent="test-client",
            remember_me=True
        )
        
        # Remember me should create longer session
        expected_expiry = datetime.utcnow() + timedelta(days=30)
        assert session.expires_at > datetime.utcnow() + timedelta(days=29)
        assert session.expires_at < expected_expiry + timedelta(minutes=1)
    
    def test_create_tokens(self, db_session, auth_service, test_user):
        """Test JWT token creation."""
        session = auth_service._create_user_session(test_user)
        tokens = auth_service.create_tokens(test_user, session)
        
        assert 'access_token' in tokens
        assert 'refresh_token' in tokens
        assert 'token_type' in tokens
        assert 'expires_in' in tokens
        
        assert tokens['token_type'] == 'bearer'
        assert tokens['expires_in'] > 0
        assert len(tokens['access_token']) > 0
        assert len(tokens['refresh_token']) > 0
    
    def test_refresh_access_token_success(self, db_session, auth_service, test_user):
        """Test successful token refresh."""
        # Create initial session and tokens
        session = auth_service._create_user_session(test_user)
        tokens = auth_service.create_tokens(test_user, session)
        
        # Update session with refresh token
        session.refresh_token = tokens['refresh_token']
        db_session.commit()
        
        # Refresh token
        new_tokens = auth_service.refresh_access_token(tokens['refresh_token'])
        
        assert new_tokens is not None
        assert 'access_token' in new_tokens
        assert 'refresh_token' in new_tokens
        assert new_tokens['access_token'] != tokens['access_token']
    
    def test_refresh_access_token_invalid_token(self, auth_service):
        """Test token refresh with invalid token."""
        result = auth_service.refresh_access_token("invalid_token")
        assert result is None
    
    def test_logout_user_single_session(self, db_session, auth_service, test_user):
        """Test user logout for single session."""
        # Create session
        session = auth_service._create_user_session(test_user)
        session_token = session.session_token
        db_session.commit()
        
        # Logout
        success = auth_service.logout_user(session_token)
        
        assert success is True
        
        # Check session is terminated
        db_session.refresh(session)
        assert session.is_active is False
        assert session.logged_out_at is not None
        assert session.logout_reason == "user_logout"
    
    def test_logout_user_all_sessions(self, db_session, auth_service, test_user):
        """Test user logout for all sessions."""
        # Create multiple sessions
        session1 = auth_service._create_user_session(test_user)
        session2 = auth_service._create_user_session(test_user)
        db_session.commit()
        
        # Logout all sessions
        success = auth_service.logout_user(session1.session_token, all_sessions=True)
        
        assert success is True
        
        # Check all sessions are terminated
        db_session.refresh(session1)
        db_session.refresh(session2)
        assert session1.is_active is False
        assert session2.is_active is False
        assert session1.logout_reason == "user_logout_all"
        assert session2.logout_reason == "user_logout_all"
    
    def test_logout_user_invalid_session(self, auth_service):
        """Test logout with invalid session token."""
        success = auth_service.logout_user("invalid_session_token")
        assert success is False
    
    def test_validate_session_success(self, db_session, auth_service, test_user):
        """Test successful session validation."""
        # Create session
        session = auth_service._create_user_session(test_user)
        db_session.commit()
        
        # Validate session
        validated_user = auth_service.validate_session(session.session_token)
        
        assert validated_user is not None
        assert validated_user.id == test_user.id
        
        # Check last activity was updated
        db_session.refresh(session)
        assert session.last_activity_at > session.created_at
    
    def test_validate_session_expired(self, db_session, auth_service, test_user):
        """Test validation of expired session."""
        # Create expired session
        session = auth_service._create_user_session(test_user)
        session.expires_at = datetime.utcnow() - timedelta(hours=1)
        db_session.commit()
        
        # Validate session
        validated_user = auth_service.validate_session(session.session_token)
        
        assert validated_user is None
    
    def test_validate_session_inactive_user(self, db_session, auth_service, test_user):
        """Test validation of session for inactive user."""
        # Create session
        session = auth_service._create_user_session(test_user)
        db_session.commit()
        
        # Deactivate user
        test_user.is_active = False
        db_session.commit()
        
        # Validate session
        validated_user = auth_service.validate_session(session.session_token)
        
        assert validated_user is None
        
        # Check session was terminated
        db_session.refresh(session)
        assert session.is_active is False
        assert session.logout_reason == "user_inactive"
    
    def test_validate_session_invalid_token(self, auth_service):
        """Test validation of invalid session token."""
        validated_user = auth_service.validate_session("invalid_token")
        assert validated_user is None
    
    def test_cleanup_user_sessions(self, db_session, auth_service, test_user):
        """Test cleanup of old user sessions."""
        # Create multiple sessions (more than the limit)
        sessions = []
        for i in range(7):  # Assuming max is 5
            session = auth_service._create_user_session(test_user)
            sessions.append(session)
        
        db_session.commit()
        
        # Trigger cleanup by creating another session
        auth_service._cleanup_user_sessions(test_user.id)
        
        # Check that old sessions were terminated
        active_sessions = db_session.query(UserSession).filter(
            UserSession.user_id == test_user.id,
            UserSession.is_active == True
        ).count()
        
        # Should be within the limit
        assert active_sessions <= 5  # MAX_CONCURRENT_SESSIONS_PER_USER


class TestMFAFunctionality:
    """Test MFA-related functionality."""
    
    def test_authenticate_user_mfa_required_no_code(self, db_session, auth_service, test_user):
        """Test authentication when MFA is required but no code provided."""
        # Enable MFA for user
        test_user.mfa_enabled = True
        
        # Create MFA device
        mfa_device = MFADevice(
            user_id=test_user.id,
            device_name="Test Device",
            device_type="totp",
            secret_key="test_secret",
            is_active=True,
            is_verified=True
        )
        db_session.add(mfa_device)
        db_session.commit()
        
        user, auth_result = auth_service.authenticate_user(
            username=test_user.username,
            password="TestPassword123!",
            ip_address="127.0.0.1"
        )
        
        assert user is None
        assert auth_result['success'] is False
        assert auth_result['requires_mfa'] is True
        assert "totp" in auth_result['mfa_methods']
        assert auth_result['error'] == "MFA verification required"
    
    @patch('app.services.auth_service.AuthService._verify_totp_code')
    def test_authenticate_user_mfa_success(self, mock_verify_totp, db_session, auth_service, test_user):
        """Test successful authentication with MFA."""
        # Mock TOTP verification to return True
        mock_verify_totp.return_value = True
        
        # Enable MFA for user
        test_user.mfa_enabled = True
        
        # Create MFA device
        mfa_device = MFADevice(
            user_id=test_user.id,
            device_name="Test Device",
            device_type="totp",
            secret_key="test_secret",
            is_active=True,
            is_verified=True
        )
        db_session.add(mfa_device)
        db_session.commit()
        
        user, auth_result = auth_service.authenticate_user(
            username=test_user.username,
            password="TestPassword123!",
            ip_address="127.0.0.1",
            mfa_code="123456"
        )
        
        assert user is not None
        assert auth_result['success'] is True
        assert auth_result['requires_mfa'] is False
    
    @patch('app.services.auth_service.AuthService._verify_totp_code')
    def test_authenticate_user_mfa_invalid_code(self, mock_verify_totp, db_session, auth_service, test_user):
        """Test authentication with invalid MFA code."""
        # Mock TOTP verification to return False
        mock_verify_totp.return_value = False
        
        # Enable MFA for user
        test_user.mfa_enabled = True
        
        # Create MFA device
        mfa_device = MFADevice(
            user_id=test_user.id,
            device_name="Test Device",
            device_type="totp",
            secret_key="test_secret",
            is_active=True,
            is_verified=True
        )
        db_session.add(mfa_device)
        db_session.commit()
        
        user, auth_result = auth_service.authenticate_user(
            username=test_user.username,
            password="TestPassword123!",
            ip_address="127.0.0.1",
            mfa_code="invalid"
        )
        
        assert user is None
        assert auth_result['success'] is False
        assert auth_result['error'] == "Invalid MFA code"
    
    def test_verify_mfa_code_backup_code(self, db_session, auth_service, test_user):
        """Test MFA verification using backup code."""
        # Create MFA device with backup codes
        mfa_device = MFADevice(
            user_id=test_user.id,
            device_name="Test Device",
            device_type="totp",
            secret_key="test_secret",
            is_active=True,
            is_verified=True,
            backup_codes=["BACKUP123", "BACKUP456"],
            backup_codes_used=[]
        )
        db_session.add(mfa_device)
        db_session.commit()
        
        # Verify backup code
        result = auth_service._verify_mfa_code(test_user.id, "BACKUP123")
        
        assert result is True
        
        # Check backup code was marked as used
        db_session.refresh(mfa_device)
        assert "BACKUP123" in mfa_device.backup_codes_used
    
    def test_verify_mfa_code_used_backup_code(self, db_session, auth_service, test_user):
        """Test MFA verification with already used backup code."""
        # Create MFA device with used backup code
        mfa_device = MFADevice(
            user_id=test_user.id,
            device_name="Test Device",
            device_type="totp",
            secret_key="test_secret",
            is_active=True,
            is_verified=True,
            backup_codes=["BACKUP123", "BACKUP456"],
            backup_codes_used=["BACKUP123"]
        )
        db_session.add(mfa_device)
        db_session.commit()
        
        # Try to use already used backup code
        result = auth_service._verify_mfa_code(test_user.id, "BACKUP123")
        
        assert result is False


class TestErrorHandling:
    """Test error handling in auth service."""
    
    def test_authenticate_user_database_error(self, auth_service):
        """Test authentication with database error."""
        # Mock database session to raise exception
        with patch.object(auth_service.db, 'query', side_effect=Exception("Database error")):
            user, auth_result = auth_service.authenticate_user(
                username="testuser",
                password="password"
            )
            
            assert user is None
            assert auth_result['success'] is False
            assert auth_result['error'] == "Authentication failed"
    
    def test_logout_user_database_error(self, auth_service):
        """Test logout with database error."""
        with patch.object(auth_service.db, 'query', side_effect=Exception("Database error")):
            success = auth_service.logout_user("session_token")
            assert success is False
