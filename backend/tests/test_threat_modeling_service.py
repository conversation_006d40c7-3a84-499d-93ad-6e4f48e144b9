"""Tests for threat modeling service."""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from app.services.threat_modeling_service import (
    ThreatModelingService,
    ThreatActorProfile,
    ThreatActorType,
    AttackMotivation,
    QuantitativeRiskAssessment,
    AttackSimulationResult
)
from app.services.attack_path_analyzer import AttackPathAnalyzer
from app.services.graph_engine import BlastRadiusResult


class TestThreatModelingService:
    """Test threat modeling service."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()
    
    @pytest.fixture
    def threat_service(self, mock_db):
        """Create threat modeling service."""
        return ThreatModelingService(mock_db)
    
    @pytest.fixture
    def sample_asset_metadata(self):
        """Sample asset metadata for testing."""
        return {
            "web_server": {
                "asset_type": "server",
                "business_criticality": "high",
                "public_facing": True,
                "contains_pii": False,
                "security_config_score": 0.7,
                "patch_level": 0.8,
                "firewall_protected": True,
                "monitoring_enabled": True,
                "risk_score": 75
            },
            "database": {
                "asset_type": "database",
                "business_criticality": "critical",
                "public_facing": False,
                "contains_pii": True,
                "data_classification": "restricted",
                "security_config_score": 0.8,
                "patch_level": 0.9,
                "firewall_protected": True,
                "monitoring_enabled": True,
                "encryption_enabled": True,
                "risk_score": 90
            }
        }
    
    def test_threat_actor_profiles_loaded(self, threat_service):
        """Test that threat actor profiles are loaded."""
        assert len(threat_service.threat_actors) > 0
        assert "apt29" in threat_service.threat_actors
        assert "apt28" in threat_service.threat_actors
        assert "fin7" in threat_service.threat_actors
        assert "malicious_insider" in threat_service.threat_actors
        
        # Check APT29 profile
        apt29 = threat_service.threat_actors["apt29"]
        assert apt29.name == "APT29 (Cozy Bear)"
        assert apt29.actor_type == ThreatActorType.NATION_STATE
        assert apt29.motivation == AttackMotivation.ESPIONAGE
        assert apt29.sophistication_level >= 0.9
        assert apt29.threat_score > 80
    
    def test_risk_parameters_loaded(self, threat_service):
        """Test that risk parameters are loaded."""
        assert "asset_values" in threat_service.risk_parameters
        assert "exposure_factors" in threat_service.risk_parameters
        assert "threat_frequencies" in threat_service.risk_parameters
        assert "control_effectiveness" in threat_service.risk_parameters
        
        # Check asset values
        assert threat_service.risk_parameters["asset_values"]["database"] == 1000000
        assert threat_service.risk_parameters["asset_values"]["server"] == 500000
    
    @pytest.mark.asyncio
    async def test_quantitative_risk_assessment(self, threat_service, sample_asset_metadata):
        """Test quantitative risk assessment."""
        # Mock the attack analyzer
        with patch.object(threat_service.attack_analyzer, 'initialize_from_database'):
            threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
            
            # Test database assessment
            assessment = await threat_service.perform_quantitative_risk_assessment("database")
            
            assert isinstance(assessment, QuantitativeRiskAssessment)
            assert assessment.asset_id == "database"
            assert assessment.asset_value > 0
            assert assessment.annual_loss_expectancy > 0
            assert assessment.single_loss_expectancy > 0
            assert assessment.annual_rate_of_occurrence > 0
            assert 0.0 <= assessment.exposure_factor <= 1.0
            assert 0.0 <= assessment.vulnerability_score <= 1.0
            assert 0.0 <= assessment.control_effectiveness <= 1.0
            assert assessment.risk_level in ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
    
    @pytest.mark.asyncio
    async def test_vulnerability_score_calculation(self, threat_service, sample_asset_metadata):
        """Test vulnerability score calculation."""
        # Test public-facing server
        web_server_score = await threat_service._calculate_vulnerability_score(
            "web_server", sample_asset_metadata["web_server"]
        )
        
        # Test internal database
        database_score = await threat_service._calculate_vulnerability_score(
            "database", sample_asset_metadata["database"]
        )
        
        # Public-facing server should have higher vulnerability
        assert web_server_score > database_score
        assert 0.0 <= web_server_score <= 1.0
        assert 0.0 <= database_score <= 1.0
    
    @pytest.mark.asyncio
    async def test_threat_frequency_calculation(self, threat_service, sample_asset_metadata):
        """Test threat frequency calculation."""
        # Test public-facing server
        web_server_freq = await threat_service._calculate_threat_frequency(
            "web_server", sample_asset_metadata["web_server"]
        )
        
        # Test internal database
        database_freq = await threat_service._calculate_threat_frequency(
            "database", sample_asset_metadata["database"]
        )
        
        # Public-facing server should have higher threat frequency
        assert web_server_freq > 12  # Base frequency
        assert database_freq > 12  # Should be higher due to criticality and PII
    
    @pytest.mark.asyncio
    async def test_control_effectiveness_calculation(self, threat_service, sample_asset_metadata):
        """Test control effectiveness calculation."""
        # Test database with multiple controls
        database_effectiveness = await threat_service._calculate_control_effectiveness(
            "database", sample_asset_metadata["database"]
        )
        
        # Test web server with fewer controls
        web_server_effectiveness = await threat_service._calculate_control_effectiveness(
            "web_server", sample_asset_metadata["web_server"]
        )
        
        # Database should have higher control effectiveness
        assert database_effectiveness >= web_server_effectiveness
        assert 0.0 <= database_effectiveness <= 1.0
        assert 0.0 <= web_server_effectiveness <= 1.0
    
    def test_exposure_factor_calculation(self, threat_service, sample_asset_metadata):
        """Test exposure factor calculation."""
        # Test database with PII and restricted classification
        database_ef = threat_service._calculate_exposure_factor(sample_asset_metadata["database"])
        
        # Test web server without PII
        web_server_ef = threat_service._calculate_exposure_factor(sample_asset_metadata["web_server"])
        
        # Database should have higher exposure factor
        assert database_ef > web_server_ef
        assert 0.0 <= database_ef <= 1.0
        assert 0.0 <= web_server_ef <= 1.0
    
    @pytest.mark.asyncio
    async def test_attack_simulation(self, threat_service, sample_asset_metadata):
        """Test attack simulation."""
        # Mock dependencies
        with patch.object(threat_service.attack_analyzer, 'initialize_from_database'):
            threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
            
            # Mock blast radius calculation
            mock_blast_result = BlastRadiusResult(
                source_asset_id="database",
                affected_assets={"database", "web_server"},
                impact_by_degree={0: {"database"}, 1: {"web_server"}},
                total_impact_score=150.0,
                critical_assets_affected={"database"},
                data_assets_affected={"database"},
                service_disruption_score=80.0,
                financial_impact=500000.0,
                compliance_impact=["GDPR"],
                recovery_time_estimate=12
            )
            
            with patch.object(threat_service.attack_analyzer, 'calculate_blast_radius', 
                            return_value=mock_blast_result):
                
                # Run simulation
                result = await threat_service.simulate_attack_scenario(
                    threat_actor_id="apt29",
                    target_assets=["database"],
                    scenario_name="Test APT29 Attack"
                )
                
                assert isinstance(result, AttackSimulationResult)
                assert result.scenario_name == "Test APT29 Attack"
                assert result.threat_actor.actor_id == "apt29"
                assert 0.0 <= result.success_probability <= 1.0
                assert 0.0 <= result.detection_probability <= 1.0
                assert result.time_to_compromise > 0
                assert result.time_to_detection > 0
                assert result.financial_impact > 0
                assert len(result.affected_assets) > 0
                assert "database" in result.affected_assets
    
    @pytest.mark.asyncio
    async def test_entry_point_identification(self, threat_service, sample_asset_metadata):
        """Test entry point identification."""
        threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
        
        # Test with external threat actor
        apt29 = threat_service.threat_actors["apt29"]
        entry_points = await threat_service._identify_entry_points(apt29)
        
        # Should include public-facing assets
        assert "web_server" in entry_points
        # Should not include internal database for external actor
        assert "database" not in entry_points
        
        # Test with insider threat
        insider = threat_service.threat_actors["malicious_insider"]
        insider_entry_points = await threat_service._identify_entry_points(insider)
        
        # Insider can access internal assets
        assert len(insider_entry_points) >= len(entry_points)
    
    def test_is_suitable_entry_point(self, threat_service, sample_asset_metadata):
        """Test entry point suitability check."""
        apt29 = threat_service.threat_actors["apt29"]
        insider = threat_service.threat_actors["malicious_insider"]
        
        # Public-facing server should be suitable for external actor
        assert threat_service._is_suitable_entry_point(
            sample_asset_metadata["web_server"], apt29
        )
        
        # Internal database should not be suitable for external actor
        assert not threat_service._is_suitable_entry_point(
            sample_asset_metadata["database"], apt29
        )
        
        # Both should be suitable for insider
        assert threat_service._is_suitable_entry_point(
            sample_asset_metadata["web_server"], insider
        )
        assert threat_service._is_suitable_entry_point(
            sample_asset_metadata["database"], insider
        )
    
    @pytest.mark.asyncio
    async def test_attack_success_probability(self, threat_service, sample_asset_metadata):
        """Test attack success probability calculation."""
        threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
        
        apt29 = threat_service.threat_actors["apt29"]
        script_kiddie = ThreatActorProfile(
            actor_id="script_kiddie",
            name="Script Kiddie",
            actor_type=ThreatActorType.SCRIPT_KIDDIE,
            sophistication_level=0.2,
            motivation=AttackMotivation.CURIOSITY,
            target_sectors=["all"],
            preferred_techniques=[],
            resource_level=0.1,
            stealth_capability=0.2,
            persistence_level=0.3,
            known_campaigns=[],
            attribution_confidence=0.5,
            last_activity=datetime.utcnow()
        )
        
        # APT29 should have higher success probability
        apt29_success = await threat_service._calculate_attack_success_probability(
            apt29, ["database"], ["web_server"]
        )
        
        script_kiddie_success = await threat_service._calculate_attack_success_probability(
            script_kiddie, ["database"], ["web_server"]
        )
        
        assert apt29_success > script_kiddie_success
        assert 0.05 <= apt29_success <= 0.95
        assert 0.05 <= script_kiddie_success <= 0.95
    
    def test_financial_impact_calculation(self, threat_service, sample_asset_metadata):
        """Test financial impact calculation."""
        threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
        
        apt29 = threat_service.threat_actors["apt29"]
        saboteur = ThreatActorProfile(
            actor_id="saboteur",
            name="Saboteur",
            actor_type=ThreatActorType.HACKTIVIST,
            sophistication_level=0.6,
            motivation=AttackMotivation.SABOTAGE,
            target_sectors=["all"],
            preferred_techniques=[],
            resource_level=0.5,
            stealth_capability=0.4,
            persistence_level=0.6,
            known_campaigns=[],
            attribution_confidence=0.6,
            last_activity=datetime.utcnow()
        )
        
        # Saboteur should cause higher financial impact
        apt29_impact = threat_service._calculate_financial_impact(
            apt29, {"database"}, 12.0
        )
        
        saboteur_impact = threat_service._calculate_financial_impact(
            saboteur, {"database"}, 12.0
        )
        
        assert saboteur_impact > apt29_impact
        assert apt29_impact > 0
        assert saboteur_impact > 0
    
    def test_reputation_impact_assessment(self, threat_service, sample_asset_metadata):
        """Test reputation impact assessment."""
        threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
        
        apt29 = threat_service.threat_actors["apt29"]
        
        # Impact with public-facing asset and PII
        high_impact = threat_service._assess_reputation_impact(
            apt29, {"web_server", "database"}
        )
        
        # Impact with only internal assets
        low_impact = threat_service._assess_reputation_impact(
            apt29, {"database"}
        )
        
        assert high_impact > low_impact
        assert 0.0 <= high_impact <= 1.0
        assert 0.0 <= low_impact <= 1.0
    
    def test_regulatory_violations_identification(self, threat_service, sample_asset_metadata):
        """Test regulatory violations identification."""
        threat_service.attack_analyzer.graph_engine.asset_metadata = sample_asset_metadata
        
        # Database contains PII
        violations = threat_service._identify_regulatory_violations({"database"})
        
        assert "GDPR" in violations
        assert "CCPA" in violations
        assert "HIPAA" in violations
        assert len(violations) > 0
        
        # Web server without PII
        web_violations = threat_service._identify_regulatory_violations({"web_server"})
        assert len(web_violations) == 0
    
    def test_threat_actor_management(self, threat_service):
        """Test threat actor management functions."""
        # Get specific threat actor
        apt29 = threat_service.get_threat_actor_profile("apt29")
        assert apt29 is not None
        assert apt29.name == "APT29 (Cozy Bear)"
        
        # Get non-existent actor
        fake_actor = threat_service.get_threat_actor_profile("fake_actor")
        assert fake_actor is None
        
        # List all actors
        all_actors = threat_service.list_threat_actors()
        assert len(all_actors) >= 4  # At least the 4 we defined
        assert all(isinstance(actor, ThreatActorProfile) for actor in all_actors)
    
    def test_export_threat_model(self, threat_service):
        """Test threat model export."""
        # Test JSON export
        export_data = threat_service.export_threat_model("json")
        assert isinstance(export_data, str)
        
        import json
        parsed_data = json.loads(export_data)
        assert "threat_actors" in parsed_data
        assert "simulations" in parsed_data
        assert "risk_parameters" in parsed_data
        assert "export_timestamp" in parsed_data
        
        # Test invalid format
        with pytest.raises(ValueError):
            threat_service.export_threat_model("invalid")
    
    def test_cache_management(self, threat_service):
        """Test simulation cache management."""
        # Add mock simulation
        mock_simulation = Mock()
        mock_simulation.simulation_id = "test_sim"
        threat_service.simulation_cache["test_sim"] = mock_simulation
        
        # Test retrieval
        retrieved = threat_service.get_simulation_result("test_sim")
        assert retrieved == mock_simulation
        
        # Test listing
        simulations = threat_service.list_simulations()
        assert mock_simulation in simulations
        
        # Test cache clearing
        threat_service.clear_simulation_cache()
        assert len(threat_service.simulation_cache) == 0
        assert threat_service.get_simulation_result("test_sim") is None


class TestThreatActorProfile:
    """Test threat actor profile."""
    
    def test_threat_score_calculation(self):
        """Test threat score calculation."""
        high_threat = ThreatActorProfile(
            actor_id="high_threat",
            name="High Threat Actor",
            actor_type=ThreatActorType.NATION_STATE,
            sophistication_level=0.9,
            motivation=AttackMotivation.ESPIONAGE,
            target_sectors=["government"],
            preferred_techniques=[],
            resource_level=0.9,
            stealth_capability=0.8,
            persistence_level=0.9,
            known_campaigns=[],
            attribution_confidence=0.8,
            last_activity=datetime.utcnow()
        )
        
        low_threat = ThreatActorProfile(
            actor_id="low_threat",
            name="Low Threat Actor",
            actor_type=ThreatActorType.SCRIPT_KIDDIE,
            sophistication_level=0.2,
            motivation=AttackMotivation.CURIOSITY,
            target_sectors=["all"],
            preferred_techniques=[],
            resource_level=0.1,
            stealth_capability=0.2,
            persistence_level=0.2,
            known_campaigns=[],
            attribution_confidence=0.3,
            last_activity=datetime.utcnow()
        )
        
        assert high_threat.threat_score > low_threat.threat_score
        assert 0 <= high_threat.threat_score <= 100
        assert 0 <= low_threat.threat_score <= 100


class TestQuantitativeRiskAssessment:
    """Test quantitative risk assessment."""
    
    def test_risk_level_determination(self):
        """Test risk level determination."""
        critical_risk = QuantitativeRiskAssessment(
            asset_id="test",
            annual_loss_expectancy=1500000,
            single_loss_expectancy=500000,
            annual_rate_of_occurrence=3.0,
            exposure_factor=0.8,
            asset_value=1000000,
            threat_frequency=12,
            vulnerability_score=0.7,
            control_effectiveness=0.6,
            residual_risk=0.15,
            risk_tolerance=0.1
        )
        
        low_risk = QuantitativeRiskAssessment(
            asset_id="test",
            annual_loss_expectancy=50000,
            single_loss_expectancy=25000,
            annual_rate_of_occurrence=2.0,
            exposure_factor=0.5,
            asset_value=100000,
            threat_frequency=6,
            vulnerability_score=0.4,
            control_effectiveness=0.8,
            residual_risk=0.05,
            risk_tolerance=0.1
        )
        
        assert critical_risk.risk_level == "CRITICAL"
        assert low_risk.risk_level == "LOW"
        assert critical_risk.exceeds_tolerance == True
        assert low_risk.exceeds_tolerance == False
