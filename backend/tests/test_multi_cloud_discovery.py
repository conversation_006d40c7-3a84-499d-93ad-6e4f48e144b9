"""
Comprehensive test suite for multi-cloud discovery integration.

Tests Azure and GCP discovery services with mock implementations
to validate the complete discovery workflow.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from typing import Dict, List, Any
import uuid

from app.services.discovery.azure_discovery import AzureDiscoveryService
from app.services.discovery.gcp_discovery import GCPDiscoveryService
from app.services.discovery.cloud_discovery import CloudDiscoveryEngine
from app.services.discovery.base_discovery import DiscoveryResult
from app.db.models.asset import AssetType, AssetProvider, DiscoverySource, RiskLevel


class TestAzureDiscoveryService:
    """Test suite for Azure discovery service."""
    
    @pytest.fixture
    def azure_config(self) -> Dict[str, Any]:
        """Create test Azure configuration."""
        return {
            "subscription_id": "********-1234-1234-1234-********9012",
            "client_id": "test-client-id",
            "client_secret": "test-client-secret",
            "tenant_id": "test-tenant-id",
            "resource_groups": ["test-rg-1", "test-rg-2"],
            "regions": ["eastus", "westus2"],
            "services": ["compute", "storage", "sql", "web"]
        }
    
    @pytest.fixture
    def mock_azure_clients(self):
        """Mock Azure service clients."""
        with patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', True), \
             patch('app.services.discovery.azure_discovery.ClientSecretCredential') as mock_cred, \
             patch('app.services.discovery.azure_discovery.ResourceManagementClient') as mock_resource, \
             patch('app.services.discovery.azure_discovery.ComputeManagementClient') as mock_compute, \
             patch('app.services.discovery.azure_discovery.StorageManagementClient') as mock_storage, \
             patch('app.services.discovery.azure_discovery.SqlManagementClient') as mock_sql, \
             patch('app.services.discovery.azure_discovery.WebSiteManagementClient') as mock_web, \
             patch('app.services.discovery.azure_discovery.ContainerServiceClient') as mock_container, \
             patch('app.services.discovery.azure_discovery.NetworkManagementClient') as mock_network:
            
            yield {
                'credential': mock_cred,
                'resource': mock_resource,
                'compute': mock_compute,
                'storage': mock_storage,
                'sql': mock_sql,
                'web': mock_web,
                'container': mock_container,
                'network': mock_network
            }
    
    def test_azure_service_initialization(self, azure_config, mock_azure_clients):
        """Test Azure discovery service initialization."""
        service = AzureDiscoveryService(azure_config)
        
        assert service.subscription_id == azure_config["subscription_id"]
        assert service.resource_groups == azure_config["resource_groups"]
        assert service.regions == azure_config["regions"]
        assert service.services == azure_config["services"]
        assert service.provider == AssetProvider.AZURE
        assert service.discovery_source == DiscoverySource.AZURE_API
    
    def test_azure_service_initialization_without_sdk(self, azure_config):
        """Test Azure service initialization when SDK is not available."""
        with patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                AzureDiscoveryService(azure_config)
            
            assert "Azure SDK not available" in str(exc_info.value)
    
    def test_azure_service_missing_subscription_id(self):
        """Test Azure service initialization with missing subscription ID."""
        config = {"client_id": "test", "client_secret": "test", "tenant_id": "test"}
        
        with patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', True):
            with pytest.raises(ValueError) as exc_info:
                AzureDiscoveryService(config)
            
            assert "subscription_id is required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_azure_config_validation_success(self, azure_config, mock_azure_clients):
        """Test successful Azure configuration validation."""
        service = AzureDiscoveryService(azure_config)
        
        # Mock successful resource group listing
        mock_azure_clients['resource'].return_value.resource_groups.list.return_value = [
            Mock(name="test-rg-1"),
            Mock(name="test-rg-2")
        ]
        
        is_valid = await service.validate_config()
        assert is_valid is True
    
    @pytest.mark.asyncio
    async def test_azure_config_validation_auth_failure(self, azure_config, mock_azure_clients):
        """Test Azure configuration validation with authentication failure."""
        service = AzureDiscoveryService(azure_config)
        
        # Mock authentication error
        from azure.core.exceptions import ClientAuthenticationError
        mock_azure_clients['resource'].return_value.resource_groups.list.side_effect = ClientAuthenticationError("Auth failed")
        
        is_valid = await service.validate_config()
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_azure_vm_discovery(self, azure_config, mock_azure_clients):
        """Test Azure virtual machine discovery."""
        service = AzureDiscoveryService(azure_config)
        
        # Mock VM data
        mock_vm = Mock()
        mock_vm.name = "test-vm-01"
        mock_vm.id = "/subscriptions/12345/resourceGroups/test-rg/providers/Microsoft.Compute/virtualMachines/test-vm-01"
        mock_vm.vm_id = "vm-12345"
        mock_vm.location = "eastus"
        mock_vm.tags = {"environment": "production"}
        mock_vm.hardware_profile = Mock(vm_size="Standard_D2s_v3")
        mock_vm.storage_profile = Mock()
        mock_vm.storage_profile.os_disk = Mock(os_type=Mock(value="Linux"))
        mock_vm.os_profile = Mock(computer_name="test-vm-01")
        mock_vm.network_profile = Mock(network_interfaces=[])
        mock_vm.provisioning_state = "Succeeded"
        mock_vm.time_created = datetime.utcnow()
        
        # Mock VM instance view
        mock_instance = Mock()
        mock_instance.statuses = [Mock(code="PowerState/running")]
        
        # Setup mocks
        mock_azure_clients['compute'].return_value.virtual_machines.list.return_value = [mock_vm]
        mock_azure_clients['compute'].return_value.virtual_machines.instance_view.return_value = mock_instance
        
        # Execute discovery
        result = await service._discover_virtual_machines()
        
        assert len(result["assets"]) == 1
        asset = result["assets"][0]
        assert asset["name"] == "test-vm-01"
        assert asset["asset_type"] == AssetType.CLOUD_INSTANCE
        assert asset["provider"] == AssetProvider.AZURE
        assert asset["environment"] == "production"
        assert asset["configuration"]["vm_size"] == "Standard_D2s_v3"
    
    @pytest.mark.asyncio
    async def test_azure_storage_discovery(self, azure_config, mock_azure_clients):
        """Test Azure storage account discovery."""
        service = AzureDiscoveryService(azure_config)
        
        # Mock storage account data
        mock_account = Mock()
        mock_account.name = "teststorageaccount"
        mock_account.id = "/subscriptions/12345/resourceGroups/test-rg/providers/Microsoft.Storage/storageAccounts/teststorageaccount"
        mock_account.location = "eastus"
        mock_account.tags = {"environment": "staging"}
        mock_account.sku = Mock(name="Standard_LRS", tier=Mock(value="Standard"))
        mock_account.kind = Mock(value="StorageV2")
        mock_account.access_tier = Mock(value="Hot")
        mock_account.enable_https_traffic_only = True
        mock_account.provisioning_state = Mock(value="Succeeded")
        mock_account.creation_time = datetime.utcnow()
        mock_account.primary_endpoints = Mock(
            blob="https://teststorageaccount.blob.core.windows.net/",
            file="https://teststorageaccount.file.core.windows.net/"
        )
        
        # Setup mocks
        mock_azure_clients['storage'].return_value.storage_accounts.list.return_value = [mock_account]
        
        # Execute discovery
        result = await service._discover_storage_accounts()
        
        assert len(result["assets"]) == 1
        asset = result["assets"][0]
        assert asset["name"] == "teststorageaccount"
        assert asset["asset_type"] == AssetType.STORAGE
        assert asset["provider"] == AssetProvider.AZURE
        assert asset["environment"] == "staging"
        assert asset["configuration"]["https_traffic_only"] is True
    
    @pytest.mark.asyncio
    async def test_azure_sql_discovery(self, azure_config, mock_azure_clients):
        """Test Azure SQL database discovery."""
        service = AzureDiscoveryService(azure_config)
        
        # Mock SQL server and database data
        mock_server = Mock()
        mock_server.name = "test-sql-server"
        mock_server.id = "/subscriptions/12345/resourceGroups/test-rg/providers/Microsoft.Sql/servers/test-sql-server"
        mock_server.location = "eastus"
        mock_server.version = "12.0"
        mock_server.administrator_login = "sqladmin"
        mock_server.fully_qualified_domain_name = "test-sql-server.database.windows.net"
        
        mock_database = Mock()
        mock_database.name = "test-database"
        mock_database.id = "/subscriptions/12345/resourceGroups/test-rg/providers/Microsoft.Sql/servers/test-sql-server/databases/test-database"
        mock_database.tags = {"environment": "production"}
        mock_database.sku = Mock(name="S2", tier="Standard")
        mock_database.collation = "SQL_Latin1_General_CP1_CI_AS"
        mock_database.max_size_bytes = 268435456000  # 250 GB
        mock_database.creation_date = datetime.utcnow()
        
        # Setup mocks
        mock_azure_clients['sql'].return_value.servers.list.return_value = [mock_server]
        mock_azure_clients['sql'].return_value.databases.list_by_server.return_value = [mock_database]
        
        # Execute discovery
        result = await service._discover_sql_databases()
        
        assert len(result["assets"]) == 1
        assert len(result["relationships"]) == 1
        
        asset = result["assets"][0]
        assert asset["name"] == "test-sql-server/test-database"
        assert asset["asset_type"] == AssetType.DATABASE
        assert asset["provider"] == AssetProvider.AZURE
        assert asset["environment"] == "production"
        assert asset["configuration"]["server_name"] == "test-sql-server"
        
        relationship = result["relationships"][0]
        assert relationship["relationship_type"] == "hosted_on"
    
    def test_azure_risk_score_calculation(self, azure_config, mock_azure_clients):
        """Test Azure asset risk score calculation."""
        service = AzureDiscoveryService(azure_config)
        
        # Test VM risk score
        mock_vm = Mock()
        mock_vm.tags = {"environment": "production"}
        mock_vm.hardware_profile = Mock(vm_size="Standard_H16r")  # High-performance
        mock_vm.storage_profile = Mock()
        mock_vm.storage_profile.os_disk = Mock(os_type=Mock(value="Windows"))
        
        mock_instance = Mock()
        mock_instance.statuses = [Mock(code="PowerState/running")]
        
        ip_addresses = ["***********", "*********"]  # Public and private
        
        risk_score = service._calculate_vm_risk_score(mock_vm, mock_instance, ip_addresses)
        
        # Should be high risk due to: production env, public IP, high-perf VM, Windows
        assert risk_score >= 70
        
        # Test storage account risk score
        mock_storage = Mock()
        mock_storage.tags = {"environment": "development"}
        mock_storage.enable_https_traffic_only = False  # Security risk
        mock_storage.kind = Mock(value="Storage")  # Classic storage
        mock_storage.access_tier = Mock(value="Hot")
        
        storage_risk = service._calculate_storage_risk_score(mock_storage)
        
        # Should be high risk due to: no HTTPS enforcement, classic storage
        assert storage_risk >= 60


class TestGCPDiscoveryService:
    """Test suite for GCP discovery service."""
    
    @pytest.fixture
    def gcp_config(self) -> Dict[str, Any]:
        """Create test GCP configuration."""
        return {
            "project_id": "test-project-12345",
            "credentials_path": "/path/to/service-account.json",
            "regions": ["us-central1", "us-east1"],
            "services": ["compute", "storage", "sql", "containers"]
        }
    
    @pytest.fixture
    def mock_gcp_clients(self):
        """Mock GCP service clients."""
        with patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', True), \
             patch('app.services.discovery.gcp_discovery.service_account') as mock_sa, \
             patch('app.services.discovery.gcp_discovery.compute_v1') as mock_compute, \
             patch('app.services.discovery.gcp_discovery.storage') as mock_storage, \
             patch('app.services.discovery.gcp_discovery.sql_v1') as mock_sql, \
             patch('app.services.discovery.gcp_discovery.container_v1') as mock_container, \
             patch('app.services.discovery.gcp_discovery.asset_v1') as mock_asset:
            
            yield {
                'service_account': mock_sa,
                'compute': mock_compute,
                'storage': mock_storage,
                'sql': mock_sql,
                'container': mock_container,
                'asset': mock_asset
            }
    
    def test_gcp_service_initialization(self, gcp_config, mock_gcp_clients):
        """Test GCP discovery service initialization."""
        service = GCPDiscoveryService(gcp_config)
        
        assert service.project_id == gcp_config["project_id"]
        assert service.regions == gcp_config["regions"]
        assert service.services == gcp_config["services"]
        assert service.provider == AssetProvider.GCP
        assert service.discovery_source == DiscoverySource.GCP_API
    
    def test_gcp_service_initialization_without_sdk(self, gcp_config):
        """Test GCP service initialization when SDK is not available."""
        with patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                GCPDiscoveryService(gcp_config)
            
            assert "Google Cloud SDK not available" in str(exc_info.value)
    
    def test_gcp_service_missing_project_id(self):
        """Test GCP service initialization with missing project ID."""
        config = {"credentials_path": "/path/to/creds.json"}
        
        with patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', True):
            with pytest.raises(ValueError) as exc_info:
                GCPDiscoveryService(config)
            
            assert "project_id is required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_gcp_config_validation_success(self, gcp_config, mock_gcp_clients):
        """Test successful GCP configuration validation."""
        service = GCPDiscoveryService(gcp_config)
        
        # Mock successful compute instance listing
        mock_response = Mock()
        mock_gcp_clients['compute'].InstancesClient.return_value.aggregated_list.return_value = mock_response
        
        is_valid = await service.validate_config()
        assert is_valid is True
    
    @pytest.mark.asyncio
    async def test_gcp_compute_discovery(self, gcp_config, mock_gcp_clients):
        """Test GCP compute instance discovery."""
        service = GCPDiscoveryService(gcp_config)
        
        # Mock compute instance data
        mock_instance = Mock()
        mock_instance.name = "test-instance-01"
        mock_instance.id = "********90********9"
        mock_instance.zone = "https://www.googleapis.com/compute/v1/projects/test-project/zones/us-central1-a"
        mock_instance.machine_type = "https://www.googleapis.com/compute/v1/projects/test-project/zones/us-central1-a/machineTypes/n1-standard-2"
        mock_instance.status = "RUNNING"
        mock_instance.labels = {"environment": "production"}
        mock_instance.tags = Mock(items=["web-server", "https-server"])
        mock_instance.creation_timestamp = "2023-01-01T00:00:00.000-00:00"
        mock_instance.network_interfaces = [
            Mock(
                name="nic0",
                network="https://www.googleapis.com/compute/v1/projects/test-project/global/networks/default",
                subnetwork="https://www.googleapis.com/compute/v1/projects/test-project/regions/us-central1/subnetworks/default",
                network_i_p="**********",
                access_configs=[Mock(nat_i_p="***********")]
            )
        ]
        mock_instance.disks = [
            Mock(device_name="persistent-disk-0", boot=True, auto_delete=True)
        ]
        mock_instance.scheduling = Mock(preemptible=False)
        
        # Mock aggregated list response
        mock_response = [
            ("zones/us-central1-a", Mock(instances=[mock_instance]))
        ]
        
        mock_gcp_clients['compute'].InstancesClient.return_value.aggregated_list.return_value = mock_response
        
        # Execute discovery
        result = await service._discover_compute_instances()
        
        assert len(result["assets"]) == 1
        asset = result["assets"][0]
        assert asset["name"] == "test-instance-01"
        assert asset["asset_type"] == AssetType.CLOUD_INSTANCE
        assert asset["provider"] == AssetProvider.GCP
        assert asset["environment"] == "production"
        assert asset["ip_addresses"] == ["**********", "***********"]
        assert asset["configuration"]["machine_type"] == "n1-standard-2"
    
    def test_gcp_risk_score_calculation(self, gcp_config, mock_gcp_clients):
        """Test GCP asset risk score calculation."""
        service = GCPDiscoveryService(gcp_config)
        
        # Test compute instance risk score
        mock_instance = Mock()
        mock_instance.labels = {"environment": "production"}
        mock_instance.machine_type = "https://www.googleapis.com/compute/v1/projects/test-project/zones/us-central1-a/machineTypes/n1-highmem-8"
        mock_instance.status = "RUNNING"
        mock_instance.scheduling = Mock(preemptible=False)
        
        ip_addresses = ["***********", "**********"]  # Public and private
        
        risk_score = service._calculate_compute_risk_score(mock_instance, ip_addresses)
        
        # Should be high risk due to: production env, public IP, high-memory instance
        assert risk_score >= 70


class TestCloudDiscoveryEngine:
    """Test suite for unified cloud discovery engine."""
    
    @pytest.mark.asyncio
    async def test_aws_discovery_routing(self):
        """Test that AWS discovery is routed correctly."""
        config = {
            "provider": "aws",
            "access_key_id": "test-key",
            "secret_access_key": "test-secret",
            "regions": ["us-east-1"]
        }
        
        engine = CloudDiscoveryEngine(config)
        assert engine.provider == AssetProvider.AWS
        assert engine.discovery_source == DiscoverySource.AWS_API
        assert engine.provider_name == "aws"
    
    @pytest.mark.asyncio
    async def test_azure_discovery_routing(self):
        """Test that Azure discovery is routed correctly."""
        config = {
            "provider": "azure",
            "subscription_id": "********-1234-1234-1234-********9012"
        }
        
        with patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', True), \
             patch('app.services.discovery.azure_discovery.ClientSecretCredential'), \
             patch('app.services.discovery.azure_discovery.ResourceManagementClient'), \
             patch('app.services.discovery.azure_discovery.ComputeManagementClient'), \
             patch('app.services.discovery.azure_discovery.StorageManagementClient'), \
             patch('app.services.discovery.azure_discovery.SqlManagementClient'), \
             patch('app.services.discovery.azure_discovery.WebSiteManagementClient'), \
             patch('app.services.discovery.azure_discovery.ContainerServiceClient'), \
             patch('app.services.discovery.azure_discovery.NetworkManagementClient'):
            
            engine = CloudDiscoveryEngine(config)
            assert engine.provider == AssetProvider.AZURE
            assert engine.discovery_source == DiscoverySource.AZURE_API
            assert engine.provider_name == "azure"
    
    @pytest.mark.asyncio
    async def test_gcp_discovery_routing(self):
        """Test that GCP discovery is routed correctly."""
        config = {
            "provider": "gcp",
            "project_id": "test-project-12345"
        }
        
        with patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', True), \
             patch('app.services.discovery.gcp_discovery.service_account'), \
             patch('app.services.discovery.gcp_discovery.compute_v1'), \
             patch('app.services.discovery.gcp_discovery.storage'), \
             patch('app.services.discovery.gcp_discovery.sql_v1'), \
             patch('app.services.discovery.gcp_discovery.container_v1'), \
             patch('app.services.discovery.gcp_discovery.asset_v1'):
            
            engine = CloudDiscoveryEngine(config)
            assert engine.provider == AssetProvider.GCP
            assert engine.discovery_source == DiscoverySource.GCP_API
            assert engine.provider_name == "gcp"
    
    def test_unsupported_provider(self):
        """Test error handling for unsupported cloud provider."""
        config = {"provider": "unsupported"}
        
        with pytest.raises(ValueError) as exc_info:
            CloudDiscoveryEngine(config)
        
        assert "Unsupported cloud provider: unsupported" in str(exc_info.value)


class TestMultiCloudIntegration:
    """Integration tests for multi-cloud discovery."""
    
    @pytest.mark.asyncio
    async def test_concurrent_multi_cloud_discovery(self):
        """Test concurrent discovery across multiple cloud providers."""
        # This would test running AWS, Azure, and GCP discovery concurrently
        # and ensuring proper isolation and result aggregation
        pass
    
    @pytest.mark.asyncio
    async def test_cross_cloud_relationship_discovery(self):
        """Test discovery of relationships across cloud providers."""
        # This would test identifying assets that span multiple clouds
        # and creating appropriate cross-cloud relationships
        pass
    
    def test_multi_cloud_risk_assessment(self):
        """Test risk assessment across multiple cloud environments."""
        # This would test aggregating risk scores and identifying
        # cross-cloud security concerns
        pass
