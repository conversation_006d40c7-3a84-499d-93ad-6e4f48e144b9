"""Tests for discovery engines."""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from app.services.discovery import (
    CloudDiscoveryEngine,
    APIDiscoveryEngine,
    NetworkDiscoveryEngine,
    DiscoveryResult,
)
from app.db.models.asset import AssetProvider, AssetType, DiscoverySource


class TestCloudDiscoveryEngine:
    """Test cloud discovery engine."""
    
    def test_aws_provider_properties(self):
        """Test AWS provider properties."""
        config = {"provider": "aws", "access_key_id": "test", "secret_access_key": "test"}
        engine = CloudDiscoveryEngine(config)
        
        assert engine.provider == AssetProvider.AWS
        assert engine.discovery_source == DiscoverySource.CLOUD_API
        assert AssetType.CLOUD_INSTANCE in engine.supported_asset_types
    
    def test_azure_provider_properties(self):
        """Test Azure provider properties."""
        config = {"provider": "azure", "subscription_id": "test", "client_id": "test"}
        engine = CloudDiscoveryEngine(config)
        
        assert engine.provider == AssetProvider.AZURE
        assert engine.discovery_source == DiscoverySource.CLOUD_API
    
    def test_gcp_provider_properties(self):
        """Test GCP provider properties."""
        config = {"provider": "gcp", "project_id": "test", "credentials_path": "test"}
        engine = CloudDiscoveryEngine(config)
        
        assert engine.provider == AssetProvider.GCP
        assert engine.discovery_source == DiscoverySource.CLOUD_API
    
    def test_validate_config_aws_missing_fields(self):
        """Test AWS config validation with missing fields."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)
        
        # This should return False due to missing required fields
        # Note: We can't use async in a regular test method, so we'll test the sync parts
        assert "access_key_id" not in config
        assert "secret_access_key" not in config
    
    @pytest.mark.asyncio
    async def test_aws_discovery_without_boto3(self):
        """Test AWS discovery when boto3 is not available."""
        config = {
            "provider": "aws",
            "access_key_id": "test",
            "secret_access_key": "test"
        }
        engine = CloudDiscoveryEngine(config)
        
        # Mock the import to fail
        with patch('builtins.__import__', side_effect=ImportError("No module named 'boto3'")):
            result = await engine.discover()
            
            assert isinstance(result, DiscoveryResult)
            assert result.total_errors > 0
            assert any("boto3 not installed" in str(error) for error in result.errors)
    
    @pytest.mark.asyncio
    async def test_create_ec2_asset(self):
        """Test EC2 asset creation."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)
        
        instance_data = {
            "InstanceId": "i-********90abcdef0",
            "InstanceType": "t3.micro",
            "State": {"Name": "running"},
            "PublicIpAddress": "***********",
            "PrivateIpAddress": "*********",
            "Tags": [
                {"Key": "Name", "Value": "test-instance"},
                {"Key": "Environment", "Value": "production"}
            ],
            "OwnerId": "********9012",
            "VpcId": "vpc-********",
            "SubnetId": "subnet-********",
            "SecurityGroups": [{"GroupId": "sg-********"}],
            "KeyName": "my-key",
            "LaunchTime": datetime.utcnow()
        }
        
        asset_data = engine._create_ec2_asset(instance_data, "us-east-1")
        
        assert asset_data["name"] == "test-instance"
        assert asset_data["asset_type"] == AssetType.CLOUD_INSTANCE
        assert asset_data["provider_id"] == "i-********90abcdef0"
        assert "***********" in asset_data["ip_addresses"]
        assert "*********" in asset_data["ip_addresses"]
        assert asset_data["environment"] == "production"
        assert asset_data["risk_score"] > 0
        assert asset_data["risk_level"] is not None


class TestAPIDiscoveryEngine:
    """Test API discovery engine."""
    
    def test_akto_provider_properties(self):
        """Test Akto discovery properties."""
        config = {"tool": "akto", "target_urls": ["https://api.example.com"]}
        engine = APIDiscoveryEngine(config)
        
        assert engine.provider == AssetProvider.ON_PREMISES
        assert engine.discovery_source == DiscoverySource.AKTO
        assert AssetType.API_ENDPOINT in engine.supported_asset_types
    
    def test_kiterunner_provider_properties(self):
        """Test Kiterunner discovery properties."""
        config = {"tool": "kiterunner", "target_urls": ["https://api.example.com"]}
        engine = APIDiscoveryEngine(config)
        
        assert engine.discovery_source == DiscoverySource.KITERUNNER
    
    @pytest.mark.asyncio
    async def test_validate_config_no_urls(self):
        """Test config validation with no target URLs."""
        config = {"tool": "akto"}
        engine = APIDiscoveryEngine(config)
        
        is_valid = await engine.validate_config()
        assert not is_valid
    
    @pytest.mark.asyncio
    async def test_validate_config_invalid_urls(self):
        """Test config validation with invalid URLs."""
        config = {"tool": "akto", "target_urls": ["not-a-url", "also-invalid"]}
        engine = APIDiscoveryEngine(config)
        
        is_valid = await engine.validate_config()
        assert not is_valid
    
    @pytest.mark.asyncio
    async def test_validate_config_valid_urls(self):
        """Test config validation with valid URLs."""
        config = {
            "tool": "akto",
            "target_urls": ["https://api.example.com", "http://localhost:8080"]
        }
        engine = APIDiscoveryEngine(config)
        
        is_valid = await engine.validate_config()
        assert is_valid
    
    def test_create_mock_api_endpoints(self):
        """Test mock API endpoint creation."""
        config = {"tool": "akto", "target_urls": ["https://api.example.com"]}
        engine = APIDiscoveryEngine(config)
        
        endpoints = engine._create_mock_api_endpoints("https://api.example.com")
        
        assert len(endpoints) > 0
        assert all(endpoint["asset_type"] == AssetType.API_ENDPOINT for endpoint in endpoints)
        assert any("GET" in endpoint["name"] for endpoint in endpoints)
        assert any("POST" in endpoint["name"] for endpoint in endpoints)
    
    def test_create_api_endpoint_asset(self):
        """Test API endpoint asset creation."""
        config = {"tool": "akto"}
        engine = APIDiscoveryEngine(config)
        
        endpoint_data = {
            "path": "/api/v1/users",
            "method": "GET",
            "status_code": 200,
            "auth_required": False,
            "response_time": 150,
            "content_type": "application/json"
        }
        
        asset_data = engine._create_api_endpoint_asset(endpoint_data, "https://api.example.com")
        
        assert asset_data["name"] == "GET /api/v1/users"
        assert asset_data["asset_type"] == AssetType.API_ENDPOINT
        assert asset_data["provider_id"] == "https://api.example.com/api/v1/users"
        assert "api.example.com" in asset_data["dns_names"]
        assert asset_data["configuration"]["method"] == "GET"
        assert asset_data["risk_score"] > 0


class TestNetworkDiscoveryEngine:
    """Test network discovery engine."""
    
    def test_provider_properties(self):
        """Test network discovery properties."""
        config = {"target_networks": ["***********/24"]}
        engine = NetworkDiscoveryEngine(config)
        
        assert engine.provider == AssetProvider.ON_PREMISES
        assert engine.discovery_source == DiscoverySource.NETWORK_SCAN
        assert AssetType.SERVER in engine.supported_asset_types
        assert AssetType.NETWORK_DEVICE in engine.supported_asset_types
    
    @pytest.mark.asyncio
    async def test_validate_config_no_networks(self):
        """Test config validation with no target networks."""
        config = {}
        engine = NetworkDiscoveryEngine(config)
        
        is_valid = await engine.validate_config()
        assert not is_valid
    
    @pytest.mark.asyncio
    async def test_validate_config_invalid_networks(self):
        """Test config validation with invalid networks."""
        config = {"target_networks": ["not-a-network", "256.256.256.256/24"]}
        engine = NetworkDiscoveryEngine(config)
        
        is_valid = await engine.validate_config()
        assert not is_valid
    
    @pytest.mark.asyncio
    async def test_validate_config_valid_networks(self):
        """Test config validation with valid networks."""
        config = {"target_networks": ["***********/24", "10.0.0.0/16"]}
        engine = NetworkDiscoveryEngine(config)
        
        is_valid = await engine.validate_config()
        assert is_valid
    
    def test_create_host_asset(self):
        """Test host asset creation."""
        config = {"target_networks": ["***********/24"]}
        engine = NetworkDiscoveryEngine(config)
        
        host_data = {
            "ip": "*************",
            "hostname": "test-host",
            "os": {"name": "Linux Ubuntu", "accuracy": "95"},
            "services": [
                {"port": 22, "protocol": "tcp", "state": "open", "service": {"name": "ssh"}},
                {"port": 80, "protocol": "tcp", "state": "open", "service": {"name": "http"}},
                {"port": 443, "protocol": "tcp", "state": "open", "service": {"name": "https"}},
            ]
        }
        
        asset_data = engine._create_host_asset(host_data, "***********/24")
        
        assert asset_data["name"] == "test-host"
        assert asset_data["asset_type"] == AssetType.SERVER
        assert asset_data["provider_id"] == "*************"
        assert "*************" in asset_data["ip_addresses"]
        assert "test-host" in asset_data["dns_names"]
        assert 22 in asset_data["ports"]
        assert 80 in asset_data["ports"]
        assert 443 in asset_data["ports"]
        assert asset_data["risk_score"] > 0
    
    def test_create_service_asset(self):
        """Test service asset creation."""
        config = {"target_networks": ["***********/24"]}
        engine = NetworkDiscoveryEngine(config)
        
        service_data = {
            "port": 22,
            "protocol": "tcp",
            "state": "open",
            "service": {
                "name": "ssh",
                "product": "OpenSSH",
                "version": "8.2",
                "extrainfo": "Ubuntu-4ubuntu0.5"
            }
        }
        
        host_data = {"ip": "*************"}
        
        asset_data = engine._create_service_asset(service_data, host_data, "***********/24")
        
        assert asset_data["name"] == "ssh on *************:22"
        assert asset_data["asset_type"] == AssetType.SERVICE
        assert asset_data["provider_id"] == "*************:22/tcp"
        assert asset_data["configuration"]["port"] == 22
        assert asset_data["configuration"]["service_name"] == "ssh"
        assert asset_data["configuration"]["product"] == "OpenSSH"
        assert asset_data["risk_score"] > 0
    
    @pytest.mark.asyncio
    async def test_mock_network_discovery(self):
        """Test mock network discovery."""
        config = {"target_networks": ["***********/29"]}  # Small network for testing
        engine = NetworkDiscoveryEngine(config)
        
        result = await engine._mock_network_discovery()
        
        assert isinstance(result, DiscoveryResult)
        assert result.total_assets > 0
        assert result.total_relationships > 0
        assert result.total_errors == 0
        
        # Check that we have both hosts and services
        host_assets = [a for a in result.assets_discovered if a["asset_type"] == AssetType.SERVER]
        service_assets = [a for a in result.assets_discovered if a["asset_type"] == AssetType.SERVICE]
        
        assert len(host_assets) > 0
        assert len(service_assets) > 0
        assert len(service_assets) >= len(host_assets) * 3  # At least 3 services per host


class TestDiscoveryResult:
    """Test DiscoveryResult class."""
    
    def test_discovery_result_properties(self):
        """Test DiscoveryResult properties."""
        assets = [{"name": "asset1"}, {"name": "asset2"}]
        relationships = [{"type": "contains"}]
        errors = [{"error": "test error"}]
        
        result = DiscoveryResult(
            assets_discovered=assets,
            relationships_discovered=relationships,
            assets_updated=[],
            errors=errors,
            execution_log=[]
        )
        
        assert result.total_assets == 2
        assert result.total_relationships == 1
        assert result.total_errors == 1
        assert result.success_rate == 0.5  # 1 error out of 2 total operations
    
    def test_discovery_result_success_rate_no_operations(self):
        """Test success rate calculation with no operations."""
        result = DiscoveryResult(
            assets_discovered=[],
            relationships_discovered=[],
            assets_updated=[],
            errors=[],
            execution_log=[]
        )
        
        assert result.success_rate == 1.0
    
    def test_discovery_result_success_rate_no_errors(self):
        """Test success rate calculation with no errors."""
        result = DiscoveryResult(
            assets_discovered=[{"name": "asset1"}],
            relationships_discovered=[{"type": "contains"}],
            assets_updated=[],
            errors=[],
            execution_log=[]
        )
        
        assert result.success_rate == 1.0


class TestDiscoveryOrchestrator:
    """Test discovery orchestrator."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()

    @pytest.fixture
    def orchestrator(self, mock_db):
        """Create orchestrator instance."""
        from app.services.discovery_orchestrator import DiscoveryOrchestrator
        return DiscoveryOrchestrator(mock_db)

    def test_engine_registry(self, orchestrator):
        """Test that all expected engines are registered."""
        expected_engines = [
            "aws_discovery",
            "azure_discovery",
            "gcp_discovery",
            "akto_discovery",
            "kiterunner_discovery",
            "network_discovery"
        ]

        for engine_type in expected_engines:
            assert engine_type in orchestrator.engine_registry

    def test_get_active_jobs_empty(self, orchestrator):
        """Test getting active jobs when none are running."""
        active_jobs = orchestrator.get_active_jobs()
        assert active_jobs == []

    def test_is_job_running_false(self, orchestrator):
        """Test job running check when job is not running."""
        import uuid
        job_id = uuid.uuid4()
        assert not orchestrator.is_job_running(job_id)

    @pytest.mark.asyncio
    async def test_get_discovery_statistics(self, orchestrator):
        """Test getting discovery statistics."""
        # Mock the asset service
        orchestrator.asset_service.get_asset_statistics = Mock(return_value={
            "total_assets": 100,
            "total_jobs": 10
        })

        stats = await orchestrator.get_discovery_statistics()

        assert "active_jobs" in stats
        assert "active_job_ids" in stats
        assert "total_assets" in stats
        assert "total_jobs" in stats
        assert stats["active_jobs"] == 0


class TestAssetMetadataExtraction:
    """Test comprehensive asset metadata extraction and edge cases."""

    def test_aws_ec2_comprehensive_metadata(self):
        """Test comprehensive EC2 metadata extraction."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Complex EC2 instance with all possible metadata
        instance_data = {
            "InstanceId": "i-********90abcdef0",
            "InstanceType": "m5.xlarge",
            "State": {"Name": "running", "Code": 16},
            "PublicIpAddress": "***********",
            "PrivateIpAddress": "*********",
            "PublicDnsName": "ec2-203-0-113-1.compute-1.amazonaws.com",
            "PrivateDnsName": "ip-10-0-1-10.ec2.internal",
            "VpcId": "vpc-********",
            "SubnetId": "subnet-********",
            "Architecture": "x86_64",
            "Platform": "Linux/UNIX",
            "Hypervisor": "xen",
            "VirtualizationType": "hvm",
            "RootDeviceType": "ebs",
            "RootDeviceName": "/dev/sda1",
            "BlockDeviceMappings": [
                {
                    "DeviceName": "/dev/sda1",
                    "Ebs": {
                        "VolumeId": "vol-********90abcdef0",
                        "Status": "attached",
                        "AttachTime": "2023-01-01T00:00:00.000Z",
                        "DeleteOnTermination": True,
                        "VolumeSize": 20,
                        "VolumeType": "gp3",
                        "Encrypted": True
                    }
                }
            ],
            "SecurityGroups": [
                {"GroupId": "sg-********", "GroupName": "web-servers"},
                {"GroupId": "sg-87654321", "GroupName": "ssh-access"}
            ],
            "IamInstanceProfile": {
                "Arn": "arn:aws:iam::********9012:instance-profile/EC2-Role",
                "Id": "AIPAI23HZ27SI6FQMGNQ2"
            },
            "Monitoring": {"State": "enabled"},
            "Placement": {
                "AvailabilityZone": "us-east-1a",
                "GroupName": "",
                "Tenancy": "default",
                "SpreadDomain": "",
                "HostId": "",
                "Affinity": "",
                "HostResourceGroupArn": ""
            },
            "NetworkInterfaces": [
                {
                    "NetworkInterfaceId": "eni-********",
                    "SubnetId": "subnet-********",
                    "VpcId": "vpc-********",
                    "Description": "Primary network interface",
                    "OwnerId": "********9012",
                    "Status": "in-use",
                    "MacAddress": "02:42:ac:11:00:02",
                    "PrivateIpAddress": "*********",
                    "PrivateDnsName": "ip-10-0-1-10.ec2.internal",
                    "SourceDestCheck": True,
                    "Groups": [{"GroupId": "sg-********", "GroupName": "web-servers"}],
                    "Association": {
                        "PublicIp": "***********",
                        "PublicDnsName": "ec2-203-0-113-1.compute-1.amazonaws.com",
                        "IpOwnerId": "amazon"
                    }
                }
            ],
            "Tags": [
                {"Key": "Name", "Value": "production-web-server-01"},
                {"Key": "Environment", "Value": "production"},
                {"Key": "Team", "Value": "platform"},
                {"Key": "Owner", "Value": "<EMAIL>"},
                {"Key": "CostCenter", "Value": "engineering"},
                {"Key": "Project", "Value": "web-platform"},
                {"Key": "Backup", "Value": "daily"},
                {"Key": "Monitoring", "Value": "enabled"},
                {"Key": "Compliance", "Value": "pci-dss,soc2"},
                {"Key": "DataClassification", "Value": "confidential"},
                {"Key": "MaintenanceWindow", "Value": "sunday-02:00-04:00"},
                {"Key": "AutoShutdown", "Value": "false"}
            ],
            "OwnerId": "********9012",
            "KeyName": "production-keypair",
            "LaunchTime": datetime(2023, 1, 1, 0, 0, 0),
            "ImageId": "ami-0abcdef********90",
            "EnaSupport": True,
            "SriovNetSupport": "simple",
            "EbsOptimized": True,
            "SourceDestCheck": True,
            "CpuOptions": {
                "CoreCount": 2,
                "ThreadsPerCore": 2
            },
            "CapacityReservationSpecification": {
                "CapacityReservationPreference": "open"
            },
            "HibernationOptions": {"Configured": False},
            "MetadataOptions": {
                "State": "applied",
                "HttpTokens": "required",
                "HttpPutResponseHopLimit": 1,
                "HttpEndpoint": "enabled"
            }
        }

        asset_data = engine._create_ec2_asset(instance_data, "us-east-1")

        # Verify basic asset information
        assert asset_data["name"] == "production-web-server-01"
        assert asset_data["asset_type"] == AssetType.CLOUD_INSTANCE
        assert asset_data["provider_id"] == "i-********90abcdef0"
        assert asset_data["provider_region"] == "us-east-1"
        assert asset_data["provider_account_id"] == "********9012"

        # Verify network information
        assert "***********" in asset_data["ip_addresses"]
        assert "*********" in asset_data["ip_addresses"]
        assert "ec2-203-0-113-1.compute-1.amazonaws.com" in asset_data["dns_names"]
        assert "ip-10-0-1-10.ec2.internal" in asset_data["dns_names"]

        # Verify tags and metadata
        assert asset_data["environment"] == "production"
        assert asset_data["team"] == "platform"
        assert asset_data["owner"] == "<EMAIL>"
        assert asset_data["cost_center"] == "engineering"

        # Verify configuration details
        config = asset_data["configuration"]
        assert config["instance_type"] == "m5.xlarge"
        assert config["architecture"] == "x86_64"
        assert config["platform"] == "Linux/UNIX"
        assert config["vpc_id"] == "vpc-********"
        assert config["subnet_id"] == "subnet-********"
        assert len(config["security_groups"]) == 2
        assert "sg-********" in config["security_groups"]
        assert config["key_name"] == "production-keypair"
        assert config["monitoring_enabled"] == True
        assert config["ebs_optimized"] == True

        # Verify risk assessment considers production environment
        assert asset_data["risk_score"] > 50  # Production should have higher risk
        assert asset_data["risk_level"] in [RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL]

    def test_api_endpoint_comprehensive_metadata(self):
        """Test comprehensive API endpoint metadata extraction."""
        config = {"tool": "akto"}
        engine = APIDiscoveryEngine(config)

        # Complex API endpoint with comprehensive metadata
        endpoint_data = {
            "path": "/api/v2/users/{userId}/profile",
            "method": "PUT",
            "status_code": 200,
            "response_time": 245,
            "content_type": "application/json",
            "content_length": 1024,
            "auth_required": True,
            "auth_methods": ["bearer_token", "api_key"],
            "rate_limit": {"requests": 1000, "window": "hour"},
            "parameters": [
                {
                    "name": "userId",
                    "type": "path",
                    "required": True,
                    "format": "uuid"
                },
                {
                    "name": "email",
                    "type": "body",
                    "required": True,
                    "format": "email",
                    "validation": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
                },
                {
                    "name": "phone",
                    "type": "body",
                    "required": False,
                    "format": "phone",
                    "pii": True
                }
            ],
            "response_schema": {
                "type": "object",
                "properties": {
                    "id": {"type": "string", "format": "uuid"},
                    "email": {"type": "string", "format": "email"},
                    "profile": {"type": "object"}
                }
            },
            "security_headers": {
                "x-frame-options": "DENY",
                "x-content-type-options": "nosniff",
                "strict-transport-security": "max-age=31536000",
                "x-xss-protection": "1; mode=block"
            },
            "cors_enabled": True,
            "cors_origins": ["https://app.example.com", "https://admin.example.com"],
            "ssl_enabled": True,
            "ssl_version": "TLSv1.3",
            "data_classification": "pii",
            "compliance_requirements": ["gdpr", "ccpa"],
            "business_criticality": "high",
            "data_flow": {
                "reads_from": ["user_database", "profile_cache"],
                "writes_to": ["user_database", "audit_log"],
                "external_calls": ["email_service", "notification_service"]
            },
            "monitoring": {
                "health_check": "/health",
                "metrics_endpoint": "/metrics",
                "logging_level": "info",
                "tracing_enabled": True
            },
            "versioning": {
                "version": "v2",
                "deprecated_versions": ["v1"],
                "sunset_date": "2024-12-31"
            }
        }

        asset_data = engine._create_api_endpoint_asset(endpoint_data, "https://api.example.com")

        # Verify basic endpoint information
        assert asset_data["name"] == "PUT /api/v2/users/{userId}/profile"
        assert asset_data["asset_type"] == AssetType.API_ENDPOINT
        assert asset_data["provider_id"] == "https://api.example.com/api/v2/users/{userId}/profile"

        # Verify configuration details
        config = asset_data["configuration"]
        assert config["method"] == "PUT"
        assert config["status_code"] == 200
        assert config["response_time"] == 245
        assert config["authentication_required"] == True
        assert config["auth_methods"] == ["bearer_token", "api_key"]
        assert config["rate_limit"]["requests"] == 1000
        assert config["ssl_enabled"] == True
        assert config["ssl_version"] == "TLSv1.3"

        # Verify security and compliance metadata
        assert config["data_classification"] == "pii"
        assert "gdpr" in config["compliance_requirements"]
        assert "ccpa" in config["compliance_requirements"]
        assert config["business_criticality"] == "high"

        # Verify risk assessment considers PII and high criticality
        assert asset_data["risk_score"] > 40  # PII endpoints should have higher risk

        # Verify properties for search and filtering
        properties = asset_data["properties"]
        assert properties["http_method"] == "PUT"
        assert properties["endpoint_path"] == "/api/v2/users/{userId}/profile"
        assert properties["base_url"] == "https://api.example.com"

    def test_network_host_comprehensive_metadata(self):
        """Test comprehensive network host metadata extraction."""
        config = {"target_networks": ["***********/24"]}
        engine = NetworkDiscoveryEngine(config)

        # Complex host with comprehensive service information
        host_data = {
            "ip": "*************",
            "hostname": "prod-db-primary.internal.company.com",
            "os": {
                "name": "Linux Ubuntu 20.04.6 LTS",
                "accuracy": "98",
                "family": "Linux",
                "generation": "4.x",
                "vendor": "Ubuntu"
            },
            "uptime": 2592000,  # 30 days in seconds
            "last_boot": "2023-11-01T10:30:00Z",
            "system_info": {
                "architecture": "x86_64",
                "cpu_cores": 8,
                "memory_gb": 32,
                "disk_gb": 500,
                "virtualization": "vmware"
            },
            "network_interfaces": [
                {
                    "name": "eth0",
                    "ip": "*************",
                    "mac": "00:50:56:12:34:56",
                    "type": "ethernet",
                    "speed": "1000Mbps",
                    "duplex": "full"
                },
                {
                    "name": "lo",
                    "ip": "127.0.0.1",
                    "type": "loopback"
                }
            ],
            "services": [
                {
                    "port": 22,
                    "protocol": "tcp",
                    "state": "open",
                    "service": {
                        "name": "ssh",
                        "product": "OpenSSH",
                        "version": "8.2p1",
                        "extrainfo": "Ubuntu-4ubuntu0.9",
                        "ostype": "Linux",
                        "method": "probed",
                        "conf": "10"
                    },
                    "scripts": {
                        "ssh-hostkey": [
                            {
                                "bits": 3072,
                                "fingerprint": "sha256:abc123...",
                                "type": "rsa",
                                "key": "AAAAB3NzaC1yc2E..."
                            }
                        ]
                    }
                },
                {
                    "port": 5432,
                    "protocol": "tcp",
                    "state": "open",
                    "service": {
                        "name": "postgresql",
                        "product": "PostgreSQL DB",
                        "version": "13.12",
                        "extrainfo": "Ubuntu 13.12-1.pgdg20.04+1",
                        "method": "probed",
                        "conf": "10"
                    },
                    "scripts": {
                        "ssl-cert": {
                            "subject": "CN=prod-db-primary.internal.company.com",
                            "issuer": "CN=Company Internal CA",
                            "valid_from": "2023-01-01T00:00:00Z",
                            "valid_to": "2024-01-01T00:00:00Z"
                        }
                    }
                },
                {
                    "port": 9100,
                    "protocol": "tcp",
                    "state": "open",
                    "service": {
                        "name": "node_exporter",
                        "product": "Prometheus Node Exporter",
                        "version": "1.6.1",
                        "method": "probed"
                    }
                },
                {
                    "port": 80,
                    "protocol": "tcp",
                    "state": "filtered",
                    "reason": "no-response"
                },
                {
                    "port": 443,
                    "protocol": "tcp",
                    "state": "closed",
                    "reason": "reset"
                }
            ],
            "security_scan": {
                "firewall_detected": True,
                "intrusion_detection": True,
                "antivirus": "ClamAV 0.103.9",
                "last_patched": "2023-11-15T02:00:00Z",
                "patch_level": "current",
                "security_updates_available": 0
            },
            "compliance": {
                "cis_benchmark": "Level 1",
                "hardening_applied": True,
                "audit_logging": True,
                "file_integrity_monitoring": True
            },
            "monitoring": {
                "agent_installed": True,
                "agent_version": "datadog-agent-7.48.0",
                "metrics_collection": True,
                "log_forwarding": True,
                "health_status": "healthy"
            }
        }

        asset_data = engine._create_host_asset(host_data, "***********/24")

        # Verify basic host information
        assert asset_data["name"] == "prod-db-primary.internal.company.com"
        assert asset_data["asset_type"] == AssetType.SERVER
        assert asset_data["provider_id"] == "*************"
        assert "*************" in asset_data["ip_addresses"]
        assert "prod-db-primary.internal.company.com" in asset_data["dns_names"]

        # Verify open ports
        open_ports = asset_data["ports"]
        assert 22 in open_ports
        assert 5432 in open_ports
        assert 9100 in open_ports
        assert 80 not in open_ports  # filtered port should not be included
        assert 443 not in open_ports  # closed port should not be included

        # Verify configuration details
        config = asset_data["configuration"]
        assert config["network"] == "***********/24"
        assert config["os_info"]["name"] == "Linux Ubuntu 20.04.6 LTS"
        assert config["os_info"]["accuracy"] == "98"
        assert len(config["open_ports"]) == 3  # Only open ports

        # Verify system information
        assert config["system_info"]["cpu_cores"] == 8
        assert config["system_info"]["memory_gb"] == 32
        assert config["system_info"]["architecture"] == "x86_64"

        # Verify security information
        assert config["security_scan"]["firewall_detected"] == True
        assert config["security_scan"]["patch_level"] == "current"
        assert config["compliance"]["cis_benchmark"] == "Level 1"

        # Verify monitoring information
        assert config["monitoring"]["agent_installed"] == True
        assert config["monitoring"]["health_status"] == "healthy"

        # Verify risk assessment considers database service and production environment
        assert asset_data["risk_score"] > 30  # Database servers should have higher risk

        # Verify properties for search and filtering
        properties = asset_data["properties"]
        assert properties["network_segment"] == "***********/24"
        assert properties["discovery_method"] == "nmap"


class TestAssetDiscoveryEdgeCases:
    """Test edge cases and error scenarios in asset discovery."""

    def test_aws_instance_minimal_metadata(self):
        """Test AWS instance with minimal metadata."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Minimal EC2 instance data
        instance_data = {
            "InstanceId": "i-minimal123",
            "InstanceType": "t2.nano",
            "State": {"Name": "pending"},
            # No IP addresses
            # No tags
            # No VPC
            # No security groups
        }

        asset_data = engine._create_ec2_asset(instance_data, "us-west-2")

        # Should still create valid asset
        assert asset_data["name"] == "i-minimal123"  # Falls back to instance ID
        assert asset_data["asset_type"] == AssetType.CLOUD_INSTANCE
        assert asset_data["provider_id"] == "i-minimal123"
        assert asset_data["provider_region"] == "us-west-2"
        assert asset_data["status"] == AssetStatus.PENDING
        assert asset_data["ip_addresses"] == []
        assert asset_data["dns_names"] == []
        assert asset_data["environment"] == "unknown"
        assert asset_data["owner"] == "unknown"
        assert asset_data["team"] == "unknown"

    def test_aws_instance_with_invalid_data(self):
        """Test AWS instance with invalid/corrupted data."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Instance with some invalid data
        instance_data = {
            "InstanceId": "i-invalid123",
            "InstanceType": "invalid.type",
            "State": {"Name": "unknown_state"},
            "PublicIpAddress": "invalid.ip.address",
            "PrivateIpAddress": "256.256.256.256",  # Invalid IP
            "Tags": [
                {"Key": "Name", "Value": "test-instance"},
                {"Key": "", "Value": "empty-key"},  # Invalid tag
                {"Key": "ValidKey", "Value": ""},  # Empty value
                {"Key": None, "Value": "null-key"},  # Null key
            ],
            "LaunchTime": "invalid-date",  # Invalid date
        }

        asset_data = engine._create_ec2_asset(instance_data, "us-west-2")

        # Should handle invalid data gracefully
        assert asset_data["name"] == "test-instance"
        assert asset_data["configuration"]["instance_type"] == "invalid.type"
        assert asset_data["status"] == AssetStatus.UNKNOWN  # Unknown state maps to unknown

        # Invalid IPs should be filtered out
        valid_ips = [ip for ip in asset_data["ip_addresses"] if ip != "invalid.ip.address"]
        assert "invalid.ip.address" not in asset_data["ip_addresses"]

        # Tags should be cleaned
        tags = asset_data["tags"]
        assert "Name" in tags
        assert "ValidKey" in tags
        assert "" not in tags  # Empty key should be filtered

    def test_api_endpoint_with_special_characters(self):
        """Test API endpoint with special characters and edge cases."""
        config = {"tool": "akto"}
        engine = APIDiscoveryEngine(config)

        endpoint_data = {
            "path": "/api/v1/users/{user-id}/files/{file%20name}.json",
            "method": "POST",
            "status_code": 201,
            "auth_required": True,
            "parameters": [
                {
                    "name": "user-id",
                    "type": "path",
                    "required": True,
                    "special_chars": True
                },
                {
                    "name": "file name",  # Space in parameter name
                    "type": "path",
                    "required": True
                },
                {
                    "name": "data",
                    "type": "body",
                    "required": True,
                    "max_size": "10MB",
                    "content_types": ["application/json", "multipart/form-data"]
                }
            ],
            "response_codes": [201, 400, 401, 403, 413, 500],
            "content_encoding": "gzip",
            "cache_control": "no-cache",
            "special_headers": {
                "X-Custom-Header": "special-value",
                "X-Rate-Limit-Remaining": "999",
                "X-Request-ID": "uuid-v4"
            }
        }

        asset_data = engine._create_api_endpoint_asset(endpoint_data, "https://api.example.com")

        # Should handle special characters in path
        assert "POST /api/v1/users/{user-id}/files/{file%20name}.json" in asset_data["name"]
        assert asset_data["provider_id"] == "https://api.example.com/api/v1/users/{user-id}/files/{file%20name}.json"

        # Should preserve special characters in configuration
        config = asset_data["configuration"]
        assert config["path"] == "/api/v1/users/{user-id}/files/{file%20name}.json"
        assert config["method"] == "POST"
        assert config["status_code"] == 201

    def test_network_host_with_no_services(self):
        """Test network host with no open services."""
        config = {"target_networks": ["10.0.0.0/24"]}
        engine = NetworkDiscoveryEngine(config)

        host_data = {
            "ip": "*********",
            "hostname": "stealth-host.internal",
            "os": {"name": "Unknown", "accuracy": "0"},
            "services": []  # No services detected
        }

        asset_data = engine._create_host_asset(host_data, "10.0.0.0/24")

        # Should still create asset
        assert asset_data["name"] == "stealth-host.internal"
        assert asset_data["asset_type"] == AssetType.SERVER
        assert asset_data["ports"] == []
        assert asset_data["protocols"] == []

        # Risk should be lower due to no open ports
        assert asset_data["risk_score"] < 50

    def test_network_host_with_filtered_ports(self):
        """Test network host with only filtered/closed ports."""
        config = {"target_networks": ["**********/24"]}
        engine = NetworkDiscoveryEngine(config)

        host_data = {
            "ip": "************",
            "hostname": "firewall-protected.internal",
            "services": [
                {"port": 22, "protocol": "tcp", "state": "filtered"},
                {"port": 80, "protocol": "tcp", "state": "closed"},
                {"port": 443, "protocol": "tcp", "state": "filtered"},
                {"port": 3389, "protocol": "tcp", "state": "closed"}
            ]
        }

        asset_data = engine._create_host_asset(host_data, "**********/24")

        # Should not include filtered/closed ports in open ports list
        assert asset_data["ports"] == []

        # But should include all scanned ports in configuration
        config = asset_data["configuration"]
        assert len(config["open_ports"]) == 0

        # Risk should be lower due to no open ports
        assert asset_data["risk_score"] < 40

    def test_discovery_with_unicode_and_encoding(self):
        """Test discovery with Unicode characters and encoding issues."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        instance_data = {
            "InstanceId": "i-unicode123",
            "InstanceType": "t3.micro",
            "State": {"Name": "running"},
            "Tags": [
                {"Key": "Name", "Value": "测试服务器"},  # Chinese characters
                {"Key": "Description", "Value": "Sérver with spéciàl chärs"},  # Accented characters
                {"Key": "Team", "Value": "Разработка"},  # Cyrillic characters
                {"Key": "Environment", "Value": "тест"},  # Cyrillic
                {"Key": "Emoji", "Value": "🚀 Production Server 🔥"},  # Emojis
            ]
        }

        asset_data = engine._create_ec2_asset(instance_data, "eu-west-1")

        # Should handle Unicode correctly
        assert asset_data["name"] == "测试服务器"
        assert asset_data["team"] == "Разработка"
        assert asset_data["environment"] == "тест"

        tags = asset_data["tags"]
        assert tags["Description"] == "Sérver with spéciàl chärs"
        assert tags["Emoji"] == "🚀 Production Server 🔥"

    def test_discovery_with_large_datasets(self):
        """Test discovery with large datasets and performance considerations."""
        config = {"tool": "akto", "target_urls": ["https://api.example.com"]}
        engine = APIDiscoveryEngine(config)

        # Simulate large API with many endpoints
        large_endpoints = []
        for i in range(1000):
            endpoint_data = {
                "path": f"/api/v1/resource{i}/subresource/{i}",
                "method": "GET",
                "status_code": 200,
                "response_time": 50 + (i % 100),  # Varying response times
                "auth_required": i % 3 == 0,  # Every 3rd endpoint requires auth
                "parameters": [
                    {"name": f"param{j}", "type": "query", "required": j == 0}
                    for j in range(i % 5)  # Varying number of parameters
                ]
            }
            large_endpoints.append(endpoint_data)

        # Process all endpoints
        assets = []
        for endpoint_data in large_endpoints[:10]:  # Test with first 10 for performance
            asset_data = engine._create_api_endpoint_asset(endpoint_data, "https://api.example.com")
            assets.append(asset_data)

        # Verify all assets were created
        assert len(assets) == 10

        # Verify unique provider IDs
        provider_ids = [asset["provider_id"] for asset in assets]
        assert len(set(provider_ids)) == 10  # All unique

        # Verify risk scores vary based on auth requirements
        auth_required_assets = [asset for asset in assets if asset["configuration"]["authentication_required"]]
        no_auth_assets = [asset for asset in assets if not asset["configuration"]["authentication_required"]]

        if auth_required_assets and no_auth_assets:
            avg_auth_risk = sum(asset["risk_score"] for asset in auth_required_assets) / len(auth_required_assets)
            avg_no_auth_risk = sum(asset["risk_score"] for asset in no_auth_assets) / len(no_auth_assets)
            assert avg_no_auth_risk > avg_auth_risk  # No auth should have higher risk

    def test_discovery_error_handling(self):
        """Test discovery error handling and recovery."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Test with None values
        instance_data = {
            "InstanceId": None,
            "InstanceType": None,
            "State": None,
            "Tags": None
        }

        try:
            asset_data = engine._create_ec2_asset(instance_data, "us-east-1")
            # Should handle None values gracefully
            assert asset_data["provider_id"] is None or asset_data["provider_id"] == ""
            assert asset_data["name"] is not None  # Should have fallback name
        except Exception as e:
            # If it raises an exception, it should be a specific, handled exception
            assert "required" in str(e).lower() or "invalid" in str(e).lower()

    def test_risk_calculation_edge_cases(self):
        """Test risk calculation with edge cases."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Test maximum risk scenario
        high_risk_asset = {
            "asset_type": AssetType.CLOUD_DATABASE,
            "environment": "production",
            "ip_addresses": ["***********"],  # Public IP
            "ports": [22, 23, 80, 443, 3389, 5432, 3306, 1433, 6379, 27017],  # Many high-risk ports
        }

        risk_score, risk_level = engine.calculate_risk_score(high_risk_asset)

        # Should be maximum risk
        assert risk_score == 100  # Capped at 100
        assert risk_level == RiskLevel.CRITICAL

        # Test minimum risk scenario
        low_risk_asset = {
            "asset_type": AssetType.CLOUD_FUNCTION,
            "environment": "development",
            "ip_addresses": ["********"],  # Private IP
            "ports": [],  # No open ports
        }

        risk_score, risk_level = engine.calculate_risk_score(low_risk_asset)

        # Should be low risk
        assert risk_score < 30
        assert risk_level in [RiskLevel.LOW, RiskLevel.INFO]

    def test_asset_relationship_creation(self):
        """Test asset relationship creation with various scenarios."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Test relationship creation
        relationship_data = engine.create_relationship_data(
            source_asset_id="asset-1",
            target_asset_id="asset-2",
            relationship_type=RelationshipType.DEPENDS_ON,
            protocol="https",
            port=443,
            description="Web server depends on database"
        )

        assert relationship_data["source_asset_id"] == "asset-1"
        assert relationship_data["target_asset_id"] == "asset-2"
        assert relationship_data["relationship_type"] == RelationshipType.DEPENDS_ON
        assert relationship_data["protocol"] == "https"
        assert relationship_data["port"] == 443
        assert relationship_data["confidence_score"] == 1.0
        assert relationship_data["is_active"] == True

    def test_discovery_logging_and_events(self):
        """Test discovery logging and event tracking."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)

        # Test event logging
        log_entry = engine.log_discovery_event(
            "test_event",
            "Test message",
            {"key": "value", "count": 42}
        )

        assert log_entry["event_type"] == "test_event"
        assert log_entry["message"] == "Test message"
        assert log_entry["details"]["key"] == "value"
        assert log_entry["details"]["count"] == 42
        assert "timestamp" in log_entry

        # Test error creation
        error = engine.create_error(
            "test_error",
            "Test error message",
            {"error_code": 500, "context": "test"}
        )

        assert error.error_type == "test_error"
        assert error.message == "Test error message"
        assert error.details["error_code"] == 500
        assert error.timestamp is not None
