"""Tests for robust asset service with soft-delete and audit capabilities."""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.services.robust_asset_service import (
    RobustAssetService,
    AssetLockError,
    AssetValidationError,
    AssetNotFoundError,
)
from app.db.models.asset_robust import (
    RobustAsset,
    AssetAuditLog,
    AssetVersion,
    AssetLock,
    AuditAction,
    DataRetentionPolicy,
)


class TestRobustAssetService:
    """Test robust asset service functionality."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        db = Mock()
        db.add.return_value = None
        db.commit.return_value = None
        db.rollback.return_value = None
        db.flush.return_value = None
        return db
    
    @pytest.fixture
    def service(self, mock_db):
        """Create robust asset service."""
        return RobustAssetService(
            db=mock_db,
            user_id="test-user-123",
            session_id="test-session-456"
        )
    
    @pytest.fixture
    def sample_asset_data(self):
        """Sample asset data for testing."""
        return {
            "name": "test-server-01",
            "asset_type": "server",
            "provider": "aws",
            "status": "active",
            "provider_id": "i-1234567890abcdef0",
            "provider_region": "us-east-1",
            "ip_addresses": ["**********", "***********"],
            "dns_names": ["test-server-01.internal"],
            "environment": "production",
            "owner": "platform-team",
            "team": "infrastructure",
            "risk_score": 75,
            "risk_level": "high",
            "configuration": {
                "instance_type": "m5.large",
                "vpc_id": "vpc-12345678"
            },
            "business_criticality": "high",
            "data_classification": "confidential"
        }
    
    def test_create_asset_success(self, service, mock_db, sample_asset_data):
        """Test successful asset creation with audit trail."""
        # Mock asset creation
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.id = uuid.uuid4()
        mock_asset.name = sample_asset_data["name"]
        mock_asset.version = 1
        mock_asset.checksum = "test-checksum"
        
        # Mock validation
        service._validate_asset = Mock(return_value=[])
        service._calculate_checksum = Mock(return_value="test-checksum")
        service._create_asset_snapshot = Mock(return_value=sample_asset_data)
        service._create_audit_log = Mock()
        service._create_version = Mock()
        
        # Mock database operations
        mock_db.add.return_value = None
        mock_db.flush.return_value = None
        mock_db.commit.return_value = None
        
        with patch('app.services.robust_asset_service.RobustAsset', return_value=mock_asset):
            result = service.create_asset(sample_asset_data, "Initial creation")
        
        # Verify asset creation
        assert result == mock_asset
        mock_db.add.assert_called_once_with(mock_asset)
        mock_db.commit.assert_called_once()
        
        # Verify audit trail
        service._create_audit_log.assert_called_once()
        service._create_version.assert_called_once()
    
    def test_create_asset_validation_failure(self, service, sample_asset_data):
        """Test asset creation with validation failure."""
        # Mock validation failure
        service._validate_asset = Mock(return_value=["Asset name is required"])
        
        with pytest.raises(AssetValidationError) as exc_info:
            service.create_asset(sample_asset_data)
        
        assert "Asset validation failed" in str(exc_info.value)
    
    def test_soft_delete_asset_success(self, service, mock_db):
        """Test successful soft delete with audit trail."""
        asset_id = uuid.uuid4()
        
        # Mock asset
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.id = asset_id
        mock_asset.name = "test-asset"
        mock_asset.is_deleted = False
        mock_asset.version = 1
        mock_asset.change_count = 0
        
        # Mock service methods
        service.get_asset = Mock(return_value=mock_asset)
        service._create_asset_snapshot = Mock(return_value={"name": "test-asset"})
        service._create_audit_log = Mock()
        service._create_version = Mock()
        
        result = service.soft_delete_asset(
            asset_id, 
            reason="Security incident",
            retention_policy=DataRetentionPolicy.MEDIUM_TERM
        )
        
        # Verify soft delete
        assert result == True
        mock_asset.soft_delete.assert_called_once_with(
            deleted_by="test-user-123",
            reason="Security incident"
        )
        
        # Verify audit trail
        service._create_audit_log.assert_called_once()
        audit_call = service._create_audit_log.call_args
        assert audit_call[1]["action"] == AuditAction.SOFT_DELETE
        
        # Verify version creation
        service._create_version.assert_called_once()
    
    def test_soft_delete_already_deleted(self, service):
        """Test soft delete of already deleted asset."""
        asset_id = uuid.uuid4()
        
        # Mock already deleted asset
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.is_deleted = True
        
        service.get_asset = Mock(return_value=mock_asset)
        
        result = service.soft_delete_asset(asset_id)
        
        assert result == False
    
    def test_restore_asset_success(self, service, mock_db):
        """Test successful asset restoration."""
        asset_id = uuid.uuid4()
        
        # Mock deleted asset
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.id = asset_id
        mock_asset.name = "test-asset"
        mock_asset.is_deleted = True
        mock_asset.version = 2
        mock_asset.change_count = 1
        
        # Mock service methods
        service.get_asset = Mock(return_value=mock_asset)
        service._create_asset_snapshot = Mock(return_value={"name": "test-asset"})
        service._create_audit_log = Mock()
        service._create_version = Mock()
        
        result = service.restore_asset(asset_id, "False positive deletion")
        
        # Verify restoration
        assert result == True
        mock_asset.restore.assert_called_once_with(restored_by="test-user-123")
        
        # Verify audit trail
        service._create_audit_log.assert_called_once()
        audit_call = service._create_audit_log.call_args
        assert audit_call[1]["action"] == AuditAction.RESTORE
    
    def test_restore_not_deleted_asset(self, service):
        """Test restore of asset that is not deleted."""
        asset_id = uuid.uuid4()
        
        # Mock active asset
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.is_deleted = False
        
        service.get_asset = Mock(return_value=mock_asset)
        
        result = service.restore_asset(asset_id)
        
        assert result == False
    
    def test_update_asset_with_lock_check(self, service, mock_db):
        """Test asset update with lock checking."""
        asset_id = uuid.uuid4()
        update_data = {"risk_score": 85, "risk_level": "critical"}
        
        # Mock asset
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.id = asset_id
        mock_asset.name = "test-asset"
        mock_asset.risk_score = 75
        mock_asset.version = 1
        mock_asset.change_count = 0
        
        # Mock service methods
        service._is_asset_locked = Mock(return_value=False)
        service.get_asset = Mock(return_value=mock_asset)
        service._create_asset_snapshot = Mock(return_value={"name": "test-asset"})
        service._asset_to_dict = Mock(return_value={"name": "test-asset"})
        service._calculate_checksum = Mock(return_value="new-checksum")
        service._validate_asset = Mock(return_value=[])
        service._is_significant_change = Mock(return_value=True)
        service._create_audit_log = Mock()
        service._create_version = Mock()
        
        result = service.update_asset(asset_id, update_data, "Risk assessment update")
        
        # Verify update
        assert result == mock_asset
        assert mock_asset.version == 2
        assert mock_asset.change_count == 1
        
        # Verify lock check
        service._is_asset_locked.assert_called_once_with(asset_id, "write")
        
        # Verify audit trail
        service._create_audit_log.assert_called()
        service._create_version.assert_called_once()
    
    def test_update_asset_locked(self, service):
        """Test asset update when asset is locked."""
        asset_id = uuid.uuid4()
        update_data = {"risk_score": 85}
        
        # Mock locked asset
        service._is_asset_locked = Mock(return_value=True)
        
        with pytest.raises(AssetLockError) as exc_info:
            service.update_asset(asset_id, update_data)
        
        assert f"Asset {asset_id} is locked for writing" in str(exc_info.value)
    
    def test_acquire_lock_success(self, service, mock_db):
        """Test successful lock acquisition."""
        asset_id = uuid.uuid4()
        
        # Mock no existing locks
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = None
        mock_db.query.return_value = mock_query
        
        # Mock lock creation
        mock_lock = Mock()
        mock_lock.id = uuid.uuid4()
        
        with patch('app.services.robust_asset_service.AssetLock', return_value=mock_lock):
            lock_id = service.acquire_lock(asset_id, "write", 30, "asset_update")
        
        assert lock_id == str(mock_lock.id)
        mock_db.add.assert_called_with(mock_lock)
        mock_db.commit.assert_called()
    
    def test_acquire_lock_already_locked(self, service, mock_db):
        """Test lock acquisition when asset is already locked."""
        asset_id = uuid.uuid4()
        
        # Mock existing lock by different user
        existing_lock = Mock()
        existing_lock.locked_by = "other-user"
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = existing_lock
        mock_db.query.return_value = mock_query
        
        with pytest.raises(AssetLockError) as exc_info:
            service.acquire_lock(asset_id)
        
        assert f"Asset {asset_id} is already locked by other-user" in str(exc_info.value)
    
    def test_acquire_lock_extend_own_lock(self, service, mock_db):
        """Test extending own existing lock."""
        asset_id = uuid.uuid4()
        
        # Mock existing lock by same user
        existing_lock = Mock()
        existing_lock.id = uuid.uuid4()
        existing_lock.locked_by = "test-user-123"
        existing_lock.expires_at = datetime.utcnow() + timedelta(minutes=10)
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = existing_lock
        mock_db.query.return_value = mock_query
        
        lock_id = service.acquire_lock(asset_id, duration_minutes=60)
        
        assert lock_id == str(existing_lock.id)
        # Verify lock was extended
        assert existing_lock.expires_at > datetime.utcnow() + timedelta(minutes=50)
    
    def test_release_lock_success(self, service, mock_db):
        """Test successful lock release."""
        lock_id = str(uuid.uuid4())
        
        # Mock lock
        mock_lock = Mock()
        mock_lock.id = lock_id
        mock_lock.locked_by = "test-user-123"
        mock_lock.is_active = True
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_lock
        mock_db.query.return_value = mock_query
        
        result = service.release_lock(lock_id)
        
        assert result == True
        assert mock_lock.is_active == False
        assert mock_lock.released_by == "test-user-123"
        mock_db.commit.assert_called()
    
    def test_release_lock_not_owner(self, service, mock_db):
        """Test lock release by non-owner."""
        lock_id = str(uuid.uuid4())
        
        # Mock lock owned by different user
        mock_lock = Mock()
        mock_lock.locked_by = "other-user"
        
        mock_query = Mock()
        mock_query.filter.return_value.first.return_value = mock_lock
        mock_db.query.return_value = mock_query
        
        with pytest.raises(AssetLockError) as exc_info:
            service.release_lock(lock_id)
        
        assert f"Lock {lock_id} is owned by other-user" in str(exc_info.value)
    
    def test_hard_delete_with_confirmation(self, service, mock_db):
        """Test hard delete with proper confirmation token."""
        asset_id = uuid.uuid4()
        
        # Calculate expected confirmation token
        import hashlib
        expected_token = hashlib.sha256(f"{asset_id}test-user-123".encode()).hexdigest()[:16]
        
        # Mock asset
        mock_asset = Mock(spec=RobustAsset)
        mock_asset.id = asset_id
        mock_asset.name = "test-asset"
        
        service.get_asset = Mock(return_value=mock_asset)
        service._create_asset_snapshot = Mock(return_value={"name": "test-asset"})
        service._create_audit_log = Mock()
        
        # Mock database operations
        mock_validation_query = Mock()
        mock_validation_query.filter.return_value.delete.return_value = None
        mock_db.query.return_value = mock_validation_query
        
        result = service.hard_delete_asset(asset_id, expected_token, "Compliance requirement")
        
        assert result == True
        mock_db.delete.assert_called_with(mock_asset)
        mock_db.commit.assert_called()
        
        # Verify audit log
        service._create_audit_log.assert_called_once()
        audit_call = service._create_audit_log.call_args
        assert audit_call[1]["action"] == AuditAction.DELETE
    
    def test_hard_delete_invalid_token(self, service):
        """Test hard delete with invalid confirmation token."""
        asset_id = uuid.uuid4()
        invalid_token = "invalid-token"
        
        with pytest.raises(ValueError) as exc_info:
            service.hard_delete_asset(asset_id, invalid_token)
        
        assert "Invalid confirmation token" in str(exc_info.value)
    
    def test_asset_validation(self, service):
        """Test asset validation logic."""
        # Test valid asset
        valid_asset = Mock(spec=RobustAsset)
        valid_asset.name = "valid-asset"
        valid_asset.asset_type = "server"
        valid_asset.provider = "aws"
        valid_asset.risk_score = 75
        
        errors = service._validate_asset(valid_asset)
        assert len(errors) == 0
        
        # Test invalid asset
        invalid_asset = Mock(spec=RobustAsset)
        invalid_asset.name = ""  # Empty name
        invalid_asset.asset_type = None  # Missing type
        invalid_asset.provider = "aws"
        invalid_asset.risk_score = 150  # Invalid score
        
        errors = service._validate_asset(invalid_asset)
        assert len(errors) == 3
        assert "Asset name is required" in errors
        assert "Asset type is required" in errors
        assert "Risk score must be between 0 and 100" in errors
    
    def test_checksum_calculation(self, service):
        """Test checksum calculation for data integrity."""
        data1 = {"name": "test", "type": "server", "score": 75}
        data2 = {"score": 75, "name": "test", "type": "server"}  # Different order
        data3 = {"name": "test", "type": "server", "score": 80}  # Different value
        
        checksum1 = service._calculate_checksum(data1)
        checksum2 = service._calculate_checksum(data2)
        checksum3 = service._calculate_checksum(data3)
        
        # Same data should produce same checksum regardless of order
        assert checksum1 == checksum2
        
        # Different data should produce different checksum
        assert checksum1 != checksum3
        
        # Checksums should be valid SHA256 hashes
        assert len(checksum1) == 64
        assert all(c in '0123456789abcdef' for c in checksum1)
    
    def test_significant_change_detection(self, service):
        """Test detection of significant changes for versioning."""
        # Significant changes
        old_values = {"name": "test", "risk_level": "medium"}
        new_values = {"risk_level": "high"}  # Risk level change is significant
        
        assert service._is_significant_change(old_values, new_values) == True
        
        # Non-significant changes
        old_values = {"name": "test", "owner": "team1"}
        new_values = {"owner": "team2"}  # Owner change is not significant
        
        assert service._is_significant_change(old_values, new_values) == False
        
        # Configuration change (significant)
        old_values = {"configuration": {"cpu": 2}}
        new_values = {"configuration": {"cpu": 4}}
        
        assert service._is_significant_change(old_values, new_values) == True
