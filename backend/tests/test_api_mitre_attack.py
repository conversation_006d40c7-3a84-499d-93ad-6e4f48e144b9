"""Tests for MITRE ATT&CK API endpoints."""

import pytest
import json
from unittest.mock import Mock, patch
from datetime import datetime

from fastapi.testclient import TestClient
from app.main import app
from app.services.mitre_attack_service import (
    AttackTechnique,
    AttackGroup,
    AttackSoftware,
    TechniqueCorrelation,
    AttackPattern,
    AttackDomain,
    ConfidenceLevel
)


class TestMitreAttackAPI:
    """Test MITRE ATT&CK API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test_token"}
    
    @pytest.fixture
    def sample_technique(self):
        """Sample MITRE ATT&CK technique."""
        return AttackTechnique(
            technique_id="T1566",
            name="Phishing",
            description="Adversaries may send phishing messages to gain access to victim systems.",
            tactic="initial-access",
            domain=AttackDomain.ENTERPRISE,
            platforms=["Windows", "Linux", "macOS"],
            data_sources=["Email Gateway", "Network Traffic"],
            mitigations=["User Training", "Email Security"],
            detection_methods=["Email Analysis", "Network Monitoring"],
            sub_techniques=["T1566.001", "T1566.002"],
            kill_chain_phases=["initial-access"],
            external_references=[],
            created=datetime.utcnow(),
            modified=datetime.utcnow(),
            version="1.0"
        )
    
    @pytest.fixture
    def sample_group(self):
        """Sample MITRE ATT&CK group."""
        return AttackGroup(
            group_id="G0016",
            name="APT29",
            description="APT29 is a threat group attributed to Russia's Foreign Intelligence Service.",
            aliases=["Cozy Bear", "The Dukes"],
            techniques=["T1566", "T1078", "T1055"],
            software=["S0154", "S0363"],
            associated_campaigns=["SolarWinds"],
            external_references=[],
            created=datetime.utcnow(),
            modified=datetime.utcnow(),
            version="1.0"
        )
    
    @pytest.fixture
    def sample_correlation(self):
        """Sample technique correlation."""
        return TechniqueCorrelation(
            correlation_id="corr_12345",
            event_id="evt_12345",
            technique_id="T1566",
            confidence=ConfidenceLevel.HIGH,
            confidence_score=0.8,
            evidence=["phishing email detected"],
            context={"host": "workstation-001", "user": "john.doe"},
            timestamp=datetime.utcnow(),
            analyst_verified=False,
            false_positive=False
        )
    
    @pytest.fixture
    def sample_pattern(self):
        """Sample attack pattern."""
        return AttackPattern(
            pattern_id="pattern_123",
            name="Multi-stage Attack",
            techniques=["T1566", "T1059", "T1055"],
            sequence=["T1566", "T1059", "T1055"],
            confidence=0.85,
            first_seen=datetime.utcnow(),
            last_seen=datetime.utcnow(),
            event_count=5,
            affected_assets={"workstation-001", "server-002"},
            attributed_groups=["G0016"],
            campaign_id="campaign_001"
        )
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_list_techniques(self, mock_mitre_service, mock_user, client, auth_headers, sample_technique):
        """Test listing MITRE ATT&CK techniques."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.techniques = {"T1566": sample_technique}
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/techniques", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["technique_id"] == "T1566"
        assert data[0]["name"] == "Phishing"
        assert data[0]["tactic"] == "initial-access"
        assert data[0]["domain"] == "enterprise"
        assert data[0]["is_sub_technique"] == False
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_list_techniques_with_filters(self, mock_mitre_service, mock_user, client, auth_headers, sample_technique):
        """Test listing techniques with filters."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.search_techniques.return_value = [sample_technique]
        mock_service.get_techniques_by_tactic.return_value = [sample_technique]
        mock_mitre_service.return_value = mock_service
        
        # Test search filter
        response = client.get("/api/v1/mitre-attack/techniques?search=phishing", headers=auth_headers)
        assert response.status_code == 200
        mock_service.search_techniques.assert_called_with("phishing", None)
        
        # Test tactic filter
        response = client.get("/api/v1/mitre-attack/techniques?tactic=initial-access", headers=auth_headers)
        assert response.status_code == 200
        mock_service.get_techniques_by_tactic.assert_called_with("initial-access", None)
        
        # Test domain filter
        response = client.get("/api/v1/mitre-attack/techniques?domain=enterprise", headers=auth_headers)
        assert response.status_code == 200
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_get_technique(self, mock_mitre_service, mock_user, client, auth_headers, sample_technique):
        """Test getting specific technique."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.get_technique.return_value = sample_technique
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/techniques/T1566", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["technique_id"] == "T1566"
        assert data["name"] == "Phishing"
        assert len(data["platforms"]) == 3
        assert len(data["sub_techniques"]) == 2
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_get_technique_not_found(self, mock_mitre_service, mock_user, client, auth_headers):
        """Test getting non-existent technique."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.get_technique.return_value = None
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/techniques/T9999", headers=auth_headers)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_list_groups(self, mock_mitre_service, mock_user, client, auth_headers, sample_group):
        """Test listing threat actor groups."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.groups = {"G0016": sample_group}
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/groups", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["group_id"] == "G0016"
        assert data[0]["name"] == "APT29"
        assert "Cozy Bear" in data[0]["aliases"]
        assert len(data[0]["techniques"]) == 3
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_get_group(self, mock_mitre_service, mock_user, client, auth_headers, sample_group):
        """Test getting specific group."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.get_group.return_value = sample_group
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/groups/G0016", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["group_id"] == "G0016"
        assert data["name"] == "APT29"
        assert data["description"].startswith("APT29 is a threat group")
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_get_group_techniques(self, mock_mitre_service, mock_user, client, auth_headers, sample_technique):
        """Test getting techniques used by a group."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.get_group_techniques.return_value = [sample_technique]
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/groups/G0016/techniques", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["technique_id"] == "T1566"
        assert data[0]["name"] == "Phishing"
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_correlate_event(self, mock_mitre_service, mock_user, client, auth_headers, sample_correlation, sample_technique):
        """Test event correlation to techniques."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.correlate_event_to_techniques.return_value = [sample_correlation]
        mock_service.get_technique.return_value = sample_technique
        mock_mitre_service.return_value = mock_service
        
        # Make request
        event_data = {
            "event_id": "evt_12345",
            "message": "Suspicious phishing email detected",
            "source": "email_security"
        }
        response = client.post(
            "/api/v1/mitre-attack/correlate",
            json={"event_data": event_data},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["correlation_id"] == "corr_12345"
        assert data[0]["technique_id"] == "T1566"
        assert data[0]["technique_name"] == "Phishing"
        assert data[0]["confidence"] == "high"
        assert data[0]["confidence_score"] == 0.8
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_list_attack_patterns(self, mock_mitre_service, mock_user, client, auth_headers, sample_pattern):
        """Test listing attack patterns."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_service.identify_attack_patterns.return_value = [sample_pattern]
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/patterns?time_window_hours=24", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["pattern_id"] == "pattern_123"
        assert data[0]["name"] == "Multi-stage Attack"
        assert len(data[0]["techniques"]) == 3
        assert data[0]["confidence"] == 0.85
        assert data[0]["affected_assets_count"] == 2
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_enrich_ioc(self, mock_mitre_service, mock_user, client, auth_headers):
        """Test IOC enrichment."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_enrichment = {
            "ioc": "malicious.exe",
            "ioc_type": "filename",
            "attack_context": {
                "techniques": [{"id": "T1566", "name": "Phishing", "confidence": 0.8}],
                "groups": [],
                "software": []
            },
            "confidence": 0.8,
            "timestamp": datetime.utcnow().isoformat()
        }
        mock_service.enrich_ioc_with_attack_context.return_value = mock_enrichment
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.post(
            "/api/v1/mitre-attack/enrich-ioc",
            json={"ioc": "malicious.exe", "ioc_type": "filename"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["ioc"] == "malicious.exe"
        assert data["ioc_type"] == "filename"
        assert data["confidence"] == 0.8
        assert len(data["attack_context"]["techniques"]) == 1
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_generate_navigator_layer(self, mock_mitre_service, mock_user, client, auth_headers):
        """Test ATT&CK Navigator layer generation."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_layer = {
            "name": "Test Layer",
            "description": "Test layer description",
            "domain": "enterprise-attack",
            "techniques": [
                {
                    "techniqueID": "T1566",
                    "tactic": "initial-access",
                    "score": 100,
                    "color": "#ff6666"
                }
            ]
        }
        mock_service.generate_attack_navigator_layer.return_value = mock_layer
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.post(
            "/api/v1/mitre-attack/navigator-layer",
            json={
                "techniques": ["T1566"],
                "name": "Test Layer",
                "description": "Test layer description"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Test Layer"
        assert data["description"] == "Test layer description"
        assert len(data["techniques"]) == 1
        assert data["techniques"][0]["techniqueID"] == "T1566"
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_get_statistics(self, mock_mitre_service, mock_user, client, auth_headers):
        """Test getting statistics."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_stats = {
            "techniques": {
                "total": 100,
                "by_domain": {"enterprise": 80, "mobile": 15, "ics": 5},
                "by_tactic": {"initial-access": 10, "execution": 15}
            },
            "groups": {"total": 50},
            "software": {"total": 200, "malware": 120, "tools": 80},
            "correlations": {"total": 1000, "by_confidence": {"high": 300, "medium": 500, "low": 200}},
            "patterns": {"total": 25}
        }
        mock_service.get_statistics.return_value = mock_stats
        mock_mitre_service.return_value = mock_service
        
        # Make request
        response = client.get("/api/v1/mitre-attack/statistics", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["techniques"]["total"] == 100
        assert data["groups"]["total"] == 50
        assert data["software"]["total"] == 200
        assert data["correlations"]["total"] == 1000
        assert data["patterns"]["total"] == 25
    
    @patch('app.api.deps.get_current_active_user')
    @patch('app.api.v1.mitre_attack.get_mitre_service')
    def test_update_attack_data(self, mock_mitre_service, mock_user, client, auth_headers):
        """Test updating ATT&CK data."""
        # Mock user authentication
        mock_user.return_value = Mock()
        
        # Mock MITRE service
        mock_service = Mock()
        mock_mitre_service.return_value = mock_service
        
        # Test update specific domain
        response = client.post("/api/v1/mitre-attack/update?domain=enterprise", headers=auth_headers)
        assert response.status_code == 200
        assert "enterprise" in response.json()["message"]
        
        # Test update all domains
        response = client.post("/api/v1/mitre-attack/update", headers=auth_headers)
        assert response.status_code == 200
        assert "all domains" in response.json()["message"]
        
        # Test invalid domain
        response = client.post("/api/v1/mitre-attack/update?domain=invalid", headers=auth_headers)
        assert response.status_code == 400
        assert "Invalid domain" in response.json()["detail"]
    
    def test_request_validation(self, client, auth_headers):
        """Test request validation."""
        # Test missing required fields
        response = client.post(
            "/api/v1/mitre-attack/correlate",
            json={},
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
        
        # Test invalid data types
        response = client.post(
            "/api/v1/mitre-attack/enrich-ioc",
            json={
                "ioc": 123,  # Should be string
                "ioc_type": ["not", "a", "string"]  # Should be string
            },
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
