"""Tests for user service."""

import pytest
import uuid
from datetime import datetime, timedelta
from app.services.user_service import (
    UserService, 
    UserNotFoundError, 
    UserAlreadyExistsError,
    InsufficientPermissionsError
)
from app.schemas.user import UserCreate, UserUpdate
from app.db.models.user import User, User<PERSON><PERSON><PERSON><PERSON>, UserSession
from app.core.permissions import UserRole


class TestUserService:
    """Test user service functionality."""
    
    def test_create_user_success(self, db_session, user_service, system_roles, sample_user_data):
        """Test successful user creation."""
        user_create = UserCreate(**sample_user_data)
        user = user_service.create_user(user_create)
        
        assert user is not None
        assert user.username == sample_user_data["username"]
        assert user.email == sample_user_data["email"]
        assert user.full_name == sample_user_data["full_name"]
        assert user.is_active == sample_user_data["is_active"]
        assert user.check_password(sample_user_data["password"])
        
        # Check roles were assigned
        role_names = [role.name for role in user.roles]
        assert "analyst" in role_names
    
    def test_create_user_duplicate_username(self, db_session, user_service, test_user, sample_user_data):
        """Test user creation with duplicate username."""
        # Try to create user with existing username
        duplicate_data = sample_user_data.copy()
        duplicate_data["username"] = test_user.username
        duplicate_data["email"] = "<EMAIL>"
        
        user_create = UserCreate(**duplicate_data)
        
        with pytest.raises(UserAlreadyExistsError) as exc_info:
            user_service.create_user(user_create)
        
        assert "Username already exists" in str(exc_info.value)
    
    def test_create_user_duplicate_email(self, db_session, user_service, test_user, sample_user_data):
        """Test user creation with duplicate email."""
        # Try to create user with existing email
        duplicate_data = sample_user_data.copy()
        duplicate_data["username"] = "differentuser"
        duplicate_data["email"] = test_user.email
        
        user_create = UserCreate(**duplicate_data)
        
        with pytest.raises(UserAlreadyExistsError) as exc_info:
            user_service.create_user(user_create)
        
        assert "Email already exists" in str(exc_info.value)
    
    def test_get_user_by_id_success(self, user_service, test_user):
        """Test getting user by ID."""
        user = user_service.get_user_by_id(test_user.id)
        
        assert user is not None
        assert user.id == test_user.id
        assert user.username == test_user.username
    
    def test_get_user_by_id_not_found(self, user_service):
        """Test getting user by non-existent ID."""
        non_existent_id = uuid.uuid4()
        user = user_service.get_user_by_id(non_existent_id)
        
        assert user is None
    
    def test_get_user_by_username_success(self, user_service, test_user):
        """Test getting user by username."""
        user = user_service.get_user_by_username(test_user.username)
        
        assert user is not None
        assert user.id == test_user.id
        assert user.username == test_user.username
    
    def test_get_user_by_username_case_insensitive(self, user_service, test_user):
        """Test getting user by username is case insensitive."""
        user = user_service.get_user_by_username(test_user.username.upper())
        
        assert user is not None
        assert user.id == test_user.id
    
    def test_get_user_by_username_not_found(self, user_service):
        """Test getting user by non-existent username."""
        user = user_service.get_user_by_username("nonexistent")
        
        assert user is None
    
    def test_get_user_by_email_success(self, user_service, test_user):
        """Test getting user by email."""
        user = user_service.get_user_by_email(test_user.email)
        
        assert user is not None
        assert user.id == test_user.id
        assert user.email == test_user.email
    
    def test_get_user_by_email_case_insensitive(self, user_service, test_user):
        """Test getting user by email is case insensitive."""
        user = user_service.get_user_by_email(test_user.email.upper())
        
        assert user is not None
        assert user.id == test_user.id
    
    def test_get_user_by_email_not_found(self, user_service):
        """Test getting user by non-existent email."""
        user = user_service.get_user_by_email("<EMAIL>")
        
        assert user is None
    
    def test_update_user_success(self, db_session, user_service, test_user):
        """Test successful user update."""
        update_data = UserUpdate(
            full_name="Updated Name",
            phone_number="+9876543210",
            department="Updated Department"
        )
        
        updated_user = user_service.update_user(test_user.id, update_data)
        
        assert updated_user.full_name == "Updated Name"
        assert updated_user.phone_number == "+9876543210"
        assert updated_user.department == "Updated Department"
        
        # Original data should remain unchanged
        assert updated_user.username == test_user.username
        assert updated_user.email == test_user.email
    
    def test_update_user_not_found(self, user_service):
        """Test updating non-existent user."""
        non_existent_id = uuid.uuid4()
        update_data = UserUpdate(full_name="New Name")
        
        with pytest.raises(UserNotFoundError):
            user_service.update_user(non_existent_id, update_data)
    
    def test_update_user_duplicate_email(self, db_session, user_service, test_user, admin_user):
        """Test updating user with duplicate email."""
        update_data = UserUpdate(email=admin_user.email)
        
        with pytest.raises(UserAlreadyExistsError) as exc_info:
            user_service.update_user(test_user.id, update_data)
        
        assert "Email already exists" in str(exc_info.value)
    
    def test_delete_user_success(self, db_session, user_service, test_user):
        """Test successful user deletion (soft delete)."""
        success = user_service.delete_user(test_user.id)
        
        assert success is True
        
        # Check user is soft deleted
        db_session.refresh(test_user)
        assert test_user.is_deleted is True
        assert test_user.deleted_at is not None
        
        # User should not be found by normal queries
        user = user_service.get_user_by_id(test_user.id)
        assert user is None
    
    def test_delete_user_not_found(self, user_service):
        """Test deleting non-existent user."""
        non_existent_id = uuid.uuid4()
        success = user_service.delete_user(non_existent_id)
        
        assert success is False
    
    def test_delete_user_terminates_sessions(self, db_session, user_service, test_user):
        """Test that deleting user terminates all sessions."""
        # Create user sessions
        session1 = UserSession(
            user_id=test_user.id,
            session_token="token1",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=True
        )
        session2 = UserSession(
            user_id=test_user.id,
            session_token="token2",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=True
        )
        db_session.add_all([session1, session2])
        db_session.commit()
        
        # Delete user
        user_service.delete_user(test_user.id)
        
        # Check sessions are terminated
        db_session.refresh(session1)
        db_session.refresh(session2)
        assert session1.is_active is False
        assert session2.is_active is False
        assert session1.logout_reason == "user_deleted"
        assert session2.logout_reason == "user_deleted"
    
    def test_delete_user_revokes_api_keys(self, db_session, user_service, test_user):
        """Test that deleting user revokes all API keys."""
        # Create API keys
        api_key1 = UserAPIKey(
            user_id=test_user.id,
            name="Key 1",
            key_hash="hash1",
            key_prefix="br_1",
            is_active=True
        )
        api_key2 = UserAPIKey(
            user_id=test_user.id,
            name="Key 2",
            key_hash="hash2",
            key_prefix="br_2",
            is_active=True
        )
        db_session.add_all([api_key1, api_key2])
        db_session.commit()
        
        # Delete user
        user_service.delete_user(test_user.id)
        
        # Check API keys are revoked
        db_session.refresh(api_key1)
        db_session.refresh(api_key2)
        assert api_key1.is_active is False
        assert api_key2.is_active is False
    
    def test_list_users_default(self, db_session, user_service, test_user, admin_user):
        """Test listing users with default parameters."""
        users, total = user_service.list_users()
        
        assert total >= 2  # At least test_user and admin_user
        assert len(users) >= 2
        
        # Check users are returned
        user_ids = [user.id for user in users]
        assert test_user.id in user_ids
        assert admin_user.id in user_ids
    
    def test_list_users_pagination(self, db_session, user_service, test_user, admin_user):
        """Test user listing with pagination."""
        users, total = user_service.list_users(page=1, size=1)
        
        assert total >= 2
        assert len(users) == 1
        
        # Get second page
        users_page2, total_page2 = user_service.list_users(page=2, size=1)
        
        assert total_page2 == total
        assert len(users_page2) == 1
        assert users[0].id != users_page2[0].id
    
    def test_list_users_search(self, db_session, user_service, test_user):
        """Test user listing with search."""
        users, total = user_service.list_users(search=test_user.username[:4])
        
        assert total >= 1
        user_usernames = [user.username for user in users]
        assert test_user.username in user_usernames
    
    def test_list_users_role_filter(self, db_session, user_service, test_user, admin_user):
        """Test user listing with role filter."""
        users, total = user_service.list_users(role_filter="admin")
        
        assert total >= 1
        user_ids = [user.id for user in users]
        assert admin_user.id in user_ids
        
        # test_user should not be in admin results
        if test_user.id in user_ids:
            # Check if test_user actually has admin role
            test_user_roles = [role.name for role in test_user.roles]
            assert "admin" in test_user_roles
    
    def test_list_users_department_filter(self, db_session, user_service, test_user):
        """Test user listing with department filter."""
        users, total = user_service.list_users(department_filter=test_user.department)
        
        assert total >= 1
        user_ids = [user.id for user in users]
        assert test_user.id in user_ids
    
    def test_list_users_active_only(self, db_session, user_service, test_user):
        """Test user listing with active only filter."""
        # Deactivate test user
        test_user.is_active = False
        db_session.commit()
        
        # List active users only
        users, total = user_service.list_users(active_only=True)
        
        user_ids = [user.id for user in users]
        assert test_user.id not in user_ids
        
        # List all users
        all_users, all_total = user_service.list_users(active_only=False)
        
        all_user_ids = [user.id for user in all_users]
        assert test_user.id in all_user_ids
    
    def test_assign_roles_success(self, db_session, user_service, test_user):
        """Test successful role assignment."""
        success = user_service.assign_roles(test_user.id, ["soc_operator", "analyst"])
        
        assert success is True
        
        # Check roles were assigned
        db_session.refresh(test_user)
        role_names = [role.name for role in test_user.roles]
        assert "soc_operator" in role_names
        assert "analyst" in role_names
    
    def test_assign_roles_user_not_found(self, user_service):
        """Test role assignment for non-existent user."""
        non_existent_id = uuid.uuid4()
        success = user_service.assign_roles(non_existent_id, ["analyst"])
        
        assert success is False
    
    def test_remove_roles_success(self, db_session, user_service, test_user):
        """Test successful role removal."""
        # First assign multiple roles
        user_service.assign_roles(test_user.id, ["soc_operator", "analyst"])
        db_session.refresh(test_user)
        
        # Remove one role
        success = user_service.remove_roles(test_user.id, ["analyst"])
        
        assert success is True
        
        # Check role was removed
        db_session.refresh(test_user)
        role_names = [role.name for role in test_user.roles]
        assert "analyst" not in role_names
        assert "soc_operator" in role_names
    
    def test_remove_roles_user_not_found(self, user_service):
        """Test role removal for non-existent user."""
        non_existent_id = uuid.uuid4()
        success = user_service.remove_roles(non_existent_id, ["analyst"])
        
        assert success is False


class TestAPIKeyManagement:
    """Test API key management functionality."""
    
    def test_create_api_key_success(self, db_session, user_service, test_user):
        """Test successful API key creation."""
        api_key, secret_key = user_service.create_api_key(
            user_id=test_user.id,
            name="Test API Key",
            description="Test description",
            scopes=["read_assets", "create_incident"],
            allowed_ips=["*************"],
            rate_limit=1000
        )
        
        assert api_key is not None
        assert api_key.user_id == test_user.id
        assert api_key.name == "Test API Key"
        assert api_key.description == "Test description"
        assert api_key.scopes == ["read_assets", "create_incident"]
        assert api_key.allowed_ips == ["*************"]
        assert api_key.rate_limit == 1000
        assert api_key.is_active is True
        
        assert secret_key is not None
        assert secret_key.startswith("br_")
        assert len(secret_key) > 10
    
    def test_create_api_key_user_not_found(self, user_service):
        """Test API key creation for non-existent user."""
        non_existent_id = uuid.uuid4()
        
        with pytest.raises(UserNotFoundError):
            user_service.create_api_key(
                user_id=non_existent_id,
                name="Test Key"
            )
    
    def test_revoke_api_key_success(self, db_session, user_service, test_user):
        """Test successful API key revocation."""
        # Create API key
        api_key, _ = user_service.create_api_key(
            user_id=test_user.id,
            name="Test Key"
        )
        
        # Revoke API key
        success = user_service.revoke_api_key(api_key.id)
        
        assert success is True
        
        # Check API key is revoked
        db_session.refresh(api_key)
        assert api_key.is_active is False
    
    def test_revoke_api_key_not_found(self, user_service):
        """Test revoking non-existent API key."""
        non_existent_id = uuid.uuid4()
        success = user_service.revoke_api_key(non_existent_id)
        
        assert success is False


class TestSessionManagement:
    """Test session management functionality."""
    
    def test_get_user_sessions_active_only(self, db_session, user_service, test_user):
        """Test getting active user sessions only."""
        # Create active and inactive sessions
        active_session = UserSession(
            user_id=test_user.id,
            session_token="active_token",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=True
        )
        inactive_session = UserSession(
            user_id=test_user.id,
            session_token="inactive_token",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=False
        )
        db_session.add_all([active_session, inactive_session])
        db_session.commit()
        
        sessions = user_service.get_user_sessions(test_user.id, active_only=True)
        
        assert len(sessions) == 1
        assert sessions[0].session_token == "active_token"
    
    def test_get_user_sessions_all(self, db_session, user_service, test_user):
        """Test getting all user sessions."""
        # Create active and inactive sessions
        active_session = UserSession(
            user_id=test_user.id,
            session_token="active_token",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=True
        )
        inactive_session = UserSession(
            user_id=test_user.id,
            session_token="inactive_token",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=False
        )
        db_session.add_all([active_session, inactive_session])
        db_session.commit()
        
        sessions = user_service.get_user_sessions(test_user.id, active_only=False)
        
        assert len(sessions) == 2
        session_tokens = [session.session_token for session in sessions]
        assert "active_token" in session_tokens
        assert "inactive_token" in session_tokens
    
    def test_terminate_user_session_success(self, db_session, user_service, test_user):
        """Test successful session termination."""
        # Create session
        session = UserSession(
            user_id=test_user.id,
            session_token="test_token",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        
        # Terminate session
        success = user_service.terminate_user_session(session.id, "admin_terminated")
        
        assert success is True
        
        # Check session is terminated
        db_session.refresh(session)
        assert session.is_active is False
        assert session.logout_reason == "admin_terminated"
    
    def test_terminate_user_session_not_found(self, user_service):
        """Test terminating non-existent session."""
        non_existent_id = uuid.uuid4()
        success = user_service.terminate_user_session(non_existent_id)
        
        assert success is False


class TestUserStatistics:
    """Test user statistics functionality."""
    
    def test_get_user_statistics(self, db_session, user_service, test_user, admin_user):
        """Test getting user statistics."""
        stats = user_service.get_user_statistics()
        
        assert isinstance(stats, dict)
        assert "total_users" in stats
        assert "active_users" in stats
        assert "locked_users" in stats
        assert "inactive_users" in stats
        assert "recent_logins_24h" in stats
        assert "users_by_role" in stats
        
        assert stats["total_users"] >= 2
        assert stats["active_users"] >= 2
        assert isinstance(stats["users_by_role"], dict)
    
    def test_get_user_statistics_with_locked_user(self, db_session, user_service, test_user):
        """Test user statistics with locked user."""
        # Lock test user
        test_user.lock_account()
        db_session.commit()
        
        stats = user_service.get_user_statistics()
        
        assert stats["locked_users"] >= 1
    
    def test_get_user_statistics_with_recent_login(self, db_session, user_service, test_user):
        """Test user statistics with recent login."""
        # Update last login to recent time
        test_user.last_login_at = datetime.utcnow() - timedelta(hours=1)
        db_session.commit()
        
        stats = user_service.get_user_statistics()
        
        assert stats["recent_logins_24h"] >= 1
