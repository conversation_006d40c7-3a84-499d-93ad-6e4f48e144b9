"""Tests for core security utilities."""

import pytest
from datetime import datetime, timedelta
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_password,
    get_password_hash,
    generate_password_reset_token,
    verify_password_reset_token,
    generate_secure_random_string,
    generate_api_key,
    mask_sensitive_data,
    SecurityHeaders,
)


class TestPasswordHashing:
    """Test password hashing functions."""
    
    def test_password_hashing_and_verification(self):
        """Test password hashing and verification."""
        password = "TestPassword123!"
        hashed = get_password_hash(password)
        
        # Hash should be different from original password
        assert hashed != password
        assert len(hashed) > 0
        
        # Verification should work
        assert verify_password(password, hashed) is True
        
        # Wrong password should fail
        assert verify_password("WrongPassword", hashed) is False
    
    def test_different_passwords_different_hashes(self):
        """Test that different passwords produce different hashes."""
        password1 = "Password123!"
        password2 = "DifferentPassword456!"
        
        hash1 = get_password_hash(password1)
        hash2 = get_password_hash(password2)
        
        assert hash1 != hash2
    
    def test_same_password_different_hashes(self):
        """Test that same password produces different hashes (salt)."""
        password = "TestPassword123!"
        
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Hashes should be different due to salt
        assert hash1 != hash2
        
        # But both should verify correctly
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True


class TestJWTTokens:
    """Test JWT token creation and verification."""
    
    def test_create_and_verify_access_token(self):
        """Test access token creation and verification."""
        subject = "test_user_id"
        token = create_access_token(subject)
        
        assert token is not None
        assert len(token) > 0
        
        # Verify token
        verified_subject = verify_token(token)
        assert verified_subject == subject
    
    def test_create_access_token_with_expiration(self):
        """Test access token with custom expiration."""
        subject = "test_user_id"
        expires_delta = timedelta(minutes=5)
        token = create_access_token(subject, expires_delta)
        
        assert token is not None
        verified_subject = verify_token(token)
        assert verified_subject == subject
    
    def test_create_and_verify_refresh_token(self):
        """Test refresh token creation and verification."""
        subject = "test_user_id"
        token = create_refresh_token(subject)
        
        assert token is not None
        assert len(token) > 0
        
        # Verify token
        verified_subject = verify_token(token)
        assert verified_subject == subject
    
    def test_verify_invalid_token(self):
        """Test verification of invalid token."""
        invalid_token = "invalid.token.here"
        result = verify_token(invalid_token)
        assert result is None
    
    def test_verify_empty_token(self):
        """Test verification of empty token."""
        result = verify_token("")
        assert result is None
    
    def test_verify_none_token(self):
        """Test verification of None token."""
        result = verify_token(None)
        assert result is None


class TestPasswordResetTokens:
    """Test password reset token functions."""
    
    def test_generate_and_verify_password_reset_token(self):
        """Test password reset token generation and verification."""
        email = "<EMAIL>"
        token = generate_password_reset_token(email)
        
        assert token is not None
        assert len(token) > 0
        
        # Verify token
        verified_email = verify_password_reset_token(token)
        assert verified_email == email
    
    def test_verify_invalid_password_reset_token(self):
        """Test verification of invalid password reset token."""
        invalid_token = "invalid.token.here"
        result = verify_password_reset_token(invalid_token)
        assert result is None


class TestRandomGeneration:
    """Test random string and key generation."""
    
    def test_generate_secure_random_string_default_length(self):
        """Test secure random string generation with default length."""
        random_string = generate_secure_random_string()
        
        assert random_string is not None
        assert len(random_string) > 0
        assert isinstance(random_string, str)
    
    def test_generate_secure_random_string_custom_length(self):
        """Test secure random string generation with custom length."""
        length = 16
        random_string = generate_secure_random_string(length)
        
        assert random_string is not None
        assert isinstance(random_string, str)
        # URL-safe base64 encoding may result in different length
        assert len(random_string) > 0
    
    def test_generate_different_random_strings(self):
        """Test that different calls generate different strings."""
        string1 = generate_secure_random_string()
        string2 = generate_secure_random_string()
        
        assert string1 != string2
    
    def test_generate_api_key(self):
        """Test API key generation."""
        api_key = generate_api_key()
        
        assert api_key is not None
        assert api_key.startswith("br_")
        assert len(api_key) > 10  # Should be longer than just the prefix
    
    def test_generate_different_api_keys(self):
        """Test that different API keys are generated."""
        key1 = generate_api_key()
        key2 = generate_api_key()
        
        assert key1 != key2
        assert key1.startswith("br_")
        assert key2.startswith("br_")


class TestDataMasking:
    """Test sensitive data masking."""
    
    def test_mask_sensitive_data_default(self):
        """Test data masking with default visible characters."""
        data = "sensitive_password_123"
        masked = mask_sensitive_data(data)
        
        assert masked.startswith("sens")
        assert "*" in masked
        assert len(masked) == len(data)
    
    def test_mask_sensitive_data_custom_visible(self):
        """Test data masking with custom visible characters."""
        data = "password123"
        visible_chars = 2
        masked = mask_sensitive_data(data, visible_chars)
        
        assert masked.startswith("pa")
        assert "*" in masked
        assert len(masked) == len(data)
    
    def test_mask_short_data(self):
        """Test masking data shorter than visible characters."""
        data = "abc"
        masked = mask_sensitive_data(data, 5)
        
        assert masked == "***"
        assert len(masked) == len(data)
    
    def test_mask_empty_data(self):
        """Test masking empty data."""
        data = ""
        masked = mask_sensitive_data(data)
        
        assert masked == ""


class TestSecurityHeaders:
    """Test security headers generation."""
    
    def test_get_security_headers(self):
        """Test security headers generation."""
        headers = SecurityHeaders.get_security_headers()
        
        assert isinstance(headers, dict)
        assert len(headers) > 0
        
        # Check for essential security headers
        expected_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options",
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy",
            "Referrer-Policy",
            "Permissions-Policy"
        ]
        
        for header in expected_headers:
            assert header in headers
            assert headers[header] is not None
            assert len(headers[header]) > 0
    
    def test_security_headers_values(self):
        """Test specific security header values."""
        headers = SecurityHeaders.get_security_headers()
        
        assert headers["X-Content-Type-Options"] == "nosniff"
        assert headers["X-Frame-Options"] == "DENY"
        assert "max-age=" in headers["Strict-Transport-Security"]
        assert "default-src 'self'" in headers["Content-Security-Policy"]


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_verify_token_with_malformed_jwt(self):
        """Test token verification with malformed JWT."""
        malformed_tokens = [
            "not.a.jwt",
            "too.few.parts",
            "too.many.parts.here.extra",
            "invalid-base64.invalid-base64.invalid-base64"
        ]
        
        for token in malformed_tokens:
            result = verify_token(token)
            assert result is None
    
    def test_password_verification_edge_cases(self):
        """Test password verification edge cases."""
        password = "TestPassword123!"
        hashed = get_password_hash(password)
        
        # Empty password
        assert verify_password("", hashed) is False
        
        # None password
        assert verify_password(None, hashed) is False
        
        # Empty hash
        assert verify_password(password, "") is False
        
        # None hash
        assert verify_password(password, None) is False
    
    def test_mask_sensitive_data_edge_cases(self):
        """Test data masking edge cases."""
        # None data
        with pytest.raises(TypeError):
            mask_sensitive_data(None)
        
        # Zero visible characters
        data = "password"
        masked = mask_sensitive_data(data, 0)
        assert masked == "*" * len(data)
        
        # Negative visible characters
        masked = mask_sensitive_data(data, -1)
        assert masked == "*" * len(data)
