"""
Comprehensive validation tests for multi-cloud discovery integration.

This test suite validates the complete multi-cloud discovery workflow
without requiring actual cloud credentials or database connections.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from typing import Dict, List, Any
import uuid

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.discovery.base_discovery import BaseDiscoveryEngine, DiscoveryResult
from app.services.discovery.cloud_discovery import CloudDiscoveryEngine
from app.db.models.asset import AssetType, AssetProvider, DiscoverySource, RiskLevel


class TestMultiCloudValidation:
    """Comprehensive validation tests for multi-cloud discovery."""
    
    def test_base_discovery_engine_interface(self):
        """Test that base discovery engine defines proper interface."""
        # Test abstract methods exist
        assert hasattr(BaseDiscoveryEngine, 'provider')
        assert hasattr(BaseDiscoveryEngine, 'discovery_source')
        assert hasattr(BaseDiscoveryEngine, 'supported_asset_types')
        assert hasattr(BaseDiscoveryEngine, 'validate_config')
        assert hasattr(BaseDiscoveryEngine, 'discover')
        
        # Test DiscoveryResult structure
        result = DiscoveryResult()
        assert hasattr(result, 'assets_discovered')
        assert hasattr(result, 'relationships_discovered')
        assert hasattr(result, 'total_assets')
        assert hasattr(result, 'total_relationships')
        assert hasattr(result, 'execution_time_seconds')
        assert hasattr(result, 'success_rate')
        assert hasattr(result, 'has_errors')
    
    def test_discovery_result_calculations(self):
        """Test DiscoveryResult property calculations."""
        result = DiscoveryResult(
            total_assets=10,
            total_relationships=5,
            total_errors=2
        )
        
        # Test success rate calculation
        expected_success_rate = 1.0 - (2 / 15)  # 2 errors out of 15 total operations
        assert abs(result.success_rate - expected_success_rate) < 0.001
        
        # Test has_errors property
        assert result.has_errors is True
        
        # Test with no errors
        result_no_errors = DiscoveryResult(total_assets=5, total_relationships=3, total_errors=0)
        assert result_no_errors.success_rate == 1.0
        assert result_no_errors.has_errors is False
    
    def test_cloud_discovery_engine_provider_routing(self):
        """Test that cloud discovery engine routes to correct providers."""
        # Test AWS routing
        aws_config = {"provider": "aws", "access_key_id": "test", "secret_access_key": "test"}
        aws_engine = CloudDiscoveryEngine(aws_config)
        assert aws_engine.provider == AssetProvider.AWS
        assert aws_engine.discovery_source == DiscoverySource.AWS_API
        assert aws_engine.provider_name == "aws"
        
        # Test unsupported provider
        with pytest.raises(ValueError) as exc_info:
            CloudDiscoveryEngine({"provider": "unsupported"})
        assert "Unsupported cloud provider" in str(exc_info.value)
    
    def test_asset_type_enums_completeness(self):
        """Test that all required asset types are defined."""
        required_types = [
            'CLOUD_INSTANCE', 'DATABASE', 'STORAGE', 'APPLICATION',
            'CONTAINER', 'NETWORK_DEVICE', 'LOAD_BALANCER', 'SERVER',
            'WORKSTATION', 'IOT_DEVICE', 'API_ENDPOINT'
        ]
        
        for asset_type in required_types:
            assert hasattr(AssetType, asset_type), f"Missing asset type: {asset_type}"
    
    def test_provider_enums_completeness(self):
        """Test that all cloud providers are defined."""
        required_providers = ['AWS', 'AZURE', 'GCP', 'ON_PREMISES']
        
        for provider in required_providers:
            assert hasattr(AssetProvider, provider), f"Missing provider: {provider}"
    
    def test_discovery_source_enums_completeness(self):
        """Test that all discovery sources are defined."""
        required_sources = [
            'AWS_API', 'AZURE_API', 'GCP_API', 'CLOUD_API',
            'AKTO', 'KITERUNNER', 'API_DISCOVERY',
            'NMAP', 'MASSCAN', 'NETWORK_SCAN'
        ]
        
        for source in required_sources:
            assert hasattr(DiscoverySource, source), f"Missing discovery source: {source}"
    
    def test_risk_level_enums_completeness(self):
        """Test that all risk levels are defined."""
        required_levels = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
        
        for level in required_levels:
            assert hasattr(RiskLevel, level), f"Missing risk level: {level}"


class TestAzureDiscoveryValidation:
    """Validation tests for Azure discovery service."""
    
    @pytest.fixture
    def azure_config(self):
        """Standard Azure configuration for testing."""
        return {
            "subscription_id": "12345678-1234-1234-1234-123456789012",
            "client_id": "test-client-id",
            "client_secret": "test-client-secret",
            "tenant_id": "test-tenant-id",
            "services": ["compute", "storage", "sql"]
        }
    
    def test_azure_discovery_import(self):
        """Test that Azure discovery service can be imported."""
        try:
            from app.services.discovery.azure_discovery import AzureDiscoveryService
            assert AzureDiscoveryService is not None
        except ImportError as e:
            pytest.skip(f"Azure discovery service import failed: {e}")
    
    @patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', True)
    @patch('app.services.discovery.azure_discovery.ClientSecretCredential')
    @patch('app.services.discovery.azure_discovery.ResourceManagementClient')
    @patch('app.services.discovery.azure_discovery.ComputeManagementClient')
    @patch('app.services.discovery.azure_discovery.StorageManagementClient')
    @patch('app.services.discovery.azure_discovery.SqlManagementClient')
    @patch('app.services.discovery.azure_discovery.WebSiteManagementClient')
    @patch('app.services.discovery.azure_discovery.ContainerServiceClient')
    @patch('app.services.discovery.azure_discovery.NetworkManagementClient')
    def test_azure_service_initialization(self, *mocks, azure_config):
        """Test Azure service initialization with mocked dependencies."""
        from app.services.discovery.azure_discovery import AzureDiscoveryService
        
        service = AzureDiscoveryService(azure_config)
        assert service.subscription_id == azure_config["subscription_id"]
        assert service.provider == AssetProvider.AZURE
        assert service.discovery_source == DiscoverySource.AZURE_API
        assert AssetType.CLOUD_INSTANCE in service.supported_asset_types
    
    def test_azure_config_validation(self, azure_config):
        """Test Azure configuration validation."""
        # Test missing subscription_id
        invalid_config = azure_config.copy()
        del invalid_config["subscription_id"]
        
        with patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', True):
            with pytest.raises(ValueError) as exc_info:
                from app.services.discovery.azure_discovery import AzureDiscoveryService
                AzureDiscoveryService(invalid_config)
            assert "subscription_id is required" in str(exc_info.value)
    
    def test_azure_sdk_availability_check(self, azure_config):
        """Test Azure SDK availability check."""
        with patch('app.services.discovery.azure_discovery.AZURE_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                from app.services.discovery.azure_discovery import AzureDiscoveryService
                AzureDiscoveryService(azure_config)
            assert "Azure SDK not available" in str(exc_info.value)


class TestGCPDiscoveryValidation:
    """Validation tests for GCP discovery service."""
    
    @pytest.fixture
    def gcp_config(self):
        """Standard GCP configuration for testing."""
        return {
            "project_id": "test-project-12345",
            "credentials_path": "/path/to/service-account.json",
            "services": ["compute", "storage", "sql"]
        }
    
    def test_gcp_discovery_import(self):
        """Test that GCP discovery service can be imported."""
        try:
            from app.services.discovery.gcp_discovery import GCPDiscoveryService
            assert GCPDiscoveryService is not None
        except ImportError as e:
            pytest.skip(f"GCP discovery service import failed: {e}")
    
    @patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', True)
    @patch('app.services.discovery.gcp_discovery.service_account')
    @patch('app.services.discovery.gcp_discovery.compute_v1')
    @patch('app.services.discovery.gcp_discovery.storage')
    @patch('app.services.discovery.gcp_discovery.sql_v1')
    @patch('app.services.discovery.gcp_discovery.container_v1')
    @patch('app.services.discovery.gcp_discovery.asset_v1')
    def test_gcp_service_initialization(self, *mocks, gcp_config):
        """Test GCP service initialization with mocked dependencies."""
        from app.services.discovery.gcp_discovery import GCPDiscoveryService
        
        service = GCPDiscoveryService(gcp_config)
        assert service.project_id == gcp_config["project_id"]
        assert service.provider == AssetProvider.GCP
        assert service.discovery_source == DiscoverySource.GCP_API
        assert AssetType.CLOUD_INSTANCE in service.supported_asset_types
    
    def test_gcp_config_validation(self, gcp_config):
        """Test GCP configuration validation."""
        # Test missing project_id
        invalid_config = gcp_config.copy()
        del invalid_config["project_id"]
        
        with patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', True):
            with pytest.raises(ValueError) as exc_info:
                from app.services.discovery.gcp_discovery import GCPDiscoveryService
                GCPDiscoveryService(invalid_config)
            assert "project_id is required" in str(exc_info.value)
    
    def test_gcp_sdk_availability_check(self, gcp_config):
        """Test GCP SDK availability check."""
        with patch('app.services.discovery.gcp_discovery.GCP_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                from app.services.discovery.gcp_discovery import GCPDiscoveryService
                GCPDiscoveryService(gcp_config)
            assert "Google Cloud SDK not available" in str(exc_info.value)


class TestAPIDiscoveryValidation:
    """Validation tests for API discovery service."""
    
    @pytest.fixture
    def api_config(self):
        """Standard API discovery configuration for testing."""
        return {
            "tool": "akto",
            "target": "https://api.example.com",
            "timeout": 30
        }
    
    def test_api_discovery_import(self):
        """Test that API discovery service can be imported."""
        try:
            from app.services.discovery.api_discovery import APIDiscoveryEngine
            assert APIDiscoveryEngine is not None
        except ImportError as e:
            pytest.skip(f"API discovery service import failed: {e}")
    
    def test_api_service_initialization(self, api_config):
        """Test API service initialization."""
        from app.services.discovery.api_discovery import APIDiscoveryEngine
        
        service = APIDiscoveryEngine(api_config)
        assert service.tool == api_config["tool"]
        assert service.target == api_config["target"]
        assert service.provider == AssetProvider.ON_PREMISES
        assert service.discovery_source == DiscoverySource.AKTO
        assert AssetType.API_ENDPOINT in service.supported_asset_types
    
    def test_api_config_validation(self):
        """Test API configuration validation."""
        # Test missing target
        invalid_config = {"tool": "akto"}
        
        with pytest.raises(ValueError) as exc_info:
            from app.services.discovery.api_discovery import APIDiscoveryEngine
            APIDiscoveryEngine(invalid_config)
        assert "target is required" in str(exc_info.value)


class TestNetworkDiscoveryValidation:
    """Validation tests for Network discovery service."""
    
    @pytest.fixture
    def network_config(self):
        """Standard network discovery configuration for testing."""
        return {
            "target_networks": ["***********/24", "10.0.0.0/16"],
            "scan_type": "nmap",
            "ports": ["22", "80", "443"]
        }
    
    def test_network_discovery_import(self):
        """Test that network discovery service can be imported."""
        try:
            from app.services.discovery.network_discovery import NetworkDiscoveryEngine
            assert NetworkDiscoveryEngine is not None
        except ImportError as e:
            pytest.skip(f"Network discovery service import failed: {e}")
    
    def test_network_service_initialization(self, network_config):
        """Test network service initialization."""
        from app.services.discovery.network_discovery import NetworkDiscoveryEngine
        
        service = NetworkDiscoveryEngine(network_config)
        assert service.target_networks == network_config["target_networks"]
        assert service.scan_type == network_config["scan_type"]
        assert service.provider == AssetProvider.ON_PREMISES
        assert service.discovery_source == DiscoverySource.NMAP
        assert AssetType.SERVER in service.supported_asset_types
    
    def test_network_config_validation(self):
        """Test network configuration validation."""
        # Test missing target_networks
        invalid_config = {"scan_type": "nmap"}
        
        with pytest.raises(ValueError) as exc_info:
            from app.services.discovery.network_discovery import NetworkDiscoveryEngine
            NetworkDiscoveryEngine(invalid_config)
        assert "target networks" in str(exc_info.value)


class TestRiskAssessmentValidation:
    """Validation tests for risk assessment algorithms."""
    
    def test_base_risk_calculation(self):
        """Test base risk score calculation logic."""
        from app.services.discovery.base_discovery import BaseDiscoveryEngine
        
        # Create a mock implementation to test the base class
        class MockDiscoveryEngine(BaseDiscoveryEngine):
            @property
            def provider(self):
                return AssetProvider.AWS
            
            @property
            def discovery_source(self):
                return DiscoverySource.AWS_API
            
            @property
            def supported_asset_types(self):
                return {AssetType.DATABASE}
            
            async def validate_config(self):
                return True
            
            async def discover(self):
                return DiscoveryResult()
        
        engine = MockDiscoveryEngine({})
        
        # Test database risk calculation
        risk_score = engine._calculate_base_risk_score(
            AssetType.DATABASE,
            environment="production",
            has_public_ip=True,
            is_running=True
        )
        
        # Database (50) + Production (25) + Public IP (20) = 95
        assert risk_score == 95
        
        # Test with development environment
        dev_risk = engine._calculate_base_risk_score(
            AssetType.DATABASE,
            environment="development",
            has_public_ip=False,
            is_running=False
        )
        
        # Database (50) + Development (5) + No Public IP (0) + Not Running (-10) = 45
        assert dev_risk == 45
    
    def test_risk_level_conversion(self):
        """Test risk score to risk level conversion."""
        from app.services.discovery.base_discovery import BaseDiscoveryEngine
        
        class MockEngine(BaseDiscoveryEngine):
            @property
            def provider(self):
                return AssetProvider.AWS
            
            @property
            def discovery_source(self):
                return DiscoverySource.AWS_API
            
            @property
            def supported_asset_types(self):
                return set()
            
            async def validate_config(self):
                return True
            
            async def discover(self):
                return DiscoveryResult()
        
        engine = MockEngine({})
        
        # Test risk level mappings
        assert engine._get_risk_level_from_score(90) == RiskLevel.CRITICAL
        assert engine._get_risk_level_from_score(70) == RiskLevel.HIGH
        assert engine._get_risk_level_from_score(50) == RiskLevel.MEDIUM
        assert engine._get_risk_level_from_score(30) == RiskLevel.LOW
        
        # Test boundary conditions
        assert engine._get_risk_level_from_score(80) == RiskLevel.CRITICAL
        assert engine._get_risk_level_from_score(79) == RiskLevel.HIGH
        assert engine._get_risk_level_from_score(60) == RiskLevel.HIGH
        assert engine._get_risk_level_from_score(59) == RiskLevel.MEDIUM
        assert engine._get_risk_level_from_score(40) == RiskLevel.MEDIUM
        assert engine._get_risk_level_from_score(39) == RiskLevel.LOW


class TestDiscoveryIntegration:
    """Integration tests for discovery workflow."""
    
    @pytest.mark.asyncio
    async def test_discovery_workflow_simulation(self):
        """Test complete discovery workflow simulation."""
        # This test simulates a complete discovery workflow without external dependencies
        
        # Mock discovery result
        mock_result = DiscoveryResult(
            assets_discovered=[
                {
                    "name": "test-vm-01",
                    "asset_type": AssetType.CLOUD_INSTANCE,
                    "provider": AssetProvider.AZURE,
                    "provider_id": "vm-12345",
                    "risk_score": 75,
                    "risk_level": RiskLevel.HIGH
                }
            ],
            relationships_discovered=[
                {
                    "source_provider_id": "vm-12345",
                    "target_provider_id": "db-67890",
                    "relationship_type": "connects_to"
                }
            ],
            total_assets=1,
            total_relationships=1,
            total_errors=0,
            execution_time_seconds=45.2
        )
        
        # Validate result structure
        assert mock_result.total_assets == 1
        assert mock_result.total_relationships == 1
        assert mock_result.success_rate == 1.0
        assert not mock_result.has_errors
        assert len(mock_result.assets_discovered) == 1
        assert len(mock_result.relationships_discovered) == 1
        
        # Validate asset data
        asset = mock_result.assets_discovered[0]
        assert asset["name"] == "test-vm-01"
        assert asset["asset_type"] == AssetType.CLOUD_INSTANCE
        assert asset["provider"] == AssetProvider.AZURE
        assert asset["risk_score"] == 75
        assert asset["risk_level"] == RiskLevel.HIGH
        
        # Validate relationship data
        relationship = mock_result.relationships_discovered[0]
        assert relationship["source_provider_id"] == "vm-12345"
        assert relationship["target_provider_id"] == "db-67890"
        assert relationship["relationship_type"] == "connects_to"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
