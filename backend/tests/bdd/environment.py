"""Behave environment setup for BDD tests."""

import asyncio
import os
import subprocess
import time
from typing import Dict, Any

import httpx
from behave import fixture, use_fixture
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.config import settings
from app.db.base import Base
from app.db.models.user import User, UserRole
from app.services.user_service import UserService
from app.schemas.user import UserCreate


# Test configuration
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
HEADLESS = os.getenv("HEADLESS", "true").lower() == "true"
SLOW_MO = int(os.getenv("SLOW_MO", "0"))


@fixture
def backend_server(context):
    """Start backend server for BDD tests."""
    # Check if server is already running
    try:
        response = httpx.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("Backend server already running")
            return
    except:
        pass
    
    # Start the server
    print("Starting backend server for BDD tests...")
    process = subprocess.Popen([
        "uvicorn", "app.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ], cwd="backend")
    
    # Wait for server to start
    time.sleep(5)
    
    # Verify server is running
    try:
        response = httpx.get(f"{BASE_URL}/health", timeout=10)
        assert response.status_code == 200
        print("Backend server started successfully")
    except Exception as e:
        process.terminate()
        raise Exception(f"Failed to start backend server: {e}")
    
    context.backend_process = process


@fixture
def test_database(context):
    """Set up test database."""
    # Use a separate test database
    test_db_url = settings.DATABASE_URL.replace("/blast_radius", "/blast_radius_bdd_test")
    engine = create_engine(test_db_url)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    context.db_session = TestingSessionLocal()
    
    # Create system roles
    UserRole.create_system_roles(context.db_session)
    context.db_session.commit()
    
    yield
    
    # Clean up
    context.db_session.close()
    Base.metadata.drop_all(bind=engine)
    engine.dispose()


@fixture
def test_users(context):
    """Create test users for BDD scenarios."""
    user_service = UserService(context.db_session)
    
    # Create test users for different roles
    context.test_users = {}
    
    # Administrator
    admin_data = UserCreate(
        username="admin_user",
        email="<EMAIL>",
        full_name="Administrator User",
        password="AdminPassword123!",
        department="IT Security",
        job_title="Security Administrator",
        roles=["admin"]
    )
    context.test_users["admin"] = user_service.create_user(admin_data)
    
    # SOC Operator
    soc_data = UserCreate(
        username="soc_operator",
        email="<EMAIL>",
        full_name="SOC Operator",
        password="SocPassword123!",
        department="Security Operations",
        job_title="SOC Analyst",
        roles=["soc_operator"]
    )
    context.test_users["soc_operator"] = user_service.create_user(soc_data)
    
    # Security Architect
    architect_data = UserCreate(
        username="security_architect",
        email="<EMAIL>",
        full_name="Security Architect",
        password="ArchitectPassword123!",
        department="Security Architecture",
        job_title="Principal Security Architect",
        roles=["security_architect"]
    )
    context.test_users["security_architect"] = user_service.create_user(architect_data)
    
    # Red Team Member
    redteam_data = UserCreate(
        username="red_team_member",
        email="<EMAIL>",
        full_name="Red Team Member",
        password="RedteamPassword123!",
        department="Offensive Security",
        job_title="Penetration Tester",
        roles=["red_team_member"]
    )
    context.test_users["red_team_member"] = user_service.create_user(redteam_data)
    
    # Purple Team Member
    purple_data = UserCreate(
        username="purple_team_member",
        email="<EMAIL>",
        full_name="Purple Team Member",
        password="PurplePassword123!",
        department="Security Engineering",
        job_title="Purple Team Lead",
        roles=["purple_team_member"]
    )
    context.test_users["purple_team_member"] = user_service.create_user(purple_data)
    
    # Analyst
    analyst_data = UserCreate(
        username="security_analyst",
        email="<EMAIL>",
        full_name="Security Analyst",
        password="AnalystPassword123!",
        department="Threat Intelligence",
        job_title="Senior Security Analyst",
        roles=["analyst"]
    )
    context.test_users["analyst"] = user_service.create_user(analyst_data)
    
    # Viewer
    viewer_data = UserCreate(
        username="security_viewer",
        email="<EMAIL>",
        full_name="Security Viewer",
        password="ViewerPassword123!",
        department="Executive",
        job_title="CISO",
        roles=["viewer"]
    )
    context.test_users["viewer"] = user_service.create_user(viewer_data)
    
    # Business User (for business scenarios)
    business_data = UserCreate(
        username="business_user",
        email="<EMAIL>",
        full_name="Business User",
        password="BusinessPassword123!",
        department="Business Operations",
        job_title="Business Analyst",
        roles=["viewer"]
    )
    context.test_users["business_user"] = user_service.create_user(business_data)
    
    context.db_session.commit()


@fixture
async def browser_context(context):
    """Set up browser context for UI testing."""
    context.playwright = await async_playwright().start()
    context.browser = await context.playwright.chromium.launch(
        headless=HEADLESS,
        slow_mo=SLOW_MO,
        args=[
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--no-sandbox",
            "--disable-setuid-sandbox",
        ]
    )
    
    context.browser_context = await context.browser.new_context(
        viewport={"width": 1920, "height": 1080},
        ignore_https_errors=True,
    )
    
    context.page = await context.browser_context.new_page()
    
    # Set up console logging
    context.page.on("console", lambda msg: print(f"Console {msg.type}: {msg.text}"))
    context.page.on("pageerror", lambda error: print(f"Page error: {error}"))
    
    yield
    
    # Clean up
    await context.page.close()
    await context.browser_context.close()
    await context.browser.close()
    await context.playwright.stop()


def before_all(context):
    """Set up test environment before all scenarios."""
    # Create reports directory
    os.makedirs("tests/bdd/reports", exist_ok=True)
    os.makedirs("tests/bdd/reports/html", exist_ok=True)
    os.makedirs("tests/bdd/reports/junit", exist_ok=True)
    os.makedirs("tests/bdd/reports/screenshots", exist_ok=True)
    
    # Set up test configuration
    context.config.userdata.setdefault("base_url", BASE_URL)
    context.config.userdata.setdefault("frontend_url", FRONTEND_URL)
    context.config.userdata.setdefault("timeout", "30")
    
    # Store configuration in context
    context.base_url = context.config.userdata.get("base_url", BASE_URL)
    context.frontend_url = context.config.userdata.get("frontend_url", FRONTEND_URL)
    context.timeout = int(context.config.userdata.get("timeout", "30"))
    
    print(f"BDD Test Configuration:")
    print(f"  Base URL: {context.base_url}")
    print(f"  Frontend URL: {context.frontend_url}")
    print(f"  Timeout: {context.timeout}s")
    print(f"  Headless: {HEADLESS}")


def before_feature(context, feature):
    """Set up before each feature."""
    print(f"\n🎯 Starting Feature: {feature.name}")
    
    # Set up backend server
    use_fixture(backend_server, context)
    
    # Set up test database
    use_fixture(test_database, context)
    
    # Create test users
    use_fixture(test_users, context)


def before_scenario(context, scenario):
    """Set up before each scenario."""
    print(f"\n📋 Starting Scenario: {scenario.name}")
    
    # Set up browser context for UI scenarios
    if "ui" in scenario.tags or "browser" in scenario.tags:
        asyncio.run(use_fixture(browser_context, context))
    
    # Initialize scenario-specific data
    context.scenario_data = {}
    context.api_responses = []
    context.current_user = None
    context.access_token = None


def after_scenario(context, scenario):
    """Clean up after each scenario."""
    # Take screenshot on failure
    if scenario.status == "failed" and hasattr(context, 'page'):
        screenshot_path = f"tests/bdd/reports/screenshots/{scenario.name.replace(' ', '_')}_failure.png"
        try:
            asyncio.run(context.page.screenshot(path=screenshot_path))
            print(f"Screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"Failed to take screenshot: {e}")
    
    # Clear scenario data
    if hasattr(context, 'scenario_data'):
        context.scenario_data.clear()
    
    if hasattr(context, 'api_responses'):
        context.api_responses.clear()


def after_feature(context, feature):
    """Clean up after each feature."""
    print(f"✅ Completed Feature: {feature.name}")
    
    # Clean up database
    if hasattr(context, 'db_session'):
        try:
            context.db_session.query(User).delete()
            context.db_session.commit()
        except:
            pass


def after_all(context):
    """Clean up after all scenarios."""
    # Stop backend server if we started it
    if hasattr(context, 'backend_process'):
        try:
            context.backend_process.terminate()
            context.backend_process.wait()
            print("Backend server stopped")
        except:
            pass
    
    print("\n🎉 All BDD tests completed!")


# Helper functions for scenarios
class BDDHelpers:
    """Helper methods for BDD scenarios."""
    
    @staticmethod
    def get_user_credentials(context, user_type: str) -> Dict[str, str]:
        """Get credentials for a user type."""
        user_map = {
            "administrator": ("admin_user", "AdminPassword123!"),
            "admin": ("admin_user", "AdminPassword123!"),
            "soc operator": ("soc_operator", "SocPassword123!"),
            "security architect": ("security_architect", "ArchitectPassword123!"),
            "red team member": ("red_team_member", "RedteamPassword123!"),
            "purple team member": ("purple_team_member", "PurplePassword123!"),
            "analyst": ("security_analyst", "AnalystPassword123!"),
            "viewer": ("security_viewer", "ViewerPassword123!"),
            "business user": ("business_user", "BusinessPassword123!"),
        }
        
        username, password = user_map.get(user_type.lower(), ("unknown", "unknown"))
        return {"username": username, "password": password}
    
    @staticmethod
    async def login_via_api(context, username: str, password: str) -> Dict[str, Any]:
        """Login via API and return tokens."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{context.base_url}/api/v1/auth/login",
                json={
                    "username": username,
                    "password": password,
                    "remember_me": False
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                context.access_token = data["tokens"]["access_token"]
                context.current_user = data["user"]
                return data
            else:
                raise Exception(f"Login failed: {response.status_code} - {response.text}")
    
    @staticmethod
    def get_auth_headers(context) -> Dict[str, str]:
        """Get authorization headers for API requests."""
        if context.access_token:
            return {"Authorization": f"Bearer {context.access_token}"}
        return {}


# Make helpers available in context
def before_step(context, step):
    """Set up before each step."""
    context.helpers = BDDHelpers()
