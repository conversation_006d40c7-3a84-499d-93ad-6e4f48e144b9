[behave]
# Behave configuration for Blast-Radius Security Tool BDD tests

# Test discovery
paths = tests/bdd/features

# Step definitions
step_definitions = tests/bdd/steps

# Output format
format = pretty
outfiles = tests/bdd/reports/behave-report.txt

# HTML reporting
outdir = tests/bdd/reports/html

# JUnit XML output for CI integration
junit = true
junit_directory = tests/bdd/reports/junit

# Logging
logging_level = INFO
logging_format = %(levelname)-8s %(name)s: %(message)s

# Tags
default_tags = ~@skip

# Behavior-driven development settings
show_skipped = false
show_timings = true
summary = true

# Stop on first failure (useful for debugging)
stop = false

# Dry run (check feature files without executing)
dry_run = false

# Color output
color = true

# Include scenario outlines in output
expand = true

# User data (can be overridden from command line)
userdata_defines = 
    base_url=http://localhost:8000
    frontend_url=http://localhost:3000
    timeout=30
    headless=true
    slow_mo=0
