"""Step definitions for authentication BDD scenarios."""

import json
import async<PERSON>
from behave import given, when, then, step
import httpx
from hamcrest import assert_that, equal_to, is_not, none, contains_string, greater_than


@given('the Blast-Radius Security Tool is running')
def step_system_running(context):
    """Verify the system is running."""
    try:
        response = httpx.get(f"{context.base_url}/health", timeout=10)
        assert_that(response.status_code, equal_to(200))
        context.system_running = True
    except Exception as e:
        raise Exception(f"System is not running: {e}")


@given('the system has the following user roles configured')
def step_roles_configured(context):
    """Verify user roles are configured."""
    # This step verifies that the role system is properly set up
    # In a real implementation, this might check role definitions
    context.roles_configured = True
    
    # Store expected roles from the table
    context.expected_roles = {}
    for row in context.table:
        context.expected_roles[row['role']] = row['description']


@given('I am an administrator with valid credentials')
def step_admin_credentials(context):
    """Set up administrator credentials."""
    context.current_user_type = "administrator"
    context.credentials = context.helpers.get_user_credentials(context, "administrator")


@given('I am a SOC operator with valid credentials')
def step_soc_credentials(context):
    """Set up SOC operator credentials."""
    context.current_user_type = "soc operator"
    context.credentials = context.helpers.get_user_credentials(context, "soc operator")


@given('I am a security architect with valid credentials')
def step_architect_credentials(context):
    """Set up security architect credentials."""
    context.current_user_type = "security architect"
    context.credentials = context.helpers.get_user_credentials(context, "security architect")


@given('I am a red team member with valid credentials')
def step_redteam_credentials(context):
    """Set up red team member credentials."""
    context.current_user_type = "red team member"
    context.credentials = context.helpers.get_user_credentials(context, "red team member")


@given('I am a purple team member with valid credentials')
def step_purple_credentials(context):
    """Set up purple team member credentials."""
    context.current_user_type = "purple team member"
    context.credentials = context.helpers.get_user_credentials(context, "purple team member")


@given('I have invalid credentials')
def step_invalid_credentials(context):
    """Set up invalid credentials."""
    context.credentials = {"username": "invalid_user", "password": "wrong_password"}


@given('I provide empty credentials')
def step_empty_credentials(context):
    """Set up empty credentials."""
    context.credentials = {"username": "", "password": ""}


@given('I attempt a SQL injection attack')
def step_sql_injection_attempt(context):
    """Set up SQL injection attack credentials."""
    context.credentials = {
        "username": "admin'; DROP TABLE users; --",
        "password": "password"
    }


@given('I am logged in as an administrator')
def step_logged_in_admin(context):
    """Log in as administrator."""
    credentials = context.helpers.get_user_credentials(context, "administrator")
    login_data = asyncio.run(context.helpers.login_via_api(
        context, 
        credentials["username"], 
        credentials["password"]
    ))
    context.login_response = login_data


@given('I am logged in as an analyst')
def step_logged_in_analyst(context):
    """Log in as analyst."""
    credentials = context.helpers.get_user_credentials(context, "analyst")
    login_data = asyncio.run(context.helpers.login_via_api(
        context, 
        credentials["username"], 
        credentials["password"]
    ))
    context.login_response = login_data


@given('I am not logged in')
def step_not_logged_in(context):
    """Ensure user is not logged in."""
    context.access_token = None
    context.current_user = None


@given('my access token is about to expire')
def step_token_about_to_expire(context):
    """Simulate token about to expire."""
    # In a real implementation, this might manipulate token expiry
    # For testing, we'll assume the token is valid but about to expire
    context.token_expiring = True


@when('I attempt to log in with username "{username}" and password "{password}"')
def step_attempt_login(context, username, password):
    """Attempt to log in with given credentials."""
    async def login():
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{context.base_url}/api/v1/auth/login",
                json={
                    "username": username,
                    "password": password,
                    "remember_me": False
                }
            )
            context.login_response = response
            context.login_status_code = response.status_code
            
            if response.status_code == 200:
                data = response.json()
                context.login_data = data
                context.access_token = data.get("tokens", {}).get("access_token")
                context.refresh_token = data.get("tokens", {}).get("refresh_token")
                context.current_user = data.get("user")
            else:
                context.login_error = response.text
    
    asyncio.run(login())


@when('I attempt to log in with empty username and password')
def step_attempt_empty_login(context):
    """Attempt to log in with empty credentials."""
    step_attempt_login(context, "", "")


@when('I access my user profile')
def step_access_user_profile(context):
    """Access the current user's profile."""
    async def get_profile():
        headers = context.helpers.get_auth_headers(context)
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{context.base_url}/api/v1/auth/me",
                headers=headers
            )
            context.profile_response = response
            context.profile_status_code = response.status_code
            
            if response.status_code == 200:
                context.profile_data = response.json()
    
    asyncio.run(get_profile())


@when('I attempt to access my user profile')
def step_attempt_access_profile(context):
    """Attempt to access user profile (may fail)."""
    step_access_user_profile(context)


@when('I use my refresh token to get a new access token')
def step_refresh_token(context):
    """Use refresh token to get new access token."""
    async def refresh():
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{context.base_url}/api/v1/auth/refresh",
                json={"refresh_token": context.refresh_token}
            )
            context.refresh_response = response
            context.refresh_status_code = response.status_code
            
            if response.status_code == 200:
                data = response.json()
                context.new_access_token = data.get("access_token")
    
    asyncio.run(refresh())


@when('I log out of the system')
def step_logout(context):
    """Log out of the system."""
    async def logout():
        headers = context.helpers.get_auth_headers(context)
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{context.base_url}/api/v1/auth/logout",
                headers=headers
            )
            context.logout_response = response
            context.logout_status_code = response.status_code
    
    asyncio.run(logout())


@when('I change my password from "{old_password}" to "{new_password}"')
def step_change_password(context, old_password, new_password):
    """Change user password."""
    async def change_password():
        headers = context.helpers.get_auth_headers(context)
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{context.base_url}/api/v1/auth/change-password",
                headers=headers,
                json={
                    "current_password": old_password,
                    "new_password": new_password
                }
            )
            context.password_change_response = response
            context.password_change_status_code = response.status_code
            context.new_password = new_password
    
    asyncio.run(change_password())


@when('I attempt to change my password to "{weak_password}"')
def step_attempt_weak_password(context, weak_password):
    """Attempt to change to a weak password."""
    step_change_password(context, "AnalystPassword123!", weak_password)


@when('I enable multi-factor authentication')
def step_enable_mfa(context):
    """Enable multi-factor authentication."""
    async def enable_mfa():
        headers = context.helpers.get_auth_headers(context)
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{context.base_url}/api/v1/auth/mfa/enable",
                headers=headers
            )
            context.mfa_response = response
            context.mfa_status_code = response.status_code
            
            if response.status_code == 200:
                context.mfa_data = response.json()
    
    asyncio.run(enable_mfa())


@then('I should be successfully authenticated')
def step_successful_authentication(context):
    """Verify successful authentication."""
    assert_that(context.login_status_code, equal_to(200))
    assert_that(context.login_data, is_not(none()))


@then('I should receive an access token')
def step_receive_access_token(context):
    """Verify access token is received."""
    assert_that(context.access_token, is_not(none()))
    assert_that(len(context.access_token), greater_than(0))


@then('I should receive a refresh token')
def step_receive_refresh_token(context):
    """Verify refresh token is received."""
    assert_that(context.refresh_token, is_not(none()))
    assert_that(len(context.refresh_token), greater_than(0))


@then('my user profile should show "{role}" role')
def step_verify_user_role(context, role):
    """Verify user has the expected role."""
    user_data = context.login_data.get("user", {})
    user_roles = user_data.get("roles", [])
    
    # Extract role names from role objects
    role_names = []
    for user_role in user_roles:
        if isinstance(user_role, dict):
            role_names.append(user_role.get("name"))
        else:
            role_names.append(str(user_role))
    
    assert_that(role_names, contains_string(role))


@then('I should have access to all system features')
def step_verify_admin_access(context):
    """Verify administrator has access to all features."""
    # This is a placeholder - in a real implementation, 
    # this would test access to various admin endpoints
    user_data = context.login_data.get("user", {})
    permissions = user_data.get("permissions", [])
    assert_that(len(permissions), greater_than(0))


@then('I should have access to security monitoring features')
def step_verify_soc_access(context):
    """Verify SOC operator has monitoring access."""
    # Placeholder for SOC-specific feature access verification
    assert_that(context.current_user, is_not(none()))


@then('I should be able to view security events')
def step_verify_security_events_access(context):
    """Verify access to security events."""
    # Placeholder for security events access verification
    pass


@then('I should be able to create incidents')
def step_verify_incident_creation_access(context):
    """Verify ability to create incidents."""
    # Placeholder for incident creation verification
    pass


@then('I should have access to architecture design features')
def step_verify_architect_access(context):
    """Verify security architect access."""
    # Placeholder for architect-specific feature verification
    pass


@then('I should be able to conduct risk assessments')
def step_verify_risk_assessment_access(context):
    """Verify risk assessment access."""
    # Placeholder for risk assessment verification
    pass


@then('I should be able to manage security policies')
def step_verify_policy_management_access(context):
    """Verify policy management access."""
    # Placeholder for policy management verification
    pass


@then('I should have access to penetration testing tools')
def step_verify_pentest_access(context):
    """Verify penetration testing access."""
    # Placeholder for pentest tools verification
    pass


@then('I should be able to conduct security tests')
def step_verify_security_testing_access(context):
    """Verify security testing access."""
    # Placeholder for security testing verification
    pass


@then('I should be able to view attack paths')
def step_verify_attack_paths_access(context):
    """Verify attack paths access."""
    # Placeholder for attack paths verification
    pass


@then('I should have access to collaborative testing features')
def step_verify_purple_team_access(context):
    """Verify purple team access."""
    # Placeholder for purple team features verification
    pass


@then('I should be able to coordinate security testing')
def step_verify_coordination_access(context):
    """Verify coordination capabilities."""
    # Placeholder for coordination verification
    pass


@then('I should be able to validate defenses')
def step_verify_defense_validation_access(context):
    """Verify defense validation access."""
    # Placeholder for defense validation verification
    pass


@then('I should receive an authentication error')
def step_verify_auth_error(context):
    """Verify authentication error is received."""
    assert_that(context.login_status_code, equal_to(401))


@then('I should not receive any tokens')
def step_verify_no_tokens(context):
    """Verify no tokens are received."""
    assert_that(getattr(context, 'access_token', None), none())
    assert_that(getattr(context, 'refresh_token', None), none())


@then('I should see an error message "{message}"')
def step_verify_error_message(context, message):
    """Verify specific error message."""
    assert_that(context.login_error, contains_string(message))


@then('I should receive a validation error')
def step_verify_validation_error(context):
    """Verify validation error is received."""
    assert_that(context.login_status_code, equal_to(422))


@then('the system should remain secure')
def step_verify_system_security(context):
    """Verify system security is maintained."""
    # This would verify that the SQL injection attempt didn't succeed
    # For now, we just verify that authentication failed
    assert_that(context.login_status_code, equal_to(401))


@then('no database damage should occur')
def step_verify_no_database_damage(context):
    """Verify no database damage occurred."""
    # In a real implementation, this might check database integrity
    # For now, we assume if the system is still responding, it's intact
    response = httpx.get(f"{context.base_url}/health", timeout=5)
    assert_that(response.status_code, equal_to(200))


@then('I should see my user information')
def step_verify_user_info(context):
    """Verify user information is displayed."""
    assert_that(context.profile_status_code, equal_to(200))
    assert_that(context.profile_data, is_not(none()))
    assert_that(context.profile_data.get("username"), is_not(none()))


@then('I should see my assigned roles')
def step_verify_assigned_roles(context):
    """Verify assigned roles are displayed."""
    assert_that(context.profile_data.get("roles"), is_not(none()))


@then('I should see my permissions')
def step_verify_permissions(context):
    """Verify permissions are displayed."""
    # Placeholder for permissions verification
    pass


@then('I should receive an unauthorized error')
def step_verify_unauthorized_error(context):
    """Verify unauthorized error is received."""
    assert_that(context.profile_status_code, equal_to(401))


@then('I should be prompted to authenticate')
def step_verify_auth_prompt(context):
    """Verify authentication prompt."""
    # This would typically check for authentication redirect or prompt
    assert_that(context.profile_status_code, equal_to(401))


@then('I should receive a new access token')
def step_verify_new_access_token(context):
    """Verify new access token is received."""
    assert_that(context.refresh_status_code, equal_to(200))
    assert_that(context.new_access_token, is_not(none()))
    assert_that(context.new_access_token, is_not(equal_to(context.access_token)))


@then('I should be able to continue accessing protected resources')
def step_verify_continued_access(context):
    """Verify continued access with new token."""
    # Update the access token and test access
    context.access_token = context.new_access_token
    step_access_user_profile(context)
    assert_that(context.profile_status_code, equal_to(200))


@then('my session should be terminated')
def step_verify_session_terminated(context):
    """Verify session is terminated."""
    assert_that(context.logout_status_code, equal_to(200))


@then('my tokens should be invalidated')
def step_verify_tokens_invalidated(context):
    """Verify tokens are invalidated."""
    # Test that the old token no longer works
    step_access_user_profile(context)
    assert_that(context.profile_status_code, equal_to(401))


@then('I should not be able to access protected resources')
def step_verify_no_protected_access(context):
    """Verify no access to protected resources."""
    step_access_user_profile(context)
    assert_that(context.profile_status_code, equal_to(401))


@then('my password should be updated successfully')
def step_verify_password_updated(context):
    """Verify password was updated."""
    assert_that(context.password_change_status_code, equal_to(200))


@then('I should be able to log in with the new password')
def step_verify_new_password_login(context):
    """Verify login with new password works."""
    credentials = context.helpers.get_user_credentials(context, "analyst")
    step_attempt_login(context, credentials["username"], context.new_password)
    assert_that(context.login_status_code, equal_to(200))


@then('I should not be able to log in with the old password')
def step_verify_old_password_fails(context):
    """Verify old password no longer works."""
    credentials = context.helpers.get_user_credentials(context, "analyst")
    step_attempt_login(context, credentials["username"], "AnalystPassword123!")
    assert_that(context.login_status_code, equal_to(401))


@then('I should receive a password validation error')
def step_verify_password_validation_error(context):
    """Verify password validation error."""
    assert_that(context.password_change_status_code, equal_to(422))


@then('my password should not be changed')
def step_verify_password_unchanged(context):
    """Verify password was not changed."""
    # Test that old password still works
    credentials = context.helpers.get_user_credentials(context, "analyst")
    step_attempt_login(context, credentials["username"], "AnalystPassword123!")
    assert_that(context.login_status_code, equal_to(200))


@then('I should see password requirements')
def step_verify_password_requirements(context):
    """Verify password requirements are shown."""
    # This would check that password requirements are displayed
    # For now, we just verify the validation error occurred
    assert_that(context.password_change_status_code, equal_to(422))


@then('I should receive a QR code for my authenticator app')
def step_verify_mfa_qr_code(context):
    """Verify MFA QR code is provided."""
    assert_that(context.mfa_status_code, equal_to(200))
    assert_that(context.mfa_data.get("qr_code"), is_not(none()))


@then('I should be able to verify the setup with a TOTP code')
def step_verify_mfa_totp(context):
    """Verify TOTP verification capability."""
    assert_that(context.mfa_data.get("secret"), is_not(none()))


@then('MFA should be enabled for my account')
def step_verify_mfa_enabled(context):
    """Verify MFA is enabled."""
    assert_that(context.mfa_data.get("enabled"), equal_to(True))
