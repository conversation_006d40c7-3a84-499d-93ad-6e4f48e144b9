Feature: User Authentication and Authorization
  As a security professional
  I want to securely authenticate to the Blast-Radius Security Tool
  So that I can access security features appropriate to my role

  Background:
    Given the Blast-Radius Security Tool is running
    And the system has the following user roles configured:
      | role               | description                    |
      | admin             | Full system access             |
      | soc_operator      | Security monitoring            |
      | security_architect| Architecture design            |
      | red_team_member   | Attack simulation              |
      | purple_team_member| Collaborative testing          |
      | analyst           | Data analysis                  |
      | viewer            | Read-only access               |

  @authentication @critical
  Scenario: Administrator successfully logs in
    Given I am an administrator with valid credentials
    When I attempt to log in with username "admin_user" and password "AdminPassword123!"
    Then I should be successfully authenticated
    And I should receive an access token
    And I should receive a refresh token
    And my user profile should show "admin" role
    And I should have access to all system features

  @authentication @critical
  Scenario: SOC Operator logs in and accesses monitoring features
    Given I am a SOC operator with valid credentials
    When I attempt to log in with username "soc_operator" and password "SocPassword123!"
    Then I should be successfully authenticated
    And my user profile should show "soc_operator" role
    And I should have access to security monitoring features
    And I should be able to view security events
    And I should be able to create incidents

  @authentication @critical
  Scenario: Security Architect accesses architecture features
    Given I am a security architect with valid credentials
    When I attempt to log in with username "security_architect" and password "ArchitectPassword123!"
    Then I should be successfully authenticated
    And my user profile should show "security_architect" role
    And I should have access to architecture design features
    And I should be able to conduct risk assessments
    And I should be able to manage security policies

  @authentication @critical
  Scenario: Red Team Member accesses offensive security tools
    Given I am a red team member with valid credentials
    When I attempt to log in with username "red_team_member" and password "RedteamPassword123!"
    Then I should be successfully authenticated
    And my user profile should show "red_team_member" role
    And I should have access to penetration testing tools
    And I should be able to conduct security tests
    And I should be able to view attack paths

  @authentication @critical
  Scenario: Purple Team Member accesses collaborative features
    Given I am a purple team member with valid credentials
    When I attempt to log in with username "purple_team_member" and password "PurplePassword123!"
    Then I should be successfully authenticated
    And my user profile should show "purple_team_member" role
    And I should have access to collaborative testing features
    And I should be able to coordinate security testing
    And I should be able to validate defenses

  @authentication @security
  Scenario: Invalid credentials are rejected
    Given I have invalid credentials
    When I attempt to log in with username "invalid_user" and password "wrong_password"
    Then I should receive an authentication error
    And I should not receive any tokens
    And I should see an error message "Invalid credentials"

  @authentication @security
  Scenario: Empty credentials are rejected
    Given I provide empty credentials
    When I attempt to log in with empty username and password
    Then I should receive a validation error
    And I should not receive any tokens

  @authentication @security
  Scenario: SQL injection attempt is prevented
    Given I attempt a SQL injection attack
    When I attempt to log in with username "admin'; DROP TABLE users; --" and password "password"
    Then I should receive an authentication error
    And the system should remain secure
    And no database damage should occur

  @authentication @session
  Scenario: User can access protected resources with valid token
    Given I am logged in as an administrator
    When I access my user profile
    Then I should see my user information
    And I should see my assigned roles
    And I should see my permissions

  @authentication @session
  Scenario: User cannot access protected resources without token
    Given I am not logged in
    When I attempt to access my user profile
    Then I should receive an unauthorized error
    And I should be prompted to authenticate

  @authentication @session
  Scenario: User can refresh their access token
    Given I am logged in as an administrator
    And my access token is about to expire
    When I use my refresh token to get a new access token
    Then I should receive a new access token
    And I should be able to continue accessing protected resources

  @authentication @session
  Scenario: User can log out successfully
    Given I am logged in as an administrator
    When I log out of the system
    Then my session should be terminated
    And my tokens should be invalidated
    And I should not be able to access protected resources

  @authentication @password
  Scenario: User can change their password
    Given I am logged in as an analyst
    When I change my password from "AnalystPassword123!" to "NewAnalystPassword456!"
    Then my password should be updated successfully
    And I should be able to log in with the new password
    And I should not be able to log in with the old password

  @authentication @password @security
  Scenario: Weak passwords are rejected
    Given I am logged in as an analyst
    When I attempt to change my password to "weak"
    Then I should receive a password validation error
    And my password should not be changed
    And I should see password requirements

  @authentication @mfa
  Scenario: User can enable multi-factor authentication
    Given I am logged in as an administrator
    When I enable multi-factor authentication
    Then I should receive a QR code for my authenticator app
    And I should be able to verify the setup with a TOTP code
    And MFA should be enabled for my account
