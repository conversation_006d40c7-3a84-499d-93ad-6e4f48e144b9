Feature: User Management and Administration
  As an administrator
  I want to manage user accounts and permissions
  So that I can control access to the security platform

  Background:
    Given the Blast-Radius Security Tool is running
    And I am logged in as an administrator

  @user_management @critical
  Scenario: Administrator creates a new SOC operator
    Given I have administrator privileges
    When I create a new user with the following details:
      | field      | value                    |
      | username   | new_soc_operator        |
      | email      | <EMAIL> |
      | full_name  | New SOC Operator        |
      | department | Security Operations     |
      | job_title  | SOC Analyst Level 1     |
      | role       | soc_operator           |
    Then the user should be created successfully
    And the user should have "soc_operator" role assigned
    And the user should be able to log in
    And the user should have appropriate SOC permissions

  @user_management @critical
  Scenario: Administrator creates a security analyst
    Given I have administrator privileges
    When I create a new user with the following details:
      | field      | value                      |
      | username   | new_analyst               |
      | email      | <EMAIL>|
      | full_name  | New Security Analyst      |
      | department | Threat Intelligence       |
      | job_title  | Junior Security Analyst   |
      | role       | analyst                   |
    Then the user should be created successfully
    And the user should have "analyst" role assigned
    And the user should be able to access analytical features
    And the user should not have administrative privileges

  @user_management @authorization
  Scenario: Non-administrator cannot create users
    Given I am logged in as an analyst
    When I attempt to create a new user
    Then I should receive an authorization error
    And the user should not be created
    And I should see a message about insufficient permissions

  @user_management @validation
  Scenario: User creation with invalid email is rejected
    Given I have administrator privileges
    When I attempt to create a user with invalid email "not-an-email"
    Then I should receive a validation error
    And the user should not be created
    And I should see an email format error message

  @user_management @validation
  Scenario: User creation with weak password is rejected
    Given I have administrator privileges
    When I attempt to create a user with password "123"
    Then I should receive a password validation error
    And the user should not be created
    And I should see password strength requirements

  @user_management @critical
  Scenario: Administrator updates user information
    Given there is an existing user "security_analyst"
    When I update the user's information:
      | field      | value                    |
      | full_name  | Senior Security Analyst |
      | department | Advanced Threat Hunting |
      | job_title  | Senior Analyst          |
    Then the user information should be updated successfully
    And the changes should be reflected in the user profile

  @user_management @roles
  Scenario: Administrator assigns multiple roles to a user
    Given there is an existing user "security_analyst"
    When I assign additional roles to the user:
      | role               |
      | purple_team_member |
    Then the user should have both "analyst" and "purple_team_member" roles
    And the user should have combined permissions from both roles

  @user_management @roles
  Scenario: Administrator removes role from user
    Given there is a user with multiple roles
    When I remove the "analyst" role from the user
    Then the user should no longer have "analyst" role
    And the user should lose analyst-specific permissions
    And the user should retain other assigned roles

  @user_management @search
  Scenario: Administrator searches for users by name
    Given there are multiple users in the system
    When I search for users with name containing "analyst"
    Then I should see all users with "analyst" in their name or role
    And the search results should be properly formatted
    And I should be able to access each user's profile

  @user_management @search
  Scenario: Administrator filters users by department
    Given there are users from different departments
    When I filter users by department "Security Operations"
    Then I should see only users from that department
    And the results should include their role information

  @user_management @pagination
  Scenario: Administrator views paginated user list
    Given there are more than 20 users in the system
    When I view the user list with page size 10
    Then I should see 10 users per page
    And I should be able to navigate to the next page
    And I should see pagination information

  @user_management @security
  Scenario: Administrator locks a user account
    Given there is an active user "security_analyst"
    When I lock the user account due to "suspicious activity"
    Then the user account should be locked
    And the user should not be able to log in
    And the lock reason should be recorded

  @user_management @security
  Scenario: Administrator unlocks a user account
    Given there is a locked user account
    When I unlock the user account
    Then the user should be able to log in again
    And the unlock action should be logged

  @user_management @audit
  Scenario: User management actions are audited
    Given audit logging is enabled
    When I create a new user
    Then the user creation should be logged in the audit trail
    And the log should include my administrator ID
    And the log should include timestamp and action details

  @user_management @bulk
  Scenario: Administrator imports multiple users from CSV
    Given I have a CSV file with valid user data
    When I import users from the CSV file
    Then all valid users should be created
    And invalid entries should be reported
    And I should receive a summary of the import results

  @user_management @profile
  Scenario: User views their own profile
    Given I am logged in as an analyst
    When I view my own profile
    Then I should see my personal information
    And I should see my assigned roles
    And I should see my account status
    And I should not see other users' information

  @user_management @profile
  Scenario: User updates their own profile information
    Given I am logged in as an analyst
    When I update my profile information:
      | field      | value                |
      | full_name  | Updated Analyst Name |
      | phone      | ******-0123         |
    Then my profile should be updated successfully
    And I should see the updated information
    And I should not be able to change my roles

  @user_management @deactivation
  Scenario: Administrator deactivates a user account
    Given there is an active user "security_analyst"
    When I deactivate the user account
    Then the user account should be marked as inactive
    And the user should not be able to log in
    And the user's data should be preserved for audit purposes

  @user_management @reactivation
  Scenario: Administrator reactivates a user account
    Given there is an inactive user account
    When I reactivate the user account
    Then the user should be able to log in again
    And the user should retain their previous roles and permissions

  @user_management @deletion
  Scenario: Administrator performs soft delete of user account
    Given there is a user account to be deleted
    When I delete the user account with data retention
    Then the user should not be able to log in
    And the user's data should be preserved for compliance
    And the deletion should be logged in audit trail
