Feature: Role-Based Access Control
  As a security platform user
  I want access controls based on my role
  So that I can only access features appropriate to my responsibilities

  Background:
    Given the Blast-Radius Security Tool is running
    And the system has role-based access control configured

  @rbac @soc_operator @critical
  Scenario: SOC Operator accesses security monitoring dashboard
    Given I am logged in as a SOC operator
    When I access the security monitoring dashboard
    Then I should see real-time security events
    And I should be able to view incident reports
    And I should be able to create new incidents
    And I should be able to update incident status
    And I should not see administrative functions

  @rbac @soc_operator @permissions
  Scenario: SOC Operator manages security incidents
    Given I am logged in as a SOC operator
    And there are active security incidents
    When I view the incident management interface
    Then I should be able to view all incidents
    And I should be able to assign incidents to myself
    And I should be able to update incident severity
    And I should be able to add incident notes
    And I should not be able to delete incidents

  @rbac @security_architect @critical
  Scenario: Security Architect accesses risk assessment tools
    Given I am logged in as a security architect
    When I access the risk assessment module
    Then I should see risk analysis dashboards
    And I should be able to conduct security assessments
    And I should be able to create security policies
    And I should be able to review architecture diagrams
    And I should not see operational incident details

  @rbac @security_architect @permissions
  Scenario: Security Architect manages security policies
    Given I am logged in as a security architect
    When I access the policy management interface
    Then I should be able to create new security policies
    And I should be able to update existing policies
    And I should be able to review policy compliance
    And I should be able to approve policy changes
    And I should not be able to access user management

  @rbac @red_team_member @critical
  Scenario: Red Team Member accesses penetration testing tools
    Given I am logged in as a red team member
    When I access the penetration testing module
    Then I should see attack simulation tools
    And I should be able to plan security tests
    And I should be able to execute penetration tests
    And I should be able to document vulnerabilities
    And I should not see defensive monitoring tools

  @rbac @red_team_member @permissions
  Scenario: Red Team Member conducts security assessments
    Given I am logged in as a red team member
    When I initiate a penetration test
    Then I should be able to select target systems
    And I should be able to configure test parameters
    And I should be able to execute attack scenarios
    And I should be able to generate test reports
    And I should not be able to modify system configurations

  @rbac @purple_team_member @critical
  Scenario: Purple Team Member coordinates security testing
    Given I am logged in as a purple team member
    When I access the collaborative testing interface
    Then I should see both offensive and defensive perspectives
    And I should be able to coordinate red team exercises
    And I should be able to validate blue team responses
    And I should be able to facilitate team collaboration
    And I should have access to cross-team communication tools

  @rbac @purple_team_member @permissions
  Scenario: Purple Team Member validates security controls
    Given I am logged in as a purple team member
    And there is an ongoing security exercise
    When I review the exercise results
    Then I should be able to assess control effectiveness
    And I should be able to document lessons learned
    And I should be able to recommend improvements
    And I should be able to schedule follow-up tests

  @rbac @analyst @critical
  Scenario: Analyst accesses threat intelligence data
    Given I am logged in as an analyst
    When I access the threat intelligence module
    Then I should see threat data dashboards
    And I should be able to analyze security trends
    And I should be able to generate intelligence reports
    And I should be able to correlate threat indicators
    And I should not see system administration functions

  @rbac @analyst @permissions
  Scenario: Analyst performs data analysis
    Given I am logged in as an analyst
    And there is security data available
    When I perform threat analysis
    Then I should be able to query security databases
    And I should be able to create custom reports
    And I should be able to export analysis results
    And I should be able to share findings with team
    And I should not be able to modify raw data

  @rbac @viewer @critical
  Scenario: Viewer accesses read-only dashboards
    Given I am logged in as a viewer
    When I access the security overview dashboard
    Then I should see high-level security metrics
    And I should be able to view summary reports
    And I should be able to access public documentation
    And I should not see detailed operational data
    And I should not have any modification capabilities

  @rbac @viewer @restrictions
  Scenario: Viewer cannot perform administrative actions
    Given I am logged in as a viewer
    When I attempt to access user management
    Then I should receive an access denied error
    And I should not see user creation options
    And I should not see system configuration options
    And I should be redirected to appropriate viewer content

  @rbac @admin @critical
  Scenario: Administrator has full system access
    Given I am logged in as an administrator
    When I access any system module
    Then I should have full read and write access
    And I should be able to manage all users
    And I should be able to configure system settings
    And I should be able to access all security features
    And I should be able to view all audit logs

  @rbac @cross_role @permissions
  Scenario: User with multiple roles has combined permissions
    Given I am a user with both "analyst" and "purple_team_member" roles
    When I access the system
    Then I should have permissions from both roles
    And I should be able to perform analyst functions
    And I should be able to perform purple team functions
    And I should not have permissions beyond my assigned roles

  @rbac @permission_inheritance @security
  Scenario: Role permissions are properly inherited
    Given I am assigned to a role with specific permissions
    When I access features covered by those permissions
    Then I should have access to all inherited capabilities
    And I should not have access to capabilities outside my role
    And permission checks should be enforced consistently

  @rbac @dynamic_permissions @security
  Scenario: Permission changes take effect immediately
    Given I am logged in with specific role permissions
    When an administrator modifies my role permissions
    Then the permission changes should take effect immediately
    And I should lose access to removed capabilities
    And I should gain access to newly granted capabilities
    And my current session should reflect the changes

  @rbac @audit_trail @compliance
  Scenario: Access attempts are logged for audit
    Given audit logging is enabled
    When I access various system features
    Then all access attempts should be logged
    And the logs should include my user ID and role
    And the logs should include timestamps and accessed resources
    And failed access attempts should be clearly marked

  @rbac @business_user @workflow
  Scenario: Business user accesses executive dashboard
    Given I am logged in as a business user
    When I access the executive security dashboard
    Then I should see high-level security KPIs
    And I should see risk summary information
    And I should see compliance status overview
    And I should not see technical implementation details
    And I should be able to export executive reports

  @rbac @escalation @security
  Scenario: User cannot escalate their own privileges
    Given I am logged in as an analyst
    When I attempt to modify my own role assignments
    Then I should receive an authorization error
    And my role assignments should remain unchanged
    And the attempt should be logged for security review

  @rbac @session_security @timeout
  Scenario: Role-based session timeout policies
    Given I am logged in with a specific role
    When my session approaches the role-based timeout limit
    Then I should receive a session warning
    And I should be able to extend my session if policy allows
    And my session should timeout according to role policy
    And I should need to re-authenticate to continue
