"""Unit tests for asset API endpoints."""

import json
import pytest
import uuid
from typing import Dict

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.db.models.asset import AssetProvider, AssetType, DiscoverySource, RiskLevel
from app.db.models.user import User


class TestAssetAPI:
    """Test cases for Asset API endpoints."""

    def test_create_asset_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset creation."""
        asset_data = {
            "name": "test-server",
            "asset_type": "server",
            "provider": "aws",
            "provider_id": "i-1234567890abcdef0",
            "provider_region": "us-east-1",
            "ip_addresses": ["**********"],
            "discovery_source": "cloud_api",
            "environment": "production",
            "owner": "team-alpha",
            "tags": {"Environment": "prod", "Team": "alpha"}
        }
        
        response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "test-server"
        assert data["asset_type"] == "server"
        assert data["provider"] == "aws"
        assert data["provider_id"] == "i-1234567890abcdef0"
        assert data["ip_addresses"] == ["**********"]
        assert data["environment"] == "production"
        assert data["owner"] == "team-alpha"
        assert data["tags"] == {"Environment": "prod", "Team": "alpha"}
        assert "id" in data
        assert "discovered_at" in data
        assert "last_seen" in data

    def test_create_asset_invalid_data(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test asset creation with invalid data."""
        asset_data = {
            "name": "",  # Invalid: empty name
            "asset_type": "invalid_type",  # Invalid: not in enum
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 422

    def test_create_asset_duplicate_provider_id(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test creating asset with duplicate provider ID."""
        asset_data = {
            "name": "test-server-1",
            "asset_type": "server",
            "provider": "aws",
            "provider_id": "i-1234567890abcdef0",
            "discovery_source": "cloud_api"
        }
        
        # Create first asset
        response1 = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert response1.status_code == 201
        
        # Try to create duplicate
        asset_data["name"] = "test-server-2"
        response2 = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert response2.status_code == 409

    def test_get_asset_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset retrieval."""
        # Create asset first
        asset_data = {
            "name": "test-server",
            "asset_type": "server",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        create_response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        asset_id = create_response.json()["id"]
        
        # Get asset
        response = client.get(
            f"/api/v1/assets/{asset_id}",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == asset_id
        assert data["name"] == "test-server"

    def test_get_asset_not_found(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test asset retrieval with non-existent ID."""
        non_existent_id = str(uuid.uuid4())
        
        response = client.get(
            f"/api/v1/assets/{non_existent_id}",
            headers=authenticated_headers
        )
        
        assert response.status_code == 404

    def test_update_asset_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset update."""
        # Create asset first
        asset_data = {
            "name": "test-server",
            "asset_type": "server",
            "provider": "aws",
            "discovery_source": "cloud_api",
            "environment": "staging"
        }
        
        create_response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        asset_id = create_response.json()["id"]
        
        # Update asset
        update_data = {
            "environment": "production",
            "owner": "team-beta",
            "tags": {"Environment": "prod"}
        }
        
        response = client.put(
            f"/api/v1/assets/{asset_id}",
            json=update_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["environment"] == "production"
        assert data["owner"] == "team-beta"
        assert data["tags"] == {"Environment": "prod"}

    def test_delete_asset_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset deletion."""
        # Create asset first
        asset_data = {
            "name": "test-server",
            "asset_type": "server",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        create_response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        asset_id = create_response.json()["id"]
        
        # Delete asset
        response = client.delete(
            f"/api/v1/assets/{asset_id}",
            headers=authenticated_headers
        )
        
        assert response.status_code == 204
        
        # Verify asset is deleted
        get_response = client.get(
            f"/api/v1/assets/{asset_id}",
            headers=authenticated_headers
        )
        assert get_response.status_code == 404

    def test_search_assets_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset search."""
        # Create test assets
        assets_data = [
            {
                "name": "web-server-1",
                "asset_type": "server",
                "provider": "aws",
                "discovery_source": "cloud_api",
                "environment": "production"
            },
            {
                "name": "db-server-1",
                "asset_type": "server",
                "provider": "aws",
                "discovery_source": "cloud_api",
                "environment": "staging"
            },
            {
                "name": "web-server-2",
                "asset_type": "server",
                "provider": "azure",
                "discovery_source": "cloud_api",
                "environment": "production"
            }
        ]
        
        for asset_data in assets_data:
            response = client.post(
                "/api/v1/assets/",
                json=asset_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
        
        # Search for web servers
        search_data = {
            "query": "web-server",
            "page": 1,
            "size": 10
        }
        
        response = client.post(
            "/api/v1/assets/search",
            json=search_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 2
        assert len(data["assets"]) == 2
        assert all("web-server" in asset["name"] for asset in data["assets"])

    def test_search_assets_by_provider(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test asset search by provider."""
        # Create test assets
        assets_data = [
            {
                "name": "aws-server",
                "asset_type": "server",
                "provider": "aws",
                "discovery_source": "cloud_api"
            },
            {
                "name": "azure-server",
                "asset_type": "server",
                "provider": "azure",
                "discovery_source": "cloud_api"
            }
        ]
        
        for asset_data in assets_data:
            response = client.post(
                "/api/v1/assets/",
                json=asset_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
        
        # Search for AWS assets
        search_data = {
            "providers": ["aws"],
            "page": 1,
            "size": 10
        }
        
        response = client.post(
            "/api/v1/assets/search",
            json=search_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert len(data["assets"]) == 1
        assert data["assets"][0]["provider"] == "aws"

    def test_create_asset_relationship_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset relationship creation."""
        # Create source and target assets
        source_data = {
            "name": "web-server",
            "asset_type": "server",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        target_data = {
            "name": "database",
            "asset_type": "cloud_database",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        source_response = client.post(
            "/api/v1/assets/",
            json=source_data,
            headers=authenticated_headers
        )
        assert source_response.status_code == 201
        source_id = source_response.json()["id"]
        
        target_response = client.post(
            "/api/v1/assets/",
            json=target_data,
            headers=authenticated_headers
        )
        assert target_response.status_code == 201
        target_id = target_response.json()["id"]
        
        # Create relationship
        relationship_data = {
            "source_asset_id": source_id,
            "target_asset_id": target_id,
            "relationship_type": "depends_on",
            "protocol": "TCP",
            "port": 5432,
            "discovery_source": "cloud_api"
        }
        
        response = client.post(
            "/api/v1/assets/relationships",
            json=relationship_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["source_asset_id"] == source_id
        assert data["target_asset_id"] == target_id
        assert data["relationship_type"] == "depends_on"
        assert data["protocol"] == "TCP"
        assert data["port"] == 5432

    def test_get_asset_relationships(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test getting asset relationships."""
        # Create assets and relationship (reuse previous test logic)
        source_data = {
            "name": "web-server",
            "asset_type": "server",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        target_data = {
            "name": "database",
            "asset_type": "cloud_database",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        source_response = client.post("/api/v1/assets/", json=source_data, headers=authenticated_headers)
        source_id = source_response.json()["id"]
        
        target_response = client.post("/api/v1/assets/", json=target_data, headers=authenticated_headers)
        target_id = target_response.json()["id"]
        
        relationship_data = {
            "source_asset_id": source_id,
            "target_asset_id": target_id,
            "relationship_type": "depends_on",
            "discovery_source": "cloud_api"
        }
        
        client.post("/api/v1/assets/relationships", json=relationship_data, headers=authenticated_headers)
        
        # Get relationships
        response = client.get(
            f"/api/v1/assets/{source_id}/relationships",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["source_asset_id"] == source_id
        assert data[0]["target_asset_id"] == target_id

    def test_add_asset_tag_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful asset tag addition."""
        # Create asset first
        asset_data = {
            "name": "test-server",
            "asset_type": "server",
            "provider": "aws",
            "discovery_source": "cloud_api"
        }
        
        create_response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        asset_id = create_response.json()["id"]
        
        # Add tag
        tag_data = {
            "key": "Environment",
            "value": "production",
            "source": "manual"
        }
        
        response = client.post(
            f"/api/v1/assets/{asset_id}/tags",
            json=tag_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["asset_id"] == asset_id
        assert data["key"] == "Environment"
        assert data["value"] == "production"
        assert data["source"] == "manual"

    def test_get_asset_statistics(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test asset statistics endpoint."""
        # Create test assets
        assets_data = [
            {
                "name": "aws-server",
                "asset_type": "server",
                "provider": "aws",
                "discovery_source": "cloud_api",
                "environment": "production",
                "risk_level": "high"
            },
            {
                "name": "azure-database",
                "asset_type": "cloud_database",
                "provider": "azure",
                "discovery_source": "cloud_api",
                "environment": "staging",
                "risk_level": "medium"
            }
        ]
        
        for asset_data in assets_data:
            response = client.post(
                "/api/v1/assets/",
                json=asset_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
        
        # Get statistics
        response = client.get(
            "/api/v1/assets/statistics",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_assets"] == 2
        assert "assets_by_type" in data
        assert "assets_by_provider" in data
        assert "assets_by_environment" in data
        assert "assets_by_risk_level" in data

    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to asset endpoints."""
        response = client.get("/api/v1/assets/statistics")
        assert response.status_code == 401
