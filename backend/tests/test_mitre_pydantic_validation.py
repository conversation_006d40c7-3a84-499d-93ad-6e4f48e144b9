"""Focused Pydantic validation tests for MITRE ATT&CK schemas."""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

def test_mitre_schema_imports():
    """Test that all MITRE schemas can be imported successfully."""
    print("🔍 Testing MITRE Schema Imports...")
    
    try:
        from app.schemas.mitre import (
            MitreTechniqueBase,
            MitreTechniqueCreate,
            MitreTechniqueUpdate,
            MitreTechniqueResponse,
            MitreTacticBase,
            MitreTacticCreate,
            MitreTacticUpdate,
            MitreTacticResponse,
            MitreGroupBase,
            MitreGroupCreate,
            MitreGroupUpdate,
            MitreGroupResponse,
            MitreSoftwareBase,
            MitreSoftwareCreate,
            MitreSoftwareUpdate,
            MitreSoftwareResponse,
            MitreMitigationBase,
            MitreMitigationCreate,
            MitreMitigationUpdate,
            MitreMitigationResponse,
            MitreDataSyncBase,
            MitreDataSyncCreate,
            MitreDataSyncUpdate,
            MitreDataSyncResponse,
            MitreSearchRequest,
            MitreSearchResponse,
        )
        print("  ✅ All MITRE schemas imported successfully")
        return True
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False


def test_technique_schema_validation():
    """Test MITRE Technique schema validation."""
    print("🧪 Testing Technique Schema Validation...")
    
    try:
        from app.schemas.mitre import MitreTechniqueBase, MitreTechniqueCreate
        from app.db.models.mitre import MitreDomain, MitreEntityStatus
        
        # Test valid technique
        technique = MitreTechniqueBase(
            technique_id="T1234",
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE,
            status=MitreEntityStatus.ACTIVE,
            platforms=["Windows", "Linux"],
            data_sources=["Process monitoring"]
        )
        
        assert technique.technique_id == "T1234"
        assert technique.name == "Test Technique"
        assert technique.platforms == ["Windows", "Linux"]
        print("  ✅ Valid technique creation successful")
        
        # Test sub-technique
        subtechnique = MitreTechniqueBase(
            technique_id="T1234.001",
            name="Sub-technique",
            domain=MitreDomain.ENTERPRISE,
            is_subtechnique=True,
            parent_technique_id="T1234"
        )
        
        assert subtechnique.is_subtechnique is True
        assert subtechnique.parent_technique_id == "T1234"
        print("  ✅ Sub-technique validation successful")
        
        # Test technique ID case normalization
        technique_lower = MitreTechniqueBase(
            technique_id="t5678",
            name="Case Test",
            domain=MitreDomain.ENTERPRISE
        )
        assert technique_lower.technique_id == "T5678"
        print("  ✅ Technique ID case normalization successful")
        
        # Test invalid technique ID
        try:
            MitreTechniqueBase(
                technique_id="INVALID",
                name="Test",
                domain=MitreDomain.ENTERPRISE
            )
            print("  ❌ Should have failed with invalid technique ID")
            return False
        except Exception:
            print("  ✅ Invalid technique ID properly rejected")
        
        # Test invalid platform
        try:
            MitreTechniqueBase(
                technique_id="T1234",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                platforms=["InvalidPlatform"]
            )
            print("  ❌ Should have failed with invalid platform")
            return False
        except Exception:
            print("  ✅ Invalid platform properly rejected")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Technique schema test failed: {e}")
        return False


def test_tactic_schema_validation():
    """Test MITRE Tactic schema validation."""
    print("🧪 Testing Tactic Schema Validation...")
    
    try:
        from app.schemas.mitre import MitreTacticBase
        from app.db.models.mitre import MitreDomain
        
        # Test valid tactic
        tactic = MitreTacticBase(
            tactic_id="TA0001",
            name="Initial Access",
            domain=MitreDomain.ENTERPRISE,
            short_name="initial-access"
        )
        
        assert tactic.tactic_id == "TA0001"
        assert tactic.short_name == "initial-access"
        print("  ✅ Valid tactic creation successful")
        
        # Test tactic ID case normalization
        tactic_lower = MitreTacticBase(
            tactic_id="ta0002",
            name="Execution",
            domain=MitreDomain.ENTERPRISE
        )
        assert tactic_lower.tactic_id == "TA0002"
        print("  ✅ Tactic ID case normalization successful")
        
        # Test invalid tactic ID
        try:
            MitreTacticBase(
                tactic_id="INVALID",
                name="Test",
                domain=MitreDomain.ENTERPRISE
            )
            print("  ❌ Should have failed with invalid tactic ID")
            return False
        except Exception:
            print("  ✅ Invalid tactic ID properly rejected")
        
        # Test invalid short name
        try:
            MitreTacticBase(
                tactic_id="TA0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                short_name="Invalid_Name"  # Contains underscore
            )
            print("  ❌ Should have failed with invalid short name")
            return False
        except Exception:
            print("  ✅ Invalid short name properly rejected")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Tactic schema test failed: {e}")
        return False


def test_group_schema_validation():
    """Test MITRE Group schema validation."""
    print("🧪 Testing Group Schema Validation...")
    
    try:
        from app.schemas.mitre import MitreGroupBase
        from app.db.models.mitre import MitreDomain
        from datetime import datetime, timedelta
        
        # Test valid group
        group = MitreGroupBase(
            group_id="G0001",
            name="APT1",
            domain=MitreDomain.ENTERPRISE,
            sophistication="High",
            motivation=["Espionage"],
            country="China"
        )
        
        assert group.group_id == "G0001"
        assert group.sophistication == "High"
        assert group.motivation == ["Espionage"]
        print("  ✅ Valid group creation successful")
        
        # Test timeline validation
        first_seen = datetime.utcnow() - timedelta(days=30)
        last_seen = datetime.utcnow()
        
        group_with_timeline = MitreGroupBase(
            group_id="G0002",
            name="APT2",
            domain=MitreDomain.ENTERPRISE,
            first_seen=first_seen,
            last_seen=last_seen
        )
        
        assert group_with_timeline.first_seen == first_seen
        assert group_with_timeline.last_seen == last_seen
        print("  ✅ Timeline validation successful")
        
        # Test invalid sophistication
        try:
            MitreGroupBase(
                group_id="G0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                sophistication="Invalid"
            )
            print("  ❌ Should have failed with invalid sophistication")
            return False
        except Exception:
            print("  ✅ Invalid sophistication properly rejected")
        
        # Test invalid timeline
        try:
            MitreGroupBase(
                group_id="G0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                first_seen=datetime.utcnow(),
                last_seen=datetime.utcnow() - timedelta(days=30)  # Last seen before first seen
            )
            print("  ❌ Should have failed with invalid timeline")
            return False
        except Exception:
            print("  ✅ Invalid timeline properly rejected")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Group schema test failed: {e}")
        return False


def test_search_schema_validation():
    """Test MITRE Search schema validation."""
    print("🧪 Testing Search Schema Validation...")
    
    try:
        from app.schemas.mitre import MitreSearchRequest, MitreSearchResponse
        from app.db.models.mitre import MitreDomain, MitreEntityStatus
        
        # Test valid search request
        search_request = MitreSearchRequest(
            query="lateral movement",
            domains=[MitreDomain.ENTERPRISE],
            entity_types=["technique"],
            status=[MitreEntityStatus.ACTIVE],
            limit=50,
            offset=0
        )
        
        assert search_request.query == "lateral movement"
        assert search_request.limit == 50
        assert search_request.offset == 0
        print("  ✅ Valid search request creation successful")
        
        # Test search response
        search_response = MitreSearchResponse(
            results=[
                {"id": "T1234", "name": "Test Technique", "type": "technique"}
            ],
            total=1,
            query="lateral movement",
            execution_time_ms=25.5
        )
        
        assert len(search_response.results) == 1
        assert search_response.total == 1
        assert search_response.execution_time_ms == 25.5
        print("  ✅ Valid search response creation successful")
        
        # Test invalid limit
        try:
            MitreSearchRequest(
                query="test",
                limit=1001  # Exceeds maximum
            )
            print("  ❌ Should have failed with invalid limit")
            return False
        except Exception:
            print("  ✅ Invalid limit properly rejected")
        
        # Test invalid offset
        try:
            MitreSearchRequest(
                query="test",
                offset=-1  # Negative offset
            )
            print("  ❌ Should have failed with invalid offset")
            return False
        except Exception:
            print("  ✅ Invalid offset properly rejected")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Search schema test failed: {e}")
        return False


def test_schema_serialization():
    """Test schema serialization and deserialization."""
    print("🧪 Testing Schema Serialization...")
    
    try:
        from app.schemas.mitre import MitreTechniqueCreate
        from app.db.models.mitre import MitreDomain
        
        # Create technique
        technique = MitreTechniqueCreate(
            technique_id="T9999",
            name="Serialization Test",
            domain=MitreDomain.ENTERPRISE,
            platforms=["Windows", "Linux"]
        )
        
        # Test JSON serialization
        json_str = technique.model_dump_json()
        assert "T9999" in json_str
        assert "Serialization Test" in json_str
        print("  ✅ JSON serialization successful")
        
        # Test dict serialization
        data_dict = technique.model_dump()
        assert data_dict["technique_id"] == "T9999"
        assert data_dict["platforms"] == ["Windows", "Linux"]
        print("  ✅ Dict serialization successful")
        
        # Test deserialization
        new_technique = MitreTechniqueCreate(**data_dict)
        assert new_technique.technique_id == technique.technique_id
        assert new_technique.platforms == technique.platforms
        print("  ✅ Deserialization successful")
        
        # Test exclude_none serialization
        minimal_technique = MitreTechniqueCreate(
            technique_id="T8888",
            name="Minimal Test",
            domain=MitreDomain.ENTERPRISE
        )
        
        serialized_exclude_none = minimal_technique.model_dump(exclude_none=True)
        assert "platforms" not in serialized_exclude_none
        assert "data_sources" not in serialized_exclude_none
        print("  ✅ Exclude None serialization successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Serialization test failed: {e}")
        return False


def run_all_tests():
    """Run all Pydantic validation tests."""
    print("🚀 Starting MITRE Pydantic Schema Validation Tests")
    print("=" * 60)
    
    tests = [
        test_mitre_schema_imports,
        test_technique_schema_validation,
        test_tactic_schema_validation,
        test_group_schema_validation,
        test_search_schema_validation,
        test_schema_serialization,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✅ {test.__name__} PASSED\n")
            else:
                failed += 1
                print(f"❌ {test.__name__} FAILED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED: {e}\n")
    
    # Summary
    total = passed + failed
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print("=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed} ✅")
    print(f"Failed: {failed} ❌")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! MITRE Pydantic schemas are working correctly.")
    else:
        print(f"\n🔧 {failed} test(s) failed. Please review and fix the issues.")
    
    print("=" * 60)
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    exit_code = 0 if success else 1
    exit(exit_code)
