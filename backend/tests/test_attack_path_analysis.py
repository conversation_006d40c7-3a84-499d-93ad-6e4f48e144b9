"""Tests for attack path analysis engine."""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime

from app.services.graph_engine import <PERSON>raph<PERSON>ngine, AttackPath, BlastRadiusResult, AttackTechnique, PathType
from app.services.attack_path_analyzer import <PERSON>PathAnalyzer, AttackScenario


class TestGraphEngine:
    """Test graph processing engine."""
    
    @pytest.fixture
    def graph_engine(self):
        """Create graph engine for testing."""
        return GraphEngine(max_workers=2)
    
    @pytest.fixture
    def sample_assets(self):
        """Sample asset data for testing."""
        return {
            "web_server": {
                "asset_type": "server",
                "provider": "aws",
                "status": "active",
                "environment": "production",
                "business_criticality": "high",
                "risk_score": 75,
                "public_facing": True,
                "contains_pii": False
            },
            "database": {
                "asset_type": "database",
                "provider": "aws",
                "status": "active",
                "environment": "production",
                "business_criticality": "critical",
                "risk_score": 90,
                "public_facing": False,
                "contains_pii": True,
                "data_classification": "restricted"
            },
            "app_server": {
                "asset_type": "server",
                "provider": "aws",
                "status": "active",
                "environment": "production",
                "business_criticality": "medium",
                "risk_score": 60,
                "public_facing": False,
                "contains_pii": False
            },
            "workstation": {
                "asset_type": "workstation",
                "provider": "on_premises",
                "status": "active",
                "environment": "corporate",
                "business_criticality": "low",
                "risk_score": 40,
                "public_facing": False,
                "contains_pii": False
            }
        }
    
    @pytest.fixture
    def sample_relationships(self):
        """Sample relationship data for testing."""
        return [
            ("web_server", "app_server", {
                "relationship_type": "communicates_with",
                "protocol": "https",
                "port": 443,
                "encrypted": True,
                "authenticated": True,
                "monitored": True
            }),
            ("app_server", "database", {
                "relationship_type": "accesses",
                "protocol": "tcp",
                "port": 5432,
                "encrypted": True,
                "authenticated": True,
                "monitored": False
            }),
            ("workstation", "web_server", {
                "relationship_type": "communicates_with",
                "protocol": "https",
                "port": 443,
                "encrypted": True,
                "authenticated": False,
                "monitored": False
            })
        ]
    
    def test_add_asset(self, graph_engine, sample_assets):
        """Test adding assets to graph."""
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        assert graph_engine.graph.number_of_nodes() == len(sample_assets)
        assert "web_server" in graph_engine.asset_metadata
        assert graph_engine.asset_metadata["database"]["business_criticality"] == "critical"
    
    def test_add_relationship(self, graph_engine, sample_assets, sample_relationships):
        """Test adding relationships to graph."""
        # Add assets first
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        # Add relationships
        for source, target, rel_data in sample_relationships:
            graph_engine.add_relationship(source, target, rel_data)
        
        assert graph_engine.graph.number_of_edges() == len(sample_relationships)
        assert graph_engine.graph.has_edge("web_server", "app_server")
        
        # Check edge weight calculation
        edge_data = graph_engine.graph.get_edge_data("web_server", "app_server")
        assert "weight" in edge_data
        assert edge_data["weight"] > 0
    
    def test_calculate_edge_weight(self, graph_engine):
        """Test edge weight calculation."""
        # High security relationship should have higher weight
        secure_rel = {
            "relationship_type": "communicates_with",
            "encrypted": True,
            "authenticated": True,
            "monitored": True,
            "public_facing": False
        }
        secure_weight = graph_engine._calculate_edge_weight(secure_rel)
        
        # Low security relationship should have lower weight
        insecure_rel = {
            "relationship_type": "communicates_with",
            "encrypted": False,
            "authenticated": False,
            "monitored": False,
            "public_facing": True
        }
        insecure_weight = graph_engine._calculate_edge_weight(insecure_rel)
        
        assert secure_weight > insecure_weight
        assert 0.1 <= secure_weight <= 2.0
        assert 0.1 <= insecure_weight <= 2.0
    
    @pytest.mark.asyncio
    async def test_find_attack_paths(self, graph_engine, sample_assets, sample_relationships):
        """Test finding attack paths."""
        # Setup graph
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        for source, target, rel_data in sample_relationships:
            graph_engine.add_relationship(source, target, rel_data)
        
        # Find paths from workstation to database
        paths = await graph_engine.find_attack_paths("workstation", "database", max_path_length=5, max_paths=10)
        
        assert len(paths) > 0
        assert all(isinstance(path, AttackPath) for path in paths)
        assert paths[0].source_asset_id == "workstation"
        assert paths[0].target_asset_id == "database"
        assert len(paths[0].path_nodes) >= 2
    
    @pytest.mark.asyncio
    async def test_blast_radius_calculation(self, graph_engine, sample_assets, sample_relationships):
        """Test blast radius calculation."""
        # Setup graph
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        for source, target, rel_data in sample_relationships:
            graph_engine.add_relationship(source, target, rel_data)
        
        # Calculate blast radius from web server
        blast_result = await graph_engine.calculate_blast_radius("web_server", max_degrees=3)
        
        assert isinstance(blast_result, BlastRadiusResult)
        assert blast_result.source_asset_id == "web_server"
        assert len(blast_result.affected_assets) > 0
        assert "web_server" in blast_result.affected_assets
        assert blast_result.total_impact_score > 0
    
    def test_identify_high_value_targets(self, graph_engine, sample_assets):
        """Test identification of high-value targets."""
        # Add assets
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        # Mock the analyzer method
        analyzer = Mock()
        analyzer.graph_engine = graph_engine
        
        # Test the logic manually
        high_value_targets = []
        for asset_id, asset_data in graph_engine.asset_metadata.items():
            if (asset_data.get("business_criticality") == "critical" or
                asset_data.get("contains_pii", False) or
                asset_data.get("risk_score", 0) >= 80 or
                "database" in asset_data.get("asset_type", "").lower()):
                high_value_targets.append(asset_id)
        
        assert "database" in high_value_targets  # Critical and contains PII
        assert len(high_value_targets) > 0
    
    def test_graph_statistics(self, graph_engine, sample_assets, sample_relationships):
        """Test graph statistics calculation."""
        # Setup graph
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        for source, target, rel_data in sample_relationships:
            graph_engine.add_relationship(source, target, rel_data)
        
        stats = graph_engine.get_graph_statistics()
        
        assert "nodes" in stats
        assert "edges" in stats
        assert "density" in stats
        assert "is_connected" in stats
        assert stats["nodes"] == len(sample_assets)
        assert stats["edges"] == len(sample_relationships)
    
    def test_cache_functionality(self, graph_engine, sample_assets):
        """Test caching functionality."""
        # Add assets
        for asset_id, asset_data in sample_assets.items():
            graph_engine.add_asset(asset_id, asset_data)
        
        # Test cache invalidation
        initial_cache_size = len(graph_engine.path_cache)
        graph_engine._invalidate_cache("web_server")
        
        # Clear cache
        graph_engine.clear_cache()
        assert len(graph_engine.path_cache) == 0
        assert len(graph_engine.blast_radius_cache) == 0


class TestAttackPathAnalyzer:
    """Test attack path analyzer."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        db = Mock()
        
        # Mock assets
        mock_assets = []
        for i in range(3):
            asset = Mock()
            asset.id = f"asset_{i}"
            asset.asset_type = Mock()
            asset.asset_type.value = "server"
            asset.provider = Mock()
            asset.provider.value = "aws"
            asset.status = Mock()
            asset.status.value = "active"
            asset.environment = "production"
            asset.risk_score = 70
            asset.ip_addresses = ["*********"]
            asset.configuration = {}
            asset.properties = {}
            mock_assets.append(asset)
        
        db.query.return_value.all.return_value = mock_assets
        return db
    
    @pytest.fixture
    def analyzer(self, mock_db):
        """Create attack path analyzer."""
        return AttackPathAnalyzer(mock_db)
    
    def test_mitre_mappings_loaded(self, analyzer):
        """Test MITRE ATT&CK mappings are loaded."""
        assert len(analyzer.mitre_mappings) > 0
        assert AttackTechnique.INITIAL_ACCESS in analyzer.mitre_mappings
        assert AttackTechnique.LATERAL_MOVEMENT in analyzer.mitre_mappings
        
        # Check mapping structure
        initial_access = analyzer.mitre_mappings[AttackTechnique.INITIAL_ACCESS]
        assert initial_access.technique_id == "TA0001"
        assert initial_access.technique_name == "Initial Access"
        assert len(initial_access.mitigations) > 0
        assert len(initial_access.detection_methods) > 0
    
    @pytest.mark.asyncio
    async def test_initialize_from_database(self, analyzer, mock_db):
        """Test initialization from database."""
        # Mock relationships
        mock_relationships = []
        for i in range(2):
            rel = Mock()
            rel.source_asset_id = f"asset_{i}"
            rel.target_asset_id = f"asset_{i+1}"
            rel.relationship_type = Mock()
            rel.relationship_type.value = "communicates_with"
            rel.protocol = "https"
            rel.port = 443
            rel.direction = Mock()
            rel.direction.value = "outbound"
            rel.description = "Test relationship"
            rel.confidence = 0.9
            mock_relationships.append(rel)
        
        # Setup mock to return relationships
        mock_db.query.return_value.all.side_effect = [
            mock_db.query.return_value.all.return_value,  # assets
            mock_relationships  # relationships
        ]
        
        await analyzer.initialize_from_database()
        
        # Verify graph was populated
        assert analyzer.graph_engine.graph.number_of_nodes() > 0
        assert analyzer.graph_engine.graph.number_of_edges() > 0
    
    @pytest.mark.asyncio
    async def test_create_attack_scenario(self, analyzer):
        """Test attack scenario creation."""
        # Setup mock graph
        analyzer.graph_engine.add_asset("entry1", {"asset_type": "server", "risk_score": 60})
        analyzer.graph_engine.add_asset("target1", {"asset_type": "database", "risk_score": 90})
        analyzer.graph_engine.add_relationship("entry1", "target1", {"relationship_type": "accesses"})
        
        # Mock find_attack_paths to return a simple path
        mock_path = AttackPath(
            path_id="test_path",
            source_asset_id="entry1",
            target_asset_id="target1",
            path_nodes=["entry1", "target1"],
            path_edges=[("entry1", "target1")],
            path_type=PathType.DIRECT,
            attack_techniques=[AttackTechnique.INITIAL_ACCESS, AttackTechnique.IMPACT],
            risk_score=75.0,
            likelihood=0.7,
            impact_score=85.0,
            blast_radius=5,
            estimated_time=60,
            required_privileges=["user"],
            detection_difficulty=0.6,
            mitigation_cost=5000.0,
            path_length=2
        )
        
        with patch.object(analyzer.graph_engine, 'find_attack_paths', return_value=[mock_path]):
            scenario = await analyzer.create_attack_scenario(
                scenario_name="Test Scenario",
                threat_actor="APT29",
                entry_points=["entry1"],
                objectives=["target1"]
            )
        
        assert isinstance(scenario, AttackScenario)
        assert scenario.name == "Test Scenario"
        assert scenario.threat_actor == "APT29"
        assert len(scenario.attack_paths) == 1
        assert scenario.total_risk_score > 0
        assert scenario.criticality_level in ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
    
    def test_mitre_attack_mapping(self, analyzer):
        """Test MITRE ATT&CK mapping generation."""
        # Create test attack path
        attack_path = AttackPath(
            path_id="test_path",
            source_asset_id="source",
            target_asset_id="target",
            path_nodes=["source", "target"],
            path_edges=[("source", "target")],
            path_type=PathType.LATERAL_MOVEMENT,
            attack_techniques=[AttackTechnique.INITIAL_ACCESS, AttackTechnique.LATERAL_MOVEMENT],
            risk_score=75.0,
            likelihood=0.7,
            impact_score=85.0,
            blast_radius=5,
            estimated_time=60,
            required_privileges=["user"],
            detection_difficulty=0.6,
            mitigation_cost=5000.0,
            path_length=2
        )
        
        mapping = analyzer.get_mitre_attack_mapping(attack_path)
        
        assert "tactics" in mapping
        assert "techniques" in mapping
        assert "mitigations" in mapping
        assert "detection_methods" in mapping
        assert len(mapping["tactics"]) > 0
        assert len(mapping["techniques"]) > 0
    
    def test_export_analysis_results(self, analyzer):
        """Test exporting analysis results."""
        # Add a test scenario to cache
        test_scenario = AttackScenario(
            scenario_id="test_scenario",
            name="Test Export",
            description="Test scenario for export",
            threat_actor="Test Actor",
            attack_paths=[],
            total_risk_score=75.0,
            likelihood=0.7,
            impact_score=80.0,
            estimated_duration=4,
            required_resources=["network_access"],
            detection_probability=0.6,
            mitigation_strategies=["Network Segmentation"],
            created_at=datetime.utcnow()
        )
        analyzer.scenario_cache["test_scenario"] = test_scenario
        
        # Test JSON export
        export_data = analyzer.export_analysis_results("json")
        assert isinstance(export_data, str)
        
        # Test invalid format
        with pytest.raises(ValueError):
            analyzer.export_analysis_results("invalid_format")
    
    def test_cache_management(self, analyzer):
        """Test cache management functionality."""
        # Add test data to caches
        analyzer.scenario_cache["test"] = Mock()
        analyzer.analysis_cache["test"] = Mock()
        
        # Clear caches
        analyzer.clear_cache()
        
        assert len(analyzer.scenario_cache) == 0
        assert len(analyzer.analysis_cache) == 0


class TestAttackPathCreation:
    """Test attack path creation and analysis."""
    
    def test_attack_path_properties(self):
        """Test attack path property calculations."""
        attack_path = AttackPath(
            path_id="test_path",
            source_asset_id="source",
            target_asset_id="target",
            path_nodes=["source", "middle", "target"],
            path_edges=[("source", "middle"), ("middle", "target")],
            path_type=PathType.LATERAL_MOVEMENT,
            attack_techniques=[AttackTechnique.INITIAL_ACCESS, AttackTechnique.LATERAL_MOVEMENT],
            risk_score=80.0,
            likelihood=0.8,
            impact_score=90.0,
            blast_radius=10,
            estimated_time=120,
            required_privileges=["user", "admin"],
            detection_difficulty=0.7,
            mitigation_cost=10000.0,
            path_length=3
        )
        
        # Test criticality score calculation
        criticality = attack_path.criticality_score
        assert 0 <= criticality <= 100
        assert criticality > 50  # Should be high given the high scores
    
    def test_blast_radius_result_properties(self):
        """Test blast radius result properties."""
        blast_result = BlastRadiusResult(
            source_asset_id="source",
            affected_assets={"asset1", "asset2", "asset3"},
            impact_by_degree={0: {"source"}, 1: {"asset1", "asset2"}, 2: {"asset3"}},
            total_impact_score=250.0,
            critical_assets_affected={"asset1"},
            data_assets_affected={"asset2"},
            service_disruption_score=75.0,
            financial_impact=50000.0,
            compliance_impact=["GDPR", "SOX"],
            recovery_time_estimate=24
        )
        
        assert len(blast_result.affected_assets) == 3
        assert blast_result.total_impact_score == 250.0
        assert "GDPR" in blast_result.compliance_impact
        assert blast_result.recovery_time_estimate == 24


class TestPerformance:
    """Test performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_large_graph_performance(self):
        """Test performance with larger graphs."""
        graph_engine = GraphEngine(max_workers=4)
        
        # Create a larger test graph
        num_assets = 100
        for i in range(num_assets):
            asset_data = {
                "asset_type": "server" if i % 2 == 0 else "database",
                "risk_score": 50 + (i % 50),
                "business_criticality": "high" if i % 10 == 0 else "medium"
            }
            graph_engine.add_asset(f"asset_{i}", asset_data)
        
        # Add relationships (create a connected graph)
        for i in range(num_assets - 1):
            rel_data = {"relationship_type": "communicates_with"}
            graph_engine.add_relationship(f"asset_{i}", f"asset_{i+1}", rel_data)
        
        # Test path finding performance
        import time
        start_time = time.time()
        
        paths = await graph_engine.find_attack_paths("asset_0", "asset_50", max_path_length=10, max_paths=5)
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert analysis_time < 5.0  # 5 seconds
        assert len(paths) > 0
    
    def test_cache_effectiveness(self):
        """Test cache effectiveness."""
        graph_engine = GraphEngine()
        
        # Add test data
        graph_engine.add_asset("asset1", {"asset_type": "server"})
        graph_engine.add_asset("asset2", {"asset_type": "database"})
        graph_engine.add_relationship("asset1", "asset2", {"relationship_type": "accesses"})
        
        # Reset metrics
        graph_engine.metrics["cache_hits"] = 0
        graph_engine.metrics["cache_misses"] = 0
        
        # First call should be a cache miss
        asyncio.run(graph_engine.find_attack_paths("asset1", "asset2"))
        assert graph_engine.metrics["cache_misses"] > 0
        
        # Second call should be a cache hit
        cache_misses_before = graph_engine.metrics["cache_misses"]
        asyncio.run(graph_engine.find_attack_paths("asset1", "asset2"))
        assert graph_engine.metrics["cache_hits"] > 0
        assert graph_engine.metrics["cache_misses"] == cache_misses_before  # No new misses
