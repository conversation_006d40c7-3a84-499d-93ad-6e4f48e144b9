"""Integration tests for asset management system."""

import pytest
import uuid
from datetime import datetime, timedelta
from typing import Dict, List

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.db.models.asset import (
    Asset,
    AssetRelationship,
    AssetTag,
    DiscoveryJob,
    AssetProvider,
    AssetType,
    DiscoverySource,
    RelationshipType,
    RiskLevel,
)
from app.services.asset_service import AssetService


class TestAssetIntegration:
    """Integration test cases for asset management system."""

    def test_asset_service_database_integration(self, db_session: Session):
        """Test asset service integration with database."""
        service = AssetService(db_session)
        
        # Test asset creation and database persistence
        from app.schemas.asset import AssetCreate
        
        asset_data = AssetCreate(
            name="integration-test-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            provider_id="i-integration123",
            provider_region="us-east-1",
            ip_addresses=["**********"],
            discovery_source=DiscoverySource.CLOUD_API,
            environment="integration-test",
            configuration={"instance_type": "t3.medium"},
            tags={"Test": "integration"}
        )
        
        # Create asset
        created_asset = service.create_asset(asset_data)
        assert created_asset.id is not None
        
        # Verify database persistence
        db_asset = db_session.query(Asset).filter(Asset.id == created_asset.id).first()
        assert db_asset is not None
        assert db_asset.name == "integration-test-server"
        assert db_asset.provider_id == "i-integration123"
        assert db_asset.configuration == {"instance_type": "t3.medium"}
        assert db_asset.tags == {"Test": "integration"}
        
        # Test asset update
        from app.schemas.asset import AssetUpdate
        
        update_data = AssetUpdate(
            environment="production",
            risk_score=85,
            configuration={"instance_type": "t3.large"}
        )
        
        updated_asset = service.update_asset(created_asset.id, update_data)
        
        # Verify update in database
        db_session.refresh(db_asset)
        assert db_asset.environment == "production"
        assert db_asset.risk_score == 85
        assert db_asset.configuration == {"instance_type": "t3.large"}
        assert db_asset.change_count == 1
        assert db_asset.last_configuration_change is not None
        
        # Test asset deletion
        service.delete_asset(created_asset.id)
        
        # Verify deletion
        deleted_asset = db_session.query(Asset).filter(Asset.id == created_asset.id).first()
        assert deleted_asset is None

    def test_asset_relationship_database_integration(self, db_session: Session):
        """Test asset relationship integration with database."""
        service = AssetService(db_session)
        
        # Create source and target assets
        from app.schemas.asset import AssetCreate, AssetRelationshipCreate
        
        source_data = AssetCreate(
            name="source-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        source_asset = service.create_asset(source_data)
        
        target_data = AssetCreate(
            name="target-database",
            asset_type=AssetType.CLOUD_DATABASE,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        target_asset = service.create_asset(target_data)
        
        # Create relationship
        relationship_data = AssetRelationshipCreate(
            source_asset_id=source_asset.id,
            target_asset_id=target_asset.id,
            relationship_type=RelationshipType.DEPENDS_ON,
            protocol="TCP",
            port=5432,
            discovery_source=DiscoverySource.CLOUD_API,
            confidence_score=0.95
        )
        
        created_relationship = service.create_relationship(relationship_data)
        
        # Verify database persistence
        db_relationship = db_session.query(AssetRelationship).filter(
            AssetRelationship.id == created_relationship.id
        ).first()
        assert db_relationship is not None
        assert db_relationship.source_asset_id == source_asset.id
        assert db_relationship.target_asset_id == target_asset.id
        assert db_relationship.relationship_type == RelationshipType.DEPENDS_ON
        assert db_relationship.protocol == "TCP"
        assert db_relationship.port == 5432
        assert db_relationship.confidence_score == 0.95
        
        # Test relationship queries
        relationships = service.get_asset_relationships(source_asset.id)
        assert len(relationships) == 1
        assert relationships[0].id == created_relationship.id
        
        # Test relationship deletion cascades
        service.delete_asset(source_asset.id)
        
        # Verify relationship is deleted when source asset is deleted
        remaining_relationships = db_session.query(AssetRelationship).filter(
            AssetRelationship.source_asset_id == source_asset.id
        ).all()
        assert len(remaining_relationships) == 0

    def test_discovery_job_asset_integration(self, db_session: Session):
        """Test discovery job integration with asset creation."""
        service = AssetService(db_session)
        
        # Create discovery job
        from app.schemas.asset import DiscoveryJobCreate, AssetCreate
        
        job_data = DiscoveryJobCreate(
            name="Integration Test Discovery",
            job_type="integration_test",
            description="Integration test discovery job",
            configuration={"test": True}
        )
        
        discovery_job = service.create_discovery_job(job_data)
        
        # Start the job
        started_job = service.start_discovery_job(discovery_job.id, executor="integration-test")
        
        # Create assets associated with the discovery job
        assets_data = [
            AssetCreate(
                name="discovered-server-1",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
            AssetCreate(
                name="discovered-server-2",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
            ),
        ]
        
        created_assets = []
        for asset_data in assets_data:
            asset = service.create_asset(asset_data)
            # Associate asset with discovery job
            asset.discovery_job_id = discovery_job.id
            db_session.commit()
            created_assets.append(asset)
        
        # Complete the discovery job
        completed_job = service.complete_discovery_job(
            discovery_job.id,
            assets_discovered=len(created_assets),
            assets_updated=0,
            relationships_discovered=0,
            errors_count=0
        )
        
        # Verify job completion
        assert completed_job.status.value == "completed"
        assert completed_job.assets_discovered == 2
        assert completed_job.completed_at is not None
        
        # Verify assets are associated with the job
        job_assets = db_session.query(Asset).filter(
            Asset.discovery_job_id == discovery_job.id
        ).all()
        assert len(job_assets) == 2
        
        # Test job deletion and asset cleanup
        service.delete_discovery_job(discovery_job.id)
        
        # Verify job is deleted
        deleted_job = db_session.query(DiscoveryJob).filter(
            DiscoveryJob.id == discovery_job.id
        ).first()
        assert deleted_job is None
        
        # Verify assets have job reference removed
        orphaned_assets = db_session.query(Asset).filter(
            Asset.id.in_([asset.id for asset in created_assets])
        ).all()
        for asset in orphaned_assets:
            assert asset.discovery_job_id is None

    def test_asset_tag_integration(self, db_session: Session):
        """Test asset tag integration with asset management."""
        service = AssetService(db_session)
        
        # Create asset
        from app.schemas.asset import AssetCreate, AssetTagCreate
        
        asset_data = AssetCreate(
            name="tagged-server",
            asset_type=AssetType.SERVER,
            provider=AssetProvider.AWS,
            discovery_source=DiscoverySource.CLOUD_API,
        )
        asset = service.create_asset(asset_data)
        
        # Add multiple tags
        tags_data = [
            AssetTagCreate(
                asset_id=asset.id,
                key="Environment",
                value="production",
                source="manual"
            ),
            AssetTagCreate(
                asset_id=asset.id,
                key="Team",
                value="backend",
                source="automated"
            ),
            AssetTagCreate(
                asset_id=asset.id,
                key="Critical",
                value="true",
                source="policy",
                is_system=True
            ),
        ]
        
        created_tags = []
        for tag_data in tags_data:
            tag = service.add_asset_tag(tag_data)
            created_tags.append(tag)
        
        # Verify tags in database
        db_tags = db_session.query(AssetTag).filter(AssetTag.asset_id == asset.id).all()
        assert len(db_tags) == 3
        
        tag_keys = [tag.key for tag in db_tags]
        assert "Environment" in tag_keys
        assert "Team" in tag_keys
        assert "Critical" in tag_keys
        
        # Test tag retrieval
        retrieved_tags = service.get_asset_tags(asset.id)
        assert len(retrieved_tags) == 3
        
        # Test tag update
        from app.schemas.asset import AssetTagUpdate
        
        env_tag = next(tag for tag in created_tags if tag.key == "Environment")
        update_data = AssetTagUpdate(value="staging")
        updated_tag = service.update_asset_tag(env_tag.id, update_data)
        assert updated_tag.value == "staging"
        
        # Test asset deletion cascades to tags
        service.delete_asset(asset.id)
        
        # Verify tags are deleted
        remaining_tags = db_session.query(AssetTag).filter(AssetTag.asset_id == asset.id).all()
        assert len(remaining_tags) == 0

    def test_asset_search_database_integration(self, db_session: Session):
        """Test asset search integration with database queries."""
        service = AssetService(db_session)
        
        # Create diverse test assets
        from app.schemas.asset import AssetCreate, AssetSearchRequest
        
        test_assets = [
            AssetCreate(
                name="web-server-prod",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="production",
                owner="web-team",
                team="frontend",
                risk_level=RiskLevel.HIGH,
                tags={"Tier": "web", "Public": "true"}
            ),
            AssetCreate(
                name="db-server-staging",
                asset_type=AssetType.CLOUD_DATABASE,
                provider=AssetProvider.AZURE,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="staging",
                owner="db-team",
                team="backend",
                risk_level=RiskLevel.MEDIUM,
                tags={"Tier": "database", "Encrypted": "true"}
            ),
            AssetCreate(
                name="api-gateway-dev",
                asset_type=AssetType.API_ENDPOINT,
                provider=AssetProvider.GCP,
                discovery_source=DiscoverySource.API_DISCOVERY,
                environment="development",
                owner="api-team",
                team="backend",
                risk_level=RiskLevel.LOW,
                tags={"Tier": "api", "Version": "v2"}
            ),
        ]
        
        created_assets = []
        for asset_data in test_assets:
            asset = service.create_asset(asset_data)
            created_assets.append(asset)
        
        # Test various search scenarios
        search_scenarios = [
            # Text search
            {
                "request": AssetSearchRequest(query="web-server", page=1, size=10),
                "expected_count": 1,
                "expected_names": ["web-server-prod"]
            },
            # Provider filter
            {
                "request": AssetSearchRequest(providers=[AssetProvider.AWS], page=1, size=10),
                "expected_count": 1,
                "expected_names": ["web-server-prod"]
            },
            # Asset type filter
            {
                "request": AssetSearchRequest(asset_types=[AssetType.SERVER, AssetType.CLOUD_DATABASE], page=1, size=10),
                "expected_count": 2,
                "expected_names": ["web-server-prod", "db-server-staging"]
            },
            # Environment filter
            {
                "request": AssetSearchRequest(environments=["production", "staging"], page=1, size=10),
                "expected_count": 2,
                "expected_names": ["web-server-prod", "db-server-staging"]
            },
            # Team filter
            {
                "request": AssetSearchRequest(teams=["backend"], page=1, size=10),
                "expected_count": 2,
                "expected_names": ["db-server-staging", "api-gateway-dev"]
            },
            # Risk level filter
            {
                "request": AssetSearchRequest(risk_levels=[RiskLevel.HIGH, RiskLevel.MEDIUM], page=1, size=10),
                "expected_count": 2,
                "expected_names": ["web-server-prod", "db-server-staging"]
            },
            # Tag filter
            {
                "request": AssetSearchRequest(tags={"Tier": "api"}, page=1, size=10),
                "expected_count": 1,
                "expected_names": ["api-gateway-dev"]
            },
            # Complex filter
            {
                "request": AssetSearchRequest(
                    query="server",
                    providers=[AssetProvider.AWS, AssetProvider.AZURE],
                    environments=["production", "staging"],
                    page=1,
                    size=10
                ),
                "expected_count": 2,
                "expected_names": ["web-server-prod", "db-server-staging"]
            },
        ]
        
        for i, scenario in enumerate(search_scenarios):
            assets, total = service.search_assets(scenario["request"])
            
            assert total == scenario["expected_count"], f"Scenario {i}: expected {scenario['expected_count']}, got {total}"
            assert len(assets) == scenario["expected_count"], f"Scenario {i}: asset count mismatch"
            
            asset_names = [asset.name for asset in assets]
            for expected_name in scenario["expected_names"]:
                assert expected_name in asset_names, f"Scenario {i}: missing expected asset {expected_name}"

    def test_asset_statistics_database_integration(self, db_session: Session):
        """Test asset statistics integration with database aggregations."""
        service = AssetService(db_session)
        
        # Create known test data for statistics
        from app.schemas.asset import AssetCreate
        
        test_assets = [
            # AWS assets
            AssetCreate(
                name="aws-server-1",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="production",
                risk_level=RiskLevel.HIGH,
                compliance_status="compliant",
                health_status="healthy"
            ),
            AssetCreate(
                name="aws-database-1",
                asset_type=AssetType.CLOUD_DATABASE,
                provider=AssetProvider.AWS,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="production",
                risk_level=RiskLevel.MEDIUM,
                compliance_status="compliant",
                health_status="healthy"
            ),
            # Azure assets
            AssetCreate(
                name="azure-server-1",
                asset_type=AssetType.SERVER,
                provider=AssetProvider.AZURE,
                discovery_source=DiscoverySource.CLOUD_API,
                environment="staging",
                risk_level=RiskLevel.MEDIUM,
                compliance_status="non-compliant",
                health_status="warning"
            ),
            # GCP assets
            AssetCreate(
                name="gcp-function-1",
                asset_type=AssetType.CLOUD_FUNCTION,
                provider=AssetProvider.GCP,
                discovery_source=DiscoverySource.API_DISCOVERY,
                environment="development",
                risk_level=RiskLevel.LOW,
                compliance_status="unknown",
                health_status="unknown"
            ),
        ]
        
        for asset_data in test_assets:
            service.create_asset(asset_data)
        
        # Get statistics
        stats = service.get_asset_statistics()
        
        # Verify statistics accuracy
        assert stats["total_assets"] == 4
        
        # Assets by type
        assert stats["assets_by_type"]["server"] == 2
        assert stats["assets_by_type"]["cloud_database"] == 1
        assert stats["assets_by_type"]["cloud_function"] == 1
        
        # Assets by provider
        assert stats["assets_by_provider"]["aws"] == 2
        assert stats["assets_by_provider"]["azure"] == 1
        assert stats["assets_by_provider"]["gcp"] == 1
        
        # Assets by environment
        assert stats["assets_by_environment"]["production"] == 2
        assert stats["assets_by_environment"]["staging"] == 1
        assert stats["assets_by_environment"]["development"] == 1
        
        # Assets by risk level
        assert stats["assets_by_risk_level"]["high"] == 1
        assert stats["assets_by_risk_level"]["medium"] == 2
        assert stats["assets_by_risk_level"]["low"] == 1
        
        # Discovery sources
        assert stats["discovery_sources"]["cloud_api"] == 3
        assert stats["discovery_sources"]["api_discovery"] == 1
        
        # Compliance summary
        assert stats["compliance_summary"]["compliant"] == 2
        assert stats["compliance_summary"]["non-compliant"] == 1
        assert stats["compliance_summary"]["unknown"] == 1
        
        # Health summary
        assert stats["health_summary"]["healthy"] == 2
        assert stats["health_summary"]["warning"] == 1
        assert stats["health_summary"]["unknown"] == 1

    def test_concurrent_asset_operations(self, db_session: Session):
        """Test concurrent asset operations for data consistency."""
        import threading
        import time
        
        service = AssetService(db_session)
        results = []
        errors = []
        
        def create_asset_worker(worker_id: int):
            """Worker function to create assets concurrently."""
            try:
                from app.schemas.asset import AssetCreate
                
                asset_data = AssetCreate(
                    name=f"concurrent-asset-{worker_id}",
                    asset_type=AssetType.SERVER,
                    provider=AssetProvider.AWS,
                    provider_id=f"i-concurrent-{worker_id}",
                    discovery_source=DiscoverySource.CLOUD_API,
                )
                
                asset = service.create_asset(asset_data)
                results.append(asset.id)
            except Exception as e:
                errors.append(f"Worker {worker_id}: {str(e)}")
        
        # Create multiple threads to test concurrent operations
        threads = []
        num_workers = 5
        
        for i in range(num_workers):
            thread = threading.Thread(target=create_asset_worker, args=(i,))
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify results
        assert len(errors) == 0, f"Concurrent operations failed: {errors}"
        assert len(results) == num_workers, f"Expected {num_workers} assets, got {len(results)}"
        
        # Verify all assets were created in database
        created_assets = db_session.query(Asset).filter(
            Asset.name.like("concurrent-asset-%")
        ).all()
        assert len(created_assets) == num_workers
