"""Comprehensive tests for MITRE ATT&CK Pydantic schemas."""

import pytest
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

from pydantic import ValidationError

from app.schemas.mitre import (
    MitreTechniqueBase,
    MitreTechniqueCreate,
    MitreTechniqueUpdate,
    MitreTechniqueResponse,
    MitreTacticBase,
    MitreTacticCreate,
    MitreTacticUpdate,
    MitreTacticResponse,
    MitreGroupBase,
    MitreGroupCreate,
    MitreGroupUpdate,
    MitreGroupResponse,
    MitreSoftwareBase,
    MitreSoftwareCreate,
    MitreSoftwareUpdate,
    MitreSoftwareResponse,
    MitreMitigationBase,
    MitreMitigationCreate,
    MitreMitigationUpdate,
    MitreMitigationResponse,
    MitreDataSyncBase,
    MitreDataSyncCreate,
    MitreDataSyncUpdate,
    MitreDataSyncResponse,
    MitreSearchRequest,
    MitreSearchResponse,
    MitreTechniqueListResponse,
    MitreTacticListResponse,
    MitreGroupListResponse,
)
from app.db.models.mitre import MitreDomain, MitreEntityStatus, MitreDataSource


class TestMitreTechniqueSchemas:
    """Test MITRE Technique Pydantic schemas."""
    
    def test_technique_base_valid_data(self):
        """Test MitreTechniqueBase with valid data."""
        data = {
            "technique_id": "T1234",
            "name": "Test Technique",
            "description": "Test technique description",
            "domain": MitreDomain.ENTERPRISE,
            "status": MitreEntityStatus.ACTIVE,
            "platforms": ["Windows", "Linux"],
            "data_sources": ["Process monitoring"]
        }
        
        technique = MitreTechniqueBase(**data)
        
        assert technique.technique_id == "T1234"
        assert technique.name == "Test Technique"
        assert technique.domain == MitreDomain.ENTERPRISE
        assert technique.platforms == ["Windows", "Linux"]
        assert technique.is_subtechnique is False
        assert technique.parent_technique_id is None
    
    def test_technique_base_subtechnique_valid(self):
        """Test MitreTechniqueBase with valid sub-technique."""
        data = {
            "technique_id": "T1234.001",
            "name": "Test Sub-technique",
            "domain": MitreDomain.ENTERPRISE,
            "is_subtechnique": True,
            "parent_technique_id": "T1234"
        }
        
        technique = MitreTechniqueBase(**data)
        
        assert technique.technique_id == "T1234.001"
        assert technique.is_subtechnique is True
        assert technique.parent_technique_id == "T1234"
    
    def test_technique_id_validation_invalid_format(self):
        """Test technique ID validation with invalid format."""
        invalid_ids = [
            "1234",  # Missing T prefix
            "T12345",  # Too many digits
            "T123",  # Too few digits
            "T1234.01",  # Invalid sub-technique format
            "t1234",  # Lowercase
            "T1234.1234",  # Invalid sub-technique format
        ]
        
        for invalid_id in invalid_ids:
            with pytest.raises(ValidationError) as exc_info:
                MitreTechniqueBase(
                    technique_id=invalid_id,
                    name="Test",
                    domain=MitreDomain.ENTERPRISE
                )
            
            assert "Technique ID must be in format" in str(exc_info.value)
    
    def test_technique_id_case_normalization(self):
        """Test technique ID case normalization."""
        technique = MitreTechniqueBase(
            technique_id="t1234",
            name="Test",
            domain=MitreDomain.ENTERPRISE
        )
        
        assert technique.technique_id == "T1234"
    
    def test_technique_platforms_validation_invalid(self):
        """Test platform validation with invalid platforms."""
        with pytest.raises(ValidationError) as exc_info:
            MitreTechniqueBase(
                technique_id="T1234",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                platforms=["InvalidPlatform"]
            )
        
        assert "Invalid platform" in str(exc_info.value)
    
    def test_technique_platforms_validation_valid(self):
        """Test platform validation with valid platforms."""
        valid_platforms = ["Windows", "Linux", "macOS", "Android", "iOS"]
        
        technique = MitreTechniqueBase(
            technique_id="T1234",
            name="Test",
            domain=MitreDomain.ENTERPRISE,
            platforms=valid_platforms
        )
        
        assert technique.platforms == valid_platforms
    
    def test_subtechnique_validation_missing_parent(self):
        """Test sub-technique validation when parent ID is missing."""
        with pytest.raises(ValidationError) as exc_info:
            MitreTechniqueBase(
                technique_id="T1234.001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                is_subtechnique=True
                # Missing parent_technique_id
            )
        
        assert "Sub-techniques must have a parent_technique_id" in str(exc_info.value)
    
    def test_subtechnique_validation_invalid_parent_relationship(self):
        """Test sub-technique validation with invalid parent relationship."""
        with pytest.raises(ValidationError) as exc_info:
            MitreTechniqueBase(
                technique_id="T1234.001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                is_subtechnique=True,
                parent_technique_id="T5678"  # Doesn't match technique_id prefix
            )
        
        assert "Sub-technique ID must start with parent technique ID" in str(exc_info.value)
    
    def test_technique_validation_parent_without_subtechnique_flag(self):
        """Test validation when parent_technique_id is set but is_subtechnique is False."""
        with pytest.raises(ValidationError) as exc_info:
            MitreTechniqueBase(
                technique_id="T1234",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                is_subtechnique=False,
                parent_technique_id="T5678"
            )
        
        assert "Only sub-techniques can have a parent_technique_id" in str(exc_info.value)
    
    def test_technique_capec_id_validation(self):
        """Test CAPEC ID validation."""
        # Valid CAPEC ID
        technique = MitreTechniqueBase(
            technique_id="T1234",
            name="Test",
            domain=MitreDomain.ENTERPRISE,
            capec_id="CAPEC-123"
        )
        assert technique.capec_id == "CAPEC-123"
        
        # Invalid CAPEC ID
        with pytest.raises(ValidationError):
            MitreTechniqueBase(
                technique_id="T1234",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                capec_id="INVALID-123"
            )
    
    def test_technique_name_length_validation(self):
        """Test technique name length validation."""
        # Empty name
        with pytest.raises(ValidationError):
            MitreTechniqueBase(
                technique_id="T1234",
                name="",
                domain=MitreDomain.ENTERPRISE
            )
        
        # Too long name
        with pytest.raises(ValidationError):
            MitreTechniqueBase(
                technique_id="T1234",
                name="x" * 256,  # Exceeds max_length=255
                domain=MitreDomain.ENTERPRISE
            )
    
    def test_technique_create_schema(self):
        """Test MitreTechniqueCreate schema."""
        data = {
            "technique_id": "T1234",
            "name": "Test Technique",
            "domain": MitreDomain.ENTERPRISE,
            "platforms": ["Windows"]
        }
        
        technique = MitreTechniqueCreate(**data)
        assert technique.technique_id == "T1234"
        assert technique.name == "Test Technique"
    
    def test_technique_update_schema(self):
        """Test MitreTechniqueUpdate schema."""
        data = {
            "name": "Updated Name",
            "description": "Updated description",
            "platforms": ["Linux", "macOS"]
        }
        
        update = MitreTechniqueUpdate(**data)
        assert update.name == "Updated Name"
        assert update.platforms == ["Linux", "macOS"]
    
    def test_technique_response_schema(self):
        """Test MitreTechniqueResponse schema."""
        data = {
            "id": uuid.uuid4(),
            "technique_id": "T1234",
            "name": "Test Technique",
            "domain": MitreDomain.ENTERPRISE,
            "status": MitreEntityStatus.ACTIVE,
            "version": "1.0",
            "created_date": datetime.utcnow(),
            "modified_date": datetime.utcnow(),
            "data_source": MitreDataSource.OFFICIAL,
            "is_subtechnique": False
        }
        
        response = MitreTechniqueResponse(**data)
        assert response.technique_id == "T1234"
        assert response.is_subtechnique is False


class TestMitreTacticSchemas:
    """Test MITRE Tactic Pydantic schemas."""
    
    def test_tactic_base_valid_data(self):
        """Test MitreTacticBase with valid data."""
        data = {
            "tactic_id": "TA0001",
            "name": "Initial Access",
            "description": "Initial access tactic",
            "domain": MitreDomain.ENTERPRISE,
            "short_name": "initial-access"
        }
        
        tactic = MitreTacticBase(**data)
        
        assert tactic.tactic_id == "TA0001"
        assert tactic.name == "Initial Access"
        assert tactic.short_name == "initial-access"
    
    def test_tactic_id_validation_invalid_format(self):
        """Test tactic ID validation with invalid format."""
        invalid_ids = [
            "0001",  # Missing TA prefix
            "TA12345",  # Too many digits
            "TA123",  # Too few digits
            "ta0001",  # Lowercase
        ]
        
        for invalid_id in invalid_ids:
            with pytest.raises(ValidationError) as exc_info:
                MitreTacticBase(
                    tactic_id=invalid_id,
                    name="Test",
                    domain=MitreDomain.ENTERPRISE
                )
            
            assert "Tactic ID must be in format" in str(exc_info.value)
    
    def test_tactic_id_case_normalization(self):
        """Test tactic ID case normalization."""
        tactic = MitreTacticBase(
            tactic_id="ta0001",
            name="Test",
            domain=MitreDomain.ENTERPRISE
        )
        
        assert tactic.tactic_id == "TA0001"
    
    def test_tactic_short_name_validation_invalid(self):
        """Test short name validation with invalid format."""
        with pytest.raises(ValidationError) as exc_info:
            MitreTacticBase(
                tactic_id="TA0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                short_name="Invalid_Name"  # Contains underscore
            )
        
        assert "Short names must be lowercase with hyphens only" in str(exc_info.value)
    
    def test_tactic_short_name_validation_valid(self):
        """Test short name validation with valid format."""
        valid_names = ["initial-access", "execution", "persistence", "privilege-escalation"]
        
        for name in valid_names:
            tactic = MitreTacticBase(
                tactic_id="TA0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                short_name=name
            )
            assert tactic.short_name == name


class TestMitreGroupSchemas:
    """Test MITRE Group Pydantic schemas."""
    
    def test_group_base_valid_data(self):
        """Test MitreGroupBase with valid data."""
        data = {
            "group_id": "G0001",
            "name": "APT1",
            "description": "Advanced Persistent Threat 1",
            "domain": MitreDomain.ENTERPRISE,
            "aliases": ["Comment Crew"],
            "country": "China",
            "sophistication": "High",
            "motivation": ["Espionage"]
        }
        
        group = MitreGroupBase(**data)
        
        assert group.group_id == "G0001"
        assert group.name == "APT1"
        assert group.aliases == ["Comment Crew"]
        assert group.sophistication == "High"
    
    def test_group_id_validation_invalid_format(self):
        """Test group ID validation with invalid format."""
        invalid_ids = [
            "0001",  # Missing G prefix
            "G12345",  # Too many digits
            "G123",  # Too few digits
            "g0001",  # Lowercase
        ]
        
        for invalid_id in invalid_ids:
            with pytest.raises(ValidationError) as exc_info:
                MitreGroupBase(
                    group_id=invalid_id,
                    name="Test",
                    domain=MitreDomain.ENTERPRISE
                )
            
            assert "Group ID must be in format" in str(exc_info.value)
    
    def test_group_sophistication_validation_invalid(self):
        """Test sophistication validation with invalid value."""
        with pytest.raises(ValidationError) as exc_info:
            MitreGroupBase(
                group_id="G0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                sophistication="Invalid"
            )
        
        assert "Sophistication must be one of" in str(exc_info.value)
    
    def test_group_sophistication_validation_valid(self):
        """Test sophistication validation with valid values."""
        valid_levels = ["High", "Medium", "Low"]
        
        for level in valid_levels:
            group = MitreGroupBase(
                group_id="G0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                sophistication=level
            )
            assert group.sophistication == level
    
    def test_group_motivation_validation_invalid(self):
        """Test motivation validation with invalid value."""
        with pytest.raises(ValidationError) as exc_info:
            MitreGroupBase(
                group_id="G0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                motivation=["InvalidMotivation"]
            )
        
        assert "Invalid motivation" in str(exc_info.value)
    
    def test_group_motivation_validation_valid(self):
        """Test motivation validation with valid values."""
        valid_motivations = ["Financial", "Espionage", "Sabotage", "Ideology"]
        
        group = MitreGroupBase(
            group_id="G0001",
            name="Test",
            domain=MitreDomain.ENTERPRISE,
            motivation=valid_motivations
        )
        assert group.motivation == valid_motivations
    
    def test_group_timeline_validation_invalid(self):
        """Test timeline validation with invalid dates."""
        first_seen = datetime.utcnow()
        last_seen = first_seen - timedelta(days=30)  # Last seen before first seen
        
        with pytest.raises(ValidationError) as exc_info:
            MitreGroupBase(
                group_id="G0001",
                name="Test",
                domain=MitreDomain.ENTERPRISE,
                first_seen=first_seen,
                last_seen=last_seen
            )
        
        assert "first_seen cannot be after last_seen" in str(exc_info.value)
    
    def test_group_timeline_validation_valid(self):
        """Test timeline validation with valid dates."""
        first_seen = datetime.utcnow() - timedelta(days=30)
        last_seen = datetime.utcnow()
        
        group = MitreGroupBase(
            group_id="G0001",
            name="Test",
            domain=MitreDomain.ENTERPRISE,
            first_seen=first_seen,
            last_seen=last_seen
        )
        
        assert group.first_seen == first_seen
        assert group.last_seen == last_seen


class TestMitreSearchSchemas:
    """Test MITRE Search Pydantic schemas."""

    def test_search_request_valid_data(self):
        """Test MitreSearchRequest with valid data."""
        data = {
            "query": "lateral movement",
            "domains": [MitreDomain.ENTERPRISE],
            "entity_types": ["technique", "tactic"],
            "status": [MitreEntityStatus.ACTIVE],
            "limit": 50,
            "offset": 0
        }

        request = MitreSearchRequest(**data)

        assert request.query == "lateral movement"
        assert request.domains == [MitreDomain.ENTERPRISE]
        assert request.limit == 50
        assert request.offset == 0

    def test_search_request_limit_validation(self):
        """Test search request limit validation."""
        # Valid limit
        request = MitreSearchRequest(query="test", limit=100)
        assert request.limit == 100

        # Invalid limit (too high)
        with pytest.raises(ValidationError):
            MitreSearchRequest(query="test", limit=1001)

    def test_search_request_offset_validation(self):
        """Test search request offset validation."""
        # Valid offset
        request = MitreSearchRequest(query="test", offset=100)
        assert request.offset == 100

        # Invalid offset (negative)
        with pytest.raises(ValidationError):
            MitreSearchRequest(query="test", offset=-1)

    def test_search_response_valid_data(self):
        """Test MitreSearchResponse with valid data."""
        data = {
            "results": [
                {"id": "T1234", "name": "Test Technique", "type": "technique"},
                {"id": "TA0001", "name": "Test Tactic", "type": "tactic"}
            ],
            "total": 2,
            "query": "test",
            "execution_time_ms": 45.5
        }

        response = MitreSearchResponse(**data)

        assert len(response.results) == 2
        assert response.total == 2
        assert response.query == "test"
        assert response.execution_time_ms == 45.5


class TestMitreDataSyncSchemas:
    """Test MITRE Data Sync Pydantic schemas."""

    def test_data_sync_base_valid_data(self):
        """Test MitreDataSyncBase with valid data."""
        data = {
            "sync_id": "sync_enterprise_123",
            "domain": MitreDomain.ENTERPRISE,
            "sync_type": "manual",
            "status": "pending",
            "total_entities": 100,
            "processed_entities": 0,
            "failed_entities": 0,
            "source_url": "https://example.com/data.json",
            "triggered_by": "test_user"
        }

        sync = MitreDataSyncBase(**data)

        assert sync.sync_id == "sync_enterprise_123"
        assert sync.domain == MitreDomain.ENTERPRISE
        assert sync.sync_type == "manual"
        assert sync.status == "pending"
        assert sync.total_entities == 100

    def test_data_sync_create_schema(self):
        """Test MitreDataSyncCreate schema."""
        data = {
            "sync_id": "sync_test_456",
            "domain": MitreDomain.MOBILE,
            "sync_type": "automatic",
            "status": "running"
        }

        sync = MitreDataSyncCreate(**data)
        assert sync.sync_id == "sync_test_456"
        assert sync.domain == MitreDomain.MOBILE

    def test_data_sync_update_schema(self):
        """Test MitreDataSyncUpdate schema."""
        data = {
            "status": "completed",
            "processed_entities": 95,
            "failed_entities": 5,
            "sync_results": {"techniques": 50, "tactics": 14}
        }

        update = MitreDataSyncUpdate(**data)
        assert update.status == "completed"
        assert update.processed_entities == 95
        assert update.sync_results["techniques"] == 50

    def test_data_sync_response_schema(self):
        """Test MitreDataSyncResponse schema."""
        data = {
            "id": uuid.uuid4(),
            "sync_id": "sync_response_789",
            "domain": MitreDomain.ICS,
            "sync_type": "manual",
            "status": "completed",
            "processed_entities": 100,
            "failed_entities": 0,
            "started_at": datetime.utcnow(),
            "completed_at": datetime.utcnow(),
            "duration_seconds": 120.5
        }

        response = MitreDataSyncResponse(**data)
        assert response.sync_id == "sync_response_789"
        assert response.domain == MitreDomain.ICS
        assert response.duration_seconds == 120.5


class TestMitreListResponseSchemas:
    """Test MITRE List Response Pydantic schemas."""

    def test_technique_list_response(self):
        """Test MitreTechniqueListResponse schema."""
        technique_data = {
            "id": uuid.uuid4(),
            "technique_id": "T1234",
            "name": "Test Technique",
            "domain": MitreDomain.ENTERPRISE,
            "status": MitreEntityStatus.ACTIVE,
            "version": "1.0",
            "created_date": datetime.utcnow(),
            "modified_date": datetime.utcnow(),
            "data_source": MitreDataSource.OFFICIAL,
            "is_subtechnique": False
        }

        technique = MitreTechniqueResponse(**technique_data)

        data = {
            "techniques": [technique],
            "total": 1,
            "page": 1,
            "size": 50
        }

        response = MitreTechniqueListResponse(**data)
        assert len(response.techniques) == 1
        assert response.total == 1
        assert response.page == 1

    def test_tactic_list_response(self):
        """Test MitreTacticListResponse schema."""
        tactic_data = {
            "id": uuid.uuid4(),
            "tactic_id": "TA0001",
            "name": "Initial Access",
            "domain": MitreDomain.ENTERPRISE,
            "status": MitreEntityStatus.ACTIVE,
            "version": "1.0",
            "created_date": datetime.utcnow(),
            "modified_date": datetime.utcnow(),
            "data_source": MitreDataSource.OFFICIAL
        }

        tactic = MitreTacticResponse(**tactic_data)

        data = {
            "tactics": [tactic],
            "total": 1,
            "page": 1,
            "size": 50
        }

        response = MitreTacticListResponse(**data)
        assert len(response.tactics) == 1
        assert response.total == 1

    def test_group_list_response(self):
        """Test MitreGroupListResponse schema."""
        group_data = {
            "id": uuid.uuid4(),
            "group_id": "G0001",
            "name": "APT1",
            "domain": MitreDomain.ENTERPRISE,
            "status": MitreEntityStatus.ACTIVE,
            "version": "1.0",
            "created_date": datetime.utcnow(),
            "modified_date": datetime.utcnow(),
            "data_source": MitreDataSource.OFFICIAL
        }

        group = MitreGroupResponse(**group_data)

        data = {
            "groups": [group],
            "total": 1,
            "page": 1,
            "size": 50
        }

        response = MitreGroupListResponse(**data)
        assert len(response.groups) == 1
        assert response.total == 1


class TestMitreSoftwareSchemas:
    """Test MITRE Software Pydantic schemas."""

    def test_software_base_valid_data(self):
        """Test MitreSoftwareBase with valid data."""
        data = {
            "software_id": "S0001",
            "name": "Poison Ivy",
            "description": "Remote access trojan",
            "domain": MitreDomain.ENTERPRISE,
            "software_type": "malware",
            "platforms": ["Windows"],
            "labels": ["trojan"]
        }

        software = MitreSoftwareBase(**data)

        assert software.software_id == "S0001"
        assert software.name == "Poison Ivy"
        assert software.software_type == "malware"
        assert software.platforms == ["Windows"]

    def test_software_create_schema(self):
        """Test MitreSoftwareCreate schema."""
        data = {
            "software_id": "S0002",
            "name": "Mimikatz",
            "domain": MitreDomain.ENTERPRISE,
            "software_type": "tool"
        }

        software = MitreSoftwareCreate(**data)
        assert software.software_id == "S0002"
        assert software.software_type == "tool"


class TestMitreMitigationSchemas:
    """Test MITRE Mitigation Pydantic schemas."""

    def test_mitigation_base_valid_data(self):
        """Test MitreMitigationBase with valid data."""
        data = {
            "mitigation_id": "M1001",
            "name": "Multi-factor Authentication",
            "description": "Use multi-factor authentication",
            "domain": MitreDomain.ENTERPRISE,
            "mitigation_type": "preventive"
        }

        mitigation = MitreMitigationBase(**data)

        assert mitigation.mitigation_id == "M1001"
        assert mitigation.name == "Multi-factor Authentication"
        assert mitigation.mitigation_type == "preventive"

    def test_mitigation_create_schema(self):
        """Test MitreMitigationCreate schema."""
        data = {
            "mitigation_id": "M1002",
            "name": "Network Segmentation",
            "domain": MitreDomain.ENTERPRISE
        }

        mitigation = MitreMitigationCreate(**data)
        assert mitigation.mitigation_id == "M1002"
        assert mitigation.name == "Network Segmentation"


class TestSchemaIntegration:
    """Test schema integration and edge cases."""

    def test_schema_serialization_deserialization(self):
        """Test schema serialization and deserialization."""
        # Create a technique
        technique_data = {
            "technique_id": "T1234",
            "name": "Test Technique",
            "domain": MitreDomain.ENTERPRISE,
            "platforms": ["Windows", "Linux"],
            "kill_chain_phases": [
                {"kill_chain_name": "mitre-attack", "phase_name": "execution"}
            ]
        }

        technique = MitreTechniqueBase(**technique_data)

        # Serialize to dict
        serialized = technique.model_dump()

        # Deserialize back
        deserialized = MitreTechniqueBase(**serialized)

        assert deserialized.technique_id == technique.technique_id
        assert deserialized.platforms == technique.platforms
        assert deserialized.kill_chain_phases == technique.kill_chain_phases

    def test_schema_json_serialization(self):
        """Test JSON serialization of schemas."""
        technique = MitreTechniqueBase(
            technique_id="T1234",
            name="Test Technique",
            domain=MitreDomain.ENTERPRISE
        )

        # Should be able to serialize to JSON
        json_str = technique.model_dump_json()
        assert "T1234" in json_str
        assert "Test Technique" in json_str

    def test_schema_partial_updates(self):
        """Test partial updates with update schemas."""
        update_data = {
            "name": "Updated Name",
            "description": "Updated description"
            # Other fields should remain None/unset
        }

        update = MitreTechniqueUpdate(**update_data)

        assert update.name == "Updated Name"
        assert update.description == "Updated description"
        assert update.platforms is None
        assert update.status is None

    def test_schema_field_validation_edge_cases(self):
        """Test edge cases in field validation."""
        # Test empty lists
        technique = MitreTechniqueBase(
            technique_id="T1234",
            name="Test",
            domain=MitreDomain.ENTERPRISE,
            platforms=[],  # Empty list should be valid
            data_sources=[]
        )

        assert technique.platforms == []
        assert technique.data_sources == []

        # Test None vs empty list distinction
        technique2 = MitreTechniqueBase(
            technique_id="T5678",
            name="Test2",
            domain=MitreDomain.ENTERPRISE,
            platforms=None,  # None should be valid
            data_sources=None
        )

        assert technique2.platforms is None
        assert technique2.data_sources is None
