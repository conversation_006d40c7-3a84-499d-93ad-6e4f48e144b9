"""Regression tests for asset management functionality."""

import pytest
import uuid
from datetime import datetime, timedelta
from typing import Dict, List

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.db.models.asset import AssetProvider, AssetType, DiscoverySource, RiskLevel
from app.services.asset_service import AssetService


class TestAssetRegression:
    """Regression test cases for asset management."""

    def test_asset_lifecycle_complete_workflow(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test complete asset lifecycle workflow."""
        # 1. Create asset
        asset_data = {
            "name": "regression-test-server",
            "asset_type": "server",
            "provider": "aws",
            "provider_id": "i-regression123",
            "provider_region": "us-east-1",
            "ip_addresses": ["**********", "*************"],
            "dns_names": ["server.example.com"],
            "ports": [22, 80, 443],
            "protocols": ["SSH", "HTTP", "HTTPS"],
            "discovery_source": "cloud_api",
            "environment": "staging",
            "owner": "regression-team",
            "team": "qa-team",
            "cost_center": "QA-001",
            "description": "Regression test server",
            "configuration": {
                "instance_type": "t3.medium",
                "ami_id": "ami-12345678",
                "security_groups": ["sg-12345678"]
            },
            "properties": {
                "monitoring_enabled": True,
                "backup_enabled": False
            },
            "tags": {
                "Environment": "staging",
                "Team": "qa",
                "Purpose": "regression-testing"
            },
            "risk_score": 75,
            "risk_level": "high",
            "compliance_status": "compliant",
            "is_monitored": True,
            "health_status": "healthy"
        }
        
        create_response = client.post(
            "/api/v1/assets/",
            json=asset_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        asset = create_response.json()
        asset_id = asset["id"]
        
        # Verify all fields are correctly set
        assert asset["name"] == "regression-test-server"
        assert asset["asset_type"] == "server"
        assert asset["provider"] == "aws"
        assert asset["provider_id"] == "i-regression123"
        assert asset["ip_addresses"] == ["**********", "*************"]
        assert asset["dns_names"] == ["server.example.com"]
        assert asset["ports"] == [22, 80, 443]
        assert asset["protocols"] == ["SSH", "HTTP", "HTTPS"]
        assert asset["environment"] == "staging"
        assert asset["owner"] == "regression-team"
        assert asset["risk_score"] == 75
        assert asset["risk_level"] == "high"
        assert asset["compliance_status"] == "compliant"
        assert asset["is_monitored"] is True
        assert asset["health_status"] == "healthy"
        
        # 2. Update asset
        update_data = {
            "environment": "production",
            "risk_score": 85,
            "risk_level": "critical",
            "compliance_status": "non-compliant",
            "health_status": "warning",
            "configuration": {
                "instance_type": "t3.large",
                "ami_id": "ami-87654321",
                "security_groups": ["sg-87654321"]
            },
            "tags": {
                "Environment": "production",
                "Team": "qa",
                "Purpose": "regression-testing",
                "Updated": "true"
            }
        }
        
        update_response = client.put(
            f"/api/v1/assets/{asset_id}",
            json=update_data,
            headers=authenticated_headers
        )
        assert update_response.status_code == 200
        updated_asset = update_response.json()
        
        # Verify updates
        assert updated_asset["environment"] == "production"
        assert updated_asset["risk_score"] == 85
        assert updated_asset["risk_level"] == "critical"
        assert updated_asset["compliance_status"] == "non-compliant"
        assert updated_asset["health_status"] == "warning"
        assert updated_asset["tags"]["Updated"] == "true"
        
        # 3. Add tags
        tag_data = {
            "key": "Regression",
            "value": "test-suite",
            "source": "automated-test"
        }
        
        tag_response = client.post(
            f"/api/v1/assets/{asset_id}/tags",
            json=tag_data,
            headers=authenticated_headers
        )
        assert tag_response.status_code == 201
        tag = tag_response.json()
        assert tag["key"] == "Regression"
        assert tag["value"] == "test-suite"
        
        # 4. Get asset tags
        tags_response = client.get(
            f"/api/v1/assets/{asset_id}/tags",
            headers=authenticated_headers
        )
        assert tags_response.status_code == 200
        tags = tags_response.json()
        assert len(tags) == 1
        assert tags[0]["key"] == "Regression"
        
        # 5. Update last seen
        last_seen_response = client.patch(
            f"/api/v1/assets/{asset_id}/last-seen",
            headers=authenticated_headers
        )
        assert last_seen_response.status_code == 200
        
        # 6. Search for asset
        search_data = {
            "query": "regression-test-server",
            "page": 1,
            "size": 10
        }
        
        search_response = client.post(
            "/api/v1/assets/search",
            json=search_data,
            headers=authenticated_headers
        )
        assert search_response.status_code == 200
        search_results = search_response.json()
        assert search_results["total"] == 1
        assert search_results["assets"][0]["id"] == asset_id
        
        # 7. Delete asset
        delete_response = client.delete(
            f"/api/v1/assets/{asset_id}",
            headers=authenticated_headers
        )
        assert delete_response.status_code == 204
        
        # 8. Verify asset is deleted
        get_response = client.get(
            f"/api/v1/assets/{asset_id}",
            headers=authenticated_headers
        )
        assert get_response.status_code == 404

    def test_asset_relationship_workflow(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test complete asset relationship workflow."""
        # Create source asset (web server)
        web_server_data = {
            "name": "web-server-regression",
            "asset_type": "server",
            "provider": "aws",
            "provider_id": "i-web123",
            "discovery_source": "cloud_api",
            "environment": "production"
        }
        
        web_response = client.post(
            "/api/v1/assets/",
            json=web_server_data,
            headers=authenticated_headers
        )
        assert web_response.status_code == 201
        web_server_id = web_response.json()["id"]
        
        # Create target asset (database)
        db_data = {
            "name": "database-regression",
            "asset_type": "cloud_database",
            "provider": "aws",
            "provider_id": "db-regression123",
            "discovery_source": "cloud_api",
            "environment": "production"
        }
        
        db_response = client.post(
            "/api/v1/assets/",
            json=db_data,
            headers=authenticated_headers
        )
        assert db_response.status_code == 201
        db_id = db_response.json()["id"]
        
        # Create load balancer asset
        lb_data = {
            "name": "load-balancer-regression",
            "asset_type": "cloud_load_balancer",
            "provider": "aws",
            "provider_id": "lb-regression123",
            "discovery_source": "cloud_api",
            "environment": "production"
        }
        
        lb_response = client.post(
            "/api/v1/assets/",
            json=lb_data,
            headers=authenticated_headers
        )
        assert lb_response.status_code == 201
        lb_id = lb_response.json()["id"]
        
        # Create relationships
        relationships_data = [
            {
                "source_asset_id": lb_id,
                "target_asset_id": web_server_id,
                "relationship_type": "routes_to",
                "protocol": "HTTP",
                "port": 80,
                "direction": "outbound",
                "discovery_source": "cloud_api",
                "confidence_score": 0.95,
                "description": "Load balancer routes traffic to web server"
            },
            {
                "source_asset_id": web_server_id,
                "target_asset_id": db_id,
                "relationship_type": "depends_on",
                "protocol": "TCP",
                "port": 5432,
                "direction": "outbound",
                "discovery_source": "cloud_api",
                "confidence_score": 0.98,
                "description": "Web server depends on database"
            }
        ]
        
        relationship_ids = []
        for rel_data in relationships_data:
            rel_response = client.post(
                "/api/v1/assets/relationships",
                json=rel_data,
                headers=authenticated_headers
            )
            assert rel_response.status_code == 201
            relationship_ids.append(rel_response.json()["id"])
        
        # Get relationships for web server
        web_rels_response = client.get(
            f"/api/v1/assets/{web_server_id}/relationships",
            headers=authenticated_headers
        )
        assert web_rels_response.status_code == 200
        web_relationships = web_rels_response.json()
        assert len(web_relationships) == 2  # Both inbound and outbound
        
        # Get relationships by type
        depends_rels_response = client.get(
            f"/api/v1/assets/{web_server_id}/relationships?relationship_type=depends_on",
            headers=authenticated_headers
        )
        assert depends_rels_response.status_code == 200
        depends_relationships = depends_rels_response.json()
        assert len(depends_relationships) == 1
        assert depends_relationships[0]["relationship_type"] == "depends_on"
        
        # Update relationship
        update_rel_data = {
            "confidence_score": 0.99,
            "description": "Updated: Web server depends on database",
            "is_verified": True,
            "verification_method": "automated-test"
        }
        
        update_rel_response = client.put(
            f"/api/v1/assets/relationships/{relationship_ids[1]}",
            json=update_rel_data,
            headers=authenticated_headers
        )
        assert update_rel_response.status_code == 200
        updated_rel = update_rel_response.json()
        assert updated_rel["confidence_score"] == 0.99
        assert updated_rel["is_verified"] is True
        
        # Delete relationship
        delete_rel_response = client.delete(
            f"/api/v1/assets/relationships/{relationship_ids[0]}",
            headers=authenticated_headers
        )
        assert delete_rel_response.status_code == 204
        
        # Verify relationship is deleted
        final_rels_response = client.get(
            f"/api/v1/assets/{web_server_id}/relationships",
            headers=authenticated_headers
        )
        assert final_rels_response.status_code == 200
        final_relationships = final_rels_response.json()
        assert len(final_relationships) == 1  # Only one relationship left

    def test_discovery_job_workflow(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test complete discovery job workflow."""
        # 1. Create discovery job
        job_data = {
            "name": "Regression AWS Discovery",
            "description": "Regression test for AWS discovery",
            "job_type": "aws_discovery",
            "configuration": {
                "region": "us-east-1",
                "access_key_id": "AKIA...",
                "secret_access_key": "secret...",
                "include_stopped": False,
                "resource_types": ["ec2", "rds", "s3"]
            },
            "parameters": {
                "max_resources": 1000,
                "timeout": 3600,
                "parallel_workers": 5
            },
            "filters": {
                "tags": {"Environment": "production"},
                "regions": ["us-east-1", "us-west-2"]
            },
            "is_scheduled": True,
            "schedule_expression": "0 2 * * *"  # Daily at 2 AM
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job = create_response.json()
        job_id = job["id"]
        
        # Verify job creation
        assert job["name"] == "Regression AWS Discovery"
        assert job["job_type"] == "aws_discovery"
        assert job["status"] == "pending"
        assert job["is_scheduled"] is True
        assert job["schedule_expression"] == "0 2 * * *"
        
        # 2. Update job
        update_data = {
            "description": "Updated regression test for AWS discovery",
            "configuration": {
                "region": "us-west-2",
                "access_key_id": "AKIA...",
                "secret_access_key": "secret...",
                "include_stopped": True,
                "resource_types": ["ec2", "rds", "s3", "lambda"]
            }
        }
        
        update_response = client.put(
            f"/api/v1/discovery/jobs/{job_id}",
            json=update_data,
            headers=authenticated_headers
        )
        assert update_response.status_code == 200
        updated_job = update_response.json()
        assert updated_job["description"] == "Updated regression test for AWS discovery"
        assert updated_job["configuration"]["region"] == "us-west-2"
        assert updated_job["configuration"]["include_stopped"] is True
        
        # 3. Start job
        start_response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/start",
            headers=authenticated_headers
        )
        assert start_response.status_code == 200
        started_job = start_response.json()
        assert started_job["status"] == "running"
        assert started_job["started_at"] is not None
        
        # 4. Complete job with results
        completion_data = {
            "assets_discovered": 25,
            "assets_updated": 10,
            "relationships_discovered": 35,
            "errors_count": 2,
            "execution_log": [
                {"timestamp": "2024-01-01T10:00:00Z", "level": "INFO", "message": "Starting AWS discovery"},
                {"timestamp": "2024-01-01T10:05:00Z", "level": "INFO", "message": "Discovered 25 EC2 instances"},
                {"timestamp": "2024-01-01T10:10:00Z", "level": "WARNING", "message": "Failed to access 2 resources"},
                {"timestamp": "2024-01-01T10:15:00Z", "level": "INFO", "message": "Discovery completed"}
            ],
            "error_details": [
                {"resource": "i-failed123", "error": "Access denied", "code": "PERMISSION_DENIED"},
                {"resource": "db-failed456", "error": "Timeout", "code": "TIMEOUT"}
            ]
        }
        
        complete_response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/complete",
            json=completion_data,
            headers=authenticated_headers
        )
        assert complete_response.status_code == 200
        completed_job = complete_response.json()
        assert completed_job["status"] == "partial"  # Has errors
        assert completed_job["completed_at"] is not None
        assert completed_job["assets_discovered"] == 25
        assert completed_job["assets_updated"] == 10
        assert completed_job["relationships_discovered"] == 35
        assert completed_job["errors_count"] == 2
        assert len(completed_job["execution_log"]) == 4
        assert len(completed_job["error_details"]) == 2
        
        # 5. Get job details
        get_response = client.get(
            f"/api/v1/discovery/jobs/{job_id}",
            headers=authenticated_headers
        )
        assert get_response.status_code == 200
        final_job = get_response.json()
        assert final_job["status"] == "partial"
        assert final_job["duration_seconds"] is not None

    def test_asset_search_comprehensive(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test comprehensive asset search functionality."""
        # Create diverse test assets
        test_assets = [
            {
                "name": "prod-web-server-1",
                "asset_type": "server",
                "provider": "aws",
                "provider_id": "i-prod-web-1",
                "discovery_source": "cloud_api",
                "environment": "production",
                "owner": "web-team",
                "team": "frontend",
                "risk_level": "high",
                "tags": {"Tier": "web", "Critical": "true"}
            },
            {
                "name": "staging-db-server-1",
                "asset_type": "cloud_database",
                "provider": "azure",
                "provider_id": "db-staging-1",
                "discovery_source": "cloud_api",
                "environment": "staging",
                "owner": "db-team",
                "team": "backend",
                "risk_level": "medium",
                "tags": {"Tier": "database", "Critical": "false"}
            },
            {
                "name": "dev-api-endpoint-1",
                "asset_type": "api_endpoint",
                "provider": "on_premises",
                "discovery_source": "api_discovery",
                "environment": "development",
                "owner": "api-team",
                "team": "backend",
                "risk_level": "low",
                "tags": {"Tier": "api", "Public": "true"}
            }
        ]
        
        asset_ids = []
        for asset_data in test_assets:
            response = client.post(
                "/api/v1/assets/",
                json=asset_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
            asset_ids.append(response.json()["id"])
        
        # Test various search scenarios
        search_scenarios = [
            # Search by name
            {
                "query": "web-server",
                "expected_count": 1
            },
            # Search by provider
            {
                "providers": ["aws"],
                "expected_count": 1
            },
            # Search by asset type
            {
                "asset_types": ["server", "cloud_database"],
                "expected_count": 2
            },
            # Search by environment
            {
                "environments": ["production", "staging"],
                "expected_count": 2
            },
            # Search by owner
            {
                "owners": ["web-team"],
                "expected_count": 1
            },
            # Search by team
            {
                "teams": ["backend"],
                "expected_count": 2
            },
            # Search by risk level
            {
                "risk_levels": ["high", "medium"],
                "expected_count": 2
            },
            # Search by tags
            {
                "tags": {"Critical": "true"},
                "expected_count": 1
            },
            # Complex search
            {
                "query": "server",
                "providers": ["aws", "azure"],
                "environments": ["production", "staging"],
                "expected_count": 2
            }
        ]
        
        for i, scenario in enumerate(search_scenarios):
            expected_count = scenario.pop("expected_count")
            scenario.update({"page": 1, "size": 10})
            
            response = client.post(
                "/api/v1/assets/search",
                json=scenario,
                headers=authenticated_headers
            )
            assert response.status_code == 200, f"Search scenario {i} failed"
            
            results = response.json()
            assert results["total"] == expected_count, f"Search scenario {i}: expected {expected_count}, got {results['total']}"

    def test_asset_statistics_accuracy(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test asset statistics accuracy with known data."""
        # Create known test data
        test_data = {
            "aws_servers": 3,
            "azure_databases": 2,
            "gcp_functions": 1,
            "production_assets": 4,
            "staging_assets": 2,
            "high_risk_assets": 2,
            "medium_risk_assets": 3,
            "low_risk_assets": 1
        }
        
        # Create assets based on test data
        assets_to_create = [
            # AWS servers (3)
            {"name": "aws-server-1", "asset_type": "server", "provider": "aws", "environment": "production", "risk_level": "high"},
            {"name": "aws-server-2", "asset_type": "server", "provider": "aws", "environment": "production", "risk_level": "medium"},
            {"name": "aws-server-3", "asset_type": "server", "provider": "aws", "environment": "staging", "risk_level": "medium"},
            
            # Azure databases (2)
            {"name": "azure-db-1", "asset_type": "cloud_database", "provider": "azure", "environment": "production", "risk_level": "high"},
            {"name": "azure-db-2", "asset_type": "cloud_database", "provider": "azure", "environment": "staging", "risk_level": "medium"},
            
            # GCP function (1)
            {"name": "gcp-function-1", "asset_type": "cloud_function", "provider": "gcp", "environment": "production", "risk_level": "low"}
        ]
        
        for asset_data in assets_to_create:
            asset_data.update({"discovery_source": "cloud_api"})
            response = client.post(
                "/api/v1/assets/",
                json=asset_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
        
        # Get statistics
        stats_response = client.get(
            "/api/v1/assets/statistics",
            headers=authenticated_headers
        )
        assert stats_response.status_code == 200
        stats = stats_response.json()
        
        # Verify statistics accuracy
        assert stats["total_assets"] == 6
        assert stats["assets_by_type"]["server"] == test_data["aws_servers"]
        assert stats["assets_by_type"]["cloud_database"] == test_data["azure_databases"]
        assert stats["assets_by_type"]["cloud_function"] == test_data["gcp_functions"]
        assert stats["assets_by_provider"]["aws"] == test_data["aws_servers"]
        assert stats["assets_by_provider"]["azure"] == test_data["azure_databases"]
        assert stats["assets_by_provider"]["gcp"] == test_data["gcp_functions"]
        assert stats["assets_by_environment"]["production"] == test_data["production_assets"]
        assert stats["assets_by_environment"]["staging"] == test_data["staging_assets"]
        assert stats["assets_by_risk_level"]["high"] == test_data["high_risk_assets"]
        assert stats["assets_by_risk_level"]["medium"] == test_data["medium_risk_assets"]
        assert stats["assets_by_risk_level"]["low"] == test_data["low_risk_assets"]
