"""Tests for authentication API endpoints."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from app.db.models.auth import PasswordResetToken
from app.core.security import generate_password_reset_token


class TestAuthenticationEndpoints:
    """Test authentication API endpoints."""
    
    def test_login_success(self, client: <PERSON><PERSON><PERSON>, test_user):
        """Test successful login."""
        login_data = {
            "username": test_user.username,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "user" in data
        assert "tokens" in data
        assert "session_id" in data
        
        # Check user data
        user_data = data["user"]
        assert user_data["username"] == test_user.username
        assert user_data["email"] == test_user.email
        assert user_data["is_active"] is True
        
        # Check tokens
        tokens = data["tokens"]
        assert "access_token" in tokens
        assert "refresh_token" in tokens
        assert tokens["token_type"] == "bearer"
        assert tokens["expires_in"] > 0
    
    def test_login_invalid_username(self, client: TestClient):
        """Test login with invalid username."""
        login_data = {
            "username": "nonexistent",
            "password": "password",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_login_invalid_password(self, client: TestClient, test_user):
        """Test login with invalid password."""
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_login_inactive_user(self, client: TestClient, test_user, db_session):
        """Test login with inactive user."""
        # Deactivate user
        test_user.is_active = False
        db_session.commit()
        
        login_data = {
            "username": test_user.username,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_login_locked_user(self, client: TestClient, test_user, db_session):
        """Test login with locked user."""
        # Lock user account
        test_user.lock_account()
        db_session.commit()
        
        login_data = {
            "username": test_user.username,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_login_with_email(self, client: TestClient, test_user):
        """Test login using email instead of username."""
        login_data = {
            "username": test_user.email,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["user"]["username"] == test_user.username
    
    def test_login_validation_errors(self, client: TestClient):
        """Test login with validation errors."""
        # Missing password
        response = client.post("/api/v1/auth/login", json={
            "username": "testuser"
        })
        assert response.status_code == 422
        
        # Empty username
        response = client.post("/api/v1/auth/login", json={
            "username": "",
            "password": "password"
        })
        assert response.status_code == 422
    
    def test_refresh_token_success(self, client: TestClient, test_user):
        """Test successful token refresh."""
        # First login to get tokens
        login_data = {
            "username": test_user.username,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        login_response = client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        tokens = login_response.json()["tokens"]
        refresh_token = tokens["refresh_token"]
        
        # Refresh token
        refresh_data = {"refresh_token": refresh_token}
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        
        # New access token should be different
        assert data["access_token"] != tokens["access_token"]
    
    def test_refresh_token_invalid(self, client: TestClient):
        """Test token refresh with invalid token."""
        refresh_data = {"refresh_token": "invalid_token"}
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_logout_success(self, client: TestClient, authenticated_headers):
        """Test successful logout."""
        logout_data = {"all_sessions": False}
        response = client.post(
            "/api/v1/auth/logout", 
            json=logout_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_logout_all_sessions(self, client: TestClient, authenticated_headers):
        """Test logout from all sessions."""
        logout_data = {"all_sessions": True}
        response = client.post(
            "/api/v1/auth/logout", 
            json=logout_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_logout_unauthorized(self, client: TestClient):
        """Test logout without authentication."""
        logout_data = {"all_sessions": False}
        response = client.post("/api/v1/auth/logout", json=logout_data)
        
        assert response.status_code == 401
    
    def test_get_current_user_info(self, client: TestClient, authenticated_headers, test_user):
        """Test getting current user information."""
        response = client.get("/api/v1/auth/me", headers=authenticated_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
        assert data["full_name"] == test_user.full_name
        assert "roles" in data
        assert "permissions" in data
        assert data["is_active"] is True
    
    def test_get_current_user_info_unauthorized(self, client: TestClient):
        """Test getting current user info without authentication."""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    def test_get_user_sessions(self, client: TestClient, authenticated_headers):
        """Test getting user sessions."""
        response = client.get("/api/v1/auth/sessions", headers=authenticated_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "sessions" in data
        assert "total" in data
        assert isinstance(data["sessions"], list)
        assert data["total"] >= 0
    
    def test_password_reset_request(self, client: TestClient, test_user):
        """Test password reset request."""
        reset_data = {"email": test_user.email}
        response = client.post("/api/v1/auth/password/reset", json=reset_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_password_reset_request_nonexistent_email(self, client: TestClient):
        """Test password reset request with non-existent email."""
        reset_data = {"email": "<EMAIL>"}
        response = client.post("/api/v1/auth/password/reset", json=reset_data)
        
        # Should still return success to prevent email enumeration
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_password_reset_confirm_success(self, client: TestClient, test_user, db_session):
        """Test successful password reset confirmation."""
        # Create password reset token
        token = "test_reset_token"
        reset_token = PasswordResetToken.create_token(
            user_id=test_user.id,
            token_hash=token,
            expires_in_hours=24
        )
        db_session.add(reset_token)
        db_session.commit()
        
        reset_data = {
            "token": token,
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!"
        }
        
        response = client.post("/api/v1/auth/password/reset/confirm", json=reset_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Check token is marked as used
        db_session.refresh(reset_token)
        assert reset_token.is_used is True
    
    def test_password_reset_confirm_invalid_token(self, client: TestClient):
        """Test password reset confirmation with invalid token."""
        reset_data = {
            "token": "invalid_token",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!"
        }
        
        response = client.post("/api/v1/auth/password/reset/confirm", json=reset_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
    
    def test_password_reset_confirm_password_mismatch(self, client: TestClient):
        """Test password reset confirmation with password mismatch."""
        reset_data = {
            "token": "test_token",
            "new_password": "NewPassword123!",
            "confirm_password": "DifferentPassword123!"
        }
        
        response = client.post("/api/v1/auth/password/reset/confirm", json=reset_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_password_reset_confirm_weak_password(self, client: TestClient):
        """Test password reset confirmation with weak password."""
        reset_data = {
            "token": "test_token",
            "new_password": "weak",
            "confirm_password": "weak"
        }
        
        response = client.post("/api/v1/auth/password/reset/confirm", json=reset_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_change_password_success(self, client: TestClient, authenticated_headers, test_user, db_session):
        """Test successful password change."""
        password_data = {
            "current_password": "TestPassword123!",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!"
        }
        
        response = client.post(
            "/api/v1/auth/password/change", 
            json=password_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Verify password was changed
        db_session.refresh(test_user)
        assert test_user.check_password("NewPassword123!")
        assert not test_user.check_password("TestPassword123!")
    
    def test_change_password_wrong_current(self, client: TestClient, authenticated_headers):
        """Test password change with wrong current password."""
        password_data = {
            "current_password": "WrongPassword123!",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!"
        }
        
        response = client.post(
            "/api/v1/auth/password/change", 
            json=password_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "incorrect" in data["detail"].lower()
    
    def test_change_password_mismatch(self, client: TestClient, authenticated_headers):
        """Test password change with password mismatch."""
        password_data = {
            "current_password": "TestPassword123!",
            "new_password": "NewPassword123!",
            "confirm_password": "DifferentPassword123!"
        }
        
        response = client.post(
            "/api/v1/auth/password/change", 
            json=password_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_change_password_unauthorized(self, client: TestClient):
        """Test password change without authentication."""
        password_data = {
            "current_password": "TestPassword123!",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!"
        }
        
        response = client.post("/api/v1/auth/password/change", json=password_data)
        
        assert response.status_code == 401


class TestAuthenticationValidation:
    """Test authentication endpoint validation."""
    
    def test_login_request_validation(self, client: TestClient):
        """Test login request validation."""
        # Test various invalid inputs
        invalid_requests = [
            {},  # Missing fields
            {"username": ""},  # Empty username
            {"username": "test", "password": ""},  # Empty password
            {"password": "test"},  # Missing username
            {"username": "test"},  # Missing password
        ]
        
        for invalid_data in invalid_requests:
            response = client.post("/api/v1/auth/login", json=invalid_data)
            assert response.status_code == 422
    
    def test_password_strength_validation(self, client: TestClient):
        """Test password strength validation."""
        weak_passwords = [
            "short",  # Too short
            "nouppercase123!",  # No uppercase
            "NOLOWERCASE123!",  # No lowercase
            "NoNumbers!",  # No numbers
            "NoSpecialChars123",  # No special characters
        ]
        
        for weak_password in weak_passwords:
            reset_data = {
                "token": "test_token",
                "new_password": weak_password,
                "confirm_password": weak_password
            }
            
            response = client.post("/api/v1/auth/password/reset/confirm", json=reset_data)
            assert response.status_code == 422
    
    def test_email_validation(self, client: TestClient):
        """Test email validation in password reset."""
        invalid_emails = [
            "not-an-email",
            "@example.com",
            "test@",
            "test.example.com",
        ]
        
        for invalid_email in invalid_emails:
            reset_data = {"email": invalid_email}
            response = client.post("/api/v1/auth/password/reset", json=reset_data)
            assert response.status_code == 422


class TestAuthenticationSecurity:
    """Test authentication security features."""
    
    def test_login_attempt_logging(self, client: TestClient, test_user, db_session):
        """Test that login attempts are logged."""
        from app.db.models.auth import LoginAttempt
        
        # Count existing login attempts
        initial_count = db_session.query(LoginAttempt).count()
        
        # Make login attempt
        login_data = {
            "username": test_user.username,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        
        # Check login attempt was logged
        final_count = db_session.query(LoginAttempt).count()
        assert final_count > initial_count
        
        # Check login attempt details
        login_attempt = db_session.query(LoginAttempt).filter(
            LoginAttempt.username == test_user.username
        ).order_by(LoginAttempt.created_at.desc()).first()
        
        assert login_attempt is not None
        assert login_attempt.success is True
        assert login_attempt.user_id == test_user.id
    
    def test_failed_login_attempt_logging(self, client: TestClient, test_user, db_session):
        """Test that failed login attempts are logged."""
        from app.db.models.auth import LoginAttempt
        
        # Make failed login attempt
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
        
        # Check failed login attempt was logged
        login_attempt = db_session.query(LoginAttempt).filter(
            LoginAttempt.username == test_user.username
        ).order_by(LoginAttempt.created_at.desc()).first()
        
        assert login_attempt is not None
        assert login_attempt.success is False
        assert login_attempt.failure_reason == "invalid_password"
    
    def test_account_lockout_after_failed_attempts(self, client: TestClient, test_user, db_session):
        """Test account lockout after multiple failed attempts."""
        # Make multiple failed login attempts
        login_data = {
            "username": test_user.username,
            "password": "wrongpassword",
            "remember_me": False
        }
        
        # Make enough failed attempts to trigger lockout
        for _ in range(6):  # Assuming max is 5
            response = client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == 401
        
        # Check user is locked
        db_session.refresh(test_user)
        assert test_user.is_account_locked() is True
        
        # Try login with correct password - should still fail
        correct_login_data = {
            "username": test_user.username,
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=correct_login_data)
        assert response.status_code == 401
