"""Unit tests for discovery API endpoints."""

import json
import pytest
import uuid
from typing import Dict

from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from app.db.models.user import User


class TestDiscoveryAPI:
    """Test cases for Discovery API endpoints."""

    def test_create_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job creation."""
        job_data = {
            "name": "AWS Discovery",
            "description": "Discover AWS resources",
            "job_type": "aws_discovery",
            "configuration": {"region": "us-east-1"},
            "parameters": {"include_stopped": False}
        }
        
        response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "AWS Discovery"
        assert data["description"] == "Discover AWS resources"
        assert data["job_type"] == "aws_discovery"
        assert data["status"] == "pending"
        assert data["configuration"] == {"region": "us-east-1"}
        assert data["parameters"] == {"include_stopped": False}
        assert "id" in data
        assert "created_at" in data

    def test_create_discovery_job_invalid_data(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test discovery job creation with invalid data."""
        job_data = {
            "name": "",  # Invalid: empty name
            "job_type": "aws_discovery"
        }
        
        response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 422

    def test_get_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job retrieval."""
        # Create job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Get job
        response = client.get(
            f"/api/v1/discovery/jobs/{job_id}",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == job_id
        assert data["name"] == "AWS Discovery"

    def test_get_discovery_job_not_found(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test discovery job retrieval with non-existent ID."""
        non_existent_id = str(uuid.uuid4())
        
        response = client.get(
            f"/api/v1/discovery/jobs/{non_existent_id}",
            headers=authenticated_headers
        )
        
        assert response.status_code == 404

    def test_update_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job update."""
        # Create job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery",
            "description": "Initial description"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Update job
        update_data = {
            "description": "Updated description",
            "configuration": {"region": "us-west-2"}
        }
        
        response = client.put(
            f"/api/v1/discovery/jobs/{job_id}",
            json=update_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == "Updated description"
        assert data["configuration"] == {"region": "us-west-2"}

    def test_delete_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job deletion."""
        # Create job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Delete job
        response = client.delete(
            f"/api/v1/discovery/jobs/{job_id}",
            headers=authenticated_headers
        )
        
        assert response.status_code == 204
        
        # Verify job is deleted
        get_response = client.get(
            f"/api/v1/discovery/jobs/{job_id}",
            headers=authenticated_headers
        )
        assert get_response.status_code == 404

    def test_get_discovery_jobs_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery jobs listing."""
        # Create test jobs
        jobs_data = [
            {
                "name": "AWS Discovery",
                "job_type": "aws_discovery"
            },
            {
                "name": "Azure Discovery",
                "job_type": "azure_discovery"
            },
            {
                "name": "API Discovery",
                "job_type": "api_discovery"
            }
        ]
        
        for job_data in jobs_data:
            response = client.post(
                "/api/v1/discovery/jobs",
                json=job_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
        
        # Get jobs
        response = client.get(
            "/api/v1/discovery/jobs",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 3
        assert len(data["jobs"]) == 3
        assert data["page"] == 1
        assert data["size"] == 20

    def test_get_discovery_jobs_with_filters(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test discovery jobs listing with filters."""
        # Create test jobs
        jobs_data = [
            {
                "name": "AWS Discovery 1",
                "job_type": "aws_discovery"
            },
            {
                "name": "AWS Discovery 2",
                "job_type": "aws_discovery"
            },
            {
                "name": "Azure Discovery",
                "job_type": "azure_discovery"
            }
        ]
        
        for job_data in jobs_data:
            response = client.post(
                "/api/v1/discovery/jobs",
                json=job_data,
                headers=authenticated_headers
            )
            assert response.status_code == 201
        
        # Get AWS jobs only
        response = client.get(
            "/api/v1/discovery/jobs?job_type=aws_discovery",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 2
        assert len(data["jobs"]) == 2
        assert all(job["job_type"] == "aws_discovery" for job in data["jobs"])

    def test_start_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job start."""
        # Create job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Start job
        response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/start",
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "running"
        assert data["started_at"] is not None
        assert data["executor"] is not None

    def test_start_discovery_job_invalid_status(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test starting discovery job with invalid status."""
        # Create and start job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Start job
        start_response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/start",
            headers=authenticated_headers
        )
        assert start_response.status_code == 200
        
        # Try to start again (should fail)
        response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/start",
            headers=authenticated_headers
        )
        
        assert response.status_code == 400

    def test_complete_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job completion."""
        # Create and start job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Start job
        start_response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/start",
            headers=authenticated_headers
        )
        assert start_response.status_code == 200
        
        # Complete job
        completion_data = {
            "assets_discovered": 10,
            "assets_updated": 5,
            "relationships_discovered": 15,
            "errors_count": 0,
            "execution_log": [{"message": "Discovery completed successfully"}]
        }
        
        response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/complete",
            json=completion_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert data["completed_at"] is not None
        assert data["assets_discovered"] == 10
        assert data["assets_updated"] == 5
        assert data["relationships_discovered"] == 15
        assert data["errors_count"] == 0

    def test_fail_discovery_job_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful discovery job failure marking."""
        # Create and start job first
        job_data = {
            "name": "AWS Discovery",
            "job_type": "aws_discovery"
        }
        
        create_response = client.post(
            "/api/v1/discovery/jobs",
            json=job_data,
            headers=authenticated_headers
        )
        assert create_response.status_code == 201
        job_id = create_response.json()["id"]
        
        # Start job
        start_response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/start",
            headers=authenticated_headers
        )
        assert start_response.status_code == 200
        
        # Fail job
        failure_data = {
            "error_details": [{"error": "Connection timeout", "code": "TIMEOUT"}]
        }
        
        response = client.post(
            f"/api/v1/discovery/jobs/{job_id}/fail",
            json=failure_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "failed"
        assert data["completed_at"] is not None
        assert data["error_details"] == [{"error": "Connection timeout", "code": "TIMEOUT"}]

    def test_start_aws_discovery_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful AWS discovery job creation."""
        discovery_config = {
            "region": "us-east-1",
            "access_key_id": "AKIA...",
            "secret_access_key": "secret...",
            "include_stopped": False
        }
        
        response = client.post(
            "/api/v1/discovery/cloud/aws",
            json=discovery_config,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "AWS Discovery" in data["name"]
        assert data["job_type"] == "aws_discovery"
        assert data["configuration"] == discovery_config

    def test_start_azure_discovery_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful Azure discovery job creation."""
        discovery_config = {
            "subscription_id": "12345678-1234-1234-1234-123456789012",
            "client_id": "client_id",
            "client_secret": "client_secret",
            "tenant_id": "tenant_id"
        }
        
        response = client.post(
            "/api/v1/discovery/cloud/azure",
            json=discovery_config,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "Azure Discovery" in data["name"]
        assert data["job_type"] == "azure_discovery"
        assert data["configuration"] == discovery_config

    def test_start_gcp_discovery_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful GCP discovery job creation."""
        discovery_config = {
            "project_id": "my-gcp-project",
            "service_account_key": "path/to/key.json",
            "regions": ["us-central1", "us-east1"]
        }
        
        response = client.post(
            "/api/v1/discovery/cloud/gcp",
            json=discovery_config,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "GCP Discovery" in data["name"]
        assert data["job_type"] == "gcp_discovery"
        assert data["configuration"] == discovery_config

    def test_start_akto_discovery_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful Akto API discovery job creation."""
        discovery_config = {
            "target": "https://api.example.com",
            "api_key": "akto_api_key",
            "collection_id": "123",
            "scan_depth": "deep"
        }
        
        response = client.post(
            "/api/v1/discovery/api/akto",
            json=discovery_config,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "Akto API Discovery" in data["name"]
        assert data["job_type"] == "akto_discovery"
        assert data["configuration"] == discovery_config

    def test_start_kiterunner_discovery_success(self, client: TestClient, authenticated_headers: Dict[str, str]):
        """Test successful Kiterunner API discovery job creation."""
        discovery_config = {
            "target": "https://api.example.com",
            "wordlist": "common-api-endpoints",
            "threads": 10,
            "delay": 100
        }
        
        response = client.post(
            "/api/v1/discovery/api/kiterunner",
            json=discovery_config,
            headers=authenticated_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "Kiterunner API Discovery" in data["name"]
        assert data["job_type"] == "kiterunner_discovery"
        assert data["configuration"] == discovery_config

    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to discovery endpoints."""
        response = client.get("/api/v1/discovery/jobs")
        assert response.status_code == 401
