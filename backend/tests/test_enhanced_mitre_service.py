"""Integration tests for Enhanced MITRE ATT&CK service."""

import json
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from datetime import datetime, timedelta
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.enhanced_mitre_service import EnhancedMitreService
from app.db.models.mitre import (
    MitreTechnique,
    MitreTactic,
    MitreGroup,
    MitreDataSync,
    MitreDomain,
    MitreEntityStatus,
)


class TestEnhancedMitreService:
    """Test Enhanced MITRE service functionality."""
    
    @pytest.fixture
    async def mitre_service(self, async_db: AsyncSession):
        """Create MITRE service instance."""
        return EnhancedMitreService(db=async_db)
    
    @pytest.fixture
    def mock_stix_data(self):
        """Mock STIX data for testing."""
        return {
            "type": "bundle",
            "id": "bundle--test-123",
            "objects": [
                {
                    "type": "attack-pattern",
                    "id": "attack-pattern--test-123",
                    "name": "Test Technique",
                    "description": "Test technique description",
                    "external_references": [
                        {
                            "source_name": "mitre-attack",
                            "external_id": "T1234",
                            "url": "https://attack.mitre.org/techniques/T1234"
                        }
                    ],
                    "x_mitre_platforms": ["Windows", "Linux"],
                    "x_mitre_data_sources": ["Process monitoring"],
                    "kill_chain_phases": [
                        {
                            "kill_chain_name": "mitre-attack",
                            "phase_name": "initial-access"
                        }
                    ]
                },
                {
                    "type": "x-mitre-tactic",
                    "id": "x-mitre-tactic--test-456",
                    "name": "Initial Access",
                    "description": "Initial access tactic",
                    "external_references": [
                        {
                            "source_name": "mitre-attack",
                            "external_id": "TA0001",
                            "url": "https://attack.mitre.org/tactics/TA0001"
                        }
                    ],
                    "x_mitre_shortname": "initial-access"
                },
                {
                    "type": "intrusion-set",
                    "id": "intrusion-set--test-789",
                    "name": "APT1",
                    "description": "Advanced Persistent Threat 1",
                    "aliases": ["Comment Crew"],
                    "external_references": [
                        {
                            "source_name": "mitre-attack",
                            "external_id": "G0001",
                            "url": "https://attack.mitre.org/groups/G0001"
                        }
                    ]
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_sync_domain_data_success(self, mitre_service, mock_stix_data):
        """Test successful domain data synchronization."""
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = mock_stix_data
            
            result = await mitre_service.sync_domain_data(
                domain=MitreDomain.ENTERPRISE,
                force_update=True,
                triggered_by="test_user"
            )
            
            assert result.domain == MitreDomain.ENTERPRISE
            assert result.status == "completed"
            assert result.triggered_by == "test_user"
            assert result.sync_type == "manual"
            assert result.processed_entities > 0
            assert result.failed_entities == 0
    
    @pytest.mark.asyncio
    async def test_sync_domain_data_current(self, mitre_service, async_db):
        """Test skipping sync when data is current."""
        # Create a recent successful sync
        recent_sync = MitreDataSync(
            sync_id="recent_sync",
            domain=MitreDomain.ENTERPRISE,
            sync_type="automatic",
            status="completed",
            completed_at=datetime.utcnow() - timedelta(hours=1),
            processed_entities=100
        )
        async_db.add(recent_sync)
        await async_db.commit()
        
        result = await mitre_service.sync_domain_data(
            domain=MitreDomain.ENTERPRISE,
            force_update=False
        )
        
        assert result.status == "completed"
        assert "current" in result.sync_results["message"]
    
    @pytest.mark.asyncio
    async def test_sync_domain_data_failure(self, mitre_service):
        """Test sync failure handling."""
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.side_effect = Exception("Download failed")
            
            with pytest.raises(Exception, match="Download failed"):
                await mitre_service.sync_domain_data(
                    domain=MitreDomain.ENTERPRISE,
                    force_update=True
                )
            
            # Check that sync record shows failure
            result = await mitre_service.db.execute(
                select(MitreDataSync).order_by(MitreDataSync.started_at.desc()).limit(1)
            )
            sync_record = result.scalar_one()
            
            assert sync_record.status == "failed"
            assert sync_record.error_message == "Download failed"
    
    @pytest.mark.asyncio
    async def test_process_technique(self, mitre_service, async_db):
        """Test processing STIX technique object."""
        stix_technique = MagicMock()
        stix_technique.id = "attack-pattern--test-123"
        stix_technique.name = "Test Technique"
        stix_technique.description = "Test description"
        stix_technique.external_references = [
            {
                "source_name": "mitre-attack",
                "external_id": "T1234",
                "url": "https://attack.mitre.org/techniques/T1234"
            }
        ]
        stix_technique.x_mitre_platforms = ["Windows", "Linux"]
        stix_technique.x_mitre_data_sources = ["Process monitoring"]
        stix_technique.kill_chain_phases = [
            {
                "kill_chain_name": "mitre-attack",
                "phase_name": "initial-access"
            }
        ]
        
        await mitre_service._process_technique(stix_technique, MitreDomain.ENTERPRISE)
        await async_db.commit()
        
        # Verify technique was created
        result = await async_db.execute(
            select(MitreTechnique).where(MitreTechnique.technique_id == "T1234")
        )
        technique = result.scalar_one()
        
        assert technique.name == "Test Technique"
        assert technique.description == "Test description"
        assert technique.domain == MitreDomain.ENTERPRISE
        assert technique.platforms == ["Windows", "Linux"]
        assert technique.data_sources == ["Process monitoring"]
        assert technique.is_subtechnique is False
    
    @pytest.mark.asyncio
    async def test_process_subtechnique(self, mitre_service, async_db):
        """Test processing STIX sub-technique object."""
        stix_subtechnique = MagicMock()
        stix_subtechnique.id = "attack-pattern--test-456"
        stix_subtechnique.name = "Test Sub-technique"
        stix_subtechnique.description = "Test sub-technique description"
        stix_subtechnique.external_references = [
            {
                "source_name": "mitre-attack",
                "external_id": "T1234.001",
                "url": "https://attack.mitre.org/techniques/T1234/001"
            }
        ]
        stix_subtechnique.x_mitre_platforms = ["Windows"]
        
        await mitre_service._process_technique(stix_subtechnique, MitreDomain.ENTERPRISE)
        await async_db.commit()
        
        # Verify sub-technique was created
        result = await async_db.execute(
            select(MitreTechnique).where(MitreTechnique.technique_id == "T1234.001")
        )
        subtechnique = result.scalar_one()
        
        assert subtechnique.name == "Test Sub-technique"
        assert subtechnique.is_subtechnique is True
        assert subtechnique.parent_technique_id == "T1234"
    
    @pytest.mark.asyncio
    async def test_process_tactic(self, mitre_service, async_db):
        """Test processing STIX tactic object."""
        stix_tactic = MagicMock()
        stix_tactic.id = "x-mitre-tactic--test-123"
        stix_tactic.name = "Initial Access"
        stix_tactic.description = "Initial access tactic"
        stix_tactic.external_references = [
            {
                "source_name": "mitre-attack",
                "external_id": "TA0001",
                "url": "https://attack.mitre.org/tactics/TA0001"
            }
        ]
        stix_tactic.x_mitre_shortname = "initial-access"
        
        await mitre_service._process_tactic(stix_tactic, MitreDomain.ENTERPRISE)
        await async_db.commit()
        
        # Verify tactic was created
        result = await async_db.execute(
            select(MitreTactic).where(MitreTactic.tactic_id == "TA0001")
        )
        tactic = result.scalar_one()
        
        assert tactic.name == "Initial Access"
        assert tactic.description == "Initial access tactic"
        assert tactic.domain == MitreDomain.ENTERPRISE
        assert tactic.short_name == "initial-access"
    
    @pytest.mark.asyncio
    async def test_process_group(self, mitre_service, async_db):
        """Test processing STIX group object."""
        stix_group = MagicMock()
        stix_group.id = "intrusion-set--test-123"
        stix_group.name = "APT1"
        stix_group.description = "Advanced Persistent Threat 1"
        stix_group.aliases = ["Comment Crew", "PLA Unit 61398"]
        stix_group.external_references = [
            {
                "source_name": "mitre-attack",
                "external_id": "G0001",
                "url": "https://attack.mitre.org/groups/G0001"
            }
        ]
        
        await mitre_service._process_group(stix_group, MitreDomain.ENTERPRISE)
        await async_db.commit()
        
        # Verify group was created
        result = await async_db.execute(
            select(MitreGroup).where(MitreGroup.group_id == "G0001")
        )
        group = result.scalar_one()
        
        assert group.name == "APT1"
        assert group.description == "Advanced Persistent Threat 1"
        assert group.domain == MitreDomain.ENTERPRISE
        assert group.aliases == ["Comment Crew", "PLA Unit 61398"]
    
    @pytest.mark.asyncio
    async def test_update_existing_technique(self, mitre_service, async_db):
        """Test updating existing technique."""
        # Create existing technique
        existing_technique = MitreTechnique(
            technique_id="T1234",
            name="Old Name",
            description="Old description",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(existing_technique)
        await async_db.commit()
        
        # Process updated technique
        stix_technique = MagicMock()
        stix_technique.id = "attack-pattern--test-123"
        stix_technique.name = "Updated Name"
        stix_technique.description = "Updated description"
        stix_technique.external_references = [
            {
                "source_name": "mitre-attack",
                "external_id": "T1234",
                "url": "https://attack.mitre.org/techniques/T1234"
            }
        ]
        stix_technique.x_mitre_platforms = ["Windows", "Linux", "macOS"]
        
        await mitre_service._process_technique(stix_technique, MitreDomain.ENTERPRISE)
        await async_db.commit()
        
        # Verify technique was updated
        result = await async_db.execute(
            select(MitreTechnique).where(MitreTechnique.technique_id == "T1234")
        )
        technique = result.scalar_one()
        
        assert technique.name == "Updated Name"
        assert technique.description == "Updated description"
        assert technique.platforms == ["Windows", "Linux", "macOS"]
    
    @pytest.mark.asyncio
    async def test_get_sync_status(self, mitre_service, async_db):
        """Test getting sync status."""
        # Create sync record
        sync = MitreDataSync(
            sync_id="test_sync_123",
            domain=MitreDomain.ENTERPRISE,
            sync_type="manual",
            status="completed",
            processed_entities=100,
            failed_entities=5
        )
        async_db.add(sync)
        await async_db.commit()
        
        # Get sync status
        result = await mitre_service.get_sync_status("test_sync_123")
        
        assert result is not None
        assert result.sync_id == "test_sync_123"
        assert result.domain == MitreDomain.ENTERPRISE
        assert result.status == "completed"
        assert result.processed_entities == 100
        assert result.failed_entities == 5
    
    @pytest.mark.asyncio
    async def test_get_sync_status_not_found(self, mitre_service):
        """Test getting sync status for non-existent sync."""
        result = await mitre_service.get_sync_status("nonexistent_sync")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_list_sync_history(self, mitre_service, async_db):
        """Test listing sync history."""
        # Create multiple sync records
        syncs = [
            MitreDataSync(
                sync_id=f"sync_{i}",
                domain=MitreDomain.ENTERPRISE,
                sync_type="automatic",
                status="completed",
                started_at=datetime.utcnow() - timedelta(hours=i)
            )
            for i in range(5)
        ]
        
        for sync in syncs:
            async_db.add(sync)
        await async_db.commit()
        
        # List sync history
        results = await mitre_service.list_sync_history(
            domain=MitreDomain.ENTERPRISE,
            limit=3
        )
        
        assert len(results) == 3
        # Should be ordered by started_at desc (most recent first)
        assert results[0].sync_id == "sync_0"
        assert results[1].sync_id == "sync_1"
        assert results[2].sync_id == "sync_2"
    
    @pytest.mark.asyncio
    async def test_list_sync_history_all_domains(self, mitre_service, async_db):
        """Test listing sync history for all domains."""
        # Create sync records for different domains
        syncs = [
            MitreDataSync(
                sync_id="enterprise_sync",
                domain=MitreDomain.ENTERPRISE,
                sync_type="automatic",
                status="completed"
            ),
            MitreDataSync(
                sync_id="mobile_sync",
                domain=MitreDomain.MOBILE,
                sync_type="manual",
                status="completed"
            ),
            MitreDataSync(
                sync_id="ics_sync",
                domain=MitreDomain.ICS,
                sync_type="automatic",
                status="failed"
            )
        ]
        
        for sync in syncs:
            async_db.add(sync)
        await async_db.commit()
        
        # List all sync history
        results = await mitre_service.list_sync_history()
        
        assert len(results) == 3
        domains = {result.domain for result in results}
        assert domains == {MitreDomain.ENTERPRISE, MitreDomain.MOBILE, MitreDomain.ICS}
    
    @pytest.mark.asyncio
    async def test_is_data_current_true(self, mitre_service, async_db):
        """Test data currency check when data is current."""
        # Create recent sync
        recent_sync = MitreDataSync(
            sync_id="recent_sync",
            domain=MitreDomain.ENTERPRISE,
            sync_type="automatic",
            status="completed",
            completed_at=datetime.utcnow() - timedelta(hours=1)
        )
        async_db.add(recent_sync)
        await async_db.commit()
        
        is_current = await mitre_service._is_data_current(MitreDomain.ENTERPRISE)
        assert is_current is True
    
    @pytest.mark.asyncio
    async def test_is_data_current_false(self, mitre_service, async_db):
        """Test data currency check when data is old."""
        # Create old sync
        old_sync = MitreDataSync(
            sync_id="old_sync",
            domain=MitreDomain.ENTERPRISE,
            sync_type="automatic",
            status="completed",
            completed_at=datetime.utcnow() - timedelta(hours=25)
        )
        async_db.add(old_sync)
        await async_db.commit()
        
        is_current = await mitre_service._is_data_current(MitreDomain.ENTERPRISE)
        assert is_current is False
    
    @pytest.mark.asyncio
    async def test_is_data_current_no_sync(self, mitre_service):
        """Test data currency check when no sync exists."""
        is_current = await mitre_service._is_data_current(MitreDomain.ENTERPRISE)
        assert is_current is False
