"""Tests for user management API endpoints."""

import pytest
import uuid
from fastapi.testclient import TestClient


class TestUserManagementEndpoints:
    """Test user management API endpoints."""
    
    def test_create_user_success(self, client: TestClient, admin_headers, system_roles):
        """Test successful user creation."""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "full_name": "New User",
            "password": "NewPassword123!",
            "phone_number": "+1234567890",
            "department": "Security",
            "job_title": "Analyst",
            "roles": ["analyst"],
            "is_active": True,
            "require_password_change": False
        }
        
        response = client.post(
            "/api/v1/users/", 
            json=user_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert data["full_name"] == user_data["full_name"]
        assert data["is_active"] == user_data["is_active"]
        assert "analyst" in data["roles"]
        
        # Password should not be returned
        assert "password" not in data
    
    def test_create_user_duplicate_username(self, client: TestClient, admin_headers, test_user):
        """Test user creation with duplicate username."""
        user_data = {
            "username": test_user.username,
            "email": "<EMAIL>",
            "full_name": "Different User",
            "password": "Password123!",
            "roles": ["analyst"]
        }
        
        response = client.post(
            "/api/v1/users/", 
            json=user_data,
            headers=admin_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "already exists" in data["detail"].lower()
    
    def test_create_user_duplicate_email(self, client: TestClient, admin_headers, test_user):
        """Test user creation with duplicate email."""
        user_data = {
            "username": "differentuser",
            "email": test_user.email,
            "full_name": "Different User",
            "password": "Password123!",
            "roles": ["analyst"]
        }
        
        response = client.post(
            "/api/v1/users/", 
            json=user_data,
            headers=admin_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "already exists" in data["detail"].lower()
    
    def test_create_user_unauthorized(self, client: TestClient, authenticated_headers):
        """Test user creation without admin permissions."""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "full_name": "New User",
            "password": "Password123!",
            "roles": ["analyst"]
        }
        
        response = client.post(
            "/api/v1/users/", 
            json=user_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 403
    
    def test_create_user_validation_errors(self, client: TestClient, admin_headers):
        """Test user creation with validation errors."""
        # Test various invalid inputs
        invalid_requests = [
            {
                "username": "",  # Empty username
                "email": "<EMAIL>",
                "full_name": "Test",
                "password": "Password123!",
                "roles": ["analyst"]
            },
            {
                "username": "test",
                "email": "invalid-email",  # Invalid email
                "full_name": "Test",
                "password": "Password123!",
                "roles": ["analyst"]
            },
            {
                "username": "test",
                "email": "<EMAIL>",
                "full_name": "Test",
                "password": "weak",  # Weak password
                "roles": ["analyst"]
            },
            {
                "username": "test",
                "email": "<EMAIL>",
                "full_name": "Test",
                "password": "Password123!",
                "roles": ["invalid_role"]  # Invalid role
            }
        ]
        
        for invalid_data in invalid_requests:
            response = client.post(
                "/api/v1/users/", 
                json=invalid_data,
                headers=admin_headers
            )
            assert response.status_code == 422
    
    def test_list_users_success(self, client: TestClient, authenticated_headers, test_user, admin_user):
        """Test successful user listing."""
        response = client.get("/api/v1/users/", headers=authenticated_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "users" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        
        assert isinstance(data["users"], list)
        assert data["total"] >= 2
        assert len(data["users"]) >= 2
        
        # Check that both test users are in the list
        user_ids = [user["id"] for user in data["users"]]
        assert str(test_user.id) in user_ids
        assert str(admin_user.id) in user_ids
    
    def test_list_users_pagination(self, client: TestClient, authenticated_headers):
        """Test user listing with pagination."""
        # Get first page with size 1
        response = client.get(
            "/api/v1/users/?page=1&size=1", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["users"]) == 1
        assert data["page"] == 1
        assert data["size"] == 1
        
        # Get second page
        response = client.get(
            "/api/v1/users/?page=2&size=1", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data2 = response.json()
        
        assert len(data2["users"]) <= 1
        assert data2["page"] == 2
        
        # Users should be different
        if len(data2["users"]) == 1:
            assert data["users"][0]["id"] != data2["users"][0]["id"]
    
    def test_list_users_search(self, client: TestClient, authenticated_headers, test_user):
        """Test user listing with search."""
        search_term = test_user.username[:4]
        response = client.get(
            f"/api/v1/users/?search={search_term}", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should find at least the test user
        user_usernames = [user["username"] for user in data["users"]]
        assert test_user.username in user_usernames
    
    def test_list_users_role_filter(self, client: TestClient, authenticated_headers, admin_user):
        """Test user listing with role filter."""
        response = client.get(
            "/api/v1/users/?role_filter=admin", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should find admin user
        user_ids = [user["id"] for user in data["users"]]
        assert str(admin_user.id) in user_ids
    
    def test_list_users_unauthorized(self, client: TestClient):
        """Test user listing without authentication."""
        response = client.get("/api/v1/users/")
        
        assert response.status_code == 401
    
    def test_get_user_success(self, client: TestClient, authenticated_headers, test_user):
        """Test successful user retrieval."""
        response = client.get(
            f"/api/v1/users/{test_user.id}", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == str(test_user.id)
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
        assert data["full_name"] == test_user.full_name
        assert "roles" in data
        assert "permissions" in data
    
    def test_get_user_not_found(self, client: TestClient, authenticated_headers):
        """Test getting non-existent user."""
        non_existent_id = str(uuid.uuid4())
        response = client.get(
            f"/api/v1/users/{non_existent_id}", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()
    
    def test_get_user_invalid_id(self, client: TestClient, authenticated_headers):
        """Test getting user with invalid ID format."""
        response = client.get(
            "/api/v1/users/invalid-id", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "invalid" in data["detail"].lower()
    
    def test_get_user_unauthorized(self, client: TestClient, test_user):
        """Test getting user without authentication."""
        response = client.get(f"/api/v1/users/{test_user.id}")
        
        assert response.status_code == 401
    
    def test_update_user_success(self, client: TestClient, admin_headers, test_user):
        """Test successful user update."""
        update_data = {
            "full_name": "Updated Name",
            "phone_number": "+9876543210",
            "department": "Updated Department",
            "job_title": "Senior Analyst"
        }
        
        response = client.put(
            f"/api/v1/users/{test_user.id}", 
            json=update_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["full_name"] == update_data["full_name"]
        assert data["phone_number"] == update_data["phone_number"]
        assert data["department"] == update_data["department"]
        assert data["job_title"] == update_data["job_title"]
        
        # Unchanged fields should remain the same
        assert data["username"] == test_user.username
        assert data["email"] == test_user.email
    
    def test_update_user_not_found(self, client: TestClient, admin_headers):
        """Test updating non-existent user."""
        non_existent_id = str(uuid.uuid4())
        update_data = {"full_name": "New Name"}
        
        response = client.put(
            f"/api/v1/users/{non_existent_id}", 
            json=update_data,
            headers=admin_headers
        )
        
        assert response.status_code == 404
    
    def test_update_user_duplicate_email(self, client: TestClient, admin_headers, test_user, admin_user):
        """Test updating user with duplicate email."""
        update_data = {"email": admin_user.email}
        
        response = client.put(
            f"/api/v1/users/{test_user.id}", 
            json=update_data,
            headers=admin_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "already exists" in data["detail"].lower()
    
    def test_update_user_unauthorized(self, client: TestClient, authenticated_headers, test_user):
        """Test updating user without admin permissions."""
        update_data = {"full_name": "New Name"}
        
        response = client.put(
            f"/api/v1/users/{test_user.id}", 
            json=update_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 403
    
    def test_delete_user_success(self, client: TestClient, admin_headers, test_user):
        """Test successful user deletion."""
        response = client.delete(
            f"/api/v1/users/{test_user.id}", 
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # User should no longer be found
        get_response = client.get(
            f"/api/v1/users/{test_user.id}", 
            headers=admin_headers
        )
        assert get_response.status_code == 404
    
    def test_delete_user_self_deletion_prevented(self, client: TestClient, admin_headers, admin_user):
        """Test that users cannot delete themselves."""
        response = client.delete(
            f"/api/v1/users/{admin_user.id}", 
            headers=admin_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "cannot delete your own account" in data["detail"].lower()
    
    def test_delete_user_not_found(self, client: TestClient, admin_headers):
        """Test deleting non-existent user."""
        non_existent_id = str(uuid.uuid4())
        
        response = client.delete(
            f"/api/v1/users/{non_existent_id}", 
            headers=admin_headers
        )
        
        assert response.status_code == 404
    
    def test_delete_user_unauthorized(self, client: TestClient, authenticated_headers, test_user):
        """Test deleting user without admin permissions."""
        response = client.delete(
            f"/api/v1/users/{test_user.id}", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 403
    
    def test_assign_user_roles_success(self, client: TestClient, admin_headers, test_user):
        """Test successful role assignment."""
        role_data = {
            "user_id": str(test_user.id),
            "roles": ["soc_operator", "analyst"]
        }
        
        response = client.post(
            f"/api/v1/users/{test_user.id}/roles", 
            json=role_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Verify roles were assigned
        get_response = client.get(
            f"/api/v1/users/{test_user.id}", 
            headers=admin_headers
        )
        user_data = get_response.json()
        assert "soc_operator" in user_data["roles"]
        assert "analyst" in user_data["roles"]
    
    def test_assign_user_roles_not_found(self, client: TestClient, admin_headers):
        """Test role assignment for non-existent user."""
        non_existent_id = str(uuid.uuid4())
        role_data = {
            "user_id": non_existent_id,
            "roles": ["analyst"]
        }
        
        response = client.post(
            f"/api/v1/users/{non_existent_id}/roles", 
            json=role_data,
            headers=admin_headers
        )
        
        assert response.status_code == 404
    
    def test_assign_user_roles_unauthorized(self, client: TestClient, authenticated_headers, test_user):
        """Test role assignment without admin permissions."""
        role_data = {
            "user_id": str(test_user.id),
            "roles": ["analyst"]
        }
        
        response = client.post(
            f"/api/v1/users/{test_user.id}/roles", 
            json=role_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 403


class TestAPIKeyManagement:
    """Test API key management endpoints."""
    
    def test_create_api_key_success(self, client: TestClient, admin_headers, test_user):
        """Test successful API key creation."""
        api_key_data = {
            "name": "Test API Key",
            "description": "Test description",
            "scopes": ["read_assets", "create_incident"],
            "allowed_ips": ["*************"],
            "rate_limit": 1000
        }
        
        response = client.post(
            f"/api/v1/users/{test_user.id}/api-keys", 
            json=api_key_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "api_key" in data
        assert "secret_key" in data
        
        api_key = data["api_key"]
        assert api_key["name"] == api_key_data["name"]
        assert api_key["description"] == api_key_data["description"]
        assert api_key["scopes"] == api_key_data["scopes"]
        assert api_key["is_active"] is True
        
        secret_key = data["secret_key"]
        assert secret_key.startswith("br_")
        assert len(secret_key) > 10
    
    def test_create_api_key_user_not_found(self, client: TestClient, admin_headers):
        """Test API key creation for non-existent user."""
        non_existent_id = str(uuid.uuid4())
        api_key_data = {"name": "Test Key"}
        
        response = client.post(
            f"/api/v1/users/{non_existent_id}/api-keys", 
            json=api_key_data,
            headers=admin_headers
        )
        
        assert response.status_code == 404
    
    def test_create_api_key_unauthorized(self, client: TestClient, authenticated_headers, test_user):
        """Test API key creation without admin permissions."""
        api_key_data = {"name": "Test Key"}
        
        response = client.post(
            f"/api/v1/users/{test_user.id}/api-keys", 
            json=api_key_data,
            headers=authenticated_headers
        )
        
        assert response.status_code == 403
    
    def test_revoke_api_key_success(self, client: TestClient, admin_headers, test_user, db_session):
        """Test successful API key revocation."""
        # First create an API key
        from app.services.user_service import UserService
        user_service = UserService(db_session)
        api_key, _ = user_service.create_api_key(
            user_id=test_user.id,
            name="Test Key"
        )
        
        response = client.delete(
            f"/api/v1/users/api-keys/{api_key.id}", 
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        
        # Verify API key is revoked
        db_session.refresh(api_key)
        assert api_key.is_active is False
    
    def test_revoke_api_key_not_found(self, client: TestClient, admin_headers):
        """Test revoking non-existent API key."""
        non_existent_id = str(uuid.uuid4())
        
        response = client.delete(
            f"/api/v1/users/api-keys/{non_existent_id}", 
            headers=admin_headers
        )
        
        assert response.status_code == 404
    
    def test_revoke_api_key_unauthorized(self, client: TestClient, authenticated_headers):
        """Test API key revocation without admin permissions."""
        api_key_id = str(uuid.uuid4())
        
        response = client.delete(
            f"/api/v1/users/api-keys/{api_key_id}", 
            headers=authenticated_headers
        )
        
        assert response.status_code == 403


class TestUserStatistics:
    """Test user statistics endpoints."""
    
    def test_get_user_statistics_success(self, client: TestClient, admin_headers):
        """Test successful user statistics retrieval."""
        response = client.get("/api/v1/users/statistics", headers=admin_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "total_users" in data
        assert "active_users" in data
        assert "locked_users" in data
        assert "inactive_users" in data
        assert "recent_logins_24h" in data
        assert "users_by_role" in data
        
        assert isinstance(data["total_users"], int)
        assert isinstance(data["active_users"], int)
        assert isinstance(data["users_by_role"], dict)
    
    def test_get_user_statistics_unauthorized(self, client: TestClient, authenticated_headers):
        """Test user statistics without admin permissions."""
        response = client.get("/api/v1/users/statistics", headers=authenticated_headers)
        
        assert response.status_code == 403
