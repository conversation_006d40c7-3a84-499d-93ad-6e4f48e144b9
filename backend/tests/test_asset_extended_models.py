"""Tests for extended asset models and comprehensive metadata coverage."""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from app.db.models.asset_extended import (
    AssetVulnerability,
    AssetCompliance,
    AssetMetrics,
    AssetDependency,
    AssetConfiguration,
    AssetCertificate,
    AssetNetworkInterface,
    VulnerabilitySeverity,
    ComplianceFramework,
)
from app.db.models.asset import AssetType, AssetProvider


class TestAssetVulnerabilityModel:
    """Test asset vulnerability tracking."""
    
    def test_vulnerability_creation(self):
        """Test creating vulnerability records."""
        vuln = AssetVulnerability(
            asset_id="asset-123",
            cve_id="CVE-2023-1234",
            vulnerability_id="VULN-001",
            title="Critical SQL Injection Vulnerability",
            description="SQL injection in user authentication endpoint",
            severity=VulnerabilitySeverity.CRITICAL,
            cvss_score=9.8,
            cvss_vector="CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
            scanner_name="Nessus",
            status="open",
            remediation_status="pending",
            remediation_effort="high",
            remediation_priority=1,
            affected_component="auth_service",
            exploit_available=True,
            patch_available=True,
            patch_complexity="medium",
            evidence={
                "request": "POST /login",
                "payload": "' OR 1=1 --",
                "response": "Authentication bypassed"
            },
            references=[
                "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-1234",
                "https://nvd.nist.gov/vuln/detail/CVE-2023-1234"
            ]
        )
        
        assert vuln.cve_id == "CVE-2023-1234"
        assert vuln.severity == VulnerabilitySeverity.CRITICAL
        assert vuln.cvss_score == 9.8
        assert vuln.exploit_available == True
        assert vuln.patch_available == True
        assert vuln.evidence["payload"] == "' OR 1=1 --"
        assert len(vuln.references) == 2
    
    def test_vulnerability_severity_levels(self):
        """Test all vulnerability severity levels."""
        severities = [
            VulnerabilitySeverity.CRITICAL,
            VulnerabilitySeverity.HIGH,
            VulnerabilitySeverity.MEDIUM,
            VulnerabilitySeverity.LOW,
            VulnerabilitySeverity.INFO
        ]
        
        for severity in severities:
            vuln = AssetVulnerability(
                asset_id="asset-123",
                vulnerability_id=f"VULN-{severity.value}",
                title=f"{severity.value.title()} Vulnerability",
                severity=severity
            )
            assert vuln.severity == severity


class TestAssetComplianceModel:
    """Test asset compliance tracking."""
    
    def test_compliance_frameworks(self):
        """Test all compliance frameworks."""
        frameworks = [
            ComplianceFramework.SOC2,
            ComplianceFramework.ISO27001,
            ComplianceFramework.PCI_DSS,
            ComplianceFramework.HIPAA,
            ComplianceFramework.GDPR,
            ComplianceFramework.NIST,
            ComplianceFramework.CIS,
            ComplianceFramework.CUSTOM
        ]
        
        for framework in frameworks:
            compliance = AssetCompliance(
                asset_id="asset-123",
                framework=framework,
                control_id=f"{framework.value.upper()}-001",
                control_name=f"Test Control for {framework.value}",
                status="compliant",
                compliance_score=95.0,
                assessor="compliance-team",
                assessment_method="automated"
            )
            assert compliance.framework == framework
            assert compliance.compliance_score == 95.0
    
    def test_compliance_assessment_tracking(self):
        """Test compliance assessment tracking."""
        now = datetime.utcnow()
        next_assessment = now + timedelta(days=90)
        
        compliance = AssetCompliance(
            asset_id="asset-123",
            framework=ComplianceFramework.SOC2,
            control_id="SOC2-CC6.1",
            control_name="Logical and Physical Access Controls",
            control_description="The entity implements logical and physical access controls",
            requirement="Multi-factor authentication required for all privileged accounts",
            status="compliant",
            compliance_score=98.5,
            last_assessment=now,
            next_assessment=next_assessment,
            assessor="security-team",
            assessment_method="hybrid",
            evidence={
                "mfa_enabled": True,
                "privileged_accounts": 15,
                "mfa_compliance_rate": 100.0
            },
            findings=[
                {
                    "type": "observation",
                    "description": "All privileged accounts have MFA enabled",
                    "severity": "info"
                }
            ],
            remediation_plan="Continue monitoring MFA compliance",
            remediation_deadline=next_assessment,
            remediation_owner="security-team"
        )
        
        assert compliance.status == "compliant"
        assert compliance.compliance_score == 98.5
        assert compliance.evidence["mfa_compliance_rate"] == 100.0
        assert len(compliance.findings) == 1


class TestAssetMetricsModel:
    """Test asset performance and health metrics."""
    
    def test_metric_types(self):
        """Test different metric types."""
        metric_configs = [
            {
                "name": "cpu_usage_percent",
                "type": "gauge",
                "value": 75.5,
                "unit": "percent",
                "warning_threshold": 80.0,
                "critical_threshold": 90.0
            },
            {
                "name": "memory_usage_bytes",
                "type": "gauge",
                "value": **********,  # 8GB in bytes
                "unit": "bytes",
                "warning_threshold": 12884901888,  # 12GB
                "critical_threshold": 15032385536   # 14GB
            },
            {
                "name": "http_requests_total",
                "type": "counter",
                "value": 1000000,
                "unit": "count"
            },
            {
                "name": "response_time_seconds",
                "type": "histogram",
                "value": 0.250,
                "unit": "seconds",
                "warning_threshold": 1.0,
                "critical_threshold": 5.0
            }
        ]
        
        for config in metric_configs:
            metric = AssetMetrics(
                asset_id="asset-123",
                metric_name=config["name"],
                metric_type=config["type"],
                value=config["value"],
                unit=config["unit"],
                source="prometheus",
                labels={"instance": "web-01", "environment": "production"},
                warning_threshold=config.get("warning_threshold"),
                critical_threshold=config.get("critical_threshold"),
                is_alerting=config["value"] > config.get("critical_threshold", float('inf'))
            )
            
            assert metric.metric_name == config["name"]
            assert metric.metric_type == config["type"]
            assert metric.value == config["value"]
            assert metric.unit == config["unit"]


class TestAssetDependencyModel:
    """Test asset dependency tracking."""
    
    def test_dependency_types(self):
        """Test different dependency types."""
        dependency_configs = [
            {
                "type": "runtime",
                "criticality": "critical",
                "protocol": "https",
                "port": 443,
                "endpoint": "https://api.database.internal:443/v1",
                "description": "Web service depends on database API",
                "failure_impact": "service_down",
                "recovery_time": 300
            },
            {
                "type": "build",
                "criticality": "high",
                "description": "Application depends on build artifacts",
                "version_requirement": ">=1.2.0",
                "is_optional": False,
                "failure_impact": "degraded",
                "recovery_time": 1800
            },
            {
                "type": "data",
                "criticality": "medium",
                "protocol": "tcp",
                "port": 5432,
                "endpoint": "postgres://db.internal:5432/app_data",
                "description": "Analytics service reads from main database",
                "failure_impact": "degraded",
                "recovery_time": 600
            },
            {
                "type": "network",
                "criticality": "low",
                "protocol": "udp",
                "port": 53,
                "endpoint": "dns.internal:53",
                "description": "Service depends on internal DNS",
                "is_optional": True,
                "failure_impact": "none",
                "recovery_time": 60
            }
        ]
        
        for config in dependency_configs:
            dependency = AssetDependency(
                dependent_asset_id="asset-web-01",
                dependency_asset_id="asset-db-01",
                dependency_type=config["type"],
                criticality=config["criticality"],
                protocol=config.get("protocol"),
                port=config.get("port"),
                endpoint=config.get("endpoint"),
                description=config["description"],
                version_requirement=config.get("version_requirement"),
                is_optional=config.get("is_optional", False),
                validation_method="health_check",
                is_healthy=True,
                failure_impact=config["failure_impact"],
                recovery_time=config["recovery_time"]
            )
            
            assert dependency.dependency_type == config["type"]
            assert dependency.criticality == config["criticality"]
            assert dependency.failure_impact == config["failure_impact"]
            assert dependency.recovery_time == config["recovery_time"]


class TestAssetConfigurationModel:
    """Test asset configuration tracking."""
    
    def test_configuration_types(self):
        """Test different configuration types."""
        configs = [
            {
                "key": "ssh.port",
                "value": "22",
                "type": "system",
                "sensitive": False,
                "encrypted": False
            },
            {
                "key": "database.password",
                "value": "encrypted_password_hash",
                "type": "security",
                "sensitive": True,
                "encrypted": True
            },
            {
                "key": "firewall.rules",
                "value": '{"allow": ["80", "443"], "deny": ["*"]}',
                "type": "network",
                "sensitive": False,
                "encrypted": False
            },
            {
                "key": "app.debug_mode",
                "value": "false",
                "type": "application",
                "sensitive": False,
                "encrypted": False
            }
        ]
        
        for config in configs:
            asset_config = AssetConfiguration(
                asset_id="asset-123",
                configuration_key=config["key"],
                configuration_value=config["value"],
                configuration_type=config["type"],
                is_sensitive=config["sensitive"],
                is_encrypted=config["encrypted"],
                previous_value="old_value",
                changed_by="admin",
                change_reason="Security hardening",
                is_compliant=True,
                compliance_rules={"min_length": 8, "complexity": "high"},
                validation_status="valid"
            )
            
            assert asset_config.configuration_key == config["key"]
            assert asset_config.configuration_type == config["type"]
            assert asset_config.is_sensitive == config["sensitive"]
            assert asset_config.is_encrypted == config["encrypted"]


class TestAssetCertificateModel:
    """Test SSL/TLS certificate tracking."""
    
    def test_certificate_tracking(self):
        """Test certificate tracking with various scenarios."""
        # Valid certificate
        valid_cert = AssetCertificate(
            asset_id="asset-web-01",
            certificate_hash="a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
            subject="CN=api.example.com,O=Example Corp,C=US",
            issuer="CN=Let's Encrypt Authority X3,O=Let's Encrypt,C=US",
            serial_number="03:4B:9F:8A:2C:1D:7E:5F",
            valid_from=datetime.utcnow() - timedelta(days=30),
            valid_to=datetime.utcnow() + timedelta(days=60),
            is_expired=False,
            days_until_expiry=60,
            key_algorithm="RSA",
            key_size=2048,
            signature_algorithm="SHA256withRSA",
            san_dns_names=["api.example.com", "www.api.example.com"],
            san_ip_addresses=["***********"],
            is_self_signed=False,
            chain_length=3,
            root_ca="CN=ISRG Root X1,O=Internet Security Research Group,C=US",
            validation_status="valid",
            is_trusted=True,
            security_score=95.0
        )
        
        assert valid_cert.is_expired == False
        assert valid_cert.days_until_expiry == 60
        assert valid_cert.key_size == 2048
        assert "api.example.com" in valid_cert.san_dns_names
        assert valid_cert.is_trusted == True
        assert valid_cert.security_score == 95.0
        
        # Expiring certificate
        expiring_cert = AssetCertificate(
            asset_id="asset-web-02",
            certificate_hash="b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567",
            subject="CN=old.example.com",
            issuer="CN=Old CA",
            serial_number="04:5C:AF:9B:3D:2E:8F:6A",
            valid_from=datetime.utcnow() - timedelta(days=365),
            valid_to=datetime.utcnow() + timedelta(days=7),
            is_expired=False,
            days_until_expiry=7,
            key_algorithm="RSA",
            key_size=1024,  # Weak key size
            is_self_signed=True,
            validation_status="expiring_soon",
            is_trusted=False,
            trust_issues=["weak_key_size", "self_signed", "expiring_soon"],
            security_score=25.0
        )
        
        assert expiring_cert.days_until_expiry == 7
        assert expiring_cert.key_size == 1024
        assert expiring_cert.is_self_signed == True
        assert expiring_cert.is_trusted == False
        assert "weak_key_size" in expiring_cert.trust_issues
        assert expiring_cert.security_score == 25.0


class TestAssetNetworkInterfaceModel:
    """Test network interface tracking."""
    
    def test_network_interface_types(self):
        """Test different network interface types."""
        interfaces = [
            {
                "name": "eth0",
                "type": "ethernet",
                "ip": "*************",
                "subnet_mask": "*************",
                "gateway": "***********",
                "mac": "00:50:56:12:34:56",
                "mtu": 1500,
                "speed": "1Gbps",
                "duplex": "full",
                "is_primary": True,
                "is_public": False
            },
            {
                "name": "wlan0",
                "type": "wifi",
                "ip": "*********",
                "subnet_mask": "*************",
                "mac": "a4:b6:c8:da:eb:fc",
                "mtu": 1500,
                "speed": "300Mbps",
                "is_primary": False,
                "is_public": False
            },
            {
                "name": "lo",
                "type": "loopback",
                "ip": "127.0.0.1",
                "subnet_mask": "*********",
                "mtu": 65536,
                "is_primary": False,
                "is_public": False
            },
            {
                "name": "tun0",
                "type": "vpn",
                "ip": "********",
                "subnet_mask": "*************",
                "mtu": 1194,
                "is_primary": False,
                "is_public": False
            }
        ]
        
        for iface_config in interfaces:
            interface = AssetNetworkInterface(
                asset_id="asset-server-01",
                interface_name=iface_config["name"],
                interface_type=iface_config["type"],
                ip_address=iface_config["ip"],
                subnet_mask=iface_config.get("subnet_mask"),
                gateway=iface_config.get("gateway"),
                mac_address=iface_config.get("mac"),
                mtu=iface_config.get("mtu"),
                speed=iface_config.get("speed"),
                duplex=iface_config.get("duplex"),
                is_up=True,
                is_primary=iface_config["is_primary"],
                is_public=iface_config["is_public"],
                bytes_sent=1000000,
                bytes_received=5000000,
                packets_sent=10000,
                packets_received=50000,
                errors=0
            )
            
            assert interface.interface_name == iface_config["name"]
            assert interface.interface_type == iface_config["type"]
            assert str(interface.ip_address) == iface_config["ip"]
            assert interface.is_primary == iface_config["is_primary"]
            assert interface.is_public == iface_config["is_public"]
