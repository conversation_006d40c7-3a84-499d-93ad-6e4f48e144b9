"""Playwright E2E test configuration and fixtures."""

import asyncio
import os
import subprocess
import time
from typing import Async<PERSON>enerator, Generator

import pytest
import pytest_asyncio
from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright, async_playwright
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.config import settings
from app.db.base import Base
from app.db.models.user import User, UserRole
from app.db.session import get_db
from app.main import app
from app.services.user_service import UserService
from app.schemas.user import UserCreate


# Test configuration
TEST_BASE_URL = "http://localhost:8000"
TEST_FRONTEND_URL = "http://localhost:3000"
HEADLESS = os.getenv("HEADLESS", "true").lower() == "true"
SLOW_MO = int(os.getenv("SLOW_MO", "0"))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def playwright_instance() -> AsyncGenerator[Playwright, None]:
    """Create a Playwright instance for the test session."""
    async with async_playwright() as p:
        yield p


@pytest.fixture(scope="session")
async def browser(playwright_instance: Playwright) -> AsyncGenerator[Browser, None]:
    """Create a browser instance for the test session."""
    browser = await playwright_instance.chromium.launch(
        headless=HEADLESS,
        slow_mo=SLOW_MO,
        args=[
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--no-sandbox",
            "--disable-setuid-sandbox",
        ]
    )
    yield browser
    await browser.close()


@pytest.fixture
async def context(browser: Browser) -> AsyncGenerator[BrowserContext, None]:
    """Create a browser context for each test."""
    context = await browser.new_context(
        viewport={"width": 1920, "height": 1080},
        ignore_https_errors=True,
        record_video_dir="tests/e2e/videos" if os.getenv("RECORD_VIDEO") else None,
        record_har_path="tests/e2e/har/test.har" if os.getenv("RECORD_HAR") else None,
    )
    yield context
    await context.close()


@pytest.fixture
async def page(context: BrowserContext) -> AsyncGenerator[Page, None]:
    """Create a page for each test."""
    page = await context.new_page()
    
    # Set up console logging
    page.on("console", lambda msg: print(f"Console {msg.type}: {msg.text}"))
    page.on("pageerror", lambda error: print(f"Page error: {error}"))
    
    yield page
    await page.close()


@pytest.fixture(scope="session")
def test_db_engine():
    """Create a test database engine."""
    # Use a separate test database
    test_db_url = settings.DATABASE_URL.replace("/blast_radius", "/blast_radius_e2e_test")
    engine = create_engine(test_db_url)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Clean up
    Base.metadata.drop_all(bind=engine)
    engine.dispose()


@pytest.fixture(scope="session")
def test_db_session(test_db_engine):
    """Create a test database session."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db_engine)
    return TestingSessionLocal


@pytest.fixture
async def clean_db(test_db_session):
    """Clean the database before each test."""
    session = test_db_session()
    try:
        # Delete all users except system roles
        session.query(User).delete()
        session.commit()
        
        # Recreate system roles
        UserRole.create_system_roles(session)
        session.commit()
        
        yield session
    finally:
        session.close()


@pytest.fixture
async def test_users(clean_db):
    """Create test users for E2E tests."""
    session = clean_db
    user_service = UserService(session)
    
    # Create test users for different roles
    test_users = {}
    
    # Administrator
    admin_data = UserCreate(
        username="test_admin",
        email="<EMAIL>",
        full_name="Test Administrator",
        password="AdminPassword123!",
        roles=["admin"]
    )
    test_users["admin"] = user_service.create_user(admin_data)
    
    # SOC Operator
    soc_data = UserCreate(
        username="test_soc",
        email="<EMAIL>",
        full_name="Test SOC Operator",
        password="SocPassword123!",
        roles=["soc_operator"]
    )
    test_users["soc_operator"] = user_service.create_user(soc_data)
    
    # Security Architect
    architect_data = UserCreate(
        username="test_architect",
        email="<EMAIL>",
        full_name="Test Security Architect",
        password="ArchitectPassword123!",
        roles=["security_architect"]
    )
    test_users["security_architect"] = user_service.create_user(architect_data)
    
    # Red Team Member
    redteam_data = UserCreate(
        username="test_redteam",
        email="<EMAIL>",
        full_name="Test Red Team Member",
        password="RedteamPassword123!",
        roles=["red_team_member"]
    )
    test_users["red_team_member"] = user_service.create_user(redteam_data)
    
    # Purple Team Member
    purple_data = UserCreate(
        username="test_purple",
        email="<EMAIL>",
        full_name="Test Purple Team Member",
        password="PurplePassword123!",
        roles=["purple_team_member"]
    )
    test_users["purple_team_member"] = user_service.create_user(purple_data)
    
    # Analyst
    analyst_data = UserCreate(
        username="test_analyst",
        email="<EMAIL>",
        full_name="Test Analyst",
        password="AnalystPassword123!",
        roles=["analyst"]
    )
    test_users["analyst"] = user_service.create_user(analyst_data)
    
    # Viewer
    viewer_data = UserCreate(
        username="test_viewer",
        email="<EMAIL>",
        full_name="Test Viewer",
        password="ViewerPassword123!",
        roles=["viewer"]
    )
    test_users["viewer"] = user_service.create_user(viewer_data)
    
    session.commit()
    return test_users


@pytest.fixture(scope="session")
def backend_server():
    """Start the backend server for E2E tests."""
    # Check if server is already running
    try:
        import httpx
        response = httpx.get(f"{TEST_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("Backend server already running")
            yield
            return
    except:
        pass
    
    # Start the server
    print("Starting backend server for E2E tests...")
    process = subprocess.Popen([
        "uvicorn", "app.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ], cwd="backend")
    
    # Wait for server to start
    time.sleep(5)
    
    # Verify server is running
    try:
        import httpx
        response = httpx.get(f"{TEST_BASE_URL}/health", timeout=10)
        assert response.status_code == 200
        print("Backend server started successfully")
    except Exception as e:
        process.terminate()
        raise Exception(f"Failed to start backend server: {e}")
    
    yield
    
    # Clean up
    process.terminate()
    process.wait()


class PageHelpers:
    """Helper methods for common page interactions."""
    
    def __init__(self, page: Page):
        self.page = page
    
    async def login(self, username: str, password: str, expect_success: bool = True):
        """Login with given credentials."""
        await self.page.goto(f"{TEST_BASE_URL}/docs")
        
        # Click on login endpoint
        await self.page.click('text="POST /api/v1/auth/login"')
        await self.page.click('button:has-text("Try it out")')
        
        # Fill login data
        login_data = {
            "username": username,
            "password": password,
            "remember_me": False
        }
        
        await self.page.fill('textarea[placeholder="Request body"]', str(login_data).replace("'", '"'))
        await self.page.click('button:has-text("Execute")')
        
        if expect_success:
            # Wait for successful response
            await self.page.wait_for_selector('text="200"', timeout=10000)
        
        return self.page
    
    async def logout(self):
        """Logout current user."""
        await self.page.goto(f"{TEST_BASE_URL}/docs")
        
        # Click on logout endpoint
        await self.page.click('text="POST /api/v1/auth/logout"')
        await self.page.click('button:has-text("Try it out")')
        await self.page.click('button:has-text("Execute")')
        
        # Wait for response
        await self.page.wait_for_selector('text="200"', timeout=5000)
    
    async def navigate_to_endpoint(self, endpoint: str):
        """Navigate to a specific API endpoint in Swagger UI."""
        await self.page.goto(f"{TEST_BASE_URL}/docs")
        await self.page.click(f'text="{endpoint}"')
    
    async def execute_api_call(self, method: str, endpoint: str, data: dict = None):
        """Execute an API call through Swagger UI."""
        await self.navigate_to_endpoint(f"{method} {endpoint}")
        await self.page.click('button:has-text("Try it out")')
        
        if data:
            await self.page.fill('textarea[placeholder="Request body"]', str(data).replace("'", '"'))
        
        await self.page.click('button:has-text("Execute")')
        
        # Wait for response
        await self.page.wait_for_selector('[class*="response"]', timeout=10000)
    
    async def check_response_status(self, expected_status: str):
        """Check the response status code."""
        status_element = await self.page.wait_for_selector(f'text="{expected_status}"', timeout=5000)
        return status_element is not None


@pytest.fixture
async def page_helpers(page: Page) -> PageHelpers:
    """Provide page helper methods."""
    return PageHelpers(page)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest for E2E tests."""
    # Create directories for test artifacts
    os.makedirs("tests/e2e/videos", exist_ok=True)
    os.makedirs("tests/e2e/screenshots", exist_ok=True)
    os.makedirs("tests/e2e/har", exist_ok=True)
    os.makedirs("tests/e2e/reports", exist_ok=True)


def pytest_runtest_makereport(item, call):
    """Generate test reports with screenshots on failure."""
    if call.when == "call" and call.excinfo is not None:
        # Test failed, take screenshot if page is available
        if hasattr(item, "funcargs") and "page" in item.funcargs:
            page = item.funcargs["page"]
            screenshot_path = f"tests/e2e/screenshots/{item.name}_failure.png"
            try:
                asyncio.run(page.screenshot(path=screenshot_path))
                print(f"Screenshot saved: {screenshot_path}")
            except Exception as e:
                print(f"Failed to take screenshot: {e}")
