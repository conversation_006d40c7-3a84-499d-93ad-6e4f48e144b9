"""E2E tests for user management functionality."""

import json
import pytest
from playwright.async_api import Page, expect

from tests.e2e.conftest import PageHel<PERSON>, TEST_BASE_URL


class TestUserManagementE2E:
    """End-to-end tests for user management system."""

    @pytest.mark.asyncio
    async def test_admin_can_view_users_list(self, page: Page, test_users, backend_server):
        """Test that admin can view the users list."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Navigate to users list endpoint
        await page.click('text="GET /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        await page.click('button:has-text("Execute")')
        
        # Should get successful response
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Check response contains users
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "test_admin" in response_text
        assert "items" in response_text

    @pytest.mark.asyncio
    async def test_admin_can_create_user(self, page: Page, test_users, backend_server):
        """Test that admin can create a new user."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Navigate to create user endpoint
        await page.click('text="POST /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        
        # Fill user creation data
        new_user_data = {
            "username": "new_test_user",
            "email": "<EMAIL>",
            "full_name": "New Test User",
            "password": "NewUserPassword123!",
            "department": "Testing",
            "job_title": "Test Engineer",
            "roles": ["analyst"]
        }
        
        await page.fill('textarea', json.dumps(new_user_data, indent=2))
        await page.click('button:has-text("Execute")')
        
        # Should get successful creation
        await page.wait_for_selector('text="201"', timeout=10000)
        
        # Check response contains new user data
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "new_test_user" in response_text

    @pytest.mark.asyncio
    async def test_admin_can_update_user(self, page: Page, test_users, backend_server):
        """Test that admin can update user information."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # First get the analyst user ID
        analyst_user = test_users["analyst"]
        
        # Navigate to update user endpoint
        await page.click('text="PUT /api/v1/users/{user_id}"')
        await page.click('button:has-text("Try it out")')
        
        # Fill user ID
        await page.fill('input[placeholder="user_id"]', str(analyst_user.id))
        
        # Fill update data
        update_data = {
            "full_name": "Updated Analyst Name",
            "department": "Updated Department",
            "job_title": "Senior Analyst"
        }
        
        await page.fill('textarea', json.dumps(update_data, indent=2))
        await page.click('button:has-text("Execute")')
        
        # Should get successful update
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Check response contains updated data
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "Updated Analyst Name" in response_text

    @pytest.mark.asyncio
    async def test_non_admin_cannot_create_user(self, page: Page, test_users, backend_server):
        """Test that non-admin users cannot create users."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as analyst (non-admin)
        await self._login_as_user(page, "test_analyst", "AnalystPassword123!")
        
        # Navigate to create user endpoint
        await page.click('text="POST /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        
        # Try to create user
        new_user_data = {
            "username": "unauthorized_user",
            "email": "<EMAIL>",
            "full_name": "Unauthorized User",
            "password": "Password123!",
            "roles": ["viewer"]
        }
        
        await page.fill('textarea', json.dumps(new_user_data, indent=2))
        await page.click('button:has-text("Execute")')
        
        # Should get forbidden response
        await page.wait_for_selector('text="403"', timeout=10000)

    @pytest.mark.asyncio
    async def test_user_can_view_own_profile(self, page: Page, test_users, backend_server):
        """Test that users can view their own profile."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as analyst
        await self._login_as_user(page, "test_analyst", "AnalystPassword123!")
        
        # Navigate to get current user endpoint
        await page.click('text="GET /api/v1/auth/me"')
        await page.click('button:has-text("Try it out")')
        await page.click('button:has-text("Execute")')
        
        # Should get successful response
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Check response contains user data
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "test_analyst" in response_text
        assert "analyst" in response_text

    @pytest.mark.asyncio
    async def test_user_can_change_own_password(self, page: Page, test_users, backend_server):
        """Test that users can change their own password."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as analyst
        await self._login_as_user(page, "test_analyst", "AnalystPassword123!")
        
        # Navigate to change password endpoint
        await page.click('text="POST /api/v1/auth/change-password"')
        await page.click('button:has-text("Try it out")')
        
        # Fill password change data
        password_data = {
            "current_password": "AnalystPassword123!",
            "new_password": "NewAnalystPassword123!"
        }
        
        await page.fill('textarea', json.dumps(password_data, indent=2))
        await page.click('button:has-text("Execute")')
        
        # Should get successful response
        await page.wait_for_selector('text="200"', timeout=10000)

    @pytest.mark.asyncio
    async def test_role_based_access_control(self, page: Page, test_users, backend_server):
        """Test role-based access control for different user types."""
        # Test SOC Operator access
        await page.goto(f"{TEST_BASE_URL}/docs")
        await self._login_as_user(page, "test_soc", "SocPassword123!")
        
        # SOC operator should be able to access their profile
        await page.click('text="GET /api/v1/auth/me"')
        await page.click('button:has-text("Try it out")')
        await page.click('button:has-text("Execute")')
        
        await page.wait_for_selector('text="200"', timeout=10000)
        
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "soc_operator" in response_text

    @pytest.mark.asyncio
    async def test_user_search_functionality(self, page: Page, test_users, backend_server):
        """Test user search functionality."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Navigate to users list with search
        await page.click('text="GET /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        
        # Add search parameter
        await page.fill('input[placeholder="search"]', "test_analyst")
        await page.click('button:has-text("Execute")')
        
        # Should get successful response with filtered results
        await page.wait_for_selector('text="200"', timeout=10000)
        
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "test_analyst" in response_text

    @pytest.mark.asyncio
    async def test_user_pagination(self, page: Page, test_users, backend_server):
        """Test user list pagination."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Navigate to users list with pagination
        await page.click('text="GET /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        
        # Set page size
        await page.fill('input[placeholder="size"]', "2")
        await page.click('button:has-text("Execute")')
        
        # Should get successful response with pagination info
        await page.wait_for_selector('text="200"', timeout=10000)
        
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "page" in response_text
        assert "total" in response_text

    @pytest.mark.asyncio
    async def test_user_role_assignment(self, page: Page, test_users, backend_server):
        """Test user role assignment functionality."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Get viewer user
        viewer_user = test_users["viewer"]
        
        # Navigate to role assignment endpoint (if available)
        # This would test the role assignment API when implemented
        await page.click('text="GET /api/v1/users/{user_id}"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('input[placeholder="user_id"]', str(viewer_user.id))
        await page.click('button:has-text("Execute")')
        
        await page.wait_for_selector('text="200"', timeout=10000)
        
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "viewer" in response_text

    @pytest.mark.asyncio
    async def test_user_deletion_protection(self, page: Page, test_users, backend_server):
        """Test user deletion with proper authorization."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Try to delete a user (if delete endpoint exists)
        viewer_user = test_users["viewer"]
        
        # Navigate to delete user endpoint
        await page.click('text="DELETE /api/v1/users/{user_id}"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('input[placeholder="user_id"]', str(viewer_user.id))
        await page.click('button:has-text("Execute")')
        
        # Should get successful deletion or appropriate response
        # The actual response depends on implementation
        response_selector = await page.wait_for_selector('[class*="response-code"]', timeout=10000)
        response_code = await response_selector.text_content()
        
        # Accept either 200 (soft delete) or 204 (hard delete) or 404 (not implemented)
        assert response_code in ["200", "204", "404"]

    async def _login_as_admin(self, page: Page):
        """Helper method to login as admin."""
        await self._login_as_user(page, "test_admin", "AdminPassword123!")

    async def _login_as_user(self, page: Page, username: str, password: str):
        """Helper method to login as any user."""
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        login_data = {
            "username": username,
            "password": password,
            "remember_me": False
        }
        
        await page.fill('textarea', json.dumps(login_data, indent=2))
        await page.click('button:has-text("Execute")')
        
        # Wait for successful login
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Extract and set authorization token
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        
        try:
            # Extract access token from response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                response_data = json.loads(json_match.group())
                access_token = response_data.get("tokens", {}).get("access_token")
                
                if access_token:
                    # Set authorization for subsequent requests
                    await page.click('button:has-text("Authorize")')
                    await page.fill('input[placeholder="Value"]', f"Bearer {access_token}")
                    await page.click('button:has-text("Authorize")')
                    await page.click('button:has-text("Close")')
                    
        except (json.JSONDecodeError, AttributeError):
            # If token extraction fails, continue without authorization
            pass

    @pytest.mark.asyncio
    async def test_user_profile_completeness(self, page: Page, test_users, backend_server):
        """Test that user profiles contain all expected fields."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Get a specific user
        analyst_user = test_users["analyst"]
        
        await page.click('text="GET /api/v1/users/{user_id}"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('input[placeholder="user_id"]', str(analyst_user.id))
        await page.click('button:has-text("Execute")')
        
        await page.wait_for_selector('text="200"', timeout=10000)
        
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        
        # Check for expected user fields
        expected_fields = ["id", "username", "email", "full_name", "roles", "created_at"]
        for field in expected_fields:
            assert field in response_text

    @pytest.mark.asyncio
    async def test_user_input_validation(self, page: Page, test_users, backend_server):
        """Test user input validation for user creation."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin
        await self._login_as_admin(page)
        
        # Test invalid email format
        await page.click('text="POST /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        
        invalid_user_data = {
            "username": "testuser",
            "email": "invalid-email",  # Invalid email format
            "full_name": "Test User",
            "password": "ValidPassword123!",
            "roles": ["viewer"]
        }
        
        await page.fill('textarea', json.dumps(invalid_user_data, indent=2))
        await page.click('button:has-text("Execute")')
        
        # Should get validation error
        await page.wait_for_selector('text="422"', timeout=10000)
