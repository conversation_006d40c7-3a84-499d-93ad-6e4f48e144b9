"""E2E tests for authentication functionality."""

import pytest
from playwright.async_api import Page, expect

from tests.e2e.conftest import PageHel<PERSON>, TEST_BASE_URL


class TestAuthenticationE2E:
    """End-to-end tests for authentication system."""

    @pytest.mark.asyncio
    async def test_api_documentation_accessible(self, page: Page, backend_server):
        """Test that API documentation is accessible."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Check that Swagger UI loads
        await expect(page.locator("text=Blast-Radius Security Tool")).to_be_visible()
        await expect(page.locator("text=Authentication")).to_be_visible()
        
        # Check that endpoints are visible
        await expect(page.locator("text=/api/v1/auth/login")).to_be_visible()
        await expect(page.locator("text=/api/v1/auth/logout")).to_be_visible()

    @pytest.mark.asyncio
    async def test_health_endpoint(self, page: Page, backend_server):
        """Test health endpoint accessibility."""
        await page.goto(f"{TEST_BASE_URL}/health")
        
        # Check response
        content = await page.content()
        assert "healthy" in content.lower()

    @pytest.mark.asyncio
    async def test_login_success_via_swagger(self, page: Page, page_helpers: PageHelpers, test_users, backend_server):
        """Test successful login through Swagger UI."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Navigate to login endpoint
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        # Fill login data
        login_data = {
            "username": "test_admin",
            "password": "AdminPassword123!",
            "remember_me": False
        }
        
        await page.fill('textarea', '{\n  "username": "test_admin",\n  "password": "AdminPassword123!",\n  "remember_me": false\n}')
        await page.click('button:has-text("Execute")')
        
        # Wait for successful response
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Check response contains tokens
        response_text = await page.locator('[class*="response-col_description"]').text_content()
        assert "access_token" in response_text
        assert "refresh_token" in response_text

    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, page: Page, backend_server):
        """Test login with invalid credentials."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Navigate to login endpoint
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        # Fill invalid login data
        await page.fill('textarea', '{\n  "username": "invalid_user",\n  "password": "wrong_password",\n  "remember_me": false\n}')
        await page.click('button:has-text("Execute")')
        
        # Wait for error response
        await page.wait_for_selector('text="401"', timeout=10000)

    @pytest.mark.asyncio
    async def test_get_current_user_unauthorized(self, page: Page, backend_server):
        """Test accessing protected endpoint without authentication."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Navigate to /me endpoint
        await page.click('text="GET /api/v1/auth/me"')
        await page.click('button:has-text("Try it out")')
        await page.click('button:has-text("Execute")')
        
        # Should get 401 unauthorized
        await page.wait_for_selector('text="401"', timeout=10000)

    @pytest.mark.asyncio
    async def test_get_current_user_authorized(self, page: Page, test_users, backend_server):
        """Test accessing protected endpoint with valid authentication."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # First login
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('textarea', '{\n  "username": "test_admin",\n  "password": "AdminPassword123!",\n  "remember_me": false\n}')
        await page.click('button:has-text("Execute")')
        
        # Wait for successful login
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Extract access token from response
        response_element = page.locator('[class*="response-col_description"]')
        response_text = await response_element.text_content()
        
        # Find access token in response (simplified extraction)
        import json
        import re
        
        # Extract JSON from response
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                response_data = json.loads(json_match.group())
                access_token = response_data.get("tokens", {}).get("access_token")
                
                if access_token:
                    # Now test /me endpoint with token
                    await page.click('text="GET /api/v1/auth/me"')
                    await page.click('button:has-text("Try it out")')
                    
                    # Add authorization header
                    await page.click('button:has-text("Authorize")')
                    await page.fill('input[placeholder="Value"]', f"Bearer {access_token}")
                    await page.click('button:has-text("Authorize")')
                    await page.click('button:has-text("Close")')
                    
                    # Execute request
                    await page.click('button:has-text("Execute")')
                    
                    # Should get 200 OK
                    await page.wait_for_selector('text="200"', timeout=10000)
                    
                    # Check response contains user data
                    me_response = await page.locator('[class*="response-col_description"]').text_content()
                    assert "test_admin" in me_response
                    
            except json.JSONDecodeError:
                pytest.skip("Could not parse login response")

    @pytest.mark.asyncio
    async def test_password_validation_requirements(self, page: Page, backend_server):
        """Test password validation through user creation."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Navigate to create user endpoint
        await page.click('text="POST /api/v1/users"')
        await page.click('button:has-text("Try it out")')
        
        # Test weak password
        weak_password_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "password": "weak",  # Too weak
            "roles": ["viewer"]
        }
        
        await page.fill('textarea', str(weak_password_data).replace("'", '"'))
        await page.click('button:has-text("Execute")')
        
        # Should get validation error
        await page.wait_for_selector('text="422"', timeout=10000)

    @pytest.mark.asyncio
    async def test_multiple_role_assignment(self, page: Page, test_users, backend_server):
        """Test user with multiple roles through API."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login as admin first
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('textarea', '{\n  "username": "test_admin",\n  "password": "AdminPassword123!",\n  "remember_me": false\n}')
        await page.click('button:has-text("Execute")')
        
        await page.wait_for_selector('text="200"', timeout=10000)

    @pytest.mark.asyncio
    async def test_session_timeout_behavior(self, page: Page, test_users, backend_server):
        """Test session timeout behavior."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('textarea', '{\n  "username": "test_admin",\n  "password": "AdminPassword123!",\n  "remember_me": false\n}')
        await page.click('button:has-text("Execute")')
        
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Test immediate access to protected endpoint
        await page.click('text="GET /api/v1/auth/me"')
        await page.click('button:has-text("Try it out")')
        await page.click('button:has-text("Execute")')
        
        # Should work immediately after login
        await page.wait_for_selector('text="200"', timeout=10000)

    @pytest.mark.asyncio
    async def test_logout_functionality(self, page: Page, test_users, backend_server):
        """Test logout functionality."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Login first
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        await page.fill('textarea', '{\n  "username": "test_admin",\n  "password": "AdminPassword123!",\n  "remember_me": false\n}')
        await page.click('button:has-text("Execute")')
        
        await page.wait_for_selector('text="200"', timeout=10000)
        
        # Now logout
        await page.click('text="POST /api/v1/auth/logout"')
        await page.click('button:has-text("Try it out")')
        await page.click('button:has-text("Execute")')
        
        # Should get successful logout
        await page.wait_for_selector('text="200"', timeout=10000)

    @pytest.mark.asyncio
    async def test_api_documentation_completeness(self, page: Page, backend_server):
        """Test that API documentation includes all expected endpoints."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Check authentication endpoints
        auth_endpoints = [
            "/api/v1/auth/login",
            "/api/v1/auth/logout", 
            "/api/v1/auth/me",
            "/api/v1/auth/refresh",
            "/api/v1/auth/change-password"
        ]
        
        for endpoint in auth_endpoints:
            await expect(page.locator(f"text={endpoint}")).to_be_visible()
        
        # Check user management endpoints
        user_endpoints = [
            "/api/v1/users",
            "/api/v1/users/{user_id}"
        ]
        
        for endpoint in user_endpoints:
            await expect(page.locator(f"text={endpoint}")).to_be_visible()

    @pytest.mark.asyncio
    async def test_openapi_spec_accessibility(self, page: Page, backend_server):
        """Test that OpenAPI specification is accessible."""
        await page.goto(f"{TEST_BASE_URL}/openapi.json")
        
        # Check that it's valid JSON
        content = await page.content()
        import json
        
        try:
            spec = json.loads(content)
            assert "openapi" in spec
            assert "info" in spec
            assert "paths" in spec
            assert "/api/v1/auth/login" in spec["paths"]
        except json.JSONDecodeError:
            pytest.fail("OpenAPI specification is not valid JSON")

    @pytest.mark.asyncio
    async def test_redoc_documentation(self, page: Page, backend_server):
        """Test ReDoc documentation accessibility."""
        await page.goto(f"{TEST_BASE_URL}/redoc")
        
        # Check that ReDoc loads
        await expect(page.locator("text=Blast-Radius Security Tool")).to_be_visible()
        
        # Check that authentication section is present
        await expect(page.locator("text=Authentication")).to_be_visible()

    @pytest.mark.asyncio
    async def test_cors_headers_present(self, page: Page, backend_server):
        """Test that CORS headers are properly set."""
        # Monitor network requests
        responses = []
        
        def handle_response(response):
            responses.append(response)
        
        page.on("response", handle_response)
        
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Check that responses include CORS headers
        api_responses = [r for r in responses if "/api/" in r.url]
        
        if api_responses:
            # Check for CORS headers in at least one API response
            headers = await api_responses[0].all_headers()
            # Note: In a real test, you'd check for specific CORS headers
            # This is a basic check that headers are present
            assert len(headers) > 0

    @pytest.mark.asyncio
    async def test_error_handling_display(self, page: Page, backend_server):
        """Test that API errors are properly displayed in Swagger UI."""
        await page.goto(f"{TEST_BASE_URL}/docs")
        
        # Try an invalid request
        await page.click('text="POST /api/v1/auth/login"')
        await page.click('button:has-text("Try it out")')
        
        # Send malformed JSON
        await page.fill('textarea', '{"invalid": json}')
        await page.click('button:has-text("Execute")')
        
        # Should show error response
        await page.wait_for_selector('text="400"', timeout=10000)
        
        # Check that error details are shown
        response_section = page.locator('[class*="response"]')
        await expect(response_section).to_be_visible()
