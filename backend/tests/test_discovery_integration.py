"""Integration tests for the complete asset discovery pipeline."""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from typing import List, Dict, Any

from app.services.discovery import (
    CloudDiscoveryEngine,
    APIDiscoveryEngine,
    NetworkDiscoveryEngine,
    DiscoveryResult,
)
from app.services.discovery_orchestrator import DiscoveryOrchestrator
from app.db.models.asset import (
    Asset,
    AssetType,
    AssetProvider,
    DiscoverySource,
    DiscoveryJob,
    DiscoveryJobStatus,
    RiskLevel,
)


class TestDiscoveryPipelineIntegration:
    """Test the complete discovery pipeline from start to finish."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        db = Mock()
        db.query.return_value.filter.return_value.all.return_value = []
        db.commit.return_value = None
        return db
    
    @pytest.fixture
    def orchestrator(self, mock_db):
        """Create orchestrator with mocked dependencies."""
        orchestrator = DiscoveryOrchestrator(mock_db)
        orchestrator.asset_service = Mock()
        return orchestrator
    
    @pytest.mark.asyncio
    async def test_multi_cloud_discovery_pipeline(self, orchestrator):
        """Test discovery pipeline across multiple cloud providers."""
        # Mock discovery job
        job = Mock()
        job.id = "job-123"
        job.job_type = "aws_discovery"
        job.configuration = {
            "provider": "aws",
            "access_key_id": "test_key",
            "secret_access_key": "test_secret",
            "regions": ["us-east-1", "us-west-2"],
            "resource_types": ["ec2", "s3", "rds"]
        }
        
        orchestrator.asset_service.get_discovery_job.return_value = job
        orchestrator.asset_service.start_discovery_job.return_value = None
        orchestrator.asset_service.complete_discovery_job.return_value = None
        orchestrator.asset_service.create_asset.return_value = Mock(id="asset-123")
        orchestrator.asset_service.create_relationship.return_value = Mock(id="rel-123")
        
        # Mock AWS discovery results
        with patch('app.services.discovery.cloud_discovery.CloudDiscoveryEngine.discover') as mock_discover:
            mock_discover.return_value = DiscoveryResult(
                assets_discovered=[
                    {
                        "name": "prod-web-01",
                        "asset_type": AssetType.CLOUD_INSTANCE,
                        "provider": AssetProvider.AWS,
                        "provider_id": "i-1234567890abcdef0",
                        "provider_region": "us-east-1",
                        "ip_addresses": ["***********", "*********"],
                        "environment": "production",
                        "risk_score": 75,
                        "risk_level": RiskLevel.HIGH,
                        "discovery_source": DiscoverySource.AWS_API,
                        "configuration": {
                            "instance_type": "m5.large",
                            "vpc_id": "vpc-12345678",
                            "security_groups": ["sg-web", "sg-ssh"]
                        }
                    },
                    {
                        "name": "app-data-bucket",
                        "asset_type": AssetType.CLOUD_STORAGE,
                        "provider": AssetProvider.AWS,
                        "provider_id": "app-data-bucket",
                        "provider_region": "us-east-1",
                        "environment": "production",
                        "risk_score": 60,
                        "risk_level": RiskLevel.MEDIUM,
                        "discovery_source": DiscoverySource.AWS_API,
                        "configuration": {
                            "bucket_type": "s3",
                            "encryption": "AES256",
                            "public_access": False
                        }
                    }
                ],
                relationships_discovered=[
                    {
                        "source_asset_id": "i-1234567890abcdef0",
                        "target_asset_id": "app-data-bucket",
                        "relationship_type": "stores_data_in",
                        "description": "Web server stores data in S3 bucket"
                    }
                ],
                assets_updated=[],
                errors=[],
                execution_log=[
                    {
                        "timestamp": datetime.utcnow().isoformat(),
                        "event_type": "discovery_started",
                        "message": "AWS discovery started"
                    }
                ]
            )
            
            # Execute discovery
            success = await orchestrator.start_discovery_job(job.id)
            
            # Verify success
            assert success == True
            
            # Verify discovery was called
            mock_discover.assert_called_once()
            
            # Verify assets were processed
            orchestrator.asset_service.create_asset.assert_called()
            orchestrator.asset_service.create_relationship.assert_called()
            orchestrator.asset_service.complete_discovery_job.assert_called()
    
    @pytest.mark.asyncio
    async def test_api_discovery_with_security_analysis(self):
        """Test API discovery with comprehensive security analysis."""
        config = {
            "tool": "akto",
            "target_urls": ["https://api.example.com"],
            "security_analysis": True,
            "compliance_check": True
        }
        
        engine = APIDiscoveryEngine(config)
        
        # Mock Akto discovery with security findings
        with patch.object(engine, '_discover_akto_endpoints') as mock_akto:
            mock_akto.return_value = [
                {
                    "name": "POST /api/v1/auth/login",
                    "asset_type": AssetType.API_ENDPOINT,
                    "provider_id": "https://api.example.com/api/v1/auth/login",
                    "configuration": {
                        "method": "POST",
                        "path": "/api/v1/auth/login",
                        "status_code": 200,
                        "authentication_required": False,  # Security issue
                        "rate_limiting": False,  # Security issue
                        "ssl_enabled": True,
                        "security_headers": {
                            "x-frame-options": "DENY",
                            "x-content-type-options": "nosniff"
                        },
                        "data_classification": "sensitive",
                        "pii_detected": True
                    },
                    "risk_score": 85,
                    "risk_level": RiskLevel.HIGH,
                    "vulnerabilities": [
                        {
                            "type": "authentication_bypass",
                            "severity": "high",
                            "description": "Login endpoint accessible without authentication"
                        },
                        {
                            "type": "rate_limiting_missing",
                            "severity": "medium",
                            "description": "No rate limiting on authentication endpoint"
                        }
                    ]
                },
                {
                    "name": "GET /api/v1/users/{id}",
                    "asset_type": AssetType.API_ENDPOINT,
                    "provider_id": "https://api.example.com/api/v1/users/{id}",
                    "configuration": {
                        "method": "GET",
                        "path": "/api/v1/users/{id}",
                        "status_code": 200,
                        "authentication_required": True,
                        "authorization": "bearer_token",
                        "rate_limiting": True,
                        "ssl_enabled": True,
                        "data_classification": "pii",
                        "gdpr_applicable": True
                    },
                    "risk_score": 45,
                    "risk_level": RiskLevel.MEDIUM,
                    "compliance": {
                        "gdpr": "compliant",
                        "ccpa": "compliant"
                    }
                }
            ]
            
            result = await engine.discover()
            
            # Verify discovery results
            assert result.total_assets == 2
            assert result.total_errors == 0
            
            # Verify security analysis
            high_risk_endpoints = [
                asset for asset in result.assets_discovered 
                if asset["risk_level"] == RiskLevel.HIGH
            ]
            assert len(high_risk_endpoints) == 1
            
            # Verify vulnerability detection
            vulnerable_endpoint = high_risk_endpoints[0]
            assert "vulnerabilities" in vulnerable_endpoint
            assert len(vulnerable_endpoint["vulnerabilities"]) == 2
            
            # Verify compliance tracking
            compliant_endpoints = [
                asset for asset in result.assets_discovered 
                if "compliance" in asset
            ]
            assert len(compliant_endpoints) == 1
    
    @pytest.mark.asyncio
    async def test_network_discovery_with_service_analysis(self):
        """Test network discovery with detailed service analysis."""
        config = {
            "target_networks": ["192.168.1.0/24"],
            "scan_type": "comprehensive",
            "service_detection": True,
            "os_detection": True,
            "vulnerability_scan": True
        }
        
        engine = NetworkDiscoveryEngine(config)
        
        # Mock comprehensive network scan results
        with patch.object(engine, '_scan_network') as mock_scan:
            mock_scan.return_value = (
                [
                    # Database server
                    {
                        "name": "db-primary.internal",
                        "asset_type": AssetType.SERVER,
                        "provider_id": "************",
                        "ip_addresses": ["************"],
                        "dns_names": ["db-primary.internal"],
                        "ports": [22, 5432, 9100],
                        "protocols": ["tcp"],
                        "configuration": {
                            "os_info": {
                                "name": "Ubuntu 20.04.6 LTS",
                                "accuracy": "98"
                            },
                            "services": [
                                {
                                    "port": 22,
                                    "service": "ssh",
                                    "version": "OpenSSH 8.2p1",
                                    "security_level": "secure"
                                },
                                {
                                    "port": 5432,
                                    "service": "postgresql",
                                    "version": "13.12",
                                    "security_level": "secure",
                                    "ssl_enabled": True
                                },
                                {
                                    "port": 9100,
                                    "service": "node_exporter",
                                    "version": "1.6.1",
                                    "security_level": "info"
                                }
                            ],
                            "security_scan": {
                                "firewall_detected": True,
                                "patch_level": "current"
                            }
                        },
                        "risk_score": 35,
                        "risk_level": RiskLevel.MEDIUM,
                        "environment": "production"
                    },
                    # Web server
                    {
                        "name": "web-01.internal",
                        "asset_type": AssetType.SERVER,
                        "provider_id": "************",
                        "ip_addresses": ["************"],
                        "dns_names": ["web-01.internal"],
                        "ports": [22, 80, 443],
                        "protocols": ["tcp"],
                        "configuration": {
                            "os_info": {
                                "name": "Ubuntu 20.04.6 LTS",
                                "accuracy": "98"
                            },
                            "services": [
                                {
                                    "port": 80,
                                    "service": "http",
                                    "version": "nginx 1.18.0",
                                    "security_level": "warning",
                                    "ssl_redirect": True
                                },
                                {
                                    "port": 443,
                                    "service": "https",
                                    "version": "nginx 1.18.0",
                                    "security_level": "secure",
                                    "ssl_version": "TLSv1.3"
                                }
                            ]
                        },
                        "risk_score": 40,
                        "risk_level": RiskLevel.MEDIUM,
                        "environment": "production"
                    }
                ],
                [
                    # Service relationships
                    {
                        "source_asset_id": "************",
                        "target_asset_id": "************",
                        "relationship_type": "depends_on",
                        "protocol": "tcp",
                        "port": 5432,
                        "description": "Web server connects to database"
                    }
                ]
            )
            
            result = await engine.discover()
            
            # Verify discovery results
            assert result.total_assets == 2
            assert result.total_relationships == 1
            assert result.total_errors == 0
            
            # Verify service detection
            db_server = next(
                asset for asset in result.assets_discovered 
                if "db-primary" in asset["name"]
            )
            assert 5432 in db_server["ports"]
            assert len(db_server["configuration"]["services"]) == 3
            
            web_server = next(
                asset for asset in result.assets_discovered 
                if "web-01" in asset["name"]
            )
            assert 443 in web_server["ports"]
            
            # Verify relationship detection
            relationship = result.relationships_discovered[0]
            assert relationship["relationship_type"] == "depends_on"
            assert relationship["port"] == 5432
    
    @pytest.mark.asyncio
    async def test_discovery_error_handling_and_recovery(self, orchestrator):
        """Test discovery error handling and recovery mechanisms."""
        # Mock failing discovery job
        job = Mock()
        job.id = "job-error-123"
        job.job_type = "aws_discovery"
        job.configuration = {"provider": "aws"}
        
        orchestrator.asset_service.get_discovery_job.return_value = job
        orchestrator.asset_service.start_discovery_job.return_value = None
        orchestrator.asset_service.fail_discovery_job.return_value = None
        
        # Mock discovery engine that raises an exception
        with patch('app.services.discovery.cloud_discovery.CloudDiscoveryEngine.discover') as mock_discover:
            mock_discover.side_effect = Exception("AWS API connection failed")
            
            # Execute discovery (should handle error gracefully)
            success = await orchestrator.start_discovery_job(job.id)
            
            # Verify error handling
            assert success == True  # Job starts successfully
            
            # Wait for job to complete (in real scenario, this would be async)
            await asyncio.sleep(0.1)
            
            # Verify error was recorded
            orchestrator.asset_service.fail_discovery_job.assert_called()
    
    @pytest.mark.asyncio
    async def test_discovery_performance_and_scalability(self):
        """Test discovery performance with large datasets."""
        config = {"provider": "aws"}
        engine = CloudDiscoveryEngine(config)
        
        # Simulate large-scale discovery
        large_instance_list = []
        for i in range(100):  # 100 instances
            instance_data = {
                "InstanceId": f"i-{i:016x}",
                "InstanceType": "t3.micro",
                "State": {"Name": "running"},
                "Tags": [
                    {"Key": "Name", "Value": f"instance-{i}"},
                    {"Key": "Environment", "Value": "production" if i % 2 == 0 else "staging"}
                ]
            }
            large_instance_list.append(instance_data)
        
        # Process all instances
        start_time = datetime.utcnow()
        assets = []
        for instance_data in large_instance_list:
            asset_data = engine._create_ec2_asset(instance_data, "us-east-1")
            assets.append(asset_data)
        end_time = datetime.utcnow()
        
        processing_time = (end_time - start_time).total_seconds()
        
        # Verify performance
        assert len(assets) == 100
        assert processing_time < 5.0  # Should process 100 assets in under 5 seconds
        
        # Verify all assets are unique
        provider_ids = [asset["provider_id"] for asset in assets]
        assert len(set(provider_ids)) == 100
        
        # Verify risk distribution
        risk_levels = [asset["risk_level"] for asset in assets]
        assert len(set(risk_levels)) > 1  # Should have varied risk levels
    
    def test_discovery_result_aggregation(self):
        """Test aggregation of discovery results from multiple sources."""
        # Create multiple discovery results
        aws_result = DiscoveryResult(
            assets_discovered=[
                {"name": "aws-instance-1", "provider": AssetProvider.AWS},
                {"name": "aws-bucket-1", "provider": AssetProvider.AWS}
            ],
            relationships_discovered=[
                {"source": "aws-instance-1", "target": "aws-bucket-1"}
            ],
            assets_updated=[],
            errors=[],
            execution_log=[]
        )
        
        api_result = DiscoveryResult(
            assets_discovered=[
                {"name": "api-endpoint-1", "provider": AssetProvider.ON_PREMISES},
                {"name": "api-endpoint-2", "provider": AssetProvider.ON_PREMISES}
            ],
            relationships_discovered=[],
            assets_updated=[],
            errors=[{"error": "timeout"}],
            execution_log=[]
        )
        
        network_result = DiscoveryResult(
            assets_discovered=[
                {"name": "server-1", "provider": AssetProvider.ON_PREMISES}
            ],
            relationships_discovered=[
                {"source": "server-1", "target": "api-endpoint-1"}
            ],
            assets_updated=[],
            errors=[],
            execution_log=[]
        )
        
        # Aggregate results
        total_assets = (
            aws_result.total_assets + 
            api_result.total_assets + 
            network_result.total_assets
        )
        total_relationships = (
            aws_result.total_relationships + 
            api_result.total_relationships + 
            network_result.total_relationships
        )
        total_errors = (
            aws_result.total_errors + 
            api_result.total_errors + 
            network_result.total_errors
        )
        
        # Verify aggregation
        assert total_assets == 5
        assert total_relationships == 2
        assert total_errors == 1
        
        # Calculate overall success rate
        total_operations = total_assets + total_relationships
        overall_success_rate = 1.0 - (total_errors / total_operations)
        assert overall_success_rate > 0.85  # Should be > 85% success rate
