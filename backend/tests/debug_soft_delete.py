"""Debug soft delete functionality."""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

def debug_soft_delete():
    """Debug soft delete functionality."""
    print("🔍 Debugging Soft Delete...")
    
    try:
        from app.db.models.mitre import MitreTechnique, MitreDomain
        
        # Create a technique instance
        technique = MitreTechnique(
            technique_id="T9999",
            name="Debug Test",
            domain=MitreDomain.ENTERPRISE
        )
        
        print(f"Technique created: {technique}")
        print(f"Has soft_delete method: {hasattr(technique, 'soft_delete')}")
        print(f"soft_delete method: {technique.soft_delete}")
        
        # Check method signature
        import inspect
        sig = inspect.signature(technique.soft_delete)
        print(f"Method signature: {sig}")
        
        # Try calling with no arguments
        print("Trying soft_delete() with no arguments...")
        technique.soft_delete()
        print("✅ No arguments worked")
        
        # Reset
        technique.restore()
        
        # Try calling with keyword arguments
        print("Trying soft_delete(deleted_by='test')...")
        technique.soft_delete(deleted_by='test')
        print("✅ Keyword arguments worked")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_soft_delete()
