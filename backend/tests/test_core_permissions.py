"""Tests for core permissions and role-based access control."""

import pytest
from app.core.permissions import (
    UserRole,
    Permission,
    get_role_permissions,
    has_permission,
    get_user_permissions,
    is_admin,
    can_access_resource,
    ROLE_PERMISSIONS,
)


class TestUserRoles:
    """Test user role enumeration."""
    
    def test_user_role_values(self):
        """Test that all expected user roles exist."""
        expected_roles = [
            "admin",
            "soc_operator", 
            "security_architect",
            "red_team_member",
            "purple_team_member",
            "analyst",
            "viewer"
        ]
        
        for role in expected_roles:
            assert hasattr(UserRole, role.upper())
            assert UserRole[role.upper()].value == role
    
    def test_user_role_string_representation(self):
        """Test user role string representation."""
        assert str(UserRole.ADMIN) == "admin"
        assert str(UserRole.SOC_OPERATOR) == "soc_operator"
        assert str(UserRole.SECURITY_ARCHITECT) == "security_architect"


class TestPermissions:
    """Test permission enumeration."""
    
    def test_permission_categories(self):
        """Test that permissions are properly categorized."""
        # User management permissions
        user_permissions = [
            Permission.CREATE_USER,
            Permission.READ_USER,
            Permission.UPDATE_USER,
            Permission.DELETE_USER,
            Permission.MANAGE_USER_ROLES,
        ]
        
        for perm in user_permissions:
            assert perm.value.startswith(("create_user", "read_user", "update_user", "delete_user", "manage_user"))
        
        # Asset management permissions
        asset_permissions = [
            Permission.CREATE_ASSET,
            Permission.READ_ASSET,
            Permission.UPDATE_ASSET,
            Permission.DELETE_ASSET,
        ]
        
        for perm in asset_permissions:
            assert "asset" in perm.value
    
    def test_permission_string_values(self):
        """Test permission string values."""
        assert Permission.CREATE_USER.value == "create_user"
        assert Permission.VIEW_SECURITY_EVENTS.value == "view_security_events"
        assert Permission.MANAGE_SYSTEM_CONFIG.value == "manage_system_config"


class TestRolePermissions:
    """Test role permission mappings."""
    
    def test_admin_has_all_permissions(self):
        """Test that admin role has all permissions."""
        admin_permissions = get_role_permissions(UserRole.ADMIN)
        
        # Admin should have a comprehensive set of permissions
        assert len(admin_permissions) > 30  # Should have most permissions
        
        # Check for key admin permissions
        key_permissions = [
            Permission.CREATE_USER,
            Permission.DELETE_USER,
            Permission.MANAGE_USER_ROLES,
            Permission.MANAGE_SYSTEM_CONFIG,
            Permission.VIEW_AUDIT_LOGS,
        ]
        
        for perm in key_permissions:
            assert perm in admin_permissions
    
    def test_soc_operator_permissions(self):
        """Test SOC operator permissions."""
        soc_permissions = get_role_permissions(UserRole.SOC_OPERATOR)
        
        # Should have monitoring and incident response permissions
        expected_permissions = [
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.UPDATE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
        ]
        
        for perm in expected_permissions:
            assert perm in soc_permissions
        
        # Should NOT have admin permissions
        admin_only_permissions = [
            Permission.DELETE_USER,
            Permission.MANAGE_SYSTEM_CONFIG,
        ]
        
        for perm in admin_only_permissions:
            assert perm not in soc_permissions
    
    def test_security_architect_permissions(self):
        """Test security architect permissions."""
        architect_permissions = get_role_permissions(UserRole.SECURITY_ARCHITECT)
        
        # Should have design and configuration permissions
        expected_permissions = [
            Permission.CREATE_ASSET,
            Permission.UPDATE_ASSET,
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.MANAGE_THREAT_INTEL,
            Permission.CREATE_DASHBOARD,
            Permission.APPROVE_REMEDIATION,
        ]
        
        for perm in expected_permissions:
            assert perm in architect_permissions
    
    def test_red_team_permissions(self):
        """Test red team member permissions."""
        red_team_permissions = get_role_permissions(UserRole.RED_TEAM_MEMBER)
        
        # Should have attack simulation permissions
        expected_permissions = [
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.RUN_ATTACK_SIMULATION,
            Permission.EXPORT_ATTACK_PATHS,
        ]
        
        for perm in expected_permissions:
            assert perm in red_team_permissions
        
        # Should NOT have administrative permissions
        assert Permission.DELETE_USER not in red_team_permissions
        assert Permission.MANAGE_SYSTEM_CONFIG not in red_team_permissions
    
    def test_purple_team_permissions(self):
        """Test purple team member permissions."""
        purple_team_permissions = get_role_permissions(UserRole.PURPLE_TEAM_MEMBER)
        
        # Should have both defensive and offensive permissions
        expected_permissions = [
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.RUN_ATTACK_SIMULATION,
            Permission.EXECUTE_REMEDIATION,
        ]
        
        for perm in expected_permissions:
            assert perm in purple_team_permissions
    
    def test_analyst_permissions(self):
        """Test analyst permissions."""
        analyst_permissions = get_role_permissions(UserRole.ANALYST)
        
        # Should have read and analysis permissions
        expected_permissions = [
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
        ]
        
        for perm in expected_permissions:
            assert perm in analyst_permissions
        
        # Should NOT have write permissions
        write_permissions = [
            Permission.CREATE_USER,
            Permission.UPDATE_USER,
            Permission.DELETE_USER,
            Permission.CREATE_INCIDENT,
        ]
        
        for perm in write_permissions:
            assert perm not in analyst_permissions
    
    def test_viewer_permissions(self):
        """Test viewer permissions."""
        viewer_permissions = get_role_permissions(UserRole.VIEWER)
        
        # Should only have read permissions
        expected_permissions = [
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
            Permission.VIEW_REPORTS,
        ]
        
        for perm in expected_permissions:
            assert perm in viewer_permissions
        
        # Should NOT have any write permissions
        write_permissions = [
            Permission.CREATE_USER,
            Permission.UPDATE_USER,
            Permission.CREATE_INCIDENT,
            Permission.CREATE_REPORTS,
        ]
        
        for perm in write_permissions:
            assert perm not in viewer_permissions


class TestPermissionChecking:
    """Test permission checking functions."""
    
    def test_has_permission_single_role(self):
        """Test permission checking with single role."""
        user_roles = [UserRole.SOC_OPERATOR]
        
        # Should have SOC operator permissions
        assert has_permission(user_roles, Permission.VIEW_SECURITY_EVENTS) is True
        assert has_permission(user_roles, Permission.CREATE_INCIDENT) is True
        
        # Should NOT have admin permissions
        assert has_permission(user_roles, Permission.DELETE_USER) is False
        assert has_permission(user_roles, Permission.MANAGE_SYSTEM_CONFIG) is False
    
    def test_has_permission_multiple_roles(self):
        """Test permission checking with multiple roles."""
        user_roles = [UserRole.ANALYST, UserRole.SOC_OPERATOR]
        
        # Should have permissions from both roles
        assert has_permission(user_roles, Permission.VIEW_REPORTS) is True  # From analyst
        assert has_permission(user_roles, Permission.CREATE_INCIDENT) is True  # From SOC operator
        
        # Should still NOT have admin permissions
        assert has_permission(user_roles, Permission.DELETE_USER) is False
    
    def test_has_permission_admin_role(self):
        """Test that admin role has all permissions."""
        user_roles = [UserRole.ADMIN]
        
        # Test various permissions
        test_permissions = [
            Permission.CREATE_USER,
            Permission.DELETE_USER,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.MANAGE_SYSTEM_CONFIG,
            Permission.CREATE_ATTACK_SCENARIO,
        ]
        
        for perm in test_permissions:
            assert has_permission(user_roles, perm) is True
    
    def test_has_permission_empty_roles(self):
        """Test permission checking with no roles."""
        user_roles = []
        
        assert has_permission(user_roles, Permission.READ_USER) is False
        assert has_permission(user_roles, Permission.VIEW_DASHBOARD) is False
    
    def test_get_user_permissions(self):
        """Test getting all permissions for user roles."""
        user_roles = [UserRole.ANALYST, UserRole.SOC_OPERATOR]
        permissions = get_user_permissions(user_roles)
        
        assert isinstance(permissions, set)
        assert len(permissions) > 0
        
        # Should include permissions from both roles
        assert Permission.VIEW_REPORTS in permissions  # From analyst
        assert Permission.CREATE_INCIDENT in permissions  # From SOC operator
    
    def test_is_admin(self):
        """Test admin role checking."""
        # Admin user
        admin_roles = [UserRole.ADMIN]
        assert is_admin(admin_roles) is True
        
        # Non-admin user
        user_roles = [UserRole.SOC_OPERATOR]
        assert is_admin(user_roles) is False
        
        # Multiple roles including admin
        mixed_roles = [UserRole.ANALYST, UserRole.ADMIN]
        assert is_admin(mixed_roles) is True
        
        # Empty roles
        empty_roles = []
        assert is_admin(empty_roles) is False


class TestResourceAccess:
    """Test resource access checking."""
    
    def test_can_access_user_resource(self):
        """Test user resource access."""
        admin_roles = [UserRole.ADMIN]
        soc_roles = [UserRole.SOC_OPERATOR]
        viewer_roles = [UserRole.VIEWER]
        
        # Admin can do everything
        assert can_access_resource(admin_roles, "user", "create") is True
        assert can_access_resource(admin_roles, "user", "read") is True
        assert can_access_resource(admin_roles, "user", "update") is True
        assert can_access_resource(admin_roles, "user", "delete") is True
        
        # SOC operator can read but not create/delete users
        assert can_access_resource(soc_roles, "user", "read") is True
        assert can_access_resource(soc_roles, "user", "create") is False
        assert can_access_resource(soc_roles, "user", "delete") is False
        
        # Viewer can only read
        assert can_access_resource(viewer_roles, "user", "read") is True
        assert can_access_resource(viewer_roles, "user", "create") is False
        assert can_access_resource(viewer_roles, "user", "update") is False
        assert can_access_resource(viewer_roles, "user", "delete") is False
    
    def test_can_access_asset_resource(self):
        """Test asset resource access."""
        architect_roles = [UserRole.SECURITY_ARCHITECT]
        analyst_roles = [UserRole.ANALYST]
        
        # Security architect can manage assets
        assert can_access_resource(architect_roles, "asset", "create") is True
        assert can_access_resource(architect_roles, "asset", "read") is True
        assert can_access_resource(architect_roles, "asset", "update") is True
        
        # Analyst can only read assets
        assert can_access_resource(analyst_roles, "asset", "read") is True
        assert can_access_resource(analyst_roles, "asset", "create") is False
        assert can_access_resource(analyst_roles, "asset", "update") is False
    
    def test_can_access_incident_resource(self):
        """Test incident resource access."""
        soc_roles = [UserRole.SOC_OPERATOR]
        purple_roles = [UserRole.PURPLE_TEAM_MEMBER]
        viewer_roles = [UserRole.VIEWER]
        
        # SOC operator can manage incidents
        assert can_access_resource(soc_roles, "incident", "create") is True
        assert can_access_resource(soc_roles, "incident", "read") is True
        assert can_access_resource(soc_roles, "incident", "update") is True
        
        # Purple team can manage incidents
        assert can_access_resource(purple_roles, "incident", "create") is True
        assert can_access_resource(purple_roles, "incident", "update") is True
        
        # Viewer can only read
        assert can_access_resource(viewer_roles, "incident", "read") is True
        assert can_access_resource(viewer_roles, "incident", "create") is False
    
    def test_can_access_unknown_resource(self):
        """Test access to unknown resource."""
        admin_roles = [UserRole.ADMIN]
        
        # Unknown resource should return False
        assert can_access_resource(admin_roles, "unknown", "read") is False
        assert can_access_resource(admin_roles, "user", "unknown_action") is False


class TestRoleHierarchy:
    """Test role hierarchy and inheritance."""
    
    def test_role_permissions_configuration(self):
        """Test that all roles are properly configured."""
        for role in UserRole:
            role_config = ROLE_PERMISSIONS.get(role)
            assert role_config is not None
            assert role_config.role == role
            assert isinstance(role_config.permissions, set)
            assert len(role_config.description) > 0
    
    def test_permission_coverage(self):
        """Test that important permissions are covered by roles."""
        all_permissions = set()
        for role_config in ROLE_PERMISSIONS.values():
            all_permissions.update(role_config.permissions)
        
        # Check that key permissions are assigned to at least one role
        key_permissions = [
            Permission.CREATE_USER,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_DASHBOARD,
        ]
        
        for perm in key_permissions:
            assert perm in all_permissions, f"Permission {perm} not assigned to any role"
