"""
Integration validation tests for multi-cloud discovery services.

This test suite validates the actual discovery service implementations
by importing and testing them with mocked dependencies.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from typing import Dict, List, Any
import importlib.util


def test_discovery_module_structure():
    """Test that discovery module structure is correct."""
    discovery_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'services', 'discovery')
    
    # Check that discovery directory exists
    assert os.path.exists(discovery_path), "Discovery services directory not found"
    
    # Check required files exist
    required_files = [
        '__init__.py',
        'base_discovery.py',
        'cloud_discovery.py',
        'azure_discovery.py',
        'gcp_discovery.py',
        'api_discovery.py',
        'network_discovery.py'
    ]
    
    for file_name in required_files:
        file_path = os.path.join(discovery_path, file_name)
        assert os.path.exists(file_path), f"Required file not found: {file_name}"
        
        # Check file is not empty
        with open(file_path, 'r') as f:
            content = f.read().strip()
            assert len(content) > 0, f"File is empty: {file_name}"


def test_base_discovery_file_structure():
    """Test base discovery file structure and content."""
    base_discovery_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', 'base_discovery.py'
    )
    
    with open(base_discovery_path, 'r') as f:
        content = f.read()
    
    # Check for required classes and functions
    required_elements = [
        'class DiscoveryResult',
        'class BaseDiscoveryEngine',
        'def validate_config',
        'def discover',
        'def _calculate_base_risk_score',
        'def _get_risk_level_from_score',
        'def _create_asset_data',
        'def _create_relationship_data'
    ]
    
    for element in required_elements:
        assert element in content, f"Missing required element in base_discovery.py: {element}"
    
    # Check for proper imports
    required_imports = [
        'from abc import ABC, abstractmethod',
        'from typing import Dict, List, Optional, Any, Set',
        'from datetime import datetime',
        'from dataclasses import dataclass, field'
    ]
    
    for import_stmt in required_imports:
        assert import_stmt in content, f"Missing required import in base_discovery.py: {import_stmt}"


def test_azure_discovery_file_structure():
    """Test Azure discovery file structure and content."""
    azure_discovery_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', 'azure_discovery.py'
    )
    
    with open(azure_discovery_path, 'r') as f:
        content = f.read()
    
    # Check for required classes and methods
    required_elements = [
        'class AzureDiscoveryService',
        'def __init__',
        'def validate_config',
        'def discover',
        'def _discover_virtual_machines',
        'def _discover_storage_accounts',
        'def _discover_sql_databases',
        'def _discover_web_apps',
        'def _discover_container_services',
        'def _discover_network_resources',
        'def _calculate_vm_risk_score',
        'def _calculate_storage_risk_score',
        'def _calculate_sql_risk_score'
    ]
    
    for element in required_elements:
        assert element in content, f"Missing required element in azure_discovery.py: {element}"
    
    # Check for Azure SDK imports (with try/except)
    assert 'try:' in content and 'from azure.identity import' in content
    assert 'AZURE_AVAILABLE = True' in content
    assert 'except ImportError:' in content
    assert 'AZURE_AVAILABLE = False' in content


def test_gcp_discovery_file_structure():
    """Test GCP discovery file structure and content."""
    gcp_discovery_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', 'gcp_discovery.py'
    )
    
    with open(gcp_discovery_path, 'r') as f:
        content = f.read()
    
    # Check for required classes and methods
    required_elements = [
        'class GCPDiscoveryService',
        'def __init__',
        'def validate_config',
        'def discover',
        'def _discover_compute_instances',
        'def _discover_storage_buckets',
        'def _discover_sql_instances',
        'def _discover_gke_clusters',
        'def _calculate_compute_risk_score',
        'def _calculate_storage_risk_score',
        'def _calculate_sql_risk_score'
    ]
    
    for element in required_elements:
        assert element in content, f"Missing required element in gcp_discovery.py: {element}"
    
    # Check for GCP SDK imports (with try/except)
    assert 'try:' in content and 'from google.cloud import' in content
    assert 'GCP_AVAILABLE = True' in content
    assert 'except ImportError:' in content
    assert 'GCP_AVAILABLE = False' in content


def test_api_discovery_file_structure():
    """Test API discovery file structure and content."""
    api_discovery_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', 'api_discovery.py'
    )
    
    with open(api_discovery_path, 'r') as f:
        content = f.read()
    
    # Check for required classes and methods
    required_elements = [
        'class APIDiscoveryEngine',
        'def __init__',
        'def validate_config',
        'def discover',
        'def _discover_with_akto',
        'def _discover_with_kiterunner',
        'def _discover_with_custom_methods',
        'def _discover_openapi_specs',
        'def _calculate_endpoint_risk_score'
    ]
    
    for element in required_elements:
        assert element in content, f"Missing required element in api_discovery.py: {element}"


def test_network_discovery_file_structure():
    """Test Network discovery file structure and content."""
    network_discovery_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', 'network_discovery.py'
    )
    
    with open(network_discovery_path, 'r') as f:
        content = f.read()
    
    # Check for required classes and methods
    required_elements = [
        'class NetworkDiscoveryEngine',
        'def __init__',
        'def validate_config',
        'def discover',
        'def _discover_with_nmap',
        'def _discover_with_masscan',
        'def _discover_with_custom_scan',
        'def _ping_sweep',
        'def _port_scan',
        'def _calculate_host_risk_score'
    ]
    
    for element in required_elements:
        assert element in content, f"Missing required element in network_discovery.py: {element}"


def test_cloud_discovery_file_structure():
    """Test Cloud discovery file structure and content."""
    cloud_discovery_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', 'cloud_discovery.py'
    )
    
    with open(cloud_discovery_path, 'r') as f:
        content = f.read()
    
    # Check for required classes and methods
    required_elements = [
        'class CloudDiscoveryEngine',
        'def __init__',
        'def validate_config',
        'def discover',
        'def _create_provider_engine',
        'class AWSDiscoveryService',
        'class APIDiscoveryEngine',
        'class NetworkDiscoveryEngine'
    ]
    
    for element in required_elements:
        assert element in content, f"Missing required element in cloud_discovery.py: {element}"


def test_discovery_init_file():
    """Test discovery __init__.py file structure."""
    init_path = os.path.join(
        os.path.dirname(__file__), '..', 'app', 'services', 'discovery', '__init__.py'
    )
    
    with open(init_path, 'r') as f:
        content = f.read()
    
    # Check for required imports
    required_imports = [
        'from .base_discovery import BaseDiscoveryEngine, DiscoveryResult',
        'from .cloud_discovery import CloudDiscoveryEngine',
        'from .api_discovery import APIDiscoveryEngine',
        'from .network_discovery import NetworkDiscoveryEngine',
        'from .azure_discovery import AzureDiscoveryService',
        'from .gcp_discovery import GCPDiscoveryService'
    ]
    
    for import_stmt in required_imports:
        assert import_stmt in content, f"Missing required import in __init__.py: {import_stmt}"
    
    # Check for __all__ definition
    assert '__all__ = [' in content, "Missing __all__ definition in __init__.py"


def test_docstring_quality():
    """Test that all discovery files have proper docstrings."""
    discovery_files = [
        'base_discovery.py',
        'cloud_discovery.py',
        'azure_discovery.py',
        'gcp_discovery.py',
        'api_discovery.py',
        'network_discovery.py'
    ]
    
    discovery_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'services', 'discovery')
    
    for file_name in discovery_files:
        file_path = os.path.join(discovery_path, file_name)
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for module docstring
        lines = content.split('\n')
        found_module_docstring = False
        for i, line in enumerate(lines[:10]):  # Check first 10 lines
            if '"""' in line and i < 5:  # Module docstring should be near the top
                found_module_docstring = True
                break
        
        assert found_module_docstring, f"Missing module docstring in {file_name}"
        
        # Check for class docstrings
        class_lines = [i for i, line in enumerate(lines) if line.strip().startswith('class ')]
        for class_line in class_lines:
            # Look for docstring in next few lines
            found_class_docstring = False
            for i in range(class_line + 1, min(class_line + 10, len(lines))):
                if '"""' in lines[i]:
                    found_class_docstring = True
                    break
            
            class_name = lines[class_line].split('class ')[1].split('(')[0].split(':')[0]
            assert found_class_docstring, f"Missing class docstring for {class_name} in {file_name}"


def test_type_hints_presence():
    """Test that discovery files have proper type hints."""
    discovery_files = [
        'base_discovery.py',
        'azure_discovery.py',
        'gcp_discovery.py',
        'api_discovery.py',
        'network_discovery.py'
    ]
    
    discovery_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'services', 'discovery')
    
    for file_name in discovery_files:
        file_path = os.path.join(discovery_path, file_name)
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for typing imports
        typing_imports = [
            'from typing import Dict',
            'from typing import List',
            'from typing import Optional',
            'from typing import Any'
        ]
        
        has_typing_import = any(import_stmt in content for import_stmt in typing_imports)
        assert has_typing_import, f"Missing typing imports in {file_name}"
        
        # Check for type hints in method definitions
        method_lines = [line for line in content.split('\n') if 'def ' in line and '(' in line]
        typed_methods = [line for line in method_lines if '->' in line]
        
        # At least 50% of methods should have return type hints
        if method_lines:
            type_hint_ratio = len(typed_methods) / len(method_lines)
            assert type_hint_ratio >= 0.5, f"Insufficient type hints in {file_name}: {type_hint_ratio:.1%}"


def test_error_handling_patterns():
    """Test that discovery files have proper error handling."""
    discovery_files = [
        'azure_discovery.py',
        'gcp_discovery.py',
        'api_discovery.py',
        'network_discovery.py'
    ]
    
    discovery_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'services', 'discovery')
    
    for file_name in discovery_files:
        file_path = os.path.join(discovery_path, file_name)
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for try/except blocks
        assert 'try:' in content, f"Missing try/except blocks in {file_name}"
        assert 'except' in content, f"Missing except clauses in {file_name}"
        
        # Check for logging
        assert 'logger' in content, f"Missing logger usage in {file_name}"
        assert 'logging.getLogger' in content, f"Missing logger initialization in {file_name}"
        
        # Check for proper exception handling
        if 'azure' in file_name:
            assert 'ClientAuthenticationError' in content, f"Missing Azure-specific exception handling in {file_name}"
        elif 'gcp' in file_name:
            assert 'gcp_exceptions' in content or 'exceptions' in content, f"Missing GCP-specific exception handling in {file_name}"


def test_async_patterns():
    """Test that discovery files use proper async patterns."""
    discovery_files = [
        'base_discovery.py',
        'azure_discovery.py',
        'gcp_discovery.py',
        'api_discovery.py',
        'network_discovery.py'
    ]
    
    discovery_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'services', 'discovery')
    
    for file_name in discovery_files:
        file_path = os.path.join(discovery_path, file_name)
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for async/await usage
        assert 'async def' in content, f"Missing async methods in {file_name}"
        
        # Check for asyncio imports where needed
        if 'asyncio' in content:
            assert 'import asyncio' in content, f"Missing asyncio import in {file_name}"


if __name__ == "__main__":
    # Run tests manually
    test_functions = [
        test_discovery_module_structure,
        test_base_discovery_file_structure,
        test_azure_discovery_file_structure,
        test_gcp_discovery_file_structure,
        test_api_discovery_file_structure,
        test_network_discovery_file_structure,
        test_cloud_discovery_file_structure,
        test_discovery_init_file,
        test_docstring_quality,
        test_type_hints_presence,
        test_error_handling_patterns,
        test_async_patterns
    ]
    
    total_tests = len(test_functions)
    passed_tests = 0
    
    for test_func in test_functions:
        try:
            test_func()
            print(f"✅ {test_func.__name__}")
            passed_tests += 1
        except Exception as e:
            print(f"❌ {test_func.__name__}: {e}")
    
    print(f"\n📊 Integration Test Results: {passed_tests}/{total_tests} passed ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 All integration tests passed!")
    else:
        print(f"⚠️  {total_tests - passed_tests} integration tests failed")
