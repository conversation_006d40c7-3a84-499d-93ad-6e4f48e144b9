"""Integration tests for Pydantic schemas with MITRE service and database models."""

import pytest
import uuid
from datetime import datetime
from unittest.mock import AsyncMock, patch
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import ValidationError

from app.services.enhanced_mitre_service import EnhancedMitreService
from app.db.models.mitre import (
    MitreTechnique,
    MitreTactic,
    MitreGroup,
    MitreDomain,
    MitreEntityStatus,
    MitreDataSource as MitreDataSourceEnum,
)
from app.schemas.mitre import (
    MitreTechniqueCreate,
    MitreTechniqueUpdate,
    MitreTechniqueResponse,
    MitreTacticCreate,
    MitreTacticResponse,
    MitreGroupCreate,
    MitreGroupResponse,
    MitreDataSyncCreate,
    MitreDataSyncResponse,
    MitreSearchRequest,
    MitreSearchResponse,
)


class TestPydanticDatabaseIntegration:
    """Test integration between Pydantic schemas and database models."""
    
    @pytest.fixture
    async def mitre_service(self, async_db: AsyncSession):
        """Create MITRE service instance."""
        return EnhancedMitreService(db=async_db)
    
    @pytest.mark.asyncio
    async def test_technique_model_to_schema_conversion(self, async_db: AsyncSession):
        """Test converting database model to Pydantic schema."""
        # Create database model
        technique_model = MitreTechnique(
            technique_id="T1234",
            name="Test Technique",
            description="Test description",
            domain=MitreDomain.ENTERPRISE,
            status=MitreEntityStatus.ACTIVE,
            platforms=["Windows", "Linux"],
            data_sources=["Process monitoring"],
            version="1.0",
            data_source=MitreDataSourceEnum.OFFICIAL
        )
        
        async_db.add(technique_model)
        await async_db.commit()
        await async_db.refresh(technique_model)
        
        # Convert to Pydantic schema
        technique_schema = MitreTechniqueResponse.model_validate(technique_model)
        
        assert technique_schema.technique_id == "T1234"
        assert technique_schema.name == "Test Technique"
        assert technique_schema.domain == MitreDomain.ENTERPRISE
        assert technique_schema.platforms == ["Windows", "Linux"]
        assert technique_schema.is_subtechnique is False
        assert isinstance(technique_schema.id, uuid.UUID)
        assert isinstance(technique_schema.created_date, datetime)
    
    @pytest.mark.asyncio
    async def test_schema_to_model_creation(self, async_db: AsyncSession):
        """Test creating database model from Pydantic schema."""
        # Create Pydantic schema
        technique_create = MitreTechniqueCreate(
            technique_id="T5678",
            name="Schema Test Technique",
            description="Created from schema",
            domain=MitreDomain.ENTERPRISE,
            platforms=["macOS", "Linux"],
            data_sources=["File monitoring"]
        )
        
        # Create database model from schema
        technique_model = MitreTechnique(
            **technique_create.model_dump(),
            id=uuid.uuid4(),
            version="1.0",
            data_source=MitreDataSourceEnum.OFFICIAL
        )
        
        async_db.add(technique_model)
        await async_db.commit()
        await async_db.refresh(technique_model)
        
        assert technique_model.technique_id == "T5678"
        assert technique_model.name == "Schema Test Technique"
        assert technique_model.platforms == ["macOS", "Linux"]
    
    @pytest.mark.asyncio
    async def test_schema_validation_with_database_constraints(self, async_db: AsyncSession):
        """Test that Pydantic validation aligns with database constraints."""
        # Test unique constraint validation
        technique1 = MitreTechnique(
            technique_id="T9999",
            name="First Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(technique1)
        await async_db.commit()
        
        # Pydantic should allow creating schema with same ID
        technique_create = MitreTechniqueCreate(
            technique_id="T9999",
            name="Duplicate Technique",
            domain=MitreDomain.ENTERPRISE
        )
        
        # But database should reject it
        technique2 = MitreTechnique(**technique_create.model_dump())
        async_db.add(technique2)
        
        with pytest.raises(Exception):  # Database integrity error
            await async_db.commit()
    
    @pytest.mark.asyncio
    async def test_subtechnique_schema_database_consistency(self, async_db: AsyncSession):
        """Test sub-technique validation consistency between schema and database."""
        # Create parent technique
        parent = MitreTechnique(
            technique_id="T1000",
            name="Parent Technique",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(parent)
        
        # Create sub-technique using schema
        subtechnique_create = MitreTechniqueCreate(
            technique_id="T1000.001",
            name="Sub-technique",
            domain=MitreDomain.ENTERPRISE,
            is_subtechnique=True,
            parent_technique_id="T1000"
        )
        
        # Convert to database model
        subtechnique = MitreTechnique(**subtechnique_create.model_dump())
        async_db.add(subtechnique)
        await async_db.commit()
        await async_db.refresh(subtechnique)
        
        # Verify consistency
        assert subtechnique.is_subtechnique is True
        assert subtechnique.parent_technique_id == "T1000"
        assert subtechnique.technique_id == "T1000.001"
    
    @pytest.mark.asyncio
    async def test_update_schema_partial_updates(self, async_db: AsyncSession):
        """Test partial updates using update schemas."""
        # Create initial technique
        technique = MitreTechnique(
            technique_id="T2000",
            name="Original Name",
            description="Original description",
            domain=MitreDomain.ENTERPRISE,
            platforms=["Windows"]
        )
        async_db.add(technique)
        await async_db.commit()
        await async_db.refresh(technique)
        
        # Create update schema with partial data
        update_data = MitreTechniqueUpdate(
            name="Updated Name",
            platforms=["Windows", "Linux", "macOS"]
            # description intentionally not updated
        )
        
        # Apply updates
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(technique, field, value)
        
        await async_db.commit()
        await async_db.refresh(technique)
        
        # Verify updates
        assert technique.name == "Updated Name"
        assert technique.platforms == ["Windows", "Linux", "macOS"]
        assert technique.description == "Original description"  # Unchanged
    
    @pytest.mark.asyncio
    async def test_list_response_schema_with_pagination(self, async_db: AsyncSession):
        """Test list response schemas with database pagination."""
        # Create multiple techniques
        techniques = []
        for i in range(25):
            technique = MitreTechnique(
                technique_id=f"T{3000 + i}",
                name=f"Pagination Test Technique {i}",
                domain=MitreDomain.ENTERPRISE
            )
            techniques.append(technique)
        
        async_db.add_all(techniques)
        await async_db.commit()
        
        # Query with pagination
        result = await async_db.execute(
            select(MitreTechnique)
            .where(MitreTechnique.technique_id.like("T30%"))
            .limit(10)
            .offset(5)
        )
        page_techniques = result.scalars().all()
        
        # Convert to response schemas
        technique_responses = [
            MitreTechniqueResponse.model_validate(t) for t in page_techniques
        ]
        
        # Create list response
        from app.schemas.mitre import MitreTechniqueListResponse
        list_response = MitreTechniqueListResponse(
            techniques=technique_responses,
            total=25,
            page=2,
            size=10
        )
        
        assert len(list_response.techniques) == 10
        assert list_response.total == 25
        assert list_response.page == 2
        assert all(isinstance(t, MitreTechniqueResponse) for t in list_response.techniques)


class TestServiceSchemaIntegration:
    """Test integration between enhanced MITRE service and Pydantic schemas."""
    
    @pytest.fixture
    async def mitre_service(self, async_db: AsyncSession):
        """Create MITRE service instance."""
        return EnhancedMitreService(db=async_db)
    
    @pytest.mark.asyncio
    async def test_sync_service_with_response_schema(self, mitre_service, async_db):
        """Test sync service returning proper response schema."""
        mock_data = {
            "type": "bundle",
            "objects": [
                {
                    "type": "attack-pattern",
                    "id": "attack-pattern--test",
                    "name": "Service Test Technique",
                    "external_references": [
                        {
                            "source_name": "mitre-attack",
                            "external_id": "T4000",
                            "url": "https://attack.mitre.org/techniques/T4000"
                        }
                    ]
                }
            ]
        }
        
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = mock_data
            
            # Service should return proper schema
            result = await mitre_service.sync_domain_data(
                domain=MitreDomain.ENTERPRISE,
                force_update=True
            )
            
            # Verify result is proper schema
            assert isinstance(result, MitreDataSyncResponse)
            assert result.domain == MitreDomain.ENTERPRISE
            assert result.status == "completed"
            assert result.processed_entities > 0
    
    @pytest.mark.asyncio
    async def test_search_functionality_with_schemas(self, mitre_service, async_db):
        """Test search functionality using request/response schemas."""
        # Create test data
        techniques = [
            MitreTechnique(
                technique_id=f"T{5000 + i}",
                name=f"Search Test {i}",
                description="lateral movement technique",
                domain=MitreDomain.ENTERPRISE,
                search_vector=f"search test {i} lateral movement"
            )
            for i in range(5)
        ]
        
        async_db.add_all(techniques)
        await async_db.commit()
        
        # Create search request schema
        search_request = MitreSearchRequest(
            query="lateral movement",
            domains=[MitreDomain.ENTERPRISE],
            entity_types=["technique"],
            limit=10,
            offset=0
        )
        
        # Perform search (simulated)
        result = await async_db.execute(
            select(MitreTechnique).where(
                MitreTechnique.search_vector.contains(search_request.query.lower())
            ).limit(search_request.limit)
        )
        search_results = result.scalars().all()
        
        # Create response schema
        search_response = MitreSearchResponse(
            results=[
                {
                    "id": t.technique_id,
                    "name": t.name,
                    "type": "technique",
                    "description": t.description
                }
                for t in search_results
            ],
            total=len(search_results),
            query=search_request.query,
            execution_time_ms=25.5
        )
        
        assert len(search_response.results) == 5
        assert search_response.query == "lateral movement"
        assert search_response.execution_time_ms == 25.5
    
    @pytest.mark.asyncio
    async def test_relationship_data_in_schemas(self, async_db):
        """Test handling relationship data in schemas."""
        # Create tactic
        tactic = MitreTactic(
            tactic_id="TA5000",
            name="Schema Test Tactic",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(tactic)
        
        # Create technique with relationship
        technique = MitreTechnique(
            technique_id="T5001",
            name="Relationship Test Technique",
            domain=MitreDomain.ENTERPRISE
        )
        technique.tactics.append(tactic)
        async_db.add(technique)
        
        await async_db.commit()
        await async_db.refresh(technique)
        await async_db.refresh(tactic)
        
        # Convert to response schema
        technique_response = MitreTechniqueResponse.model_validate(technique)
        tactic_response = MitreTacticResponse.model_validate(tactic)
        
        # Verify schema conversion preserves core data
        assert technique_response.technique_id == "T5001"
        assert tactic_response.tactic_id == "TA5000"
        
        # Note: Relationship data would need to be handled separately
        # or through custom serialization methods


class TestSchemaValidationEdgeCases:
    """Test edge cases and error handling in schema validation."""
    
    def test_technique_id_edge_cases(self):
        """Test technique ID validation edge cases."""
        # Test boundary values
        valid_ids = ["T0001", "T9999", "T1234.001", "T9999.999"]
        
        for technique_id in valid_ids:
            technique = MitreTechniqueCreate(
                technique_id=technique_id,
                name="Test",
                domain=MitreDomain.ENTERPRISE
            )
            assert technique.technique_id == technique_id.upper()
    
    def test_platform_validation_comprehensive(self):
        """Test comprehensive platform validation."""
        # Test all valid platforms
        all_valid_platforms = [
            "Windows", "Linux", "macOS", "Android", "iOS",
            "Network", "PRE", "Office 365", "Azure AD", "Google Workspace",
            "SaaS", "IaaS", "Containers", "Kubernetes"
        ]
        
        technique = MitreTechniqueCreate(
            technique_id="T1234",
            name="Platform Test",
            domain=MitreDomain.ENTERPRISE,
            platforms=all_valid_platforms
        )
        
        assert technique.platforms == all_valid_platforms
    
    def test_schema_field_length_limits(self):
        """Test field length validation limits."""
        # Test name length limit
        with pytest.raises(ValidationError):
            MitreTechniqueCreate(
                technique_id="T1234",
                name="x" * 256,  # Exceeds max_length
                domain=MitreDomain.ENTERPRISE
            )
        
        # Test description length limit
        with pytest.raises(ValidationError):
            MitreTechniqueCreate(
                technique_id="T1234",
                name="Test",
                description="x" * 10001,  # Exceeds max_length
                domain=MitreDomain.ENTERPRISE
            )
    
    def test_schema_enum_validation(self):
        """Test enum field validation."""
        # Valid enum values
        technique = MitreTechniqueCreate(
            technique_id="T1234",
            name="Enum Test",
            domain=MitreDomain.ENTERPRISE,
            status=MitreEntityStatus.ACTIVE
        )
        assert technique.status == MitreEntityStatus.ACTIVE
        
        # Invalid enum values should be caught by Pydantic
        with pytest.raises(ValidationError):
            MitreTechniqueCreate(
                technique_id="T1234",
                name="Enum Test",
                domain="invalid_domain",  # Invalid enum value
            )
    
    def test_optional_field_handling(self):
        """Test handling of optional fields."""
        # Minimal required fields only
        technique = MitreTechniqueCreate(
            technique_id="T1234",
            name="Minimal Test",
            domain=MitreDomain.ENTERPRISE
        )
        
        # All optional fields should be None or default values
        assert technique.description is None
        assert technique.platforms is None
        assert technique.data_sources is None
        assert technique.is_subtechnique is False
        assert technique.status == MitreEntityStatus.ACTIVE
    
    def test_schema_serialization_with_none_values(self):
        """Test schema serialization handling None values correctly."""
        technique = MitreTechniqueCreate(
            technique_id="T1234",
            name="None Test",
            domain=MitreDomain.ENTERPRISE,
            description=None,
            platforms=None
        )
        
        # Serialize and verify None values are handled
        serialized = technique.model_dump()
        assert serialized["description"] is None
        assert serialized["platforms"] is None
        
        # Serialize excluding None values
        serialized_exclude_none = technique.model_dump(exclude_none=True)
        assert "description" not in serialized_exclude_none
        assert "platforms" not in serialized_exclude_none
