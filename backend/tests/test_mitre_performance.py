"""Performance tests for MITRE ATT&CK service."""

import asyncio
import pytest
import time
from unittest.mock import AsyncMock, patch
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.enhanced_mitre_service import EnhancedMitreService
from app.db.models.mitre import (
    MitreTechnique,
    MitreTactic,
    MitreGroup,
    MitreDomain,
)


class TestMitrePerformance:
    """Performance tests for MITRE service."""
    
    @pytest.fixture
    async def mitre_service(self, async_db: AsyncSession):
        """Create MITRE service instance."""
        return EnhancedMitreService(db=async_db)
    
    @pytest.fixture
    def large_stix_dataset(self):
        """Create large STIX dataset for performance testing."""
        objects = []
        
        # Create 1000 techniques
        for i in range(1000):
            objects.append({
                "type": "attack-pattern",
                "id": f"attack-pattern--perf-test-{i}",
                "name": f"Performance Test Technique {i}",
                "description": f"Performance test technique {i} for load testing",
                "external_references": [
                    {
                        "source_name": "mitre-attack",
                        "external_id": f"T{10000 + i}",
                        "url": f"https://attack.mitre.org/techniques/T{10000 + i}"
                    }
                ],
                "x_mitre_platforms": ["Windows", "Linux", "macOS"],
                "x_mitre_data_sources": ["Process monitoring", "File monitoring"],
                "kill_chain_phases": [
                    {
                        "kill_chain_name": "mitre-attack",
                        "phase_name": "execution"
                    }
                ]
            })
        
        # Create 50 tactics
        for i in range(50):
            objects.append({
                "type": "x-mitre-tactic",
                "id": f"x-mitre-tactic--perf-test-{i}",
                "name": f"Performance Test Tactic {i}",
                "description": f"Performance test tactic {i}",
                "external_references": [
                    {
                        "source_name": "mitre-attack",
                        "external_id": f"TA{1000 + i}",
                        "url": f"https://attack.mitre.org/tactics/TA{1000 + i}"
                    }
                ],
                "x_mitre_shortname": f"perf-test-tactic-{i}"
            })
        
        # Create 100 groups
        for i in range(100):
            objects.append({
                "type": "intrusion-set",
                "id": f"intrusion-set--perf-test-{i}",
                "name": f"Performance Test Group {i}",
                "description": f"Performance test group {i}",
                "aliases": [f"Alias {i}", f"Group {i}"],
                "external_references": [
                    {
                        "source_name": "mitre-attack",
                        "external_id": f"G{1000 + i}",
                        "url": f"https://attack.mitre.org/groups/G{1000 + i}"
                    }
                ]
            })
        
        return {
            "type": "bundle",
            "id": "bundle--performance-test",
            "objects": objects
        }
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_large_dataset_sync_performance(self, mitre_service, large_stix_dataset):
        """Test performance of syncing large dataset."""
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = large_stix_dataset
            
            start_time = time.time()
            
            result = await mitre_service.sync_domain_data(
                domain=MitreDomain.ENTERPRISE,
                force_update=True,
                triggered_by="performance_test"
            )
            
            end_time = time.time()
            sync_duration = end_time - start_time
            
            # Performance assertions
            assert result.status == "completed"
            assert result.processed_entities == 1150  # 1000 techniques + 50 tactics + 100 groups
            assert result.failed_entities == 0
            
            # Should complete within 30 seconds for 1150 objects
            assert sync_duration < 30.0, f"Sync took {sync_duration:.2f} seconds, expected < 30s"
            
            # Calculate throughput
            throughput = result.processed_entities / sync_duration
            assert throughput > 38, f"Throughput {throughput:.2f} objects/sec, expected > 38/sec"
            
            print(f"Sync Performance: {sync_duration:.2f}s, {throughput:.2f} objects/sec")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_technique_query_performance(self, mitre_service, async_db):
        """Test performance of technique queries."""
        # Create test data
        techniques = []
        for i in range(1000):
            technique = MitreTechnique(
                technique_id=f"T{20000 + i}",
                name=f"Query Test Technique {i}",
                description=f"Query performance test technique {i}",
                domain=MitreDomain.ENTERPRISE,
                search_vector=f"query test technique {i} performance"
            )
            techniques.append(technique)
        
        # Batch insert
        async_db.add_all(techniques)
        await async_db.commit()
        
        # Test simple query performance
        start_time = time.time()
        result = await async_db.execute(
            select(MitreTechnique).where(MitreTechnique.domain == MitreDomain.ENTERPRISE).limit(100)
        )
        query_results = result.scalars().all()
        query_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        assert len(query_results) == 100
        assert query_time < 100, f"Query took {query_time:.2f}ms, expected < 100ms"
        
        print(f"Simple Query Performance: {query_time:.2f}ms for 100 results")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_search_query_performance(self, mitre_service, async_db):
        """Test performance of search queries."""
        # Create test data with search vectors
        techniques = []
        for i in range(500):
            technique = MitreTechnique(
                technique_id=f"T{30000 + i}",
                name=f"Search Test Technique {i}",
                description=f"This technique is used for lateral movement and persistence",
                domain=MitreDomain.ENTERPRISE,
                search_vector=f"search test technique {i} lateral movement persistence"
            )
            techniques.append(technique)
        
        async_db.add_all(techniques)
        await async_db.commit()
        
        # Test search performance
        search_terms = ["lateral movement", "persistence", "test technique"]
        
        for term in search_terms:
            start_time = time.time()
            result = await async_db.execute(
                select(MitreTechnique).where(
                    MitreTechnique.search_vector.contains(term.lower())
                ).limit(50)
            )
            search_results = result.scalars().all()
            search_time = (time.time() - start_time) * 1000
            
            assert len(search_results) > 0
            assert search_time < 50, f"Search for '{term}' took {search_time:.2f}ms, expected < 50ms"
            
            print(f"Search Performance for '{term}': {search_time:.2f}ms, {len(search_results)} results")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_relationship_query_performance(self, mitre_service, async_db):
        """Test performance of relationship queries."""
        # Create test data with relationships
        tactic = MitreTactic(
            tactic_id="TA9999",
            name="Performance Test Tactic",
            domain=MitreDomain.ENTERPRISE
        )
        async_db.add(tactic)
        
        techniques = []
        for i in range(100):
            technique = MitreTechnique(
                technique_id=f"T{40000 + i}",
                name=f"Relationship Test Technique {i}",
                domain=MitreDomain.ENTERPRISE
            )
            technique.tactics.append(tactic)
            techniques.append(technique)
        
        async_db.add_all(techniques)
        await async_db.commit()
        
        # Test relationship query performance
        start_time = time.time()
        result = await async_db.execute(
            select(MitreTechnique)
            .join(MitreTechnique.tactics)
            .where(MitreTactic.tactic_id == "TA9999")
            .limit(50)
        )
        relationship_results = result.scalars().all()
        relationship_time = (time.time() - start_time) * 1000
        
        assert len(relationship_results) == 50
        assert relationship_time < 100, f"Relationship query took {relationship_time:.2f}ms, expected < 100ms"
        
        print(f"Relationship Query Performance: {relationship_time:.2f}ms for 50 results")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_concurrent_query_performance(self, mitre_service, async_db):
        """Test performance under concurrent load."""
        # Create test data
        techniques = []
        for i in range(200):
            technique = MitreTechnique(
                technique_id=f"T{50000 + i}",
                name=f"Concurrent Test Technique {i}",
                domain=MitreDomain.ENTERPRISE
            )
            techniques.append(technique)
        
        async_db.add_all(techniques)
        await async_db.commit()
        
        async def query_task():
            """Single query task."""
            start_time = time.time()
            result = await async_db.execute(
                select(MitreTechnique).where(MitreTechnique.domain == MitreDomain.ENTERPRISE).limit(10)
            )
            query_results = result.scalars().all()
            query_time = (time.time() - start_time) * 1000
            return len(query_results), query_time
        
        # Run 10 concurrent queries
        start_time = time.time()
        tasks = [query_task() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        total_time = (time.time() - start_time) * 1000
        
        # Verify all queries succeeded
        for result_count, query_time in results:
            assert result_count == 10
            assert query_time < 200  # Individual query should be fast
        
        # Total time for 10 concurrent queries should be reasonable
        assert total_time < 500, f"10 concurrent queries took {total_time:.2f}ms, expected < 500ms"
        
        avg_query_time = sum(query_time for _, query_time in results) / len(results)
        print(f"Concurrent Query Performance: {total_time:.2f}ms total, {avg_query_time:.2f}ms average")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_batch_processing_performance(self, mitre_service):
        """Test performance of batch processing."""
        # Create large batch of objects to process
        batch_objects = []
        for i in range(500):
            batch_objects.append({
                "type": "attack-pattern",
                "id": f"attack-pattern--batch-{i}",
                "name": f"Batch Test Technique {i}",
                "description": f"Batch processing test technique {i}",
                "external_references": [
                    {
                        "source_name": "mitre-attack",
                        "external_id": f"T{60000 + i}",
                        "url": f"https://attack.mitre.org/techniques/T{60000 + i}"
                    }
                ]
            })
        
        start_time = time.time()
        
        # Process objects in batches
        results = await mitre_service._process_objects_by_type(
            "attack-pattern",
            batch_objects,
            MitreDomain.ENTERPRISE
        )
        
        processing_time = time.time() - start_time
        
        assert results["successful"] == 500
        assert results["failed"] == 0
        
        # Should process 500 objects in under 10 seconds
        assert processing_time < 10.0, f"Batch processing took {processing_time:.2f}s, expected < 10s"
        
        throughput = 500 / processing_time
        assert throughput > 50, f"Batch throughput {throughput:.2f} objects/sec, expected > 50/sec"
        
        print(f"Batch Processing Performance: {processing_time:.2f}s, {throughput:.2f} objects/sec")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_memory_usage_during_sync(self, mitre_service, large_stix_dataset):
        """Test memory usage during large sync operations."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = large_stix_dataset
            
            result = await mitre_service.sync_domain_data(
                domain=MitreDomain.ENTERPRISE,
                force_update=True
            )
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            assert result.status == "completed"
            
            # Memory increase should be reasonable (less than 500MB for this dataset)
            assert memory_increase < 500, f"Memory increased by {memory_increase:.2f}MB, expected < 500MB"
            
            print(f"Memory Usage: Initial {initial_memory:.2f}MB, Peak {peak_memory:.2f}MB, Increase {memory_increase:.2f}MB")
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_database_connection_efficiency(self, mitre_service, async_db):
        """Test database connection efficiency."""
        # Create test data
        techniques = []
        for i in range(100):
            technique = MitreTechnique(
                technique_id=f"T{70000 + i}",
                name=f"Connection Test Technique {i}",
                domain=MitreDomain.ENTERPRISE
            )
            techniques.append(technique)
        
        # Test batch insert efficiency
        start_time = time.time()
        async_db.add_all(techniques)
        await async_db.commit()
        insert_time = time.time() - start_time
        
        # Should insert 100 records quickly
        assert insert_time < 2.0, f"Batch insert took {insert_time:.2f}s, expected < 2s"
        
        # Test query efficiency
        start_time = time.time()
        result = await async_db.execute(
            select(func.count(MitreTechnique.id)).where(
                MitreTechnique.technique_id.like("T7%")
            )
        )
        count = result.scalar()
        query_time = time.time() - start_time
        
        assert count == 100
        assert query_time < 0.1, f"Count query took {query_time:.3f}s, expected < 0.1s"
        
        print(f"DB Efficiency: Insert {insert_time:.2f}s, Count query {query_time:.3f}s")


@pytest.mark.performance
class TestMitreStressTests:
    """Stress tests for MITRE service under heavy load."""
    
    @pytest.mark.asyncio
    async def test_high_volume_sync_stress(self, async_db):
        """Stress test with very high volume data."""
        mitre_service = EnhancedMitreService(db=async_db)
        
        # Create extremely large dataset (5000 objects)
        objects = []
        for i in range(5000):
            objects.append({
                "type": "attack-pattern",
                "id": f"attack-pattern--stress-{i}",
                "name": f"Stress Test Technique {i}",
                "description": f"Stress test technique {i} with detailed description for testing",
                "external_references": [
                    {
                        "source_name": "mitre-attack",
                        "external_id": f"T{80000 + i}",
                        "url": f"https://attack.mitre.org/techniques/T{80000 + i}"
                    }
                ],
                "x_mitre_platforms": ["Windows", "Linux", "macOS", "Android", "iOS"],
                "x_mitre_data_sources": ["Process monitoring", "File monitoring", "Network monitoring"]
            })
        
        stress_dataset = {
            "type": "bundle",
            "id": "bundle--stress-test",
            "objects": objects
        }
        
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = stress_dataset
            
            start_time = time.time()
            
            result = await mitre_service.sync_domain_data(
                domain=MitreDomain.ENTERPRISE,
                force_update=True,
                triggered_by="stress_test"
            )
            
            stress_time = time.time() - start_time
            
            assert result.status == "completed"
            assert result.processed_entities == 5000
            
            # Should handle 5000 objects within reasonable time (2 minutes)
            assert stress_time < 120.0, f"Stress test took {stress_time:.2f}s, expected < 120s"
            
            throughput = 5000 / stress_time
            print(f"Stress Test Performance: {stress_time:.2f}s, {throughput:.2f} objects/sec")
    
    @pytest.mark.asyncio
    async def test_rapid_successive_syncs(self, async_db):
        """Test rapid successive sync operations."""
        mitre_service = EnhancedMitreService(db=async_db)
        
        small_dataset = {
            "type": "bundle",
            "id": "bundle--rapid-test",
            "objects": [
                {
                    "type": "attack-pattern",
                    "id": "attack-pattern--rapid-test",
                    "name": "Rapid Test Technique",
                    "external_references": [
                        {
                            "source_name": "mitre-attack",
                            "external_id": "T90000",
                            "url": "https://attack.mitre.org/techniques/T90000"
                        }
                    ]
                }
            ]
        }
        
        with patch.object(mitre_service, '_download_stix_data', new_callable=AsyncMock) as mock_download:
            mock_download.return_value = small_dataset
            
            # Perform 5 rapid successive syncs
            start_time = time.time()
            
            for i in range(5):
                result = await mitre_service.sync_domain_data(
                    domain=MitreDomain.ENTERPRISE,
                    force_update=True,
                    triggered_by=f"rapid_test_{i}"
                )
                assert result.status == "completed"
            
            total_time = time.time() - start_time
            
            # 5 rapid syncs should complete within 30 seconds
            assert total_time < 30.0, f"5 rapid syncs took {total_time:.2f}s, expected < 30s"
            
            print(f"Rapid Sync Performance: {total_time:.2f}s for 5 syncs")
