"""Add robust asset models with soft-delete and audit capabilities

Revision ID: 001_robust_assets
Revises: 
Create Date: 2024-12-11 22:20:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_robust_assets'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add robust asset models and related tables."""
    
    # Create enum types
    op.execute("CREATE TYPE auditaction AS ENUM ('create', 'update', 'delete', 'soft_delete', 'restore', 'archive', 'merge', 'split')")
    op.execute("CREATE TYPE dataretentionpolicy AS ENUM ('immediate', 'short_term', 'medium_term', 'long_term', 'permanent', 'compliance')")
    
    # Create assets_robust table (enhanced asset model)
    op.create_table('assets_robust',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('asset_type', sa.String(length=50), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        
        # Provider-specific identification
        sa.Column('provider_id', sa.String(length=255), nullable=True),
        sa.Column('provider_region', sa.String(length=100), nullable=True),
        sa.Column('provider_account_id', sa.String(length=100), nullable=True),
        sa.Column('provider_resource_group', sa.String(length=255), nullable=True),
        
        # Network and connectivity
        sa.Column('ip_addresses', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('dns_names', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('ports', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('protocols', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        
        # Asset metadata and context
        sa.Column('environment', sa.String(length=100), nullable=True),
        sa.Column('owner', sa.String(length=255), nullable=True),
        sa.Column('team', sa.String(length=255), nullable=True),
        sa.Column('cost_center', sa.String(length=100), nullable=True),
        sa.Column('business_unit', sa.String(length=255), nullable=True),
        
        # Security and risk assessment
        sa.Column('risk_score', sa.Integer(), nullable=True),
        sa.Column('risk_level', sa.String(length=20), nullable=True),
        sa.Column('security_score', sa.Float(), nullable=True),
        sa.Column('compliance_score', sa.Float(), nullable=True),
        
        # Discovery and tracking
        sa.Column('discovery_source', sa.String(length=50), nullable=False),
        sa.Column('discovery_job_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('discovered_at', sa.DateTime(), nullable=False),
        sa.Column('last_seen', sa.DateTime(), nullable=False),
        
        # Configuration and properties
        sa.Column('configuration', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('properties', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        
        # Health and monitoring
        sa.Column('health_status', sa.String(length=50), nullable=False, server_default='unknown'),
        sa.Column('health_score', sa.Float(), nullable=True),
        sa.Column('last_health_check', sa.DateTime(), nullable=True),
        sa.Column('monitoring_enabled', sa.Boolean(), nullable=False, server_default='false'),
        
        # Data classification and privacy
        sa.Column('data_classification', sa.String(length=50), nullable=True),
        sa.Column('contains_pii', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('gdpr_applicable', sa.Boolean(), nullable=False, server_default='false'),
        
        # Business context
        sa.Column('business_criticality', sa.String(length=20), nullable=True),
        sa.Column('revenue_impact', sa.String(length=20), nullable=True),
        sa.Column('customer_impact', sa.String(length=20), nullable=True),
        
        # Soft delete fields
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_by', sa.String(length=255), nullable=True),
        sa.Column('deletion_reason', sa.String(length=500), nullable=True),
        sa.Column('retention_policy', sa.Enum('immediate', 'short_term', 'medium_term', 'long_term', 'permanent', 'compliance', name='dataretentionpolicy'), nullable=False, server_default='long_term'),
        sa.Column('purge_after', sa.DateTime(), nullable=True),
        
        # Audit fields
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('created_by', sa.String(length=255), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('updated_by', sa.String(length=255), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('change_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('checksum', sa.String(length=64), nullable=True),
        sa.Column('last_verified_at', sa.DateTime(), nullable=True),
        sa.Column('verification_status', sa.String(length=50), nullable=False, server_default='unverified'),
        
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('provider', 'provider_id', name='uq_robust_asset_provider_id'),
        sa.CheckConstraint('risk_score >= 0 AND risk_score <= 100', name='chk_risk_score_range'),
        sa.CheckConstraint('security_score >= 0.0 AND security_score <= 100.0', name='chk_security_score_range'),
        sa.CheckConstraint('compliance_score >= 0.0 AND compliance_score <= 100.0', name='chk_compliance_score_range'),
        sa.CheckConstraint('health_score >= 0.0 AND health_score <= 100.0', name='chk_health_score_range'),
        sa.CheckConstraint('version >= 1', name='chk_version_positive'),
        sa.CheckConstraint('change_count >= 0', name='chk_change_count_non_negative')
    )
    
    # Create asset_audit_log table
    op.create_table('asset_audit_log',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('action', sa.Enum('create', 'update', 'delete', 'soft_delete', 'restore', 'archive', 'merge', 'split', name='auditaction'), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.String(length=255), nullable=True),
        sa.Column('session_id', sa.String(length=255), nullable=True),
        sa.Column('ip_address', postgresql.INET(), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('table_name', sa.String(length=100), nullable=False),
        sa.Column('record_id', sa.String(length=100), nullable=False),
        sa.Column('field_name', sa.String(length=100), nullable=True),
        sa.Column('old_value', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('new_value', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('change_reason', sa.String(length=500), nullable=True),
        sa.Column('change_source', sa.String(length=100), nullable=True),
        sa.Column('correlation_id', sa.String(length=100), nullable=True),
        sa.Column('before_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('after_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('retention_policy', sa.Enum('immediate', 'short_term', 'medium_term', 'long_term', 'permanent', 'compliance', name='dataretentionpolicy'), nullable=False, server_default='compliance'),
        sa.Column('is_sensitive', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('compliance_tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    """Remove robust asset models and related tables."""
    
    # Drop tables in reverse order
    op.drop_table('asset_audit_log')
    op.drop_table('assets_robust')
    
    # Drop enum types
    op.execute("DROP TYPE IF EXISTS dataretentionpolicy")
    op.execute("DROP TYPE IF EXISTS auditaction")
