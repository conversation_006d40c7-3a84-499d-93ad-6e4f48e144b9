"""Add comprehensive MITRE ATT&CK schema with enhanced models

Revision ID: 002_mitre_schema
Revises: 001_robust_assets
Create Date: 2025-12-13 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002_mitre_schema'
down_revision = '001_robust_assets'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add comprehensive MITRE ATT&CK schema."""
    
    # Create enum types for MITRE models
    op.execute("CREATE TYPE mitredomain AS ENUM ('enterprise', 'mobile', 'ics')")
    op.execute("CREATE TYPE mitreentitystatus AS ENUM ('active', 'deprecated', 'revoked')")
    op.execute("CREATE TYPE mitredatasource AS ENUM ('official', 'community', 'custom')")
    
    # Create MITRE Tactics table
    op.create_table('mitre_tactics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tactic_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('short_name', sa.String(length=100), nullable=True),
        sa.Column('x_mitre_shortname', sa.String(length=100), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tactic_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_tactic_version_format')
    )
    
    # Create MITRE Techniques table
    op.create_table('mitre_techniques',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('technique_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('is_subtechnique', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('parent_technique_id', sa.String(length=20), nullable=True),
        sa.Column('platforms', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('data_sources', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('system_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('permissions_required', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('effective_permissions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('defense_bypassed', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('supports_remote', sa.Boolean(), nullable=True),
        sa.Column('impact_type', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('capec_id', sa.String(length=20), nullable=True),
        sa.Column('mtc_id', sa.String(length=20), nullable=True),
        sa.Column('kill_chain_phases', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('detection_methods', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('contributors', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('search_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('technique_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_technique_version_format')
    )
    
    # Create MITRE Groups table
    op.create_table('mitre_groups',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('group_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('aliases', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('associated_groups', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('country', sa.String(length=100), nullable=True),
        sa.Column('motivation', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('sophistication', sa.String(length=50), nullable=True),
        sa.Column('first_seen', sa.DateTime(), nullable=True),
        sa.Column('last_seen', sa.DateTime(), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('search_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('group_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_group_version_format')
    )
    
    # Create MITRE Software table
    op.create_table('mitre_software',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('software_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('software_type', sa.String(length=50), nullable=True),
        sa.Column('aliases', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('associated_software', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('platforms', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('labels', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('search_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('software_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_software_version_format')
    )
    
    # Create MITRE Mitigations table
    op.create_table('mitre_mitigations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('mitigation_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('mitigation_type', sa.String(length=100), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('search_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('mitigation_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_mitigation_version_format')
    )
    
    # Create MITRE Data Sources table
    op.create_table('mitre_data_sources',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('data_source_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('collection_layers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('platforms', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('data_components', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('search_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('data_source_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_data_source_version_format')
    )
    
    # Create MITRE Campaigns table
    op.create_table('mitre_campaigns',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('campaign_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('aliases', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('associated_groups', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('first_seen', sa.DateTime(), nullable=True),
        sa.Column('last_seen', sa.DateTime(), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('search_vector', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('campaign_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_campaign_version_format')
    )
    
    # Create MITRE Matrices table
    op.create_table('mitre_matrices',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('matrix_id', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('status', sa.Enum('active', 'deprecated', 'revoked', name='mitreentitystatus'), nullable=False, server_default='active'),
        sa.Column('tactic_order', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('platforms', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('references', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('version', sa.String(length=10), nullable=False, server_default='1.0'),
        sa.Column('created_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('modified_date', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('data_source', sa.Enum('official', 'community', 'custom', name='mitredatasource'), nullable=False, server_default='official'),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('matrix_id'),
        sa.CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_matrix_version_format')
    )
    
    # Create MITRE Data Sync table
    op.create_table('mitre_data_sync',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('sync_id', sa.String(length=100), nullable=False),
        sa.Column('domain', sa.Enum('enterprise', 'mobile', 'ics', name='mitredomain'), nullable=False),
        sa.Column('sync_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('total_entities', sa.Integer(), nullable=True),
        sa.Column('processed_entities', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('failed_entities', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('started_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('duration_seconds', sa.Float(), nullable=True),
        sa.Column('source_url', sa.String(length=500), nullable=True),
        sa.Column('source_version', sa.String(length=20), nullable=True),
        sa.Column('source_checksum', sa.String(length=64), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('sync_results', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('triggered_by', sa.String(length=255), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('sync_id'),
        sa.CheckConstraint('processed_entities >= 0', name='chk_processed_entities_non_negative'),
        sa.CheckConstraint('failed_entities >= 0', name='chk_failed_entities_non_negative'),
        sa.CheckConstraint('duration_seconds >= 0', name='chk_duration_non_negative')
    )

    # Create association tables for many-to-many relationships

    # Technique-Tactic associations
    op.create_table('mitre_technique_tactic_associations',
        sa.Column('technique_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tactic_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['technique_id'], ['mitre_techniques.id'], ),
        sa.ForeignKeyConstraint(['tactic_id'], ['mitre_tactics.id'], ),
        sa.PrimaryKeyConstraint('technique_id', 'tactic_id')
    )

    # Technique-Mitigation associations
    op.create_table('mitre_technique_mitigation_associations',
        sa.Column('technique_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('mitigation_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
        sa.Column('effectiveness', sa.String(length=20), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['technique_id'], ['mitre_techniques.id'], ),
        sa.ForeignKeyConstraint(['mitigation_id'], ['mitre_mitigations.id'], ),
        sa.PrimaryKeyConstraint('technique_id', 'mitigation_id')
    )

    # Group-Technique associations
    op.create_table('mitre_group_technique_associations',
        sa.Column('group_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('technique_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
        sa.Column('procedure_examples', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['group_id'], ['mitre_groups.id'], ),
        sa.ForeignKeyConstraint(['technique_id'], ['mitre_techniques.id'], ),
        sa.PrimaryKeyConstraint('group_id', 'technique_id')
    )

    # Group-Software associations
    op.create_table('mitre_group_software_associations',
        sa.Column('group_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('software_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
        sa.Column('usage_examples', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['group_id'], ['mitre_groups.id'], ),
        sa.ForeignKeyConstraint(['software_id'], ['mitre_software.id'], ),
        sa.PrimaryKeyConstraint('group_id', 'software_id')
    )

    # Create indexes for performance optimization

    # Tactic indexes
    op.create_index('idx_tactic_domain_status', 'mitre_tactics', ['domain', 'status'])
    op.create_index('idx_tactic_name', 'mitre_tactics', ['name'])

    # Technique indexes
    op.create_index('idx_technique_domain_status', 'mitre_techniques', ['domain', 'status'])
    op.create_index('idx_technique_parent', 'mitre_techniques', ['parent_technique_id'])
    op.create_index('idx_technique_search', 'mitre_techniques', ['name'])
    op.create_index('idx_technique_subtechnique', 'mitre_techniques', ['is_subtechnique'])

    # Group indexes
    op.create_index('idx_group_domain_status', 'mitre_groups', ['domain', 'status'])
    op.create_index('idx_group_country', 'mitre_groups', ['country'])
    op.create_index('idx_group_search', 'mitre_groups', ['name'])

    # Software indexes
    op.create_index('idx_software_domain_status', 'mitre_software', ['domain', 'status'])
    op.create_index('idx_software_type', 'mitre_software', ['software_type'])
    op.create_index('idx_software_search', 'mitre_software', ['name'])

    # Mitigation indexes
    op.create_index('idx_mitigation_domain_status', 'mitre_mitigations', ['domain', 'status'])
    op.create_index('idx_mitigation_search', 'mitre_mitigations', ['name'])

    # Data Source indexes
    op.create_index('idx_data_source_domain_status', 'mitre_data_sources', ['domain', 'status'])
    op.create_index('idx_data_source_search', 'mitre_data_sources', ['name'])

    # Campaign indexes
    op.create_index('idx_campaign_domain_status', 'mitre_campaigns', ['domain', 'status'])
    op.create_index('idx_campaign_timeline', 'mitre_campaigns', ['first_seen', 'last_seen'])
    op.create_index('idx_campaign_search', 'mitre_campaigns', ['name'])

    # Matrix indexes
    op.create_index('idx_matrix_domain_status', 'mitre_matrices', ['domain', 'status'])

    # Data Sync indexes
    op.create_index('idx_sync_domain_status', 'mitre_data_sync', ['domain', 'status'])
    op.create_index('idx_sync_started_at', 'mitre_data_sync', ['started_at'])


def downgrade() -> None:
    """Remove comprehensive MITRE ATT&CK schema."""

    # Drop indexes first
    op.drop_index('idx_sync_started_at', table_name='mitre_data_sync')
    op.drop_index('idx_sync_domain_status', table_name='mitre_data_sync')
    op.drop_index('idx_matrix_domain_status', table_name='mitre_matrices')
    op.drop_index('idx_campaign_search', table_name='mitre_campaigns')
    op.drop_index('idx_campaign_timeline', table_name='mitre_campaigns')
    op.drop_index('idx_campaign_domain_status', table_name='mitre_campaigns')
    op.drop_index('idx_data_source_search', table_name='mitre_data_sources')
    op.drop_index('idx_data_source_domain_status', table_name='mitre_data_sources')
    op.drop_index('idx_mitigation_search', table_name='mitre_mitigations')
    op.drop_index('idx_mitigation_domain_status', table_name='mitre_mitigations')
    op.drop_index('idx_software_search', table_name='mitre_software')
    op.drop_index('idx_software_type', table_name='mitre_software')
    op.drop_index('idx_software_domain_status', table_name='mitre_software')
    op.drop_index('idx_group_search', table_name='mitre_groups')
    op.drop_index('idx_group_country', table_name='mitre_groups')
    op.drop_index('idx_group_domain_status', table_name='mitre_groups')
    op.drop_index('idx_technique_subtechnique', table_name='mitre_techniques')
    op.drop_index('idx_technique_search', table_name='mitre_techniques')
    op.drop_index('idx_technique_parent', table_name='mitre_techniques')
    op.drop_index('idx_technique_domain_status', table_name='mitre_techniques')
    op.drop_index('idx_tactic_name', table_name='mitre_tactics')
    op.drop_index('idx_tactic_domain_status', table_name='mitre_tactics')

    # Drop association tables
    op.drop_table('mitre_group_software_associations')
    op.drop_table('mitre_group_technique_associations')
    op.drop_table('mitre_technique_mitigation_associations')
    op.drop_table('mitre_technique_tactic_associations')

    # Drop main tables in reverse order
    op.drop_table('mitre_data_sync')
    op.drop_table('mitre_matrices')
    op.drop_table('mitre_campaigns')
    op.drop_table('mitre_data_sources')
    op.drop_table('mitre_mitigations')
    op.drop_table('mitre_software')
    op.drop_table('mitre_groups')
    op.drop_table('mitre_techniques')
    op.drop_table('mitre_tactics')

    # Drop enum types
    op.execute("DROP TYPE IF EXISTS mitredatasource")
    op.execute("DROP TYPE IF EXISTS mitreentitystatus")
    op.execute("DROP TYPE IF EXISTS mitredomain")
