Run started:2025-06-14 13:03:54.836372

Test results:
>> Issue: [B110:try_except_pass] Try, Except, Pass detected.
   Severity: Low   Confidence: High
   CWE: CWE-703 (https://cwe.mitre.org/data/definitions/703.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html
   Location: app/core/advanced_security.py:227:8
226	                data["body"] = body.decode("utf-8", errors="ignore")
227	        except Exception:
228	            pass
229	        

--------------------------------------------------
>> Issue: [B403:blacklist] Consider possible security implications associated with pickle module.
   Severity: Low   Confidence: High
   CWE: CWE-502 (https://cwe.mitre.org/data/definitions/502.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle
   Location: app/core/performance.py:15:0
14	import json
15	import pickle
16	from datetime import datetime, timedelta

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: app/services/discovery/api_discovery.py:10:0
9	import logging
10	import subprocess
11	import json

--------------------------------------------------
>> Issue: [B110:try_except_pass] Try, Except, Pass detected.
   Severity: Low   Confidence: High
   CWE: CWE-703 (https://cwe.mitre.org/data/definitions/703.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html
   Location: app/services/discovery/api_discovery.py:384:16
383	                            
384	                except Exception as e:
385	                    # Silently continue - spec might not exist
386	                    pass
387	        

--------------------------------------------------
>> Issue: [B110:try_except_pass] Try, Except, Pass detected.
   Severity: Low   Confidence: High
   CWE: CWE-703 (https://cwe.mitre.org/data/definitions/703.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html
   Location: app/services/discovery/api_discovery.py:439:16
438	                            
439	                except Exception as e:
440	                    # Silently continue
441	                    pass
442	        

--------------------------------------------------
>> Issue: [B110:try_except_pass] Try, Except, Pass detected.
   Severity: Low   Confidence: High
   CWE: CWE-703 (https://cwe.mitre.org/data/definitions/703.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html
   Location: app/services/discovery/gcp_discovery.py:471:8
470	                    break
471	        except Exception:
472	            pass  # IAM policy might not be accessible
473	

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: app/services/discovery/network_discovery.py:10:0
9	import logging
10	import subprocess
11	import json

--------------------------------------------------
>> Issue: [B607:start_process_with_partial_path] Starting a process with a partial executable path
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html
   Location: app/services/discovery/network_discovery.py:125:25
124	            if self.scan_type == "nmap":
125	                result = subprocess.run(["which", "nmap"], capture_output=True, text=True)
126	                if result.returncode != 0:

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: app/services/discovery/network_discovery.py:125:25
124	            if self.scan_type == "nmap":
125	                result = subprocess.run(["which", "nmap"], capture_output=True, text=True)
126	                if result.returncode != 0:

--------------------------------------------------
>> Issue: [B607:start_process_with_partial_path] Starting a process with a partial executable path
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html
   Location: app/services/discovery/network_discovery.py:130:25
129	            elif self.scan_type == "masscan":
130	                result = subprocess.run(["which", "masscan"], capture_output=True, text=True)
131	                if result.returncode != 0:

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: app/services/discovery/network_discovery.py:130:25
129	            elif self.scan_type == "masscan":
130	                result = subprocess.run(["which", "masscan"], capture_output=True, text=True)
131	                if result.returncode != 0:

--------------------------------------------------
>> Issue: [B110:try_except_pass] Try, Except, Pass detected.
   Severity: Low   Confidence: High
   CWE: CWE-703 (https://cwe.mitre.org/data/definitions/703.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html
   Location: app/services/discovery/network_discovery.py:413:12
412	                open_ports.append(int(port))
413	            except:
414	                pass  # Port closed or filtered
415	        

--------------------------------------------------

Code scanned:
	Total lines of code: 20464
	Total lines skipped (#nosec): 0
	Total potential issues skipped due to specifically being disabled (e.g., #nosec BXXX): 7

Run metrics:
	Total issues (by severity):
		Undefined: 0
		Low: 12
		Medium: 0
		High: 0
	Total issues (by confidence):
		Undefined: 0
		Low: 0
		Medium: 0
		High: 12
Files skipped (0):
