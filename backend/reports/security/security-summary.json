{"scan_timestamp": "2025-06-14T13:04:12+00:00", "scans_performed": {"bandit": {"status": "completed", "exit_code": 1, "issues_found": true, "output_file": "reports/security/bandit-results.json"}, "safety": {"status": "completed", "exit_code": 2, "vulnerabilities_found": true, "output_file": "reports/security/safety-results.json"}, "semgrep": {"status": "completed", "exit_code": 0, "findings": false, "output_file": "reports/security/semgrep-results.json"}, "docker": {"status": "completed", "exit_code": 0, "vulnerabilities_found": false, "output_file": "reports/security/trivy-results.json"}, "secrets": {"status": "completed", "exit_code": 1, "secrets_found": false, "output_file": "reports/security/secrets-scan.json"}}, "overall_status": "FAIL", "critical_issues": 2, "recommendations": ["Review and fix all identified security vulnerabilities", "Update dependencies with known security issues", "Remove or secure any exposed secrets", "Run security scans regularly in CI/CD pipeline"]}