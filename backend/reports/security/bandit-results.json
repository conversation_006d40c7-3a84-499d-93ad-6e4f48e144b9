{"errors": [], "generated_at": "2025-06-14T13:03:52Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 12, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 12, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20464, "nosec": 0, "skipped_tests": 7}, "app/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 7, "nosec": 0, "skipped_tests": 0}, "app/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/api/api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 59, "nosec": 0, "skipped_tests": 0}, "app/api/health.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 439, "nosec": 0, "skipped_tests": 0}, "app/api/security.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 483, "nosec": 0, "skipped_tests": 0}, "app/api/v1/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/api/v1/assets.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 355, "nosec": 0, "skipped_tests": 0}, "app/api/v1/attack_paths.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 336, "nosec": 0, "skipped_tests": 0}, "app/api/v1/auth.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 336, "nosec": 0, "skipped_tests": 0}, "app/api/v1/discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 487, "nosec": 0, "skipped_tests": 0}, "app/api/v1/mitre_attack.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 425, "nosec": 0, "skipped_tests": 0}, "app/api/v1/realtime.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 326, "nosec": 0, "skipped_tests": 0}, "app/api/v1/robust_assets.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 393, "nosec": 0, "skipped_tests": 0}, "app/api/v1/threat_modeling.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 290, "nosec": 0, "skipped_tests": 0}, "app/api/v1/users.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 404, "nosec": 0, "skipped_tests": 0}, "app/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 182, "nosec": 0, "skipped_tests": 0}, "app/core/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/core/advanced_security.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 471, "nosec": 0, "skipped_tests": 2}, "app/core/compliance.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 522, "nosec": 0, "skipped_tests": 0}, "app/core/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "app/core/data_integrity.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 437, "nosec": 0, "skipped_tests": 0}, "app/core/performance.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 351, "nosec": 0, "skipped_tests": 0}, "app/core/permissions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 368, "nosec": 0, "skipped_tests": 0}, "app/core/resilience.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 311, "nosec": 0, "skipped_tests": 0}, "app/core/secrets_manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 540, "nosec": 0, "skipped_tests": 4}, "app/core/security.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 179, "nosec": 0, "skipped_tests": 0}, "app/db/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/db/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 177, "nosec": 0, "skipped_tests": 0}, "app/db/mixins/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "app/db/mixins/soft_delete.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 100, "nosec": 0, "skipped_tests": 0}, "app/db/models/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "app/db/models/asset.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 342, "nosec": 0, "skipped_tests": 1}, "app/db/models/asset_extended.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 240, "nosec": 0, "skipped_tests": 0}, "app/db/models/asset_robust.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 280, "nosec": 0, "skipped_tests": 0}, "app/db/models/auth.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 293, "nosec": 0, "skipped_tests": 0}, "app/db/models/mitre.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 333, "nosec": 0, "skipped_tests": 0}, "app/db/models/user.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 433, "nosec": 0, "skipped_tests": 0}, "app/db/session.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 255, "nosec": 0, "skipped_tests": 0}, "app/dependencies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 244, "nosec": 0, "skipped_tests": 0}, "app/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 258, "nosec": 0, "skipped_tests": 0}, "app/schemas/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 137, "nosec": 0, "skipped_tests": 0}, "app/schemas/asset.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 252, "nosec": 0, "skipped_tests": 0}, "app/schemas/auth.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 300, "nosec": 0, "skipped_tests": 0}, "app/schemas/mitre.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 382, "nosec": 0, "skipped_tests": 0}, "app/schemas/user.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 319, "nosec": 0, "skipped_tests": 0}, "app/services/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "app/services/asset_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 410, "nosec": 0, "skipped_tests": 0}, "app/services/attack_path_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 417, "nosec": 0, "skipped_tests": 0}, "app/services/auth_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 432, "nosec": 0, "skipped_tests": 0}, "app/services/data_retention_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 280, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/api_discovery.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 416, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/azure_discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 770, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/base_discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 342, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/cloud_discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 282, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/gcp_discovery.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 601, "nosec": 0, "skipped_tests": 0}, "app/services/discovery/network_discovery.py": {"CONFIDENCE.HIGH": 6, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 6, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 357, "nosec": 0, "skipped_tests": 0}, "app/services/discovery_orchestrator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 237, "nosec": 0, "skipped_tests": 0}, "app/services/enhanced_mitre_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 412, "nosec": 0, "skipped_tests": 0}, "app/services/graph_engine.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 600, "nosec": 0, "skipped_tests": 0}, "app/services/mitre_attack_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 951, "nosec": 0, "skipped_tests": 0}, "app/services/realtime_monitoring_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 317, "nosec": 0, "skipped_tests": 0}, "app/services/robust_asset_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 373, "nosec": 0, "skipped_tests": 0}, "app/services/threat_modeling_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 650, "nosec": 0, "skipped_tests": 0}, "app/services/user_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 448, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "226                 data[\"body\"] = body.decode(\"utf-8\", errors=\"ignore\")\n227         except Exception:\n228             pass\n229         \n", "col_offset": 8, "end_col_offset": 16, "filename": "app/core/advanced_security.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 227, "line_range": [227, 228], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "14 import json\n15 import pickle\n16 from datetime import datetime, timedelta\n", "col_offset": 0, "end_col_offset": 13, "filename": "app/core/performance.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 502, "link": "https://cwe.mitre.org/data/definitions/502.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with pickle module.", "line_number": 15, "line_range": [15], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle", "test_id": "B403", "test_name": "blacklist"}, {"code": "9 import logging\n10 import subprocess\n11 import json\n", "col_offset": 0, "end_col_offset": 17, "filename": "app/services/discovery/api_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "383                             \n384                 except Exception as e:\n385                     # Silently continue - spec might not exist\n386                     pass\n387         \n", "col_offset": 16, "end_col_offset": 24, "filename": "app/services/discovery/api_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 384, "line_range": [384, 385, 386], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "438                             \n439                 except <PERSON><PERSON> as e:\n440                     # Silently continue\n441                     pass\n442         \n", "col_offset": 16, "end_col_offset": 24, "filename": "app/services/discovery/api_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 439, "line_range": [439, 440, 441], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "470                     break\n471         except Exception:\n472             pass  # IAM policy might not be accessible\n473 \n", "col_offset": 8, "end_col_offset": 16, "filename": "app/services/discovery/gcp_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 471, "line_range": [471, 472], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "9 import logging\n10 import subprocess\n11 import json\n", "col_offset": 0, "end_col_offset": 17, "filename": "app/services/discovery/network_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 10, "line_range": [10], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "124             if self.scan_type == \"nmap\":\n125                 result = subprocess.run([\"which\", \"nmap\"], capture_output=True, text=True)\n126                 if result.returncode != 0:\n", "col_offset": 25, "end_col_offset": 90, "filename": "app/services/discovery/network_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 125, "line_range": [125], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "124             if self.scan_type == \"nmap\":\n125                 result = subprocess.run([\"which\", \"nmap\"], capture_output=True, text=True)\n126                 if result.returncode != 0:\n", "col_offset": 25, "end_col_offset": 90, "filename": "app/services/discovery/network_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 125, "line_range": [125], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "129             elif self.scan_type == \"masscan\":\n130                 result = subprocess.run([\"which\", \"masscan\"], capture_output=True, text=True)\n131                 if result.returncode != 0:\n", "col_offset": 25, "end_col_offset": 93, "filename": "app/services/discovery/network_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "129             elif self.scan_type == \"masscan\":\n130                 result = subprocess.run([\"which\", \"masscan\"], capture_output=True, text=True)\n131                 if result.returncode != 0:\n", "col_offset": 25, "end_col_offset": 93, "filename": "app/services/discovery/network_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "412                 open_ports.append(int(port))\n413             except:\n414                 pass  # Port closed or filtered\n415         \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/services/discovery/network_discovery.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 413, "line_range": [413, 414], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}]}