{"version": "1.125.0", "results": [{"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "app/api/v1/auth.py", "start": {"line": 372, "col": 9, "offset": 11050}, "end": {"line": 372, "col": 51, "offset": 11092}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(reset_data.new_password, user=user):\n            user.set_password(reset_data.new_password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "app/api/v1/auth.py", "start": {"line": 421, "col": 9, "offset": 12398}, "end": {"line": 421, "col": 62, "offset": 12451}, "extra": {"message": "The password on 'current_user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password_data.new_password, user=current_user):\n            current_user.set_password(password_data.new_password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["app/__init__.py", "app/api/__init__.py", "app/api/api.py", "app/api/health.py", "app/api/security.py", "app/api/v1/__init__.py", "app/api/v1/assets.py", "app/api/v1/attack_paths.py", "app/api/v1/auth.py", "app/api/v1/discovery.py", "app/api/v1/mitre_attack.py", "app/api/v1/realtime.py", "app/api/v1/robust_assets.py", "app/api/v1/threat_modeling.py", "app/api/v1/users.py", "app/config.py", "app/core/__init__.py", "app/core/advanced_security.py", "app/core/compliance.py", "app/core/config.py", "app/core/data_integrity.py", "app/core/performance.py", "app/core/permissions.py", "app/core/resilience.py", "app/core/secrets_manager.py", "app/core/security.py", "app/db/__init__.py", "app/db/base.py", "app/db/mixins/__init__.py", "app/db/mixins/soft_delete.py", "app/db/models/__init__.py", "app/db/models/asset.py", "app/db/models/asset_extended.py", "app/db/models/asset_robust.py", "app/db/models/auth.py", "app/db/models/mitre.py", "app/db/models/user.py", "app/db/session.py", "app/dependencies.py", "app/main.py", "app/schemas/__init__.py", "app/schemas/asset.py", "app/schemas/auth.py", "app/schemas/mitre.py", "app/schemas/user.py", "app/services/__init__.py", "app/services/asset_service.py", "app/services/attack_path_analyzer.py", "app/services/auth_service.py", "app/services/data_retention_service.py", "app/services/discovery/__init__.py", "app/services/discovery/api_discovery.py", "app/services/discovery/azure_discovery.py", "app/services/discovery/base_discovery.py", "app/services/discovery/cloud_discovery.py", "app/services/discovery/gcp_discovery.py", "app/services/discovery/network_discovery.py", "app/services/discovery_orchestrator.py", "app/services/enhanced_mitre_service.py", "app/services/graph_engine.py", "app/services/mitre_attack_service.py", "app/services/realtime_monitoring_service.py", "app/services/robust_asset_service.py", "app/services/threat_modeling_service.py", "app/services/user_service.py"]}, "time": {"rules": [], "rules_parse_time": 1.5899150371551514, "profiling_times": {"config_time": 5.491297721862793, "core_time": 4.502714157104492, "ignores_time": 0.0018000602722167969, "total_time": 9.996736526489258}, "parsing_time": {"total_time": 0.47672009468078613, "per_file_time": {"mean": 0.007334155302781328, "std_dev": 3.42410144431871e-05}, "very_slow_files": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1072566784}, "skipped_rules": []}