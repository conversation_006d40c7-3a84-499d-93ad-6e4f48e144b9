# Blast-Radius Security Tool - Backend

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![Code style: ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)
[![Type checked: mypy](https://img.shields.io/badge/type%20checked-mypy-blue.svg)](https://mypy.readthedocs.io/)
[![Security: bandit](https://img.shields.io/badge/security-bandit-yellow.svg)](https://github.com/PyCQA/bandit)

The backend API for the Blast-Radius Security Tool, built with FastAPI and designed for high-performance security operations.

## 🚀 Features

### 🔐 Authentication & Security
- **JWT-based authentication** with access and refresh tokens
- **Multi-factor authentication (MFA)** with TOTP, SMS, and email support
- **Role-based access control (RBAC)** with 7 distinct user roles
- **Session management** with configurable timeouts
- **Account lockout protection** and rate limiting
- **API key authentication** for programmatic access

### 👥 User Management
- Complete user lifecycle management
- Granular permission system (25+ permissions)
- Password policies with complexity requirements
- User activity tracking and audit logging
- Bulk user operations

### 🏗️ Architecture
- **FastAPI** framework with async/await support
- **PostgreSQL** database with SQLAlchemy ORM
- **Redis** for caching and session storage
- **Alembic** for database migrations
- **Pydantic** for data validation and serialization

### 🧪 Quality Assurance
- **95%+ test coverage** with pytest
- **Type checking** with mypy (PEP 484 compliant)
- **Code formatting** with ruff (PEP 8 compliant)
- **Docstring standards** (PEP 257 compliant)
- **Security scanning** with bandit
- **Pre-commit hooks** for code quality

## 📋 Requirements

- **Python**: 3.11 or higher
- **PostgreSQL**: 14.0 or higher
- **Redis**: 6.0 or higher
- **Memory**: Minimum 2GB RAM
- **Storage**: Minimum 5GB free space

## 🛠️ Installation

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius/backend
   ```

2. **Create virtual environment**
   ```bash
   python3.11 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   make install-dev
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Setup database**
   ```bash
   # Create PostgreSQL database
   createdb blast_radius
   
   # Run migrations
   make migrate-upgrade
   ```

6. **Start development server**
   ```bash
   make run-dev
   ```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost/blast_radius

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security Configuration
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Application Configuration
DEBUG=false
LOG_LEVEL=info
CORS_ORIGINS=["http://localhost:3000"]
```

## 🔄 Local CI/CD Pipeline

### Complete Pipeline
```bash
# Run the complete CI/CD pipeline locally
make ci-full
```

**Pipeline Stages**:
1. 🧹 **Clean**: Remove cache and build artifacts
2. 🔍 **Lint**: Code quality with ruff (30+ rules)
3. 🎯 **Type Check**: Static analysis with mypy
4. 🔒 **Security Scan**: Vulnerability detection with bandit
5. 🧪 **Tests**: 95%+ coverage with pytest
6. 📚 **Docs**: Sphinx documentation build
7. 🐳 **Docker**: Container image build and test

### Individual Commands
```bash
# Development workflow
make install-dev        # One-time setup
make run-dev           # Start with hot reload
make format            # Auto-format code
make quality-check     # All quality checks

# Testing
make test              # Run all tests
make test-coverage     # Tests with coverage report
make test-unit         # Unit tests only
make test-integration  # Integration tests only
make test-security     # Security tests only

# Quality assurance
make lint              # Code linting
make type-check        # Type checking
make security-check    # Security scanning
make pre-commit        # Pre-commit hooks
```

## 🧪 Testing & Quality

## 🔍 Code Quality

### Linting and Formatting
```bash
make lint               # Check code with ruff
make format             # Format code with ruff
make type-check         # Type checking with mypy
make security-check     # Security scan with bandit
```

### Pre-commit Hooks
```bash
make pre-commit         # Run all pre-commit hooks
```

### Quality Check (All)
```bash
make quality-check      # Run all quality checks
```

## 📚 API Documentation

### Interactive Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Spec**: http://localhost:8000/openapi.json

### Comprehensive Documentation
```bash
make docs-build         # Build Sphinx documentation
make docs-serve         # Serve documentation locally
```

## 🐳 Docker

### Build Image
```bash
make docker-build
```

### Run Container
```bash
make docker-run
```

### Docker Compose
```bash
docker-compose up -d
```

## 🚀 Usage Examples

### Authentication
```python
import httpx

# Login
response = httpx.post("http://localhost:8000/api/v1/auth/login", json={
    "username": "admin",
    "password": "AdminPassword123!",
    "remember_me": False
})

tokens = response.json()["tokens"]
access_token = tokens["access_token"]

# Use token for authenticated requests
headers = {"Authorization": f"Bearer {access_token}"}
user_response = httpx.get("http://localhost:8000/api/v1/auth/me", headers=headers)
```

### User Management
```python
# Create user (admin only)
user_data = {
    "username": "newuser",
    "email": "<EMAIL>",
    "full_name": "New User",
    "password": "SecurePassword123!",
    "roles": ["analyst"]
}

response = httpx.post(
    "http://localhost:8000/api/v1/users",
    json=user_data,
    headers=headers
)
```

## 👥 User Roles

| Role | Description | Key Permissions |
|------|-------------|-----------------|
| **Administrator** | Full system access | All permissions |
| **SOC Operator** | Security monitoring | Security events, incidents |
| **Security Architect** | Architecture design | Risk assessment, policies |
| **Red Team Member** | Attack simulation | Penetration testing |
| **Purple Team Member** | Collaborative testing | Cross-team coordination |
| **Analyst** | Data analysis | Reports, threat intelligence |
| **Viewer** | Read-only access | Dashboard viewing |

## 🔒 Security Features

### Authentication Security
- Secure password hashing with bcrypt
- JWT tokens with configurable expiration
- MFA with TOTP, SMS, and email support
- Account lockout after failed attempts
- Rate limiting on authentication endpoints

### Application Security
- Input validation and sanitization
- SQL injection prevention
- XSS protection with secure headers
- CSRF protection
- CORS configuration
- Security headers middleware

## 📊 Performance

### Benchmarks
- **API Response Time**: <200ms average
- **Database Queries**: Optimized with indexing
- **Concurrent Users**: Tested up to 1000 simultaneous
- **Memory Usage**: ~500MB baseline
- **CPU Usage**: <10% under normal load

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run quality checks: `make quality-check`
5. Run tests: `make test`
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Full Documentation](docs/)
- **API Reference**: http://localhost:8000/docs
- **Issues**: [GitHub Issues](https://github.com/forkrul/blast-radius/issues)
