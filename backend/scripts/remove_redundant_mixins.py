#!/usr/bin/env python3
"""Script to remove redundant mixins from MITRE models since Base already provides soft delete."""

import re
import sys
from pathlib import Path

def remove_redundant_mixins():
    """Remove redundant mixins from MITRE models."""
    
    # Path to the models file
    models_file = Path(__file__).parent.parent / "app" / "db" / "models" / "mitre.py"
    
    if not models_file.exists():
        print(f"❌ Models file not found: {models_file}")
        return False
    
    # Read the file
    with open(models_file, 'r') as f:
        content = f.read()
    
    print("🔧 Removing redundant mixins from MITRE models...")
    
    # Fix 1: Remove mixin inheritance from all class definitions
    class_updates = [
        ("class MitreTactic(Base, SoftDeleteMixin, AuditMixin):", "class MitreTactic(Base):"),
        ("class MitreGroup(Base, SoftDeleteMixin, AuditMixin):", "class MitreGroup(Base):"),
        ("class MitreSoftware(Base, SoftDeleteMixin, AuditMixin):", "class MitreSoftware(Base):"),
        ("class MitreMitigation(Base, SoftDeleteMixin, AuditMixin):", "class MitreMitigation(Base):"),
        ("class MitreDataSourceModel(Base, SoftDeleteMixin, AuditMixin):", "class MitreDataSourceModel(Base):"),
        ("class MitreCampaign(Base, SoftDeleteMixin, AuditMixin):", "class MitreCampaign(Base):"),
        ("class MitreMatrix(Base, SoftDeleteMixin, AuditMixin):", "class MitreMatrix(Base):"),
        ("class MitreDataSync(Base, SoftDeleteMixin, AuditMixin):", "class MitreDataSync(Base):"),
    ]
    
    for old_class, new_class in class_updates:
        if old_class in content:
            content = content.replace(old_class, new_class)
            print(f"  ✅ Updated {old_class}")
    
    # Write the fixed content back
    with open(models_file, 'w') as f:
        f.write(content)
    
    print("✅ Redundant mixins removed successfully!")
    return True

def main():
    """Main function."""
    print("🚀 Removing redundant mixins from MITRE models...")
    
    if remove_redundant_mixins():
        print("\n🎉 Mixins removal completed successfully!")
        print("\n📋 Summary of changes:")
        print("  • Removed SoftDeleteMixin and AuditMixin from all model classes")
        print("  • Base class already provides all soft delete and audit functionality")
        print("\n💡 The Base class provides:")
        print("  • Soft delete: is_deleted, deleted_at, deleted_by")
        print("  • Audit trail: created_at, updated_at, created_by, updated_by")
        print("  • Methods: soft_delete(), restore(), get_active_query(), get_deleted_query()")
    else:
        print("❌ Failed to remove mixins")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
