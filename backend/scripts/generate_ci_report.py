#!/usr/bin/env python3
"""
Generate comprehensive CI/CD report for Blast-Radius Security Tool.

This script aggregates results from various CI/CD pipeline stages and
generates a beautiful HTML report with metrics, charts, and insights.
"""

import json
import os
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
import xml.etree.ElementTree as ET


class CIReportGenerator:
    """Generate comprehensive CI/CD reports."""
    
    def __init__(self, reports_dir: str = "reports"):
        self.reports_dir = Path(reports_dir)
        self.timestamp = datetime.now()
        self.report_data = {
            "timestamp": self.timestamp.isoformat(),
            "pipeline_status": "SUCCESS",
            "stages": {},
            "metrics": {},
            "recommendations": []
        }
    
    def collect_test_results(self) -> Dict[str, Any]:
        """Collect test results from JUnit XML."""
        junit_file = self.reports_dir / "test" / "junit.xml"
        
        if not junit_file.exists():
            return {"status": "not_run", "tests": 0, "failures": 0, "errors": 0}
        
        try:
            tree = ET.parse(junit_file)
            root = tree.getroot()
            
            return {
                "status": "completed",
                "tests": int(root.get("tests", 0)),
                "failures": int(root.get("failures", 0)),
                "errors": int(root.get("errors", 0)),
                "time": float(root.get("time", 0)),
                "success_rate": self._calculate_success_rate(
                    int(root.get("tests", 0)),
                    int(root.get("failures", 0)) + int(root.get("errors", 0))
                )
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def collect_coverage_results(self) -> Dict[str, Any]:
        """Collect coverage results from JSON report."""
        coverage_file = self.reports_dir / "coverage" / "coverage.json"
        
        if not coverage_file.exists():
            return {"status": "not_run", "coverage": 0}
        
        try:
            with open(coverage_file) as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data["totals"]["percent_covered"]
            
            return {
                "status": "completed",
                "total_coverage": round(total_coverage, 2),
                "lines_covered": coverage_data["totals"]["covered_lines"],
                "lines_total": coverage_data["totals"]["num_statements"],
                "missing_lines": coverage_data["totals"]["missing_lines"]
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def collect_security_results(self) -> Dict[str, Any]:
        """Collect security scan results."""
        security_dir = self.reports_dir / "security"
        results = {
            "bandit": self._load_json_report(security_dir / "bandit-results.json"),
            "safety": self._load_json_report(security_dir / "safety-results.json"),
            "secrets": self._load_json_report(security_dir / "secrets-scan.json")
        }
        
        # Calculate overall security score
        total_issues = 0
        critical_issues = 0
        
        if results["bandit"].get("status") == "completed":
            bandit_results = results["bandit"].get("data", {}).get("results", [])
            total_issues += len(bandit_results)
            critical_issues += len([r for r in bandit_results if r.get("issue_severity") == "HIGH"])
        
        if results["safety"].get("status") == "completed":
            safety_vulns = results["safety"].get("data", {}).get("vulnerabilities", [])
            total_issues += len(safety_vulns)
            critical_issues += len(safety_vulns)  # All safety issues are critical
        
        if results["secrets"].get("status") == "completed":
            secrets_found = results["secrets"].get("data", {}).get("total", 0)
            total_issues += secrets_found
            critical_issues += secrets_found  # All secrets are critical
        
        return {
            "status": "completed",
            "total_issues": total_issues,
            "critical_issues": critical_issues,
            "security_score": max(0, 100 - (critical_issues * 20) - (total_issues * 5)),
            "scans": results
        }
    
    def collect_code_quality_results(self) -> Dict[str, Any]:
        """Collect code quality results."""
        ruff_file = self.reports_dir / "ruff-results.json"
        mypy_dir = self.reports_dir / "mypy"
        
        results = {
            "ruff": self._load_json_report(ruff_file),
            "mypy": {"status": "completed" if mypy_dir.exists() else "not_run"}
        }
        
        # Calculate code quality score
        quality_issues = 0
        if results["ruff"].get("status") == "completed":
            quality_issues += len(results["ruff"].get("data", []))
        
        return {
            "status": "completed",
            "quality_issues": quality_issues,
            "quality_score": max(0, 100 - (quality_issues * 2)),
            "tools": results
        }
    
    def generate_metrics(self) -> Dict[str, Any]:
        """Generate overall pipeline metrics."""
        stages = self.report_data["stages"]
        
        # Calculate overall pipeline score
        scores = []
        if stages.get("tests", {}).get("success_rate"):
            scores.append(stages["tests"]["success_rate"])
        if stages.get("coverage", {}).get("total_coverage"):
            scores.append(min(100, stages["coverage"]["total_coverage"] * 1.25))  # Boost coverage score
        if stages.get("security", {}).get("security_score"):
            scores.append(stages["security"]["security_score"])
        if stages.get("quality", {}).get("quality_score"):
            scores.append(stages["quality"]["quality_score"])
        
        overall_score = sum(scores) / len(scores) if scores else 0
        
        return {
            "overall_score": round(overall_score, 1),
            "pipeline_health": self._get_health_status(overall_score),
            "total_stages": len(stages),
            "successful_stages": len([s for s in stages.values() if s.get("status") == "completed"]),
            "duration": "~2-5 minutes",  # Estimated based on typical pipeline
            "commit_hash": self._get_git_commit(),
            "branch": self._get_git_branch()
        }
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on results."""
        recommendations = []
        stages = self.report_data["stages"]
        
        # Test recommendations
        if stages.get("tests", {}).get("success_rate", 100) < 95:
            recommendations.append("🧪 Improve test reliability - some tests are failing")
        
        # Coverage recommendations
        coverage = stages.get("coverage", {}).get("total_coverage", 0)
        if coverage < 80:
            recommendations.append(f"📊 Increase test coverage - currently at {coverage}%, target is 80%+")
        elif coverage < 90:
            recommendations.append(f"📈 Good coverage at {coverage}% - consider targeting 90%+ for critical components")
        
        # Security recommendations
        security = stages.get("security", {})
        if security.get("critical_issues", 0) > 0:
            recommendations.append("🔒 Address critical security issues immediately")
        elif security.get("total_issues", 0) > 5:
            recommendations.append("🛡️ Consider addressing non-critical security findings")
        
        # Quality recommendations
        quality = stages.get("quality", {})
        if quality.get("quality_issues", 0) > 10:
            recommendations.append("✨ Address code quality issues for better maintainability")
        
        # General recommendations
        if not recommendations:
            recommendations.extend([
                "🎉 Excellent pipeline health! All checks passing",
                "🚀 Consider adding performance benchmarks",
                "📚 Keep documentation updated with new features"
            ])
        
        return recommendations
    
    def generate_html_report(self) -> str:
        """Generate beautiful HTML report."""
        metrics = self.report_data["metrics"]
        stages = self.report_data["stages"]
        
        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blast-Radius CI/CD Report</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #0d1117; color: #c9d1d9; }}
        .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
        .header {{ text-align: center; margin-bottom: 40px; }}
        .header h1 {{ color: #58a6ff; font-size: 2.5em; margin-bottom: 10px; }}
        .header .subtitle {{ color: #8b949e; font-size: 1.2em; }}
        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px; }}
        .metric-card {{ background: #161b22; border: 1px solid #30363d; border-radius: 8px; padding: 20px; }}
        .metric-card h3 {{ color: #f0f6fc; margin-bottom: 10px; }}
        .metric-value {{ font-size: 2em; font-weight: bold; margin-bottom: 5px; }}
        .metric-value.success {{ color: #3fb950; }}
        .metric-value.warning {{ color: #d29922; }}
        .metric-value.error {{ color: #f85149; }}
        .stages-section {{ margin-bottom: 40px; }}
        .stage-card {{ background: #161b22; border: 1px solid #30363d; border-radius: 8px; padding: 20px; margin-bottom: 20px; }}
        .stage-header {{ display: flex; justify-content: between; align-items: center; margin-bottom: 15px; }}
        .stage-status {{ padding: 4px 12px; border-radius: 20px; font-size: 0.9em; font-weight: bold; }}
        .status-success {{ background: #238636; color: white; }}
        .status-error {{ background: #da3633; color: white; }}
        .status-warning {{ background: #bf8700; color: white; }}
        .recommendations {{ background: #161b22; border: 1px solid #30363d; border-radius: 8px; padding: 20px; }}
        .recommendations h3 {{ color: #f0f6fc; margin-bottom: 15px; }}
        .recommendations ul {{ list-style: none; }}
        .recommendations li {{ padding: 8px 0; border-bottom: 1px solid #21262d; }}
        .footer {{ text-align: center; margin-top: 40px; color: #8b949e; }}
        .progress-bar {{ width: 100%; height: 8px; background: #21262d; border-radius: 4px; overflow: hidden; }}
        .progress-fill {{ height: 100%; transition: width 0.3s ease; }}
        .progress-success {{ background: #3fb950; }}
        .progress-warning {{ background: #d29922; }}
        .progress-error {{ background: #f85149; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Blast-Radius CI/CD Report</h1>
            <div class="subtitle">Pipeline executed on {self.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}</div>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>📊 Overall Score</h3>
                <div class="metric-value {self._get_score_class(metrics.get('overall_score', 0))}">{metrics.get('overall_score', 0)}%</div>
                <div class="progress-bar">
                    <div class="progress-fill {self._get_progress_class(metrics.get('overall_score', 0))}" 
                         style="width: {metrics.get('overall_score', 0)}%"></div>
                </div>
            </div>
            
            <div class="metric-card">
                <h3>🧪 Test Success Rate</h3>
                <div class="metric-value {self._get_score_class(stages.get('tests', {}).get('success_rate', 0))}">{stages.get('tests', {}).get('success_rate', 0)}%</div>
                <div>Tests: {stages.get('tests', {}).get('tests', 0)} | Failures: {stages.get('tests', {}).get('failures', 0)}</div>
            </div>
            
            <div class="metric-card">
                <h3>📈 Code Coverage</h3>
                <div class="metric-value {self._get_score_class(stages.get('coverage', {}).get('total_coverage', 0))}">{stages.get('coverage', {}).get('total_coverage', 0)}%</div>
                <div>Lines: {stages.get('coverage', {}).get('lines_covered', 0)}/{stages.get('coverage', {}).get('lines_total', 0)}</div>
            </div>
            
            <div class="metric-card">
                <h3>🔒 Security Score</h3>
                <div class="metric-value {self._get_score_class(stages.get('security', {}).get('security_score', 0))}">{stages.get('security', {}).get('security_score', 0)}%</div>
                <div>Issues: {stages.get('security', {}).get('total_issues', 0)} | Critical: {stages.get('security', {}).get('critical_issues', 0)}</div>
            </div>
        </div>
        
        <div class="recommendations">
            <h3>💡 Recommendations</h3>
            <ul>
                {''.join([f'<li>{rec}</li>' for rec in self.report_data.get('recommendations', [])])}
            </ul>
        </div>
        
        <div class="footer">
            <p>Generated by Blast-Radius CI/CD Pipeline | Commit: {metrics.get('commit_hash', 'unknown')[:8]} | Branch: {metrics.get('branch', 'unknown')}</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html_template
    
    def _load_json_report(self, file_path: Path) -> Dict[str, Any]:
        """Load JSON report file."""
        if not file_path.exists():
            return {"status": "not_run"}
        
        try:
            with open(file_path) as f:
                data = json.load(f)
            return {"status": "completed", "data": data}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def _calculate_success_rate(self, total: int, failures: int) -> float:
        """Calculate test success rate."""
        if total == 0:
            return 100.0
        return round(((total - failures) / total) * 100, 1)
    
    def _get_health_status(self, score: float) -> str:
        """Get health status based on score."""
        if score >= 90:
            return "EXCELLENT"
        elif score >= 80:
            return "GOOD"
        elif score >= 70:
            return "FAIR"
        else:
            return "NEEDS_ATTENTION"
    
    def _get_score_class(self, score: float) -> str:
        """Get CSS class for score."""
        if score >= 80:
            return "success"
        elif score >= 60:
            return "warning"
        else:
            return "error"
    
    def _get_progress_class(self, score: float) -> str:
        """Get CSS class for progress bar."""
        if score >= 80:
            return "progress-success"
        elif score >= 60:
            return "progress-warning"
        else:
            return "progress-error"
    
    def _get_git_commit(self) -> str:
        """Get current git commit hash."""
        try:
            result = subprocess.run(["git", "rev-parse", "HEAD"], 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except:
            return "unknown"
    
    def _get_git_branch(self) -> str:
        """Get current git branch."""
        try:
            result = subprocess.run(["git", "branch", "--show-current"], 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except:
            return "unknown"
    
    def generate_report(self) -> None:
        """Generate complete CI/CD report."""
        print("📊 Collecting CI/CD results...")
        
        # Collect results from all stages
        self.report_data["stages"]["tests"] = self.collect_test_results()
        self.report_data["stages"]["coverage"] = self.collect_coverage_results()
        self.report_data["stages"]["security"] = self.collect_security_results()
        self.report_data["stages"]["quality"] = self.collect_code_quality_results()
        
        # Generate metrics and recommendations
        self.report_data["metrics"] = self.generate_metrics()
        self.report_data["recommendations"] = self.generate_recommendations()
        
        # Generate HTML report
        html_content = self.generate_html_report()
        
        # Save reports
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
        # Save JSON report
        with open(self.reports_dir / "ci-summary.json", "w") as f:
            json.dump(self.report_data, f, indent=2)
        
        # Save HTML report
        with open(self.reports_dir / "ci-summary.html", "w") as f:
            f.write(html_content)
        
        print(f"✅ CI/CD report generated: {self.reports_dir / 'ci-summary.html'}")
        print(f"📊 Overall Score: {self.report_data['metrics']['overall_score']}%")
        print(f"🏥 Pipeline Health: {self.report_data['metrics']['pipeline_health']}")


def main():
    """Main entry point."""
    generator = CIReportGenerator()
    generator.generate_report()


if __name__ == "__main__":
    main()
