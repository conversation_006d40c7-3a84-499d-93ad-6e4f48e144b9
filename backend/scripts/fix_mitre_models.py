#!/usr/bin/env python3
"""Script to fix MITRE models with soft delete and correct enum references."""

import re
import sys
from pathlib import Path

def fix_mitre_models():
    """Fix MITRE models file."""
    
    # Path to the models file
    models_file = Path(__file__).parent.parent / "app" / "db" / "models" / "mitre.py"
    
    if not models_file.exists():
        print(f"❌ Models file not found: {models_file}")
        return False
    
    # Read the file
    with open(models_file, 'r') as f:
        content = f.read()
    
    print("🔧 Fixing MITRE models...")
    
    # Fix 1: Update all class definitions to inherit from SoftDeleteMixin and AuditMixin
    class_updates = [
        ("class MitreSoftware(Base):", "class MitreSoftware(Base, SoftDeleteMixin, AuditMixin):"),
        ("class MitreMitigation(Base):", "class MitreMitigation(Base, SoftDeleteMixin, AuditMixin):"),
        ("class MitreDataSource(Base):", "class MitreDataSource(Base, SoftDeleteMixin, AuditMixin):"),
        ("class MitreCampaign(Base):", "class MitreCampaign(Base, SoftDeleteMixin, AuditMixin):"),
        ("class MitreMatrix(Base):", "class MitreMatrix(Base, SoftDeleteMixin, AuditMixin):"),
        ("class MitreDataSync(Base):", "class MitreDataSync(Base, SoftDeleteMixin, AuditMixin):"),
    ]
    
    for old_class, new_class in class_updates:
        if old_class in content:
            content = content.replace(old_class, new_class)
            print(f"  ✅ Updated {old_class}")
    
    # Fix 2: Replace all MitreDataSource enum references with MitreDataSourceType
    enum_fixes = [
        ("Enum(MitreDataSource)", "Enum(MitreDataSourceType)"),
        ("default=MitreDataSource.OFFICIAL", "default=MitreDataSourceType.OFFICIAL"),
    ]
    
    for old_enum, new_enum in enum_fixes:
        count = content.count(old_enum)
        if count > 0:
            content = content.replace(old_enum, new_enum)
            print(f"  ✅ Fixed {count} enum references: {old_enum} -> {new_enum}")
    
    # Fix 3: Remove duplicate created_date and modified_date columns (provided by AuditMixin)
    # Pattern to match the versioning and tracking section with duplicate fields
    versioning_pattern = r'(\s+# Versioning and tracking\s+version = Column\(String\(10\), nullable=False, default="1\.0"\)\s+)created_date = Column\(DateTime, nullable=False, default=func\.now\(\)\)\s+modified_date = Column\(DateTime, nullable=False, default=func\.now\(\), onupdate=func\.now\(\)\)\s+'
    
    # Replace with just the version field
    replacement = r'\1'
    
    content = re.sub(versioning_pattern, replacement, content)
    print("  ✅ Removed duplicate audit fields")
    
    # Fix 4: Update docstrings to mention soft delete capabilities
    docstring_updates = [
        ('"""MITRE ATT&CK Software (Malware/Tool) model."""', '"""MITRE ATT&CK Software (Malware/Tool) model with soft delete and audit capabilities."""'),
        ('"""MITRE ATT&CK Mitigation model."""', '"""MITRE ATT&CK Mitigation model with soft delete and audit capabilities."""'),
        ('"""MITRE ATT&CK Data Source model."""', '"""MITRE ATT&CK Data Source model with soft delete and audit capabilities."""'),
        ('"""MITRE ATT&CK Campaign model."""', '"""MITRE ATT&CK Campaign model with soft delete and audit capabilities."""'),
        ('"""MITRE ATT&CK Matrix model for storing matrix configurations."""', '"""MITRE ATT&CK Matrix model with soft delete and audit capabilities."""'),
        ('"""MITRE data synchronization tracking model."""', '"""MITRE data synchronization tracking model with soft delete and audit capabilities."""'),
    ]
    
    for old_doc, new_doc in docstring_updates:
        if old_doc in content:
            content = content.replace(old_doc, new_doc)
            print(f"  ✅ Updated docstring")
    
    # Fix 5: Handle the naming conflict with MitreDataSource class
    # Rename the class to MitreDataSourceModel to avoid conflict with enum
    if "class MitreDataSource(Base" in content:
        content = content.replace(
            "class MitreDataSource(Base, SoftDeleteMixin, AuditMixin):",
            "class MitreDataSourceModel(Base, SoftDeleteMixin, AuditMixin):"
        )
        content = content.replace(
            '__tablename__ = "mitre_data_sources"',
            '__tablename__ = "mitre_data_sources"'
        )
        content = content.replace(
            "return f\"<MitreDataSource(id='{self.data_source_id}', name='{self.name}')>\"",
            "return f\"<MitreDataSourceModel(id='{self.data_source_id}', name='{self.name}')>\""
        )
        print("  ✅ Renamed MitreDataSource class to MitreDataSourceModel")
    
    # Write the fixed content back
    with open(models_file, 'w') as f:
        f.write(content)
    
    print("✅ MITRE models fixed successfully!")
    return True

def update_model_imports():
    """Update __init__.py to reflect the renamed model."""
    init_file = Path(__file__).parent.parent / "app" / "db" / "models" / "__init__.py"
    
    if not init_file.exists():
        print(f"⚠️  Init file not found: {init_file}")
        return
    
    with open(init_file, 'r') as f:
        content = f.read()
    
    # Update import and __all__ list
    if "MitreDataSource," in content:
        content = content.replace("MitreDataSource,", "MitreDataSourceModel,")
        content = content.replace('"MitreDataSource",', '"MitreDataSourceModel",')
        
        with open(init_file, 'w') as f:
            f.write(content)
        
        print("✅ Updated model imports")

def main():
    """Main function."""
    print("🚀 Starting MITRE models fix...")
    
    if fix_mitre_models():
        update_model_imports()
        print("\n🎉 All fixes completed successfully!")
        print("\n📋 Summary of changes:")
        print("  • Added SoftDeleteMixin and AuditMixin to all models")
        print("  • Fixed MitreDataSource enum references")
        print("  • Removed duplicate audit fields")
        print("  • Updated docstrings")
        print("  • Renamed MitreDataSource class to avoid conflicts")
        print("\n💡 Next steps:")
        print("  • Run database migration to add soft delete columns")
        print("  • Update service layer to use soft delete methods")
        print("  • Test the updated models")
    else:
        print("❌ Failed to fix models")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
