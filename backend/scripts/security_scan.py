#!/usr/bin/env python3
"""
Comprehensive security scanning script for Blast-Radius Security Tool.

This script orchestrates various security testing tools to provide
comprehensive security validation of the application.
"""

import subprocess
import sys
import json
import os
from pathlib import Path
from typing import Dict, List, Any
import argparse


class SecurityScanner:
    """Orchestrates security scanning tools."""
    
    def __init__(self, output_dir: str = "reports/security"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = {}
    
    def run_bandit_scan(self) -> Dict[str, Any]:
        """Run Bandit static security analysis."""
        print("🔍 Running Bandit security scan...")
        
        try:
            result = subprocess.run([
                "bandit", "-r", "app/", 
                "-f", "json", 
                "-o", str(self.output_dir / "bandit-results.json")
            ], capture_output=True, text=True, check=False)
            
            # Also generate human-readable report
            subprocess.run([
                "bandit", "-r", "app/", 
                "-f", "txt", 
                "-o", str(self.output_dir / "bandit-report.txt")
            ], check=False)
            
            return {
                "status": "completed",
                "exit_code": result.returncode,
                "issues_found": result.returncode > 0,
                "output_file": str(self.output_dir / "bandit-results.json")
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def run_safety_scan(self) -> Dict[str, Any]:
        """Run Safety dependency vulnerability scan."""
        print("🛡️ Running Safety dependency scan...")
        
        try:
            result = subprocess.run([
                "safety", "check", 
                "--json", 
                "--output", str(self.output_dir / "safety-results.json")
            ], capture_output=True, text=True, check=False)
            
            return {
                "status": "completed",
                "exit_code": result.returncode,
                "vulnerabilities_found": result.returncode > 0,
                "output_file": str(self.output_dir / "safety-results.json")
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def run_semgrep_scan(self) -> Dict[str, Any]:
        """Run Semgrep security analysis."""
        print("🔎 Running Semgrep security analysis...")
        
        try:
            result = subprocess.run([
                "semgrep", "--config=auto", 
                "--json", 
                "--output", str(self.output_dir / "semgrep-results.json"),
                "app/"
            ], capture_output=True, text=True, check=False)
            
            return {
                "status": "completed",
                "exit_code": result.returncode,
                "findings": result.returncode > 0,
                "output_file": str(self.output_dir / "semgrep-results.json")
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def run_docker_security_scan(self) -> Dict[str, Any]:
        """Run Docker security scanning."""
        print("🐳 Running Docker security scan...")
        
        try:
            # Check if Docker is available
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            
            # Scan the application image if it exists
            result = subprocess.run([
                "docker", "run", "--rm", "-v", "/var/run/docker.sock:/var/run/docker.sock",
                "-v", f"{os.getcwd()}:/app", "aquasec/trivy", 
                "fs", "--format", "json", 
                "--output", "/app/reports/security/trivy-results.json", "/app"
            ], capture_output=True, text=True, check=False)
            
            return {
                "status": "completed",
                "exit_code": result.returncode,
                "vulnerabilities_found": result.returncode > 0,
                "output_file": str(self.output_dir / "trivy-results.json")
            }
        except subprocess.CalledProcessError:
            return {"status": "skipped", "reason": "Docker not available"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def run_secrets_scan(self) -> Dict[str, Any]:
        """Scan for secrets and sensitive data."""
        print("🔐 Running secrets scan...")
        
        try:
            result = subprocess.run([
                "python", "-c", """
import re
import os
from pathlib import Path

patterns = {
    'api_key': r'(?i)(api[_-]?key|apikey)\\s*[:=]\\s*[\'"]?([a-zA-Z0-9_-]{20,})[\'"]?',
    'password': r'(?i)(password|passwd|pwd)\\s*[:=]\\s*[\'"]?([^\\s\'"]{8,})[\'"]?',
    'secret': r'(?i)(secret|token)\\s*[:=]\\s*[\'"]?([a-zA-Z0-9_-]{20,})[\'"]?',
    'private_key': r'-----BEGIN [A-Z ]+PRIVATE KEY-----',
    'aws_key': r'AKIA[0-9A-Z]{16}',
    'jwt': r'eyJ[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*'
}

findings = []
for root, dirs, files in os.walk('app'):
    for file in files:
        if file.endswith(('.py', '.yaml', '.yml', '.json', '.env')):
            filepath = Path(root) / file
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for pattern_name, pattern in patterns.items():
                        matches = re.finditer(pattern, content)
                        for match in matches:
                            findings.append({
                                'file': str(filepath),
                                'pattern': pattern_name,
                                'line': content[:match.start()].count('\\n') + 1,
                                'match': match.group()[:50] + '...' if len(match.group()) > 50 else match.group()
                            })
            except Exception:
                continue

import json
with open('reports/security/secrets-scan.json', 'w') as f:
    json.dump({'findings': findings, 'total': len(findings)}, f, indent=2)
print(f'Found {len(findings)} potential secrets')
"""
            ], capture_output=True, text=True, check=False)
            
            return {
                "status": "completed",
                "exit_code": result.returncode,
                "secrets_found": "Found" in result.stdout and "0 potential" not in result.stdout,
                "output_file": str(self.output_dir / "secrets-scan.json")
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def generate_summary_report(self) -> None:
        """Generate a comprehensive security summary report."""
        print("📊 Generating security summary report...")
        
        summary = {
            "scan_timestamp": subprocess.run(["date", "-Iseconds"], 
                                           capture_output=True, text=True).stdout.strip(),
            "scans_performed": self.results,
            "overall_status": "PASS",
            "critical_issues": 0,
            "recommendations": []
        }
        
        # Analyze results and determine overall status
        for scan_name, result in self.results.items():
            if result.get("status") == "error":
                summary["overall_status"] = "ERROR"
            elif result.get("issues_found") or result.get("vulnerabilities_found") or result.get("secrets_found"):
                summary["overall_status"] = "FAIL"
                summary["critical_issues"] += 1
        
        # Add recommendations
        if summary["critical_issues"] > 0:
            summary["recommendations"].extend([
                "Review and fix all identified security vulnerabilities",
                "Update dependencies with known security issues",
                "Remove or secure any exposed secrets",
                "Run security scans regularly in CI/CD pipeline"
            ])
        
        # Save summary report
        with open(self.output_dir / "security-summary.json", "w") as f:
            json.dump(summary, f, indent=2)
        
        # Print summary to console
        print(f"\n{'='*60}")
        print("🔒 SECURITY SCAN SUMMARY")
        print(f"{'='*60}")
        print(f"Overall Status: {summary['overall_status']}")
        print(f"Critical Issues: {summary['critical_issues']}")
        print(f"Scans Completed: {len([r for r in self.results.values() if r.get('status') == 'completed'])}")
        print(f"Report Location: {self.output_dir}")
        
        if summary["recommendations"]:
            print("\n📋 RECOMMENDATIONS:")
            for i, rec in enumerate(summary["recommendations"], 1):
                print(f"  {i}. {rec}")
    
    def run_all_scans(self) -> None:
        """Run all security scans."""
        print("🚀 Starting comprehensive security scan...")
        
        self.results["bandit"] = self.run_bandit_scan()
        self.results["safety"] = self.run_safety_scan()
        self.results["semgrep"] = self.run_semgrep_scan()
        self.results["docker"] = self.run_docker_security_scan()
        self.results["secrets"] = self.run_secrets_scan()
        
        self.generate_summary_report()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run security scans for Blast-Radius")
    parser.add_argument("--output-dir", default="reports/security", 
                       help="Output directory for reports")
    parser.add_argument("--scan", choices=["bandit", "safety", "semgrep", "docker", "secrets", "all"],
                       default="all", help="Specific scan to run")
    
    args = parser.parse_args()
    
    scanner = SecurityScanner(args.output_dir)
    
    if args.scan == "all":
        scanner.run_all_scans()
    elif args.scan == "bandit":
        scanner.results["bandit"] = scanner.run_bandit_scan()
    elif args.scan == "safety":
        scanner.results["safety"] = scanner.run_safety_scan()
    elif args.scan == "semgrep":
        scanner.results["semgrep"] = scanner.run_semgrep_scan()
    elif args.scan == "docker":
        scanner.results["docker"] = scanner.run_docker_security_scan()
    elif args.scan == "secrets":
        scanner.results["secrets"] = scanner.run_secrets_scan()
    
    if args.scan != "all":
        scanner.generate_summary_report()


if __name__ == "__main__":
    main()
