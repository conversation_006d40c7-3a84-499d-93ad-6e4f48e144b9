[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "blast-radius-backend"
version = "0.0.1"
description = "Blast-Radius Security Tool Backend API"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Blast-Radius Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Security",
    "Topic :: System :: Monitoring",
    "Topic :: System :: Systems Administration",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.12.1",
    "psycopg2-binary>=2.9.9",
    "redis>=5.0.1",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-jose[cryptography]>=3.5.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "email-validator>=2.1.0",
    "pyotp>=2.9.0",
    "qrcode[pil]>=7.4.2",
    "python-dateutil>=2.8.2",
    "httpx>=0.25.2",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "cryptography>=42.0.2",
    "pyjwt>=2.4.0",
    "oauthlib>=3.2.1",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
    "pre-commit>=3.6.0",
]
docs = [
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.25.2",
    "myst-parser>=2.0.0",
    "sphinx-copybutton>=0.5.2",
    "sphinxcontrib-openapi>=0.8.1",
]
test = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]

[project.urls]
Homepage = "https://github.com/forkrul/blast-radius"
Documentation = "https://blast-radius.readthedocs.io"
Repository = "https://github.com/forkrul/blast-radius"
Issues = "https://github.com/forkrul/blast-radius/issues"

[project.scripts]
blast-radius = "app.main:main"

# Ruff configuration for code quality
[tool.ruff]
target-version = "py311"
line-length = 88

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "C90", # mccabe
    "T20", # flake8-print
    "DJ",  # flake8-django
    "PL",  # pylint
    "PIE", # flake8-pie
    "T10", # flake8-debugger
    "PYI", # flake8-pyi
    "PT",  # flake8-pytest-style
    "Q",   # flake8-quotes
    "RSE", # flake8-raise
    "RET", # flake8-return
    "SLF", # flake8-self
    "SIM", # flake8-simplify
    "TID", # flake8-tidy-imports
    "TCH", # flake8-type-checking
    "INT", # flake8-gettext
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate
    "PD",  # pandas-vet
    "PGH", # pygrep-hooks
    "FLY", # flynt
    "NPY", # NumPy-specific rules
    "PERF", # Perflint
    "RUF", # Ruff-specific rules
]
ignore = [
    "E501",   # line too long, handled by formatter
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "W191",   # indentation contains tabs
    "PLR0913", # too many arguments
    "PLR0915", # too many statements
    "PLR2004", # magic value used in comparison
    "PLR0911", # too many return statements
    "B904",   # raise from exception
    "ARG001", # unused function argument
    "ARG002", # unused method argument
    "TID252", # prefer absolute imports (we use relative imports in packages)
    "RUF012", # mutable class attributes (Pydantic schema examples)
    "SLF001", # private member access (needed for audit logging)
    "E712",   # comparison to True/False (SQLAlchemy style)
    "F821",   # undefined name (will be fixed separately)
    "RET504", # unnecessary assignment before return
    "PERF401", # use list comprehension
    "UP038",  # use X | Y instead of (X, Y)
    "SIM102", # use single if statement
    "B007",   # loop control variable not used
    "PERF102", # use .keys() instead of .items()
    "RUF022", # __all__ is not sorted
    "C416",   # unnecessary dict comprehension
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*.py" = [
    "S101",   # use of assert
    "ARG",    # unused function args
    "FBT",    # boolean trap
    "PLR2004", # magic value used in comparison
    "S311",   # standard pseudo-random generators
]

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.isort]
known-first-party = ["app"]
force-sort-within-sections = true

[tool.ruff.lint.flake8-tidy-imports]
ban-relative-imports = "all"

[tool.ruff.lint.flake8-type-checking]
strict = true

# MyPy configuration for type checking
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
no_implicit_reexport = true
strict_equality = true
warn_redundant_casts = true
warn_return_any = true
warn_unreachable = true
warn_unused_configs = true
warn_unused_ignores = true

[[tool.mypy.overrides]]
module = [
    "passlib.*",
    "jose.*",
    "pyotp.*",
    "qrcode.*",
    "factory.*",
    "faker.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=95",
]
testpaths = ["tests"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "security: marks tests as security tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"
