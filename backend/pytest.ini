[tool:pytest]
# Pytest configuration for Blast-Radius Security Tool

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=95
    --junitxml=test-results.xml
    --maxfail=5
    --disable-warnings

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    auth: Authentication tests
    acl: Access control tests
    slow: Slow running tests
    critical: Critical functionality tests
    security: Security-related tests
    performance: Performance tests
    smoke: Smoke tests for basic functionality

# Test environment
env = 
    TESTING = true
    DATABASE_URL = postgresql://blastradius_test:blastradius_test_password_2024@localhost:5432/blastradius_test
    REDIS_URL = redis://localhost:6379/1
    NEO4J_URL = bolt://localhost:7687
    NEO4J_USER = neo4j
    NEO4J_PASSWORD = blastradius_neo4j_secure_password_2024
    SECRET_KEY = test-secret-key-for-testing-only
    ALGORITHM = HS256
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    DEBUG = false
    LOG_LEVEL = WARNING

# Asyncio configuration
asyncio_mode = auto

# Filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning
    error::UserWarning

# Coverage configuration
[coverage:run]
source = app
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */migrations/*
    */alembic/*
    app/main.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
