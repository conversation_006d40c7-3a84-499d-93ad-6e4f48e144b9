"""Setup configuration for Blast-Radius Security Tool backend."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="blast-radius-backend",
    version="1.0.0",
    author="Blast-Radius Security Tool Team",
    author_email="<EMAIL>",
    description="Backend API for Blast-Radius Security Tool - Comprehensive security platform for attack path analysis",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/forkrul/blast-radius",
    packages=find_packages(where=".", include=["app*"]),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Information Technology",
        "Topic :: Security",
        "Topic :: System :: Monitoring",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Framework :: FastAPI",
        "Framework :: AsyncIO",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.12.0",
            "pytest-xdist>=3.5.0",
            "black>=23.11.0",
            "isort>=5.12.0",
            "flake8>=6.1.0",
            "ruff>=0.1.6",
            "mypy>=1.7.1",
            "pydoclint>=0.3.8",
        ],
        "docs": [
            "sphinx>=7.2.6",
            "sphinx-rtd-theme>=1.3.0",
            "sphinx-autodoc-typehints>=1.25.2",
        ],
        "testing": [
            "behave>=1.2.6",
            "factory-boy>=3.3.0",
            "parameterized>=0.9.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "blast-radius-api=app.main:main",
            "blast-radius-worker=app.worker:main",
            "blast-radius-scheduler=app.scheduler:main",
        ],
    },
    include_package_data=True,
    package_data={
        "app": ["*.yaml", "*.yml", "*.json", "*.sql"],
    },
    zip_safe=False,
    keywords="security, attack-path, graph-analysis, threat-intelligence, soc, red-team, purple-team",
    project_urls={
        "Bug Reports": "https://github.com/forkrul/blast-radius/issues",
        "Source": "https://github.com/forkrul/blast-radius",
        "Documentation": "https://blast-radius.readthedocs.io/",
    },
)
