Blast-Radius Security Tool Documentation
=========================================

.. image:: _static/logo.png
   :alt: Blast-Radius Security Tool
   :align: center
   :width: 300px

Welcome to the comprehensive documentation for the **Blast-Radius Security Tool**,
a cutting-edge security platform designed for purple teams, SOC operators,
security architects, and red teamers.

.. important::
   🚀 **New in v0.0.1**: Complete authentication system with role-based access control,
   comprehensive API documentation, and production-ready infrastructure.

.. note::
   This documentation covers version |version| of the Blast-Radius Security Tool.
   For the latest updates, visit our `GitHub repository <https://github.com/forkrul/blast-radius>`_.

.. tip::
   **Quick Start**: Jump to our :doc:`user-guide/quick-start` guide to get up and running in minutes!

Quick Start
-----------

Get up and running with Blast-Radius in minutes:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius/backend
   
   # Install dependencies
   make install-dev
   
   # Run the application
   make run-dev

Features Overview
-----------------

🔐 **Enterprise Authentication & Security**
   - **Multi-factor authentication (MFA)** with TOTP, SMS, and email support
   - **Role-based access control (RBAC)** with 7 distinct user roles
   - **Secure JWT token management** with access and refresh tokens
   - **Session management** with configurable timeouts and security controls
   - **API key authentication** for programmatic access with scoped permissions
   - **Account lockout protection** and intelligent rate limiting

👥 **Advanced User Management**
   - **Comprehensive user lifecycle** management with audit trails
   - **Granular permission system** with 25+ fine-grained permissions
   - **Bulk operations** for user import/export and role management
   - **Activity tracking** and comprehensive audit logging
   - **Self-service capabilities** for password changes and MFA setup

🏗️ **Production-Ready Architecture**
   - **FastAPI framework** with async/await support and automatic OpenAPI docs
   - **PostgreSQL database** with SQLAlchemy ORM and Alembic migrations
   - **Redis caching** for sessions, rate limiting, and performance
   - **Comprehensive error handling** with structured logging
   - **Docker containerization** with security best practices
   - **Local CI/CD pipeline** with automated quality gates

🧪 **Quality Assurance & Security**
   - **95%+ test coverage** with pytest and comprehensive test suite
   - **Static type checking** with mypy for enhanced code reliability
   - **Code quality enforcement** with ruff (modern Python linter)
   - **Security scanning** with bandit for vulnerability detection
   - **Pre-commit hooks** for automated quality gates
   - **Zero known vulnerabilities** with regular dependency updates

📊 **Monitoring & Observability**
   - **Structured logging** with configurable levels and formats
   - **Health check endpoints** for monitoring and alerting
   - **Performance metrics** and request timing
   - **Audit trails** for compliance and security investigations
   - **Error tracking** with detailed stack traces and context

🚀 **Developer Experience**
   - **One-command setup** with comprehensive Makefile
   - **Interactive API documentation** with Swagger UI and ReDoc
   - **Professional documentation** with Sphinx and custom themes
   - **Local development tools** with hot reloading and debugging
   - **Automated code formatting** and quality checks

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   user-guide/installation
   user-guide/quick-start
   user-guide/authentication
   user-guide/user-management
   user-guide/api-usage
   user-guide/configuration

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/authentication
   api/users
   api/roles
   api/permissions
   api/sessions
   api/health

.. toctree::
   :maxdepth: 2
   :caption: Developer Guide

   developer/setup
   developer/local-cicd
   developer/architecture
   developer/testing
   developer/contributing
   developer/deployment

.. toctree::
   :maxdepth: 2
   :caption: Security

   security/overview
   security/authentication
   security/authorization
   security/best-practices
   security/compliance

.. toctree::
   :maxdepth: 2
   :caption: Administration

   admin/installation
   admin/configuration
   admin/monitoring
   admin/backup
   admin/troubleshooting

.. toctree::
   :maxdepth: 1
   :caption: Reference

   reference/changelog
   reference/faq
   reference/glossary
   reference/license

User Roles
----------

The Blast-Radius Security Tool supports seven distinct user roles, each with specific permissions and capabilities:

.. list-table:: User Roles Overview
   :header-rows: 1
   :widths: 20 80

   * - Role
     - Description
   * - **Administrator**
     - Full system access and user management capabilities
   * - **SOC Operator**
     - Security event monitoring and incident response
   * - **Security Architect**
     - Architecture design and risk assessment
   * - **Red Team Member**
     - Attack simulation and penetration testing
   * - **Purple Team Member**
     - Collaborative security testing and improvement
   * - **Analyst**
     - Data analysis and threat intelligence research
   * - **Viewer**
     - Read-only access to dashboards and reports

API Documentation
-----------------

Interactive API documentation is available at:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Spec**: http://localhost:8000/openapi.json

Authentication Example
----------------------

Here's a quick example of how to authenticate with the API:

.. code-block:: python

   import httpx

   # Login to get access token
   response = httpx.post("http://localhost:8000/api/v1/auth/login", json={
       "username": "admin",
       "password": "AdminPassword123!",
       "remember_me": False
   })
   
   tokens = response.json()["tokens"]
   access_token = tokens["access_token"]
   
   # Use token for authenticated requests
   headers = {"Authorization": f"Bearer {access_token}"}
   user_response = httpx.get("http://localhost:8000/api/v1/auth/me", headers=headers)
   user_info = user_response.json()

Support and Community
---------------------

- **GitHub Repository**: https://github.com/forkrul/blast-radius
- **Issue Tracker**: https://github.com/forkrul/blast-radius/issues
- **Documentation**: https://blast-radius.readthedocs.io
- **License**: MIT License

Contributing
------------

We welcome contributions! Please see our :doc:`developer/contributing` guide for details on:

- Setting up the development environment
- Running tests
- Submitting pull requests
- Code style guidelines

License
-------

This project is licensed under the MIT License. See the :doc:`reference/license` file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
