/* Custom CSS for Blast-Radius Documentation */

/* Color scheme */
:root {
  --primary-color: #2196f3;
  --secondary-color: #1976d2;
  --accent-color: #03a9f4;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --dark-bg: #1e1e1e;
  --light-bg: #f5f5f5;
}

/* Logo styling */
.logo img {
  max-height: 60px;
  width: auto;
}

/* API endpoint styling */
.api-endpoint {
  background-color: var(--light-bg);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  color: var(--primary-color);
}

/* HTTP method styling */
.http-method-get {
  background-color: #4caf50;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 0.8em;
}

.http-method-post {
  background-color: #2196f3;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 0.8em;
}

.http-method-put {
  background-color: #ff9800;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 0.8em;
}

.http-method-delete {
  background-color: #f44336;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 0.8em;
}

.http-method-patch {
  background-color: #9c27b0;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 0.8em;
}

/* Code block enhancements */
.highlight {
  border-radius: 6px;
  border: 1px solid #e1e4e8;
}

.highlight pre {
  margin: 0;
  padding: 16px;
  overflow: auto;
  font-size: 14px;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
}

/* Dark theme code blocks */
@media (prefers-color-scheme: dark) {
  .highlight {
    border-color: #30363d;
  }
  
  .highlight pre {
    background-color: #161b22;
    color: #c9d1d9;
  }
}

/* Table styling */
.rst-content table.docutils {
  border: 1px solid #e1e4e8;
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.rst-content table.docutils th,
.rst-content table.docutils td {
  border: 1px solid #e1e4e8;
  padding: 8px 12px;
  text-align: left;
}

.rst-content table.docutils th {
  background-color: #f6f8fa;
  font-weight: 600;
}

/* Admonition styling */
.rst-content .admonition {
  border-radius: 6px;
  border: 1px solid;
  margin: 1em 0;
  padding: 0;
}

.rst-content .admonition .admonition-title {
  border-radius: 6px 6px 0 0;
  font-weight: 600;
  margin: 0;
  padding: 8px 16px;
}

.rst-content .admonition p {
  margin: 16px;
}

.rst-content .note {
  border-color: #0969da;
}

.rst-content .note .admonition-title {
  background-color: #dbeafe;
  color: #0969da;
}

.rst-content .warning {
  border-color: #d1242f;
}

.rst-content .warning .admonition-title {
  background-color: #ffebe9;
  color: #d1242f;
}

.rst-content .tip {
  border-color: #1a7f37;
}

.rst-content .tip .admonition-title {
  background-color: #dcfce7;
  color: #1a7f37;
}

/* Navigation improvements */
.wy-nav-side {
  background: linear-gradient(180deg, #2980b9 0%, #3498db 100%);
}

.wy-menu-vertical a {
  color: #ffffff;
}

.wy-menu-vertical a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.wy-menu-vertical li.current a {
  background-color: rgba(255, 255, 255, 0.2);
  border-right: 3px solid #ffffff;
}

/* Search box styling */
.wy-side-nav-search {
  background: #2980b9;
}

.wy-side-nav-search input[type=text] {
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.wy-side-nav-search input[type=text]::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Content area improvements */
.rst-content {
  max-width: none;
}

.rst-content h1,
.rst-content h2,
.rst-content h3,
.rst-content h4,
.rst-content h5,
.rst-content h6 {
  color: #2c3e50;
  font-weight: 600;
}

.rst-content h1 {
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.rst-content h2 {
  border-bottom: 1px solid #bdc3c7;
  padding-bottom: 4px;
}

/* Link styling */
.rst-content a {
  color: #3498db;
  text-decoration: none;
}

.rst-content a:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* Button styling */
.btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  background-color: #3498db;
  color: #ffffff;
  border-color: #3498db;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

/* Footer styling */
.rst-footer-buttons {
  margin-top: 2em;
  padding-top: 1em;
  border-top: 1px solid #e1e4e8;
}

/* Responsive design */
@media (max-width: 768px) {
  .wy-nav-side {
    left: -300px;
  }
  
  .wy-nav-content-wrap {
    margin-left: 0;
  }
  
  .rst-content table.docutils {
    font-size: 0.9em;
  }
  
  .rst-content .highlight pre {
    font-size: 12px;
    padding: 12px;
  }
}

/* Print styles */
@media print {
  .wy-nav-side,
  .wy-nav-top,
  .rst-footer-buttons {
    display: none !important;
  }
  
  .wy-nav-content-wrap {
    margin-left: 0 !important;
  }
  
  .rst-content {
    color: #000000;
  }
  
  .rst-content a {
    color: #000000;
    text-decoration: underline;
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .rst-content {
    background-color: #ffffff;
    color: #000000;
  }
  
  .rst-content a {
    color: #0000ee;
  }
  
  .rst-content a:visited {
    color: #551a8b;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
