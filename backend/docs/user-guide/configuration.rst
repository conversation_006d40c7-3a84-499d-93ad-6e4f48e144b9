Configuration Guide
===================

This guide covers all configuration options for the Blast-Radius Security Tool, including environment variables, security settings, and deployment configurations.

Overview
--------

The Blast-Radius Security Tool uses a hierarchical configuration system:

1. **Environment Variables** (highest priority)
2. **Configuration Files** (.env files)
3. **Default Values** (lowest priority)

Configuration is managed through Pydantic Settings, providing type validation and automatic environment variable parsing.

Environment Variables
---------------------

Core Application Settings
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Application Identity
   PROJECT_NAME="Blast-Radius Security Tool"
   VERSION="1.0.0"
   DESCRIPTION="Comprehensive security platform for attack path analysis"
   
   # Environment
   ENVIRONMENT="development"  # development, staging, production
   DEBUG=false
   LOG_LEVEL="info"  # debug, info, warning, error, critical
   
   # Server Configuration
   HOST="0.0.0.0"
   PORT=8000
   WORKERS=4  # Number of worker processes (production)
   
   # API Configuration
   API_V1_STR="/api/v1"
   DOCS_URL="/docs"  # Set to null to disable in production
   REDOC_URL="/redoc"  # Set to null to disable in production

Database Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # PostgreSQL Database
   DATABASE_URL="postgresql://username:password@localhost:5432/blast_radius"
   DATABASE_POOL_SIZE=20
   DATABASE_MAX_OVERFLOW=30
   DATABASE_POOL_TIMEOUT=30
   DATABASE_POOL_RECYCLE=3600
   
   # Database Connection Testing
   DATABASE_ECHO=false  # Log all SQL queries (development only)
   DATABASE_ECHO_POOL=false  # Log connection pool events

Redis Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Redis Cache and Sessions
   REDIS_URL="redis://localhost:6379/0"
   REDIS_PASSWORD=""  # Optional password
   REDIS_SSL=false
   REDIS_SSL_CERT_REQS="required"  # none, optional, required
   
   # Redis Connection Pool
   REDIS_MAX_CONNECTIONS=50
   REDIS_RETRY_ON_TIMEOUT=true
   REDIS_SOCKET_TIMEOUT=5
   REDIS_SOCKET_CONNECT_TIMEOUT=5

Security Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # JWT Configuration
   SECRET_KEY="your-super-secret-key-change-in-production"
   ALGORITHM="HS256"
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   REFRESH_TOKEN_EXPIRE_DAYS=7
   
   # Password Security
   PASSWORD_MIN_LENGTH=8
   PASSWORD_REQUIRE_UPPERCASE=true
   PASSWORD_REQUIRE_LOWERCASE=true
   PASSWORD_REQUIRE_NUMBERS=true
   PASSWORD_REQUIRE_SYMBOLS=true
   PASSWORD_HISTORY_COUNT=5
   PASSWORD_EXPIRY_DAYS=90  # 0 = never expires
   
   # Account Security
   MAX_LOGIN_ATTEMPTS=5
   ACCOUNT_LOCKOUT_DURATION_MINUTES=30
   ACCOUNT_LOCKOUT_PROGRESSIVE=true  # Increase lockout time for repeated failures
   
   # Session Management
   SESSION_TIMEOUT_MINUTES=480  # 8 hours
   SESSION_ABSOLUTE_TIMEOUT_HOURS=24
   MAX_CONCURRENT_SESSIONS=5
   SESSION_COOKIE_SECURE=true  # HTTPS only
   SESSION_COOKIE_HTTPONLY=true
   SESSION_COOKIE_SAMESITE="strict"  # strict, lax, none

Multi-Factor Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # MFA Configuration
   MFA_ISSUER_NAME="Blast-Radius Security Tool"
   MFA_TOKEN_VALIDITY_SECONDS=300  # 5 minutes
   MFA_BACKUP_CODES_COUNT=10
   MFA_REQUIRE_FOR_ADMIN=true
   MFA_REQUIRE_FOR_ALL=false
   
   # TOTP Configuration
   TOTP_ALGORITHM="SHA1"  # SHA1, SHA256, SHA512
   TOTP_DIGITS=6
   TOTP_PERIOD=30  # seconds
   TOTP_WINDOW=1  # Allow 1 period before/after for clock drift
   
   # SMS MFA (if enabled)
   SMS_PROVIDER="twilio"  # twilio, aws_sns
   SMS_FROM_NUMBER="+**********"
   TWILIO_ACCOUNT_SID=""
   TWILIO_AUTH_TOKEN=""
   
   # Email MFA (if enabled)
   EMAIL_MFA_FROM="<EMAIL>"
   EMAIL_MFA_SUBJECT="Your Blast-Radius Security Code"

CORS and Security Headers
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # CORS Configuration
   CORS_ORIGINS='["http://localhost:3000","https://yourapp.com"]'
   CORS_ALLOW_CREDENTIALS=true
   CORS_ALLOW_METHODS='["GET","POST","PUT","DELETE","PATCH","OPTIONS"]'
   CORS_ALLOW_HEADERS='["*"]'
   
   # Security Headers
   SECURITY_HEADERS_ENABLED=true
   CONTENT_SECURITY_POLICY="default-src 'self'"
   X_FRAME_OPTIONS="DENY"
   X_CONTENT_TYPE_OPTIONS="nosniff"
   STRICT_TRANSPORT_SECURITY="max-age=********; includeSubDomains"

Email Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # SMTP Configuration
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT=587
   SMTP_USERNAME="<EMAIL>"
   SMTP_PASSWORD="your-app-password"
   SMTP_TLS=true
   SMTP_SSL=false
   
   # Email Settings
   EMAIL_FROM="<EMAIL>"
   EMAIL_FROM_NAME="Blast-Radius Security Tool"
   EMAIL_TEMPLATES_DIR="app/email-templates"
   
   # Email Features
   SEND_WELCOME_EMAILS=true
   SEND_PASSWORD_RESET_EMAILS=true
   SEND_SECURITY_ALERTS=true

Logging Configuration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Logging Configuration
   LOG_LEVEL="info"  # debug, info, warning, error, critical
   LOG_FORMAT="json"  # json, text
   LOG_FILE_ENABLED=true
   LOG_FILE_PATH="logs/app.log"
   LOG_FILE_MAX_SIZE="10MB"
   LOG_FILE_BACKUP_COUNT=5
   
   # Structured Logging
   LOG_INCLUDE_TIMESTAMP=true
   LOG_INCLUDE_LEVEL=true
   LOG_INCLUDE_LOGGER_NAME=true
   LOG_INCLUDE_THREAD_ID=false
   LOG_INCLUDE_PROCESS_ID=false
   
   # Audit Logging
   AUDIT_LOG_ENABLED=true
   AUDIT_LOG_FILE="logs/audit.log"
   AUDIT_LOG_RETENTION_DAYS=2555  # 7 years

Monitoring and Metrics
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Prometheus Metrics
   PROMETHEUS_ENABLED=true
   PROMETHEUS_PORT=9090
   PROMETHEUS_PATH="/metrics"
   
   # Health Checks
   HEALTH_CHECK_ENABLED=true
   HEALTH_CHECK_DATABASE=true
   HEALTH_CHECK_REDIS=true
   HEALTH_CHECK_EXTERNAL_SERVICES=true
   
   # Performance Monitoring
   REQUEST_TIMEOUT_SECONDS=30
   SLOW_QUERY_THRESHOLD_SECONDS=1.0
   ENABLE_QUERY_LOGGING=false

Rate Limiting
~~~~~~~~~~~~~

.. code-block:: bash

   # Global Rate Limiting
   RATE_LIMITING_ENABLED=true
   RATE_LIMIT_STORAGE="redis"  # memory, redis
   
   # Authentication Rate Limits
   AUTH_RATE_LIMIT="5/minute"
   PASSWORD_RESET_RATE_LIMIT="3/hour"
   
   # API Rate Limits
   API_RATE_LIMIT="100/minute"
   API_BURST_LIMIT="200/minute"
   
   # Bulk Operation Limits
   BULK_OPERATION_RATE_LIMIT="10/hour"
   BULK_OPERATION_MAX_ITEMS=1000

File Upload Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # File Upload Settings
   MAX_UPLOAD_SIZE="10MB"
   ALLOWED_UPLOAD_EXTENSIONS='[".csv",".json",".xlsx"]'
   UPLOAD_DIRECTORY="uploads"
   UPLOAD_VIRUS_SCANNING=false
   
   # Temporary File Cleanup
   TEMP_FILE_CLEANUP_ENABLED=true
   TEMP_FILE_MAX_AGE_HOURS=24

Configuration Files
-------------------

Environment-Specific Files
~~~~~~~~~~~~~~~~~~~~~~~~~~

Create separate configuration files for different environments:

**.env.development**:

.. code-block:: bash

   DEBUG=true
   LOG_LEVEL="debug"
   DATABASE_ECHO=true
   CORS_ORIGINS='["http://localhost:3000"]'
   DOCS_URL="/docs"
   REDOC_URL="/redoc"

**.env.staging**:

.. code-block:: bash

   DEBUG=false
   LOG_LEVEL="info"
   DATABASE_ECHO=false
   CORS_ORIGINS='["https://staging.yourapp.com"]'
   DOCS_URL="/docs"  # Keep docs in staging
   REDOC_URL="/redoc"

**.env.production**:

.. code-block:: bash

   DEBUG=false
   LOG_LEVEL="warning"
   DATABASE_ECHO=false
   CORS_ORIGINS='["https://yourapp.com"]'
   DOCS_URL=null  # Disable docs in production
   REDOC_URL=null
   SESSION_COOKIE_SECURE=true
   MFA_REQUIRE_FOR_ALL=true

Loading Configuration
~~~~~~~~~~~~~~~~~~~~~

The application automatically loads configuration in this order:

1. **.env.{ENVIRONMENT}** (if ENVIRONMENT is set)
2. **.env.local** (local overrides, git-ignored)
3. **.env** (default configuration)
4. **Environment variables** (highest priority)

Security Configuration
---------------------

Production Security Checklist
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Essential Security Settings**:

.. code-block:: bash

   # Strong secret key (generate with: openssl rand -hex 32)
   SECRET_KEY="your-cryptographically-strong-secret-key"
   
   # Disable debug features
   DEBUG=false
   DOCS_URL=null
   REDOC_URL=null
   
   # Secure cookies
   SESSION_COOKIE_SECURE=true
   SESSION_COOKIE_HTTPONLY=true
   SESSION_COOKIE_SAMESITE="strict"
   
   # Strong password policy
   PASSWORD_MIN_LENGTH=12
   PASSWORD_REQUIRE_UPPERCASE=true
   PASSWORD_REQUIRE_LOWERCASE=true
   PASSWORD_REQUIRE_NUMBERS=true
   PASSWORD_REQUIRE_SYMBOLS=true
   
   # Mandatory MFA
   MFA_REQUIRE_FOR_ALL=true
   
   # Strict CORS
   CORS_ORIGINS='["https://yourapp.com"]'
   CORS_ALLOW_CREDENTIALS=true

SSL/TLS Configuration
~~~~~~~~~~~~~~~~~~~~

**Database SSL**:

.. code-block:: bash

   DATABASE_URL="********************************/db?sslmode=require"

**Redis SSL**:

.. code-block:: bash

   REDIS_URL="rediss://user:pass@host:6380/0"  # Note: rediss:// for SSL
   REDIS_SSL=true
   REDIS_SSL_CERT_REQS="required"

**SMTP SSL/TLS**:

.. code-block:: bash

   SMTP_TLS=true  # Use STARTTLS
   SMTP_SSL=false  # Don't use implicit SSL
   SMTP_PORT=587  # Standard TLS port

Secrets Management
~~~~~~~~~~~~~~~~~

**Using Environment Variables**:

.. code-block:: bash

   # Set in your deployment environment
   export SECRET_KEY="$(openssl rand -hex 32)"
   export DATABASE_PASSWORD="your-secure-db-password"
   export REDIS_PASSWORD="your-secure-redis-password"

**Using Docker Secrets**:

.. code-block:: yaml

   # docker-compose.yml
   version: '3.8'
   services:
     app:
       image: blast-radius:latest
       secrets:
         - db_password
         - secret_key
       environment:
         - DATABASE_PASSWORD_FILE=/run/secrets/db_password
         - SECRET_KEY_FILE=/run/secrets/secret_key
   
   secrets:
     db_password:
       file: ./secrets/db_password.txt
     secret_key:
       file: ./secrets/secret_key.txt

**Using Kubernetes Secrets**:

.. code-block:: yaml

   apiVersion: v1
   kind: Secret
   metadata:
     name: blast-radius-secrets
   type: Opaque
   data:
     secret-key: <base64-encoded-secret>
     db-password: <base64-encoded-password>

Performance Configuration
------------------------

Database Optimization
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Connection Pool Tuning
   DATABASE_POOL_SIZE=20  # Base connections
   DATABASE_MAX_OVERFLOW=30  # Additional connections
   DATABASE_POOL_TIMEOUT=30  # Seconds to wait for connection
   DATABASE_POOL_RECYCLE=3600  # Recycle connections after 1 hour
   
   # Query Optimization
   DATABASE_ECHO=false  # Disable SQL logging in production
   SLOW_QUERY_THRESHOLD_SECONDS=1.0  # Log slow queries

Redis Optimization
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Connection Pool
   REDIS_MAX_CONNECTIONS=50
   REDIS_SOCKET_TIMEOUT=5
   REDIS_SOCKET_CONNECT_TIMEOUT=5
   
   # Memory Management
   REDIS_MAX_MEMORY="256mb"
   REDIS_MAX_MEMORY_POLICY="allkeys-lru"

Application Performance
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Worker Configuration
   WORKERS=4  # Number of worker processes
   WORKER_CONNECTIONS=1000  # Connections per worker
   
   # Request Handling
   REQUEST_TIMEOUT_SECONDS=30
   KEEP_ALIVE_TIMEOUT=2
   
   # Caching
   CACHE_TTL_SECONDS=300  # 5 minutes default cache
   CACHE_MAX_SIZE=1000  # Maximum cached items

Monitoring Configuration
-----------------------

Application Metrics
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Prometheus Integration
   PROMETHEUS_ENABLED=true
   PROMETHEUS_PORT=9090
   PROMETHEUS_MULTIPROC_DIR="/tmp/prometheus_multiproc"
   
   # Custom Metrics
   TRACK_REQUEST_DURATION=true
   TRACK_DATABASE_QUERIES=true
   TRACK_CACHE_HITS=true
   TRACK_AUTHENTICATION_EVENTS=true

Health Checks
~~~~~~~~~~~~~

.. code-block:: bash

   # Health Check Configuration
   HEALTH_CHECK_ENABLED=true
   HEALTH_CHECK_DATABASE=true
   HEALTH_CHECK_REDIS=true
   HEALTH_CHECK_EXTERNAL_SERVICES=true
   
   # Health Check Timeouts
   HEALTH_CHECK_TIMEOUT_SECONDS=5
   HEALTH_CHECK_DATABASE_TIMEOUT=3
   HEALTH_CHECK_REDIS_TIMEOUT=2

Logging for Monitoring
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Structured Logging for Monitoring
   LOG_FORMAT="json"
   LOG_INCLUDE_REQUEST_ID=true
   LOG_INCLUDE_USER_ID=true
   LOG_INCLUDE_IP_ADDRESS=true
   
   # Log Levels by Component
   LOG_LEVEL_DATABASE="warning"
   LOG_LEVEL_AUTHENTICATION="info"
   LOG_LEVEL_API="info"
   LOG_LEVEL_SECURITY="debug"

Deployment Configurations
-------------------------

Docker Configuration
~~~~~~~~~~~~~~~~~~~~

**Dockerfile Environment**:

.. code-block:: dockerfile

   # Set default environment
   ENV ENVIRONMENT=production
   ENV PYTHONPATH=/app
   ENV PYTHONUNBUFFERED=1
   
   # Security settings
   ENV DEBUG=false
   ENV DOCS_URL=""
   ENV REDOC_URL=""

**Docker Compose**:

.. code-block:: yaml

   version: '3.8'
   services:
     app:
       build: .
       environment:
         - ENVIRONMENT=production
         - DATABASE_URL=******************************/blast_radius
         - REDIS_URL=redis://redis:6379/0
       depends_on:
         - db
         - redis
     
     db:
       image: postgres:15
       environment:
         - POSTGRES_DB=blast_radius
         - POSTGRES_USER=blast_radius
         - POSTGRES_PASSWORD=secure_password
     
     redis:
       image: redis:7-alpine
       command: redis-server --requirepass secure_password

Kubernetes Configuration
~~~~~~~~~~~~~~~~~~~~~~~~

**ConfigMap**:

.. code-block:: yaml

   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: blast-radius-config
   data:
     ENVIRONMENT: "production"
     LOG_LEVEL: "info"
     DATABASE_POOL_SIZE: "20"
     REDIS_MAX_CONNECTIONS: "50"

**Deployment**:

.. code-block:: yaml

   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: blast-radius
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: blast-radius
     template:
       metadata:
         labels:
           app: blast-radius
       spec:
         containers:
         - name: app
           image: blast-radius:latest
           envFrom:
           - configMapRef:
               name: blast-radius-config
           - secretRef:
               name: blast-radius-secrets

Configuration Validation
------------------------

Environment Validation
~~~~~~~~~~~~~~~~~~~~~~

The application validates configuration on startup:

.. code-block:: python

   # Example validation errors
   ValidationError: 
   - SECRET_KEY: Field required
   - DATABASE_URL: Invalid database URL format
   - PASSWORD_MIN_LENGTH: Must be at least 8
   - CORS_ORIGINS: Invalid JSON format

Configuration Testing
~~~~~~~~~~~~~~~~~~~~~

Test your configuration:

.. code-block:: bash

   # Validate configuration
   python -c "from app.config import settings; print('Configuration valid')"
   
   # Test database connection
   python -c "from app.db.session import engine; engine.connect()"
   
   # Test Redis connection
   python -c "from app.core.cache import redis_client; redis_client.ping()"

Troubleshooting
---------------

Common Configuration Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Database Connection Failures**:
- Verify DATABASE_URL format
- Check database server accessibility
- Confirm credentials are correct
- Test SSL/TLS settings

**Redis Connection Issues**:
- Verify REDIS_URL format
- Check Redis server status
- Confirm password if required
- Test SSL settings

**Authentication Problems**:
- Verify SECRET_KEY is set and secure
- Check token expiration settings
- Confirm MFA configuration
- Validate CORS settings

**Performance Issues**:
- Review connection pool sizes
- Check timeout settings
- Monitor resource usage
- Validate caching configuration

Configuration Best Practices
----------------------------

Security Best Practices
~~~~~~~~~~~~~~~~~~~~~~~

1. **Never commit secrets** to version control
2. **Use strong, unique SECRET_KEY** for each environment
3. **Enable MFA** for all production users
4. **Disable debug features** in production
5. **Use HTTPS** for all production deployments
6. **Implement proper CORS** policies
7. **Regular security audits** of configuration

Operational Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Environment-specific configurations**
2. **Centralized secrets management**
3. **Configuration validation** in CI/CD
4. **Monitoring and alerting** for configuration changes
5. **Regular backup** of configuration
6. **Documentation** of all settings
7. **Change management** processes

For additional help with configuration, consult the troubleshooting guide or contact your system administrator.
