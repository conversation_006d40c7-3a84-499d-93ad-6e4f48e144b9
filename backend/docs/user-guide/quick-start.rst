Quick Start Guide
==================

This guide will help you get started with the Blast-Radius Security Tool quickly.

Overview
--------

The Blast-Radius Security Tool is a comprehensive security platform that provides:

- **Authentication & Authorization**: Secure user management with role-based access control
- **Multi-Factor Authentication**: TOTP, SMS, and email-based MFA
- **API-First Design**: RESTful APIs with comprehensive documentation
- **Role-Based Dashboards**: Customized interfaces for different user roles

First Steps
-----------

1. **Access the Application**

   After installation, open your web browser and navigate to:
   
   - **Frontend**: http://localhost:3000
   - **API Documentation**: http://localhost:8000/docs

2. **Default Login Credentials**

   The system comes with pre-configured users for testing:

   .. list-table:: Default Users
      :header-rows: 1
      :widths: 20 30 50

      * - Role
        - Username/Password
        - Capabilities
      * - Administrator
        - ``admin`` / ``AdminPassword123!``
        - Full system access and user management
      * - Analyst
        - ``analyst`` / ``AnalystPassword123!``
        - Data analysis and threat intelligence
      * - Viewer
        - ``viewer`` / ``ViewerPassword123!``
        - Read-only access to dashboards

   .. warning::
      Change these default passwords immediately in production environments!

3. **First Login**

   a. Navigate to the login page
   b. Enter the admin credentials
   c. You'll be redirected to the dashboard
   d. The interface will adapt based on your user role

Basic Usage
-----------

Dashboard Navigation
~~~~~~~~~~~~~~~~~~~~

The dashboard provides role-specific content:

**Administrator Dashboard**
   - User management controls
   - System configuration options
   - Security statistics and monitoring
   - All available features

**Analyst Dashboard**
   - Data analysis tools
   - Threat intelligence feeds
   - Report generation capabilities
   - Security metrics

**Viewer Dashboard**
   - Read-only security dashboards
   - Report viewing capabilities
   - Basic system status information

User Management (Admin Only)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

As an administrator, you can manage users:

1. **Navigate to User Management**
   
   Click on "Users" in the navigation menu

2. **Create a New User**

   .. code-block:: none

      1. Click "Add User" button
      2. Fill in user details:
         - Username (required)
         - Email (required)
         - Full Name
         - Password (required)
         - Department
         - Job Title
      3. Assign roles
      4. Click "Create User"

3. **Manage User Roles**

   .. code-block:: none

      1. Find the user in the list
      2. Click "Manage Roles"
      3. Select/deselect roles
      4. Click "Save Roles"

4. **User Actions**

   Available actions for each user:
   - Edit user information
   - Reset password
   - Lock/unlock account
   - View activity log
   - Delete user (with confirmation)

API Usage
---------

Authentication
~~~~~~~~~~~~~~

All API requests require authentication. Here's how to get started:

1. **Login to Get Access Token**

   .. code-block:: bash

      curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "admin",
          "password": "AdminPassword123!",
          "remember_me": false
        }'

   Response:

   .. code-block:: json

      {
        "user": {
          "id": "1",
          "username": "admin",
          "email": "<EMAIL>",
          "roles": ["admin"]
        },
        "tokens": {
          "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
          "token_type": "bearer",
          "expires_in": 1800
        }
      }

2. **Use Access Token for API Calls**

   .. code-block:: bash

      curl -X GET "http://localhost:8000/api/v1/auth/me" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

Common API Operations
~~~~~~~~~~~~~~~~~~~~~

**Get Current User Information**

.. code-block:: bash

   curl -X GET "http://localhost:8000/api/v1/auth/me" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

**List Users** (Admin only)

.. code-block:: bash

   curl -X GET "http://localhost:8000/api/v1/users" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

**Create User** (Admin only)

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/users" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "newuser",
       "email": "<EMAIL>",
       "full_name": "New User",
       "password": "SecurePassword123!",
       "roles": ["analyst"]
     }'

**Health Check**

.. code-block:: bash

   curl -X GET "http://localhost:8000/health"

Multi-Factor Authentication
---------------------------

Setting Up MFA
~~~~~~~~~~~~~~~

1. **Enable MFA for Your Account**

   a. Go to your profile page
   b. Click "Enable MFA"
   c. Choose your preferred method:
      - **TOTP**: Use an authenticator app (Google Authenticator, Authy)
      - **SMS**: Receive codes via text message
      - **Email**: Receive codes via email

2. **TOTP Setup Process**

   .. code-block:: none

      1. Scan QR code with authenticator app
      2. Enter the 6-digit code from your app
      3. Save backup codes securely
      4. MFA is now enabled

3. **Login with MFA**

   .. code-block:: none

      1. Enter username and password
      2. System prompts for MFA code
      3. Enter 6-digit code from your authenticator
      4. Successfully logged in

Role-Based Access Control
-------------------------

Understanding Roles
~~~~~~~~~~~~~~~~~~~~

The system uses seven distinct roles with specific permissions:

.. list-table:: Role Capabilities
   :header-rows: 1
   :widths: 25 75

   * - Role
     - Key Capabilities
   * - **Administrator**
     - • Full system access
       • User management
       • System configuration
       • All security features
   * - **SOC Operator**
     - • Security event monitoring
       • Incident response
       • Alert management
       • Real-time dashboards
   * - **Security Architect**
     - • Architecture design
       • Risk assessment
       • Security policies
       • Compliance oversight
   * - **Red Team Member**
     - • Attack simulation
       • Penetration testing
       • Vulnerability assessment
       • Attack path analysis
   * - **Purple Team Member**
     - • Collaborative testing
       • Defense validation
       • Security improvement
       • Cross-team coordination
   * - **Analyst**
     - • Data analysis
       • Threat intelligence
       • Report generation
       • Security metrics
   * - **Viewer**
     - • Read-only dashboards
       • Report viewing
       • Basic system information
       • Export capabilities

Permission System
~~~~~~~~~~~~~~~~~

Each role has specific permissions that control access to features:

- **User Management**: Create, read, update, delete users
- **Role Management**: Assign and manage user roles
- **System Configuration**: Modify system settings
- **Security Events**: View and manage security events
- **Reports**: Create, view, and export reports
- **API Access**: Different levels of API access

Configuration
-------------

Basic Configuration
~~~~~~~~~~~~~~~~~~~

Key settings you might want to configure:

1. **Session Timeout**

   .. code-block:: bash

      # In .env file
      ACCESS_TOKEN_EXPIRE_MINUTES=30
      REFRESH_TOKEN_EXPIRE_DAYS=7

2. **Password Policy**

   .. code-block:: bash

      # In .env file
      PASSWORD_MIN_LENGTH=8
      PASSWORD_REQUIRE_UPPERCASE=true
      PASSWORD_REQUIRE_LOWERCASE=true
      PASSWORD_REQUIRE_NUMBERS=true
      PASSWORD_REQUIRE_SYMBOLS=true

3. **MFA Settings**

   .. code-block:: bash

      # In .env file
      MFA_ISSUER_NAME="Blast-Radius Security Tool"
      MFA_TOKEN_VALIDITY_SECONDS=300

Security Best Practices
-----------------------

Initial Security Setup
~~~~~~~~~~~~~~~~~~~~~~

1. **Change Default Passwords**

   Immediately change all default user passwords:

   .. code-block:: bash

      # Via API
      curl -X POST "http://localhost:8000/api/v1/auth/change-password" \
        -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "current_password": "AdminPassword123!",
          "new_password": "YourNewSecurePassword123!"
        }'

2. **Enable MFA for All Users**

   Require MFA for all administrative accounts

3. **Review User Permissions**

   Ensure users have only the minimum required permissions

4. **Configure Secure Headers**

   The application includes security headers by default, but review the configuration

5. **Set Up Monitoring**

   Enable audit logging and monitoring for security events

Next Steps
----------

Now that you're familiar with the basics:

1. **Explore the Interface**: Navigate through different sections based on your role
2. **Read the User Guide**: Detailed information about each feature
3. **API Documentation**: Comprehensive API reference at http://localhost:8000/docs
4. **Security Configuration**: Review :doc:`../security/best-practices`
5. **User Management**: Learn advanced user management features
6. **Monitoring**: Set up monitoring and alerting

Getting Help
------------

If you need assistance:

- **Documentation**: Comprehensive guides in this documentation
- **API Reference**: Interactive API documentation
- **GitHub Issues**: Report bugs or request features
- **Community**: Join our community discussions

Troubleshooting
---------------

Common issues and solutions:

**Can't Login**
   - Verify credentials are correct
   - Check if account is locked
   - Ensure MFA codes are current

**Permission Denied**
   - Verify your user role has required permissions
   - Contact administrator for role assignment

**API Errors**
   - Check access token is valid and not expired
   - Verify request format matches API documentation
   - Review error messages for specific issues

For more detailed troubleshooting, see :doc:`../admin/troubleshooting`.
