Authentication Guide
====================

This guide covers all aspects of authentication in the Blast-Radius Security Tool, including setup, configuration, and best practices.

Overview
--------

The Blast-Radius Security Tool uses a multi-layered authentication system designed for enterprise security environments:

- **JWT-based authentication** with access and refresh tokens
- **Multi-factor authentication (MFA)** support
- **Role-based access control (RBAC)**
- **Session management** with configurable timeouts
- **API key authentication** for programmatic access

Authentication Flow
-------------------

Standard Login Flow
~~~~~~~~~~~~~~~~~~~

1. **User submits credentials** (username/email + password)
2. **System validates credentials** against the database
3. **MFA challenge** (if enabled for the user)
4. **JWT tokens generated** (access + refresh tokens)
5. **Session created** and tracked in the database
6. **User redirected** to appropriate dashboard

.. mermaid::

   sequenceDiagram
       participant U as User
       participant F as Frontend
       participant A as API
       participant D as Database
       
       U->>F: Enter credentials
       F->>A: POST /api/v1/auth/login
       A->>D: Validate user
       D-->>A: User data
       A->>A: Check MFA requirement
       alt MFA Required
           A-->>F: MFA challenge
           F-->>U: Show MFA prompt
           U->>F: Enter MFA code
           F->>A: Submit MFA code
       end
       A->>A: Generate JWT tokens
       A->>D: Create session
       A-->>F: Login response + tokens
       F-->>U: Redirect to dashboard

Token Types
-----------

Access Tokens
~~~~~~~~~~~~~

**Purpose**: Short-lived tokens for API authentication

**Characteristics**:
- Default expiration: 30 minutes
- Contains user ID, roles, and permissions
- Used for all authenticated API requests
- Cannot be revoked individually

**Usage**:

.. code-block:: bash

   curl -H "Authorization: Bearer <access_token>" \
        http://localhost:8000/api/v1/auth/me

Refresh Tokens
~~~~~~~~~~~~~~

**Purpose**: Long-lived tokens for obtaining new access tokens

**Characteristics**:
- Default expiration: 7 days (configurable)
- Stored securely in the database
- Can be revoked individually
- Used only for token refresh

**Usage**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/refresh \
        -H "Content-Type: application/json" \
        -d '{"refresh_token": "<refresh_token>"}'

Multi-Factor Authentication (MFA)
---------------------------------

Supported Methods
~~~~~~~~~~~~~~~~~

**TOTP (Time-based One-Time Password)**
   - Compatible with Google Authenticator, Authy, 1Password
   - 6-digit codes that change every 30 seconds
   - Most secure option for most users

**SMS**
   - Text message with 6-digit code
   - Requires phone number verification
   - Good for users without smartphone apps

**Email**
   - Email with 6-digit code
   - Backup option when other methods unavailable
   - Less secure but widely accessible

Setting Up TOTP MFA
~~~~~~~~~~~~~~~~~~~~

1. **Navigate to Profile Settings**

   Go to your user profile and click "Security Settings"

2. **Enable MFA**

   .. code-block:: bash

      curl -X POST http://localhost:8000/api/v1/auth/mfa/setup \
           -H "Authorization: Bearer <access_token>" \
           -H "Content-Type: application/json" \
           -d '{"method": "totp"}'

3. **Scan QR Code**

   Use your authenticator app to scan the provided QR code

4. **Verify Setup**

   .. code-block:: bash

      curl -X POST http://localhost:8000/api/v1/auth/mfa/verify-setup \
           -H "Authorization: Bearer <access_token>" \
           -H "Content-Type: application/json" \
           -d '{"method": "totp", "code": "123456"}'

5. **Save Backup Codes**

   Store the provided backup codes in a secure location

Backup Codes
~~~~~~~~~~~~

Backup codes are single-use codes that can be used when your primary MFA method is unavailable:

- **10 codes generated** during MFA setup
- **Single use only** - each code becomes invalid after use
- **Store securely** - treat like passwords
- **Generate new codes** when running low

Using Backup Codes
~~~~~~~~~~~~~~~~~~~

If you lose access to your MFA device:

1. Use a backup code instead of the MFA code during login
2. The backup code will be consumed and cannot be reused
3. Generate new backup codes when you regain access

Session Management
------------------

Session Lifecycle
~~~~~~~~~~~~~~~~~

**Session Creation**
   - Created upon successful login
   - Stores IP address, user agent, and device info
   - Tracks last activity timestamp

**Session Validation**
   - Checked on every authenticated request
   - Automatically extends session lifetime
   - Validates against user status (active, not locked)

**Session Termination**
   - Automatic expiration after inactivity
   - Manual logout (single session or all sessions)
   - Administrative termination

Session Security
~~~~~~~~~~~~~~~~

**IP Address Tracking**
   - Sessions are tied to originating IP address
   - Suspicious IP changes trigger security alerts
   - Configurable IP validation strictness

**Device Fingerprinting**
   - User agent and device characteristics tracked
   - Helps detect session hijacking attempts
   - Provides audit trail for security investigations

**Concurrent Session Limits**
   - Configurable maximum concurrent sessions per user
   - Oldest sessions automatically terminated when limit exceeded
   - Prevents credential sharing

Viewing Active Sessions
~~~~~~~~~~~~~~~~~~~~~~~

Users can view and manage their active sessions:

.. code-block:: bash

   curl -H "Authorization: Bearer <access_token>" \
        http://localhost:8000/api/v1/auth/sessions

Response includes:
- Session ID and creation time
- IP address and user agent
- Last activity timestamp
- Current session indicator

Terminating Sessions
~~~~~~~~~~~~~~~~~~~~

**Single Session Logout**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/logout \
        -H "Authorization: Bearer <access_token>" \
        -H "Content-Type: application/json" \
        -d '{"all_sessions": false}'

**All Sessions Logout**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/logout \
        -H "Authorization: Bearer <access_token>" \
        -H "Content-Type: application/json" \
        -d '{"all_sessions": true}'

API Key Authentication
----------------------

API keys provide programmatic access to the Blast-Radius API without requiring interactive login.

Creating API Keys
~~~~~~~~~~~~~~~~~

API keys can be created by administrators or users with appropriate permissions:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/{user_id}/api-keys \
        -H "Authorization: Bearer <access_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Integration Key",
          "description": "For automated security scanning",
          "scopes": ["read_assets", "create_incident"],
          "expires_at": "2024-12-31T23:59:59Z"
        }'

API Key Properties
~~~~~~~~~~~~~~~~~~

**Name and Description**
   - Human-readable identification
   - Helps track key usage and purpose

**Scopes**
   - Limit API key permissions
   - More restrictive than user's full permissions
   - Principle of least privilege

**IP Restrictions**
   - Optional IP address allowlist
   - Restricts key usage to specific networks
   - Enhanced security for sensitive operations

**Rate Limiting**
   - Per-key rate limits
   - Prevents abuse and ensures fair usage
   - Configurable based on key purpose

**Expiration**
   - Optional expiration date
   - Automatic key deactivation
   - Reduces risk of forgotten keys

Using API Keys
~~~~~~~~~~~~~~

API keys are used in the Authorization header with the "ApiKey" scheme:

.. code-block:: bash

   curl -H "Authorization: ApiKey <api_key>" \
        http://localhost:8000/api/v1/auth/me

Managing API Keys
~~~~~~~~~~~~~~~~~

**List User's API Keys**:

.. code-block:: bash

   curl -H "Authorization: Bearer <access_token>" \
        http://localhost:8000/api/v1/users/{user_id}/api-keys

**Revoke API Key**:

.. code-block:: bash

   curl -X DELETE http://localhost:8000/api/v1/users/api-keys/{key_id} \
        -H "Authorization: Bearer <access_token>"

Password Management
-------------------

Password Requirements
~~~~~~~~~~~~~~~~~~~~~

The system enforces strong password policies:

- **Minimum length**: 8 characters
- **Complexity requirements**:
  - At least one uppercase letter
  - At least one lowercase letter
  - At least one number
  - At least one special character
- **Password history**: Cannot reuse last 5 passwords
- **Expiration**: Configurable password age limits

Changing Passwords
~~~~~~~~~~~~~~~~~~

Users can change their passwords through the API:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/change-password \
        -H "Authorization: Bearer <access_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "current_password": "CurrentPassword123!",
          "new_password": "NewSecurePassword123!"
        }'

Password Reset
~~~~~~~~~~~~~~

**Request Password Reset**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/reset-password \
        -H "Content-Type: application/json" \
        -d '{"email": "<EMAIL>"}'

**Confirm Password Reset**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/reset-password/confirm \
        -H "Content-Type: application/json" \
        -d '{
          "token": "reset_token_from_email",
          "new_password": "NewSecurePassword123!"
        }'

Security Best Practices
-----------------------

For Users
~~~~~~~~~

1. **Use Strong Passwords**
   - Follow password complexity requirements
   - Use unique passwords for each system
   - Consider using a password manager

2. **Enable MFA**
   - Always enable MFA for your account
   - Use TOTP when possible for best security
   - Keep backup codes in a secure location

3. **Monitor Sessions**
   - Regularly review active sessions
   - Terminate suspicious or unused sessions
   - Report unusual activity immediately

4. **Secure API Keys**
   - Use API keys only when necessary
   - Apply principle of least privilege with scopes
   - Rotate keys regularly
   - Never share API keys

For Administrators
~~~~~~~~~~~~~~~~~~

1. **Enforce Security Policies**
   - Require MFA for all users
   - Set appropriate session timeouts
   - Configure strong password policies
   - Monitor authentication logs

2. **Regular Security Reviews**
   - Audit user permissions regularly
   - Review API key usage
   - Monitor for suspicious login patterns
   - Update security configurations

3. **Incident Response**
   - Have procedures for compromised accounts
   - Know how to terminate all user sessions
   - Monitor authentication failure patterns
   - Implement account lockout policies

Configuration
-------------

Authentication settings can be configured through environment variables:

.. code-block:: bash

   # Token Configuration
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   REFRESH_TOKEN_EXPIRE_DAYS=7
   
   # Session Configuration
   SESSION_TIMEOUT_MINUTES=480
   MAX_CONCURRENT_SESSIONS=5
   
   # Password Policy
   PASSWORD_MIN_LENGTH=8
   PASSWORD_REQUIRE_UPPERCASE=true
   PASSWORD_REQUIRE_LOWERCASE=true
   PASSWORD_REQUIRE_NUMBERS=true
   PASSWORD_REQUIRE_SYMBOLS=true
   PASSWORD_HISTORY_COUNT=5
   
   # Account Security
   MAX_LOGIN_ATTEMPTS=5
   ACCOUNT_LOCKOUT_DURATION_MINUTES=30
   
   # MFA Configuration
   MFA_ISSUER_NAME="Blast-Radius Security Tool"
   MFA_TOKEN_VALIDITY_SECONDS=300

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**"Invalid credentials" error**
   - Verify username and password are correct
   - Check if account is locked or disabled
   - Ensure caps lock is not enabled

**"MFA code invalid" error**
   - Verify time synchronization on device
   - Try the next code if using TOTP
   - Use a backup code if available

**"Session expired" error**
   - Login again to get new tokens
   - Check if session timeout is configured appropriately
   - Verify system time is correct

**API key authentication fails**
   - Verify API key format and validity
   - Check if key has required scopes
   - Ensure IP address is allowed (if restricted)

Getting Help
~~~~~~~~~~~~

If you encounter authentication issues:

1. Check the application logs for detailed error messages
2. Verify your configuration matches the requirements
3. Consult the troubleshooting section in the admin guide
4. Contact your system administrator for assistance

For security-related issues, contact your security team immediately.
