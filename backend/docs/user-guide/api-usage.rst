API Usage Guide
===============

This comprehensive guide covers how to use the Blast-Radius Security Tool API effectively, including authentication, common patterns, and best practices.

Overview
--------

The Blast-Radius API is a RESTful API built with FastAPI that provides:

- **OpenAPI 3.0 specification** with interactive documentation
- **JSON-based** request and response formats
- **JWT authentication** with role-based access control
- **Comprehensive error handling** with detailed error messages
- **Rate limiting** and security controls
- **Versioned endpoints** for backward compatibility

Base URL and Versioning
-----------------------

**Base URL**: ``http://localhost:8000`` (development)

**API Version**: All endpoints are prefixed with ``/api/v1/``

**Full Endpoint Format**: ``http://localhost:8000/api/v1/{endpoint}``

Interactive Documentation
-------------------------

The API provides interactive documentation through multiple interfaces:

**Swagger UI**
   - URL: http://localhost:8000/docs
   - Interactive API explorer
   - Try endpoints directly from the browser
   - View request/response schemas

**ReDoc**
   - URL: http://localhost:8000/redoc
   - Clean, readable documentation
   - Detailed schema information
   - Code examples

**OpenAPI Specification**
   - URL: http://localhost:8000/openapi.json
   - Machine-readable API specification
   - Use for code generation and tooling

Authentication
--------------

JWT Token Authentication
~~~~~~~~~~~~~~~~~~~~~~~~

Most API endpoints require authentication using JWT tokens:

**1. Obtain Access Token**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/auth/login \
        -H "Content-Type: application/json" \
        -d '{
          "username": "your_username",
          "password": "your_password"
        }'

**Response**:

.. code-block:: json

   {
     "user": {
       "id": "123e4567-e89b-12d3-a456-426614174000",
       "username": "your_username",
       "roles": ["analyst"]
     },
     "tokens": {
       "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
       "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
       "token_type": "bearer",
       "expires_in": 1800
     }
   }

**2. Use Access Token**:

.. code-block:: bash

   curl -H "Authorization: Bearer <access_token>" \
        http://localhost:8000/api/v1/auth/me

API Key Authentication
~~~~~~~~~~~~~~~~~~~~~~

For programmatic access, use API keys:

.. code-block:: bash

   curl -H "Authorization: ApiKey <api_key>" \
        http://localhost:8000/api/v1/auth/me

Common Request Patterns
-----------------------

GET Requests
~~~~~~~~~~~~

**Simple GET**:

.. code-block:: bash

   curl -H "Authorization: Bearer <token>" \
        http://localhost:8000/api/v1/users/123

**GET with Query Parameters**:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        -d "page=1" \
        -d "size=20" \
        -d "search=john" \
        -d "active_only=true"

**GET with Complex Filtering**:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        --data-urlencode "created_after=2024-01-01T00:00:00Z" \
        --data-urlencode "roles=analyst,soc_operator"

POST Requests
~~~~~~~~~~~~~

**Create Resource**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "newuser",
          "email": "<EMAIL>",
          "full_name": "New User",
          "password": "SecurePassword123!",
          "roles": ["analyst"]
        }'

**File Upload**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/import \
        -H "Authorization: Bearer <token>" \
        -F "file=@users.csv" \
        -F "format=csv"

PUT/PATCH Requests
~~~~~~~~~~~~~~~~~~

**Full Update (PUT)**:

.. code-block:: bash

   curl -X PUT http://localhost:8000/api/v1/users/123 \
        -H "Authorization: Bearer <token>" \
        -H "Content-Type: application/json" \
        -d '{
          "full_name": "Updated Name",
          "email": "<EMAIL>",
          "department": "New Department"
        }'

**Partial Update (PATCH)**:

.. code-block:: bash

   curl -X PATCH http://localhost:8000/api/v1/users/123 \
        -H "Authorization: Bearer <token>" \
        -H "Content-Type: application/json" \
        -d '{"department": "New Department"}'

DELETE Requests
~~~~~~~~~~~~~~~

**Delete Resource**:

.. code-block:: bash

   curl -X DELETE http://localhost:8000/api/v1/users/123 \
        -H "Authorization: Bearer <token>"

**Delete with Confirmation**:

.. code-block:: bash

   curl -X DELETE http://localhost:8000/api/v1/users/123/permanent \
        -H "Authorization: Bearer <token>" \
        -H "Content-Type: application/json" \
        -d '{"confirmation": "PERMANENTLY_DELETE"}'

Response Formats
----------------

Success Responses
~~~~~~~~~~~~~~~~~

**Single Resource**:

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-426614174000",
     "username": "john.doe",
     "email": "<EMAIL>",
     "full_name": "John Doe",
     "created_at": "2024-01-01T00:00:00Z"
   }

**List Response with Pagination**:

.. code-block:: json

   {
     "items": [
       {
         "id": "123e4567-e89b-12d3-a456-426614174000",
         "username": "john.doe",
         "email": "<EMAIL>"
       }
     ],
     "total": 150,
     "page": 1,
     "size": 20,
     "pages": 8
   }

**Operation Success**:

.. code-block:: json

   {
     "message": "User created successfully",
     "id": "123e4567-e89b-12d3-a456-426614174000"
   }

Error Responses
~~~~~~~~~~~~~~~

**Validation Error (400)**:

.. code-block:: json

   {
     "detail": "Validation error",
     "errors": [
       {
         "field": "email",
         "message": "Invalid email format"
       },
       {
         "field": "password",
         "message": "Password must be at least 8 characters"
       }
     ]
   }

**Authentication Error (401)**:

.. code-block:: json

   {
     "detail": "Invalid credentials"
   }

**Authorization Error (403)**:

.. code-block:: json

   {
     "detail": "Insufficient permissions"
   }

**Not Found Error (404)**:

.. code-block:: json

   {
     "detail": "User not found"
   }

**Rate Limit Error (429)**:

.. code-block:: json

   {
     "detail": "Rate limit exceeded",
     "retry_after": 60
   }

Pagination
----------

List endpoints support pagination with consistent parameters:

**Parameters**:
- ``page``: Page number (1-based, default: 1)
- ``size``: Items per page (default: 20, max: 100)

**Example**:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        -d "page=2" \
        -d "size=50"

**Response**:

.. code-block:: json

   {
     "items": [...],
     "total": 150,
     "page": 2,
     "size": 50,
     "pages": 3,
     "has_next": true,
     "has_prev": true
   }

Filtering and Searching
-----------------------

Search Parameters
~~~~~~~~~~~~~~~~~

Most list endpoints support search functionality:

.. code-block:: bash

   # Search users by name or email
   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        -d "search=john"

Filter Parameters
~~~~~~~~~~~~~~~~~

Use specific filters for precise results:

.. code-block:: bash

   # Filter users by department and role
   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        -d "department=Security" \
        -d "roles=analyst,soc_operator" \
        -d "active_only=true"

Date Range Filtering
~~~~~~~~~~~~~~~~~~~~

Filter by date ranges using ISO 8601 format:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        --data-urlencode "created_after=2024-01-01T00:00:00Z" \
        --data-urlencode "created_before=2024-12-31T23:59:59Z"

Sorting
-------

Sort results using ``sort_by`` and ``sort_order`` parameters:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <token>" \
        -d "sort_by=created_at" \
        -d "sort_order=desc"

**Available Sort Orders**:
- ``asc``: Ascending (default)
- ``desc``: Descending

Rate Limiting
-------------

The API implements rate limiting to ensure fair usage:

**Default Limits**:
- **Authentication endpoints**: 5 requests per minute
- **General API endpoints**: 100 requests per minute
- **Bulk operations**: 10 requests per hour

**Rate Limit Headers**:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1640995200

**Handling Rate Limits**:

.. code-block:: python

   import time
   import requests

   def api_request_with_retry(url, headers, max_retries=3):
       for attempt in range(max_retries):
           response = requests.get(url, headers=headers)
           
           if response.status_code == 429:
               retry_after = int(response.headers.get('Retry-After', 60))
               time.sleep(retry_after)
               continue
               
           return response
       
       raise Exception("Max retries exceeded")

Error Handling
--------------

HTTP Status Codes
~~~~~~~~~~~~~~~~~~

The API uses standard HTTP status codes:

- **200 OK**: Successful GET, PUT, PATCH
- **201 Created**: Successful POST
- **204 No Content**: Successful DELETE
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict (e.g., duplicate)
- **422 Unprocessable Entity**: Validation error
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

Error Response Structure
~~~~~~~~~~~~~~~~~~~~~~~~

All error responses follow a consistent structure:

.. code-block:: json

   {
     "detail": "Human-readable error message",
     "error_code": "SPECIFIC_ERROR_CODE",
     "errors": [
       {
         "field": "field_name",
         "message": "Field-specific error message"
       }
     ],
     "timestamp": "2024-01-01T00:00:00Z",
     "path": "/api/v1/users"
   }

Client Error Handling
~~~~~~~~~~~~~~~~~~~~~

**Python Example**:

.. code-block:: python

   import requests

   def handle_api_response(response):
       if response.status_code == 200:
           return response.json()
       elif response.status_code == 401:
           raise AuthenticationError("Invalid credentials")
       elif response.status_code == 403:
           raise AuthorizationError("Insufficient permissions")
       elif response.status_code == 404:
           raise NotFoundError("Resource not found")
       elif response.status_code == 422:
           errors = response.json().get('errors', [])
           raise ValidationError(errors)
       else:
           raise APIError(f"API error: {response.status_code}")

**JavaScript Example**:

.. code-block:: javascript

   async function apiRequest(url, options = {}) {
     try {
       const response = await fetch(url, {
         headers: {
           'Authorization': `Bearer ${accessToken}`,
           'Content-Type': 'application/json',
           ...options.headers
         },
         ...options
       });

       if (!response.ok) {
         const error = await response.json();
         throw new APIError(error.detail, response.status, error);
       }

       return await response.json();
     } catch (error) {
       console.error('API request failed:', error);
       throw error;
     }
   }

SDK and Client Libraries
------------------------

Python SDK
~~~~~~~~~~

.. code-block:: python

   from blast_radius_client import BlastRadiusClient

   # Initialize client
   client = BlastRadiusClient(
       base_url="http://localhost:8000",
       username="your_username",
       password="your_password"
   )

   # Use the client
   users = client.users.list(page=1, size=20)
   user = client.users.get("user_id")
   new_user = client.users.create({
       "username": "newuser",
       "email": "<EMAIL>",
       "roles": ["analyst"]
   })

JavaScript SDK
~~~~~~~~~~~~~~

.. code-block:: javascript

   import { BlastRadiusClient } from '@blast-radius/client';

   // Initialize client
   const client = new BlastRadiusClient({
     baseURL: 'http://localhost:8000',
     username: 'your_username',
     password: 'your_password'
   });

   // Use the client
   const users = await client.users.list({ page: 1, size: 20 });
   const user = await client.users.get('user_id');
   const newUser = await client.users.create({
     username: 'newuser',
     email: '<EMAIL>',
     roles: ['analyst']
   });

Best Practices
--------------

Authentication
~~~~~~~~~~~~~~

1. **Token Management**
   - Store tokens securely (not in localStorage for web apps)
   - Implement automatic token refresh
   - Handle token expiration gracefully

2. **API Keys**
   - Use API keys for server-to-server communication
   - Rotate API keys regularly
   - Limit API key scopes to minimum required permissions

Request Optimization
~~~~~~~~~~~~~~~~~~~~

1. **Pagination**
   - Use appropriate page sizes (20-50 items)
   - Implement cursor-based pagination for large datasets
   - Cache results when appropriate

2. **Filtering**
   - Use specific filters to reduce response size
   - Combine multiple filters for precise results
   - Use date ranges to limit historical data

3. **Field Selection**
   - Request only needed fields when supported
   - Use summary endpoints for overview data
   - Avoid requesting large nested objects unnecessarily

Error Handling
~~~~~~~~~~~~~~

1. **Retry Logic**
   - Implement exponential backoff for retries
   - Handle rate limiting with appropriate delays
   - Set maximum retry limits

2. **Graceful Degradation**
   - Provide fallback behavior for API failures
   - Cache critical data locally when possible
   - Display meaningful error messages to users

Security
~~~~~~~~

1. **Input Validation**
   - Validate all input data before sending
   - Sanitize user input to prevent injection attacks
   - Use parameterized queries for database operations

2. **Secure Communication**
   - Always use HTTPS in production
   - Validate SSL certificates
   - Implement certificate pinning for mobile apps

3. **Data Protection**
   - Never log sensitive data (passwords, tokens)
   - Implement proper data encryption
   - Follow data retention policies

Performance
~~~~~~~~~~~

1. **Caching**
   - Cache frequently accessed data
   - Use ETags for conditional requests
   - Implement client-side caching strategies

2. **Batch Operations**
   - Use bulk endpoints for multiple operations
   - Combine related requests when possible
   - Implement request queuing for high-volume scenarios

3. **Monitoring**
   - Monitor API response times
   - Track error rates and patterns
   - Set up alerts for API availability

Testing
-------

Unit Testing
~~~~~~~~~~~~

**Python Example**:

.. code-block:: python

   import pytest
   from unittest.mock import Mock, patch
   from your_app import BlastRadiusClient

   @pytest.fixture
   def client():
       return BlastRadiusClient(base_url="http://test.example.com")

   @patch('requests.get')
   def test_get_user(mock_get, client):
       mock_response = Mock()
       mock_response.json.return_value = {"id": "123", "username": "test"}
       mock_response.status_code = 200
       mock_get.return_value = mock_response

       user = client.users.get("123")
       assert user["username"] == "test"

Integration Testing
~~~~~~~~~~~~~~~~~~~

**Python Example**:

.. code-block:: python

   import pytest
   from blast_radius_client import BlastRadiusClient

   @pytest.fixture
   def authenticated_client():
       client = BlastRadiusClient(base_url="http://localhost:8000")
       client.authenticate("test_user", "test_password")
       return client

   def test_user_lifecycle(authenticated_client):
       # Create user
       user_data = {
           "username": "test_user_123",
           "email": "<EMAIL>",
           "roles": ["viewer"]
       }
       user = authenticated_client.users.create(user_data)
       assert user["username"] == "test_user_123"

       # Update user
       updated_user = authenticated_client.users.update(
           user["id"], 
           {"full_name": "Test User"}
       )
       assert updated_user["full_name"] == "Test User"

       # Delete user
       authenticated_client.users.delete(user["id"])

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Authentication Failures**
   - Verify credentials are correct
   - Check token expiration
   - Ensure proper Authorization header format

**Permission Errors**
   - Verify user has required role
   - Check API key scopes
   - Confirm endpoint permissions

**Rate Limiting**
   - Implement proper retry logic
   - Reduce request frequency
   - Use bulk operations when available

**Network Issues**
   - Check API server availability
   - Verify network connectivity
   - Implement timeout handling

Debugging
~~~~~~~~~

**Enable Request Logging**:

.. code-block:: python

   import logging
   import requests

   # Enable debug logging
   logging.basicConfig(level=logging.DEBUG)
   requests_log = logging.getLogger("requests.packages.urllib3")
   requests_log.setLevel(logging.DEBUG)
   requests_log.propagate = True

**Inspect API Responses**:

.. code-block:: bash

   # Use curl with verbose output
   curl -v -H "Authorization: Bearer <token>" \
        http://localhost:8000/api/v1/users

**Check API Health**:

.. code-block:: bash

   curl http://localhost:8000/health

Getting Help
~~~~~~~~~~~~

1. **Interactive Documentation**: http://localhost:8000/docs
2. **API Specification**: http://localhost:8000/openapi.json
3. **Health Check**: http://localhost:8000/health
4. **Error Logs**: Check application logs for detailed error information
5. **Support**: Contact your system administrator or development team
