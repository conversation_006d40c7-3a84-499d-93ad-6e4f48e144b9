Installation Guide
==================

This guide will walk you through installing and setting up the Blast-Radius Security Tool.

Prerequisites
-------------

Before installing Blast-Radius, ensure you have the following prerequisites:

System Requirements
~~~~~~~~~~~~~~~~~~~

- **Operating System**: Linux (Ubuntu 20.04+, CentOS 8+), macOS 10.15+, or Windows 10+
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: Minimum 10GB free space
- **Network**: Internet connection for downloading dependencies

Software Requirements
~~~~~~~~~~~~~~~~~~~~~~

- **Python**: 3.11 or higher
- **Node.js**: 18.0 or higher (for frontend)
- **PostgreSQL**: 14.0 or higher
- **Redis**: 6.0 or higher
- **Git**: Latest version

Installation Methods
--------------------

Method 1: Development Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This method is recommended for development and testing purposes.

1. **Clone the Repository**

   .. code-block:: bash

      git clone https://github.com/forkrul/blast-radius.git
      cd blast-radius

2. **Backend Setup**

   .. code-block:: bash

      cd backend
      
      # Create virtual environment
      python3.11 -m venv venv
      source venv/bin/activate  # On Windows: venv\Scripts\activate
      
      # Install dependencies
      make install-dev
      
      # Copy environment configuration
      cp .env.example .env

3. **Database Setup**

   .. code-block:: bash

      # Create PostgreSQL database
      createdb blast_radius
      
      # Run migrations
      make migrate-upgrade

4. **Frontend Setup**

   .. code-block:: bash

      cd ../frontend
      
      # Install dependencies
      npm install
      
      # Start development server
      npm start

5. **Start the Backend**

   .. code-block:: bash

      cd ../backend
      make run-dev

Method 2: Docker Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This method provides a containerized environment for easy deployment.

1. **Clone the Repository**

   .. code-block:: bash

      git clone https://github.com/forkrul/blast-radius.git
      cd blast-radius

2. **Configure Environment**

   .. code-block:: bash

      cp .env.example .env
      # Edit .env with your configuration

3. **Start with Docker Compose**

   .. code-block:: bash

      docker-compose up -d

Method 3: Production Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This method is for production deployments.

1. **System Preparation**

   .. code-block:: bash

      # Ubuntu/Debian
      sudo apt update
      sudo apt install python3.11 python3.11-venv postgresql redis-server nginx
      
      # CentOS/RHEL
      sudo yum install python311 postgresql-server redis nginx

2. **Database Configuration**

   .. code-block:: bash

      # Initialize PostgreSQL
      sudo postgresql-setup initdb
      sudo systemctl enable postgresql
      sudo systemctl start postgresql
      
      # Create database and user
      sudo -u postgres createuser blast_radius
      sudo -u postgres createdb blast_radius -O blast_radius

3. **Application Setup**

   .. code-block:: bash

      # Create application user
      sudo useradd -m -s /bin/bash blast-radius
      sudo su - blast-radius
      
      # Clone and setup application
      git clone https://github.com/forkrul/blast-radius.git
      cd blast-radius/backend
      
      # Install in production mode
      python3.11 -m venv venv
      source venv/bin/activate
      pip install -r requirements.txt

4. **Service Configuration**

   Create systemd service file:

   .. code-block:: ini

      # /etc/systemd/system/blast-radius.service
      [Unit]
      Description=Blast-Radius Security Tool
      After=network.target postgresql.service redis.service
      
      [Service]
      Type=exec
      User=blast-radius
      Group=blast-radius
      WorkingDirectory=/home/<USER>/blast-radius/backend
      Environment=PATH=/home/<USER>/blast-radius/backend/venv/bin
      ExecStart=/home/<USER>/blast-radius/backend/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
      Restart=always
      
      [Install]
      WantedBy=multi-user.target

Configuration
-------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~~

The application uses environment variables for configuration. Copy the example file and modify as needed:

.. code-block:: bash

   cp .env.example .env

Key configuration options:

.. code-block:: bash

   # Database Configuration
   DATABASE_URL=postgresql://username:password@localhost/blast_radius
   
   # Redis Configuration
   REDIS_URL=redis://localhost:6379/0
   
   # Security Configuration
   SECRET_KEY=your-secret-key-here
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   REFRESH_TOKEN_EXPIRE_DAYS=7
   
   # Application Configuration
   DEBUG=false
   LOG_LEVEL=info
   CORS_ORIGINS=["http://localhost:3000"]

Database Configuration
~~~~~~~~~~~~~~~~~~~~~~

1. **PostgreSQL Setup**

   .. code-block:: sql

      -- Connect to PostgreSQL as superuser
      CREATE USER blast_radius WITH PASSWORD 'your_password';
      CREATE DATABASE blast_radius OWNER blast_radius;
      GRANT ALL PRIVILEGES ON DATABASE blast_radius TO blast_radius;

2. **Run Migrations**

   .. code-block:: bash

      alembic upgrade head

Redis Configuration
~~~~~~~~~~~~~~~~~~~

1. **Basic Redis Setup**

   .. code-block:: bash

      # Ubuntu/Debian
      sudo systemctl enable redis-server
      sudo systemctl start redis-server
      
      # CentOS/RHEL
      sudo systemctl enable redis
      sudo systemctl start redis

2. **Redis Security** (Production)

   Edit ``/etc/redis/redis.conf``:

   .. code-block:: bash

      # Bind to specific interface
      bind 127.0.0.1
      
      # Set password
      requirepass your_redis_password
      
      # Disable dangerous commands
      rename-command FLUSHDB ""
      rename-command FLUSHALL ""

Verification
------------

After installation, verify that everything is working correctly:

1. **Check Backend Health**

   .. code-block:: bash

      curl http://localhost:8000/health

   Expected response:

   .. code-block:: json

      {
        "status": "healthy",
        "version": "0.0.1",
        "timestamp": "2024-01-01T00:00:00Z"
      }

2. **Check API Documentation**

   Open your browser and navigate to:
   
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

3. **Test Authentication**

   .. code-block:: bash

      curl -X POST http://localhost:8000/api/v1/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "AdminPassword123!"}'

4. **Check Frontend** (if installed)

   Open your browser and navigate to: http://localhost:3000

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Database Connection Error**

.. code-block:: bash

   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check database exists
   sudo -u postgres psql -l | grep blast_radius

**Redis Connection Error**

.. code-block:: bash

   # Check Redis status
   sudo systemctl status redis
   
   # Test Redis connection
   redis-cli ping

**Permission Errors**

.. code-block:: bash

   # Fix file permissions
   sudo chown -R blast-radius:blast-radius /home/<USER>/blast-radius
   chmod +x /home/<USER>/blast-radius/backend/venv/bin/*

**Port Already in Use**

.. code-block:: bash

   # Check what's using port 8000
   sudo netstat -tulpn | grep :8000
   
   # Kill process if needed
   sudo kill -9 <PID>

Getting Help
~~~~~~~~~~~~

If you encounter issues during installation:

1. Check the :doc:`../admin/troubleshooting` guide
2. Review the application logs
3. Search existing issues on GitHub
4. Create a new issue with detailed error information

Next Steps
----------

After successful installation:

1. Read the :doc:`quick-start` guide
2. Configure :doc:`authentication` settings
3. Set up :doc:`user-management`
4. Review :doc:`../security/best-practices`
