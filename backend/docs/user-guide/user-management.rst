User Management Guide
=====================

This guide covers comprehensive user management in the Blast-Radius Security Tool, including user lifecycle, roles, permissions, and administrative tasks.

Overview
--------

The Blast-Radius Security Tool provides enterprise-grade user management with:

- **Role-based access control (RBAC)** with 7 distinct roles
- **Granular permission system** with 25+ permissions
- **Complete user lifecycle management**
- **Bulk operations** for efficient administration
- **Audit logging** for compliance and security

User Roles
----------

The system supports seven distinct user roles, each designed for specific security functions:

Administrator
~~~~~~~~~~~~~

**Purpose**: Full system access and user management

**Key Capabilities**:
- Complete user management (create, modify, delete users)
- System configuration and settings
- Access to all security features and data
- Audit log access and compliance reporting
- API key management for all users

**Typical Users**: IT administrators, security managers, system owners

**Permissions**: All available permissions in the system

SOC Operator
~~~~~~~~~~~~

**Purpose**: Security Operations Center monitoring and incident response

**Key Capabilities**:
- Real-time security event monitoring
- Incident creation and management
- Alert triage and escalation
- Security dashboard access
- Basic threat intelligence viewing

**Typical Users**: SOC analysts, security monitors, incident responders

**Key Permissions**:
- ``view_security_events``
- ``create_incident``
- ``manage_incidents``
- ``view_dashboards``
- ``export_reports``

Security Architect
~~~~~~~~~~~~~~~~~~~

**Purpose**: Security architecture design and risk assessment

**Key Capabilities**:
- Security architecture documentation
- Risk assessment and analysis
- Security policy development
- Compliance framework management
- Strategic security planning

**Typical Users**: Security architects, risk managers, compliance officers

**Key Permissions**:
- ``view_architecture``
- ``manage_policies``
- ``conduct_risk_assessment``
- ``view_compliance_reports``
- ``manage_security_frameworks``

Red Team Member
~~~~~~~~~~~~~~~~

**Purpose**: Offensive security testing and attack simulation

**Key Capabilities**:
- Penetration testing tools access
- Attack path analysis
- Vulnerability assessment
- Security testing coordination
- Exploit development and testing

**Typical Users**: Penetration testers, red team operators, security researchers

**Key Permissions**:
- ``conduct_penetration_tests``
- ``view_attack_paths``
- ``manage_vulnerabilities``
- ``access_testing_tools``
- ``create_security_tests``

Purple Team Member
~~~~~~~~~~~~~~~~~~~

**Purpose**: Collaborative security testing and defense validation

**Key Capabilities**:
- Cross-team collaboration tools
- Defense validation testing
- Security control effectiveness measurement
- Threat hunting coordination
- Security improvement recommendations

**Typical Users**: Purple team leads, security engineers, threat hunters

**Key Permissions**:
- ``coordinate_testing``
- ``validate_defenses``
- ``conduct_threat_hunting``
- ``measure_security_effectiveness``
- ``recommend_improvements``

Analyst
~~~~~~~

**Purpose**: Data analysis and threat intelligence research

**Key Capabilities**:
- Security data analysis
- Threat intelligence research
- Report generation and analysis
- Trend identification and reporting
- Security metrics development

**Typical Users**: Security analysts, threat researchers, data scientists

**Key Permissions**:
- ``analyze_security_data``
- ``research_threats``
- ``generate_reports``
- ``view_threat_intelligence``
- ``create_analytics``

Viewer
~~~~~~

**Purpose**: Read-only access to dashboards and reports

**Key Capabilities**:
- Dashboard viewing
- Report access (read-only)
- Basic system information viewing
- Export capabilities for accessible data

**Typical Users**: Executives, auditors, stakeholders, contractors

**Key Permissions**:
- ``view_dashboards``
- ``view_reports``
- ``export_accessible_data``
- ``view_system_status``

User Lifecycle Management
-------------------------

Creating Users
~~~~~~~~~~~~~~

**Prerequisites**: Administrator or user with ``create_user`` permission

**Basic User Creation**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "john.doe",
          "email": "<EMAIL>",
          "full_name": "John Doe",
          "password": "SecurePassword123!",
          "department": "Security Operations",
          "job_title": "SOC Analyst",
          "roles": ["soc_operator"],
          "phone_number": "+**********",
          "timezone": "America/New_York"
        }'

**Bulk User Creation**:

For creating multiple users, use the bulk creation endpoint:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/bulk \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "users": [
            {
              "username": "analyst1",
              "email": "<EMAIL>",
              "full_name": "Security Analyst 1",
              "roles": ["analyst"]
            },
            {
              "username": "analyst2", 
              "email": "<EMAIL>",
              "full_name": "Security Analyst 2",
              "roles": ["analyst"]
            }
          ],
          "send_welcome_email": true,
          "require_password_change": true
        }'

User Activation
~~~~~~~~~~~~~~~

New users must be activated before they can log in:

**Email Verification**:
- Users receive an email with activation link
- Link expires after 24 hours
- Can be resent if needed

**Manual Activation** (Admin):

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/{user_id}/activate \
        -H "Authorization: Bearer <admin_token>"

Modifying Users
~~~~~~~~~~~~~~~

**Update User Information**:

.. code-block:: bash

   curl -X PUT http://localhost:8000/api/v1/users/{user_id} \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "full_name": "John Smith",
          "department": "Cybersecurity",
          "job_title": "Senior SOC Analyst",
          "phone_number": "+**********"
        }'

**Role Assignment**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/{user_id}/roles \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "roles": ["soc_operator", "analyst"],
          "expires_at": "2024-12-31T23:59:59Z"
        }'

**Account Status Changes**:

.. code-block:: bash

   # Lock user account
   curl -X POST http://localhost:8000/api/v1/users/{user_id}/lock \
        -H "Authorization: Bearer <admin_token>" \
        -d '{"reason": "Security investigation"}'
   
   # Unlock user account
   curl -X POST http://localhost:8000/api/v1/users/{user_id}/unlock \
        -H "Authorization: Bearer <admin_token>"
   
   # Disable user account
   curl -X POST http://localhost:8000/api/v1/users/{user_id}/disable \
        -H "Authorization: Bearer <admin_token>" \
        -d '{"reason": "Employee departure"}'

User Deactivation
~~~~~~~~~~~~~~~~~

**Soft Delete** (Recommended):

.. code-block:: bash

   curl -X DELETE http://localhost:8000/api/v1/users/{user_id} \
        -H "Authorization: Bearer <admin_token>" \
        -d '{"reason": "Employee departure", "retain_data": true}'

Soft deletion:
- Preserves audit trails and historical data
- Prevents login but maintains data integrity
- Can be reversed if needed
- Recommended for compliance requirements

**Hard Delete** (Permanent):

.. code-block:: bash

   curl -X DELETE http://localhost:8000/api/v1/users/{user_id}/permanent \
        -H "Authorization: Bearer <admin_token>" \
        -d '{"confirmation": "PERMANENTLY_DELETE"}'

⚠️ **Warning**: Hard deletion permanently removes all user data and cannot be undone.

Permission Management
--------------------

Understanding Permissions
~~~~~~~~~~~~~~~~~~~~~~~~~

Permissions are granular capabilities that control access to specific features:

**User Management Permissions**:
- ``create_user``: Create new user accounts
- ``read_user``: View user information
- ``update_user``: Modify user accounts
- ``delete_user``: Remove user accounts
- ``manage_user_roles``: Assign/remove user roles

**Security Operations Permissions**:
- ``view_security_events``: Access security event data
- ``create_incident``: Create security incidents
- ``manage_incidents``: Modify incident details
- ``escalate_incidents``: Escalate incidents to higher levels
- ``close_incidents``: Mark incidents as resolved

**System Administration Permissions**:
- ``manage_system_config``: Modify system settings
- ``view_audit_logs``: Access audit trail data
- ``manage_integrations``: Configure external integrations
- ``backup_system``: Perform system backups
- ``restore_system``: Restore from backups

**Reporting and Analytics Permissions**:
- ``generate_reports``: Create custom reports
- ``view_analytics``: Access analytics dashboards
- ``export_data``: Export system data
- ``schedule_reports``: Set up automated reporting

Custom Permission Sets
~~~~~~~~~~~~~~~~~~~~~~

Create custom permission combinations for specific needs:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/permissions/sets \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Incident Response Team",
          "description": "Permissions for incident response specialists",
          "permissions": [
            "view_security_events",
            "create_incident", 
            "manage_incidents",
            "escalate_incidents",
            "generate_reports"
          ]
        }'

Bulk Operations
---------------

User Import
~~~~~~~~~~~

Import users from CSV or JSON files:

**CSV Format**:

.. code-block:: csv

   username,email,full_name,department,job_title,roles
   john.doe,<EMAIL>,John Doe,Security,Analyst,"analyst,viewer"
   jane.smith,<EMAIL>,Jane Smith,IT,Admin,"admin"

**Import Command**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/import \
        -H "Authorization: Bearer <admin_token>" \
        -F "file=@users.csv" \
        -F "format=csv" \
        -F "send_welcome_emails=true"

Role Assignment
~~~~~~~~~~~~~~~

Bulk role assignment for multiple users:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/bulk-roles \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "user_ids": [
            "user1-uuid",
            "user2-uuid", 
            "user3-uuid"
          ],
          "roles": ["analyst"],
          "action": "add"
        }'

User Search and Filtering
-------------------------

Advanced User Search
~~~~~~~~~~~~~~~~~~~~

Search users with multiple criteria:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <admin_token>" \
        -d "search=john" \
        -d "department=Security" \
        -d "roles=analyst" \
        -d "active_only=true" \
        -d "page=1" \
        -d "size=20"

**Available Filters**:
- ``search``: Username, email, or full name
- ``department``: Department name
- ``roles``: User roles (comma-separated)
- ``active_only``: Only active users
- ``created_after``: Users created after date
- ``last_login_after``: Users who logged in after date

Sorting Options
~~~~~~~~~~~~~~~

Sort results by various fields:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users \
        -H "Authorization: Bearer <admin_token>" \
        -d "sort_by=last_login_at" \
        -d "sort_order=desc"

**Sort Fields**:
- ``username``: Alphabetical by username
- ``email``: Alphabetical by email
- ``full_name``: Alphabetical by full name
- ``created_at``: By creation date
- ``last_login_at``: By last login time
- ``department``: By department name

User Activity Monitoring
------------------------

Login History
~~~~~~~~~~~~~

View user login history and patterns:

.. code-block:: bash

   curl -H "Authorization: Bearer <admin_token>" \
        http://localhost:8000/api/v1/users/{user_id}/login-history

**Response includes**:
- Login timestamps
- IP addresses
- User agents (browsers/devices)
- Success/failure status
- MFA usage
- Geographic location (if available)

Session Management
~~~~~~~~~~~~~~~~~~

Monitor and manage user sessions:

.. code-block:: bash

   # View user's active sessions
   curl -H "Authorization: Bearer <admin_token>" \
        http://localhost:8000/api/v1/users/{user_id}/sessions
   
   # Terminate specific session
   curl -X DELETE http://localhost:8000/api/v1/users/sessions/{session_id} \
        -H "Authorization: Bearer <admin_token>"
   
   # Terminate all user sessions
   curl -X DELETE http://localhost:8000/api/v1/users/{user_id}/sessions \
        -H "Authorization: Bearer <admin_token>"

Activity Logs
~~~~~~~~~~~~~

Track user actions and system interactions:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/users/{user_id}/activity \
        -H "Authorization: Bearer <admin_token>" \
        -d "start_date=2024-01-01" \
        -d "end_date=2024-01-31" \
        -d "action_type=login"

Security Features
-----------------

Account Lockout
~~~~~~~~~~~~~~~

Automatic account lockout after failed login attempts:

**Configuration**:
- Maximum failed attempts: 5 (configurable)
- Lockout duration: 30 minutes (configurable)
- Progressive lockout: Longer lockouts for repeated failures

**Manual Lockout**:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/{user_id}/lock \
        -H "Authorization: Bearer <admin_token>" \
        -d '{
          "reason": "Suspicious activity detected",
          "duration_minutes": 60
        }'

Password Policies
~~~~~~~~~~~~~~~~~

Enforce strong password requirements:

- **Complexity**: Uppercase, lowercase, numbers, symbols
- **Length**: Minimum 8 characters
- **History**: Cannot reuse last 5 passwords
- **Expiration**: Optional password age limits
- **Strength**: Real-time password strength validation

Force Password Change
~~~~~~~~~~~~~~~~~~~~~

Require users to change passwords:

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/{user_id}/force-password-change \
        -H "Authorization: Bearer <admin_token>" \
        -d '{"reason": "Security policy update"}'

Compliance and Auditing
-----------------------

User Access Reviews
~~~~~~~~~~~~~~~~~~~

Generate user access reports for compliance:

.. code-block:: bash

   curl -H "Authorization: Bearer <admin_token>" \
        http://localhost:8000/api/v1/reports/user-access-review

**Report includes**:
- User roles and permissions
- Last login dates
- Account status
- Role assignment history
- Permission changes

Audit Trail
~~~~~~~~~~~

Complete audit trail of user management actions:

.. code-block:: bash

   curl -G http://localhost:8000/api/v1/audit/user-management \
        -H "Authorization: Bearer <admin_token>" \
        -d "start_date=2024-01-01" \
        -d "action_type=role_assignment"

**Tracked Events**:
- User creation/modification/deletion
- Role assignments/removals
- Permission changes
- Account status changes
- Password changes
- Login/logout events

Data Retention
~~~~~~~~~~~~~~

Configure data retention policies:

.. code-block:: bash

   curl -X PUT http://localhost:8000/api/v1/config/data-retention \
        -H "Authorization: Bearer <admin_token>" \
        -H "Content-Type: application/json" \
        -d '{
          "user_data_retention_days": 2555,
          "audit_log_retention_days": 2555,
          "session_data_retention_days": 90
        }'

Best Practices
--------------

User Management
~~~~~~~~~~~~~~~

1. **Principle of Least Privilege**
   - Assign minimum required roles and permissions
   - Regularly review and adjust user access
   - Use time-limited role assignments when appropriate

2. **Regular Access Reviews**
   - Conduct quarterly user access reviews
   - Remove unused accounts promptly
   - Verify role assignments match job functions

3. **Onboarding/Offboarding**
   - Standardize user creation processes
   - Implement automated offboarding workflows
   - Ensure proper data retention compliance

Security
~~~~~~~~

1. **Account Security**
   - Enforce strong password policies
   - Require MFA for all users
   - Monitor for suspicious login patterns

2. **Role Management**
   - Regularly audit role definitions
   - Minimize users with administrative privileges
   - Use role-based rather than user-specific permissions

3. **Monitoring**
   - Set up alerts for unusual user activity
   - Monitor failed login attempts
   - Track privilege escalations

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**User cannot login**
   - Check account status (active, not locked)
   - Verify password hasn't expired
   - Confirm MFA is properly configured
   - Check for IP restrictions

**Permission denied errors**
   - Verify user has required role
   - Check role includes necessary permissions
   - Confirm role assignment hasn't expired

**Bulk operations failing**
   - Validate input data format
   - Check for duplicate usernames/emails
   - Verify sufficient permissions
   - Review error logs for specific failures

Getting Help
~~~~~~~~~~~~

For user management issues:

1. Check the audit logs for recent changes
2. Verify role and permission configurations
3. Review system configuration settings
4. Contact your system administrator
5. Consult the API documentation for detailed error codes
