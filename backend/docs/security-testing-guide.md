# Security Testing Guide for Blast-Radius Security Tool

## Overview

This guide covers the comprehensive security testing strategy implemented for the Blast-Radius Security Tool. Our multi-layered approach ensures robust security validation across all components.

## 🔒 Security Testing Types

### 1. Static Application Security Testing (SAST)

**Tools Used:**
- **Bandit** - Python security linter
- **Semgrep** - Multi-language static analysis
- **CodeQL** - GitHub's semantic code analysis

**What it Tests:**
- SQL injection vulnerabilities
- Cross-site scripting (XSS) risks
- Insecure cryptographic practices
- Hardcoded secrets
- Authentication bypasses

**Running SAST:**
```bash
# Run all static security analysis
make security-scan

# Run individual tools
bandit -r app/
semgrep --config=auto app/
```

### 2. Dynamic Application Security Testing (DAST)

**Tools Used:**
- **Custom API Security Tests** - Comprehensive API testing
- **OWASP ZAP** - Web application scanner (planned)
- **Nuclei** - Fast vulnerability scanner (planned)

**What it Tests:**
- Runtime authentication bypasses
- Authorization flaws
- Input validation failures
- Session management issues
- Business logic vulnerabilities

**Running DAST:**
```bash
# Run API security tests
make security-api-test

# Run all security tests
make security-test
```

### 3. Dependency Security Testing

**Tools Used:**
- **Safety** - Python dependency vulnerability scanner
- **Dependabot** - Automated dependency updates
- **Trivy** - Container vulnerability scanner

**What it Tests:**
- Known vulnerabilities in dependencies
- Outdated packages with security issues
- Container image vulnerabilities
- License compliance issues

**Running Dependency Tests:**
```bash
# Check for vulnerable dependencies
make dependency-check

# Scan Docker images
make docker-security-scan
```

### 4. Infrastructure Security Testing

**Tools Used:**
- **Custom Infrastructure Tests** - Docker, database, Redis security
- **Docker Bench** - Docker security best practices (planned)
- **CIS Benchmarks** - Security configuration standards (planned)

**What it Tests:**
- Container security configurations
- Database security settings
- Network exposure and SSL/TLS
- Secrets management
- Access controls

**Running Infrastructure Tests:**
```bash
# Run infrastructure security tests
make security-infra-test
```

### 5. Secrets Detection

**Tools Used:**
- **Custom Secrets Scanner** - Pattern-based secret detection
- **GitLeaks** - Git repository secret scanner (planned)
- **TruffleHog** - High-entropy string detection (planned)

**What it Tests:**
- API keys in code
- Database passwords
- JWT secrets
- Private keys
- Authentication tokens

**Running Secrets Detection:**
```bash
# Scan for secrets
make secrets-scan
```

## 🛡️ Security Test Categories

### Authentication & Authorization Tests

```python
# Example: Test authentication bypass attempts
def test_authentication_bypass_attempts(client):
    bypass_attempts = [
        {"Authorization": "Bearer invalid-token"},
        {"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature"},
        {"Authorization": "Bearer "},
    ]
    
    for headers in bypass_attempts:
        response = client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 401
```

### Input Validation Tests

```python
# Example: Test SQL injection prevention
def test_sql_injection_prevention(client):
    sql_payloads = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
    ]
    
    for payload in sql_payloads:
        response = client.post("/api/v1/auth/login", json={
            "username": payload,
            "password": "password123"
        })
        assert response.status_code != 500  # Should not cause SQL error
```

### Infrastructure Security Tests

```python
# Example: Test Docker container security
def test_docker_container_security_config():
    client = docker.from_env()
    containers = client.containers.list()
    
    for container in containers:
        # Check if container is running as root
        exec_result = container.exec_run("whoami")
        if exec_result.exit_code == 0:
            user = exec_result.output.decode().strip()
            assert user != "root", f"Container {container.name} running as root"
```

## 🚀 Running Security Tests

### Local Development

```bash
# Install security testing dependencies
pip install -r requirements-dev.txt

# Run comprehensive security scan
make security-full

# Run specific security test categories
make security-api-test      # API security tests
make security-infra-test    # Infrastructure tests
make dependency-check       # Dependency vulnerabilities
make secrets-scan          # Secret detection
```

### CI/CD Pipeline

Security tests are automatically run in GitHub Actions:

- **On every push/PR** - Full security scan
- **Daily scheduled runs** - Comprehensive security audit
- **Docker image builds** - Container vulnerability scanning
- **Dependency updates** - Automated security patches

### Security Test Reports

Reports are generated in `reports/security/`:

```
reports/security/
├── bandit-results.json      # Static analysis results
├── safety-results.json     # Dependency vulnerabilities
├── semgrep-results.json     # Advanced static analysis
├── secrets-scan.json       # Secret detection results
├── trivy-results.json      # Container vulnerabilities
└── security-summary.json   # Overall security status
```

## 🎯 Security Testing Best Practices

### 1. Test Early and Often
- Run security tests in pre-commit hooks
- Include security tests in CI/CD pipeline
- Perform regular security audits

### 2. Comprehensive Coverage
- Test all API endpoints for security issues
- Validate both positive and negative test cases
- Include edge cases and boundary conditions

### 3. Realistic Test Data
- Use production-like test data
- Test with various user roles and permissions
- Include malicious payloads and attack vectors

### 4. Continuous Monitoring
- Set up alerts for security test failures
- Monitor for new vulnerabilities in dependencies
- Track security metrics and trends

## 🔧 Customizing Security Tests

### Adding New Security Tests

1. **Create test file** in `tests/security/`
2. **Follow naming convention** `test_security_*.py`
3. **Use appropriate fixtures** for setup/teardown
4. **Document test purpose** and expected behavior

### Example Custom Security Test

```python
def test_custom_security_control(client, db):
    """Test custom security control implementation."""
    # Arrange
    malicious_input = "custom_attack_payload"
    
    # Act
    response = client.post("/api/v1/custom-endpoint", json={
        "data": malicious_input
    })
    
    # Assert
    assert response.status_code in [400, 422]  # Should reject malicious input
    assert malicious_input not in response.text  # Should not echo back
```

### Configuring Security Thresholds

Update `pyproject.toml` for Bandit configuration:

```toml
[tool.bandit]
exclude_dirs = ["tests", "venv"]
skips = ["B101"]  # Skip assert_used test
severity = "medium"
confidence = "medium"
```

## 📊 Security Metrics and KPIs

### Key Security Metrics

- **Critical Vulnerabilities**: 0 tolerance
- **High Severity Issues**: < 5 per release
- **Dependency Vulnerabilities**: < 10 per month
- **Security Test Coverage**: > 80%
- **Mean Time to Fix**: < 24 hours for critical issues

### Security Dashboard

Monitor security health through:

- GitHub Security tab for vulnerability alerts
- CI/CD pipeline security test results
- Dependency update notifications
- Security scan reports and trends

## 🚨 Incident Response

### Security Test Failures

1. **Immediate Actions**
   - Stop deployment if critical issues found
   - Notify security team
   - Create incident ticket

2. **Investigation**
   - Analyze security test results
   - Determine impact and severity
   - Identify root cause

3. **Remediation**
   - Fix identified vulnerabilities
   - Update security tests if needed
   - Verify fix with additional testing

4. **Prevention**
   - Update security policies
   - Enhance security tests
   - Conduct security training

## 📚 Additional Resources

- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [CIS Controls](https://www.cisecurity.org/controls/)
- [SANS Security Testing](https://www.sans.org/white-papers/security-testing/)

## 🤝 Contributing to Security Testing

1. **Review security test coverage** for new features
2. **Add security tests** for new endpoints/functionality
3. **Update documentation** when adding new security controls
4. **Participate in security reviews** and threat modeling sessions

---

**Remember**: Security is everyone's responsibility. When in doubt, err on the side of caution and consult the security team.
