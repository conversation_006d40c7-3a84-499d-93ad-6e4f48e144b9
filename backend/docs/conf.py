"""Sphinx configuration for Blast-Radius Security Tool documentation."""

import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Project information
project = "Blast-Radius Security Tool"
copyright = "2024, Blast-Radius Team"
author = "Blast-Radius Team"
version = "0.0.1"
release = "0.0.1"

# General configuration
extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.autosummary",
    "sphinx.ext.viewcode",
    "sphinx.ext.napoleon",
    "sphinx.ext.intersphinx",
    "sphinx.ext.todo",
    "sphinx.ext.coverage",
    "sphinx.ext.ifconfig",
    "sphinx.ext.githubpages",
    "sphinx_autodoc_typehints",
    "sphinx_copybutton",
    "myst_parser",
    "sphinxcontrib.openapi",
]

# Source file suffixes
source_suffix = {
    ".rst": None,
    ".md": "myst_parser",
}

# Master document
master_doc = "index"

# Language
language = "en"

# Exclude patterns
exclude_patterns = ["_build", "Thumbs.db", ".DS_Store"]

# Pygments style
pygments_style = "sphinx"

# HTML theme
html_theme = "sphinx_rtd_theme"
html_theme_options = {
    "canonical_url": "",
    "analytics_id": "",
    "logo_only": False,
    "display_version": True,
    "prev_next_buttons_location": "bottom",
    "style_external_links": False,
    "vcs_pageview_mode": "",
    "style_nav_header_background": "#2980B9",
    # Toc options
    "collapse_navigation": True,
    "sticky_navigation": True,
    "navigation_depth": 4,
    "includehidden": True,
    "titles_only": False,
}

# HTML static files
html_static_path = ["_static"]

# HTML custom CSS
html_css_files = [
    "custom.css",
]

# HTML logo
html_logo = "_static/logo.png"

# HTML favicon
html_favicon = "_static/favicon.ico"

# HTML title
html_title = f"{project} v{version}"

# HTML short title
html_short_title = project

# HTML context
html_context = {
    "display_github": True,
    "github_user": "forkrul",
    "github_repo": "blast-radius",
    "github_version": "master",
    "conf_py_path": "/backend/docs/",
}

# Autodoc configuration
autodoc_default_options = {
    "members": True,
    "member-order": "bysource",
    "special-members": "__init__",
    "undoc-members": True,
    "exclude-members": "__weakref__",
    "show-inheritance": True,
}

# Autodoc type hints
autodoc_typehints = "description"
autodoc_typehints_description_target = "documented"

# Napoleon configuration
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_preprocess_types = False
napoleon_type_aliases = None
napoleon_attr_annotations = True

# Intersphinx mapping
intersphinx_mapping = {
    "python": ("https://docs.python.org/3", None),
    "fastapi": ("https://fastapi.tiangolo.com", None),
    "sqlalchemy": ("https://docs.sqlalchemy.org/en/20/", None),
    "pydantic": ("https://docs.pydantic.dev/latest/", None),
    "redis": ("https://redis-py.readthedocs.io/en/stable/", None),
}

# Todo configuration
todo_include_todos = True

# MyST parser configuration
myst_enable_extensions = [
    "deflist",
    "tasklist",
    "html_admonition",
    "html_image",
    "colon_fence",
    "smartquotes",
    "replacements",
    "linkify",
    "strikethrough",
    "substitution",
]

# Copy button configuration
copybutton_prompt_text = r">>> |\.\.\. |\$ |In \[\d*\]: | {2,5}\.\.\.: | {5,8}: "
copybutton_prompt_is_regexp = True

# OpenAPI configuration
openapi_spec_url = "http://localhost:8000/openapi.json"

# Custom roles
def setup(app):
    """Set up custom roles and directives."""
    app.add_css_file("custom.css")
    
    # Add custom roles
    from docutils.parsers.rst import roles
    
    def role_api_endpoint(name, rawtext, text, lineno, inliner, options={}, content=[]):
        """Custom role for API endpoints."""
        from docutils import nodes
        node = nodes.literal(rawtext, f"/{text}")
        node["classes"].append("api-endpoint")
        return [node], []
    
    def role_http_method(name, rawtext, text, lineno, inliner, options={}, content=[]):
        """Custom role for HTTP methods."""
        from docutils import nodes
        node = nodes.literal(rawtext, text.upper())
        node["classes"].append(f"http-method-{text.lower()}")
        return [node], []
    
    roles.register_local_role("api", role_api_endpoint)
    roles.register_local_role("http", role_http_method)

# LaTeX configuration
latex_elements = {
    "papersize": "letterpaper",
    "pointsize": "10pt",
    "preamble": "",
    "fncychap": "\\usepackage[Bjornstrup]{fncychap}",
    "printindex": "\\footnotesize\\raggedright\\printindex",
}

latex_documents = [
    (master_doc, "blast-radius.tex", "Blast-Radius Security Tool Documentation", "Blast-Radius Team", "manual"),
]

# Manual page configuration
man_pages = [
    (master_doc, "blast-radius", "Blast-Radius Security Tool Documentation", [author], 1)
]

# Texinfo configuration
texinfo_documents = [
    (
        master_doc,
        "blast-radius",
        "Blast-Radius Security Tool Documentation",
        author,
        "blast-radius",
        "Comprehensive security platform for purple teams.",
        "Miscellaneous",
    ),
]

# Epub configuration
epub_title = project
epub_author = author
epub_publisher = author
epub_copyright = copyright
epub_exclude_files = ["search.html"]
