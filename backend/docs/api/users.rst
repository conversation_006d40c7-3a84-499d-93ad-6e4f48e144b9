Users API
=========

The Users API provides comprehensive user management functionality including user creation, modification, role assignment, and lifecycle management.

Base URL
--------

All user endpoints are prefixed with:

.. code-block:: none

   /api/v1/users

Authentication
--------------

All user management endpoints require authentication and appropriate permissions:

- **JWT <PERSON>ken**: ``Authorization: Bearer <access_token>``
- **API Key**: ``Authorization: Api<PERSON>ey <api_key>``

Required permissions vary by endpoint and are documented for each operation.

User Model
----------

User Resource Structure
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "username": "john.doe",
     "email": "<EMAIL>",
     "full_name": "<PERSON> Doe",
     "department": "Security Operations",
     "job_title": "SOC Analyst",
     "phone_number": "+**********",
     "timezone": "America/New_York",
     "is_active": true,
     "is_verified": true,
     "is_locked": false,
     "mfa_enabled": true,
     "roles": [
       {
         "id": "role-uuid",
         "name": "soc_operator",
         "display_name": "SOC Operator",
         "assigned_at": "2024-01-01T00:00:00Z",
         "expires_at": null
       }
     ],
     "permissions": [
       "view_security_events",
       "create_incident",
       "manage_incidents"
     ],
     "last_login_at": "2024-01-15T10:30:00Z",
     "created_at": "2024-01-01T00:00:00Z",
     "updated_at": "2024-01-15T10:30:00Z"
   }

Field Descriptions
~~~~~~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Field
     - Type
     - Description
   * - id
     - UUID
     - Unique user identifier
   * - username
     - string
     - Unique username (3-50 characters)
   * - email
     - string
     - User email address (must be unique)
   * - full_name
     - string
     - User's full display name
   * - department
     - string
     - User's department or team
   * - job_title
     - string
     - User's job title or role
   * - phone_number
     - string
     - Phone number in E.164 format
   * - timezone
     - string
     - User's timezone (IANA timezone name)
   * - is_active
     - boolean
     - Whether the user account is active
   * - is_verified
     - boolean
     - Whether the user's email is verified
   * - is_locked
     - boolean
     - Whether the account is locked
   * - mfa_enabled
     - boolean
     - Whether MFA is enabled for the user
   * - roles
     - array
     - List of assigned roles with metadata
   * - permissions
     - array
     - Computed list of user permissions
   * - last_login_at
     - datetime
     - Timestamp of last successful login
   * - created_at
     - datetime
     - Account creation timestamp
   * - updated_at
     - datetime
     - Last modification timestamp

Endpoints
---------

List Users
~~~~~~~~~~

Retrieve a paginated list of users with optional filtering.

.. http:get:: /api/v1/users

   **Required Permission**: ``read_user``

   **Query Parameters**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - page
        - integer
        - Page number (default: 1)
      * - size
        - integer
        - Items per page (default: 20, max: 100)
      * - search
        - string
        - Search in username, email, or full name
      * - department
        - string
        - Filter by department
      * - roles
        - string
        - Comma-separated list of role names
      * - active_only
        - boolean
        - Only return active users (default: false)
      * - verified_only
        - boolean
        - Only return verified users (default: false)
      * - created_after
        - datetime
        - Users created after this date
      * - created_before
        - datetime
        - Users created before this date
      * - last_login_after
        - datetime
        - Users who logged in after this date
      * - sort_by
        - string
        - Sort field (username, email, created_at, last_login_at)
      * - sort_order
        - string
        - Sort order (asc, desc)

   **Response**:

   .. code-block:: json

      {
        "items": [
          {
            "id": "123e4567-e89b-12d3-a456-************",
            "username": "john.doe",
            "email": "<EMAIL>",
            "full_name": "John Doe",
            "department": "Security Operations",
            "is_active": true,
            "roles": ["soc_operator"],
            "last_login_at": "2024-01-15T10:30:00Z"
          }
        ],
        "total": 150,
        "page": 1,
        "size": 20,
        "pages": 8,
        "has_next": true,
        "has_prev": false
      }

   **Status Codes**:

   - ``200 OK``: Users retrieved successfully
   - ``401 Unauthorized``: Authentication required
   - ``403 Forbidden``: Insufficient permissions

   **Example**:

   .. code-block:: bash

      curl -G "http://localhost:8000/api/v1/users" \
        -H "Authorization: Bearer <access_token>" \
        -d "search=john" \
        -d "department=Security" \
        -d "active_only=true" \
        -d "page=1" \
        -d "size=20"

Get User
~~~~~~~~

Retrieve detailed information about a specific user.

.. http:get:: /api/v1/users/{user_id}

   **Required Permission**: ``read_user`` (or own user data)

   **Path Parameters**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - user_id
        - UUID
        - User identifier

   **Response**:

   .. code-block:: json

      {
        "id": "123e4567-e89b-12d3-a456-************",
        "username": "john.doe",
        "email": "<EMAIL>",
        "full_name": "John Doe",
        "department": "Security Operations",
        "job_title": "SOC Analyst",
        "phone_number": "+**********",
        "timezone": "America/New_York",
        "is_active": true,
        "is_verified": true,
        "is_locked": false,
        "mfa_enabled": true,
        "roles": [
          {
            "id": "role-uuid",
            "name": "soc_operator",
            "display_name": "SOC Operator",
            "assigned_at": "2024-01-01T00:00:00Z"
          }
        ],
        "permissions": [
          "view_security_events",
          "create_incident"
        ],
        "last_login_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }

   **Status Codes**:

   - ``200 OK``: User retrieved successfully
   - ``401 Unauthorized``: Authentication required
   - ``403 Forbidden``: Insufficient permissions
   - ``404 Not Found``: User not found

Create User
~~~~~~~~~~~

Create a new user account.

.. http:post:: /api/v1/users

   **Required Permission**: ``create_user``

   **Request Body**:

   .. code-block:: json

      {
        "username": "jane.smith",
        "email": "<EMAIL>",
        "full_name": "Jane Smith",
        "password": "SecurePassword123!",
        "department": "Cybersecurity",
        "job_title": "Security Analyst",
        "phone_number": "+**********",
        "timezone": "America/New_York",
        "roles": ["analyst"],
        "send_welcome_email": true,
        "require_password_change": false
      }

   **Required Fields**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Field
        - Type
        - Description
      * - username
        - string
        - Unique username (3-50 characters, alphanumeric + underscore/dot)
      * - email
        - string
        - Valid email address (must be unique)
      * - full_name
        - string
        - User's full name (1-100 characters)
      * - password
        - string
        - Password meeting complexity requirements

   **Optional Fields**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Field
        - Type
        - Description
      * - department
        - string
        - User's department
      * - job_title
        - string
        - User's job title
      * - phone_number
        - string
        - Phone number in E.164 format
      * - timezone
        - string
        - IANA timezone name (default: UTC)
      * - roles
        - array
        - List of role names to assign
      * - send_welcome_email
        - boolean
        - Send welcome email (default: true)
      * - require_password_change
        - boolean
        - Force password change on first login (default: false)

   **Response**:

   .. code-block:: json

      {
        "id": "456e7890-e89b-12d3-a456-************",
        "username": "jane.smith",
        "email": "<EMAIL>",
        "full_name": "Jane Smith",
        "is_active": true,
        "is_verified": false,
        "roles": ["analyst"],
        "created_at": "2024-01-16T00:00:00Z"
      }

   **Status Codes**:

   - ``201 Created``: User created successfully
   - ``400 Bad Request``: Invalid request data
   - ``401 Unauthorized``: Authentication required
   - ``403 Forbidden``: Insufficient permissions
   - ``409 Conflict``: Username or email already exists
   - ``422 Unprocessable Entity``: Validation errors

Update User
~~~~~~~~~~~

Update an existing user's information.

.. http:put:: /api/v1/users/{user_id}

   **Required Permission**: ``update_user`` (or own user data for limited fields)

   **Path Parameters**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - user_id
        - UUID
        - User identifier

   **Request Body**:

   .. code-block:: json

      {
        "full_name": "Jane Smith-Johnson",
        "department": "Advanced Cybersecurity",
        "job_title": "Senior Security Analyst",
        "phone_number": "+1234567891",
        "timezone": "America/Los_Angeles"
      }

   **Updatable Fields**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Field
        - Permission Required
        - Description
      * - full_name
        - Own user or ``update_user``
        - User's display name
      * - email
        - ``update_user``
        - Email address (triggers verification)
      * - department
        - ``update_user``
        - User's department
      * - job_title
        - ``update_user``
        - User's job title
      * - phone_number
        - Own user or ``update_user``
        - Phone number
      * - timezone
        - Own user or ``update_user``
        - User's timezone

   **Response**:

   .. code-block:: json

      {
        "id": "456e7890-e89b-12d3-a456-************",
        "username": "jane.smith",
        "email": "<EMAIL>",
        "full_name": "Jane Smith-Johnson",
        "department": "Advanced Cybersecurity",
        "job_title": "Senior Security Analyst",
        "updated_at": "2024-01-16T12:00:00Z"
      }

   **Status Codes**:

   - ``200 OK``: User updated successfully
   - ``400 Bad Request``: Invalid request data
   - ``401 Unauthorized``: Authentication required
   - ``403 Forbidden``: Insufficient permissions
   - ``404 Not Found``: User not found
   - ``409 Conflict``: Email already exists
   - ``422 Unprocessable Entity``: Validation errors

Delete User
~~~~~~~~~~~

Delete a user account (soft delete by default).

.. http:delete:: /api/v1/users/{user_id}

   **Required Permission**: ``delete_user``

   **Path Parameters**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - user_id
        - UUID
        - User identifier

   **Request Body** (Optional):

   .. code-block:: json

      {
        "reason": "Employee departure",
        "retain_data": true,
        "transfer_ownership_to": "manager-user-id"
      }

   **Response**:

   .. code-block:: json

      {
        "message": "User deleted successfully",
        "deleted_at": "2024-01-16T12:00:00Z"
      }

   **Status Codes**:

   - ``200 OK``: User deleted successfully
   - ``401 Unauthorized``: Authentication required
   - ``403 Forbidden``: Insufficient permissions
   - ``404 Not Found``: User not found

Role Management
---------------

Assign Roles
~~~~~~~~~~~~

Assign roles to a user.

.. http:post:: /api/v1/users/{user_id}/roles

   **Required Permission**: ``manage_user_roles``

   **Request Body**:

   .. code-block:: json

      {
        "roles": ["analyst", "soc_operator"],
        "expires_at": "2024-12-31T23:59:59Z",
        "reason": "Promotion to senior analyst"
      }

   **Response**:

   .. code-block:: json

      {
        "message": "Roles assigned successfully",
        "roles": [
          {
            "name": "analyst",
            "assigned_at": "2024-01-16T12:00:00Z",
            "expires_at": "2024-12-31T23:59:59Z"
          },
          {
            "name": "soc_operator",
            "assigned_at": "2024-01-16T12:00:00Z",
            "expires_at": "2024-12-31T23:59:59Z"
          }
        ]
      }

Remove Roles
~~~~~~~~~~~~

Remove roles from a user.

.. http:delete:: /api/v1/users/{user_id}/roles

   **Required Permission**: ``manage_user_roles``

   **Request Body**:

   .. code-block:: json

      {
        "roles": ["soc_operator"],
        "reason": "Role no longer needed"
      }

   **Response**:

   .. code-block:: json

      {
        "message": "Roles removed successfully",
        "removed_roles": ["soc_operator"]
      }

Account Management
------------------

Lock User Account
~~~~~~~~~~~~~~~~~

Lock a user account to prevent login.

.. http:post:: /api/v1/users/{user_id}/lock

   **Required Permission**: ``manage_user_accounts``

   **Request Body**:

   .. code-block:: json

      {
        "reason": "Suspicious activity detected",
        "duration_minutes": 60,
        "notify_user": true
      }

   **Response**:

   .. code-block:: json

      {
        "message": "User account locked",
        "locked_until": "2024-01-16T13:00:00Z"
      }

Unlock User Account
~~~~~~~~~~~~~~~~~~~

Unlock a previously locked user account.

.. http:post:: /api/v1/users/{user_id}/unlock

   **Required Permission**: ``manage_user_accounts``

   **Request Body**:

   .. code-block:: json

      {
        "reason": "Investigation completed",
        "notify_user": true
      }

   **Response**:

   .. code-block:: json

      {
        "message": "User account unlocked"
      }

Force Password Change
~~~~~~~~~~~~~~~~~~~~~

Require a user to change their password on next login.

.. http:post:: /api/v1/users/{user_id}/force-password-change

   **Required Permission**: ``manage_user_accounts``

   **Request Body**:

   .. code-block:: json

      {
        "reason": "Security policy update",
        "notify_user": true
      }

   **Response**:

   .. code-block:: json

      {
        "message": "Password change required for user"
      }

Bulk Operations
---------------

Bulk Create Users
~~~~~~~~~~~~~~~~~

Create multiple users in a single operation.

.. http:post:: /api/v1/users/bulk

   **Required Permission**: ``create_user``

   **Request Body**:

   .. code-block:: json

      {
        "users": [
          {
            "username": "user1",
            "email": "<EMAIL>",
            "full_name": "User One",
            "roles": ["analyst"]
          },
          {
            "username": "user2",
            "email": "<EMAIL>",
            "full_name": "User Two",
            "roles": ["viewer"]
          }
        ],
        "default_password": "TempPassword123!",
        "send_welcome_emails": true,
        "require_password_change": true
      }

   **Response**:

   .. code-block:: json

      {
        "created": 2,
        "failed": 0,
        "results": [
          {
            "username": "user1",
            "status": "created",
            "id": "user1-uuid"
          },
          {
            "username": "user2",
            "status": "created",
            "id": "user2-uuid"
          }
        ]
      }

Import Users
~~~~~~~~~~~~

Import users from CSV or JSON file.

.. http:post:: /api/v1/users/import

   **Required Permission**: ``create_user``

   **Request Body** (multipart/form-data):

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Field
        - Type
        - Description
      * - file
        - file
        - CSV or JSON file with user data
      * - format
        - string
        - File format (csv, json)
      * - send_welcome_emails
        - boolean
        - Send welcome emails to new users
      * - require_password_change
        - boolean
        - Require password change on first login

   **CSV Format**:

   .. code-block:: csv

      username,email,full_name,department,job_title,roles
      john.doe,<EMAIL>,John Doe,Security,Analyst,"analyst,viewer"
      jane.smith,<EMAIL>,Jane Smith,IT,Admin,admin

   **Response**:

   .. code-block:: json

      {
        "imported": 2,
        "failed": 0,
        "errors": []
      }

User Activity
-------------

Get User Activity
~~~~~~~~~~~~~~~~~

Retrieve user activity logs and statistics.

.. http:get:: /api/v1/users/{user_id}/activity

   **Required Permission**: ``view_user_activity`` (or own user data)

   **Query Parameters**:

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - start_date
        - datetime
        - Activity start date
      * - end_date
        - datetime
        - Activity end date
      * - action_type
        - string
        - Filter by action type
      * - page
        - integer
        - Page number
      * - size
        - integer
        - Items per page

   **Response**:

   .. code-block:: json

      {
        "activities": [
          {
            "id": "activity-uuid",
            "action": "login",
            "timestamp": "2024-01-16T10:00:00Z",
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "details": {
              "mfa_used": true,
              "location": "New York, NY"
            }
          }
        ],
        "summary": {
          "total_logins": 45,
          "failed_logins": 2,
          "last_login": "2024-01-16T10:00:00Z",
          "most_active_day": "Monday"
        }
      }

Get User Sessions
~~~~~~~~~~~~~~~~~

Retrieve active sessions for a user.

.. http:get:: /api/v1/users/{user_id}/sessions

   **Required Permission**: ``manage_user_sessions`` (or own user data)

   **Response**:

   .. code-block:: json

      {
        "sessions": [
          {
            "id": "session-uuid",
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "created_at": "2024-01-16T10:00:00Z",
            "last_activity": "2024-01-16T10:30:00Z",
            "is_current": true
          }
        ]
      }

Terminate User Sessions
~~~~~~~~~~~~~~~~~~~~~~~

Terminate all sessions for a user.

.. http:delete:: /api/v1/users/{user_id}/sessions

   **Required Permission**: ``manage_user_sessions``

   **Response**:

   .. code-block:: json

      {
        "message": "All user sessions terminated",
        "terminated_sessions": 3
      }

Error Handling
--------------

The Users API returns detailed error information for validation failures and other issues.

**Validation Error Example**:

.. code-block:: json

   {
     "detail": "Validation error",
     "errors": [
       {
         "field": "username",
         "message": "Username must be between 3 and 50 characters"
       },
       {
         "field": "email",
         "message": "Invalid email format"
       },
       {
         "field": "password",
         "message": "Password must contain at least one uppercase letter"
       }
     ]
   }

**Conflict Error Example**:

.. code-block:: json

   {
     "detail": "User with this email already exists",
     "error_code": "USER_EMAIL_EXISTS",
     "conflicting_field": "email"
   }

For more information about error handling, see the :doc:`../user-guide/api-usage` guide.
