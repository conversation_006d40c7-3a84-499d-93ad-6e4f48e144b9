Authentication API
==================

The Authentication API provides endpoints for user authentication, session management, and multi-factor authentication.

Base URL
--------

All authentication endpoints are prefixed with:

.. code-block:: none

   /api/v1/auth

Authentication Flow
-------------------

The Blast-Radius Security Tool uses JWT (JSON Web Tokens) for authentication with the following flow:

1. **Login**: Exchange credentials for access and refresh tokens
2. **Access**: Use access token for API requests
3. **Refresh**: Use refresh token to get new access tokens
4. **Logout**: Invalidate tokens and end session

Token Types
~~~~~~~~~~~

**Access Token**
   - Short-lived (default: 30 minutes)
   - Used for API authentication
   - Contains user permissions and roles

**Refresh Token**
   - Long-lived (default: 7 days)
   - Used to obtain new access tokens
   - Stored securely and can be revoked

Endpoints
---------

Login
~~~~~

Authenticate a user and receive access tokens.

.. http:post:: /api/v1/auth/login

   **Request Body:**

   .. code-block:: json

      {
        "username": "string",
        "password": "string",
        "remember_me": false,
        "mfa_code": "string"
      }

   **Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - username
        - string
        - Username or email address (required)
      * - password
        - string
        - User password (required)
      * - remember_me
        - boolean
        - Extend session duration (optional, default: false)
      * - mfa_code
        - string
        - MFA verification code (required if MFA enabled)

   **Response (Success):**

   .. code-block:: json

      {
        "user": {
          "id": "string",
          "username": "string",
          "email": "string",
          "full_name": "string",
          "roles": ["string"],
          "permissions": ["string"],
          "is_active": true,
          "is_verified": true,
          "mfa_enabled": false,
          "last_login_at": "2024-01-01T00:00:00Z"
        },
        "tokens": {
          "access_token": "string",
          "refresh_token": "string",
          "token_type": "bearer",
          "expires_in": 1800
        },
        "session_id": "string"
      }

   **Response (MFA Required):**

   .. code-block:: json

      {
        "requires_mfa": true,
        "mfa_methods": ["totp", "sms", "email"],
        "user": {
          "id": "string",
          "username": "string"
        }
      }

   **Status Codes:**

   - ``200 OK``: Login successful
   - ``202 Accepted``: MFA required
   - ``400 Bad Request``: Invalid request data
   - ``401 Unauthorized``: Invalid credentials
   - ``423 Locked``: Account locked
   - ``429 Too Many Requests``: Rate limit exceeded

   **Example:**

   .. code-block:: bash

      curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "admin",
          "password": "AdminPassword123!",
          "remember_me": false
        }'

Logout
~~~~~~

Invalidate tokens and end the user session.

.. http:post:: /api/v1/auth/logout

   **Headers:**

   .. code-block:: none

      Authorization: Bearer <access_token>

   **Request Body:**

   .. code-block:: json

      {
        "all_sessions": false
      }

   **Parameters:**

   .. list-table::
      :header-rows: 1
      :widths: 20 20 60

      * - Parameter
        - Type
        - Description
      * - all_sessions
        - boolean
        - Logout from all sessions (optional, default: false)

   **Response:**

   .. code-block:: json

      {
        "message": "Logged out successfully"
      }

   **Status Codes:**

   - ``200 OK``: Logout successful
   - ``401 Unauthorized``: Invalid or expired token

Refresh Token
~~~~~~~~~~~~~

Obtain a new access token using a refresh token.

.. http:post:: /api/v1/auth/refresh

   **Request Body:**

   .. code-block:: json

      {
        "refresh_token": "string"
      }

   **Response:**

   .. code-block:: json

      {
        "access_token": "string",
        "token_type": "bearer",
        "expires_in": 1800
      }

   **Status Codes:**

   - ``200 OK``: Token refreshed successfully
   - ``401 Unauthorized``: Invalid or expired refresh token

Current User
~~~~~~~~~~~~

Get information about the currently authenticated user.

.. http:get:: /api/v1/auth/me

   **Headers:**

   .. code-block:: none

      Authorization: Bearer <access_token>

   **Response:**

   .. code-block:: json

      {
        "id": "string",
        "username": "string",
        "email": "string",
        "full_name": "string",
        "roles": ["string"],
        "permissions": ["string"],
        "is_active": true,
        "is_verified": true,
        "mfa_enabled": false,
        "last_login_at": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }

   **Status Codes:**

   - ``200 OK``: User information retrieved
   - ``401 Unauthorized``: Invalid or expired token

Change Password
~~~~~~~~~~~~~~~

Change the current user's password.

.. http:post:: /api/v1/auth/change-password

   **Headers:**

   .. code-block:: none

      Authorization: Bearer <access_token>

   **Request Body:**

   .. code-block:: json

      {
        "current_password": "string",
        "new_password": "string"
      }

   **Response:**

   .. code-block:: json

      {
        "message": "Password changed successfully"
      }

   **Status Codes:**

   - ``200 OK``: Password changed successfully
   - ``400 Bad Request``: Invalid password format
   - ``401 Unauthorized``: Invalid current password
   - ``422 Unprocessable Entity``: Password validation failed

Password Reset
~~~~~~~~~~~~~~

Request a password reset for a user account.

.. http:post:: /api/v1/auth/reset-password

   **Request Body:**

   .. code-block:: json

      {
        "email": "string"
      }

   **Response:**

   .. code-block:: json

      {
        "message": "Password reset email sent"
      }

   **Status Codes:**

   - ``200 OK``: Reset email sent (always returned for security)
   - ``400 Bad Request``: Invalid email format

Confirm Password Reset
~~~~~~~~~~~~~~~~~~~~~~

Confirm password reset with token and set new password.

.. http:post:: /api/v1/auth/reset-password/confirm

   **Request Body:**

   .. code-block:: json

      {
        "token": "string",
        "new_password": "string"
      }

   **Response:**

   .. code-block:: json

      {
        "message": "Password reset successfully"
      }

   **Status Codes:**

   - ``200 OK``: Password reset successfully
   - ``400 Bad Request``: Invalid or expired token
   - ``422 Unprocessable Entity``: Password validation failed

Multi-Factor Authentication
---------------------------

Setup MFA
~~~~~~~~~~

Setup multi-factor authentication for the current user.

.. http:post:: /api/v1/auth/mfa/setup

   **Headers:**

   .. code-block:: none

      Authorization: Bearer <access_token>

   **Request Body:**

   .. code-block:: json

      {
        "method": "totp"
      }

   **Response (TOTP):**

   .. code-block:: json

      {
        "method": "totp",
        "secret": "string",
        "qr_code": "data:image/png;base64,...",
        "backup_codes": ["string"]
      }

   **Status Codes:**

   - ``200 OK``: MFA setup initiated
   - ``400 Bad Request``: Invalid MFA method
   - ``409 Conflict``: MFA already enabled

Verify MFA Setup
~~~~~~~~~~~~~~~~~

Verify and enable MFA with a test code.

.. http:post:: /api/v1/auth/mfa/verify-setup

   **Headers:**

   .. code-block:: none

      Authorization: Bearer <access_token>

   **Request Body:**

   .. code-block:: json

      {
        "method": "totp",
        "code": "123456"
      }

   **Response:**

   .. code-block:: json

      {
        "message": "MFA enabled successfully",
        "backup_codes": ["string"]
      }

   **Status Codes:**

   - ``200 OK``: MFA enabled successfully
   - ``400 Bad Request``: Invalid verification code

Disable MFA
~~~~~~~~~~~~

Disable multi-factor authentication for the current user.

.. http:post:: /api/v1/auth/mfa/disable

   **Headers:**

   .. code-block:: none

      Authorization: Bearer <access_token>

   **Request Body:**

   .. code-block:: json

      {
        "password": "string",
        "code": "123456"
      }

   **Response:**

   .. code-block:: json

      {
        "message": "MFA disabled successfully"
      }

   **Status Codes:**

   - ``200 OK``: MFA disabled successfully
   - ``400 Bad Request``: Invalid password or MFA code

Error Responses
---------------

All authentication endpoints may return the following error responses:

**400 Bad Request**

.. code-block:: json

   {
     "detail": "Invalid request data",
     "errors": [
       {
         "field": "username",
         "message": "Username is required"
       }
     ]
   }

**401 Unauthorized**

.. code-block:: json

   {
     "detail": "Invalid credentials"
   }

**423 Locked**

.. code-block:: json

   {
     "detail": "Account is locked",
     "locked_until": "2024-01-01T01:00:00Z"
   }

**429 Too Many Requests**

.. code-block:: json

   {
     "detail": "Too many login attempts",
     "retry_after": 300
   }

Rate Limiting
-------------

Authentication endpoints are rate-limited to prevent abuse:

- **Login**: 5 attempts per minute per IP
- **Password Reset**: 3 requests per hour per email
- **MFA Setup**: 3 attempts per hour per user

Security Considerations
-----------------------

**Token Security**
   - Access tokens are short-lived (30 minutes default)
   - Refresh tokens are long-lived but can be revoked
   - Tokens are signed with HMAC-SHA256

**Password Security**
   - Passwords are hashed with bcrypt
   - Minimum password requirements enforced
   - Password history prevents reuse

**Account Security**
   - Account lockout after failed attempts
   - Rate limiting on sensitive endpoints
   - Audit logging for all authentication events

**MFA Security**
   - TOTP uses RFC 6238 standard
   - Backup codes for account recovery
   - Time-based code validation with drift tolerance
