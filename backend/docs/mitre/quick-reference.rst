MITRE Quick Reference
====================

This page provides a quick reference for common MITRE ATT&CK operations and configurations
in the Blast-Radius Security Tool.

.. important::
   📚 **Quick Access**: This reference provides the most commonly used commands,
   configurations, and code snippets for MITRE ATT&CK integration.

Common Operations
-----------------

Database Queries
~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.db.models.mitre import MitreTechnique, MitreTactic, MitreGroup
   
   # Get technique by ID
   technique = MitreTechnique.query.filter_by(
       technique_id="T1055",
       is_deleted=False
   ).first()
   
   # Get all Windows techniques
   windows_techniques = MitreTechnique.query.filter(
       MitreTechnique.platforms.contains(["Windows"]),
       MitreTechnique.is_deleted == False
   ).all()
   
   # Get techniques by tactic
   initial_access = MitreTactic.query.filter_by(tactic_id="TA0001").first()
   techniques = initial_access.techniques if initial_access else []
   
   # Get group techniques
   apt29 = MitreGroup.query.filter_by(group_id="G0016").first()
   group_techniques = apt29.techniques if apt29 else []

API Calls
~~~~~~~~~

.. code-block:: bash

   # List techniques
   curl -H "Authorization: Bearer TOKEN" \
        "https://api.example.com/api/v1/mitre/techniques?domain=enterprise&limit=50"
   
   # Get specific technique
   curl -H "Authorization: Bearer TOKEN" \
        "https://api.example.com/api/v1/mitre/techniques/T1055"
   
   # Search techniques
   curl -X POST -H "Authorization: Bearer TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"query":"lateral movement","limit":20}' \
        "https://api.example.com/api/v1/mitre/search"
   
   # Trigger sync
   curl -X POST -H "Authorization: Bearer TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"domain":"enterprise","force_update":true}' \
        "https://api.example.com/api/v1/mitre/sync"

Schema Usage
~~~~~~~~~~~~

.. code-block:: python

   from app.schemas.mitre import MitreTechniqueCreate, MitreSearchRequest
   from app.db.models.mitre import MitreDomain
   
   # Create technique schema
   technique_data = MitreTechniqueCreate(
       technique_id="T9999",
       name="Custom Technique",
       domain=MitreDomain.ENTERPRISE,
       platforms=["Windows", "Linux"]
   )
   
   # Search request schema
   search_request = MitreSearchRequest(
       query="process injection",
       domains=[MitreDomain.ENTERPRISE],
       entity_types=["technique"],
       limit=50
   )

Configuration Snippets
----------------------

Basic Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # config/mitre.yaml
   mitre:
     data_sources:
       enterprise:
         url: "https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json"
         enabled: true
     
     sync:
       auto_sync_enabled: true
       schedules:
         enterprise: "0 2 * * 1"  # Weekly Monday 2 AM
       batch_size: 100
     
     cache:
       enabled: true
       backend: "redis"
       ttl_seconds: 3600

Environment Variables
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Essential environment variables
   export MITRE_AUTO_SYNC_ENABLED=true
   export MITRE_BATCH_SIZE=100
   export MITRE_CACHE_ENABLED=true
   export MITRE_REQUIRE_AUTH=true

Common Patterns
--------------

Threat Hunting
~~~~~~~~~~~~~~

.. code-block:: python

   # Find techniques used by specific groups
   def get_group_techniques(group_name):
       group = MitreGroup.query.filter(
           MitreGroup.name.ilike(f"%{group_name}%"),
           MitreGroup.is_deleted == False
       ).first()
       return group.techniques if group else []
   
   # Find techniques for specific platforms
   def get_platform_techniques(platform):
       return MitreTechnique.query.filter(
           MitreTechnique.platforms.contains([platform]),
           MitreTechnique.is_deleted == False
       ).all()

Security Analysis
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Analyze mitigation coverage
   def analyze_coverage(platform="Windows"):
       techniques = MitreTechnique.query.filter(
           MitreTechnique.platforms.contains([platform]),
           MitreTechnique.is_deleted == False
       ).all()
       
       mitigated = sum(1 for t in techniques if t.mitigations)
       total = len(techniques)
       coverage = (mitigated / total * 100) if total > 0 else 0
       
       return {
           "total_techniques": total,
           "mitigated_techniques": mitigated,
           "coverage_percentage": coverage
       }

Attack Path Mapping
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Map attack progression
   def map_attack_path(tactic_ids):
       attack_path = []
       for tactic_id in tactic_ids:
           tactic = MitreTactic.query.filter_by(
               tactic_id=tactic_id,
               is_deleted=False
           ).first()
           
           if tactic:
               techniques = tactic.techniques[:5]  # Top 5 techniques
               attack_path.append({
                   "tactic": tactic.name,
                   "techniques": [t.name for t in techniques]
               })
       
       return attack_path

Error Handling
--------------

Common Error Patterns
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from sqlalchemy.exc import SQLAlchemyError
   from pydantic import ValidationError
   
   try:
       # Database operation
       technique = MitreTechnique.query.filter_by(technique_id="T1055").first()
       if not technique:
           raise ValueError("Technique not found")
   
   except SQLAlchemyError as e:
       logger.error(f"Database error: {e}")
       # Handle database errors
   
   except ValidationError as e:
       logger.error(f"Validation error: {e}")
       # Handle validation errors
   
   except Exception as e:
       logger.error(f"Unexpected error: {e}")
       # Handle unexpected errors

API Error Handling
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import httpx
   
   async def safe_api_call(url, headers):
       try:
           async with httpx.AsyncClient() as client:
               response = await client.get(url, headers=headers)
               response.raise_for_status()
               return response.json()
       
       except httpx.HTTPStatusError as e:
           if e.response.status_code == 404:
               return None  # Not found
           elif e.response.status_code == 401:
               raise AuthenticationError("Invalid token")
           else:
               raise APIError(f"API error: {e}")
       
       except httpx.RequestError as e:
           raise NetworkError(f"Network error: {e}")

Performance Tips
---------------

Database Optimization
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Use eager loading for relationships
   technique = MitreTechnique.query.options(
       joinedload(MitreTechnique.tactics),
       joinedload(MitreTechnique.mitigations)
   ).filter_by(technique_id="T1055").first()
   
   # Use batch queries
   technique_ids = ["T1055", "T1021", "T1078"]
   techniques = MitreTechnique.query.filter(
       MitreTechnique.technique_id.in_(technique_ids),
       MitreTechnique.is_deleted == False
   ).all()
   
   # Use pagination for large results
   page_size = 50
   techniques = MitreTechnique.query.filter(
       MitreTechnique.is_deleted == False
   ).offset(page * page_size).limit(page_size).all()

Caching Strategies
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from functools import lru_cache
   
   @lru_cache(maxsize=1000)
   def get_technique_cached(technique_id):
       return MitreTechnique.query.filter_by(
           technique_id=technique_id,
           is_deleted=False
       ).first()
   
   # Clear cache when needed
   get_technique_cached.cache_clear()

Monitoring and Debugging
------------------------

Logging Configuration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import logging
   
   # Configure MITRE-specific logging
   mitre_logger = logging.getLogger('mitre')
   mitre_logger.setLevel(logging.INFO)
   
   handler = logging.StreamHandler()
   formatter = logging.Formatter(
       '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
   )
   handler.setFormatter(formatter)
   mitre_logger.addHandler(handler)
   
   # Use in code
   mitre_logger.info(f"Processing technique {technique_id}")
   mitre_logger.error(f"Failed to sync domain {domain}: {error}")

Health Checks
~~~~~~~~~~~~

.. code-block:: python

   async def check_mitre_health():
       """Check MITRE system health."""
       health_status = {
           "database": False,
           "cache": False,
           "data_sources": False,
           "last_sync": None
       }
       
       try:
           # Check database
           count = MitreTechnique.query.count()
           health_status["database"] = count > 0
           
           # Check cache (if Redis)
           # health_status["cache"] = await check_redis_connection()
           
           # Check data sources
           # health_status["data_sources"] = await check_data_sources()
           
           # Get last sync
           last_sync = MitreDataSync.query.order_by(
               MitreDataSync.completed_at.desc()
           ).first()
           health_status["last_sync"] = last_sync.completed_at if last_sync else None
           
       except Exception as e:
           logger.error(f"Health check failed: {e}")
       
       return health_status

Useful Queries
--------------

Statistics Queries
~~~~~~~~~~~~~~~~~

.. code-block:: sql

   -- Count techniques by domain
   SELECT domain, COUNT(*) as technique_count
   FROM mitre_techniques
   WHERE is_deleted = false
   GROUP BY domain;
   
   -- Count techniques by platform
   SELECT platform, COUNT(*) as count
   FROM (
       SELECT unnest(platforms) as platform
       FROM mitre_techniques
       WHERE is_deleted = false
   ) t
   GROUP BY platform
   ORDER BY count DESC;
   
   -- Find techniques without mitigations
   SELECT t.technique_id, t.name
   FROM mitre_techniques t
   LEFT JOIN mitre_technique_mitigation_associations tm ON t.id = tm.technique_id
   WHERE t.is_deleted = false AND tm.technique_id IS NULL;

Relationship Queries
~~~~~~~~~~~~~~~~~~~

.. code-block:: sql

   -- Get technique-tactic relationships
   SELECT t.technique_id, t.name, ta.tactic_id, ta.name as tactic_name
   FROM mitre_techniques t
   JOIN mitre_technique_tactic_associations tta ON t.id = tta.technique_id
   JOIN mitre_tactics ta ON tta.tactic_id = ta.id
   WHERE t.is_deleted = false AND ta.is_deleted = false;
   
   -- Get group-technique usage
   SELECT g.group_id, g.name, COUNT(gt.technique_id) as technique_count
   FROM mitre_groups g
   LEFT JOIN mitre_group_technique_associations gt ON g.id = gt.group_id
   WHERE g.is_deleted = false
   GROUP BY g.group_id, g.name
   ORDER BY technique_count DESC;

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Issue
     - Solution
   * - Sync timeout
     - Increase timeout, reduce batch size, check network
   * - Memory issues
     - Reduce batch size, increase available memory
   * - Cache misses
     - Check Redis connection, adjust TTL values
   * - Search slow
     - Rebuild indexes, consider Elasticsearch
   * - API rate limits
     - Implement backoff, check rate limit settings

Debug Commands
~~~~~~~~~~~~~

.. code-block:: bash

   # Check sync status
   curl -H "Authorization: Bearer TOKEN" \
        "https://api.example.com/api/v1/mitre/sync"
   
   # Get statistics
   curl -H "Authorization: Bearer TOKEN" \
        "https://api.example.com/api/v1/mitre/statistics"
   
   # Health check
   curl -H "Authorization: Bearer TOKEN" \
        "https://api.example.com/api/v1/health/mitre"

Quick Links
----------

- :doc:`overview` - Complete MITRE overview
- :doc:`api-reference` - Full API documentation
- :doc:`configuration` - Configuration options
- :doc:`examples` - Detailed examples and use cases
