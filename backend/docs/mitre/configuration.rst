MITRE Configuration
===================

.. currentmodule:: app.config

The Blast-Radius Security Tool provides comprehensive configuration options for
MITRE ATT&CK integration, allowing you to customize synchronization, caching,
and performance settings.

.. important::
   ⚙️ **Environment-Specific**: Configuration can be customized per environment
   (development, staging, production) using environment variables or config files.

Configuration Overview
----------------------

MITRE configuration is organized into several sections:

📊 **Data Sources**
   - Official MITRE STIX data sources
   - Custom data source configurations
   - Authentication and access settings
   - Backup and fallback sources

🔄 **Synchronization**
   - Automatic sync schedules
   - Manual sync triggers
   - Conflict resolution strategies
   - Performance optimization

🔒 **Security & Audit**
   - User access controls
   - Audit logging configuration
   - Data retention policies
   - Soft delete settings

⚡ **Performance**
   - Caching strategies
   - Database optimization
   - Search indexing
   - Rate limiting

Configuration Files
-------------------

Main Configuration
~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # config/mitre.yaml
   mitre:
     # Data source configuration
     data_sources:
       enterprise:
         url: "https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json"
         format: "stix2.1"
         enabled: true
         timeout: 300
         retry_attempts: 3
         retry_delay: 60
       
       mobile:
         url: "https://raw.githubusercontent.com/mitre/cti/master/mobile-attack/mobile-attack.json"
         format: "stix2.1"
         enabled: true
         timeout: 300
         retry_attempts: 3
         retry_delay: 60
       
       ics:
         url: "https://raw.githubusercontent.com/mitre/cti/master/ics-attack/ics-attack.json"
         format: "stix2.1"
         enabled: true
         timeout: 300
         retry_attempts: 3
         retry_delay: 60
     
     # Synchronization settings
     sync:
       # Automatic sync schedules (cron format)
       schedules:
         enterprise: "0 2 * * 1"    # Weekly on Monday at 2 AM
         mobile: "0 3 1 * *"        # Monthly on 1st at 3 AM
         ics: "0 4 1 */3 *"          # Quarterly on 1st at 4 AM
       
       # Sync behavior
       auto_sync_enabled: true
       force_update_on_schedule: false
       max_concurrent_syncs: 1
       sync_timeout_minutes: 60
       
       # Conflict resolution
       conflict_resolution: "mitre_precedence"  # mitre_precedence, custom_precedence, manual
       preserve_custom_data: true
       backup_before_sync: true
       
       # Performance settings
       batch_size: 100
       commit_frequency: 1000
       parallel_processing: true
       max_workers: 4
     
     # Caching configuration
     cache:
       enabled: true
       backend: "redis"  # redis, memory, database
       ttl_seconds: 3600
       max_size: 10000
       
       # Cache keys
       technique_cache_ttl: 7200
       search_cache_ttl: 1800
       relationship_cache_ttl: 3600
     
     # Search configuration
     search:
       enabled: true
       engine: "postgresql"  # postgresql, elasticsearch
       
       # Full-text search settings
       language: "english"
       min_word_length: 3
       max_results: 1000
       
       # Search indexing
       auto_index_updates: true
       index_batch_size: 500
       
       # Faceted search
       enable_facets: true
       max_facet_values: 100
     
     # Audit and logging
     audit:
       enabled: true
       log_level: "INFO"
       
       # Audit events
       log_sync_operations: true
       log_data_changes: true
       log_user_actions: true
       log_api_requests: false
       
       # Retention
       audit_retention_days: 365
       cleanup_frequency: "daily"
     
     # Security settings
     security:
       # Access control
       require_authentication: true
       admin_only_sync: true
       read_only_users: []
       
       # Data protection
       soft_delete_enabled: true
       permanent_delete_disabled: true
       data_encryption_at_rest: false
       
       # Rate limiting
       api_rate_limit: 1000  # requests per hour
       sync_rate_limit: 10   # syncs per hour

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Data source URLs
   MITRE_ENTERPRISE_URL="https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json"
   MITRE_MOBILE_URL="https://raw.githubusercontent.com/mitre/cti/master/mobile-attack/mobile-attack.json"
   MITRE_ICS_URL="https://raw.githubusercontent.com/mitre/cti/master/ics-attack/ics-attack.json"
   
   # Sync settings
   MITRE_AUTO_SYNC_ENABLED=true
   MITRE_SYNC_TIMEOUT_MINUTES=60
   MITRE_BATCH_SIZE=100
   
   # Cache settings
   MITRE_CACHE_ENABLED=true
   MITRE_CACHE_TTL_SECONDS=3600
   MITRE_CACHE_BACKEND=redis
   
   # Security settings
   MITRE_REQUIRE_AUTH=true
   MITRE_ADMIN_ONLY_SYNC=true
   MITRE_SOFT_DELETE_ENABLED=true

Development Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # config/mitre_development.yaml
   mitre:
     data_sources:
       enterprise:
         url: "file:///app/test_data/enterprise-attack.json"  # Local test data
         enabled: true
       mobile:
         enabled: false  # Disable in development
       ics:
         enabled: false  # Disable in development
     
     sync:
       auto_sync_enabled: false  # Manual sync only in dev
       batch_size: 10           # Smaller batches for testing
       max_workers: 1           # Single worker for debugging
     
     cache:
       backend: "memory"        # Use memory cache for development
       ttl_seconds: 300         # Shorter TTL for testing
     
     audit:
       log_level: "DEBUG"       # Verbose logging
       log_api_requests: true   # Log all API requests

Production Configuration
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # config/mitre_production.yaml
   mitre:
     sync:
       auto_sync_enabled: true
       backup_before_sync: true
       max_workers: 8           # More workers for production
       sync_timeout_minutes: 120 # Longer timeout
     
     cache:
       backend: "redis"
       ttl_seconds: 7200        # Longer TTL for production
       max_size: 50000          # Larger cache
     
     search:
       engine: "elasticsearch"  # Use Elasticsearch in production
       max_results: 10000
     
     security:
       data_encryption_at_rest: true
       api_rate_limit: 5000     # Higher rate limit
     
     audit:
       log_level: "INFO"
       audit_retention_days: 1095  # 3 years retention

Configuration Validation
------------------------

Schema Validation
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pydantic import BaseModel, Field, validator
   from typing import Optional, Dict, List
   
   class MitreDataSourceConfig(BaseModel):
       """Configuration for a MITRE data source."""
       url: str = Field(..., description="Data source URL")
       format: str = Field(default="stix2.1", description="Data format")
       enabled: bool = Field(default=True, description="Enable this source")
       timeout: int = Field(default=300, description="Request timeout in seconds")
       retry_attempts: int = Field(default=3, description="Number of retry attempts")
       retry_delay: int = Field(default=60, description="Delay between retries")
       
       @validator('timeout')
       def validate_timeout(cls, v):
           if v < 30 or v > 3600:
               raise ValueError('Timeout must be between 30 and 3600 seconds')
           return v
   
   class MitreSyncConfig(BaseModel):
       """Configuration for MITRE synchronization."""
       schedules: Dict[str, str] = Field(default_factory=dict)
       auto_sync_enabled: bool = Field(default=True)
       force_update_on_schedule: bool = Field(default=False)
       max_concurrent_syncs: int = Field(default=1)
       sync_timeout_minutes: int = Field(default=60)
       batch_size: int = Field(default=100)
       
       @validator('batch_size')
       def validate_batch_size(cls, v):
           if v < 1 or v > 1000:
               raise ValueError('Batch size must be between 1 and 1000')
           return v
   
   class MitreConfig(BaseModel):
       """Complete MITRE configuration."""
       data_sources: Dict[str, MitreDataSourceConfig]
       sync: MitreSyncConfig
       cache: Dict[str, any] = Field(default_factory=dict)
       search: Dict[str, any] = Field(default_factory=dict)
       audit: Dict[str, any] = Field(default_factory=dict)
       security: Dict[str, any] = Field(default_factory=dict)

Configuration Loading
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import yaml
   import os
   from pathlib import Path
   
   class MitreConfigLoader:
       """Load and validate MITRE configuration."""
       
       def __init__(self, config_dir: str = "config"):
           self.config_dir = Path(config_dir)
           self.environment = os.getenv("ENVIRONMENT", "development")
       
       def load_config(self) -> MitreConfig:
           """Load configuration from files and environment."""
           
           # Load base configuration
           base_config = self._load_yaml_config("mitre.yaml")
           
           # Load environment-specific overrides
           env_config_file = f"mitre_{self.environment}.yaml"
           env_config = self._load_yaml_config(env_config_file, required=False)
           
           # Merge configurations
           merged_config = self._merge_configs(base_config, env_config)
           
           # Apply environment variable overrides
           final_config = self._apply_env_overrides(merged_config)
           
           # Validate configuration
           return MitreConfig(**final_config)
       
       def _load_yaml_config(self, filename: str, required: bool = True) -> dict:
           """Load YAML configuration file."""
           config_file = self.config_dir / filename
           
           if not config_file.exists():
               if required:
                   raise FileNotFoundError(f"Configuration file not found: {config_file}")
               return {}
           
           with open(config_file, 'r') as f:
               return yaml.safe_load(f) or {}
       
       def _merge_configs(self, base: dict, override: dict) -> dict:
           """Recursively merge configuration dictionaries."""
           result = base.copy()
           
           for key, value in override.items():
               if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                   result[key] = self._merge_configs(result[key], value)
               else:
                   result[key] = value
           
           return result
       
       def _apply_env_overrides(self, config: dict) -> dict:
           """Apply environment variable overrides."""
           # Map environment variables to config paths
           env_mappings = {
               "MITRE_AUTO_SYNC_ENABLED": "mitre.sync.auto_sync_enabled",
               "MITRE_BATCH_SIZE": "mitre.sync.batch_size",
               "MITRE_CACHE_ENABLED": "mitre.cache.enabled",
               "MITRE_CACHE_TTL_SECONDS": "mitre.cache.ttl_seconds",
           }
           
           for env_var, config_path in env_mappings.items():
               env_value = os.getenv(env_var)
               if env_value is not None:
                   self._set_nested_value(config, config_path, env_value)
           
           return config

Configuration Best Practices
----------------------------

1. **Environment Separation**
   - Use separate configurations for dev/staging/production
   - Store sensitive values in environment variables
   - Use configuration validation to catch errors early

2. **Performance Tuning**
   - Adjust batch sizes based on system resources
   - Configure appropriate cache TTL values
   - Set reasonable timeout values

3. **Security Considerations**
   - Enable authentication for all environments
   - Use HTTPS for data source URLs
   - Configure appropriate rate limits

4. **Monitoring and Alerting**
   - Enable comprehensive audit logging
   - Set up alerts for sync failures
   - Monitor cache hit rates and performance

5. **Backup and Recovery**
   - Enable backup before sync operations
   - Configure appropriate data retention
   - Test recovery procedures regularly

Configuration Examples
----------------------

High-Performance Setup
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   mitre:
     sync:
       batch_size: 500
       max_workers: 8
       parallel_processing: true
       commit_frequency: 2000
     
     cache:
       backend: "redis"
       ttl_seconds: 14400  # 4 hours
       max_size: 100000
     
     search:
       engine: "elasticsearch"
       index_batch_size: 1000

High-Security Setup
~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   mitre:
     security:
       require_authentication: true
       admin_only_sync: true
       data_encryption_at_rest: true
       api_rate_limit: 100  # Conservative rate limit
     
     audit:
       enabled: true
       log_level: "INFO"
       log_sync_operations: true
       log_data_changes: true
       log_user_actions: true
       audit_retention_days: 2555  # 7 years

Troubleshooting
--------------

Common Configuration Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Sync Timeouts**
   - Increase `sync_timeout_minutes`
   - Reduce `batch_size`
   - Check network connectivity

2. **Memory Issues**
   - Reduce `batch_size`
   - Decrease `max_workers`
   - Increase available memory

3. **Cache Performance**
   - Verify Redis connectivity
   - Adjust TTL values
   - Monitor cache hit rates

4. **Search Performance**
   - Rebuild search indexes
   - Adjust `index_batch_size`
   - Consider Elasticsearch for large datasets

Next Steps
----------

- :doc:`import-sync` - Learn about data import and synchronization
- :doc:`updates` - Understanding the update system
- :doc:`api-reference` - Complete API reference
