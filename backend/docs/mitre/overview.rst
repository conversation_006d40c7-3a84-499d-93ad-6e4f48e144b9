MITRE ATT&CK Integration
========================

.. currentmodule:: app.db.models.mitre

The Blast-Radius Security Tool includes comprehensive integration with the MITRE ATT&CK framework,
providing enterprise-grade threat intelligence capabilities for security operations, threat hunting,
and attack path analysis.

.. important::
   🚀 **New Feature**: Complete MITRE ATT&CK integration with Pydantic validation,
   soft delete functionality, and real-time synchronization capabilities.

Overview
--------

The MITRE ATT&CK integration provides:

🎯 **Complete Framework Coverage**
   - **Techniques**: 600+ attack techniques with sub-technique support
   - **Tactics**: 14 tactical objectives across the attack lifecycle
   - **Groups**: 130+ threat actor groups with attribution data
   - **Software**: 700+ malware families and tools
   - **Mitigations**: 40+ security controls and countermeasures
   - **Data Sources**: Detection and monitoring capabilities

🔒 **Enterprise Security Features**
   - **Soft Delete**: Complete audit trail with data preservation
   - **User Tracking**: All operations tracked to specific users
   - **Version Control**: Support for MITRE data versioning
   - **Multi-Domain**: Enterprise, Mobile, and ICS domain support

🚀 **Performance & Scalability**
   - **Optimized Database**: Strategic indexes for fast queries
   - **Full-Text Search**: Advanced search capabilities across all entities
   - **Relationship Mapping**: Efficient many-to-many associations
   - **Bulk Operations**: High-performance data synchronization

Key Components
--------------

Database Models
~~~~~~~~~~~~~~~

The MITRE integration includes 9 core database models:

.. list-table:: MITRE Database Models
   :header-rows: 1
   :widths: 25 75

   * - Model
     - Description
   * - :class:`MitreTechnique`
     - ATT&CK techniques with sub-technique support and platform mapping
   * - :class:`MitreTactic`
     - Tactical objectives and kill chain phases
   * - :class:`MitreGroup`
     - Threat actor groups with attribution and campaign data
   * - :class:`MitreSoftware`
     - Malware families, tools, and associated techniques
   * - :class:`MitreMitigation`
     - Security controls and countermeasures
   * - :class:`MitreDataSourceModel`
     - Detection capabilities and data collection methods
   * - :class:`MitreCampaign`
     - Threat campaigns and operations
   * - :class:`MitreMatrix`
     - ATT&CK matrix configurations and customizations
   * - :class:`MitreDataSync`
     - Synchronization tracking and audit logs

Pydantic Schemas
~~~~~~~~~~~~~~~~

Comprehensive validation schemas ensure data integrity:

- **Base Schemas**: Core field definitions and validation rules
- **Create Schemas**: Input validation for new records
- **Update Schemas**: Partial update validation with optional fields
- **Response Schemas**: API response formatting with computed fields
- **List Schemas**: Paginated list responses with metadata
- **Search Schemas**: Advanced search request and response handling

Supported Domains
-----------------

The integration supports all MITRE ATT&CK domains:

Enterprise Domain
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from app.db.models.mitre import MitreDomain
   
   # Enterprise techniques for traditional IT environments
   domain = MitreDomain.ENTERPRISE

**Coverage**: 600+ techniques across 14 tactics
**Focus**: Traditional IT infrastructure, cloud environments, and hybrid systems

Mobile Domain
~~~~~~~~~~~~~

.. code-block:: python

   # Mobile-specific attack techniques
   domain = MitreDomain.MOBILE

**Coverage**: 100+ mobile-specific techniques
**Focus**: iOS and Android attack vectors, mobile device management

ICS Domain
~~~~~~~~~~

.. code-block:: python

   # Industrial Control Systems techniques
   domain = MitreDomain.ICS

**Coverage**: 80+ ICS-specific techniques
**Focus**: SCADA, PLCs, and industrial network attacks

Data Relationships
------------------

The MITRE models include comprehensive relationship mapping:

Technique Relationships
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Techniques can be linked to multiple tactics
   technique = MitreTechnique.query.filter_by(technique_id="T1055").first()
   tactics = technique.tactics  # Associated tactics
   
   # Sub-techniques have parent relationships
   subtechnique = MitreTechnique.query.filter_by(technique_id="T1055.001").first()
   parent = subtechnique.parent_technique

Group Associations
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Groups use specific techniques and software
   group = MitreGroup.query.filter_by(group_id="G0016").first()
   techniques = group.techniques  # Techniques used by this group
   software = group.software      # Software used by this group

Mitigation Mappings
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Mitigations address specific techniques
   mitigation = MitreMitigation.query.filter_by(mitigation_id="M1013").first()
   techniques = mitigation.techniques  # Techniques this mitigation addresses

Quick Start Example
-------------------

Here's a quick example of working with MITRE data:

.. code-block:: python

   from app.db.models.mitre import MitreTechnique, MitreDomain
   from app.schemas.mitre import MitreTechniqueCreate
   
   # Create a new technique
   technique_data = MitreTechniqueCreate(
       technique_id="T9999",
       name="Custom Technique",
       domain=MitreDomain.ENTERPRISE,
       platforms=["Windows", "Linux"],
       data_sources=["Process monitoring"]
   )
   
   # Search for lateral movement techniques
   lateral_movement = MitreTechnique.query.filter(
       MitreTechnique.name.contains("lateral movement")
   ).all()
   
   # Get all techniques for a specific platform
   windows_techniques = MitreTechnique.query.filter(
       MitreTechnique.platforms.contains(["Windows"])
   ).all()

Next Steps
----------

- :doc:`quick-reference` - Quick reference for common operations
- :doc:`import-sync` - Learn how to import and synchronize MITRE data
- :doc:`schemas` - Detailed schema documentation and validation rules
- :doc:`updates` - Understanding the update and versioning system
- :doc:`configuration` - Configuration options and best practices
- :doc:`api-reference` - Complete API reference for MITRE endpoints
- :doc:`examples` - Practical examples and use cases

.. toctree::
   :maxdepth: 2
   :hidden:

   quick-reference
   import-sync
   schemas
   updates
   configuration
   api-reference
   examples
