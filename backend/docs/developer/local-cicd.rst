Local CI/CD Pipeline
====================

This guide covers the comprehensive local CI/CD pipeline for the Blast-Radius Security Tool, providing automated quality gates and deployment workflows without external dependencies.

Overview
--------

The Blast-Radius Security Tool includes a complete local CI/CD pipeline that provides:

- **Automated Quality Gates**: Code quality, type checking, and security scanning
- **Comprehensive Testing**: Unit, integration, and security tests with coverage reporting
- **Documentation Building**: Automated Sphinx documentation generation
- **Container Building**: Docker image creation and testing
- **Reporting**: Detailed reports for all pipeline stages
- **One-Command Execution**: Complete pipeline with a single command

Pipeline Architecture
--------------------

.. code-block:: text

   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │   🧹 Clean  │───▶│  🔍 Lint    │───▶│ 🎯 Type     │───▶│ 🔒 Security │
   │             │    │             │    │   Check     │    │   Scan      │
   └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
           │                   │                   │                   │
           ▼                   ▼                   ▼                   ▼
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │ 🧪 Tests    │───▶│ 📚 Docs     │───▶│ 🐳 Docker   │───▶│ ✅ Success  │
   │   95%+      │    │   Build     │    │   Build     │    │   Report    │
   └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘

Quick Start
-----------

**Complete CI/CD Pipeline**:

.. code-block:: bash

   # Run the complete pipeline
   cd backend
   make ci-full

**Individual Pipeline Stages**:

.. code-block:: bash

   # Setup development environment
   make install-dev
   
   # Code quality checks
   make quality-check
   
   # Run tests with coverage
   make test-coverage
   
   # Security scanning
   make security-check
   
   # Build documentation
   make docs-build
   
   # Build Docker image
   make docker-build

Pipeline Stages
---------------

1. Clean Environment
~~~~~~~~~~~~~~~~~~~~

**Purpose**: Prepare a clean environment for the pipeline

**Commands**:

.. code-block:: bash

   make clean

**What it does**:
- Removes Python cache files (``__pycache__``, ``*.pyc``)
- Cleans build artifacts (``build/``, ``dist/``, ``*.egg-info/``)
- Removes test artifacts (``.pytest_cache/``, ``.coverage``)
- Clears documentation build files (``docs/_build/``)
- Removes temporary files and caches

2. Code Quality (Linting)
~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Ensure code follows style guidelines and best practices

**Commands**:

.. code-block:: bash

   make lint
   # or
   make ci-quality

**Tools Used**:
- **Ruff**: Modern Python linter with 30+ rule categories
- **Output**: JSON and GitHub-style reports

**What it checks**:
- Code style (PEP 8 compliance)
- Import organization
- Unused variables and imports
- Code complexity
- Security patterns
- Performance anti-patterns

**Example Output**:

.. code-block:: text

   ✅ Code Quality: PASSED
   📊 Ruff: 0 errors, 0 warnings
   📁 Files checked: 45
   🔧 Auto-fixable issues: 0

3. Type Checking
~~~~~~~~~~~~~~~~

**Purpose**: Verify type annotations and catch type-related errors

**Commands**:

.. code-block:: bash

   make type-check

**Tools Used**:
- **MyPy**: Static type checker with strict configuration
- **Output**: JUnit XML and HTML reports

**What it checks**:
- Type annotation correctness
- Function signature compatibility
- Variable type consistency
- Return type validation
- Generic type usage

**Configuration**:

.. code-block:: ini

   [tool.mypy]
   python_version = "3.11"
   strict = true
   disallow_untyped_defs = true
   no_implicit_optional = true
   warn_return_any = true

4. Security Scanning
~~~~~~~~~~~~~~~~~~~~

**Purpose**: Identify security vulnerabilities and unsafe patterns

**Commands**:

.. code-block:: bash

   make security-check
   # or
   make ci-security

**Tools Used**:
- **Bandit**: Security linter for Python code
- **pip-audit**: Dependency vulnerability scanner
- **Output**: JSON, TXT, and console reports

**What it scans**:
- SQL injection vulnerabilities
- Hardcoded passwords and secrets
- Insecure random number generation
- Unsafe deserialization
- Command injection risks
- Dependency vulnerabilities

**Example Output**:

.. code-block:: text

   🔒 Security Scan: PASSED
   🛡️  Bandit: No issues found
   📦 Dependencies: 0 vulnerabilities
   🔍 Files scanned: 45

5. Testing with Coverage
~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Run comprehensive test suite with coverage analysis

**Commands**:

.. code-block:: bash

   make test-coverage
   # or
   make ci-test

**Tools Used**:
- **Pytest**: Test framework with plugins
- **Coverage.py**: Code coverage measurement
- **Output**: XML, HTML, and terminal reports

**Test Categories**:
- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **Security Tests**: Security feature validation
- **API Tests**: Endpoint testing with test client

**Coverage Requirements**:
- **Minimum**: 95% line coverage
- **Branches**: 90% branch coverage
- **Missing**: Detailed report of uncovered lines

**Example Output**:

.. code-block:: text

   🧪 Tests: PASSED
   📊 Coverage: 97.2% (target: 95%)
   ✅ Unit Tests: 156 passed
   ✅ Integration Tests: 45 passed
   ✅ Security Tests: 23 passed

6. Documentation Building
~~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Generate comprehensive documentation

**Commands**:

.. code-block:: bash

   make docs-build

**Tools Used**:
- **Sphinx**: Documentation generator
- **Read the Docs Theme**: Professional styling
- **Custom CSS**: Enhanced appearance

**What it builds**:
- User guides and tutorials
- API reference documentation
- Developer guides
- Configuration documentation
- Interactive examples

7. Docker Image Building
~~~~~~~~~~~~~~~~~~~~~~~~

**Purpose**: Create production-ready container images

**Commands**:

.. code-block:: bash

   make docker-build

**Features**:
- **Multi-stage build**: Optimized image size
- **Security**: Non-root user, minimal attack surface
- **Health checks**: Container health monitoring
- **Caching**: Efficient layer caching

**Image Testing**:

.. code-block:: bash

   # Test the built image
   docker run --rm blast-radius-backend:latest python -c "import app; print('✅ Import successful')"

Reporting and Artifacts
-----------------------

Report Generation
~~~~~~~~~~~~~~~~~

The pipeline generates comprehensive reports in the ``reports/`` directory:

.. code-block:: text

   reports/
   ├── coverage.xml              # Coverage XML report
   ├── coverage.html             # Coverage HTML report
   ├── test-results.xml          # JUnit test results
   ├── test-report.html          # HTML test report
   ├── ruff-results.json         # Linting results
   ├── mypy-results.xml          # Type checking results
   ├── mypy-html/                # Type checking HTML report
   ├── bandit-results.json       # Security scan results
   └── bandit-report.txt         # Security scan text report

Viewing Reports
~~~~~~~~~~~~~~~

**Coverage Report**:

.. code-block:: bash

   # Open HTML coverage report
   open reports/coverage.html
   # or
   python -m http.server 8080 -d reports/

**Test Report**:

.. code-block:: bash

   # Open HTML test report
   open reports/test-report.html

**Type Checking Report**:

.. code-block:: bash

   # Open MyPy HTML report
   open reports/mypy-html/index.html

CI/CD Configuration
-------------------

Makefile Configuration
~~~~~~~~~~~~~~~~~~~~~~

The CI/CD pipeline is configured through the comprehensive Makefile:

.. code-block:: makefile

   # Complete CI/CD pipeline
   ci-full: clean quality-check test-coverage security-check docs-build docker-build
   	@echo "🎉 Full CI/CD pipeline completed successfully!"

   # Individual stages
   ci-quality: lint type-check
   ci-security: security-check
   ci-test: test-coverage
   ci-reports: coverage-badge quality-reports

Quality Gates
~~~~~~~~~~~~~

The pipeline enforces strict quality gates:

.. list-table::
   :header-rows: 1
   :widths: 30 20 50

   * - Stage
     - Threshold
     - Action on Failure
   * - Code Quality
     - 0 errors
     - Pipeline stops
   * - Type Checking
     - 0 type errors
     - Pipeline stops
   * - Security Scan
     - 0 high/critical
     - Pipeline stops
   * - Test Coverage
     - 95% minimum
     - Pipeline stops
   * - Documentation
     - Build success
     - Warning only
   * - Docker Build
     - Build success
     - Pipeline stops

Pre-commit Integration
~~~~~~~~~~~~~~~~~~~~~~

The pipeline integrates with pre-commit hooks:

.. code-block:: bash

   # Install pre-commit hooks
   make install-dev
   
   # Run pre-commit manually
   make pre-commit

**Pre-commit Configuration** (``.pre-commit-config.yaml``):

.. code-block:: yaml

   repos:
     - repo: https://github.com/astral-sh/ruff-pre-commit
       hooks:
         - id: ruff
           args: [--fix, --exit-non-zero-on-fix]
         - id: ruff-format
     
     - repo: https://github.com/pre-commit/mirrors-mypy
       hooks:
         - id: mypy
           args: [--strict]

Development Workflow
--------------------

Daily Development
~~~~~~~~~~~~~~~~~

**Recommended workflow for daily development**:

.. code-block:: bash

   # 1. Start development session
   make run-dev
   
   # 2. Make code changes
   # ... edit files ...
   
   # 3. Format and check code
   make format
   make lint
   
   # 4. Run tests
   make test
   
   # 5. Before committing
   make quality-check

Feature Development
~~~~~~~~~~~~~~~~~~~

**Workflow for new features**:

.. code-block:: bash

   # 1. Create feature branch
   git checkout -b feature/new-feature
   
   # 2. Develop feature with tests
   # ... implement feature ...
   # ... write tests ...
   
   # 3. Run full pipeline
   make ci-full
   
   # 4. Commit and push
   git add .
   git commit -m "feat: implement new feature"
   git push origin feature/new-feature

Release Preparation
~~~~~~~~~~~~~~~~~~~

**Workflow for releases**:

.. code-block:: bash

   # 1. Run complete pipeline
   make ci-full
   
   # 2. Build and test Docker image
   make docker-build
   make docker-run
   
   # 3. Generate documentation
   make docs-build
   make docs-serve
   
   # 4. Create release
   git tag v1.0.0
   git push origin v1.0.0

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Pipeline Failures**:

.. code-block:: bash

   # Check specific stage
   make lint          # Check linting issues
   make type-check    # Check type errors
   make test -v       # Verbose test output

**Coverage Issues**:

.. code-block:: bash

   # Generate detailed coverage report
   make test-coverage
   open reports/coverage.html

**Docker Build Issues**:

.. code-block:: bash

   # Build with verbose output
   docker build --no-cache -t blast-radius-backend:debug .

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~

**Speed up pipeline execution**:

.. code-block:: bash

   # Parallel test execution
   pytest -n auto
   
   # Skip slow tests during development
   pytest -m "not slow"
   
   # Use cached Docker layers
   docker build --cache-from blast-radius-backend:latest .

Monitoring and Metrics
----------------------

Pipeline Metrics
~~~~~~~~~~~~~~~~~

The pipeline tracks key metrics:

- **Execution Time**: Total and per-stage timing
- **Test Coverage**: Line and branch coverage percentages
- **Code Quality**: Number of issues found and fixed
- **Security**: Vulnerabilities detected and resolved
- **Success Rate**: Pipeline success/failure rates

**Example Metrics Output**:

.. code-block:: text

   📊 Pipeline Metrics:
   ⏱️  Total Time: 2m 34s
   🧪 Test Coverage: 97.2%
   🔍 Quality Issues: 0
   🔒 Security Issues: 0
   ✅ Success Rate: 100%

Continuous Improvement
~~~~~~~~~~~~~~~~~~~~~~

**Regular maintenance tasks**:

.. code-block:: bash

   # Update dependencies
   make update-deps
   
   # Check for outdated packages
   make check-deps
   
   # Security audit
   make security-audit

Best Practices
--------------

Pipeline Best Practices
~~~~~~~~~~~~~~~~~~~~~~~

1. **Run pipeline before commits**: Always run ``make ci-full`` before committing
2. **Fix issues immediately**: Don't accumulate technical debt
3. **Monitor coverage**: Maintain 95%+ test coverage
4. **Update dependencies**: Regular security updates
5. **Document changes**: Update documentation with code changes

Development Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Write tests first**: Test-driven development approach
2. **Use type hints**: Comprehensive type annotations
3. **Follow conventions**: Consistent code style and patterns
4. **Security mindset**: Consider security implications
5. **Performance awareness**: Monitor and optimize performance

For more information about specific tools and configurations, see the individual tool documentation and configuration files in the repository.
