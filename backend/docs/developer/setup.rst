Developer Setup Guide
======================

This guide will help you set up a complete development environment for the Blast-Radius Security Tool backend.

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

- **Operating System**: Linux, macOS, or Windows with WSL2
- **Python**: 3.11 or higher
- **Git**: Latest version
- **PostgreSQL**: 14.0 or higher
- **Redis**: 6.0 or higher
- **Memory**: Minimum 8GB RAM recommended
- **Storage**: At least 10GB free space

Development Tools
~~~~~~~~~~~~~~~~~

**Required Tools**:
- **Code Editor**: VS Code, PyCharm, or similar with Python support
- **Terminal**: Bash, Zsh, or PowerShell
- **Database Client**: pgAdmin, DBeaver, or psql command line
- **API Client**: Postman, Insomnia, or curl

**Recommended Tools**:
- **Docker**: For containerized development
- **Make**: For using the provided Makefile commands
- **Git GUI**: GitKraken, SourceTree, or VS Code Git integration

Environment Setup
-----------------

1. Clone the Repository
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius/backend

   # Verify you're on the correct branch
   git branch -a
   git checkout feature/auth-acl  # or your development branch

2. Python Environment
~~~~~~~~~~~~~~~~~~~~~

**Using venv (Recommended)**:

.. code-block:: bash

   # Create virtual environment
   python3.11 -m venv venv
   
   # Activate virtual environment
   # On Linux/macOS:
   source venv/bin/activate
   
   # On Windows:
   venv\Scripts\activate
   
   # Verify Python version
   python --version  # Should show Python 3.11+

**Using pyenv (Alternative)**:

.. code-block:: bash

   # Install Python 3.11 if not available
   pyenv install 3.11.7
   pyenv local 3.11.7
   
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate

3. Install Dependencies
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Upgrade pip and install build tools
   pip install --upgrade pip setuptools wheel
   
   # Install development dependencies
   make install-dev
   
   # Or manually:
   pip install -r requirements-dev.txt
   pip install -e .

4. Database Setup
~~~~~~~~~~~~~~~~~

**PostgreSQL Installation**:

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   
   # macOS with Homebrew
   brew install postgresql
   brew services start postgresql
   
   # Windows
   # Download from https://www.postgresql.org/download/windows/

**Create Development Database**:

.. code-block:: bash

   # Connect to PostgreSQL as superuser
   sudo -u postgres psql
   
   # Create database and user
   CREATE USER blast_radius WITH PASSWORD 'dev_password';
   CREATE DATABASE blast_radius_dev OWNER blast_radius;
   GRANT ALL PRIVILEGES ON DATABASE blast_radius_dev TO blast_radius;
   
   # Exit PostgreSQL
   \q

**Test Database Connection**:

.. code-block:: bash

   # Test connection
   psql -h localhost -U blast_radius -d blast_radius_dev
   
   # Should prompt for password and connect successfully

5. Redis Setup
~~~~~~~~~~~~~~

**Redis Installation**:

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt install redis-server
   sudo systemctl start redis-server
   sudo systemctl enable redis-server
   
   # macOS with Homebrew
   brew install redis
   brew services start redis
   
   # Windows
   # Use Docker or WSL2 with Linux installation

**Test Redis Connection**:

.. code-block:: bash

   # Test Redis
   redis-cli ping
   # Should return: PONG

6. Environment Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Create Environment File**:

.. code-block:: bash

   # Copy example environment file
   cp .env.example .env
   
   # Edit with your settings
   nano .env  # or your preferred editor

**Development Environment Variables**:

.. code-block:: bash

   # .env file for development
   ENVIRONMENT=development
   DEBUG=true
   LOG_LEVEL=debug
   
   # Database
   DATABASE_URL=postgresql://blast_radius:dev_password@localhost:5432/blast_radius_dev
   
   # Redis
   REDIS_URL=redis://localhost:6379/0
   
   # Security (generate with: openssl rand -hex 32)
   SECRET_KEY=your-development-secret-key-here
   
   # CORS for frontend development
   CORS_ORIGINS=["http://localhost:3000"]
   
   # Email (for development - use console backend)
   EMAIL_BACKEND=console
   
   # Disable some security features for development
   SESSION_COOKIE_SECURE=false
   MFA_REQUIRE_FOR_ALL=false

7. Database Migration
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run database migrations
   make migrate-upgrade
   
   # Or manually:
   alembic upgrade head
   
   # Verify tables were created
   psql -h localhost -U blast_radius -d blast_radius_dev -c "\dt"

8. Create Development Data
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create initial admin user and test data
   python scripts/create_dev_data.py
   
   # Or manually create admin user
   python -c "
   from app.db.session import SessionLocal
   from app.services.user_service import UserService
   from app.schemas.user import UserCreate
   
   db = SessionLocal()
   user_service = UserService(db)
   
   admin_data = UserCreate(
       username='admin',
       email='<EMAIL>',
       full_name='Development Admin',
       password='AdminPassword123!',
       roles=['admin']
   )
   
   user = user_service.create_user(admin_data)
   print(f'Created admin user: {user.username}')
   db.close()
   "

9. Verify Installation
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run the development server
   make run-dev
   
   # Or manually:
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   
   # Test the API
   curl http://localhost:8000/health
   
   # Should return:
   # {"status": "healthy", "version": "0.0.1"}

Development Workflow
--------------------

Daily Development Commands
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Start development server with auto-reload
   make run-dev
   
   # Run tests
   make test
   
   # Run code quality checks
   make quality-check
   
   # Format code
   make format
   
   # Check types
   make type-check
   
   # Run security scan
   make security-check

Pre-commit Setup
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install pre-commit hooks
   make install-dev  # Already includes pre-commit installation
   
   # Or manually:
   pre-commit install
   
   # Test pre-commit hooks
   pre-commit run --all-files

Code Quality Tools
~~~~~~~~~~~~~~~~~~

**Ruff (Linting and Formatting)**:

.. code-block:: bash

   # Check code quality
   ruff check app/ tests/
   
   # Fix auto-fixable issues
   ruff check --fix app/ tests/
   
   # Format code
   ruff format app/ tests/

**MyPy (Type Checking)**:

.. code-block:: bash

   # Run type checking
   mypy app/
   
   # Check specific file
   mypy app/main.py

**Bandit (Security Scanning)**:

.. code-block:: bash

   # Run security scan
   bandit -r app/
   
   # Generate report
   bandit -r app/ -f json -o security-report.json

Testing
-------

Running Tests
~~~~~~~~~~~~~

.. code-block:: bash

   # Run all tests
   make test
   
   # Run specific test categories
   make test-unit
   make test-integration
   make test-security
   
   # Run with coverage
   make test-coverage
   
   # Run specific test file
   pytest tests/test_auth.py -v
   
   # Run specific test function
   pytest tests/test_auth.py::test_login_success -v

Test Configuration
~~~~~~~~~~~~~~~~~~

Tests use a separate test database to avoid affecting development data:

.. code-block:: bash

   # Create test database
   sudo -u postgres createdb blast_radius_test
   
   # Set test environment variable
   export DATABASE_URL_TEST=postgresql://blast_radius:dev_password@localhost:5432/blast_radius_test

Writing Tests
~~~~~~~~~~~~~

**Test Structure**:

.. code-block:: python

   # tests/test_example.py
   import pytest
   from fastapi.testclient import TestClient
   from app.main import app
   from app.db.session import get_db
   from tests.conftest import override_get_db
   
   app.dependency_overrides[get_db] = override_get_db
   client = TestClient(app)
   
   def test_example_endpoint():
       response = client.get("/api/v1/example")
       assert response.status_code == 200
       assert response.json()["status"] == "success"

**Test Fixtures**:

.. code-block:: python

   # tests/conftest.py
   import pytest
   from sqlalchemy import create_engine
   from sqlalchemy.orm import sessionmaker
   from app.db.base import Base
   
   @pytest.fixture(scope="session")
   def test_db():
       engine = create_engine("sqlite:///./test.db")
       Base.metadata.create_all(bind=engine)
       yield engine
       Base.metadata.drop_all(bind=engine)

Database Development
--------------------

Creating Migrations
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create new migration
   make migrate MSG="Add user preferences table"
   
   # Or manually:
   alembic revision --autogenerate -m "Add user preferences table"
   
   # Review the generated migration file
   # Edit if necessary before applying

Applying Migrations
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Apply migrations
   make migrate-upgrade
   
   # Or manually:
   alembic upgrade head
   
   # Rollback migration
   alembic downgrade -1

Database Utilities
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Reset development database
   make db-reset
   
   # Seed with test data
   python scripts/seed_data.py
   
   # Backup development database
   pg_dump blast_radius_dev > backup.sql
   
   # Restore from backup
   psql blast_radius_dev < backup.sql

API Development
---------------

Adding New Endpoints
~~~~~~~~~~~~~~~~~~~~

1. **Create Pydantic Schema** (``app/schemas/``):

.. code-block:: python

   # app/schemas/example.py
   from pydantic import BaseModel
   from typing import Optional
   from datetime import datetime
   
   class ExampleBase(BaseModel):
       name: str
       description: Optional[str] = None
   
   class ExampleCreate(ExampleBase):
       pass
   
   class ExampleUpdate(ExampleBase):
       name: Optional[str] = None
   
   class ExampleResponse(ExampleBase):
       id: int
       created_at: datetime
       
       class Config:
           from_attributes = True

2. **Create Database Model** (``app/db/models/``):

.. code-block:: python

   # app/db/models/example.py
   from sqlalchemy import Column, Integer, String, DateTime
   from sqlalchemy.sql import func
   from app.db.base_class import Base
   
   class Example(Base):
       __tablename__ = "examples"
       
       id = Column(Integer, primary_key=True, index=True)
       name = Column(String(100), nullable=False, index=True)
       description = Column(String(500))
       created_at = Column(DateTime(timezone=True), server_default=func.now())

3. **Create Service Layer** (``app/services/``):

.. code-block:: python

   # app/services/example_service.py
   from typing import List, Optional
   from sqlalchemy.orm import Session
   from app.db.models.example import Example
   from app.schemas.example import ExampleCreate, ExampleUpdate
   
   class ExampleService:
       def __init__(self, db: Session):
           self.db = db
       
       def create_example(self, example_data: ExampleCreate) -> Example:
           example = Example(**example_data.dict())
           self.db.add(example)
           self.db.commit()
           self.db.refresh(example)
           return example
       
       def get_examples(self, skip: int = 0, limit: int = 100) -> List[Example]:
           return self.db.query(Example).offset(skip).limit(limit).all()

4. **Create API Router** (``app/api/v1/``):

.. code-block:: python

   # app/api/v1/examples.py
   from typing import List
   from fastapi import APIRouter, Depends, HTTPException
   from sqlalchemy.orm import Session
   from app.api.deps import get_db, get_current_user
   from app.schemas.example import ExampleCreate, ExampleResponse
   from app.services.example_service import ExampleService
   
   router = APIRouter()
   
   @router.post("/", response_model=ExampleResponse)
   def create_example(
       example_data: ExampleCreate,
       db: Session = Depends(get_db),
       current_user = Depends(get_current_user)
   ):
       service = ExampleService(db)
       return service.create_example(example_data)
   
   @router.get("/", response_model=List[ExampleResponse])
   def list_examples(
       skip: int = 0,
       limit: int = 100,
       db: Session = Depends(get_db)
   ):
       service = ExampleService(db)
       return service.get_examples(skip=skip, limit=limit)

5. **Register Router** (``app/api/v1/__init__.py``):

.. code-block:: python

   from fastapi import APIRouter
   from app.api.v1 import auth, users, examples
   
   api_router = APIRouter()
   api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
   api_router.include_router(users.router, prefix="/users", tags=["users"])
   api_router.include_router(examples.router, prefix="/examples", tags=["examples"])

Documentation
-------------

API Documentation
~~~~~~~~~~~~~~~~~

The API documentation is automatically generated from your code:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

**Enhancing Documentation**:

.. code-block:: python

   @router.post(
       "/",
       response_model=ExampleResponse,
       summary="Create a new example",
       description="Create a new example with the provided data",
       response_description="The created example"
   )
   def create_example(
       example_data: ExampleCreate,
       db: Session = Depends(get_db)
   ):
       """
       Create a new example.
       
       - **name**: The example name (required)
       - **description**: Optional description
       """
       pass

Sphinx Documentation
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Build documentation
   make docs-build
   
   # Serve documentation locally
   make docs-serve
   
   # Clean documentation build
   make docs-clean

Debugging
---------

VS Code Setup
~~~~~~~~~~~~~

Create ``.vscode/launch.json``:

.. code-block:: json

   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "FastAPI Debug",
         "type": "python",
         "request": "launch",
         "program": "${workspaceFolder}/venv/bin/uvicorn",
         "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
         "console": "integratedTerminal",
         "envFile": "${workspaceFolder}/.env",
         "cwd": "${workspaceFolder}"
       }
     ]
   }

PyCharm Setup
~~~~~~~~~~~~~

1. Open the project in PyCharm
2. Configure Python interpreter to use your virtual environment
3. Set up run configuration:
   - Script path: ``uvicorn``
   - Parameters: ``app.main:app --reload --host 0.0.0.0 --port 8000``
   - Environment variables: Load from ``.env`` file

Debugging Tips
~~~~~~~~~~~~~~

.. code-block:: python

   # Add breakpoints in code
   import pdb; pdb.set_trace()
   
   # Or use ipdb for better interface
   import ipdb; ipdb.set_trace()
   
   # Log debugging information
   import logging
   logger = logging.getLogger(__name__)
   logger.debug("Debug information here")

Performance Profiling
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Profile application performance
   make profile
   
   # Or manually:
   python -m cProfile -o profile.stats app/main.py
   
   # Analyze profile
   python -c "
   import pstats
   p = pstats.Stats('profile.stats')
   p.sort_stats('cumulative').print_stats(20)
   "

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Import Errors**:
- Ensure virtual environment is activated
- Verify all dependencies are installed
- Check PYTHONPATH includes project root

**Database Connection Issues**:
- Verify PostgreSQL is running
- Check database credentials in .env
- Ensure database exists and user has permissions

**Redis Connection Issues**:
- Verify Redis is running: ``redis-cli ping``
- Check Redis URL in .env file
- Ensure Redis is accessible from your application

**Port Already in Use**:
- Check what's using port 8000: ``lsof -i :8000``
- Kill the process or use a different port
- Update PORT in .env if using different port

**Permission Errors**:
- Ensure proper file permissions
- Check virtual environment ownership
- Verify database user permissions

Getting Help
~~~~~~~~~~~~

1. **Check the logs** for detailed error messages
2. **Review the documentation** for configuration options
3. **Search existing issues** on GitHub
4. **Ask for help** in team chat or create an issue
5. **Use debugging tools** to trace the problem

Next Steps
----------

After completing the setup:

1. **Explore the codebase** to understand the architecture
2. **Run the test suite** to ensure everything works
3. **Try the API endpoints** using the interactive documentation
4. **Read the contributing guide** for development standards
5. **Start working on your first feature** or bug fix

For more advanced topics, see:
- :doc:`architecture` - Understanding the system design
- :doc:`testing` - Comprehensive testing guide
- :doc:`contributing` - Development standards and workflow
