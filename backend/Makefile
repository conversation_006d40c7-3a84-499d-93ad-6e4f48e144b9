# Blast-Radius Backend Makefile
# Comprehensive development and deployment automation

.PHONY: help install install-dev clean test test-unit test-integration test-security test-coverage
.PHONY: lint format type-check security-check quality-check pre-commit
.PHONY: run run-dev run-prod migrate migrate-upgrade migrate-downgrade
.PHONY: docs docs-build docs-serve docs-clean
.PHONY: docker docker-build docker-run docker-clean
.PHONY: deploy deploy-staging deploy-prod
.PHONY: backup restore monitoring logs

# Default target
.DEFAULT_GOAL := help

# Variables
PYTHON := python3.11
PIP := pip
VENV := venv
VENV_BIN := $(VENV)/bin
PYTHON_VENV := $(VENV_BIN)/python
PIP_VENV := $(VENV_BIN)/pip
PROJECT_NAME := blast-radius-backend
VERSION := 0.0.1

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

help: ## Show this help message
	@echo "$(CYAN)Blast-Radius Backend Development Commands$(RESET)"
	@echo ""
	@echo "$(GREEN)Setup Commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(install|clean|setup)"
	@echo ""
	@echo "$(GREEN)Development Commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(run|dev|test|lint|format)"
	@echo ""
	@echo "$(GREEN)Quality Commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(quality|security|type|pre-commit)"
	@echo ""
	@echo "$(GREEN)Database Commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(migrate|backup|restore)"
	@echo ""
	@echo "$(GREEN)Documentation Commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(docs)"
	@echo ""
	@echo "$(GREEN)Deployment Commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(docker|deploy)"

# Setup Commands
install: ## Install production dependencies
	@echo "$(GREEN)Installing production dependencies...$(RESET)"
	$(PIP) install -r requirements.txt

install-dev: $(VENV) ## Install development dependencies
	@echo "$(GREEN)Installing development dependencies...$(RESET)"
	$(PIP_VENV) install -r requirements-dev.txt
	$(PIP_VENV) install -e .
	$(VENV_BIN)/pre-commit install

$(VENV): ## Create virtual environment
	@echo "$(GREEN)Creating virtual environment...$(RESET)"
	$(PYTHON) -m venv $(VENV)
	$(PIP_VENV) install --upgrade pip setuptools wheel

clean: ## Clean up temporary files and caches
	@echo "$(YELLOW)Cleaning up...$(RESET)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/ dist/ .coverage htmlcov/ .pytest_cache/ .mypy_cache/ .ruff_cache/
	rm -rf docs/_build/

# Development Commands
run: ## Run the application in development mode
	@echo "$(GREEN)Starting development server...$(RESET)"
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

run-dev: $(VENV) ## Run with development configuration
	@echo "$(GREEN)Starting development server with debug mode...$(RESET)"
	$(PYTHON_VENV) -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-level debug

run-prod: ## Run in production mode
	@echo "$(GREEN)Starting production server...$(RESET)"
	uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

# Testing Commands
test: test-unit test-integration ## Run all tests

test-unit: $(VENV) ## Run unit tests
	@echo "$(GREEN)Running unit tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/unit/ -v --tb=short

test-integration: $(VENV) ## Run integration tests
	@echo "$(GREEN)Running integration tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/integration/ -v --tb=short

test-security: $(VENV) ## Run security tests
	@echo "$(GREEN)Running security tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/security/ -v --tb=short

test-coverage: $(VENV) ## Run tests with coverage report
	@echo "$(GREEN)Running tests with coverage...$(RESET)"
	$(PYTHON_VENV) -m pytest --cov=app --cov-report=html --cov-report=term-missing --cov-fail-under=95

test-e2e: $(VENV) ## Run E2E tests with Playwright
	@echo "$(GREEN)Running E2E tests...$(RESET)"
	@echo "$(CYAN)Installing Playwright browsers...$(RESET)"
	$(PYTHON_VENV) -m playwright install chromium
	@mkdir -p tests/e2e/reports tests/e2e/screenshots tests/e2e/videos
	@echo "$(CYAN)Running E2E test suite...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/e2e/ -v --html=tests/e2e/reports/e2e-report.html --self-contained-html

test-e2e-headed: $(VENV) ## Run E2E tests with browser UI visible
	@echo "$(GREEN)Running E2E tests with browser UI...$(RESET)"
	@mkdir -p tests/e2e/reports tests/e2e/screenshots tests/e2e/videos
	HEADLESS=false $(PYTHON_VENV) -m pytest tests/e2e/ -v --html=tests/e2e/reports/e2e-report.html --self-contained-html

test-e2e-record: $(VENV) ## Run E2E tests with video recording
	@echo "$(GREEN)Running E2E tests with video recording...$(RESET)"
	@mkdir -p tests/e2e/reports tests/e2e/screenshots tests/e2e/videos
	RECORD_VIDEO=true $(PYTHON_VENV) -m pytest tests/e2e/ -v --html=tests/e2e/reports/e2e-report.html --self-contained-html

test-bdd: $(VENV) ## Run BDD tests with Behave
	@echo "$(GREEN)Running BDD tests...$(RESET)"
	@mkdir -p tests/bdd/reports/html tests/bdd/reports/junit tests/bdd/reports/screenshots
	cd tests/bdd && $(PYTHON_VENV) -m behave --format=pretty --format=html --outdir=reports/html --junit --junit-directory=reports/junit

test-bdd-tags: $(VENV) ## Run BDD tests with specific tags (usage: make test-bdd-tags TAGS=@authentication)
	@echo "$(GREEN)Running BDD tests with tags: $(TAGS)$(RESET)"
	@mkdir -p tests/bdd/reports/html tests/bdd/reports/junit tests/bdd/reports/screenshots
	cd tests/bdd && $(PYTHON_VENV) -m behave --tags=$(TAGS) --format=pretty --format=html --outdir=reports/html --junit --junit-directory=reports/junit

test-bdd-dry: $(VENV) ## Run BDD tests in dry-run mode (check scenarios without execution)
	@echo "$(GREEN)Running BDD dry-run...$(RESET)"
	cd tests/bdd && $(PYTHON_VENV) -m behave --dry-run --format=pretty

test-all: test test-security test-e2e test-bdd ## Run all test suites (unit, integration, security, E2E, BDD)
	@echo "$(GREEN)🎉 All test suites completed!$(RESET)"
	@echo "$(CYAN)📊 Test Summary:$(RESET)"
	@echo "  ✅ Unit Tests: PASSED"
	@echo "  ✅ Integration Tests: PASSED"
	@echo "  ✅ Security Tests: PASSED"
	@echo "  ✅ E2E Tests: PASSED"
	@echo "  ✅ BDD Tests: PASSED"

test-comprehensive: $(VENV) ## Run comprehensive test suite with detailed reporting
	@echo "$(GREEN)Running comprehensive test suite...$(RESET)"
	$(PYTHON_VENV) scripts/run_all_tests.py --verbose

test-comprehensive-headed: $(VENV) ## Run comprehensive test suite with browser UI visible
	@echo "$(GREEN)Running comprehensive test suite with browser UI...$(RESET)"
	$(PYTHON_VENV) scripts/run_all_tests.py --verbose --headed

test-quick: $(VENV) ## Run quick test suite (skip E2E and BDD)
	@echo "$(GREEN)Running quick test suite...$(RESET)"
	$(PYTHON_VENV) scripts/run_all_tests.py --skip-e2e --skip-bdd

test-contract: $(VENV) ## Run contract tests for API compatibility
	@echo "$(GREEN)Running contract tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/contract/ -v -m contract

test-property: $(VENV) ## Run property-based tests with Hypothesis
	@echo "$(GREEN)Running property-based tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/property/ -v --hypothesis-show-statistics

test-architecture: $(VENV) ## Run architecture constraint tests
	@echo "$(GREEN)Running architecture tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/architecture/ -v -m architecture

test-performance: $(VENV) ## Run performance and load tests
	@echo "$(GREEN)Running performance tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/performance/ -v -m performance --tb=short

test-chaos: $(VENV) ## Run chaos engineering tests
	@echo "$(GREEN)Running chaos engineering tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/chaos/ -v -m chaos --tb=short

test-smoke: $(VENV) ## Run smoke tests for critical functionality
	@echo "$(GREEN)Running smoke tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/ -v -m smoke

test-regression: $(VENV) ## Run regression tests for bug prevention
	@echo "$(GREEN)Running regression tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/ -v -m regression

test-advanced: test-contract test-property test-architecture test-performance ## Run all advanced test types
	@echo "$(GREEN)🎉 All advanced tests completed!$(RESET)"

test-resilience: test-chaos test-performance ## Run resilience and stress tests
	@echo "$(GREEN)🛡️ Resilience testing completed!$(RESET)"

test-mutation: $(VENV) ## Run mutation tests to validate test quality
	@echo "$(GREEN)Running mutation tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/mutation/ -v -m mutation --tb=short

test-visual: $(VENV) ## Run visual regression tests
	@echo "$(GREEN)Running visual regression tests...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/visual/ -v -m visual --tb=short

test-visual-baseline: $(VENV) ## Save visual regression baselines
	@echo "$(GREEN)Saving visual regression baselines...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/visual/ -v -m visual --save-baseline

test-metrics: $(VENV) ## Run test metrics collection and analysis
	@echo "$(GREEN)Running test metrics analysis...$(RESET)"
	$(PYTHON_VENV) -m pytest tests/metrics/ -v -m metrics --tb=short

test-quality: test-mutation test-visual test-metrics ## Run test quality validation suite
	@echo "$(GREEN)🎯 Test quality validation completed!$(RESET)"

test-complete: test-all test-advanced test-resilience test-quality ## Run absolutely all test types
	@echo "$(GREEN)🎉 Complete test suite finished!$(RESET)"
	@echo "$(CYAN)📊 All 15 test types completed:$(RESET)"
	@echo "  ✅ Unit Tests"
	@echo "  ✅ Integration Tests"
	@echo "  ✅ Security Tests"
	@echo "  ✅ E2E Tests"
	@echo "  ✅ BDD Tests"
	@echo "  ✅ Contract Tests"
	@echo "  ✅ Property Tests"
	@echo "  ✅ Architecture Tests"
	@echo "  ✅ Performance Tests"
	@echo "  ✅ Chaos Tests"
	@echo "  ✅ Smoke Tests"
	@echo "  ✅ Regression Tests"
	@echo "  ✅ Mutation Tests"
	@echo "  ✅ Visual Tests"
	@echo "  ✅ Metrics Tests"

# Code Quality Commands
lint: $(VENV) ## Run linting with ruff
	@echo "$(GREEN)Running linting...$(RESET)"
	$(VENV_BIN)/ruff check app/ tests/

format: $(VENV) ## Format code with ruff
	@echo "$(GREEN)Formatting code...$(RESET)"
	$(VENV_BIN)/ruff format app/ tests/
	$(VENV_BIN)/ruff check --fix app/ tests/

type-check: $(VENV) ## Run type checking with mypy
	@echo "$(GREEN)Running type checking...$(RESET)"
	$(VENV_BIN)/mypy app/

security-check: $(VENV) ## Run security checks with bandit
	@echo "$(GREEN)Running security checks...$(RESET)"
	$(VENV_BIN)/bandit -r app/ -f json

quality-check: lint type-check security-check ## Run all quality checks

pre-commit: $(VENV) ## Run pre-commit hooks
	@echo "$(GREEN)Running pre-commit hooks...$(RESET)"
	$(VENV_BIN)/pre-commit run --all-files

# Database Commands
migrate: ## Create new migration
	@echo "$(GREEN)Creating new migration...$(RESET)"
	alembic revision --autogenerate -m "$(MSG)"

migrate-upgrade: ## Apply database migrations
	@echo "$(GREEN)Applying database migrations...$(RESET)"
	alembic upgrade head

migrate-downgrade: ## Rollback database migration
	@echo "$(YELLOW)Rolling back database migration...$(RESET)"
	alembic downgrade -1

# Documentation Commands
docs: docs-build ## Build documentation

docs-build: $(VENV) ## Build Sphinx documentation
	@echo "$(GREEN)Building documentation...$(RESET)"
	cd docs && $(VENV_BIN)/sphinx-build -b html . _build/html

docs-serve: $(VENV) ## Serve documentation locally
	@echo "$(GREEN)Serving documentation at http://localhost:8080...$(RESET)"
	cd docs/_build/html && $(PYTHON_VENV) -m http.server 8080

docs-clean: ## Clean documentation build
	@echo "$(YELLOW)Cleaning documentation...$(RESET)"
	rm -rf docs/_build/

# Docker Commands
docker-build: ## Build Docker image
	@echo "$(GREEN)Building Docker image...$(RESET)"
	docker build -t $(PROJECT_NAME):$(VERSION) .
	docker tag $(PROJECT_NAME):$(VERSION) $(PROJECT_NAME):latest

docker-run: ## Run Docker container
	@echo "$(GREEN)Running Docker container...$(RESET)"
	docker run -p 8000:8000 --env-file .env $(PROJECT_NAME):latest

docker-clean: ## Clean Docker images and containers
	@echo "$(YELLOW)Cleaning Docker resources...$(RESET)"
	docker system prune -f
	docker image prune -f

# Deployment Commands
deploy-staging: ## Deploy to staging environment
	@echo "$(GREEN)Deploying to staging...$(RESET)"
	# Add staging deployment commands here

deploy-prod: ## Deploy to production environment
	@echo "$(RED)Deploying to production...$(RESET)"
	# Add production deployment commands here

# Monitoring Commands
monitoring: ## Start monitoring dashboard
	@echo "$(GREEN)Starting monitoring dashboard...$(RESET)"
	# Add monitoring commands here

logs: ## View application logs
	@echo "$(GREEN)Viewing application logs...$(RESET)"
	tail -f logs/app.log

# Backup Commands
backup: ## Create database backup
	@echo "$(GREEN)Creating database backup...$(RESET)"
	# Add backup commands here

restore: ## Restore database from backup
	@echo "$(YELLOW)Restoring database from backup...$(RESET)"
	# Add restore commands here

# Development Utilities
shell: $(VENV) ## Start Python shell with app context
	@echo "$(GREEN)Starting Python shell...$(RESET)"
	$(PYTHON_VENV) -c "from app.main import app; import IPython; IPython.embed()"

check-deps: $(VENV) ## Check for outdated dependencies
	@echo "$(GREEN)Checking for outdated dependencies...$(RESET)"
	$(PIP_VENV) list --outdated

update-deps: $(VENV) ## Update dependencies
	@echo "$(GREEN)Updating dependencies...$(RESET)"
	$(PIP_VENV) install --upgrade -r requirements-dev.txt

# Performance Commands
profile: $(VENV) ## Profile application performance
	@echo "$(GREEN)Profiling application...$(RESET)"
	$(PYTHON_VENV) -m cProfile -o profile.stats app/main.py

benchmark: $(VENV) ## Run performance benchmarks
	@echo "$(GREEN)Running benchmarks...$(RESET)"
	# Add benchmark commands here

# Local CI/CD Commands
ci-full: clean quality-check test-coverage security-check docs-build docker-build ## Complete CI/CD pipeline
	@echo "$(GREEN)🎉 Full CI/CD pipeline completed successfully!$(RESET)"
	@echo "$(CYAN)📊 Results Summary:$(RESET)"
	@echo "  ✅ Code Quality: PASSED"
	@echo "  ✅ Type Checking: PASSED"
	@echo "  ✅ Security Scan: PASSED"
	@echo "  ✅ Test Coverage: PASSED"
	@echo "  ✅ Documentation: BUILT"
	@echo "  ✅ Docker Image: BUILT"

ci-test: ## Run CI test suite with reporting
	@echo "$(GREEN)Running CI test suite...$(RESET)"
	$(PYTHON_VENV) -m pytest \
		--cov=app \
		--cov-report=xml \
		--cov-report=html \
		--cov-report=term-missing \
		--cov-fail-under=95 \
		--junitxml=reports/test-results.xml \
		--html=reports/test-report.html \
		--self-contained-html

ci-quality: ## Run CI quality checks with reporting
	@echo "$(GREEN)Running CI quality checks...$(RESET)"
	@mkdir -p reports
	$(VENV_BIN)/ruff check app/ tests/ --output-format=json --output-file=reports/ruff-results.json || true
	$(VENV_BIN)/ruff check app/ tests/ --output-format=github
	$(VENV_BIN)/mypy app/ --junit-xml=reports/mypy-results.xml --html-report=reports/mypy-html
	@echo "$(GREEN)Quality reports generated in reports/ directory$(RESET)"

ci-security: ## Run security checks with reporting
	@echo "$(GREEN)Running security checks...$(RESET)"
	@mkdir -p reports
	$(VENV_BIN)/bandit -r app/ -f json -o reports/bandit-results.json || true
	$(VENV_BIN)/bandit -r app/ -f txt -o reports/bandit-report.txt || true
	$(VENV_BIN)/bandit -r app/
	@echo "$(GREEN)Security reports generated in reports/ directory$(RESET)"

security-scan: ## Run comprehensive security scan
	@echo "$(GREEN)Running comprehensive security scan...$(RESET)"
	$(VENV_BIN)/python scripts/security_scan.py --output-dir reports/security

security-test: ## Run security-specific tests
	@echo "$(GREEN)Running security tests...$(RESET)"
	$(VENV_BIN)/pytest tests/security/ -v --tb=short

security-api-test: ## Run API security tests
	@echo "$(GREEN)Running API security tests...$(RESET)"
	$(VENV_BIN)/pytest tests/security/test_api_security.py -v

security-infra-test: ## Run infrastructure security tests
	@echo "$(GREEN)Running infrastructure security tests...$(RESET)"
	$(VENV_BIN)/pytest tests/security/test_infrastructure_security.py -v

dependency-check: ## Check for vulnerable dependencies
	@echo "$(GREEN)Checking for vulnerable dependencies...$(RESET)"
	$(VENV_BIN)/safety check --json --output reports/safety-results.json || true
	$(VENV_BIN)/safety check

secrets-scan: ## Scan for secrets in codebase
	@echo "$(GREEN)Scanning for secrets...$(RESET)"
	@mkdir -p reports/security
	$(VENV_BIN)/python scripts/security_scan.py --scan secrets

docker-security-scan: ## Scan Docker images for vulnerabilities
	@echo "$(GREEN)Scanning Docker images...$(RESET)"
	@mkdir -p reports/security
	$(VENV_BIN)/python scripts/security_scan.py --scan docker

semgrep-scan: ## Run Semgrep security analysis
	@echo "$(GREEN)Running Semgrep security analysis...$(RESET)"
	@mkdir -p reports/security
	$(VENV_BIN)/semgrep --config=auto --json --output reports/security/semgrep-results.json app/ || true

security-full: dependency-check secrets-scan security-scan security-test ## Run all security checks

# Local CI/CD Pipeline Commands
ci-local: ## Run complete local CI/CD pipeline
	@echo "$(CYAN)🚀 Starting Local CI/CD Pipeline for Blast-Radius$(RESET)"
	@echo "$(CYAN)================================================$(RESET)"
	$(MAKE) ci-setup
	$(MAKE) ci-lint
	$(MAKE) ci-security
	$(MAKE) ci-test
	$(MAKE) ci-build
	$(MAKE) ci-deploy-local
	@echo "$(GREEN)✅ Local CI/CD Pipeline Completed Successfully!$(RESET)"

ci-setup: ## Setup CI environment
	@echo "$(BLUE)📦 Setting up CI environment...$(RESET)"
	@mkdir -p reports/{coverage,security,test,build}
	@mkdir -p logs
	$(MAKE) install-dev
	@echo "$(GREEN)✅ CI environment ready$(RESET)"

ci-lint: ## Run all linting and code quality checks
	@echo "$(BLUE)🔍 Running code quality checks...$(RESET)"
	$(VENV_BIN)/ruff check app/ tests/ --output-format=json > reports/ruff-results.json || true
	$(VENV_BIN)/ruff check app/ tests/
	$(VENV_BIN)/mypy app/ --json-report reports/mypy || true
	$(VENV_BIN)/mypy app/
	@echo "$(GREEN)✅ Code quality checks passed$(RESET)"

ci-test: ## Run comprehensive test suite with reporting
	@echo "$(BLUE)🧪 Running comprehensive test suite...$(RESET)"
	$(VENV_BIN)/pytest tests/ \
		--cov=app \
		--cov-report=html:reports/coverage/html \
		--cov-report=xml:reports/coverage/coverage.xml \
		--cov-report=json:reports/coverage/coverage.json \
		--junitxml=reports/test/junit.xml \
		--html=reports/test/report.html \
		--self-contained-html \
		-v
	@echo "$(GREEN)✅ All tests passed$(RESET)"

ci-build: ## Build application artifacts
	@echo "$(BLUE)🏗️ Building application...$(RESET)"
	@echo "Building Docker images..."
	docker-compose build --no-cache
	@echo "$(GREEN)✅ Build completed$(RESET)"

ci-deploy-local: ## Deploy to local environment
	@echo "$(BLUE)🚀 Deploying to local environment...$(RESET)"
	docker-compose up -d
	@echo "Waiting for services to be ready..."
	@sleep 10
	@echo "$(GREEN)✅ Local deployment completed$(RESET)"
	@echo "$(CYAN)🌐 Application available at: http://localhost:8000$(RESET)"
	@echo "$(CYAN)📚 API Documentation: http://localhost:8000/docs$(RESET)"

ci-validate: ## Validate deployment
	@echo "$(BLUE)✅ Validating deployment...$(RESET)"
	@curl -f http://localhost:8000/health || (echo "$(RED)❌ Health check failed$(RESET)" && exit 1)
	@echo "$(GREEN)✅ Deployment validation passed$(RESET)"

ci-report: ## Generate CI/CD summary report
	@echo "$(BLUE)📊 Generating CI/CD report...$(RESET)"
	@python scripts/generate_ci_report.py
	@echo "$(GREEN)✅ CI/CD report generated: reports/ci-summary.html$(RESET)"

ci-clean: ## Clean CI artifacts
	@echo "$(BLUE)🧹 Cleaning CI artifacts...$(RESET)"
	rm -rf reports/*
	rm -rf logs/*
	docker-compose down -v
	@echo "$(GREEN)✅ CI artifacts cleaned$(RESET)"

# Pre-commit pipeline
pre-commit-local: ## Run pre-commit checks locally
	@echo "$(CYAN)🔄 Running pre-commit pipeline...$(RESET)"
	$(MAKE) format
	$(MAKE) lint
	$(MAKE) security-scan
	$(MAKE) test-unit
	@echo "$(GREEN)✅ Pre-commit checks passed$(RESET)"

# Development workflow
dev-setup: ## Complete development environment setup
	@echo "$(CYAN)🛠️ Setting up development environment...$(RESET)"
	$(MAKE) install-dev
	$(MAKE) db-setup
	$(MAKE) create-admin-user
	@echo "$(GREEN)✅ Development environment ready$(RESET)"
	@echo "$(CYAN)💡 Run 'make dev-start' to start development server$(RESET)"

dev-start: ## Start development environment
	@echo "$(CYAN)🚀 Starting development environment...$(RESET)"
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
	$(MAKE) run-dev &
	@echo "$(GREEN)✅ Development environment started$(RESET)"
	@echo "$(CYAN)🌐 Backend API: http://localhost:8000$(RESET)"
	@echo "$(CYAN)📚 API Docs: http://localhost:8000/docs$(RESET)"
	@echo "$(CYAN)🗄️ Database: postgresql://localhost:5432/blast_radius$(RESET)"

dev-stop: ## Stop development environment
	@echo "$(BLUE)🛑 Stopping development environment...$(RESET)"
	docker-compose down
	@pkill -f "uvicorn" || true
	@echo "$(GREEN)✅ Development environment stopped$(RESET)"

# Production-like local deployment
prod-local: ## Deploy production-like environment locally
	@echo "$(CYAN)🏭 Deploying production-like environment...$(RESET)"
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "$(GREEN)✅ Production-like environment deployed$(RESET)"
	@echo "$(CYAN)🌐 Application: https://localhost:8443$(RESET)"

ci-reports: ## Generate comprehensive CI reports
	@echo "$(GREEN)Generating comprehensive CI reports...$(RESET)"
	@mkdir -p reports
	@echo "$(CYAN)Generating coverage badge...$(RESET)"
	$(PYTHON_VENV) -c "
	import xml.etree.ElementTree as ET
	try:
		tree = ET.parse('reports/coverage.xml')
		coverage = tree.getroot().attrib['line-rate']
		coverage_pct = float(coverage) * 100
		color = 'brightgreen' if coverage_pct >= 95 else 'yellow' if coverage_pct >= 80 else 'red'
		print(f'Coverage: {coverage_pct:.1f}% - {color}')
	except:
		print('Coverage: Unknown')
	"

# Version Management
version: ## Show current version
	@echo "$(GREEN)Current version: $(VERSION)$(RESET)"

bump-version: ## Bump version (usage: make bump-version VERSION=0.0.2)
	@echo "$(GREEN)Bumping version to $(VERSION)...$(RESET)"
	sed -i 's/version = ".*"/version = "$(VERSION)"/' pyproject.toml
