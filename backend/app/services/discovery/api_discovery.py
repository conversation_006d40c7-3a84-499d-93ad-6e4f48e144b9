"""
API Discovery Engine

Comprehensive API endpoint discovery using multiple tools and techniques
including <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and custom discovery methods.
"""

import asyncio
import logging
import subprocess
import json
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from urllib.parse import urlparse

from app.services.discovery.base_discovery import BaseDiscoveryEngine, DiscoveryResult
from app.db.models.asset import AssetType, AssetProvider, DiscoverySource, RiskLevel
from app.core.config import settings


logger = logging.getLogger(__name__)


class APIDiscoveryEngine(BaseDiscoveryEngine):
    """
    API discovery engine for REST/GraphQL endpoint discovery.
    
    Supports multiple discovery tools and techniques:
    - Akto API discovery
    - Kiterunner endpoint enumeration
    - OpenAPI/Swagger specification parsing
    - Custom endpoint discovery
    
    Attributes:
        tool: Discovery tool to use (akto, kiterunner, custom)
        target: Target URL or domain for discovery
        wordlists: List of wordlist files for brute force discovery
        timeout: Request timeout in seconds
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Initialize API discovery engine.
        
        Args:
            config: API discovery configuration containing:
                - tool: Discovery tool (akto, kiterunner, custom)
                - target: Target URL or domain
                - wordlists: List of wordlist files (optional)
                - timeout: Request timeout in seconds (optional)
                - auth_token: Authentication token (optional)
                - headers: Custom headers (optional)
                
        Raises:
            ValueError: If required configuration is missing
        """
        super().__init__(config)
        
        self.tool = config.get("tool", "akto").lower()
        self.target = config.get("target")
        self.wordlists = config.get("wordlists", [])
        self.timeout = config.get("timeout", 30)
        self.auth_token = config.get("auth_token")
        self.headers = config.get("headers", {})
        
        if not self.target:
            raise ValueError("API discovery target is required")
        
        # Parse target URL
        self.parsed_target = urlparse(self.target)
        if not self.parsed_target.scheme:
            self.target = f"https://{self.target}"
            self.parsed_target = urlparse(self.target)
        
        logger.info(f"Initialized API discovery engine with tool: {self.tool}")
    
    @property
    def provider(self) -> AssetProvider:
        """Get the asset provider for this discovery engine."""
        return AssetProvider.ON_PREMISES
    
    @property
    def discovery_source(self) -> DiscoverySource:
        """Get the discovery source for this discovery engine."""
        tool_mapping = {
            "akto": DiscoverySource.AKTO,
            "kiterunner": DiscoverySource.KITERUNNER,
            "custom": DiscoverySource.API_DISCOVERY
        }
        return tool_mapping.get(self.tool, DiscoverySource.API_DISCOVERY)
    
    @property
    def supported_asset_types(self) -> Set[AssetType]:
        """Get the set of asset types supported by API discovery."""
        return {AssetType.APPLICATION, AssetType.API_ENDPOINT}
    
    async def validate_config(self) -> bool:
        """
        Validate API discovery configuration.
        
        Returns:
            bool: True if configuration is valid
        """
        try:
            # Validate target URL
            if not self.target:
                self.logger.error("API discovery target is required")
                return False
            
            # Check if target is reachable (basic connectivity test)
            import aiohttp
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                try:
                    async with session.get(self.target) as response:
                        self.logger.info(f"Target {self.target} is reachable (status: {response.status})")
                        return True
                except Exception as e:
                    self.logger.warning(f"Target {self.target} connectivity test failed: {e}")
                    # Still return True as the target might be protected but valid
                    return True
            
        except Exception as e:
            self.logger.error(f"API discovery config validation failed: {e}")
            return False
    
    async def discover(self) -> DiscoveryResult:
        """
        Execute API endpoint discovery.
        
        Returns:
            DiscoveryResult: Discovery results with API endpoints
        """
        self._discovery_start_time = datetime.utcnow()
        self._log_discovery_event(f"Starting {self.tool} API discovery for {self.target}")
        
        try:
            # Execute discovery based on selected tool
            if self.tool == "akto":
                discovery_result = await self._discover_with_akto()
            elif self.tool == "kiterunner":
                discovery_result = await self._discover_with_kiterunner()
            else:
                discovery_result = await self._discover_with_custom_methods()
            
            # Process discovered endpoints
            self._assets_discovered = discovery_result.get("assets", [])
            self._relationships_discovered = discovery_result.get("relationships", [])
            self._execution_log.extend(discovery_result.get("logs", []))
            
            self._log_discovery_event(
                f"API discovery completed. Discovered {len(self._assets_discovered)} endpoints"
            )
            
            return self._build_discovery_result()
            
        except Exception as e:
            error_msg = f"API discovery failed: {e}"
            self._log_discovery_event(error_msg, "error")
            return self._build_discovery_result()
    
    async def _discover_with_akto(self) -> Dict[str, Any]:
        """Discover APIs using Akto."""
        self._log_discovery_event("Using Akto for API discovery")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            # Mock Akto discovery for now
            # In a real implementation, you would integrate with Akto API or CLI
            
            # Simulate discovered endpoints
            mock_endpoints = [
                "/api/v1/users",
                "/api/v1/auth/login",
                "/api/v1/auth/logout",
                "/api/v1/products",
                "/api/v1/orders",
                "/api/v2/users",
                "/graphql"
            ]
            
            for endpoint in mock_endpoints:
                full_url = f"{self.target.rstrip('/')}{endpoint}"
                
                # Calculate risk score based on endpoint characteristics
                risk_score = self._calculate_endpoint_risk_score(endpoint)
                
                asset_data = self._create_asset_data(
                    name=f"API Endpoint: {endpoint}",
                    asset_type=AssetType.API_ENDPOINT,
                    provider_id=full_url,
                    hostname=self.parsed_target.hostname,
                    environment="unknown",
                    risk_score=risk_score,
                    risk_level=self._get_risk_level_from_score(risk_score),
                    configuration={
                        "endpoint_path": endpoint,
                        "full_url": full_url,
                        "discovery_tool": "akto",
                        "methods": ["GET", "POST"],  # Mock methods
                        "requires_auth": "auth" in endpoint.lower(),
                        "api_version": self._extract_api_version(endpoint)
                    },
                    metadata={
                        "discovery_tool": "akto",
                        "target_domain": self.parsed_target.hostname,
                        "last_discovery": datetime.utcnow().isoformat()
                    }
                )
                
                assets.append(asset_data)
            
            logs.append(f"Akto discovered {len(assets)} API endpoints")
            
        except Exception as e:
            error_msg = f"Akto discovery failed: {e}"
            logs.append(error_msg)
        
        return {
            "assets": assets,
            "relationships": relationships,
            "logs": logs
        }
    
    async def _discover_with_kiterunner(self) -> Dict[str, Any]:
        """Discover APIs using Kiterunner."""
        self._log_discovery_event("Using Kiterunner for API discovery")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            # Mock Kiterunner discovery for now
            # In a real implementation, you would execute Kiterunner CLI
            
            # Simulate discovered endpoints from wordlist scanning
            mock_endpoints = [
                "/admin/api/v1/config",
                "/api/internal/health",
                "/api/debug/vars",
                "/swagger.json",
                "/openapi.yaml",
                "/.well-known/openapi_desc",
                "/docs/api",
                "/api-docs"
            ]
            
            for endpoint in mock_endpoints:
                full_url = f"{self.target.rstrip('/')}{endpoint}"
                
                # Calculate risk score
                risk_score = self._calculate_endpoint_risk_score(endpoint)
                
                asset_data = self._create_asset_data(
                    name=f"API Endpoint: {endpoint}",
                    asset_type=AssetType.API_ENDPOINT,
                    provider_id=full_url,
                    hostname=self.parsed_target.hostname,
                    environment="unknown",
                    risk_score=risk_score,
                    risk_level=self._get_risk_level_from_score(risk_score),
                    configuration={
                        "endpoint_path": endpoint,
                        "full_url": full_url,
                        "discovery_tool": "kiterunner",
                        "methods": ["GET"],  # Mock methods
                        "is_documentation": any(doc in endpoint.lower() for doc in ["swagger", "openapi", "docs"]),
                        "is_admin": "admin" in endpoint.lower(),
                        "is_debug": "debug" in endpoint.lower()
                    },
                    metadata={
                        "discovery_tool": "kiterunner",
                        "target_domain": self.parsed_target.hostname,
                        "last_discovery": datetime.utcnow().isoformat()
                    }
                )
                
                assets.append(asset_data)
            
            logs.append(f"Kiterunner discovered {len(assets)} API endpoints")
            
        except Exception as e:
            error_msg = f"Kiterunner discovery failed: {e}"
            logs.append(error_msg)
        
        return {
            "assets": assets,
            "relationships": relationships,
            "logs": logs
        }
    
    async def _discover_with_custom_methods(self) -> Dict[str, Any]:
        """Discover APIs using custom methods."""
        self._log_discovery_event("Using custom methods for API discovery")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            # Custom discovery methods
            discovery_tasks = [
                self._discover_openapi_specs(),
                self._discover_common_endpoints(),
                self._discover_robots_txt_endpoints()
            ]
            
            # Execute discovery tasks
            results = await asyncio.gather(*discovery_tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, Exception):
                    logs.append(f"Custom discovery task failed: {result}")
                else:
                    assets.extend(result.get("assets", []))
                    logs.extend(result.get("logs", []))
            
            logs.append(f"Custom discovery found {len(assets)} API endpoints")
            
        except Exception as e:
            error_msg = f"Custom API discovery failed: {e}"
            logs.append(error_msg)
        
        return {
            "assets": assets,
            "relationships": relationships,
            "logs": logs
        }
    
    async def _discover_openapi_specs(self) -> Dict[str, Any]:
        """Discover OpenAPI/Swagger specifications."""
        assets = []
        logs = []
        
        # Common OpenAPI spec locations
        spec_paths = [
            "/swagger.json",
            "/swagger.yaml",
            "/openapi.json",
            "/openapi.yaml",
            "/api-docs",
            "/docs/swagger.json",
            "/v1/swagger.json",
            "/v2/swagger.json"
        ]
        
        import aiohttp
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            for spec_path in spec_paths:
                try:
                    spec_url = f"{self.target.rstrip('/')}{spec_path}"
                    async with session.get(spec_url, headers=self.headers) as response:
                        if response.status == 200:
                            risk_score = self._calculate_endpoint_risk_score(spec_path)
                            
                            asset_data = self._create_asset_data(
                                name=f"OpenAPI Spec: {spec_path}",
                                asset_type=AssetType.API_ENDPOINT,
                                provider_id=spec_url,
                                hostname=self.parsed_target.hostname,
                                environment="unknown",
                                risk_score=risk_score,
                                risk_level=self._get_risk_level_from_score(risk_score),
                                configuration={
                                    "endpoint_path": spec_path,
                                    "full_url": spec_url,
                                    "discovery_tool": "custom",
                                    "is_specification": True,
                                    "content_type": response.headers.get("content-type", "unknown")
                                },
                                metadata={
                                    "discovery_tool": "custom",
                                    "target_domain": self.parsed_target.hostname,
                                    "last_discovery": datetime.utcnow().isoformat()
                                }
                            )
                            
                            assets.append(asset_data)
                            logs.append(f"Found OpenAPI spec: {spec_path}")
                            
                except Exception as e:
                    # Silently continue - spec might not exist
                    pass
        
        return {"assets": assets, "logs": logs}
    
    async def _discover_common_endpoints(self) -> Dict[str, Any]:
        """Discover common API endpoints."""
        assets = []
        logs = []
        
        # Common API endpoints
        common_paths = [
            "/health",
            "/status",
            "/version",
            "/info",
            "/metrics",
            "/ping"
        ]
        
        import aiohttp
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            for path in common_paths:
                try:
                    url = f"{self.target.rstrip('/')}{path}"
                    async with session.get(url, headers=self.headers) as response:
                        if response.status in [200, 401, 403]:  # Include auth-protected endpoints
                            risk_score = self._calculate_endpoint_risk_score(path)
                            
                            asset_data = self._create_asset_data(
                                name=f"Common Endpoint: {path}",
                                asset_type=AssetType.API_ENDPOINT,
                                provider_id=url,
                                hostname=self.parsed_target.hostname,
                                environment="unknown",
                                risk_score=risk_score,
                                risk_level=self._get_risk_level_from_score(risk_score),
                                configuration={
                                    "endpoint_path": path,
                                    "full_url": url,
                                    "discovery_tool": "custom",
                                    "response_status": response.status,
                                    "is_health_check": path in ["/health", "/status", "/ping"]
                                },
                                metadata={
                                    "discovery_tool": "custom",
                                    "target_domain": self.parsed_target.hostname,
                                    "last_discovery": datetime.utcnow().isoformat()
                                }
                            )
                            
                            assets.append(asset_data)
                            logs.append(f"Found common endpoint: {path} (status: {response.status})")
                            
                except Exception as e:
                    # Silently continue
                    pass
        
        return {"assets": assets, "logs": logs}
    
    async def _discover_robots_txt_endpoints(self) -> Dict[str, Any]:
        """Discover endpoints from robots.txt."""
        assets = []
        logs = []
        
        try:
            import aiohttp
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                robots_url = f"{self.target.rstrip('/')}/robots.txt"
                async with session.get(robots_url, headers=self.headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        # Parse robots.txt for disallowed paths
                        for line in content.split('\n'):
                            line = line.strip()
                            if line.startswith('Disallow:'):
                                path = line.split(':', 1)[1].strip()
                                if path and path != '/' and '/api' in path.lower():
                                    url = f"{self.target.rstrip('/')}{path}"
                                    risk_score = self._calculate_endpoint_risk_score(path)
                                    
                                    asset_data = self._create_asset_data(
                                        name=f"Robots.txt Endpoint: {path}",
                                        asset_type=AssetType.API_ENDPOINT,
                                        provider_id=url,
                                        hostname=self.parsed_target.hostname,
                                        environment="unknown",
                                        risk_score=risk_score,
                                        risk_level=self._get_risk_level_from_score(risk_score),
                                        configuration={
                                            "endpoint_path": path,
                                            "full_url": url,
                                            "discovery_tool": "custom",
                                            "source": "robots.txt",
                                            "is_disallowed": True
                                        },
                                        metadata={
                                            "discovery_tool": "custom",
                                            "target_domain": self.parsed_target.hostname,
                                            "last_discovery": datetime.utcnow().isoformat()
                                        }
                                    )
                                    
                                    assets.append(asset_data)
                        
                        if assets:
                            logs.append(f"Found {len(assets)} endpoints from robots.txt")
                        
        except Exception as e:
            logs.append(f"Failed to parse robots.txt: {e}")
        
        return {"assets": assets, "logs": logs}
    
    def _calculate_endpoint_risk_score(self, endpoint: str) -> int:
        """Calculate risk score for an API endpoint."""
        # Base risk score for API endpoints
        risk_score = 40
        
        # High-risk patterns
        high_risk_patterns = ["admin", "debug", "internal", "private", "secret", "config", "env"]
        if any(pattern in endpoint.lower() for pattern in high_risk_patterns):
            risk_score += 30
        
        # Authentication endpoints
        if any(auth in endpoint.lower() for auth in ["auth", "login", "token", "oauth"]):
            risk_score += 20
        
        # API versioning (newer versions might have fewer security issues)
        if "/v1/" in endpoint:
            risk_score += 10  # Older API versions
        elif "/v2/" in endpoint:
            risk_score += 5
        
        # Documentation endpoints (information disclosure)
        if any(doc in endpoint.lower() for doc in ["swagger", "openapi", "docs", "api-docs"]):
            risk_score += 15
        
        # Health/status endpoints (usually lower risk)
        if any(health in endpoint.lower() for health in ["health", "status", "ping"]):
            risk_score -= 10
        
        return min(max(risk_score, 0), 100)
    
    def _extract_api_version(self, endpoint: str) -> Optional[str]:
        """Extract API version from endpoint path."""
        import re
        version_match = re.search(r'/v(\d+)', endpoint)
        return f"v{version_match.group(1)}" if version_match else None
