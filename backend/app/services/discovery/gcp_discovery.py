"""
Google Cloud Platform Discovery Service

Comprehensive GCP asset discovery using Google Cloud APIs,
providing detailed asset information with security context and threat intelligence.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
import json

try:
    from google.cloud import compute_v1
    from google.cloud import storage
    from google.cloud import sql_v1
    from google.cloud import container_v1
    from google.cloud import asset_v1
    from google.oauth2 import service_account
    from google.auth import default
    from google.api_core import exceptions as gcp_exceptions
    GCP_AVAILABLE = True
except ImportError:
    GCP_AVAILABLE = False

from app.services.discovery.base_discovery import BaseDiscoveryEngine, DiscoveryResult
from app.db.models.asset import AssetType, AssetProvider, DiscoverySource, RiskLevel
from app.core.config import settings


logger = logging.getLogger(__name__)


class GCPDiscoveryService(BaseDiscoveryEngine):
    """
    Google Cloud Platform asset discovery service.
    
    Discovers and catalogs GCP resources across projects with comprehensive
    metadata collection, security context analysis, and threat intelligence integration.
    
    Attributes:
        project_id: GCP project identifier
        credentials: GCP authentication credentials
        compute_client: GCP Compute Engine client
        storage_client: GCP Cloud Storage client
        sql_client: GCP Cloud SQL client
        container_client: GCP Container/GKE client
        asset_client: GCP Asset Inventory client
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Initialize GCP discovery service.
        
        Args:
            config: GCP discovery configuration containing:
                - project_id: GCP project ID
                - credentials_path: Path to service account JSON (optional)
                - regions: List of GCP regions to scan (optional)
                - services: List of GCP services to discover (optional)
                
        Raises:
            ValueError: If required configuration is missing
            ImportError: If GCP SDK is not available
        """
        if not GCP_AVAILABLE:
            raise ImportError(
                "Google Cloud SDK not available. Install with: pip install google-cloud-compute "
                "google-cloud-storage google-cloud-sql google-cloud-container google-cloud-asset"
            )
        
        super().__init__(config)
        
        self.project_id = config.get("project_id")
        if not self.project_id:
            raise ValueError("GCP project_id is required")
        
        # Initialize GCP credentials
        self.credentials = self._initialize_credentials(config)
        
        # Initialize GCP service clients
        self._initialize_clients()
        
        # Discovery configuration
        self.regions = config.get("regions", ["us-central1", "us-east1"])
        self.services = config.get("services", [
            "compute", "storage", "sql", "containers", "functions"
        ])
        
        logger.info(f"Initialized GCP discovery for project {self.project_id}")
    
    @property
    def provider(self) -> AssetProvider:
        """Get the asset provider for this discovery engine."""
        return AssetProvider.GCP
    
    @property
    def discovery_source(self) -> DiscoverySource:
        """Get the discovery source for this discovery engine."""
        return DiscoverySource.GCP_API
    
    @property
    def supported_asset_types(self) -> Set[AssetType]:
        """Get the set of asset types supported by GCP discovery."""
        return {
            AssetType.CLOUD_INSTANCE,
            AssetType.DATABASE,
            AssetType.STORAGE,
            AssetType.APPLICATION,
            AssetType.CONTAINER,
            AssetType.NETWORK_DEVICE,
            AssetType.LOAD_BALANCER
        }
    
    def _initialize_credentials(self, config: Dict[str, Any]) -> Any:
        """Initialize GCP authentication credentials."""
        credentials_path = config.get("credentials_path")
        
        if credentials_path:
            # Service account authentication
            return service_account.Credentials.from_service_account_file(credentials_path)
        else:
            # Default credential chain (service account, gcloud, etc.)
            credentials, _ = default()
            return credentials
    
    def _initialize_clients(self) -> None:
        """Initialize GCP service clients."""
        try:
            self.compute_client = compute_v1.InstancesClient(credentials=self.credentials)
            self.storage_client = storage.Client(
                project=self.project_id, 
                credentials=self.credentials
            )
            self.sql_client = sql_v1.SqlInstancesServiceClient(credentials=self.credentials)
            self.container_client = container_v1.ClusterManagerClient(credentials=self.credentials)
            self.asset_client = asset_v1.AssetServiceClient(credentials=self.credentials)
            
        except Exception as e:
            logger.error(f"Failed to initialize GCP clients: {e}")
            raise
    
    async def validate_config(self) -> bool:
        """
        Validate GCP discovery configuration.
        
        Returns:
            bool: True if configuration is valid
        """
        try:
            # Test authentication by listing compute instances
            request = compute_v1.AggregatedListInstancesRequest(
                project=self.project_id,
                max_results=1
            )
            
            # This will raise an exception if authentication fails
            response = self.compute_client.aggregated_list(request=request)
            
            logger.info("GCP validation successful")
            return True
            
        except gcp_exceptions.Unauthenticated:
            logger.error("GCP authentication validation failed")
            return False
        except Exception as e:
            logger.error(f"GCP configuration validation failed: {e}")
            return False
    
    async def discover(self) -> DiscoveryResult:
        """
        Execute comprehensive GCP asset discovery.
        
        Returns:
            DiscoveryResult: Discovery results with assets and relationships
        """
        self._discovery_start_time = datetime.utcnow()
        self._log_discovery_event("Starting GCP asset discovery")
        
        try:
            # Discover different GCP service types
            discovery_tasks = []
            
            if "compute" in self.services:
                discovery_tasks.append(self._discover_compute_instances())
            
            if "storage" in self.services:
                discovery_tasks.append(self._discover_storage_buckets())
            
            if "sql" in self.services:
                discovery_tasks.append(self._discover_sql_instances())
            
            if "containers" in self.services:
                discovery_tasks.append(self._discover_gke_clusters())
            
            if "functions" in self.services:
                discovery_tasks.append(self._discover_cloud_functions())
            
            # Execute discovery tasks concurrently
            discovery_results = await asyncio.gather(*discovery_tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(discovery_results):
                if isinstance(result, Exception):
                    error_msg = f"Discovery task {i} failed: {result}"
                    self._log_discovery_event(error_msg, "error")
                else:
                    self._assets_discovered.extend(result.get("assets", []))
                    self._relationships_discovered.extend(result.get("relationships", []))
                    self._execution_log.extend(result.get("logs", []))
            
            # Discover cross-service relationships
            cross_service_relationships = await self._discover_cross_service_relationships()
            self._relationships_discovered.extend(cross_service_relationships)
            
            self._log_discovery_event(
                f"GCP discovery completed. Discovered {len(self._assets_discovered)} assets "
                f"and {len(self._relationships_discovered)} relationships"
            )
            
            return self._build_discovery_result()
            
        except Exception as e:
            error_msg = f"GCP discovery failed: {e}"
            self._log_discovery_event(error_msg, "error")
            return self._build_discovery_result()
    
    async def _discover_compute_instances(self) -> Dict[str, Any]:
        """Discover GCP Compute Engine instances."""
        self._log_discovery_event("Discovering GCP Compute Engine instances")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            # Get all instances across all zones
            request = compute_v1.AggregatedListInstancesRequest(
                project=self.project_id
            )
            
            response = self.compute_client.aggregated_list(request=request)
            
            instance_count = 0
            for zone, instances_scoped_list in response:
                if instances_scoped_list.instances:
                    for instance in instances_scoped_list.instances:
                        try:
                            instance_count += 1
                            
                            # Extract network information
                            ip_addresses = self._extract_instance_ips(instance)
                            
                            # Calculate risk score
                            risk_score = self._calculate_compute_risk_score(instance, ip_addresses)
                            
                            # Extract zone from instance zone URL
                            zone_name = instance.zone.split('/')[-1] if instance.zone else "unknown"
                            
                            asset_data = self._create_asset_data(
                                name=instance.name,
                                asset_type=AssetType.CLOUD_INSTANCE,
                                provider_id=str(instance.id),
                                provider_region=zone_name,
                                ip_addresses=ip_addresses,
                                hostname=instance.name,
                                environment=self._determine_environment_from_labels(instance.labels or {}),
                                risk_score=risk_score,
                                risk_level=self._get_risk_level_from_score(risk_score),
                                configuration={
                                    "machine_type": instance.machine_type.split('/')[-1] if instance.machine_type else None,
                                    "status": instance.status,
                                    "zone": zone_name,
                                    "labels": dict(instance.labels) if instance.labels else {},
                                    "tags": list(instance.tags.items) if instance.tags else [],
                                    "disks": [
                                        {
                                            "device_name": disk.device_name,
                                            "boot": disk.boot,
                                            "auto_delete": disk.auto_delete
                                        }
                                        for disk in instance.disks or []
                                    ],
                                    "network_interfaces": [
                                        {
                                            "name": ni.name,
                                            "network": ni.network.split('/')[-1] if ni.network else None,
                                            "subnetwork": ni.subnetwork.split('/')[-1] if ni.subnetwork else None
                                        }
                                        for ni in instance.network_interfaces or []
                                    ]
                                },
                                metadata={
                                    "gcp_instance_id": str(instance.id),
                                    "self_link": instance.self_link,
                                    "creation_timestamp": instance.creation_timestamp,
                                    "last_discovery": datetime.utcnow().isoformat()
                                }
                            )
                            
                            assets.append(asset_data)
                            
                        except Exception as e:
                            logs.append(f"Failed to process instance {instance.name}: {e}")
                            continue
            
            logs.append(f"Found {instance_count} Compute Engine instances")
            
            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }
            
        except Exception as e:
            error_msg = f"Failed to discover GCP Compute instances: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}
    
    def _extract_instance_ips(self, instance: Any) -> List[str]:
        """Extract IP addresses from GCP compute instance."""
        ip_addresses = []
        
        if instance.network_interfaces:
            for ni in instance.network_interfaces:
                # Private IP
                if ni.network_i_p:
                    ip_addresses.append(ni.network_i_p)
                
                # Public IPs from access configs
                if ni.access_configs:
                    for access_config in ni.access_configs:
                        if access_config.nat_i_p:
                            ip_addresses.append(access_config.nat_i_p)
        
        return ip_addresses
    
    def _calculate_compute_risk_score(self, instance: Any, ip_addresses: List[str]) -> int:
        """Calculate risk score for GCP Compute instance."""
        # Get base risk score
        environment = self._determine_environment_from_labels(instance.labels or {})
        has_public_ip = any(not self._is_private_ip_address(ip) for ip in ip_addresses)
        is_running = instance.status == "RUNNING"
        
        risk_score = self._calculate_base_risk_score(
            AssetType.CLOUD_INSTANCE,
            environment,
            has_public_ip,
            is_running
        )
        
        # GCP-specific adjustments
        if instance.machine_type:
            machine_type = instance.machine_type.split('/')[-1].lower()
            if "n1-highmem" in machine_type or "n1-highcpu" in machine_type:
                risk_score += 10  # High-performance instances
            elif "f1-micro" in machine_type or "g1-small" in machine_type:
                risk_score -= 5  # Small instances have lower risk
        
        # Check for preemptible instances
        if instance.scheduling and instance.scheduling.preemptible:
            risk_score -= 5  # Preemptible instances are temporary
        
        return min(max(risk_score, 0), 100)
    
    def _determine_environment_from_labels(self, labels: Dict[str, str]) -> str:
        """Determine environment from GCP labels."""
        env_label_keys = ["environment", "env", "stage", "tier"]
        
        for key, value in labels.items():
            if key.lower() in env_label_keys:
                env_value = value.lower()
                if env_value in ["prod", "production"]:
                    return "production"
                elif env_value in ["stage", "staging"]:
                    return "staging"
                elif env_value in ["dev", "development"]:
                    return "development"
                elif env_value in ["test", "testing"]:
                    return "testing"
        
        return "unknown"

    async def _discover_storage_buckets(self) -> Dict[str, Any]:
        """Discover GCP Cloud Storage buckets."""
        self._log_discovery_event("Discovering GCP Cloud Storage buckets")

        assets = []
        relationships = []
        logs = []

        try:
            # List all buckets in the project
            buckets = list(self.storage_client.list_buckets())

            logs.append(f"Found {len(buckets)} Cloud Storage buckets")

            for bucket in buckets:
                try:
                    # Get bucket metadata
                    bucket_metadata = self.storage_client.get_bucket(bucket.name)

                    risk_score = self._calculate_storage_risk_score(bucket_metadata)

                    asset_data = self._create_asset_data(
                        name=bucket.name,
                        asset_type=AssetType.STORAGE,
                        provider_id=bucket.name,
                        provider_region=bucket.location,
                        environment=self._determine_environment_from_labels(bucket.labels or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "storage_class": bucket.storage_class,
                            "location": bucket.location,
                            "location_type": bucket.location_type,
                            "labels": dict(bucket.labels) if bucket.labels else {},
                            "versioning_enabled": bucket.versioning_enabled,
                            "lifecycle_rules": len(bucket.lifecycle_rules) if bucket.lifecycle_rules else 0,
                            "cors": len(bucket.cors) if bucket.cors else 0,
                            "encryption": {
                                "default_kms_key_name": bucket.default_kms_key_name
                            } if bucket.default_kms_key_name else None
                        },
                        metadata={
                            "gcp_bucket_name": bucket.name,
                            "self_link": bucket.self_link,
                            "time_created": bucket.time_created.isoformat() if bucket.time_created else None,
                            "updated": bucket.updated.isoformat() if bucket.updated else None,
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process bucket {bucket.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover GCP Storage buckets: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_storage_risk_score(self, bucket: Any) -> int:
        """Calculate risk score for GCP Storage bucket."""
        environment = self._determine_environment_from_labels(bucket.labels or {})

        # Base risk score for storage
        risk_score = self._calculate_base_risk_score(
            AssetType.STORAGE,
            environment,
            has_public_ip=True,  # Storage buckets are accessible via internet
            is_running=True
        )

        # Check for public access
        try:
            iam_policy = bucket.get_iam_policy()
            for binding in iam_policy.bindings:
                if "allUsers" in binding.get("members", []) or "allAuthenticatedUsers" in binding.get("members", []):
                    risk_score += 30  # Major security risk
                    break
        except Exception:
            pass  # IAM policy might not be accessible

        # Encryption considerations
        if not bucket.default_kms_key_name:
            risk_score += 10  # No customer-managed encryption

        # Versioning
        if not bucket.versioning_enabled:
            risk_score += 5  # No versioning protection

        return min(max(risk_score, 0), 100)

    async def _discover_sql_instances(self) -> Dict[str, Any]:
        """Discover GCP Cloud SQL instances."""
        self._log_discovery_event("Discovering GCP Cloud SQL instances")

        assets = []
        relationships = []
        logs = []

        try:
            # List all SQL instances in the project
            request = sql_v1.SqlInstancesListRequest(project=self.project_id)
            response = self.sql_client.list(request=request)

            instances = list(response.items) if response.items else []
            logs.append(f"Found {len(instances)} Cloud SQL instances")

            for instance in instances:
                try:
                    risk_score = self._calculate_sql_risk_score(instance)

                    # Extract IP addresses
                    ip_addresses = []
                    if instance.ip_addresses:
                        for ip_config in instance.ip_addresses:
                            if ip_config.ip_address:
                                ip_addresses.append(ip_config.ip_address)

                    asset_data = self._create_asset_data(
                        name=instance.name,
                        asset_type=AssetType.DATABASE,
                        provider_id=instance.name,
                        provider_region=instance.region,
                        ip_addresses=ip_addresses,
                        hostname=instance.connection_name,
                        environment=self._determine_environment_from_labels(instance.settings.user_labels or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "database_version": instance.database_version,
                            "tier": instance.settings.tier if instance.settings else None,
                            "state": instance.state,
                            "backend_type": instance.backend_type,
                            "region": instance.region,
                            "labels": dict(instance.settings.user_labels) if instance.settings and instance.settings.user_labels else {},
                            "backup_enabled": instance.settings.backup_configuration.enabled if instance.settings and instance.settings.backup_configuration else False,
                            "ssl_mode": instance.settings.ip_configuration.ssl_mode if instance.settings and instance.settings.ip_configuration else None,
                            "authorized_networks": [
                                net.value for net in instance.settings.ip_configuration.authorized_networks
                            ] if instance.settings and instance.settings.ip_configuration and instance.settings.ip_configuration.authorized_networks else []
                        },
                        metadata={
                            "gcp_instance_name": instance.name,
                            "connection_name": instance.connection_name,
                            "self_link": instance.self_link,
                            "create_time": instance.create_time,
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process SQL instance {instance.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover GCP SQL instances: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_sql_risk_score(self, instance: Any) -> int:
        """Calculate risk score for GCP Cloud SQL instance."""
        environment = self._determine_environment_from_labels(
            instance.settings.user_labels or {} if instance.settings else {}
        )

        # Check for public IP
        has_public_ip = False
        if instance.ip_addresses:
            for ip_config in instance.ip_addresses:
                if ip_config.type == sql_v1.SqlIpAddressType.PRIMARY:
                    has_public_ip = True
                    break

        # Base risk score for databases
        risk_score = self._calculate_base_risk_score(
            AssetType.DATABASE,
            environment,
            has_public_ip,
            is_running=instance.state == "RUNNABLE"
        )

        # Database version considerations
        if instance.database_version:
            db_version = instance.database_version.lower()
            if "mysql_5_6" in db_version or "postgres_9" in db_version:
                risk_score += 15  # Older database versions

        # Backup configuration
        if instance.settings and instance.settings.backup_configuration:
            if not instance.settings.backup_configuration.enabled:
                risk_score += 10  # No backup protection

        # SSL configuration
        if instance.settings and instance.settings.ip_configuration:
            if instance.settings.ip_configuration.ssl_mode == sql_v1.SqlSslMode.ALLOW_UNENCRYPTED_AND_ENCRYPTED:
                risk_score += 15  # Allows unencrypted connections

        return min(max(risk_score, 0), 100)

    async def _discover_gke_clusters(self) -> Dict[str, Any]:
        """Discover GCP Google Kubernetes Engine clusters."""
        self._log_discovery_event("Discovering GCP GKE clusters")

        assets = []
        relationships = []
        logs = []

        try:
            # List all GKE clusters across all zones
            parent = f"projects/{self.project_id}/locations/-"
            request = container_v1.ListClustersRequest(parent=parent)
            response = self.container_client.list_clusters(request=request)

            clusters = list(response.clusters) if response.clusters else []
            logs.append(f"Found {len(clusters)} GKE clusters")

            for cluster in clusters:
                try:
                    risk_score = self._calculate_gke_risk_score(cluster)

                    asset_data = self._create_asset_data(
                        name=cluster.name,
                        asset_type=AssetType.CONTAINER,
                        provider_id=cluster.name,
                        provider_region=cluster.location,
                        hostname=cluster.endpoint,
                        environment=self._determine_environment_from_labels(cluster.resource_labels or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "status": cluster.status.name if cluster.status else None,
                            "current_master_version": cluster.current_master_version,
                            "current_node_version": cluster.current_node_version,
                            "location": cluster.location,
                            "location_type": cluster.location_type.name if cluster.location_type else None,
                            "labels": dict(cluster.resource_labels) if cluster.resource_labels else {},
                            "node_pools": [
                                {
                                    "name": pool.name,
                                    "initial_node_count": pool.initial_node_count,
                                    "status": pool.status.name if pool.status else None,
                                    "version": pool.version,
                                    "machine_type": pool.config.machine_type if pool.config else None,
                                    "disk_size_gb": pool.config.disk_size_gb if pool.config else None
                                }
                                for pool in cluster.node_pools or []
                            ],
                            "network": cluster.network,
                            "subnetwork": cluster.subnetwork,
                            "enable_autopilot": cluster.autopilot.enabled if cluster.autopilot else False,
                            "private_cluster": cluster.private_cluster_config.enable_private_nodes if cluster.private_cluster_config else False
                        },
                        metadata={
                            "gcp_cluster_name": cluster.name,
                            "self_link": cluster.self_link,
                            "endpoint": cluster.endpoint,
                            "create_time": cluster.create_time,
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process GKE cluster {cluster.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover GCP GKE clusters: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_gke_risk_score(self, cluster: Any) -> int:
        """Calculate risk score for GCP GKE cluster."""
        environment = self._determine_environment_from_labels(cluster.resource_labels or {})

        # Check if cluster has private nodes
        is_private = cluster.private_cluster_config and cluster.private_cluster_config.enable_private_nodes

        # Base risk score for containers
        risk_score = self._calculate_base_risk_score(
            AssetType.CONTAINER,
            environment,
            has_public_ip=not is_private,
            is_running=cluster.status and cluster.status.name == "RUNNING"
        )

        # Kubernetes version considerations
        if cluster.current_master_version:
            # Add risk for older versions (simplified check)
            version_parts = cluster.current_master_version.split('.')
            if len(version_parts) >= 2:
                try:
                    major, minor = int(version_parts[0]), int(version_parts[1])
                    if major == 1 and minor < 20:  # Older than 1.20
                        risk_score += 15
                except ValueError:
                    pass

        # Private cluster configuration
        if not is_private:
            risk_score += 20  # Public clusters are higher risk

        # Autopilot mode (Google-managed)
        if cluster.autopilot and cluster.autopilot.enabled:
            risk_score -= 10  # Autopilot has better security defaults

        # Node pool considerations
        if cluster.node_pools:
            total_nodes = sum(pool.initial_node_count for pool in cluster.node_pools if pool.initial_node_count)
            if total_nodes > 20:
                risk_score += 10  # Large clusters are higher value targets

        return min(max(risk_score, 0), 100)

    async def _discover_cloud_functions(self) -> Dict[str, Any]:
        """Discover GCP Cloud Functions."""
        self._log_discovery_event("Discovering GCP Cloud Functions")

        assets = []
        relationships = []
        logs = []

        try:
            # Note: This is a simplified implementation
            # In a real implementation, you would use the Cloud Functions API
            # For now, we'll create a placeholder

            logs.append("Cloud Functions discovery not fully implemented - placeholder")

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover GCP Cloud Functions: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    async def _discover_cross_service_relationships(self) -> List[Dict[str, Any]]:
        """Discover relationships between different GCP services."""
        self._log_discovery_event("Discovering cross-service relationships")

        relationships = []

        try:
            # This is a simplified implementation
            # In a real implementation, you would analyze resource dependencies
            # based on IAM policies, network connections, service accounts, etc.

            # For now, we'll create some basic relationships based on regions
            regions = {}

            # Group assets by region
            for asset in self._assets_discovered:
                region = asset.get("provider_region", "unknown")
                if region not in regions:
                    regions[region] = []
                regions[region].append(asset)

            # Create relationships within regions
            for region_name, assets in regions.items():
                for i, asset1 in enumerate(assets):
                    for asset2 in assets[i+1:]:
                        # Create a generic "same_region" relationship
                        relationships.append(
                            self._create_relationship_data(
                                source_provider_id=asset1["provider_id"],
                                target_provider_id=asset2["provider_id"],
                                relationship_type="same_region",
                                metadata={
                                    "region": region_name,
                                    "relationship_strength": 0.2
                                }
                            )
                        )

            self._log_discovery_event(f"Discovered {len(relationships)} cross-service relationships")

        except Exception as e:
            self._log_discovery_event(f"Failed to discover cross-service relationships: {e}", "error")

        return relationships
