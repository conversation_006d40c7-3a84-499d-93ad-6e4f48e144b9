"""
Network Discovery Engine

Comprehensive network discovery using multiple scanning tools and techniques
including nmap, masscan, and custom network analysis.
"""

import asyncio
import logging
import subprocess
import json
import ipaddress
from typing import Dict, List, Optional, Any, Set
from datetime import datetime

from app.services.discovery.base_discovery import BaseDiscoveryEngine, DiscoveryResult
from app.db.models.asset import Asset<PERSON>ype, AssetProvider, DiscoverySource, RiskLevel
from app.core.config import settings


logger = logging.getLogger(__name__)


class NetworkDiscoveryEngine(BaseDiscoveryEngine):
    """
    Network discovery engine for host and service discovery.
    
    Supports multiple scanning tools and techniques:
    - Nmap network scanning
    - Masscan high-speed scanning
    - Custom network analysis
    - Service detection and fingerprinting
    
    Attributes:
        target_networks: List of target networks to scan
        scan_type: Type of scan to perform (nmap, masscan, custom)
        ports: List of ports to scan
        scan_intensity: Scan intensity level (1-5)
        timeout: Scan timeout in seconds
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Initialize network discovery engine.
        
        Args:
            config: Network discovery configuration containing:
                - target_networks: List of networks to scan (CIDR notation)
                - scan_type: Scanning tool (nmap, masscan, custom)
                - ports: List of ports to scan (optional)
                - scan_intensity: Scan intensity 1-5 (optional)
                - timeout: Scan timeout in seconds (optional)
                - exclude_hosts: List of hosts to exclude (optional)
                
        Raises:
            ValueError: If required configuration is missing
        """
        super().__init__(config)
        
        self.target_networks = config.get("target_networks", [])
        self.scan_type = config.get("scan_type", "nmap").lower()
        self.ports = config.get("ports", ["22", "80", "443", "3389", "5432", "3306"])
        self.scan_intensity = config.get("scan_intensity", 3)
        self.timeout = config.get("timeout", 300)
        self.exclude_hosts = config.get("exclude_hosts", [])
        
        if not self.target_networks:
            raise ValueError("Network discovery requires target networks")
        
        # Validate network formats
        self._validate_networks()
        
        logger.info(f"Initialized network discovery engine with {len(self.target_networks)} networks")
    
    @property
    def provider(self) -> AssetProvider:
        """Get the asset provider for this discovery engine."""
        return AssetProvider.ON_PREMISES
    
    @property
    def discovery_source(self) -> DiscoverySource:
        """Get the discovery source for this discovery engine."""
        scan_mapping = {
            "nmap": DiscoverySource.NMAP,
            "masscan": DiscoverySource.MASSCAN,
            "custom": DiscoverySource.NETWORK_SCAN
        }
        return scan_mapping.get(self.scan_type, DiscoverySource.NETWORK_SCAN)
    
    @property
    def supported_asset_types(self) -> Set[AssetType]:
        """Get the set of asset types supported by network discovery."""
        return {
            AssetType.SERVER,
            AssetType.WORKSTATION,
            AssetType.NETWORK_DEVICE,
            AssetType.IOT_DEVICE,
            AssetType.DATABASE,
            AssetType.APPLICATION
        }
    
    def _validate_networks(self) -> None:
        """Validate network CIDR formats."""
        for network in self.target_networks:
            try:
                ipaddress.ip_network(network, strict=False)
            except ValueError as e:
                raise ValueError(f"Invalid network format '{network}': {e}")
    
    async def validate_config(self) -> bool:
        """
        Validate network discovery configuration.
        
        Returns:
            bool: True if configuration is valid
        """
        try:
            # Validate target networks
            if not self.target_networks:
                self.logger.error("Network discovery requires target networks")
                return False
            
            # Check if scanning tools are available
            if self.scan_type == "nmap":
                result = subprocess.run(["which", "nmap"], capture_output=True, text=True)
                if result.returncode != 0:
                    self.logger.warning("nmap not found in PATH")
                    return False
            elif self.scan_type == "masscan":
                result = subprocess.run(["which", "masscan"], capture_output=True, text=True)
                if result.returncode != 0:
                    self.logger.warning("masscan not found in PATH")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Network discovery config validation failed: {e}")
            return False
    
    async def discover(self) -> DiscoveryResult:
        """
        Execute network discovery.
        
        Returns:
            DiscoveryResult: Discovery results with discovered hosts and services
        """
        self._discovery_start_time = datetime.utcnow()
        self._log_discovery_event(f"Starting {self.scan_type} network discovery")
        
        try:
            # Execute discovery based on selected scan type
            if self.scan_type == "nmap":
                discovery_result = await self._discover_with_nmap()
            elif self.scan_type == "masscan":
                discovery_result = await self._discover_with_masscan()
            else:
                discovery_result = await self._discover_with_custom_scan()
            
            # Process discovered hosts and services
            self._assets_discovered = discovery_result.get("assets", [])
            self._relationships_discovered = discovery_result.get("relationships", [])
            self._execution_log.extend(discovery_result.get("logs", []))
            
            self._log_discovery_event(
                f"Network discovery completed. Discovered {len(self._assets_discovered)} assets"
            )
            
            return self._build_discovery_result()
            
        except Exception as e:
            error_msg = f"Network discovery failed: {e}"
            self._log_discovery_event(error_msg, "error")
            return self._build_discovery_result()
    
    async def _discover_with_nmap(self) -> Dict[str, Any]:
        """Discover hosts and services using nmap."""
        self._log_discovery_event("Using nmap for network discovery")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            for network in self.target_networks:
                logs.append(f"Scanning network: {network}")
                
                # Build nmap command
                nmap_cmd = [
                    "nmap",
                    "-sS",  # SYN scan
                    "-O",   # OS detection
                    "-sV",  # Service version detection
                    "-p", ",".join(self.ports),
                    "--host-timeout", f"{self.timeout}s",
                    "-oX", "-",  # XML output to stdout
                    network
                ]
                
                # Add exclusions
                if self.exclude_hosts:
                    nmap_cmd.extend(["--exclude", ",".join(self.exclude_hosts)])
                
                # Execute nmap scan
                process = await asyncio.create_subprocess_exec(
                    *nmap_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.timeout + 60
                )
                
                if process.returncode == 0:
                    # Parse nmap XML output
                    scan_results = self._parse_nmap_xml(stdout.decode())
                    
                    # Convert to assets
                    network_assets = self._convert_nmap_results_to_assets(scan_results, network)
                    assets.extend(network_assets)
                    
                    logs.append(f"Nmap scan of {network} completed: {len(network_assets)} hosts found")
                else:
                    error_msg = f"Nmap scan failed for {network}: {stderr.decode()}"
                    logs.append(error_msg)
            
        except asyncio.TimeoutError:
            error_msg = f"Nmap scan timed out after {self.timeout} seconds"
            logs.append(error_msg)
        except Exception as e:
            error_msg = f"Nmap discovery failed: {e}"
            logs.append(error_msg)
        
        return {
            "assets": assets,
            "relationships": relationships,
            "logs": logs
        }
    
    async def _discover_with_masscan(self) -> Dict[str, Any]:
        """Discover hosts and services using masscan."""
        self._log_discovery_event("Using masscan for network discovery")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            for network in self.target_networks:
                logs.append(f"Scanning network: {network}")
                
                # Build masscan command
                masscan_cmd = [
                    "masscan",
                    network,
                    "-p", ",".join(self.ports),
                    "--rate", "1000",
                    "--output-format", "json",
                    "--output-filename", "-"
                ]
                
                # Add exclusions
                if self.exclude_hosts:
                    masscan_cmd.extend(["--exclude", ",".join(self.exclude_hosts)])
                
                # Execute masscan
                process = await asyncio.create_subprocess_exec(
                    *masscan_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.timeout + 60
                )
                
                if process.returncode == 0:
                    # Parse masscan JSON output
                    scan_results = self._parse_masscan_json(stdout.decode())
                    
                    # Convert to assets
                    network_assets = self._convert_masscan_results_to_assets(scan_results, network)
                    assets.extend(network_assets)
                    
                    logs.append(f"Masscan scan of {network} completed: {len(network_assets)} hosts found")
                else:
                    error_msg = f"Masscan scan failed for {network}: {stderr.decode()}"
                    logs.append(error_msg)
            
        except asyncio.TimeoutError:
            error_msg = f"Masscan scan timed out after {self.timeout} seconds"
            logs.append(error_msg)
        except Exception as e:
            error_msg = f"Masscan discovery failed: {e}"
            logs.append(error_msg)
        
        return {
            "assets": assets,
            "relationships": relationships,
            "logs": logs
        }
    
    async def _discover_with_custom_scan(self) -> Dict[str, Any]:
        """Discover hosts using custom scanning methods."""
        self._log_discovery_event("Using custom methods for network discovery")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            # Simple ping sweep and port check
            for network in self.target_networks:
                network_obj = ipaddress.ip_network(network, strict=False)
                
                # Limit scan to reasonable size
                if network_obj.num_addresses > 1024:
                    logs.append(f"Network {network} too large for custom scan, skipping")
                    continue
                
                logs.append(f"Custom scanning network: {network}")
                
                # Ping sweep
                alive_hosts = await self._ping_sweep(network_obj)
                
                # Port scan alive hosts
                for host_ip in alive_hosts:
                    open_ports = await self._port_scan(host_ip, self.ports[:5])  # Limit ports for performance
                    
                    if open_ports:
                        # Create host asset
                        risk_score = self._calculate_host_risk_score(host_ip, open_ports)
                        
                        asset_data = self._create_asset_data(
                            name=f"Host-{host_ip}",
                            asset_type=self._determine_asset_type_from_ports(open_ports),
                            provider_id=host_ip,
                            ip_addresses=[host_ip],
                            hostname=host_ip,
                            environment="unknown",
                            risk_score=risk_score,
                            risk_level=self._get_risk_level_from_score(risk_score),
                            configuration={
                                "open_ports": open_ports,
                                "scan_type": "custom",
                                "network": network,
                                "ping_responsive": True
                            },
                            metadata={
                                "discovery_tool": "custom",
                                "network_scanned": network,
                                "last_discovery": datetime.utcnow().isoformat()
                            }
                        )
                        
                        assets.append(asset_data)
                
                logs.append(f"Custom scan of {network} completed: {len(assets)} hosts found")
            
        except Exception as e:
            error_msg = f"Custom network discovery failed: {e}"
            logs.append(error_msg)
        
        return {
            "assets": assets,
            "relationships": relationships,
            "logs": logs
        }
    
    async def _ping_sweep(self, network: ipaddress.IPv4Network) -> List[str]:
        """Perform ping sweep to find alive hosts."""
        alive_hosts = []
        
        # Limit concurrent pings
        semaphore = asyncio.Semaphore(50)
        
        async def ping_host(ip: str) -> Optional[str]:
            async with semaphore:
                try:
                    process = await asyncio.create_subprocess_exec(
                        "ping", "-c", "1", "-W", "1", ip,
                        stdout=asyncio.subprocess.DEVNULL,
                        stderr=asyncio.subprocess.DEVNULL
                    )
                    await asyncio.wait_for(process.wait(), timeout=3)
                    return ip if process.returncode == 0 else None
                except:
                    return None
        
        # Ping all hosts in network
        tasks = [ping_host(str(ip)) for ip in network.hosts()]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        alive_hosts = [ip for ip in results if ip and isinstance(ip, str)]
        return alive_hosts
    
    async def _port_scan(self, host: str, ports: List[str]) -> List[int]:
        """Scan specific ports on a host."""
        open_ports = []
        
        for port in ports:
            try:
                reader, writer = await asyncio.wait_for(
                    asyncio.open_connection(host, int(port)),
                    timeout=2
                )
                writer.close()
                await writer.wait_closed()
                open_ports.append(int(port))
            except:
                pass  # Port closed or filtered
        
        return open_ports
    
    def _determine_asset_type_from_ports(self, open_ports: List[int]) -> AssetType:
        """Determine asset type based on open ports."""
        # Database ports
        db_ports = {3306, 5432, 1433, 1521, 27017}
        if any(port in db_ports for port in open_ports):
            return AssetType.DATABASE
        
        # Web server ports
        web_ports = {80, 443, 8080, 8443}
        if any(port in web_ports for port in open_ports):
            return AssetType.APPLICATION
        
        # SSH/RDP (likely servers)
        admin_ports = {22, 3389}
        if any(port in admin_ports for port in open_ports):
            return AssetType.SERVER
        
        # Default to server
        return AssetType.SERVER
    
    def _calculate_host_risk_score(self, host_ip: str, open_ports: List[int]) -> int:
        """Calculate risk score for a discovered host."""
        # Base risk score
        risk_score = 30
        
        # More open ports = higher risk
        risk_score += min(len(open_ports) * 5, 30)
        
        # High-risk ports
        high_risk_ports = {23, 135, 139, 445, 1433, 3389, 5432, 3306}
        risk_score += len(set(open_ports) & high_risk_ports) * 10
        
        # Administrative ports
        admin_ports = {22, 3389, 5985, 5986}
        if any(port in admin_ports for port in open_ports):
            risk_score += 15
        
        # Database ports
        db_ports = {3306, 5432, 1433, 1521, 27017}
        if any(port in db_ports for port in open_ports):
            risk_score += 20
        
        return min(max(risk_score, 0), 100)
    
    def _parse_nmap_xml(self, xml_output: str) -> Dict[str, Any]:
        """Parse nmap XML output."""
        # Simplified XML parsing - in production, use proper XML parser
        # This is a mock implementation
        return {"hosts": []}
    
    def _parse_masscan_json(self, json_output: str) -> Dict[str, Any]:
        """Parse masscan JSON output."""
        try:
            return json.loads(json_output)
        except json.JSONDecodeError:
            return {"hosts": []}
    
    def _convert_nmap_results_to_assets(self, results: Dict[str, Any], network: str) -> List[Dict[str, Any]]:
        """Convert nmap results to asset format."""
        # Mock implementation
        return []
    
    def _convert_masscan_results_to_assets(self, results: Dict[str, Any], network: str) -> List[Dict[str, Any]]:
        """Convert masscan results to asset format."""
        # Mock implementation
        return []
