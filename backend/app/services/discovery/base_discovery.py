"""
Base Discovery Engine

Abstract base class for all discovery engines providing common functionality
and interface definitions for asset discovery operations.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from dataclasses import dataclass, field

from app.db.models.asset import AssetType, AssetProvider, DiscoverySource, RiskLevel


logger = logging.getLogger(__name__)


@dataclass
class DiscoveryResult:
    """
    Discovery operation result containing discovered assets and relationships.
    
    Attributes:
        assets_discovered: List of discovered asset data dictionaries
        relationships_discovered: List of discovered relationship data
        assets_updated: List of updated existing assets
        total_assets: Total number of assets discovered
        total_relationships: Total number of relationships discovered
        total_errors: Total number of errors encountered
        execution_time_seconds: Time taken for discovery operation
        execution_log: List of log messages from discovery process
        errors: List of error messages and details
        metadata: Additional metadata about the discovery operation
    """
    
    assets_discovered: List[Dict[str, Any]] = field(default_factory=list)
    relationships_discovered: List[Dict[str, Any]] = field(default_factory=list)
    assets_updated: List[Dict[str, Any]] = field(default_factory=list)
    total_assets: int = 0
    total_relationships: int = 0
    total_errors: int = 0
    execution_time_seconds: float = 0.0
    execution_log: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate based on errors vs total operations."""
        total_operations = self.total_assets + self.total_relationships
        if total_operations == 0:
            return 1.0
        return max(0.0, 1.0 - (self.total_errors / total_operations))
    
    @property
    def has_errors(self) -> bool:
        """Check if discovery had any errors."""
        return self.total_errors > 0 or len(self.errors) > 0


class BaseDiscoveryEngine(ABC):
    """
    Abstract base class for all discovery engines.
    
    Provides common functionality and interface definitions for asset discovery
    operations across different platforms and providers.
    
    Attributes:
        config: Discovery configuration dictionary
        provider: Asset provider type
        discovery_source: Discovery source type
        supported_asset_types: Set of supported asset types
        logger: Logging instance for the discovery engine
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Initialize base discovery engine.
        
        Args:
            config: Discovery configuration containing provider-specific settings
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize provider-specific properties
        self._initialize_provider_properties()
        
        # Discovery state
        self._discovery_start_time: Optional[datetime] = None
        self._assets_discovered: List[Dict[str, Any]] = []
        self._relationships_discovered: List[Dict[str, Any]] = []
        self._execution_log: List[str] = []
        self._errors: List[str] = []
    
    @property
    @abstractmethod
    def provider(self) -> AssetProvider:
        """Get the asset provider for this discovery engine."""
        pass
    
    @property
    @abstractmethod
    def discovery_source(self) -> DiscoverySource:
        """Get the discovery source for this discovery engine."""
        pass
    
    @property
    @abstractmethod
    def supported_asset_types(self) -> Set[AssetType]:
        """Get the set of asset types supported by this discovery engine."""
        pass
    
    @abstractmethod
    async def validate_config(self) -> bool:
        """
        Validate the discovery configuration.
        
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        pass
    
    @abstractmethod
    async def discover(self) -> DiscoveryResult:
        """
        Execute the discovery operation.
        
        Returns:
            DiscoveryResult: Results of the discovery operation
        """
        pass
    
    def _initialize_provider_properties(self) -> None:
        """Initialize provider-specific properties based on configuration."""
        # This method can be overridden by subclasses for custom initialization
        pass
    
    def _log_discovery_event(self, message: str, level: str = "info") -> None:
        """
        Log a discovery event.
        
        Args:
            message: Log message
            level: Log level (info, warning, error)
        """
        timestamp = datetime.utcnow().isoformat()
        log_entry = f"[{timestamp}] {message}"
        
        self._execution_log.append(log_entry)
        
        if level == "error":
            self.logger.error(message)
            self._errors.append(message)
        elif level == "warning":
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def _calculate_base_risk_score(
        self, 
        asset_type: AssetType, 
        environment: str = "unknown",
        has_public_ip: bool = False,
        is_running: bool = True
    ) -> int:
        """
        Calculate base risk score for an asset.
        
        Args:
            asset_type: Type of the asset
            environment: Environment (production, staging, development, etc.)
            has_public_ip: Whether asset has public IP exposure
            is_running: Whether asset is currently running
            
        Returns:
            int: Risk score from 0 to 100
        """
        # Base scores by asset type
        base_scores = {
            AssetType.DATABASE: 50,
            AssetType.APPLICATION: 45,
            AssetType.CLOUD_INSTANCE: 40,
            AssetType.SERVER: 40,
            AssetType.CONTAINER: 35,
            AssetType.NETWORK_DEVICE: 35,
            AssetType.STORAGE: 30,
            AssetType.WORKSTATION: 25,
            AssetType.IOT_DEVICE: 20,
        }
        
        risk_score = base_scores.get(asset_type, 30)
        
        # Environment-based adjustments
        env_adjustments = {
            "production": 25,
            "staging": 15,
            "testing": 10,
            "development": 5,
            "unknown": 10
        }
        
        risk_score += env_adjustments.get(environment.lower(), 10)
        
        # Public IP exposure
        if has_public_ip:
            risk_score += 20
        
        # Running state
        if not is_running:
            risk_score -= 10
        
        return min(max(risk_score, 0), 100)
    
    def _get_risk_level_from_score(self, risk_score: int) -> RiskLevel:
        """
        Convert risk score to risk level enum.
        
        Args:
            risk_score: Numeric risk score (0-100)
            
        Returns:
            RiskLevel: Corresponding risk level
        """
        if risk_score >= 80:
            return RiskLevel.CRITICAL
        elif risk_score >= 60:
            return RiskLevel.HIGH
        elif risk_score >= 40:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _determine_environment_from_tags(self, tags: Dict[str, str]) -> str:
        """
        Determine environment from resource tags.
        
        Args:
            tags: Dictionary of resource tags
            
        Returns:
            str: Determined environment
        """
        env_tag_keys = ["environment", "env", "stage", "tier", "deployment"]
        
        for key, value in tags.items():
            if key.lower() in env_tag_keys:
                env_value = value.lower()
                if env_value in ["prod", "production"]:
                    return "production"
                elif env_value in ["stage", "staging"]:
                    return "staging"
                elif env_value in ["dev", "development"]:
                    return "development"
                elif env_value in ["test", "testing"]:
                    return "testing"
        
        return "unknown"
    
    def _is_private_ip_address(self, ip_address: str) -> bool:
        """
        Check if an IP address is private.
        
        Args:
            ip_address: IP address string
            
        Returns:
            bool: True if IP is private, False otherwise
        """
        try:
            import ipaddress
            ip = ipaddress.ip_address(ip_address)
            return ip.is_private
        except ValueError:
            return False
    
    def _create_asset_data(
        self,
        name: str,
        asset_type: AssetType,
        provider_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create standardized asset data dictionary.
        
        Args:
            name: Asset name
            asset_type: Asset type
            provider_id: Provider-specific asset identifier
            **kwargs: Additional asset properties
            
        Returns:
            Dict containing standardized asset data
        """
        asset_data = {
            "name": name,
            "asset_type": asset_type,
            "provider": self.provider,
            "provider_id": provider_id,
            "discovery_source": self.discovery_source,
            "last_discovery": datetime.utcnow().isoformat(),
            **kwargs
        }
        
        # Ensure required fields have defaults
        asset_data.setdefault("environment", "unknown")
        asset_data.setdefault("risk_score", 30)
        asset_data.setdefault("risk_level", RiskLevel.MEDIUM)
        asset_data.setdefault("configuration", {})
        asset_data.setdefault("metadata", {})
        
        return asset_data
    
    def _create_relationship_data(
        self,
        source_provider_id: str,
        target_provider_id: str,
        relationship_type: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create standardized relationship data dictionary.
        
        Args:
            source_provider_id: Source asset provider ID
            target_provider_id: Target asset provider ID
            relationship_type: Type of relationship
            **kwargs: Additional relationship properties
            
        Returns:
            Dict containing standardized relationship data
        """
        return {
            "source_provider_id": source_provider_id,
            "target_provider_id": target_provider_id,
            "relationship_type": relationship_type,
            "discovery_source": self.discovery_source,
            "discovered_at": datetime.utcnow().isoformat(),
            **kwargs
        }
    
    async def _execute_with_timeout(
        self, 
        coro, 
        timeout_seconds: int = 300,
        operation_name: str = "discovery operation"
    ) -> Any:
        """
        Execute a coroutine with timeout.
        
        Args:
            coro: Coroutine to execute
            timeout_seconds: Timeout in seconds
            operation_name: Name of operation for logging
            
        Returns:
            Result of the coroutine
            
        Raises:
            asyncio.TimeoutError: If operation times out
        """
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            error_msg = f"{operation_name} timed out after {timeout_seconds} seconds"
            self._log_discovery_event(error_msg, "error")
            raise
    
    def _build_discovery_result(self) -> DiscoveryResult:
        """
        Build final discovery result from collected data.
        
        Returns:
            DiscoveryResult: Complete discovery result
        """
        execution_time = 0.0
        if self._discovery_start_time:
            execution_time = (datetime.utcnow() - self._discovery_start_time).total_seconds()
        
        return DiscoveryResult(
            assets_discovered=self._assets_discovered,
            relationships_discovered=self._relationships_discovered,
            total_assets=len(self._assets_discovered),
            total_relationships=len(self._relationships_discovered),
            total_errors=len(self._errors),
            execution_time_seconds=execution_time,
            execution_log=self._execution_log,
            errors=self._errors,
            metadata={
                "provider": self.provider.value,
                "discovery_source": self.discovery_source.value,
                "discovery_timestamp": self._discovery_start_time.isoformat() if self._discovery_start_time else None,
                "config_summary": self._get_config_summary()
            }
        )
    
    def _get_config_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the discovery configuration (excluding sensitive data).
        
        Returns:
            Dict containing non-sensitive configuration summary
        """
        sensitive_keys = {
            "password", "secret", "key", "token", "credential", 
            "access_key", "secret_key", "private_key"
        }
        
        summary = {}
        for key, value in self.config.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                summary[key] = "***REDACTED***"
            else:
                summary[key] = value
        
        return summary
