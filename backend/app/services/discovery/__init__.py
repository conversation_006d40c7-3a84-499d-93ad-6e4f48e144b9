"""
Discovery Services Module

Provides asset discovery capabilities across multiple cloud providers and platforms.
"""

from .base_discovery import BaseDiscoveryEngine, DiscoveryResult
from .cloud_discovery import CloudDiscoveryEngine
from .api_discovery import APIDiscoveryEngine
from .network_discovery import NetworkDiscoveryEngine
from .azure_discovery import AzureDiscoveryService
from .gcp_discovery import GCPDiscoveryService

__all__ = [
    "BaseDiscoveryEngine",
    "DiscoveryResult", 
    "CloudDiscoveryEngine",
    "APIDiscoveryEngine",
    "NetworkDiscoveryEngine",
    "AzureDiscoveryService",
    "GCPDiscoveryService"
]
