"""
Azure Cloud Discovery Service

Comprehensive Azure asset discovery using Azure Resource Manager APIs,
providing detailed asset information with security context and threat intelligence.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set, AsyncGenerator
from datetime import datetime
import json

try:
    from azure.identity import ClientSecretCredential, DefaultAzureCredential
    from azure.mgmt.resource import ResourceManagementClient
    from azure.mgmt.compute import ComputeManagementClient
    from azure.mgmt.storage import StorageManagementClient
    from azure.mgmt.sql import SqlManagementClient
    from azure.mgmt.web import WebSiteManagementClient
    from azure.mgmt.containerservice import ContainerServiceClient
    from azure.mgmt.network import NetworkManagementClient
    from azure.core.exceptions import AzureError, ClientAuthenticationError
    AZURE_AVAILABLE = True
except ImportError:
    AZURE_AVAILABLE = False

from app.services.discovery.base_discovery import BaseDiscoveryEngine, DiscoveryResult
from app.db.models.asset import AssetType, AssetProvider, DiscoverySource, RiskLevel
from app.core.config import settings


logger = logging.getLogger(__name__)


class AzureDiscoveryService(BaseDiscoveryEngine):
    """
    Azure cloud asset discovery service.
    
    Discovers and catalogs Azure resources across subscriptions with comprehensive
    metadata collection, security context analysis, and threat intelligence integration.
    
    Attributes:
        subscription_id: Azure subscription identifier
        credential: Azure authentication credential
        resource_client: Azure Resource Management client
        compute_client: Azure Compute Management client
        storage_client: Azure Storage Management client
        sql_client: Azure SQL Management client
        web_client: Azure Web App Management client
        container_client: Azure Container Service client
        network_client: Azure Network Management client
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """
        Initialize Azure discovery service.
        
        Args:
            config: Azure discovery configuration containing:
                - subscription_id: Azure subscription ID
                - client_id: Service principal client ID (optional)
                - client_secret: Service principal secret (optional)
                - tenant_id: Azure tenant ID (optional)
                - resource_groups: List of resource groups to scan (optional)
                - regions: List of Azure regions to scan (optional)
                - services: List of Azure services to discover (optional)
                
        Raises:
            ValueError: If required configuration is missing
            ImportError: If Azure SDK is not available
        """
        if not AZURE_AVAILABLE:
            raise ImportError(
                "Azure SDK not available. Install with: pip install azure-identity azure-mgmt-resource "
                "azure-mgmt-compute azure-mgmt-storage azure-mgmt-sql azure-mgmt-web "
                "azure-mgmt-containerservice azure-mgmt-network"
            )
        
        super().__init__(config)
        
        self.subscription_id = config.get("subscription_id")
        if not self.subscription_id:
            raise ValueError("Azure subscription_id is required")
        
        # Initialize Azure credentials
        self.credential = self._initialize_credentials(config)
        
        # Initialize Azure service clients
        self._initialize_clients()
        
        # Discovery configuration
        self.resource_groups = config.get("resource_groups", [])
        self.regions = config.get("regions", [])
        self.services = config.get("services", [
            "compute", "storage", "sql", "web", "containers", "network"
        ])
        
        logger.info(f"Initialized Azure discovery for subscription {self.subscription_id}")
    
    @property
    def provider(self) -> AssetProvider:
        """Get the asset provider for this discovery engine."""
        return AssetProvider.AZURE
    
    @property
    def discovery_source(self) -> DiscoverySource:
        """Get the discovery source for this discovery engine."""
        return DiscoverySource.AZURE_API
    
    @property
    def supported_asset_types(self) -> Set[AssetType]:
        """Get the set of asset types supported by Azure discovery."""
        return {
            AssetType.CLOUD_INSTANCE,
            AssetType.DATABASE,
            AssetType.STORAGE,
            AssetType.APPLICATION,
            AssetType.CONTAINER,
            AssetType.NETWORK_DEVICE,
            AssetType.LOAD_BALANCER
        }
    
    def _initialize_credentials(self, config: Dict[str, Any]) -> Any:
        """Initialize Azure authentication credentials."""
        client_id = config.get("client_id")
        client_secret = config.get("client_secret")
        tenant_id = config.get("tenant_id")
        
        if client_id and client_secret and tenant_id:
            # Service principal authentication
            return ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret
            )
        else:
            # Default credential chain (managed identity, CLI, etc.)
            return DefaultAzureCredential()
    
    def _initialize_clients(self) -> None:
        """Initialize Azure service management clients."""
        try:
            self.resource_client = ResourceManagementClient(
                self.credential, self.subscription_id
            )
            self.compute_client = ComputeManagementClient(
                self.credential, self.subscription_id
            )
            self.storage_client = StorageManagementClient(
                self.credential, self.subscription_id
            )
            self.sql_client = SqlManagementClient(
                self.credential, self.subscription_id
            )
            self.web_client = WebSiteManagementClient(
                self.credential, self.subscription_id
            )
            self.container_client = ContainerServiceClient(
                self.credential, self.subscription_id
            )
            self.network_client = NetworkManagementClient(
                self.credential, self.subscription_id
            )
            
        except ClientAuthenticationError as e:
            logger.error(f"Azure authentication failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Azure clients: {e}")
            raise
    
    async def validate_config(self) -> bool:
        """
        Validate Azure discovery configuration.
        
        Returns:
            bool: True if configuration is valid
        """
        try:
            # Test authentication by listing resource groups
            resource_groups = list(self.resource_client.resource_groups.list())
            logger.info(f"Azure validation successful. Found {len(resource_groups)} resource groups")
            return True
            
        except ClientAuthenticationError:
            logger.error("Azure authentication validation failed")
            return False
        except Exception as e:
            logger.error(f"Azure configuration validation failed: {e}")
            return False
    
    async def discover(self) -> DiscoveryResult:
        """
        Execute comprehensive Azure asset discovery.
        
        Returns:
            DiscoveryResult: Discovery results with assets and relationships
        """
        self._discovery_start_time = datetime.utcnow()
        self._log_discovery_event("Starting Azure asset discovery")
        
        try:
            # Discover different Azure service types
            discovery_tasks = []
            
            if "compute" in self.services:
                discovery_tasks.append(self._discover_virtual_machines())
            
            if "storage" in self.services:
                discovery_tasks.append(self._discover_storage_accounts())
            
            if "sql" in self.services:
                discovery_tasks.append(self._discover_sql_databases())
            
            if "web" in self.services:
                discovery_tasks.append(self._discover_web_apps())
            
            if "containers" in self.services:
                discovery_tasks.append(self._discover_container_services())
            
            if "network" in self.services:
                discovery_tasks.append(self._discover_network_resources())
            
            # Execute discovery tasks concurrently
            discovery_results = await asyncio.gather(*discovery_tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(discovery_results):
                if isinstance(result, Exception):
                    error_msg = f"Discovery task {i} failed: {result}"
                    self._log_discovery_event(error_msg, "error")
                else:
                    self._assets_discovered.extend(result.get("assets", []))
                    self._relationships_discovered.extend(result.get("relationships", []))
                    self._execution_log.extend(result.get("logs", []))
            
            # Discover cross-service relationships
            cross_service_relationships = await self._discover_cross_service_relationships()
            self._relationships_discovered.extend(cross_service_relationships)
            
            self._log_discovery_event(
                f"Azure discovery completed. Discovered {len(self._assets_discovered)} assets "
                f"and {len(self._relationships_discovered)} relationships"
            )
            
            return self._build_discovery_result()
            
        except Exception as e:
            error_msg = f"Azure discovery failed: {e}"
            self._log_discovery_event(error_msg, "error")
            return self._build_discovery_result()
    
    async def _discover_virtual_machines(self) -> Dict[str, Any]:
        """Discover Azure Virtual Machines."""
        self._log_discovery_event("Discovering Azure Virtual Machines")
        
        assets = []
        relationships = []
        logs = []
        
        try:
            # Get all VMs across resource groups
            if self.resource_groups:
                vms = []
                for rg in self.resource_groups:
                    rg_vms = list(self.compute_client.virtual_machines.list(rg))
                    vms.extend(rg_vms)
            else:
                vms = list(self.compute_client.virtual_machines.list_all())
            
            logs.append(f"Found {len(vms)} Virtual Machines")
            
            for vm in vms:
                try:
                    # Get VM instance view for runtime information
                    vm_instance = self.compute_client.virtual_machines.instance_view(
                        vm.id.split('/')[4],  # Resource group name
                        vm.name
                    )
                    
                    # Extract network interfaces and IP addresses
                    network_interfaces, ip_addresses = await self._get_vm_network_info(vm)
                    
                    # Calculate risk score based on VM configuration
                    risk_score = self._calculate_vm_risk_score(vm, vm_instance, ip_addresses)
                    
                    # Create asset record
                    asset_data = self._create_asset_data(
                        name=vm.name,
                        asset_type=AssetType.CLOUD_INSTANCE,
                        provider_id=vm.vm_id or vm.id,
                        provider_region=vm.location,
                        ip_addresses=ip_addresses,
                        hostname=vm.os_profile.computer_name if vm.os_profile else vm.name,
                        environment=self._determine_environment_from_tags(vm.tags or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "vm_size": vm.hardware_profile.vm_size if vm.hardware_profile else None,
                            "os_type": vm.storage_profile.os_disk.os_type.value if vm.storage_profile and vm.storage_profile.os_disk else None,
                            "resource_group": vm.id.split('/')[4],
                            "availability_zone": getattr(vm, 'zones', None),
                            "network_interfaces": network_interfaces,
                            "tags": vm.tags or {},
                            "power_state": self._get_vm_power_state(vm_instance),
                            "provisioning_state": vm.provisioning_state
                        },
                        metadata={
                            "azure_vm_id": vm.vm_id,
                            "azure_resource_id": vm.id,
                            "created_time": vm.time_created.isoformat() if vm.time_created else None,
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )
                    
                    assets.append(asset_data)
                    
                except Exception as e:
                    logs.append(f"Failed to process VM {vm.name}: {e}")
                    continue
            
            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }
            
        except Exception as e:
            error_msg = f"Failed to discover Azure VMs: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}
    
    async def _get_vm_network_info(self, vm: Any) -> tuple[List[str], List[str]]:
        """Extract network interface and IP address information for a VM."""
        network_interfaces = []
        ip_addresses = []
        
        if vm.network_profile and vm.network_profile.network_interfaces:
            for nic_ref in vm.network_profile.network_interfaces:
                nic_id = nic_ref.id
                nic_name = nic_id.split('/')[-1]
                nic_rg = nic_id.split('/')[4]
                
                try:
                    nic = self.network_client.network_interfaces.get(nic_rg, nic_name)
                    network_interfaces.append(nic_name)
                    
                    # Extract IP addresses
                    if nic.ip_configurations:
                        for ip_config in nic.ip_configurations:
                            if ip_config.private_ip_address:
                                ip_addresses.append(ip_config.private_ip_address)
                            if ip_config.public_ip_address:
                                # Get public IP details
                                pub_ip_id = ip_config.public_ip_address.id
                                pub_ip_name = pub_ip_id.split('/')[-1]
                                pub_ip_rg = pub_ip_id.split('/')[4]
                                
                                pub_ip = self.network_client.public_ip_addresses.get(pub_ip_rg, pub_ip_name)
                                if pub_ip.ip_address:
                                    ip_addresses.append(pub_ip.ip_address)
                                    
                except Exception as e:
                    logger.warning(f"Failed to get network interface {nic_name}: {e}")
        
        return network_interfaces, ip_addresses
    
    def _calculate_vm_risk_score(self, vm: Any, vm_instance: Any, ip_addresses: List[str]) -> int:
        """Calculate risk score for Azure VM based on configuration."""
        # Get base risk score
        environment = self._determine_environment_from_tags(vm.tags or {})
        has_public_ip = any(not self._is_private_ip_address(ip) for ip in ip_addresses)
        is_running = self._get_vm_power_state(vm_instance) == "running"
        
        risk_score = self._calculate_base_risk_score(
            AssetType.CLOUD_INSTANCE,
            environment,
            has_public_ip,
            is_running
        )
        
        # Azure-specific adjustments
        if vm.hardware_profile and vm.hardware_profile.vm_size:
            vm_size = vm.hardware_profile.vm_size.lower()
            if any(size in vm_size for size in ['standard_d', 'standard_f']):
                risk_score += 5  # General purpose VMs
            elif any(size in vm_size for size in ['standard_h', 'standard_n']):
                risk_score += 15  # High-performance VMs
        
        # Operating system considerations
        if vm.storage_profile and vm.storage_profile.os_disk:
            os_type = vm.storage_profile.os_disk.os_type
            if os_type and 'windows' in str(os_type).lower():
                risk_score += 5  # Windows systems may have more attack surface
        
        return min(max(risk_score, 0), 100)
    
    def _get_vm_power_state(self, vm_instance: Any) -> str:
        """Extract VM power state from instance view."""
        if not vm_instance or not vm_instance.statuses:
            return "unknown"
        
        for status in vm_instance.statuses:
            if status.code and status.code.startswith("PowerState/"):
                return status.code.replace("PowerState/", "")
        
        return "unknown"

    async def _discover_storage_accounts(self) -> Dict[str, Any]:
        """Discover Azure Storage Accounts."""
        self._log_discovery_event("Discovering Azure Storage Accounts")

        assets = []
        relationships = []
        logs = []

        try:
            # Get all storage accounts
            if self.resource_groups:
                storage_accounts = []
                for rg in self.resource_groups:
                    rg_accounts = list(self.storage_client.storage_accounts.list_by_resource_group(rg))
                    storage_accounts.extend(rg_accounts)
            else:
                storage_accounts = list(self.storage_client.storage_accounts.list())

            logs.append(f"Found {len(storage_accounts)} Storage Accounts")

            for account in storage_accounts:
                try:
                    rg_name = account.id.split('/')[4]

                    # Calculate risk score
                    risk_score = self._calculate_storage_risk_score(account)

                    asset_data = self._create_asset_data(
                        name=account.name,
                        asset_type=AssetType.STORAGE,
                        provider_id=account.id,
                        provider_region=account.location,
                        environment=self._determine_environment_from_tags(account.tags or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "sku_name": account.sku.name if account.sku else None,
                            "sku_tier": account.sku.tier.value if account.sku and account.sku.tier else None,
                            "kind": account.kind.value if account.kind else None,
                            "access_tier": account.access_tier.value if account.access_tier else None,
                            "https_traffic_only": account.enable_https_traffic_only,
                            "resource_group": rg_name,
                            "tags": account.tags or {},
                            "provisioning_state": account.provisioning_state.value if account.provisioning_state else None,
                            "creation_time": account.creation_time.isoformat() if account.creation_time else None
                        },
                        metadata={
                            "azure_resource_id": account.id,
                            "primary_endpoints": {
                                "blob": account.primary_endpoints.blob if account.primary_endpoints else None,
                                "file": account.primary_endpoints.file if account.primary_endpoints else None,
                                "queue": account.primary_endpoints.queue if account.primary_endpoints else None,
                                "table": account.primary_endpoints.table if account.primary_endpoints else None
                            },
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process storage account {account.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover Azure Storage Accounts: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_storage_risk_score(self, account: Any) -> int:
        """Calculate risk score for Azure Storage Account."""
        environment = self._determine_environment_from_tags(account.tags or {})

        # Base risk score for storage
        risk_score = self._calculate_base_risk_score(
            AssetType.STORAGE,
            environment,
            has_public_ip=True,  # Storage accounts are typically accessible via internet
            is_running=True
        )

        # HTTPS-only traffic
        if not account.enable_https_traffic_only:
            risk_score += 25  # Major security risk

        # Storage account kind
        if account.kind and account.kind.value == "Storage":
            risk_score += 10  # Classic storage accounts have more risks

        # Access tier
        if account.access_tier and account.access_tier.value == "Hot":
            risk_score += 5  # Hot tier is more frequently accessed

        return min(max(risk_score, 0), 100)

    async def _discover_sql_databases(self) -> Dict[str, Any]:
        """Discover Azure SQL Databases."""
        self._log_discovery_event("Discovering Azure SQL Databases")

        assets = []
        relationships = []
        logs = []

        try:
            # Get all SQL servers first
            if self.resource_groups:
                sql_servers = []
                for rg in self.resource_groups:
                    rg_servers = list(self.sql_client.servers.list_by_resource_group(rg))
                    sql_servers.extend(rg_servers)
            else:
                sql_servers = list(self.sql_client.servers.list())

            logs.append(f"Found {len(sql_servers)} SQL Servers")

            # Discover databases on each server
            for server in sql_servers:
                try:
                    rg_name = server.id.split('/')[4]
                    databases = list(self.sql_client.databases.list_by_server(rg_name, server.name))

                    for db in databases:
                        # Skip master database
                        if db.name == "master":
                            continue

                        risk_score = self._calculate_sql_risk_score(server, db)

                        asset_data = self._create_asset_data(
                            name=f"{server.name}/{db.name}",
                            asset_type=AssetType.DATABASE,
                            provider_id=db.id,
                            provider_region=server.location,
                            hostname=server.fully_qualified_domain_name,
                            environment=self._determine_environment_from_tags(db.tags or {}),
                            risk_score=risk_score,
                            risk_level=self._get_risk_level_from_score(risk_score),
                            configuration={
                                "server_name": server.name,
                                "database_name": db.name,
                                "sku_name": db.sku.name if db.sku else None,
                                "sku_tier": db.sku.tier if db.sku else None,
                                "collation": db.collation,
                                "max_size_bytes": db.max_size_bytes,
                                "resource_group": rg_name,
                                "tags": db.tags or {},
                                "server_version": server.version,
                                "server_admin": server.administrator_login
                            },
                            metadata={
                                "azure_resource_id": db.id,
                                "server_resource_id": server.id,
                                "creation_date": db.creation_date.isoformat() if db.creation_date else None,
                                "last_discovery": datetime.utcnow().isoformat()
                            }
                        )

                        assets.append(asset_data)

                        # Create relationship between database and server
                        relationships.append(
                            self._create_relationship_data(
                                source_provider_id=db.id,
                                target_provider_id=server.id,
                                relationship_type="hosted_on",
                                metadata={
                                    "server_name": server.name,
                                    "database_name": db.name
                                }
                            )
                        )

                except Exception as e:
                    logs.append(f"Failed to process SQL server {server.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover Azure SQL Databases: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_sql_risk_score(self, server: Any, database: Any) -> int:
        """Calculate risk score for Azure SQL Database."""
        environment = self._determine_environment_from_tags(database.tags or {})

        # Base risk score for databases
        risk_score = self._calculate_base_risk_score(
            AssetType.DATABASE,
            environment,
            has_public_ip=True,  # SQL databases are typically accessible via internet
            is_running=True
        )

        # Server version considerations
        if server.version and "12.0" not in server.version:
            risk_score += 10  # Older SQL Server versions

        # Database size considerations
        if database.max_size_bytes:
            size_gb = database.max_size_bytes / (1024**3)
            if size_gb > 100:
                risk_score += 10  # Large databases are higher value targets

        # SKU tier considerations
        if database.sku and database.sku.tier:
            tier = database.sku.tier.lower()
            if "premium" in tier or "business" in tier:
                risk_score += 15  # Premium tiers likely contain sensitive data

        return min(max(risk_score, 0), 100)

    async def _discover_web_apps(self) -> Dict[str, Any]:
        """Discover Azure Web Apps and App Services."""
        self._log_discovery_event("Discovering Azure Web Apps")

        assets = []
        relationships = []
        logs = []

        try:
            # Get all web apps
            if self.resource_groups:
                web_apps = []
                for rg in self.resource_groups:
                    rg_apps = list(self.web_client.web_apps.list_by_resource_group(rg))
                    web_apps.extend(rg_apps)
            else:
                web_apps = list(self.web_client.web_apps.list())

            logs.append(f"Found {len(web_apps)} Web Apps")

            for app in web_apps:
                try:
                    rg_name = app.id.split('/')[4]

                    # Get app configuration for security analysis
                    app_config = None
                    try:
                        app_config = self.web_client.web_apps.get_configuration(rg_name, app.name)
                    except Exception as e:
                        logs.append(f"Failed to get config for web app {app.name}: {e}")

                    risk_score = self._calculate_webapp_risk_score(app, app_config)

                    asset_data = self._create_asset_data(
                        name=app.name,
                        asset_type=AssetType.APPLICATION,
                        provider_id=app.id,
                        provider_region=app.location,
                        hostname=app.default_host_name,
                        environment=self._determine_environment_from_tags(app.tags or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "kind": app.kind,
                            "state": app.state,
                            "enabled": app.enabled,
                            "https_only": app.https_only,
                            "resource_group": rg_name,
                            "server_farm_id": app.server_farm_id,
                            "tags": app.tags or {},
                            "runtime_stack": app_config.linux_fx_version if app_config else None,
                            "always_on": app_config.always_on if app_config else None
                        },
                        metadata={
                            "azure_resource_id": app.id,
                            "default_hostname": app.default_host_name,
                            "outbound_ip_addresses": app.outbound_ip_addresses.split(',') if app.outbound_ip_addresses else [],
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process web app {app.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover Azure Web Apps: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_webapp_risk_score(self, app: Any, app_config: Any) -> int:
        """Calculate risk score for Azure Web App."""
        environment = self._determine_environment_from_tags(app.tags or {})

        # Base risk score for web applications
        risk_score = self._calculate_base_risk_score(
            AssetType.APPLICATION,
            environment,
            has_public_ip=True,  # Web apps are typically accessible via internet
            is_running=app.state and app.state.lower() == "running"
        )

        # HTTPS enforcement
        if not app.https_only:
            risk_score += 20  # Major security risk

        # Application state
        if app.state and app.state.lower() == "running":
            risk_score += 5  # Running apps are exposed

        # Always On configuration
        if app_config and app_config.always_on:
            risk_score += 5  # Always-on apps have larger attack window

        return min(max(risk_score, 0), 100)

    async def _discover_container_services(self) -> Dict[str, Any]:
        """Discover Azure Container Services (AKS)."""
        self._log_discovery_event("Discovering Azure Container Services")

        assets = []
        relationships = []
        logs = []

        try:
            # Get all managed clusters (AKS)
            if self.resource_groups:
                clusters = []
                for rg in self.resource_groups:
                    rg_clusters = list(self.container_client.managed_clusters.list_by_resource_group(rg))
                    clusters.extend(rg_clusters)
            else:
                clusters = list(self.container_client.managed_clusters.list())

            logs.append(f"Found {len(clusters)} AKS Clusters")

            for cluster in clusters:
                try:
                    rg_name = cluster.id.split('/')[4]

                    risk_score = self._calculate_aks_risk_score(cluster)

                    asset_data = self._create_asset_data(
                        name=cluster.name,
                        asset_type=AssetType.CONTAINER,
                        provider_id=cluster.id,
                        provider_region=cluster.location,
                        hostname=cluster.fqdn,
                        environment=self._determine_environment_from_tags(cluster.tags or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "kubernetes_version": cluster.kubernetes_version,
                            "node_resource_group": cluster.node_resource_group,
                            "dns_prefix": cluster.dns_prefix,
                            "resource_group": rg_name,
                            "tags": cluster.tags or {},
                            "provisioning_state": cluster.provisioning_state,
                            "power_state": cluster.power_state.code if cluster.power_state else None,
                            "agent_pool_profiles": [
                                {
                                    "name": pool.name,
                                    "count": pool.count,
                                    "vm_size": pool.vm_size,
                                    "os_type": pool.os_type
                                }
                                for pool in cluster.agent_pool_profiles or []
                            ]
                        },
                        metadata={
                            "azure_resource_id": cluster.id,
                            "fqdn": cluster.fqdn,
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process AKS cluster {cluster.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover Azure Container Services: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_aks_risk_score(self, cluster: Any) -> int:
        """Calculate risk score for Azure AKS cluster."""
        environment = self._determine_environment_from_tags(cluster.tags or {})

        # Base risk score for containers
        risk_score = self._calculate_base_risk_score(
            AssetType.CONTAINER,
            environment,
            has_public_ip=True,  # AKS clusters are typically accessible via internet
            is_running=cluster.power_state and cluster.power_state.code == "Running"
        )

        # Kubernetes version considerations
        if cluster.kubernetes_version:
            # Add risk for older versions (simplified check)
            version_parts = cluster.kubernetes_version.split('.')
            if len(version_parts) >= 2:
                try:
                    major, minor = int(version_parts[0]), int(version_parts[1])
                    if major == 1 and minor < 20:  # Older than 1.20
                        risk_score += 15
                except ValueError:
                    pass

        # Node pool considerations
        if cluster.agent_pool_profiles:
            total_nodes = sum(pool.count for pool in cluster.agent_pool_profiles if pool.count)
            if total_nodes > 10:
                risk_score += 10  # Large clusters are higher value targets

        return min(max(risk_score, 0), 100)

    async def _discover_network_resources(self) -> Dict[str, Any]:
        """Discover Azure Network Resources (Load Balancers, NSGs, etc.)."""
        self._log_discovery_event("Discovering Azure Network Resources")

        assets = []
        relationships = []
        logs = []

        try:
            # Discover Load Balancers
            if self.resource_groups:
                load_balancers = []
                for rg in self.resource_groups:
                    rg_lbs = list(self.network_client.load_balancers.list(rg))
                    load_balancers.extend(rg_lbs)
            else:
                load_balancers = list(self.network_client.load_balancers.list_all())

            logs.append(f"Found {len(load_balancers)} Load Balancers")

            for lb in load_balancers:
                try:
                    rg_name = lb.id.split('/')[4]

                    risk_score = self._calculate_lb_risk_score(lb)

                    asset_data = self._create_asset_data(
                        name=lb.name,
                        asset_type=AssetType.LOAD_BALANCER,
                        provider_id=lb.id,
                        provider_region=lb.location,
                        environment=self._determine_environment_from_tags(lb.tags or {}),
                        risk_score=risk_score,
                        risk_level=self._get_risk_level_from_score(risk_score),
                        configuration={
                            "sku_name": lb.sku.name if lb.sku else None,
                            "sku_tier": lb.sku.tier if lb.sku else None,
                            "resource_group": rg_name,
                            "tags": lb.tags or {},
                            "provisioning_state": lb.provisioning_state,
                            "frontend_ip_configurations": [
                                {
                                    "name": config.name,
                                    "private_ip_address": config.private_ip_address,
                                    "public_ip_address": config.public_ip_address.id if config.public_ip_address else None
                                }
                                for config in lb.frontend_ip_configurations or []
                            ],
                            "backend_address_pools": [
                                pool.name for pool in lb.backend_address_pools or []
                            ]
                        },
                        metadata={
                            "azure_resource_id": lb.id,
                            "last_discovery": datetime.utcnow().isoformat()
                        }
                    )

                    assets.append(asset_data)

                except Exception as e:
                    logs.append(f"Failed to process load balancer {lb.name}: {e}")
                    continue

            return {
                "assets": assets,
                "relationships": relationships,
                "logs": logs
            }

        except Exception as e:
            error_msg = f"Failed to discover Azure Network Resources: {e}"
            logs.append(error_msg)
            return {"assets": [], "relationships": [], "logs": logs}

    def _calculate_lb_risk_score(self, lb: Any) -> int:
        """Calculate risk score for Azure Load Balancer."""
        environment = self._determine_environment_from_tags(lb.tags or {})

        # Check if load balancer has public IP
        has_public_ip = False
        if lb.frontend_ip_configurations:
            for config in lb.frontend_ip_configurations:
                if config.public_ip_address:
                    has_public_ip = True
                    break

        # Base risk score for load balancers
        risk_score = self._calculate_base_risk_score(
            AssetType.LOAD_BALANCER,
            environment,
            has_public_ip,
            is_running=True
        )

        # SKU considerations
        if lb.sku and lb.sku.name:
            if lb.sku.name.lower() == "standard":
                risk_score += 5  # Standard SKU has more features and exposure

        return min(max(risk_score, 0), 100)

    async def _discover_cross_service_relationships(self) -> List[Dict[str, Any]]:
        """Discover relationships between different Azure services."""
        self._log_discovery_event("Discovering cross-service relationships")

        relationships = []

        try:
            # This is a simplified implementation
            # In a real implementation, you would analyze resource dependencies
            # based on ARM templates, resource references, network connections, etc.

            # For now, we'll create some basic relationships based on resource groups
            resource_groups = {}

            # Group assets by resource group
            for asset in self._assets_discovered:
                rg = asset.get("configuration", {}).get("resource_group")
                if rg:
                    if rg not in resource_groups:
                        resource_groups[rg] = []
                    resource_groups[rg].append(asset)

            # Create relationships within resource groups
            for rg_name, assets in resource_groups.items():
                for i, asset1 in enumerate(assets):
                    for asset2 in assets[i+1:]:
                        # Create a generic "same_resource_group" relationship
                        relationships.append(
                            self._create_relationship_data(
                                source_provider_id=asset1["provider_id"],
                                target_provider_id=asset2["provider_id"],
                                relationship_type="same_resource_group",
                                metadata={
                                    "resource_group": rg_name,
                                    "relationship_strength": 0.3
                                }
                            )
                        )

            self._log_discovery_event(f"Discovered {len(relationships)} cross-service relationships")

        except Exception as e:
            self._log_discovery_event(f"Failed to discover cross-service relationships: {e}", "error")

        return relationships
