"""Discovery orchestrator service for coordinating asset discovery operations."""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Type
from sqlalchemy.orm import Session

from app.db.models.asset import DiscoveryJob, DiscoveryJobStatus
from app.services.asset_service import AssetService
from app.services.discovery import (
    BaseDiscoveryEngine,
    CloudDiscoveryEngine,
    APIDiscoveryEngine,
    NetworkDiscoveryEngine,
    DiscoveryResult,
)

logger = logging.getLogger(__name__)


class DiscoveryOrchestrator:
    """Orchestrates discovery operations across different engines."""
    
    def __init__(self, db: Session):
        self.db = db
        self.asset_service = AssetService(db)
        self.active_jobs: Dict[str, asyncio.Task] = {}
        
        # Registry of discovery engines
        self.engine_registry: Dict[str, Type[BaseDiscoveryEngine]] = {
            "aws_discovery": CloudDiscoveryEngine,
            "azure_discovery": CloudDiscoveryEngine,
            "gcp_discovery": CloudDiscoveryEngine,
            "akto_discovery": APIDiscoveryEngine,
            "kiterunner_discovery": APIDiscoveryEngine,
            "network_discovery": NetworkDiscoveryEngine,
        }
    
    async def start_discovery_job(self, job_id: uuid.UUID) -> bool:
        """Start a discovery job asynchronously."""
        try:
            # Get the job from database
            job = self.asset_service.get_discovery_job(job_id)
            
            if job.status != DiscoveryJobStatus.PENDING:
                logger.error(f"Cannot start job {job_id} in status {job.status}")
                return False
            
            # Start the job
            self.asset_service.start_discovery_job(job_id)
            
            # Create and start the discovery task
            task = asyncio.create_task(self._execute_discovery_job(job))
            self.active_jobs[str(job_id)] = task
            
            logger.info(f"Started discovery job {job_id} ({job.job_type})")
            return True
            
        except Exception as e:
            logger.error(f"Error starting discovery job {job_id}: {e}")
            try:
                self.asset_service.fail_discovery_job(job_id, [{"error": str(e)}])
            except Exception as inner_e:
                logger.error(f"Failed to mark job {job_id} as failed: {inner_e}")
            return False
    
    async def _execute_discovery_job(self, job: DiscoveryJob) -> None:
        """Execute a discovery job."""
        job_id = job.id
        
        try:
            logger.info(f"Executing discovery job {job_id} ({job.job_type})")
            
            # Get the appropriate discovery engine
            engine_class = self.engine_registry.get(job.job_type)
            if not engine_class:
                raise ValueError(f"Unknown job type: {job.job_type}")
            
            # Prepare configuration
            config = job.configuration or {}
            
            # Add job-specific configuration
            if job.job_type.endswith("_discovery"):
                provider = job.job_type.replace("_discovery", "")
                config["provider"] = provider
            
            if job.job_type in ["akto_discovery", "kiterunner_discovery"]:
                config["tool"] = job.job_type.replace("_discovery", "")
            
            # Create and validate the discovery engine
            engine = engine_class(config)
            
            if not await engine.validate_config():
                raise ValueError("Invalid discovery configuration")
            
            # Execute discovery
            result = await engine.discover()
            
            # Process the results
            await self._process_discovery_results(job_id, result, engine)
            
            # Complete the job
            self.asset_service.complete_discovery_job(
                job_id=job_id,
                assets_discovered=result.total_assets,
                assets_updated=len(result.assets_updated),
                relationships_discovered=result.total_relationships,
                errors_count=result.total_errors,
                execution_log=result.execution_log,
                error_details=result.errors
            )
            
            logger.info(f"Completed discovery job {job_id}")
            
        except Exception as e:
            logger.error(f"Discovery job {job_id} failed: {e}")
            try:
                self.asset_service.fail_discovery_job(
                    job_id,
                    [{"error": str(e), "timestamp": datetime.utcnow().isoformat()}]
                )
            except Exception as inner_e:
                logger.error(f"Failed to mark job {job_id} as failed: {inner_e}")
        
        finally:
            # Remove from active jobs
            if str(job_id) in self.active_jobs:
                del self.active_jobs[str(job_id)]
    
    async def _process_discovery_results(self, job_id: uuid.UUID, result: DiscoveryResult, engine: BaseDiscoveryEngine) -> None:
        """Process discovery results and update the database."""
        try:
            # Create or update discovered assets
            for asset_data in result.assets_discovered:
                try:
                    # Check if asset already exists
                    provider_id = asset_data.get("provider_id")
                    provider = asset_data.get("provider")
                    
                    if provider_id and provider:
                        existing_asset = self.asset_service.get_asset_by_provider_id(provider, provider_id)
                        
                        if existing_asset:
                            # Update existing asset
                            from app.schemas.asset import AssetUpdate
                            update_data = AssetUpdate(**{
                                k: v for k, v in asset_data.items() 
                                if k not in ["id", "created_at", "updated_at"]
                            })
                            self.asset_service.update_asset(existing_asset.id, update_data)
                            logger.debug(f"Updated existing asset: {existing_asset.name}")
                        else:
                            # Create new asset
                            from app.schemas.asset import AssetCreate
                            asset_data["discovery_job_id"] = job_id
                            create_data = AssetCreate(**asset_data)
                            new_asset = self.asset_service.create_asset(create_data)
                            logger.debug(f"Created new asset: {new_asset.name}")
                    else:
                        # Create asset without provider_id check
                        from app.schemas.asset import AssetCreate
                        asset_data["discovery_job_id"] = job_id
                        create_data = AssetCreate(**asset_data)
                        new_asset = self.asset_service.create_asset(create_data)
                        logger.debug(f"Created new asset: {new_asset.name}")
                        
                except Exception as e:
                    logger.error(f"Error processing asset {asset_data.get('name', 'unknown')}: {e}")
            
            # Create discovered relationships
            for relationship_data in result.relationships_discovered:
                try:
                    from app.schemas.asset import AssetRelationshipCreate
                    create_data = AssetRelationshipCreate(**relationship_data)
                    self.asset_service.create_relationship(create_data)
                    logger.debug(f"Created relationship: {relationship_data.get('relationship_type')}")
                    
                except Exception as e:
                    logger.error(f"Error processing relationship: {e}")
            
            logger.info(f"Processed {len(result.assets_discovered)} assets and {len(result.relationships_discovered)} relationships for job {job_id}")
            
        except Exception as e:
            logger.error(f"Error processing discovery results for job {job_id}: {e}")
            raise
    
    async def cancel_discovery_job(self, job_id: uuid.UUID) -> bool:
        """Cancel a running discovery job."""
        try:
            job_id_str = str(job_id)
            
            if job_id_str in self.active_jobs:
                # Cancel the task
                task = self.active_jobs[job_id_str]
                task.cancel()
                
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                
                # Update job status
                job = self.asset_service.get_discovery_job(job_id)
                job.status = DiscoveryJobStatus.CANCELLED
                job.completed_at = datetime.utcnow()
                
                if job.started_at:
                    job.duration_seconds = int((job.completed_at - job.started_at).total_seconds())
                
                self.db.commit()
                
                # Remove from active jobs
                del self.active_jobs[job_id_str]
                
                logger.info(f"Cancelled discovery job {job_id}")
                return True
            else:
                logger.warning(f"Discovery job {job_id} is not running")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling discovery job {job_id}: {e}")
            return False
    
    def get_active_jobs(self) -> List[str]:
        """Get list of active job IDs."""
        return list(self.active_jobs.keys())
    
    def is_job_running(self, job_id: uuid.UUID) -> bool:
        """Check if a job is currently running."""
        return str(job_id) in self.active_jobs
    
    async def get_job_status(self, job_id: uuid.UUID) -> Dict[str, Any]:
        """Get detailed status of a discovery job."""
        try:
            job = self.asset_service.get_discovery_job(job_id)
            
            status_info = {
                "job_id": str(job_id),
                "name": job.name,
                "job_type": job.job_type,
                "status": job.status.value,
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "duration_seconds": job.duration_seconds,
                "assets_discovered": job.assets_discovered,
                "assets_updated": job.assets_updated,
                "relationships_discovered": job.relationships_discovered,
                "errors_count": job.errors_count,
                "is_running": self.is_job_running(job_id),
            }
            
            # Add progress information for running jobs
            if self.is_job_running(job_id):
                task = self.active_jobs[str(job_id)]
                status_info["task_done"] = task.done()
                status_info["task_cancelled"] = task.cancelled()
            
            return status_info
            
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {e}")
            return {"error": str(e)}
    
    async def cleanup_completed_jobs(self, max_age_hours: int = 24) -> int:
        """Clean up old completed discovery jobs."""
        try:
            from datetime import timedelta
            cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            
            # Get old completed jobs
            old_jobs = self.db.query(DiscoveryJob).filter(
                DiscoveryJob.status.in_([
                    DiscoveryJobStatus.COMPLETED,
                    DiscoveryJobStatus.FAILED,
                    DiscoveryJobStatus.CANCELLED
                ]),
                DiscoveryJob.completed_at < cutoff_time
            ).all()
            
            cleaned_count = 0
            for job in old_jobs:
                try:
                    self.asset_service.delete_discovery_job(job.id)
                    cleaned_count += 1
                except Exception as e:
                    logger.error(f"Error deleting old job {job.id}: {e}")
            
            logger.info(f"Cleaned up {cleaned_count} old discovery jobs")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old jobs: {e}")
            return 0
    
    async def get_discovery_statistics(self) -> Dict[str, Any]:
        """Get discovery statistics."""
        try:
            stats = {
                "active_jobs": len(self.active_jobs),
                "active_job_ids": list(self.active_jobs.keys()),
            }
            
            # Get job statistics from database
            job_stats = self.asset_service.get_asset_statistics()
            stats.update(job_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting discovery statistics: {e}")
            return {"error": str(e)}


# Global orchestrator instance
_orchestrator: Optional[DiscoveryOrchestrator] = None


def get_discovery_orchestrator(db: Session) -> DiscoveryOrchestrator:
    """Get or create the global discovery orchestrator instance."""
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = DiscoveryOrchestrator(db)
    return _orchestrator
