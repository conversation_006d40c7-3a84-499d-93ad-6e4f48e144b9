"""
Advanced Threat Modeling Service

This service provides sophisticated threat modeling capabilities including
quantitative risk assessment, threat actor profiling, and attack simulation.
"""

import logging
import asyncio
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import json
import numpy as np
from collections import defaultdict

from app.services.attack_path_analyzer import AttackPathAnalyzer, AttackScenario
from app.services.graph_engine import AttackPath, AttackTechnique, PathType
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class ThreatActorType(Enum):
    """Types of threat actors."""
    NATION_STATE = "nation_state"
    CYBERCRIMINAL = "cybercriminal"
    HACKTIVIST = "hacktivist"
    INSIDER_THREAT = "insider_threat"
    SCRIPT_KIDDIE = "script_kiddie"
    ADVANCED_PERSISTENT_THREAT = "apt"


class AttackMotivation(Enum):
    """Attack motivations."""
    FINANCIAL_GAIN = "financial_gain"
    ESPIONAGE = "espionage"
    SABOTAGE = "sabotage"
    IDEOLOGY = "ideology"
    REVENGE = "revenge"
    CURIOSITY = "curiosity"
    REPUTATION = "reputation"


@dataclass
class ThreatActorProfile:
    """Comprehensive threat actor profile."""
    
    actor_id: str
    name: str
    actor_type: ThreatActorType
    sophistication_level: float  # 0.0 to 1.0
    motivation: AttackMotivation
    target_sectors: List[str]
    preferred_techniques: List[AttackTechnique]
    resource_level: float  # 0.0 to 1.0
    stealth_capability: float  # 0.0 to 1.0
    persistence_level: float  # 0.0 to 1.0
    known_campaigns: List[str]
    attribution_confidence: float  # 0.0 to 1.0
    last_activity: datetime
    geographic_origin: Optional[str] = None
    
    @property
    def threat_score(self) -> float:
        """Calculate overall threat score."""
        return (
            self.sophistication_level * 0.3 +
            self.resource_level * 0.25 +
            self.stealth_capability * 0.25 +
            self.persistence_level * 0.2
        ) * 100


@dataclass
class QuantitativeRiskAssessment:
    """Quantitative risk assessment results."""
    
    asset_id: str
    annual_loss_expectancy: float  # ALE in USD
    single_loss_expectancy: float  # SLE in USD
    annual_rate_of_occurrence: float  # ARO
    exposure_factor: float  # EF (0.0 to 1.0)
    asset_value: float  # AV in USD
    threat_frequency: float  # Events per year
    vulnerability_score: float  # 0.0 to 1.0
    control_effectiveness: float  # 0.0 to 1.0
    residual_risk: float  # After controls
    risk_tolerance: float  # Acceptable risk level
    
    @property
    def risk_level(self) -> str:
        """Determine risk level category."""
        if self.annual_loss_expectancy >= 1000000:
            return "CRITICAL"
        elif self.annual_loss_expectancy >= 500000:
            return "HIGH"
        elif self.annual_loss_expectancy >= 100000:
            return "MEDIUM"
        else:
            return "LOW"
    
    @property
    def exceeds_tolerance(self) -> bool:
        """Check if risk exceeds tolerance."""
        return self.residual_risk > self.risk_tolerance


@dataclass
class AttackSimulationResult:
    """Results from attack simulation."""
    
    simulation_id: str
    scenario_name: str
    threat_actor: ThreatActorProfile
    success_probability: float
    detection_probability: float
    time_to_compromise: float  # hours
    time_to_detection: float  # hours
    impact_assessment: Dict[str, float]
    affected_assets: Set[str]
    data_exfiltrated: float  # GB
    service_downtime: float  # hours
    financial_impact: float  # USD
    reputation_impact: float  # 0.0 to 1.0
    regulatory_violations: List[str]
    simulation_timestamp: datetime


class ThreatModelingService:
    """Advanced threat modeling and risk quantification service."""
    
    def __init__(self, db: Session):
        self.db = db
        self.attack_analyzer = AttackPathAnalyzer(db)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Threat actor database
        self.threat_actors = self._load_threat_actor_profiles()
        
        # Risk assessment parameters
        self.risk_parameters = self._load_risk_parameters()
        
        # Simulation cache
        self.simulation_cache = {}
    
    def _load_threat_actor_profiles(self) -> Dict[str, ThreatActorProfile]:
        """Load known threat actor profiles."""
        profiles = {}
        
        # APT Groups
        profiles["apt29"] = ThreatActorProfile(
            actor_id="apt29",
            name="APT29 (Cozy Bear)",
            actor_type=ThreatActorType.NATION_STATE,
            sophistication_level=0.95,
            motivation=AttackMotivation.ESPIONAGE,
            target_sectors=["government", "healthcare", "technology"],
            preferred_techniques=[
                AttackTechnique.INITIAL_ACCESS,
                AttackTechnique.PERSISTENCE,
                AttackTechnique.LATERAL_MOVEMENT,
                AttackTechnique.COLLECTION
            ],
            resource_level=0.9,
            stealth_capability=0.95,
            persistence_level=0.9,
            known_campaigns=["SolarWinds", "COVID-19 Research"],
            attribution_confidence=0.85,
            last_activity=datetime.utcnow() - timedelta(days=30),
            geographic_origin="Russia"
        )
        
        profiles["apt28"] = ThreatActorProfile(
            actor_id="apt28",
            name="APT28 (Fancy Bear)",
            actor_type=ThreatActorType.NATION_STATE,
            sophistication_level=0.9,
            motivation=AttackMotivation.ESPIONAGE,
            target_sectors=["government", "military", "media"],
            preferred_techniques=[
                AttackTechnique.INITIAL_ACCESS,
                AttackTechnique.CREDENTIAL_ACCESS,
                AttackTechnique.LATERAL_MOVEMENT
            ],
            resource_level=0.85,
            stealth_capability=0.8,
            persistence_level=0.85,
            known_campaigns=["DNC Hack", "Olympic Destroyer"],
            attribution_confidence=0.9,
            last_activity=datetime.utcnow() - timedelta(days=15),
            geographic_origin="Russia"
        )
        
        # Cybercriminal Groups
        profiles["fin7"] = ThreatActorProfile(
            actor_id="fin7",
            name="FIN7",
            actor_type=ThreatActorType.CYBERCRIMINAL,
            sophistication_level=0.8,
            motivation=AttackMotivation.FINANCIAL_GAIN,
            target_sectors=["retail", "hospitality", "financial"],
            preferred_techniques=[
                AttackTechnique.INITIAL_ACCESS,
                AttackTechnique.CREDENTIAL_ACCESS,
                AttackTechnique.COLLECTION
            ],
            resource_level=0.7,
            stealth_capability=0.75,
            persistence_level=0.6,
            known_campaigns=["Carbanak", "Point-of-Sale Attacks"],
            attribution_confidence=0.8,
            last_activity=datetime.utcnow() - timedelta(days=60),
            geographic_origin="Unknown"
        )
        
        # Insider Threat
        profiles["malicious_insider"] = ThreatActorProfile(
            actor_id="malicious_insider",
            name="Malicious Insider",
            actor_type=ThreatActorType.INSIDER_THREAT,
            sophistication_level=0.6,
            motivation=AttackMotivation.REVENGE,
            target_sectors=["all"],
            preferred_techniques=[
                AttackTechnique.COLLECTION,
                AttackTechnique.EXFILTRATION,
                AttackTechnique.IMPACT
            ],
            resource_level=0.5,
            stealth_capability=0.8,  # High due to legitimate access
            persistence_level=0.7,
            known_campaigns=["Data Theft", "Sabotage"],
            attribution_confidence=0.7,
            last_activity=datetime.utcnow() - timedelta(days=1),
            geographic_origin="Internal"
        )
        
        return profiles
    
    def _load_risk_parameters(self) -> Dict[str, Any]:
        """Load risk assessment parameters."""
        return {
            "asset_values": {
                "database": 1000000,
                "server": 500000,
                "workstation": 50000,
                "network_device": 100000,
                "application": 250000
            },
            "exposure_factors": {
                "data_breach": 0.8,
                "service_disruption": 0.6,
                "system_compromise": 0.7,
                "data_corruption": 0.9
            },
            "threat_frequencies": {
                "external_attack": 12,  # per year
                "insider_threat": 2,
                "malware": 24,
                "phishing": 52
            },
            "control_effectiveness": {
                "firewall": 0.7,
                "ids_ips": 0.6,
                "antivirus": 0.5,
                "encryption": 0.8,
                "access_control": 0.75,
                "monitoring": 0.65
            }
        }

    async def perform_quantitative_risk_assessment(self, asset_id: str) -> QuantitativeRiskAssessment:
        """Perform quantitative risk assessment for an asset."""
        self.logger.info(f"Performing quantitative risk assessment for asset {asset_id}")

        # Get asset metadata
        await self.attack_analyzer.initialize_from_database()
        asset_metadata = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id)

        if not asset_metadata:
            raise ValueError(f"Asset {asset_id} not found")

        # Calculate asset value
        asset_type = asset_metadata.get("asset_type", "server")
        base_value = self.risk_parameters["asset_values"].get(asset_type, 500000)

        # Adjust value based on business criticality
        criticality_multiplier = {
            "critical": 2.0,
            "high": 1.5,
            "medium": 1.0,
            "low": 0.5
        }
        business_criticality = asset_metadata.get("business_criticality", "medium")
        asset_value = base_value * criticality_multiplier.get(business_criticality, 1.0)

        # Calculate vulnerability score
        vulnerability_score = await self._calculate_vulnerability_score(asset_id, asset_metadata)

        # Calculate threat frequency
        threat_frequency = await self._calculate_threat_frequency(asset_id, asset_metadata)

        # Calculate control effectiveness
        control_effectiveness = await self._calculate_control_effectiveness(asset_id, asset_metadata)

        # Calculate exposure factor
        exposure_factor = self._calculate_exposure_factor(asset_metadata)

        # Calculate Single Loss Expectancy (SLE)
        single_loss_expectancy = asset_value * exposure_factor

        # Calculate Annual Rate of Occurrence (ARO)
        annual_rate_of_occurrence = threat_frequency * vulnerability_score * (1 - control_effectiveness)

        # Calculate Annual Loss Expectancy (ALE)
        annual_loss_expectancy = single_loss_expectancy * annual_rate_of_occurrence

        # Calculate residual risk
        residual_risk = annual_loss_expectancy / asset_value

        # Risk tolerance (configurable per organization)
        risk_tolerance = 0.1  # 10% of asset value

        return QuantitativeRiskAssessment(
            asset_id=asset_id,
            annual_loss_expectancy=annual_loss_expectancy,
            single_loss_expectancy=single_loss_expectancy,
            annual_rate_of_occurrence=annual_rate_of_occurrence,
            exposure_factor=exposure_factor,
            asset_value=asset_value,
            threat_frequency=threat_frequency,
            vulnerability_score=vulnerability_score,
            control_effectiveness=control_effectiveness,
            residual_risk=residual_risk,
            risk_tolerance=risk_tolerance
        )

    async def simulate_attack_scenario(self, threat_actor_id: str,
                                     target_assets: List[str],
                                     scenario_name: str = None) -> AttackSimulationResult:
        """Simulate an attack scenario with a specific threat actor."""
        self.logger.info(f"Simulating attack scenario with {threat_actor_id}")

        threat_actor = self.threat_actors.get(threat_actor_id)
        if not threat_actor:
            raise ValueError(f"Threat actor {threat_actor_id} not found")

        scenario_name = scenario_name or f"Simulation_{threat_actor_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        # Initialize attack analyzer
        await self.attack_analyzer.initialize_from_database()

        # Find potential entry points
        entry_points = await self._identify_entry_points(threat_actor)

        # Calculate success probability
        success_probability = await self._calculate_attack_success_probability(
            threat_actor, target_assets, entry_points
        )

        # Calculate detection probability
        detection_probability = await self._calculate_detection_probability(
            threat_actor, target_assets
        )

        # Estimate time to compromise
        time_to_compromise = await self._estimate_time_to_compromise(
            threat_actor, target_assets, entry_points
        )

        # Estimate time to detection
        time_to_detection = await self._estimate_time_to_detection(
            threat_actor, target_assets
        )

        # Assess impact
        impact_assessment = await self._assess_attack_impact(
            threat_actor, target_assets
        )

        # Calculate affected assets (blast radius)
        affected_assets = set()
        for target in target_assets:
            blast_result = await self.attack_analyzer.calculate_blast_radius(target, max_degrees=3)
            affected_assets.update(blast_result.affected_assets)

        # Estimate data exfiltration
        data_exfiltrated = self._estimate_data_exfiltration(threat_actor, affected_assets)

        # Calculate service downtime
        service_downtime = self._calculate_service_downtime(threat_actor, affected_assets)

        # Calculate financial impact
        financial_impact = self._calculate_financial_impact(
            threat_actor, affected_assets, service_downtime
        )

        # Assess reputation impact
        reputation_impact = self._assess_reputation_impact(threat_actor, affected_assets)

        # Identify regulatory violations
        regulatory_violations = self._identify_regulatory_violations(affected_assets)

        simulation_result = AttackSimulationResult(
            simulation_id=f"sim_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            scenario_name=scenario_name,
            threat_actor=threat_actor,
            success_probability=success_probability,
            detection_probability=detection_probability,
            time_to_compromise=time_to_compromise,
            time_to_detection=time_to_detection,
            impact_assessment=impact_assessment,
            affected_assets=affected_assets,
            data_exfiltrated=data_exfiltrated,
            service_downtime=service_downtime,
            financial_impact=financial_impact,
            reputation_impact=reputation_impact,
            regulatory_violations=regulatory_violations,
            simulation_timestamp=datetime.utcnow()
        )

        # Cache result
        self.simulation_cache[simulation_result.simulation_id] = simulation_result

        return simulation_result

    async def _calculate_vulnerability_score(self, asset_id: str, asset_metadata: Dict) -> float:
        """Calculate vulnerability score for an asset."""
        base_score = 0.5  # Base vulnerability

        # Adjust based on asset type
        asset_type = asset_metadata.get("asset_type", "server")
        type_multipliers = {
            "database": 1.2,  # Databases are high-value targets
            "server": 1.0,
            "workstation": 0.8,
            "network_device": 1.1,
            "application": 1.0
        }

        # Adjust based on public exposure
        if asset_metadata.get("public_facing", False):
            base_score += 0.3

        # Adjust based on patch level (simulated)
        patch_level = asset_metadata.get("patch_level", 0.7)  # 0.0 to 1.0
        base_score += (1.0 - patch_level) * 0.2

        # Adjust based on configuration security
        config_score = asset_metadata.get("security_config_score", 0.7)
        base_score += (1.0 - config_score) * 0.15

        vulnerability_score = base_score * type_multipliers.get(asset_type, 1.0)
        return min(vulnerability_score, 1.0)  # Cap at 1.0

    async def _calculate_threat_frequency(self, asset_id: str, asset_metadata: Dict) -> float:
        """Calculate threat frequency for an asset."""
        base_frequency = 12  # Base attacks per year

        # Adjust based on asset visibility
        if asset_metadata.get("public_facing", False):
            base_frequency *= 2.0

        # Adjust based on business criticality
        criticality_multipliers = {
            "critical": 1.5,
            "high": 1.2,
            "medium": 1.0,
            "low": 0.7
        }
        business_criticality = asset_metadata.get("business_criticality", "medium")
        base_frequency *= criticality_multipliers.get(business_criticality, 1.0)

        # Adjust based on data sensitivity
        if asset_metadata.get("contains_pii", False):
            base_frequency *= 1.3

        return base_frequency

    async def _calculate_control_effectiveness(self, asset_id: str, asset_metadata: Dict) -> float:
        """Calculate security control effectiveness."""
        total_effectiveness = 0.0
        control_count = 0

        # Check for various security controls
        controls = {
            "firewall_protected": 0.7,
            "ids_protected": 0.6,
            "antivirus_enabled": 0.5,
            "encryption_enabled": 0.8,
            "access_control_enabled": 0.75,
            "monitoring_enabled": 0.65,
            "backup_enabled": 0.4,
            "patch_management": 0.6
        }

        for control, effectiveness in controls.items():
            if asset_metadata.get(control, False):
                total_effectiveness += effectiveness
                control_count += 1

        if control_count == 0:
            return 0.1  # Minimal baseline protection

        # Average effectiveness with diminishing returns
        avg_effectiveness = total_effectiveness / len(controls)
        return min(avg_effectiveness, 0.9)  # Cap at 90% effectiveness

    def _calculate_exposure_factor(self, asset_metadata: Dict) -> float:
        """Calculate exposure factor based on asset characteristics."""
        base_exposure = 0.6

        # Adjust based on data sensitivity
        if asset_metadata.get("contains_pii", False):
            base_exposure += 0.2

        if asset_metadata.get("data_classification") == "restricted":
            base_exposure += 0.15

        # Adjust based on business criticality
        criticality_adjustments = {
            "critical": 0.2,
            "high": 0.1,
            "medium": 0.0,
            "low": -0.1
        }
        business_criticality = asset_metadata.get("business_criticality", "medium")
        base_exposure += criticality_adjustments.get(business_criticality, 0.0)

        return min(base_exposure, 1.0)

    async def _identify_entry_points(self, threat_actor: ThreatActorProfile) -> List[str]:
        """Identify potential entry points for a threat actor."""
        entry_points = []

        # Get all assets
        all_assets = self.attack_analyzer.graph_engine.asset_metadata

        for asset_id, asset_data in all_assets.items():
            # Check if asset is suitable as entry point
            if self._is_suitable_entry_point(asset_data, threat_actor):
                entry_points.append(asset_id)

        return entry_points

    def _is_suitable_entry_point(self, asset_data: Dict, threat_actor: ThreatActorProfile) -> bool:
        """Check if an asset is suitable as an entry point for a threat actor."""
        # External threat actors prefer public-facing assets
        if threat_actor.actor_type != ThreatActorType.INSIDER_THREAT:
            if not asset_data.get("public_facing", False):
                return False

        # Sophisticated actors can target any asset
        if threat_actor.sophistication_level >= 0.8:
            return True

        # Less sophisticated actors prefer easier targets
        if asset_data.get("security_config_score", 0.7) < 0.5:
            return True

        return asset_data.get("public_facing", False)

    async def _calculate_attack_success_probability(self, threat_actor: ThreatActorProfile,
                                                  target_assets: List[str],
                                                  entry_points: List[str]) -> float:
        """Calculate probability of attack success."""
        if not entry_points:
            return 0.0

        # Base success probability based on threat actor capabilities
        base_probability = (
            threat_actor.sophistication_level * 0.4 +
            threat_actor.resource_level * 0.3 +
            threat_actor.persistence_level * 0.3
        )

        # Adjust based on target difficulty
        target_difficulty = 0.0
        for target_id in target_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(target_id, {})
            security_score = asset_data.get("security_config_score", 0.7)
            target_difficulty += security_score

        avg_target_difficulty = target_difficulty / len(target_assets) if target_assets else 0.5

        # Adjust probability based on target difficulty
        success_probability = base_probability * (1.0 - avg_target_difficulty * 0.5)

        return min(max(success_probability, 0.05), 0.95)  # Bound between 5% and 95%

    async def _calculate_detection_probability(self, threat_actor: ThreatActorProfile,
                                             target_assets: List[str]) -> float:
        """Calculate probability of attack detection."""
        # Base detection probability
        base_detection = 0.3

        # Adjust based on threat actor stealth
        stealth_factor = 1.0 - threat_actor.stealth_capability
        detection_probability = base_detection + (stealth_factor * 0.4)

        # Adjust based on monitoring coverage
        monitoring_coverage = 0.0
        for target_id in target_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(target_id, {})
            if asset_data.get("monitoring_enabled", False):
                monitoring_coverage += 1.0

        avg_monitoring = monitoring_coverage / len(target_assets) if target_assets else 0.0
        detection_probability += avg_monitoring * 0.3

        return min(max(detection_probability, 0.1), 0.9)

    async def _estimate_time_to_compromise(self, threat_actor: ThreatActorProfile,
                                         target_assets: List[str],
                                         entry_points: List[str]) -> float:
        """Estimate time to compromise in hours."""
        # Base time based on threat actor sophistication
        base_time = 72  # 3 days for average actor

        # Sophisticated actors are faster
        sophistication_factor = 1.0 - threat_actor.sophistication_level
        time_to_compromise = base_time * (0.5 + sophistication_factor * 0.5)

        # Adjust based on target security
        security_factor = 0.0
        for target_id in target_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(target_id, {})
            security_factor += asset_data.get("security_config_score", 0.7)

        avg_security = security_factor / len(target_assets) if target_assets else 0.5
        time_to_compromise *= (0.5 + avg_security)

        return max(time_to_compromise, 1.0)  # Minimum 1 hour

    async def _estimate_time_to_detection(self, threat_actor: ThreatActorProfile,
                                        target_assets: List[str]) -> float:
        """Estimate time to detection in hours."""
        # Base detection time
        base_detection_time = 168  # 1 week average

        # Stealth actors take longer to detect
        stealth_factor = threat_actor.stealth_capability
        detection_time = base_detection_time * (0.5 + stealth_factor * 1.5)

        # Better monitoring reduces detection time
        monitoring_factor = 0.0
        for target_id in target_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(target_id, {})
            if asset_data.get("monitoring_enabled", False):
                monitoring_factor += 1.0

        avg_monitoring = monitoring_factor / len(target_assets) if target_assets else 0.0
        detection_time *= (1.0 - avg_monitoring * 0.6)

        return max(detection_time, 0.5)  # Minimum 30 minutes

    async def _assess_attack_impact(self, threat_actor: ThreatActorProfile,
                                  target_assets: List[str]) -> Dict[str, float]:
        """Assess various impact categories."""
        impact = {
            "confidentiality": 0.0,
            "integrity": 0.0,
            "availability": 0.0,
            "financial": 0.0,
            "reputation": 0.0,
            "regulatory": 0.0
        }

        for target_id in target_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(target_id, {})

            # Confidentiality impact
            if asset_data.get("contains_pii", False):
                impact["confidentiality"] += 0.8
            if asset_data.get("data_classification") == "restricted":
                impact["confidentiality"] += 0.6

            # Integrity impact
            if asset_data.get("asset_type") == "database":
                impact["integrity"] += 0.7

            # Availability impact
            criticality = asset_data.get("business_criticality", "medium")
            criticality_scores = {"critical": 0.9, "high": 0.7, "medium": 0.5, "low": 0.3}
            impact["availability"] += criticality_scores.get(criticality, 0.5)

            # Financial impact
            impact["financial"] += asset_data.get("risk_score", 50) / 100.0

            # Reputation impact
            if asset_data.get("public_facing", False):
                impact["reputation"] += 0.6

            # Regulatory impact
            if asset_data.get("contains_pii", False):
                impact["regulatory"] += 0.8

        # Normalize by number of assets
        for key in impact:
            impact[key] = min(impact[key] / len(target_assets), 1.0) if target_assets else 0.0

        return impact

    def _estimate_data_exfiltration(self, threat_actor: ThreatActorProfile,
                                  affected_assets: Set[str]) -> float:
        """Estimate data exfiltration in GB."""
        total_data = 0.0

        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})

            # Estimate data volume based on asset type
            asset_type = asset_data.get("asset_type", "server")
            data_volumes = {
                "database": 1000,  # GB
                "server": 100,
                "workstation": 50,
                "application": 200
            }

            base_volume = data_volumes.get(asset_type, 100)

            # Adjust based on threat actor motivation
            if threat_actor.motivation == AttackMotivation.ESPIONAGE:
                base_volume *= 1.5
            elif threat_actor.motivation == AttackMotivation.FINANCIAL_GAIN:
                base_volume *= 1.2

            total_data += base_volume

        return total_data

    def _calculate_service_downtime(self, threat_actor: ThreatActorProfile,
                                  affected_assets: Set[str]) -> float:
        """Calculate service downtime in hours."""
        max_downtime = 0.0

        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})

            # Base downtime based on asset criticality
            criticality = asset_data.get("business_criticality", "medium")
            base_downtime = {
                "critical": 24,  # hours
                "high": 12,
                "medium": 6,
                "low": 2
            }.get(criticality, 6)

            # Adjust based on attack motivation
            if threat_actor.motivation == AttackMotivation.SABOTAGE:
                base_downtime *= 3
            elif threat_actor.motivation == AttackMotivation.FINANCIAL_GAIN:
                base_downtime *= 0.5  # Want to stay undetected

            max_downtime = max(max_downtime, base_downtime)

        return max_downtime

    def _calculate_financial_impact(self, threat_actor: ThreatActorProfile,
                                  affected_assets: Set[str],
                                  service_downtime: float) -> float:
        """Calculate financial impact in USD."""
        total_impact = 0.0

        # Direct asset value impact
        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})
            asset_type = asset_data.get("asset_type", "server")
            asset_value = self.risk_parameters["asset_values"].get(asset_type, 500000)

            # Percentage of asset value lost
            if threat_actor.motivation == AttackMotivation.SABOTAGE:
                loss_percentage = 0.3
            elif threat_actor.motivation == AttackMotivation.FINANCIAL_GAIN:
                loss_percentage = 0.1
            else:
                loss_percentage = 0.05

            total_impact += asset_value * loss_percentage

        # Downtime costs
        hourly_cost = 50000  # Average hourly cost of downtime
        downtime_cost = service_downtime * hourly_cost

        # Incident response costs
        response_cost = 100000  # Base incident response cost

        # Regulatory fines (if applicable)
        regulatory_cost = 0.0
        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})
            if asset_data.get("contains_pii", False):
                regulatory_cost += 500000  # GDPR-style fine

        total_impact += downtime_cost + response_cost + regulatory_cost

        return total_impact

    def _assess_reputation_impact(self, threat_actor: ThreatActorProfile,
                                affected_assets: Set[str]) -> float:
        """Assess reputation impact (0.0 to 1.0)."""
        reputation_impact = 0.0

        # Check for public-facing assets
        public_assets = 0
        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})
            if asset_data.get("public_facing", False):
                public_assets += 1

        if public_assets > 0:
            reputation_impact += 0.4

        # Check for data breaches
        data_breach = False
        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})
            if asset_data.get("contains_pii", False):
                data_breach = True
                break

        if data_breach:
            reputation_impact += 0.5

        # Adjust based on threat actor type
        if threat_actor.actor_type == ThreatActorType.NATION_STATE:
            reputation_impact += 0.2  # High-profile attribution

        return min(reputation_impact, 1.0)

    def _identify_regulatory_violations(self, affected_assets: Set[str]) -> List[str]:
        """Identify potential regulatory violations."""
        violations = []

        # Check for PII exposure
        pii_exposed = False
        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})
            if asset_data.get("contains_pii", False):
                pii_exposed = True
                break

        if pii_exposed:
            violations.extend(["GDPR", "CCPA", "HIPAA"])

        # Check for financial data
        financial_data = False
        for asset_id in affected_assets:
            asset_data = self.attack_analyzer.graph_engine.asset_metadata.get(asset_id, {})
            if asset_data.get("data_classification") == "financial":
                financial_data = True
                break

        if financial_data:
            violations.extend(["SOX", "PCI-DSS"])

        return list(set(violations))  # Remove duplicates

    def get_threat_actor_profile(self, actor_id: str) -> Optional[ThreatActorProfile]:
        """Get threat actor profile by ID."""
        return self.threat_actors.get(actor_id)

    def list_threat_actors(self) -> List[ThreatActorProfile]:
        """List all available threat actor profiles."""
        return list(self.threat_actors.values())

    def get_simulation_result(self, simulation_id: str) -> Optional[AttackSimulationResult]:
        """Get simulation result by ID."""
        return self.simulation_cache.get(simulation_id)

    def list_simulations(self) -> List[AttackSimulationResult]:
        """List all cached simulation results."""
        return list(self.simulation_cache.values())

    def export_threat_model(self, format: str = "json") -> str:
        """Export threat model data."""
        if format.lower() != "json":
            raise ValueError("Only JSON format is currently supported")

        export_data = {
            "threat_actors": {
                actor_id: asdict(profile)
                for actor_id, profile in self.threat_actors.items()
            },
            "simulations": {
                sim_id: asdict(result)
                for sim_id, result in self.simulation_cache.items()
            },
            "risk_parameters": self.risk_parameters,
            "export_timestamp": datetime.utcnow().isoformat()
        }

        return json.dumps(export_data, indent=2, default=str)

    def clear_simulation_cache(self):
        """Clear simulation cache."""
        self.simulation_cache.clear()
        self.logger.info("Simulation cache cleared")
