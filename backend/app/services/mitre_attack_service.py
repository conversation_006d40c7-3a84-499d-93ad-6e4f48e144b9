"""
MITRE ATT&CK Integration Service

Comprehensive integration with the MITRE ATT&CK framework providing:
- STIX 2.0/2.1 data parsing and management
- Real-time technique correlation and mapping
- Threat actor profiling and attribution
- Attack pattern recognition and analysis
"""

import logging
import asyncio
import json
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Set, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import aiohttp
import aiofiles

try:
    import stix2
    from mitreattack.stix20 import MitreAttackData
    MITRE_LIBRARIES_AVAILABLE = True
except ImportError:
    MITRE_LIBRARIES_AVAILABLE = False
    logging.warning("MITRE ATT&CK libraries not available. Install with: pip install stix2 mitreattack-python")

from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class AttackDomain(Enum):
    """MITRE ATT&CK domains."""
    ENTERPRISE = "enterprise"
    MOBILE = "mobile"
    ICS = "ics"


class ConfidenceLevel(Enum):
    """Confidence levels for technique attribution."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class AttackTechnique:
    """MITRE ATT&CK technique representation."""
    
    technique_id: str
    name: str
    description: str
    tactic: str
    domain: AttackDomain
    platforms: List[str]
    data_sources: List[str]
    mitigations: List[str]
    detection_methods: List[str]
    sub_techniques: List[str]
    kill_chain_phases: List[str]
    external_references: List[Dict[str, str]]
    created: datetime
    modified: datetime
    version: str
    
    @property
    def is_sub_technique(self) -> bool:
        """Check if this is a sub-technique."""
        return "." in self.technique_id
    
    @property
    def parent_technique_id(self) -> Optional[str]:
        """Get parent technique ID if this is a sub-technique."""
        if self.is_sub_technique:
            return self.technique_id.split(".")[0]
        return None


@dataclass
class AttackGroup:
    """MITRE ATT&CK threat actor group."""
    
    group_id: str
    name: str
    description: str
    aliases: List[str]
    techniques: List[str]
    software: List[str]
    associated_campaigns: List[str]
    external_references: List[Dict[str, str]]
    created: datetime
    modified: datetime
    version: str


@dataclass
class AttackSoftware:
    """MITRE ATT&CK software/malware."""
    
    software_id: str
    name: str
    description: str
    software_type: str  # malware, tool
    aliases: List[str]
    techniques: List[str]
    platforms: List[str]
    external_references: List[Dict[str, str]]
    created: datetime
    modified: datetime
    version: str


@dataclass
class TechniqueCorrelation:
    """Correlation between security event and MITRE ATT&CK technique."""
    
    correlation_id: str
    event_id: str
    technique_id: str
    confidence: ConfidenceLevel
    confidence_score: float  # 0.0 to 1.0
    evidence: List[str]
    context: Dict[str, Any]
    timestamp: datetime
    analyst_verified: bool = False
    false_positive: bool = False


@dataclass
class AttackPattern:
    """Identified attack pattern with multiple techniques."""
    
    pattern_id: str
    name: str
    techniques: List[str]
    sequence: List[str]  # Ordered technique sequence
    confidence: float
    first_seen: datetime
    last_seen: datetime
    event_count: int
    affected_assets: Set[str]
    attributed_groups: List[str]
    campaign_id: Optional[str] = None


class MitreAttackService:
    """Comprehensive MITRE ATT&CK integration service."""
    
    def __init__(self, db: Session, data_dir: str = "data/mitre"):
        self.db = db
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # MITRE ATT&CK data
        self.techniques: Dict[str, AttackTechnique] = {}
        self.groups: Dict[str, AttackGroup] = {}
        self.software: Dict[str, AttackSoftware] = {}
        self.tactics: Dict[str, Dict[str, Any]] = {}
        
        # Correlation data
        self.correlations: Dict[str, TechniqueCorrelation] = {}
        self.patterns: Dict[str, AttackPattern] = {}
        
        # Data sources
        self.data_sources = {
            AttackDomain.ENTERPRISE: "https://raw.githubusercontent.com/mitre-attack/attack-stix-data/master/enterprise-attack/enterprise-attack.json",
            AttackDomain.MOBILE: "https://raw.githubusercontent.com/mitre-attack/attack-stix-data/master/mobile-attack/mobile-attack.json",
            AttackDomain.ICS: "https://raw.githubusercontent.com/mitre-attack/attack-stix-data/master/ics-attack/ics-attack.json"
        }
        
        # Technique correlation rules
        self.correlation_rules = self._load_correlation_rules()
        
        # Last update timestamps
        self.last_update: Dict[AttackDomain, datetime] = {}
        
        if not MITRE_LIBRARIES_AVAILABLE:
            self.logger.warning("MITRE ATT&CK libraries not available. Limited functionality.")
    
    def _load_correlation_rules(self) -> Dict[str, Dict[str, Any]]:
        """Load technique correlation rules."""
        return {
            # Initial Access techniques
            "T1566": {  # Phishing
                "keywords": ["phishing", "malicious email", "suspicious attachment", "email threat"],
                "log_sources": ["email_security", "web_proxy", "endpoint"],
                "confidence_base": 0.8
            },
            "T1190": {  # Exploit Public-Facing Application
                "keywords": ["web exploit", "application vulnerability", "public exploit"],
                "log_sources": ["web_application_firewall", "web_server", "ids"],
                "confidence_base": 0.9
            },
            "T1078": {  # Valid Accounts
                "keywords": ["credential abuse", "account compromise", "unauthorized access"],
                "log_sources": ["authentication", "active_directory", "vpn"],
                "confidence_base": 0.7
            },
            
            # Execution techniques
            "T1059": {  # Command and Scripting Interpreter
                "keywords": ["powershell", "cmd.exe", "bash", "script execution"],
                "log_sources": ["endpoint", "process_monitoring", "command_line"],
                "confidence_base": 0.8
            },
            "T1203": {  # Exploitation for Client Execution
                "keywords": ["client exploit", "browser exploit", "document exploit"],
                "log_sources": ["endpoint", "browser", "email_security"],
                "confidence_base": 0.9
            },
            
            # Persistence techniques
            "T1053": {  # Scheduled Task/Job
                "keywords": ["scheduled task", "cron job", "task scheduler"],
                "log_sources": ["endpoint", "system_logs", "process_monitoring"],
                "confidence_base": 0.8
            },
            "T1547": {  # Boot or Logon Autostart Execution
                "keywords": ["autostart", "registry run", "startup folder"],
                "log_sources": ["endpoint", "registry", "file_monitoring"],
                "confidence_base": 0.8
            },
            
            # Privilege Escalation techniques
            "T1068": {  # Exploitation for Privilege Escalation
                "keywords": ["privilege escalation", "local exploit", "elevation"],
                "log_sources": ["endpoint", "system_logs", "security_logs"],
                "confidence_base": 0.9
            },
            "T1134": {  # Access Token Manipulation
                "keywords": ["token manipulation", "impersonation", "privilege abuse"],
                "log_sources": ["endpoint", "security_logs", "process_monitoring"],
                "confidence_base": 0.8
            },
            
            # Defense Evasion techniques
            "T1055": {  # Process Injection
                "keywords": ["process injection", "dll injection", "code injection"],
                "log_sources": ["endpoint", "process_monitoring", "memory_analysis"],
                "confidence_base": 0.9
            },
            "T1027": {  # Obfuscated Files or Information
                "keywords": ["obfuscation", "encoded payload", "packed malware"],
                "log_sources": ["endpoint", "file_analysis", "network"],
                "confidence_base": 0.7
            },
            
            # Credential Access techniques
            "T1003": {  # OS Credential Dumping
                "keywords": ["credential dumping", "lsass", "sam database", "mimikatz"],
                "log_sources": ["endpoint", "security_logs", "process_monitoring"],
                "confidence_base": 0.9
            },
            "T1110": {  # Brute Force
                "keywords": ["brute force", "password spray", "credential stuffing"],
                "log_sources": ["authentication", "network", "security_logs"],
                "confidence_base": 0.8
            },
            
            # Discovery techniques
            "T1083": {  # File and Directory Discovery
                "keywords": ["file enumeration", "directory listing", "file discovery"],
                "log_sources": ["endpoint", "file_monitoring", "process_monitoring"],
                "confidence_base": 0.7
            },
            "T1018": {  # Remote System Discovery
                "keywords": ["network scanning", "host discovery", "ping sweep"],
                "log_sources": ["network", "ids", "endpoint"],
                "confidence_base": 0.8
            },
            
            # Lateral Movement techniques
            "T1021": {  # Remote Services
                "keywords": ["remote desktop", "psexec", "wmi", "ssh"],
                "log_sources": ["network", "authentication", "endpoint"],
                "confidence_base": 0.8
            },
            "T1570": {  # Lateral Tool Transfer
                "keywords": ["file transfer", "tool staging", "lateral movement"],
                "log_sources": ["network", "file_monitoring", "endpoint"],
                "confidence_base": 0.7
            },
            
            # Collection techniques
            "T1005": {  # Data from Local System
                "keywords": ["data collection", "file access", "sensitive data"],
                "log_sources": ["file_monitoring", "endpoint", "dlp"],
                "confidence_base": 0.7
            },
            "T1056": {  # Input Capture
                "keywords": ["keylogger", "input capture", "credential harvesting"],
                "log_sources": ["endpoint", "process_monitoring", "security_logs"],
                "confidence_base": 0.9
            },
            
            # Command and Control techniques
            "T1071": {  # Application Layer Protocol
                "keywords": ["c2 communication", "command control", "beacon"],
                "log_sources": ["network", "proxy", "dns"],
                "confidence_base": 0.8
            },
            "T1573": {  # Encrypted Channel
                "keywords": ["encrypted c2", "ssl tunnel", "encrypted communication"],
                "log_sources": ["network", "ssl_inspection", "proxy"],
                "confidence_base": 0.7
            },
            
            # Exfiltration techniques
            "T1041": {  # Exfiltration Over C2 Channel
                "keywords": ["data exfiltration", "c2 exfil", "data theft"],
                "log_sources": ["network", "dlp", "proxy"],
                "confidence_base": 0.8
            },
            "T1567": {  # Exfiltration Over Web Service
                "keywords": ["cloud exfiltration", "web service upload", "data upload"],
                "log_sources": ["network", "cloud_security", "dlp"],
                "confidence_base": 0.8
            },
            
            # Impact techniques
            "T1486": {  # Data Encrypted for Impact
                "keywords": ["ransomware", "file encryption", "data encryption"],
                "log_sources": ["endpoint", "file_monitoring", "security_logs"],
                "confidence_base": 0.9
            },
            "T1489": {  # Service Stop
                "keywords": ["service termination", "process kill", "system disruption"],
                "log_sources": ["endpoint", "system_logs", "process_monitoring"],
                "confidence_base": 0.8
            }
        }

    async def initialize(self) -> None:
        """Initialize MITRE ATT&CK service with data loading."""
        self.logger.info("Initializing MITRE ATT&CK service...")

        try:
            # Load data for all domains
            for domain in AttackDomain:
                await self.load_attack_data(domain)

            self.logger.info(f"Loaded {len(self.techniques)} techniques, {len(self.groups)} groups, {len(self.software)} software")

        except Exception as e:
            self.logger.error(f"Failed to initialize MITRE ATT&CK service: {e}")
            raise

    async def load_attack_data(self, domain: AttackDomain, force_update: bool = False) -> None:
        """Load MITRE ATT&CK data for specified domain."""
        self.logger.info(f"Loading MITRE ATT&CK data for {domain.value} domain")

        try:
            # Check if update is needed
            if not force_update and self._is_data_current(domain):
                self.logger.info(f"Data for {domain.value} is current, skipping update")
                await self._load_cached_data(domain)
                return

            # Download fresh data
            data_url = self.data_sources[domain]
            data_file = self.data_dir / f"{domain.value}-attack.json"

            await self._download_attack_data(data_url, data_file)
            await self._parse_attack_data(data_file, domain)

            # Update timestamp
            self.last_update[domain] = datetime.utcnow()

            self.logger.info(f"Successfully loaded {domain.value} ATT&CK data")

        except Exception as e:
            self.logger.error(f"Failed to load {domain.value} ATT&CK data: {e}")
            raise

    async def _download_attack_data(self, url: str, file_path: Path) -> None:
        """Download MITRE ATT&CK data from URL."""
        self.logger.info(f"Downloading ATT&CK data from {url}")

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    async with aiofiles.open(file_path, 'wb') as f:
                        await f.write(content)
                    self.logger.info(f"Downloaded {len(content)} bytes to {file_path}")
                else:
                    raise Exception(f"Failed to download data: HTTP {response.status}")

    async def _parse_attack_data(self, file_path: Path, domain: AttackDomain) -> None:
        """Parse MITRE ATT&CK STIX data."""
        self.logger.info(f"Parsing ATT&CK data from {file_path}")

        if not MITRE_LIBRARIES_AVAILABLE:
            # Fallback to basic JSON parsing
            await self._parse_attack_data_fallback(file_path, domain)
            return

        try:
            # Use MITRE ATT&CK library for parsing
            attack_data = MitreAttackData(str(file_path))

            # Parse techniques
            techniques = attack_data.get_techniques()
            for technique in techniques:
                parsed_technique = self._parse_technique(technique, domain)
                self.techniques[parsed_technique.technique_id] = parsed_technique

            # Parse groups
            groups = attack_data.get_groups()
            for group in groups:
                parsed_group = self._parse_group(group, domain)
                self.groups[parsed_group.group_id] = parsed_group

            # Parse software
            software_list = attack_data.get_software()
            for software in software_list:
                parsed_software = self._parse_software(software, domain)
                self.software[parsed_software.software_id] = parsed_software

            # Parse tactics
            tactics = attack_data.get_tactics()
            for tactic in tactics:
                tactic_data = self._parse_tactic(tactic, domain)
                self.tactics[tactic_data["id"]] = tactic_data

            self.logger.info(f"Parsed {len(techniques)} techniques, {len(groups)} groups, {len(software_list)} software")

        except Exception as e:
            self.logger.error(f"Failed to parse ATT&CK data: {e}")
            # Fallback to basic parsing
            await self._parse_attack_data_fallback(file_path, domain)

    async def _parse_attack_data_fallback(self, file_path: Path, domain: AttackDomain) -> None:
        """Fallback parsing without MITRE libraries."""
        self.logger.info("Using fallback parsing method")

        async with aiofiles.open(file_path, 'r') as f:
            content = await f.read()
            data = json.loads(content)

        # Basic STIX parsing
        objects = data.get("objects", [])

        for obj in objects:
            obj_type = obj.get("type")

            if obj_type == "attack-pattern":
                technique = self._parse_technique_fallback(obj, domain)
                if technique:
                    self.techniques[technique.technique_id] = technique

            elif obj_type == "intrusion-set":
                group = self._parse_group_fallback(obj, domain)
                if group:
                    self.groups[group.group_id] = group

            elif obj_type == "malware" or obj_type == "tool":
                software = self._parse_software_fallback(obj, domain)
                if software:
                    self.software[software.software_id] = software

            elif obj_type == "x-mitre-tactic":
                tactic = self._parse_tactic_fallback(obj, domain)
                if tactic:
                    self.tactics[tactic["id"]] = tactic

    def _parse_technique(self, technique_obj: Any, domain: AttackDomain) -> AttackTechnique:
        """Parse MITRE ATT&CK technique using library."""
        external_refs = technique_obj.get("external_references", [])
        mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

        return AttackTechnique(
            technique_id=mitre_ref.get("external_id", ""),
            name=technique_obj.get("name", ""),
            description=technique_obj.get("description", ""),
            tactic=self._extract_tactic_from_technique(technique_obj),
            domain=domain,
            platforms=technique_obj.get("x_mitre_platforms", []),
            data_sources=technique_obj.get("x_mitre_data_sources", []),
            mitigations=[],  # Will be populated separately
            detection_methods=[],  # Will be populated separately
            sub_techniques=[],  # Will be populated separately
            kill_chain_phases=[phase.get("phase_name", "") for phase in technique_obj.get("kill_chain_phases", [])],
            external_references=external_refs,
            created=datetime.fromisoformat(technique_obj.get("created", "").replace("Z", "+00:00")),
            modified=datetime.fromisoformat(technique_obj.get("modified", "").replace("Z", "+00:00")),
            version=technique_obj.get("x_mitre_version", "1.0")
        )

    def _parse_technique_fallback(self, obj: Dict[str, Any], domain: AttackDomain) -> Optional[AttackTechnique]:
        """Parse technique using fallback method."""
        try:
            external_refs = obj.get("external_references", [])
            mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

            if not mitre_ref.get("external_id"):
                return None

            return AttackTechnique(
                technique_id=mitre_ref.get("external_id", ""),
                name=obj.get("name", ""),
                description=obj.get("description", ""),
                tactic=self._extract_tactic_from_kill_chain(obj.get("kill_chain_phases", [])),
                domain=domain,
                platforms=obj.get("x_mitre_platforms", []),
                data_sources=obj.get("x_mitre_data_sources", []),
                mitigations=[],
                detection_methods=[],
                sub_techniques=[],
                kill_chain_phases=[phase.get("phase_name", "") for phase in obj.get("kill_chain_phases", [])],
                external_references=external_refs,
                created=datetime.fromisoformat(obj.get("created", "").replace("Z", "+00:00")),
                modified=datetime.fromisoformat(obj.get("modified", "").replace("Z", "+00:00")),
                version=obj.get("x_mitre_version", "1.0")
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse technique: {e}")
            return None

    def _parse_group(self, group_obj: Any, domain: AttackDomain) -> AttackGroup:
        """Parse MITRE ATT&CK group using library."""
        external_refs = group_obj.get("external_references", [])
        mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

        return AttackGroup(
            group_id=mitre_ref.get("external_id", ""),
            name=group_obj.get("name", ""),
            description=group_obj.get("description", ""),
            aliases=group_obj.get("aliases", []),
            techniques=[],  # Will be populated from relationships
            software=[],  # Will be populated from relationships
            associated_campaigns=[],
            external_references=external_refs,
            created=datetime.fromisoformat(group_obj.get("created", "").replace("Z", "+00:00")),
            modified=datetime.fromisoformat(group_obj.get("modified", "").replace("Z", "+00:00")),
            version=group_obj.get("x_mitre_version", "1.0")
        )

    def _parse_group_fallback(self, obj: Dict[str, Any], domain: AttackDomain) -> Optional[AttackGroup]:
        """Parse group using fallback method."""
        try:
            external_refs = obj.get("external_references", [])
            mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

            if not mitre_ref.get("external_id"):
                return None

            return AttackGroup(
                group_id=mitre_ref.get("external_id", ""),
                name=obj.get("name", ""),
                description=obj.get("description", ""),
                aliases=obj.get("aliases", []),
                techniques=[],
                software=[],
                associated_campaigns=[],
                external_references=external_refs,
                created=datetime.fromisoformat(obj.get("created", "").replace("Z", "+00:00")),
                modified=datetime.fromisoformat(obj.get("modified", "").replace("Z", "+00:00")),
                version=obj.get("x_mitre_version", "1.0")
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse group: {e}")
            return None

    def _parse_software(self, software_obj: Any, domain: AttackDomain) -> AttackSoftware:
        """Parse MITRE ATT&CK software using library."""
        external_refs = software_obj.get("external_references", [])
        mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

        return AttackSoftware(
            software_id=mitre_ref.get("external_id", ""),
            name=software_obj.get("name", ""),
            description=software_obj.get("description", ""),
            software_type=software_obj.get("type", ""),
            aliases=software_obj.get("x_mitre_aliases", []),
            techniques=[],  # Will be populated from relationships
            platforms=software_obj.get("x_mitre_platforms", []),
            external_references=external_refs,
            created=datetime.fromisoformat(software_obj.get("created", "").replace("Z", "+00:00")),
            modified=datetime.fromisoformat(software_obj.get("modified", "").replace("Z", "+00:00")),
            version=software_obj.get("x_mitre_version", "1.0")
        )

    def _parse_software_fallback(self, obj: Dict[str, Any], domain: AttackDomain) -> Optional[AttackSoftware]:
        """Parse software using fallback method."""
        try:
            external_refs = obj.get("external_references", [])
            mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

            if not mitre_ref.get("external_id"):
                return None

            return AttackSoftware(
                software_id=mitre_ref.get("external_id", ""),
                name=obj.get("name", ""),
                description=obj.get("description", ""),
                software_type=obj.get("type", ""),
                aliases=obj.get("x_mitre_aliases", []),
                techniques=[],
                platforms=obj.get("x_mitre_platforms", []),
                external_references=external_refs,
                created=datetime.fromisoformat(obj.get("created", "").replace("Z", "+00:00")),
                modified=datetime.fromisoformat(obj.get("modified", "").replace("Z", "+00:00")),
                version=obj.get("x_mitre_version", "1.0")
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse software: {e}")
            return None

    def _parse_tactic(self, tactic_obj: Any, domain: AttackDomain) -> Dict[str, Any]:
        """Parse MITRE ATT&CK tactic using library."""
        external_refs = tactic_obj.get("external_references", [])
        mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

        return {
            "id": mitre_ref.get("external_id", ""),
            "name": tactic_obj.get("name", ""),
            "description": tactic_obj.get("description", ""),
            "short_name": tactic_obj.get("x_mitre_shortname", ""),
            "domain": domain.value,
            "external_references": external_refs
        }

    def _parse_tactic_fallback(self, obj: Dict[str, Any], domain: AttackDomain) -> Optional[Dict[str, Any]]:
        """Parse tactic using fallback method."""
        try:
            external_refs = obj.get("external_references", [])
            mitre_ref = next((ref for ref in external_refs if ref.get("source_name") == "mitre-attack"), {})

            if not mitre_ref.get("external_id"):
                return None

            return {
                "id": mitre_ref.get("external_id", ""),
                "name": obj.get("name", ""),
                "description": obj.get("description", ""),
                "short_name": obj.get("x_mitre_shortname", ""),
                "domain": domain.value,
                "external_references": external_refs
            }
        except Exception as e:
            self.logger.warning(f"Failed to parse tactic: {e}")
            return None

    def _extract_tactic_from_technique(self, technique_obj: Any) -> str:
        """Extract tactic from technique object."""
        kill_chain_phases = technique_obj.get("kill_chain_phases", [])
        if kill_chain_phases:
            return kill_chain_phases[0].get("phase_name", "")
        return ""

    def _extract_tactic_from_kill_chain(self, kill_chain_phases: List[Dict[str, Any]]) -> str:
        """Extract tactic from kill chain phases."""
        if kill_chain_phases:
            return kill_chain_phases[0].get("phase_name", "")
        return ""

    def _is_data_current(self, domain: AttackDomain) -> bool:
        """Check if cached data is current."""
        data_file = self.data_dir / f"{domain.value}-attack.json"
        if not data_file.exists():
            return False

        # Check if data is less than 24 hours old
        file_time = datetime.fromtimestamp(data_file.stat().st_mtime)
        return datetime.utcnow() - file_time < timedelta(hours=24)

    async def _load_cached_data(self, domain: AttackDomain) -> None:
        """Load cached data for domain."""
        data_file = self.data_dir / f"{domain.value}-attack.json"
        if data_file.exists():
            await self._parse_attack_data(data_file, domain)

    async def correlate_event_to_techniques(self, event_data: Dict[str, Any]) -> List[TechniqueCorrelation]:
        """Correlate security event to MITRE ATT&CK techniques."""
        correlations = []

        try:
            event_id = event_data.get("event_id", "")
            event_text = self._extract_event_text(event_data)
            log_source = event_data.get("source", "").lower()

            # Check each correlation rule
            for technique_id, rule in self.correlation_rules.items():
                confidence_score = self._calculate_correlation_confidence(
                    event_text, log_source, rule
                )

                if confidence_score > 0.3:  # Minimum confidence threshold
                    confidence_level = self._score_to_confidence_level(confidence_score)

                    correlation = TechniqueCorrelation(
                        correlation_id=self._generate_correlation_id(event_id, technique_id),
                        event_id=event_id,
                        technique_id=technique_id,
                        confidence=confidence_level,
                        confidence_score=confidence_score,
                        evidence=self._extract_evidence(event_text, rule),
                        context=self._extract_context(event_data),
                        timestamp=datetime.utcnow()
                    )

                    correlations.append(correlation)
                    self.correlations[correlation.correlation_id] = correlation

            # Sort by confidence score
            correlations.sort(key=lambda x: x.confidence_score, reverse=True)

            self.logger.info(f"Found {len(correlations)} technique correlations for event {event_id}")
            return correlations

        except Exception as e:
            self.logger.error(f"Failed to correlate event: {e}")
            return []

    def _extract_event_text(self, event_data: Dict[str, Any]) -> str:
        """Extract searchable text from event data."""
        text_fields = ["message", "description", "command_line", "process_name",
                      "file_name", "network_connection", "registry_key"]

        text_parts = []
        for field in text_fields:
            if field in event_data and event_data[field]:
                text_parts.append(str(event_data[field]).lower())

        return " ".join(text_parts)

    def _calculate_correlation_confidence(self, event_text: str, log_source: str, rule: Dict[str, Any]) -> float:
        """Calculate correlation confidence score."""
        confidence = 0.0

        # Check keywords
        keywords = rule.get("keywords", [])
        keyword_matches = sum(1 for keyword in keywords if keyword.lower() in event_text)
        if keywords:
            keyword_confidence = (keyword_matches / len(keywords)) * rule.get("confidence_base", 0.5)
            confidence += keyword_confidence * 0.7  # 70% weight for keywords

        # Check log source
        log_sources = rule.get("log_sources", [])
        if log_sources and any(source in log_source for source in log_sources):
            confidence += 0.3  # 30% weight for log source match

        return min(confidence, 1.0)

    def _score_to_confidence_level(self, score: float) -> ConfidenceLevel:
        """Convert confidence score to confidence level."""
        if score >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif score >= 0.6:
            return ConfidenceLevel.HIGH
        elif score >= 0.4:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW

    def _generate_correlation_id(self, event_id: str, technique_id: str) -> str:
        """Generate unique correlation ID."""
        combined = f"{event_id}_{technique_id}_{datetime.utcnow().isoformat()}"
        return hashlib.sha256(combined.encode()).hexdigest()[:16]

    def _extract_evidence(self, event_text: str, rule: Dict[str, Any]) -> List[str]:
        """Extract evidence from event text."""
        evidence = []
        keywords = rule.get("keywords", [])

        for keyword in keywords:
            if keyword.lower() in event_text:
                evidence.append(f"Keyword match: {keyword}")

        return evidence

    def _extract_context(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant context from event data."""
        context_fields = ["timestamp", "source_ip", "destination_ip", "user",
                         "host", "process_id", "parent_process", "file_hash"]

        context = {}
        for field in context_fields:
            if field in event_data:
                context[field] = event_data[field]

        return context

    async def identify_attack_patterns(self, time_window_hours: int = 24) -> List[AttackPattern]:
        """Identify attack patterns from correlations."""
        self.logger.info(f"Identifying attack patterns in {time_window_hours}h window")

        patterns = []
        cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)

        # Get recent correlations
        recent_correlations = [
            corr for corr in self.correlations.values()
            if corr.timestamp >= cutoff_time and not corr.false_positive
        ]

        # Group correlations by affected assets
        asset_correlations = {}
        for corr in recent_correlations:
            asset_id = corr.context.get("host", "unknown")
            if asset_id not in asset_correlations:
                asset_correlations[asset_id] = []
            asset_correlations[asset_id].append(corr)

        # Analyze patterns for each asset
        for asset_id, correlations in asset_correlations.items():
            if len(correlations) >= 2:  # Minimum 2 techniques for a pattern
                pattern = self._analyze_technique_sequence(asset_id, correlations)
                if pattern:
                    patterns.append(pattern)
                    self.patterns[pattern.pattern_id] = pattern

        self.logger.info(f"Identified {len(patterns)} attack patterns")
        return patterns

    def _analyze_technique_sequence(self, asset_id: str, correlations: List[TechniqueCorrelation]) -> Optional[AttackPattern]:
        """Analyze technique sequence for attack pattern."""
        # Sort correlations by timestamp
        correlations.sort(key=lambda x: x.timestamp)

        techniques = [corr.technique_id for corr in correlations]
        sequence = self._determine_attack_sequence(techniques)

        # Calculate pattern confidence
        avg_confidence = sum(corr.confidence_score for corr in correlations) / len(correlations)

        # Check if sequence follows known attack patterns
        pattern_confidence = self._validate_attack_sequence(sequence)
        overall_confidence = (avg_confidence + pattern_confidence) / 2

        if overall_confidence > 0.5:  # Minimum confidence for pattern
            pattern_id = self._generate_pattern_id(asset_id, techniques)

            return AttackPattern(
                pattern_id=pattern_id,
                name=f"Attack Pattern on {asset_id}",
                techniques=techniques,
                sequence=sequence,
                confidence=overall_confidence,
                first_seen=correlations[0].timestamp,
                last_seen=correlations[-1].timestamp,
                event_count=len(correlations),
                affected_assets={asset_id},
                attributed_groups=self._attribute_to_groups(techniques)
            )

        return None

    def _determine_attack_sequence(self, techniques: List[str]) -> List[str]:
        """Determine logical attack sequence from techniques."""
        # MITRE ATT&CK tactic order
        tactic_order = [
            "initial-access", "execution", "persistence", "privilege-escalation",
            "defense-evasion", "credential-access", "discovery", "lateral-movement",
            "collection", "command-and-control", "exfiltration", "impact"
        ]

        # Map techniques to tactics
        technique_tactics = {}
        for technique_id in techniques:
            if technique_id in self.techniques:
                tactic = self.techniques[technique_id].tactic
                technique_tactics[technique_id] = tactic

        # Sort techniques by tactic order
        sorted_techniques = sorted(
            techniques,
            key=lambda t: tactic_order.index(technique_tactics.get(t, "unknown"))
            if technique_tactics.get(t, "unknown") in tactic_order else 999
        )

        return sorted_techniques

    def _validate_attack_sequence(self, sequence: List[str]) -> float:
        """Validate attack sequence against known patterns."""
        # Simple validation - check if sequence follows logical tactic progression
        if len(sequence) < 2:
            return 0.5

        tactic_order = [
            "initial-access", "execution", "persistence", "privilege-escalation",
            "defense-evasion", "credential-access", "discovery", "lateral-movement",
            "collection", "command-and-control", "exfiltration", "impact"
        ]

        valid_transitions = 0
        total_transitions = len(sequence) - 1

        for i in range(total_transitions):
            current_technique = sequence[i]
            next_technique = sequence[i + 1]

            current_tactic = self.techniques.get(current_technique, {}).tactic if current_technique in self.techniques else ""
            next_tactic = self.techniques.get(next_technique, {}).tactic if next_technique in self.techniques else ""

            if current_tactic in tactic_order and next_tactic in tactic_order:
                current_index = tactic_order.index(current_tactic)
                next_index = tactic_order.index(next_tactic)

                # Allow same tactic or progression forward
                if next_index >= current_index:
                    valid_transitions += 1

        return valid_transitions / total_transitions if total_transitions > 0 else 0.5

    def _generate_pattern_id(self, asset_id: str, techniques: List[str]) -> str:
        """Generate unique pattern ID."""
        combined = f"{asset_id}_{'_'.join(sorted(techniques))}_{datetime.utcnow().date()}"
        return hashlib.sha256(combined.encode()).hexdigest()[:16]

    def _attribute_to_groups(self, techniques: List[str]) -> List[str]:
        """Attribute techniques to known threat actor groups."""
        group_scores = {}

        for group_id, group in self.groups.items():
            score = 0
            for technique in techniques:
                if technique in group.techniques:
                    score += 1

            if score > 0:
                # Normalize score by group technique count
                normalized_score = score / len(group.techniques) if group.techniques else 0
                group_scores[group_id] = normalized_score

        # Return groups with score > 0.3
        attributed_groups = [
            group_id for group_id, score in group_scores.items()
            if score > 0.3
        ]

        return attributed_groups

    async def enrich_ioc_with_attack_context(self, ioc: str, ioc_type: str) -> Dict[str, Any]:
        """Enrich IOC with MITRE ATT&CK context."""
        enrichment = {
            "ioc": ioc,
            "ioc_type": ioc_type,
            "attack_context": {
                "techniques": [],
                "tactics": [],
                "groups": [],
                "software": []
            },
            "confidence": 0.0,
            "timestamp": datetime.utcnow().isoformat()
        }

        try:
            # Search for IOC in technique descriptions and references
            matching_techniques = []
            for technique_id, technique in self.techniques.items():
                if self._ioc_matches_technique(ioc, technique):
                    matching_techniques.append({
                        "id": technique_id,
                        "name": technique.name,
                        "tactic": technique.tactic,
                        "confidence": 0.8
                    })

            # Search for IOC in group descriptions
            matching_groups = []
            for group_id, group in self.groups.items():
                if self._ioc_matches_group(ioc, group):
                    matching_groups.append({
                        "id": group_id,
                        "name": group.name,
                        "confidence": 0.7
                    })

            # Search for IOC in software descriptions
            matching_software = []
            for software_id, software in self.software.items():
                if self._ioc_matches_software(ioc, software):
                    matching_software.append({
                        "id": software_id,
                        "name": software.name,
                        "type": software.software_type,
                        "confidence": 0.8
                    })

            enrichment["attack_context"]["techniques"] = matching_techniques
            enrichment["attack_context"]["groups"] = matching_groups
            enrichment["attack_context"]["software"] = matching_software

            # Calculate overall confidence
            total_matches = len(matching_techniques) + len(matching_groups) + len(matching_software)
            enrichment["confidence"] = min(total_matches * 0.2, 1.0)

            self.logger.info(f"Enriched IOC {ioc} with {total_matches} ATT&CK matches")

        except Exception as e:
            self.logger.error(f"Failed to enrich IOC {ioc}: {e}")

        return enrichment

    def _ioc_matches_technique(self, ioc: str, technique: AttackTechnique) -> bool:
        """Check if IOC matches technique."""
        search_text = f"{technique.description} {' '.join(technique.platforms)}".lower()
        return ioc.lower() in search_text

    def _ioc_matches_group(self, ioc: str, group: AttackGroup) -> bool:
        """Check if IOC matches group."""
        search_text = f"{group.description} {' '.join(group.aliases)}".lower()
        return ioc.lower() in search_text

    def _ioc_matches_software(self, ioc: str, software: AttackSoftware) -> bool:
        """Check if IOC matches software."""
        search_text = f"{software.description} {' '.join(software.aliases)}".lower()
        return ioc.lower() in search_text

    def get_technique(self, technique_id: str) -> Optional[AttackTechnique]:
        """Get technique by ID."""
        return self.techniques.get(technique_id)

    def get_group(self, group_id: str) -> Optional[AttackGroup]:
        """Get group by ID."""
        return self.groups.get(group_id)

    def get_software(self, software_id: str) -> Optional[AttackSoftware]:
        """Get software by ID."""
        return self.software.get(software_id)

    def search_techniques(self, query: str, domain: Optional[AttackDomain] = None) -> List[AttackTechnique]:
        """Search techniques by query."""
        results = []
        query_lower = query.lower()

        for technique in self.techniques.values():
            if domain and technique.domain != domain:
                continue

            if (query_lower in technique.name.lower() or
                query_lower in technique.description.lower() or
                query_lower in technique.technique_id.lower()):
                results.append(technique)

        return results

    def search_groups(self, query: str) -> List[AttackGroup]:
        """Search groups by query."""
        results = []
        query_lower = query.lower()

        for group in self.groups.values():
            if (query_lower in group.name.lower() or
                query_lower in group.description.lower() or
                any(query_lower in alias.lower() for alias in group.aliases)):
                results.append(group)

        return results

    def get_techniques_by_tactic(self, tactic: str, domain: Optional[AttackDomain] = None) -> List[AttackTechnique]:
        """Get techniques by tactic."""
        results = []

        for technique in self.techniques.values():
            if domain and technique.domain != domain:
                continue

            if technique.tactic.lower() == tactic.lower():
                results.append(technique)

        return results

    def get_group_techniques(self, group_id: str) -> List[AttackTechnique]:
        """Get techniques used by a group."""
        group = self.groups.get(group_id)
        if not group:
            return []

        return [
            self.techniques[tech_id]
            for tech_id in group.techniques
            if tech_id in self.techniques
        ]

    def generate_attack_navigator_layer(self, techniques: List[str],
                                      name: str = "Custom Layer",
                                      description: str = "") -> Dict[str, Any]:
        """Generate ATT&CK Navigator layer JSON."""
        layer = {
            "name": name,
            "versions": {
                "attack": "14",
                "navigator": "4.9.1",
                "layer": "4.5"
            },
            "domain": "enterprise-attack",
            "description": description,
            "filters": {
                "platforms": ["Windows", "Linux", "macOS", "Cloud"]
            },
            "sorting": 0,
            "layout": {
                "layout": "side",
                "aggregateFunction": "average",
                "showID": False,
                "showName": True,
                "showAggregateScores": False,
                "countUnscored": False
            },
            "hideDisabled": False,
            "techniques": [],
            "gradient": {
                "colors": ["#ff6666", "#ffe766", "#8ec843"],
                "minValue": 0,
                "maxValue": 100
            },
            "legendItems": [],
            "metadata": [],
            "links": [],
            "showTacticRowBackground": False,
            "tacticRowBackground": "#dddddd",
            "selectTechniquesAcrossTactics": True,
            "selectSubtechniquesWithParent": False
        }

        # Add techniques to layer
        for technique_id in techniques:
            if technique_id in self.techniques:
                technique = self.techniques[technique_id]
                layer["techniques"].append({
                    "techniqueID": technique_id,
                    "tactic": technique.tactic,
                    "score": 100,
                    "color": "#ff6666",
                    "comment": technique.description[:100] + "..." if len(technique.description) > 100 else technique.description,
                    "enabled": True,
                    "metadata": [],
                    "links": [],
                    "showSubtechniques": False
                })

        return layer

    def get_statistics(self) -> Dict[str, Any]:
        """Get MITRE ATT&CK data statistics."""
        stats = {
            "techniques": {
                "total": len(self.techniques),
                "by_domain": {},
                "by_tactic": {}
            },
            "groups": {
                "total": len(self.groups)
            },
            "software": {
                "total": len(self.software),
                "malware": 0,
                "tools": 0
            },
            "correlations": {
                "total": len(self.correlations),
                "by_confidence": {}
            },
            "patterns": {
                "total": len(self.patterns)
            },
            "last_update": {domain.value: timestamp.isoformat() if timestamp else None
                           for domain, timestamp in self.last_update.items()}
        }

        # Count by domain
        for technique in self.techniques.values():
            domain = technique.domain.value
            stats["techniques"]["by_domain"][domain] = stats["techniques"]["by_domain"].get(domain, 0) + 1

            tactic = technique.tactic
            stats["techniques"]["by_tactic"][tactic] = stats["techniques"]["by_tactic"].get(tactic, 0) + 1

        # Count software types
        for software in self.software.values():
            if software.software_type == "malware":
                stats["software"]["malware"] += 1
            elif software.software_type == "tool":
                stats["software"]["tools"] += 1

        # Count correlations by confidence
        for correlation in self.correlations.values():
            confidence = correlation.confidence.value
            stats["correlations"]["by_confidence"][confidence] = stats["correlations"]["by_confidence"].get(confidence, 0) + 1

        return stats

    async def update_all_domains(self) -> None:
        """Update all MITRE ATT&CK domains."""
        self.logger.info("Updating all MITRE ATT&CK domains")

        for domain in AttackDomain:
            try:
                await self.load_attack_data(domain, force_update=True)
            except Exception as e:
                self.logger.error(f"Failed to update {domain.value}: {e}")

        self.logger.info("Completed MITRE ATT&CK domain updates")
