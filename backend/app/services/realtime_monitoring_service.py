"""
Real-time Monitoring Service

Provides real-time monitoring capabilities including threat detection,
attack path analysis, and MITRE ATT&CK correlation for the dashboard.
"""

import asyncio
import json
import logging
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.db.session import get_db
from app.models.asset import Asset
from app.models.mitre_attack import Mitre<PERSON>ech<PERSON>que, MitreTactic
from app.services.mitre_attack_service import MitreAttackService
from app.services.attack_path_service import AttackPathService
from app.core.config import settings


logger = logging.getLogger(__name__)


class ThreatLevel(Enum):
    """Threat severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class EventType(Enum):
    """Real-time event types"""
    ASSET_DISCOVERED = "asset_discovered"
    THREAT_DETECTED = "threat_detected"
    ATTACK_PATH_FOUND = "attack_path_found"
    VULNERABILITY_FOUND = "vulnerability_found"
    MITRE_CORRELATION = "mitre_correlation"
    RISK_SCORE_CHANGED = "risk_score_changed"
    SYSTEM_ALERT = "system_alert"


@dataclass
class RealTimeEvent:
    """Real-time event data structure"""
    id: str
    event_type: EventType
    timestamp: datetime
    severity: ThreatLevel
    title: str
    description: str
    asset_id: Optional[int] = None
    asset_name: Optional[str] = None
    mitre_technique_id: Optional[str] = None
    risk_score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['event_type'] = self.event_type.value
        data['severity'] = self.severity.value
        return data


@dataclass
class ThreatMapData:
    """Geographic threat visualization data"""
    country: str
    country_code: str
    threat_count: int
    severity_distribution: Dict[str, int]
    top_techniques: List[str]
    coordinates: Dict[str, float]  # lat, lng


@dataclass
class RiskHeatmapData:
    """Risk heatmap visualization data"""
    asset_id: int
    asset_name: str
    asset_type: str
    risk_score: float
    threat_count: int
    mitre_techniques: List[str]
    last_updated: datetime


@dataclass
class AttackTimelineEvent:
    """Attack timeline event data"""
    timestamp: datetime
    event_id: str
    technique_id: str
    technique_name: str
    tactic: str
    asset_name: str
    severity: ThreatLevel
    description: str


class RealTimeMonitoringService:
    """Service for real-time monitoring and dashboard data"""
    
    def __init__(self):
        self.mitre_service = MitreAttackService()
        self.attack_path_service = AttackPathService()
        self.active_connections: Set[Any] = set()
        self.event_buffer: List[RealTimeEvent] = []
        self.max_buffer_size = 1000
        
    async def add_connection(self, websocket):
        """Add WebSocket connection for real-time updates"""
        self.active_connections.add(websocket)
        logger.info(f"Added WebSocket connection. Total: {len(self.active_connections)}")
        
    async def remove_connection(self, websocket):
        """Remove WebSocket connection"""
        self.active_connections.discard(websocket)
        logger.info(f"Removed WebSocket connection. Total: {len(self.active_connections)}")
        
    async def broadcast_event(self, event: RealTimeEvent):
        """Broadcast event to all connected clients"""
        if not self.active_connections:
            return
            
        message = json.dumps({
            "type": "realtime_event",
            "data": event.to_dict()
        })
        
        # Remove disconnected connections
        disconnected = set()
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.warning(f"Failed to send message to connection: {e}")
                disconnected.add(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.active_connections.discard(connection)
            
        # Add to event buffer
        self.event_buffer.append(event)
        if len(self.event_buffer) > self.max_buffer_size:
            self.event_buffer.pop(0)
    
    async def get_dashboard_overview(self, db: Session) -> Dict[str, Any]:
        """Get comprehensive dashboard overview data"""
        
        # Get basic metrics
        total_assets = db.query(Asset).filter(Asset.is_active == True).count()
        high_risk_assets = db.query(Asset).filter(
            and_(Asset.is_active == True, Asset.risk_score >= 7.0)
        ).count()
        
        # Get recent events
        recent_events = self.event_buffer[-50:] if self.event_buffer else []
        
        # Get MITRE technique distribution
        mitre_distribution = await self._get_mitre_technique_distribution(db)
        
        # Get threat trends
        threat_trends = await self._get_threat_trends(db)
        
        return {
            "overview": {
                "total_assets": total_assets,
                "high_risk_assets": high_risk_assets,
                "active_threats": len([e for e in recent_events if e.severity in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]]),
                "mitre_techniques_detected": len(set(e.mitre_technique_id for e in recent_events if e.mitre_technique_id)),
                "last_updated": datetime.utcnow().isoformat()
            },
            "recent_events": [event.to_dict() for event in recent_events],
            "mitre_distribution": mitre_distribution,
            "threat_trends": threat_trends
        }
    
    async def get_threat_map_data(self, db: Session) -> List[ThreatMapData]:
        """Get geographic threat distribution data"""
        
        # Simulate geographic threat data (in real implementation, this would come from threat intelligence)
        threat_locations = [
            ThreatMapData(
                country="United States",
                country_code="US",
                threat_count=45,
                severity_distribution={"low": 20, "medium": 15, "high": 8, "critical": 2},
                top_techniques=["T1190", "T1078", "T1021"],
                coordinates={"lat": 39.8283, "lng": -98.5795}
            ),
            ThreatMapData(
                country="China",
                country_code="CN",
                threat_count=32,
                severity_distribution={"low": 12, "medium": 12, "high": 6, "critical": 2},
                top_techniques=["T1566", "T1055", "T1003"],
                coordinates={"lat": 35.8617, "lng": 104.1954}
            ),
            ThreatMapData(
                country="Russia",
                country_code="RU",
                threat_count=28,
                severity_distribution={"low": 10, "medium": 10, "high": 6, "critical": 2},
                top_techniques=["T1059", "T1105", "T1027"],
                coordinates={"lat": 61.5240, "lng": 105.3188}
            ),
            ThreatMapData(
                country="Germany",
                country_code="DE",
                threat_count=18,
                severity_distribution={"low": 8, "medium": 6, "high": 3, "critical": 1},
                top_techniques=["T1190", "T1566", "T1078"],
                coordinates={"lat": 51.1657, "lng": 10.4515}
            )
        ]
        
        return threat_locations
    
    async def get_risk_heatmap_data(self, db: Session) -> List[RiskHeatmapData]:
        """Get risk heatmap data for assets"""
        
        assets = db.query(Asset).filter(Asset.is_active == True).order_by(desc(Asset.risk_score)).limit(100).all()
        
        heatmap_data = []
        for asset in assets:
            # Get associated MITRE techniques (simulated)
            techniques = await self._get_asset_mitre_techniques(db, asset.id)
            
            heatmap_data.append(RiskHeatmapData(
                asset_id=asset.id,
                asset_name=asset.name,
                asset_type=asset.asset_type,
                risk_score=asset.risk_score or 0.0,
                threat_count=len(techniques),
                mitre_techniques=techniques[:5],  # Top 5 techniques
                last_updated=asset.updated_at or datetime.utcnow()
            ))
        
        return heatmap_data
    
    async def get_attack_timeline(self, db: Session, hours: int = 24) -> List[AttackTimelineEvent]:
        """Get attack timeline events for specified time period"""
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Filter events from buffer
        timeline_events = []
        for event in self.event_buffer:
            if event.timestamp >= cutoff_time and event.mitre_technique_id:
                technique = await self.mitre_service.get_technique_by_id(event.mitre_technique_id)
                if technique:
                    timeline_events.append(AttackTimelineEvent(
                        timestamp=event.timestamp,
                        event_id=event.id,
                        technique_id=event.mitre_technique_id,
                        technique_name=technique.name,
                        tactic=technique.tactic.name if technique.tactic else "Unknown",
                        asset_name=event.asset_name or "Unknown",
                        severity=event.severity,
                        description=event.description
                    ))
        
        return sorted(timeline_events, key=lambda x: x.timestamp, reverse=True)
    
    async def get_mitre_visualization_data(self, db: Session) -> Dict[str, Any]:
        """Get MITRE ATT&CK visualization data"""
        
        # Get all tactics and techniques
        tactics = await self.mitre_service.get_all_tactics()
        techniques = await self.mitre_service.get_all_techniques()
        
        # Calculate technique usage from recent events
        technique_usage = {}
        for event in self.event_buffer:
            if event.mitre_technique_id:
                technique_usage[event.mitre_technique_id] = technique_usage.get(event.mitre_technique_id, 0) + 1
        
        # Build visualization data
        mitre_matrix = {}
        for tactic in tactics:
            tactic_techniques = [t for t in techniques if t.tactic_id == tactic.id]
            mitre_matrix[tactic.name] = {
                "tactic_id": tactic.external_id,
                "techniques": [
                    {
                        "id": tech.external_id,
                        "name": tech.name,
                        "usage_count": technique_usage.get(tech.external_id, 0),
                        "risk_level": self._calculate_technique_risk(tech.external_id, technique_usage)
                    }
                    for tech in tactic_techniques
                ]
            }
        
        return {
            "matrix": mitre_matrix,
            "total_techniques": len(techniques),
            "active_techniques": len(technique_usage),
            "top_techniques": sorted(technique_usage.items(), key=lambda x: x[1], reverse=True)[:10]
        }
    
    async def simulate_real_time_events(self, db: Session):
        """Simulate real-time events for demonstration (remove in production)"""
        
        import uuid

        # Use secure random for simulation
        secure_random = secrets.SystemRandom()

        # Get some assets for simulation
        assets = db.query(Asset).filter(Asset.is_active == True).limit(10).all()
        if not assets:
            return

        # Get some MITRE techniques
        techniques = await self.mitre_service.get_all_techniques()
        if not techniques:
            return

        # Generate random events
        event_types = [
            (EventType.THREAT_DETECTED, ThreatLevel.HIGH, "Suspicious activity detected"),
            (EventType.ATTACK_PATH_FOUND, ThreatLevel.MEDIUM, "New attack path discovered"),
            (EventType.VULNERABILITY_FOUND, ThreatLevel.HIGH, "Critical vulnerability identified"),
            (EventType.MITRE_CORRELATION, ThreatLevel.MEDIUM, "MITRE technique correlation found")
        ]

        for _ in range(secure_random.randint(1, 3)):
            asset = secure_random.choice(assets)
            technique = secure_random.choice(techniques)
            event_type, severity, title = secure_random.choice(event_types)
            
            event = RealTimeEvent(
                id=str(uuid.uuid4()),
                event_type=event_type,
                timestamp=datetime.utcnow(),
                severity=severity,
                title=title,
                description=f"{title} on {asset.name} using {technique.name}",
                asset_id=asset.id,
                asset_name=asset.name,
                mitre_technique_id=technique.external_id,
                risk_score=secure_random.uniform(5.0, 9.5),
                metadata={
                    "source": "simulation",
                    "confidence": secure_random.uniform(0.7, 0.95)
                }
            )
            
            await self.broadcast_event(event)
    
    async def _get_mitre_technique_distribution(self, db: Session) -> Dict[str, int]:
        """Get distribution of MITRE techniques from recent events"""
        
        distribution = {}
        for event in self.event_buffer:
            if event.mitre_technique_id:
                distribution[event.mitre_technique_id] = distribution.get(event.mitre_technique_id, 0) + 1
        
        return dict(sorted(distribution.items(), key=lambda x: x[1], reverse=True)[:20])
    
    async def _get_threat_trends(self, db: Session) -> Dict[str, List[int]]:
        """Get threat trends over time"""

        # Simulate trend data (in real implementation, this would come from historical data)
        secure_random = secrets.SystemRandom()
        hours = 24
        trends = {
            "threats": [secure_random.randint(5, 25) for _ in range(hours)],
            "attacks": [secure_random.randint(1, 8) for _ in range(hours)],
            "vulnerabilities": [secure_random.randint(2, 12) for _ in range(hours)]
        }

        return trends
    
    async def _get_asset_mitre_techniques(self, db: Session, asset_id: int) -> List[str]:
        """Get MITRE techniques associated with an asset"""

        # Simulate technique association (in real implementation, this would come from analysis)
        secure_random = secrets.SystemRandom()
        techniques = ["T1190", "T1078", "T1021", "T1566", "T1055", "T1003", "T1059", "T1105", "T1027"]
        return secure_random.sample(techniques, secure_random.randint(1, 5))
    
    def _calculate_technique_risk(self, technique_id: str, usage_data: Dict[str, int]) -> str:
        """Calculate risk level for a MITRE technique"""
        
        usage_count = usage_data.get(technique_id, 0)
        
        if usage_count >= 10:
            return "critical"
        elif usage_count >= 5:
            return "high"
        elif usage_count >= 2:
            return "medium"
        else:
            return "low"


# Global service instance
realtime_monitoring_service = RealTimeMonitoringService()
