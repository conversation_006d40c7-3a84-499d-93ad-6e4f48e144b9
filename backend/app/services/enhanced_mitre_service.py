"""Enhanced MITRE ATT&CK service with comprehensive data processing and caching."""

import asyncio
import hashlib
import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import aiohttp
import redis
from mitreattack.stix20 import MitreAttackData
from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from stix2 import Bundle, parse

from app.core.config import settings
from app.db.models.mitre import (
    MitreCampaign,
    MitreDataSource,
    MitreDataSync,
    MitreDomain,
    MitreEntityStatus,
    MitreGroup,
    MitreMatrix,
    MitreMitigation,
    MitreSoftware,
    MitreTactic,
    MitreTechnique,
)
from app.schemas.mitre import (
    MitreDataSyncCreate,
    MitreDataSyncResponse,
    MitreSearchRequest,
    MitreSearchResponse,
    MitreTechniqueResponse,
)


class EnhancedMitreService:
    """Enhanced MITRE ATT&CK service with comprehensive data processing."""
    
    def __init__(self, db: AsyncSession, redis_client: Optional[redis.Redis] = None):
        self.db = db
        self.redis = redis_client
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Data source URLs for different domains
        self.data_sources = {
            MitreDomain.ENTERPRISE: "https://raw.githubusercontent.com/mitre-attack/attack-stix-data/master/enterprise-attack/enterprise-attack.json",
            MitreDomain.MOBILE: "https://raw.githubusercontent.com/mitre-attack/attack-stix-data/master/mobile-attack/mobile-attack.json",
            MitreDomain.ICS: "https://raw.githubusercontent.com/mitre-attack/attack-stix-data/master/ics-attack/ics-attack.json"
        }
        
        # Cache configuration
        self.cache_ttl = 3600  # 1 hour
        self.cache_prefix = "mitre:"
        
        # Data processing configuration
        self.batch_size = 100
        self.max_retries = 3
        
    async def sync_domain_data(
        self,
        domain: MitreDomain,
        force_update: bool = False,
        triggered_by: Optional[str] = None
    ) -> MitreDataSyncResponse:
        """Synchronize MITRE ATT&CK data for a specific domain."""
        sync_id = f"sync_{domain.value}_{int(time.time())}"
        
        # Create sync record
        sync_data = MitreDataSyncCreate(
            sync_id=sync_id,
            domain=domain,
            sync_type="manual" if force_update else "automatic",
            status="pending",
            source_url=self.data_sources[domain],
            triggered_by=triggered_by
        )
        
        sync_record = MitreDataSync(**sync_data.model_dump())
        self.db.add(sync_record)
        await self.db.commit()
        await self.db.refresh(sync_record)
        
        try:
            # Update sync status
            sync_record.status = "running"
            sync_record.started_at = datetime.utcnow()
            await self.db.commit()
            
            # Check if update is needed
            if not force_update and await self._is_data_current(domain):
                self.logger.info(f"Data for {domain.value} is current, skipping update")
                sync_record.status = "completed"
                sync_record.completed_at = datetime.utcnow()
                sync_record.sync_results = {"message": "Data is current, no update needed"}
                await self.db.commit()
                return MitreDataSyncResponse.model_validate(sync_record)
            
            # Download and process data
            data_url = self.data_sources[domain]
            stix_data = await self._download_stix_data(data_url)
            
            # Calculate checksum
            data_checksum = hashlib.sha256(json.dumps(stix_data, sort_keys=True).encode()).hexdigest()
            sync_record.source_checksum = data_checksum
            
            # Process STIX data
            results = await self._process_stix_data(stix_data, domain, sync_record)
            
            # Update sync record with results
            sync_record.status = "completed"
            sync_record.completed_at = datetime.utcnow()
            sync_record.duration_seconds = (sync_record.completed_at - sync_record.started_at).total_seconds()
            sync_record.sync_results = results
            sync_record.total_entities = results.get("total_processed", 0)
            sync_record.processed_entities = results.get("successful", 0)
            sync_record.failed_entities = results.get("failed", 0)
            
            await self.db.commit()
            
            # Clear cache for this domain
            await self._clear_domain_cache(domain)
            
            self.logger.info(f"Successfully synchronized {domain.value} data: {results}")
            
        except Exception as e:
            self.logger.error(f"Failed to synchronize {domain.value} data: {e}")
            sync_record.status = "failed"
            sync_record.completed_at = datetime.utcnow()
            sync_record.error_message = str(e)
            sync_record.error_details = {"exception_type": type(e).__name__}
            await self.db.commit()
            raise
        
        return MitreDataSyncResponse.model_validate(sync_record)
    
    async def _download_stix_data(self, url: str) -> Dict[str, Any]:
        """Download STIX data from URL."""
        self.logger.info(f"Downloading STIX data from {url}")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"Failed to download data: HTTP {response.status}")
                
                data = await response.json()
                self.logger.info(f"Downloaded {len(data.get('objects', []))} STIX objects")
                return data
    
    async def _process_stix_data(
        self,
        stix_data: Dict[str, Any],
        domain: MitreDomain,
        sync_record: MitreDataSync
    ) -> Dict[str, Any]:
        """Process STIX data and update database."""
        results = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "by_type": {},
            "errors": []
        }
        
        try:
            # Parse STIX bundle
            bundle = parse(stix_data)
            objects = bundle.objects if hasattr(bundle, 'objects') else []
            
            # Group objects by type
            objects_by_type = {}
            for obj in objects:
                obj_type = obj.type
                if obj_type not in objects_by_type:
                    objects_by_type[obj_type] = []
                objects_by_type[obj_type].append(obj)
            
            # Process each object type
            for obj_type, objs in objects_by_type.items():
                type_results = await self._process_objects_by_type(obj_type, objs, domain)
                results["by_type"][obj_type] = type_results
                results["successful"] += type_results.get("successful", 0)
                results["failed"] += type_results.get("failed", 0)
                results["total_processed"] += len(objs)
                
                if type_results.get("errors"):
                    results["errors"].extend(type_results["errors"])
            
        except Exception as e:
            self.logger.error(f"Error processing STIX data: {e}")
            results["errors"].append(f"STIX processing error: {str(e)}")
            results["failed"] = results["total_processed"]
            results["successful"] = 0
        
        return results
    
    async def _process_objects_by_type(
        self,
        obj_type: str,
        objects: List[Any],
        domain: MitreDomain
    ) -> Dict[str, Any]:
        """Process STIX objects of a specific type."""
        results = {"successful": 0, "failed": 0, "errors": []}
        
        # Map STIX types to our models
        type_mapping = {
            "attack-pattern": self._process_technique,
            "x-mitre-tactic": self._process_tactic,
            "intrusion-set": self._process_group,
            "malware": self._process_software,
            "tool": self._process_software,
            "course-of-action": self._process_mitigation,
            "x-mitre-data-source": self._process_data_source,
            "campaign": self._process_campaign,
            "x-mitre-matrix": self._process_matrix,
        }
        
        processor = type_mapping.get(obj_type)
        if not processor:
            self.logger.debug(f"No processor for STIX type: {obj_type}")
            return results
        
        # Process objects in batches
        for i in range(0, len(objects), self.batch_size):
            batch = objects[i:i + self.batch_size]
            
            for obj in batch:
                try:
                    await processor(obj, domain)
                    results["successful"] += 1
                except Exception as e:
                    self.logger.error(f"Error processing {obj_type} {getattr(obj, 'id', 'unknown')}: {e}")
                    results["failed"] += 1
                    results["errors"].append(f"{obj_type} {getattr(obj, 'id', 'unknown')}: {str(e)}")
            
            # Commit batch
            await self.db.commit()
        
        return results
    
    async def _process_technique(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX attack-pattern (technique) object."""
        # Extract technique ID from external references
        technique_id = None
        for ref in getattr(stix_obj, 'external_references', []):
            if ref.get('source_name') == 'mitre-attack':
                technique_id = ref.get('external_id')
                break
        
        if not technique_id:
            raise ValueError(f"No MITRE technique ID found for {stix_obj.id}")
        
        # Check if technique already exists
        existing = await self.db.execute(
            select(MitreTechnique).where(MitreTechnique.technique_id == technique_id)
        )
        technique = existing.scalar_one_or_none()
        
        if not technique:
            technique = MitreTechnique(id=uuid.uuid4(), technique_id=technique_id)
            self.db.add(technique)
        
        # Update technique data
        technique.name = stix_obj.name
        technique.description = getattr(stix_obj, 'description', None)
        technique.domain = domain
        technique.status = MitreEntityStatus.ACTIVE
        
        # Handle sub-techniques
        if '.' in technique_id:
            technique.is_subtechnique = True
            technique.parent_technique_id = technique_id.split('.')[0]
        
        # Extract additional properties
        technique.platforms = getattr(stix_obj, 'x_mitre_platforms', None)
        technique.data_sources = getattr(stix_obj, 'x_mitre_data_sources', None)
        technique.permissions_required = getattr(stix_obj, 'x_mitre_permissions_required', None)
        technique.defense_bypassed = getattr(stix_obj, 'x_mitre_defense_bypassed', None)
        technique.supports_remote = getattr(stix_obj, 'x_mitre_remote_support', None)
        technique.impact_type = getattr(stix_obj, 'x_mitre_impact_type', None)
        
        # Extract kill chain phases
        kill_chain_phases = []
        for phase in getattr(stix_obj, 'kill_chain_phases', []):
            if phase.get('kill_chain_name') == 'mitre-attack':
                kill_chain_phases.append(phase)
        technique.kill_chain_phases = kill_chain_phases if kill_chain_phases else None
        
        # Extract references
        references = []
        for ref in getattr(stix_obj, 'external_references', []):
            references.append({
                'source_name': ref.get('source_name'),
                'external_id': ref.get('external_id'),
                'url': ref.get('url'),
                'description': ref.get('description')
            })
        technique.references = references if references else None
        
        # Update metadata
        technique.modified_date = datetime.utcnow()
        technique.version = getattr(stix_obj, 'x_mitre_version', '1.0')
        
        # Build search vector for full-text search
        search_terms = [technique.name]
        if technique.description:
            search_terms.append(technique.description)
        technique.search_vector = ' '.join(search_terms).lower()
    
    async def _process_tactic(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX x-mitre-tactic object."""
        # Extract tactic ID from external references
        tactic_id = None
        for ref in getattr(stix_obj, 'external_references', []):
            if ref.get('source_name') == 'mitre-attack':
                tactic_id = ref.get('external_id')
                break
        
        if not tactic_id:
            raise ValueError(f"No MITRE tactic ID found for {stix_obj.id}")
        
        # Check if tactic already exists
        existing = await self.db.execute(
            select(MitreTactic).where(MitreTactic.tactic_id == tactic_id)
        )
        tactic = existing.scalar_one_or_none()
        
        if not tactic:
            tactic = MitreTactic(id=uuid.uuid4(), tactic_id=tactic_id)
            self.db.add(tactic)
        
        # Update tactic data
        tactic.name = stix_obj.name
        tactic.description = getattr(stix_obj, 'description', None)
        tactic.domain = domain
        tactic.status = MitreEntityStatus.ACTIVE
        tactic.short_name = getattr(stix_obj, 'x_mitre_shortname', None)
        
        # Extract references
        references = []
        for ref in getattr(stix_obj, 'external_references', []):
            references.append({
                'source_name': ref.get('source_name'),
                'external_id': ref.get('external_id'),
                'url': ref.get('url'),
                'description': ref.get('description')
            })
        tactic.references = references if references else None
        
        # Update metadata
        tactic.modified_date = datetime.utcnow()
        tactic.version = getattr(stix_obj, 'x_mitre_version', '1.0')
    
    async def _process_group(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX intrusion-set (group) object."""
        # Extract group ID from external references
        group_id = None
        for ref in getattr(stix_obj, 'external_references', []):
            if ref.get('source_name') == 'mitre-attack':
                group_id = ref.get('external_id')
                break
        
        if not group_id:
            raise ValueError(f"No MITRE group ID found for {stix_obj.id}")
        
        # Check if group already exists
        existing = await self.db.execute(
            select(MitreGroup).where(MitreGroup.group_id == group_id)
        )
        group = existing.scalar_one_or_none()
        
        if not group:
            group = MitreGroup(id=uuid.uuid4(), group_id=group_id)
            self.db.add(group)
        
        # Update group data
        group.name = stix_obj.name
        group.description = getattr(stix_obj, 'description', None)
        group.domain = domain
        group.status = MitreEntityStatus.ACTIVE
        group.aliases = getattr(stix_obj, 'aliases', None)
        
        # Update metadata
        group.modified_date = datetime.utcnow()
        group.version = getattr(stix_obj, 'x_mitre_version', '1.0')
        
        # Build search vector
        search_terms = [group.name]
        if group.description:
            search_terms.append(group.description)
        if group.aliases:
            search_terms.extend(group.aliases)
        group.search_vector = ' '.join(search_terms).lower()
    
    async def _process_software(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX malware/tool (software) object."""
        # Extract software ID from external references
        software_id = None
        for ref in getattr(stix_obj, 'external_references', []):
            if ref.get('source_name') == 'mitre-attack':
                software_id = ref.get('external_id')
                break
        
        if not software_id:
            raise ValueError(f"No MITRE software ID found for {stix_obj.id}")
        
        # Check if software already exists
        existing = await self.db.execute(
            select(MitreSoftware).where(MitreSoftware.software_id == software_id)
        )
        software = existing.scalar_one_or_none()
        
        if not software:
            software = MitreSoftware(id=uuid.uuid4(), software_id=software_id)
            self.db.add(software)
        
        # Update software data
        software.name = stix_obj.name
        software.description = getattr(stix_obj, 'description', None)
        software.domain = domain
        software.status = MitreEntityStatus.ACTIVE
        software.software_type = stix_obj.type  # malware or tool
        software.labels = getattr(stix_obj, 'labels', None)
        software.platforms = getattr(stix_obj, 'x_mitre_platforms', None)
        
        # Update metadata
        software.modified_date = datetime.utcnow()
        software.version = getattr(stix_obj, 'x_mitre_version', '1.0')
        
        # Build search vector
        search_terms = [software.name]
        if software.description:
            search_terms.append(software.description)
        software.search_vector = ' '.join(search_terms).lower()
    
    async def _process_mitigation(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX course-of-action (mitigation) object."""
        # Extract mitigation ID from external references
        mitigation_id = None
        for ref in getattr(stix_obj, 'external_references', []):
            if ref.get('source_name') == 'mitre-attack':
                mitigation_id = ref.get('external_id')
                break
        
        if not mitigation_id:
            raise ValueError(f"No MITRE mitigation ID found for {stix_obj.id}")
        
        # Check if mitigation already exists
        existing = await self.db.execute(
            select(MitreMitigation).where(MitreMitigation.mitigation_id == mitigation_id)
        )
        mitigation = existing.scalar_one_or_none()
        
        if not mitigation:
            mitigation = MitreMitigation(id=uuid.uuid4(), mitigation_id=mitigation_id)
            self.db.add(mitigation)
        
        # Update mitigation data
        mitigation.name = stix_obj.name
        mitigation.description = getattr(stix_obj, 'description', None)
        mitigation.domain = domain
        mitigation.status = MitreEntityStatus.ACTIVE
        
        # Update metadata
        mitigation.modified_date = datetime.utcnow()
        mitigation.version = getattr(stix_obj, 'x_mitre_version', '1.0')
        
        # Build search vector
        search_terms = [mitigation.name]
        if mitigation.description:
            search_terms.append(mitigation.description)
        mitigation.search_vector = ' '.join(search_terms).lower()
    
    async def _process_data_source(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX x-mitre-data-source object."""
        # This is a placeholder for data source processing
        # Implementation would be similar to other processors
        pass
    
    async def _process_campaign(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX campaign object."""
        # This is a placeholder for campaign processing
        # Implementation would be similar to other processors
        pass
    
    async def _process_matrix(self, stix_obj: Any, domain: MitreDomain) -> None:
        """Process STIX x-mitre-matrix object."""
        # This is a placeholder for matrix processing
        # Implementation would be similar to other processors
        pass
    
    async def _is_data_current(self, domain: MitreDomain) -> bool:
        """Check if data for domain is current (less than 24 hours old)."""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        result = await self.db.execute(
            select(MitreDataSync)
            .where(
                and_(
                    MitreDataSync.domain == domain,
                    MitreDataSync.status == "completed",
                    MitreDataSync.completed_at > cutoff_time
                )
            )
            .order_by(MitreDataSync.completed_at.desc())
            .limit(1)
        )
        
        return result.scalar_one_or_none() is not None
    
    async def _clear_domain_cache(self, domain: MitreDomain) -> None:
        """Clear Redis cache for a specific domain."""
        if not self.redis:
            return
        
        pattern = f"{self.cache_prefix}{domain.value}:*"
        keys = self.redis.keys(pattern)
        if keys:
            self.redis.delete(*keys)
            self.logger.info(f"Cleared {len(keys)} cache keys for domain {domain.value}")
    
    async def get_sync_status(self, sync_id: str) -> Optional[MitreDataSyncResponse]:
        """Get sync status by sync ID."""
        result = await self.db.execute(
            select(MitreDataSync).where(MitreDataSync.sync_id == sync_id)
        )
        sync_record = result.scalar_one_or_none()
        
        if not sync_record:
            return None
        
        return MitreDataSyncResponse.model_validate(sync_record)
    
    async def list_sync_history(
        self,
        domain: Optional[MitreDomain] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[MitreDataSyncResponse]:
        """List sync history with optional domain filter."""
        query = select(MitreDataSync).order_by(MitreDataSync.started_at.desc())
        
        if domain:
            query = query.where(MitreDataSync.domain == domain)
        
        query = query.limit(limit).offset(offset)
        
        result = await self.db.execute(query)
        sync_records = result.scalars().all()
        
        return [MitreDataSyncResponse.model_validate(record) for record in sync_records]
