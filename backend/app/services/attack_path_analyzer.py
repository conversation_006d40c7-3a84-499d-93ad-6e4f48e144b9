"""
Attack Path Analysis Service

This service provides comprehensive attack path analysis capabilities,
including MITRE ATT&CK framework integration and risk assessment.
"""

import logging
import asyncio
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json

from app.services.graph_engine import GraphEngine, AttackPath, BlastRadiusResult, AttackTechnique, PathType
from app.db.models.asset import Asset, AssetRelationship
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


@dataclass
class AttackScenario:
    """Represents a complete attack scenario with multiple paths."""
    
    scenario_id: str
    name: str
    description: str
    threat_actor: str
    attack_paths: List[AttackPath]
    total_risk_score: float
    likelihood: float
    impact_score: float
    estimated_duration: int  # hours
    required_resources: List[str]
    detection_probability: float
    mitigation_strategies: List[str]
    created_at: datetime
    
    @property
    def criticality_level(self) -> str:
        """Determine criticality level based on risk score."""
        if self.total_risk_score >= 80:
            return "CRITICAL"
        elif self.total_risk_score >= 60:
            return "HIGH"
        elif self.total_risk_score >= 40:
            return "MEDIUM"
        else:
            return "LOW"


@dataclass
class MitreAttackMapping:
    """Maps attack paths to MITRE ATT&CK framework."""
    
    technique_id: str
    technique_name: str
    tactic: str
    description: str
    platforms: List[str]
    data_sources: List[str]
    mitigations: List[str]
    detection_methods: List[str]


class AttackPathAnalyzer:
    """Advanced attack path analysis with MITRE ATT&CK integration."""
    
    def __init__(self, db: Session):
        self.db = db
        self.graph_engine = GraphEngine(max_workers=4)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # MITRE ATT&CK technique mappings
        self.mitre_mappings = self._load_mitre_mappings()
        
        # Analysis cache
        self.scenario_cache = {}
        self.analysis_cache = {}
    
    def _load_mitre_mappings(self) -> Dict[AttackTechnique, MitreAttackMapping]:
        """Load MITRE ATT&CK technique mappings."""
        mappings = {
            AttackTechnique.INITIAL_ACCESS: MitreAttackMapping(
                technique_id="TA0001",
                technique_name="Initial Access",
                tactic="Initial Access",
                description="Techniques used to gain an initial foothold within a network",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Network Traffic", "Authentication Logs", "Web Logs"],
                mitigations=["Network Segmentation", "Multi-factor Authentication", "Application Isolation"],
                detection_methods=["Anomaly Detection", "Signature-based Detection", "Behavioral Analysis"]
            ),
            AttackTechnique.EXECUTION: MitreAttackMapping(
                technique_id="TA0002",
                technique_name="Execution",
                tactic="Execution",
                description="Techniques that result in adversary-controlled code running on a local or remote system",
                platforms=["Windows", "Linux", "macOS"],
                data_sources=["Process Monitoring", "Command Line", "PowerShell Logs"],
                mitigations=["Application Control", "Execution Prevention", "Privileged Account Management"],
                detection_methods=["Process Monitoring", "Command Line Analysis", "Script Blocking"]
            ),
            AttackTechnique.PERSISTENCE: MitreAttackMapping(
                technique_id="TA0003",
                technique_name="Persistence",
                tactic="Persistence",
                description="Techniques that adversaries use to keep access to systems across restarts",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["File Monitoring", "Registry", "Services", "Scheduled Tasks"],
                mitigations=["User Account Management", "Privileged Account Management", "Operating System Configuration"],
                detection_methods=["File Integrity Monitoring", "Registry Monitoring", "Service Monitoring"]
            ),
            AttackTechnique.PRIVILEGE_ESCALATION: MitreAttackMapping(
                technique_id="TA0004",
                technique_name="Privilege Escalation",
                tactic="Privilege Escalation",
                description="Techniques that adversaries use to gain higher-level permissions",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Authentication Logs", "Process Monitoring", "Windows Event Logs"],
                mitigations=["Privileged Account Management", "User Account Control", "Operating System Configuration"],
                detection_methods=["Privilege Monitoring", "Authentication Analysis", "Process Monitoring"]
            ),
            AttackTechnique.DEFENSE_EVASION: MitreAttackMapping(
                technique_id="TA0005",
                technique_name="Defense Evasion",
                tactic="Defense Evasion",
                description="Techniques that adversaries use to avoid detection",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["File Monitoring", "Process Monitoring", "Network Traffic"],
                mitigations=["Application Control", "Behavior Prevention", "Code Signing"],
                detection_methods=["Behavioral Analysis", "Signature Detection", "Anomaly Detection"]
            ),
            AttackTechnique.CREDENTIAL_ACCESS: MitreAttackMapping(
                technique_id="TA0006",
                technique_name="Credential Access",
                tactic="Credential Access",
                description="Techniques for stealing credentials like account names and passwords",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Authentication Logs", "Process Monitoring", "PowerShell Logs"],
                mitigations=["Multi-factor Authentication", "Privileged Account Management", "Password Policies"],
                detection_methods=["Credential Monitoring", "Authentication Analysis", "Behavioral Analysis"]
            ),
            AttackTechnique.DISCOVERY: MitreAttackMapping(
                technique_id="TA0007",
                technique_name="Discovery",
                tactic="Discovery",
                description="Techniques an adversary may use to gain knowledge about the system",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Process Monitoring", "Command Line", "Network Traffic"],
                mitigations=["Network Segmentation", "Operating System Configuration"],
                detection_methods=["Process Monitoring", "Network Monitoring", "Command Line Analysis"]
            ),
            AttackTechnique.LATERAL_MOVEMENT: MitreAttackMapping(
                technique_id="TA0008",
                technique_name="Lateral Movement",
                tactic="Lateral Movement",
                description="Techniques that adversaries use to enter and control remote systems",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Authentication Logs", "Network Traffic", "Process Monitoring"],
                mitigations=["Network Segmentation", "Multi-factor Authentication", "Privileged Account Management"],
                detection_methods=["Network Monitoring", "Authentication Analysis", "Behavioral Analysis"]
            ),
            AttackTechnique.COLLECTION: MitreAttackMapping(
                technique_id="TA0009",
                technique_name="Collection",
                tactic="Collection",
                description="Techniques adversaries may use to gather information",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["File Monitoring", "Process Monitoring", "Network Traffic"],
                mitigations=["Data Loss Prevention", "Operating System Configuration"],
                detection_methods=["File Monitoring", "Process Monitoring", "Data Loss Prevention"]
            ),
            AttackTechnique.COMMAND_AND_CONTROL: MitreAttackMapping(
                technique_id="TA0011",
                technique_name="Command and Control",
                tactic="Command and Control",
                description="Techniques that adversaries may use to communicate with systems under their control",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Network Traffic", "Process Monitoring", "Netflow/Enclave Netflow"],
                mitigations=["Network Intrusion Prevention", "Network Segmentation", "Restrict Web-Based Content"],
                detection_methods=["Network Monitoring", "Traffic Analysis", "DNS Monitoring"]
            ),
            AttackTechnique.EXFILTRATION: MitreAttackMapping(
                technique_id="TA0010",
                technique_name="Exfiltration",
                tactic="Exfiltration",
                description="Techniques that adversaries may use to steal data from your network",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Network Traffic", "File Monitoring", "Process Monitoring"],
                mitigations=["Data Loss Prevention", "Network Segmentation", "Restrict Web-Based Content"],
                detection_methods=["Network Monitoring", "Data Loss Prevention", "File Monitoring"]
            ),
            AttackTechnique.IMPACT: MitreAttackMapping(
                technique_id="TA0040",
                technique_name="Impact",
                tactic="Impact",
                description="Techniques that adversaries use to disrupt availability or compromise integrity",
                platforms=["Windows", "Linux", "macOS", "Cloud"],
                data_sources=["Process Monitoring", "File Monitoring", "Network Traffic"],
                mitigations=["Data Backup", "Behavior Prevention", "Operating System Configuration"],
                detection_methods=["Process Monitoring", "File Integrity Monitoring", "System Monitoring"]
            )
        }
        return mappings
    
    async def initialize_from_database(self) -> None:
        """Initialize graph engine with assets and relationships from database."""
        self.logger.info("Initializing attack path analyzer from database...")
        
        try:
            # Load all assets
            assets = self.db.query(Asset).all()
            for asset in assets:
                asset_data = {
                    "asset_type": asset.asset_type.value if asset.asset_type else "unknown",
                    "provider": asset.provider.value if asset.provider else "unknown",
                    "status": asset.status.value if asset.status else "unknown",
                    "environment": asset.environment,
                    "owner": asset.owner,
                    "team": asset.team,
                    "risk_score": asset.risk_score or 50,
                    "risk_level": asset.risk_level.value if asset.risk_level else "medium",
                    "business_criticality": getattr(asset, "business_criticality", "medium"),
                    "data_classification": getattr(asset, "data_classification", "internal"),
                    "contains_pii": getattr(asset, "contains_pii", False),
                    "gdpr_applicable": getattr(asset, "gdpr_applicable", False),
                    "public_facing": any("public" in str(ip) for ip in (asset.ip_addresses or [])),
                    "configuration": asset.configuration or {},
                    "properties": asset.properties or {}
                }
                
                self.graph_engine.add_asset(str(asset.id), asset_data)
            
            # Load all relationships
            relationships = self.db.query(AssetRelationship).all()
            for rel in relationships:
                rel_data = {
                    "relationship_type": rel.relationship_type.value if rel.relationship_type else "unknown",
                    "protocol": rel.protocol,
                    "port": rel.port,
                    "direction": rel.direction.value if rel.direction else "bidirectional",
                    "encrypted": getattr(rel, "encrypted", False),
                    "authenticated": getattr(rel, "authenticated", False),
                    "monitored": getattr(rel, "monitored", False),
                    "firewall_protected": getattr(rel, "firewall_protected", False),
                    "description": rel.description,
                    "confidence": rel.confidence or 0.8
                }
                
                self.graph_engine.add_relationship(
                    str(rel.source_asset_id), 
                    str(rel.target_asset_id), 
                    rel_data
                )
            
            self.logger.info(f"Initialized graph with {len(assets)} assets and {len(relationships)} relationships")
            
        except Exception as e:
            self.logger.error(f"Error initializing from database: {e}")
            raise
    
    async def analyze_attack_paths(self, source_asset_id: str, 
                                 target_asset_ids: Optional[List[str]] = None,
                                 max_path_length: int = 5,
                                 max_paths_per_target: int = 5) -> List[AttackPath]:
        """Analyze attack paths from source to target assets."""
        self.logger.info(f"Analyzing attack paths from {source_asset_id}")
        
        all_paths = []
        
        if target_asset_ids:
            # Analyze paths to specific targets
            for target_id in target_asset_ids:
                paths = await self.graph_engine.find_attack_paths(
                    source_asset_id, target_id, max_path_length, max_paths_per_target
                )
                all_paths.extend(paths)
        else:
            # Find paths to all high-value targets
            high_value_targets = self._identify_high_value_targets()
            for target_id in high_value_targets:
                if target_id != source_asset_id:
                    paths = await self.graph_engine.find_attack_paths(
                        source_asset_id, target_id, max_path_length, 1
                    )
                    all_paths.extend(paths)
        
        # Sort by criticality
        all_paths.sort(key=lambda x: x.criticality_score, reverse=True)
        
        self.logger.info(f"Found {len(all_paths)} attack paths")
        return all_paths
    
    def _identify_high_value_targets(self) -> List[str]:
        """Identify high-value target assets."""
        high_value_targets = []
        
        for asset_id, asset_data in self.graph_engine.asset_metadata.items():
            # Critical business assets
            if asset_data.get("business_criticality") == "critical":
                high_value_targets.append(asset_id)
            
            # Assets with sensitive data
            if asset_data.get("contains_pii", False):
                high_value_targets.append(asset_id)
            
            # High-risk assets
            if asset_data.get("risk_score", 0) >= 80:
                high_value_targets.append(asset_id)
            
            # Database and storage assets
            asset_type = asset_data.get("asset_type", "").lower()
            if any(data_type in asset_type for data_type in ["database", "storage", "backup"]):
                high_value_targets.append(asset_id)
        
        return list(set(high_value_targets))  # Remove duplicates
    
    async def create_attack_scenario(self, scenario_name: str, 
                                   threat_actor: str,
                                   entry_points: List[str],
                                   objectives: List[str]) -> AttackScenario:
        """Create a comprehensive attack scenario."""
        self.logger.info(f"Creating attack scenario: {scenario_name}")
        
        all_attack_paths = []
        
        # Find paths from each entry point to each objective
        for entry_point in entry_points:
            for objective in objectives:
                paths = await self.graph_engine.find_attack_paths(
                    entry_point, objective, max_path_length=5, max_paths=3
                )
                all_attack_paths.extend(paths)
        
        if not all_attack_paths:
            self.logger.warning(f"No attack paths found for scenario {scenario_name}")
        
        # Calculate scenario metrics
        total_risk_score = self._calculate_scenario_risk(all_attack_paths)
        likelihood = self._calculate_scenario_likelihood(all_attack_paths)
        impact_score = self._calculate_scenario_impact(all_attack_paths)
        estimated_duration = self._estimate_scenario_duration(all_attack_paths)
        required_resources = self._identify_required_resources(all_attack_paths)
        detection_probability = self._calculate_detection_probability(all_attack_paths)
        mitigation_strategies = self._generate_mitigation_strategies(all_attack_paths)
        
        scenario = AttackScenario(
            scenario_id=f"scenario_{int(datetime.utcnow().timestamp())}",
            name=scenario_name,
            description=f"Attack scenario targeting {len(objectives)} objectives from {len(entry_points)} entry points",
            threat_actor=threat_actor,
            attack_paths=all_attack_paths,
            total_risk_score=total_risk_score,
            likelihood=likelihood,
            impact_score=impact_score,
            estimated_duration=estimated_duration,
            required_resources=required_resources,
            detection_probability=detection_probability,
            mitigation_strategies=mitigation_strategies,
            created_at=datetime.utcnow()
        )
        
        # Cache scenario
        self.scenario_cache[scenario.scenario_id] = scenario
        
        self.logger.info(f"Created attack scenario with {len(all_attack_paths)} paths, risk score: {total_risk_score:.1f}")
        return scenario
    
    def _calculate_scenario_risk(self, attack_paths: List[AttackPath]) -> float:
        """Calculate overall risk score for attack scenario."""
        if not attack_paths:
            return 0.0
        
        # Use maximum risk score among all paths
        max_risk = max(path.risk_score for path in attack_paths)
        
        # Adjust for number of paths (more paths = higher risk)
        path_multiplier = 1.0 + (len(attack_paths) - 1) * 0.1
        
        return min(100.0, max_risk * path_multiplier)
    
    def _calculate_scenario_likelihood(self, attack_paths: List[AttackPath]) -> float:
        """Calculate likelihood of scenario success."""
        if not attack_paths:
            return 0.0
        
        # Use maximum likelihood among all paths
        return max(path.likelihood for path in attack_paths)
    
    def _calculate_scenario_impact(self, attack_paths: List[AttackPath]) -> float:
        """Calculate potential impact of scenario."""
        if not attack_paths:
            return 0.0
        
        # Sum impact scores from all unique targets
        unique_targets = set(path.target_asset_id for path in attack_paths)
        total_impact = 0.0
        
        for target in unique_targets:
            target_paths = [path for path in attack_paths if path.target_asset_id == target]
            max_target_impact = max(path.impact_score for path in target_paths)
            total_impact += max_target_impact
        
        return min(100.0, total_impact)
    
    def _estimate_scenario_duration(self, attack_paths: List[AttackPath]) -> int:
        """Estimate scenario duration in hours."""
        if not attack_paths:
            return 0
        
        # Use maximum time among all paths
        max_time_minutes = max(path.estimated_time for path in attack_paths)
        return max(1, max_time_minutes // 60)  # Convert to hours
    
    def _identify_required_resources(self, attack_paths: List[AttackPath]) -> List[str]:
        """Identify resources required for attack scenario."""
        all_resources = set()
        
        for path in attack_paths:
            all_resources.update(path.required_privileges)
            
            # Add technique-specific resources
            for technique in path.attack_techniques:
                if technique == AttackTechnique.INITIAL_ACCESS:
                    all_resources.add("network_access")
                elif technique == AttackTechnique.CREDENTIAL_ACCESS:
                    all_resources.add("credential_harvesting_tools")
                elif technique == AttackTechnique.LATERAL_MOVEMENT:
                    all_resources.add("remote_access_tools")
                elif technique == AttackTechnique.PRIVILEGE_ESCALATION:
                    all_resources.add("exploitation_tools")
        
        return list(all_resources)
    
    def _calculate_detection_probability(self, attack_paths: List[AttackPath]) -> float:
        """Calculate probability of scenario detection."""
        if not attack_paths:
            return 1.0
        
        # Calculate average detection difficulty
        avg_difficulty = sum(path.detection_difficulty for path in attack_paths) / len(attack_paths)
        
        # Detection probability is inverse of difficulty
        return 1.0 - avg_difficulty
    
    def _generate_mitigation_strategies(self, attack_paths: List[AttackPath]) -> List[str]:
        """Generate mitigation strategies for attack scenario."""
        strategies = set()
        
        # Collect all attack techniques
        all_techniques = set()
        for path in attack_paths:
            all_techniques.update(path.attack_techniques)
        
        # Generate mitigations based on techniques
        for technique in all_techniques:
            if technique in self.mitre_mappings:
                strategies.update(self.mitre_mappings[technique].mitigations)
        
        return list(strategies)
    
    async def calculate_blast_radius(self, source_asset_id: str, 
                                   max_degrees: int = 5) -> BlastRadiusResult:
        """Calculate blast radius from compromised asset."""
        return await self.graph_engine.calculate_blast_radius(source_asset_id, max_degrees)
    
    def get_mitre_attack_mapping(self, attack_path: AttackPath) -> Dict[str, Any]:
        """Get MITRE ATT&CK mapping for attack path."""
        mapping = {
            "tactics": [],
            "techniques": [],
            "mitigations": [],
            "detection_methods": []
        }
        
        for technique in attack_path.attack_techniques:
            if technique in self.mitre_mappings:
                mitre_data = self.mitre_mappings[technique]
                mapping["tactics"].append(mitre_data.tactic)
                mapping["techniques"].append({
                    "id": mitre_data.technique_id,
                    "name": mitre_data.technique_name,
                    "description": mitre_data.description
                })
                mapping["mitigations"].extend(mitre_data.mitigations)
                mapping["detection_methods"].extend(mitre_data.detection_methods)
        
        # Remove duplicates
        mapping["tactics"] = list(set(mapping["tactics"]))
        mapping["mitigations"] = list(set(mapping["mitigations"]))
        mapping["detection_methods"] = list(set(mapping["detection_methods"]))
        
        return mapping
    
    def export_analysis_results(self, format: str = "json") -> str:
        """Export analysis results in various formats."""
        if format.lower() == "json":
            results = {
                "scenarios": [asdict(scenario) for scenario in self.scenario_cache.values()],
                "graph_statistics": self.graph_engine.get_graph_statistics(),
                "mitre_mappings": {k.value: asdict(v) for k, v in self.mitre_mappings.items()},
                "export_timestamp": datetime.utcnow().isoformat()
            }
            return json.dumps(results, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def clear_cache(self) -> None:
        """Clear all analysis caches."""
        self.scenario_cache.clear()
        self.analysis_cache.clear()
        self.graph_engine.clear_cache()
        self.logger.info("Cleared all analysis caches")
