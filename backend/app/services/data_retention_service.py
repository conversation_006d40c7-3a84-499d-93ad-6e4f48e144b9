"""Data retention and cleanup service for enterprise data management."""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session

from app.db.models.asset_robust import (
    RobustAsset,
    AssetAuditLog,
    AssetVersion,
    AssetLock,
    AssetBackup,
    DataRetentionPolicy,
)

logger = logging.getLogger(__name__)


class DataRetentionService:
    """Service for managing data retention and cleanup operations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def cleanup_expired_data(self, dry_run: bool = True) -> Dict[str, int]:
        """Cleanup expired data based on retention policies."""
        cleanup_stats = {
            "soft_deleted_assets_purged": 0,
            "expired_audit_logs_archived": 0,
            "old_versions_compressed": 0,
            "expired_locks_cleaned": 0,
            "expired_backups_removed": 0,
            "total_space_freed_mb": 0
        }
        
        try:
            # 1. Purge soft-deleted assets past retention period
            cleanup_stats["soft_deleted_assets_purged"] = self._purge_expired_assets(dry_run)
            
            # 2. Archive old audit logs
            cleanup_stats["expired_audit_logs_archived"] = self._archive_old_audit_logs(dry_run)
            
            # 3. Compress old asset versions
            cleanup_stats["old_versions_compressed"] = self._compress_old_versions(dry_run)
            
            # 4. Clean expired locks
            cleanup_stats["expired_locks_cleaned"] = self._clean_expired_locks(dry_run)
            
            # 5. Remove expired backups
            cleanup_stats["expired_backups_removed"] = self._remove_expired_backups(dry_run)
            
            if not dry_run:
                self.db.commit()
                self.logger.info(f"Data cleanup completed: {cleanup_stats}")
            else:
                self.logger.info(f"Data cleanup dry run completed: {cleanup_stats}")
            
            return cleanup_stats
            
        except Exception as e:
            if not dry_run:
                self.db.rollback()
            self.logger.error(f"Data cleanup failed: {e}")
            raise
    
    def _purge_expired_assets(self, dry_run: bool = True) -> int:
        """Purge soft-deleted assets past their retention period."""
        now = datetime.utcnow()
        
        # Find assets eligible for purging
        expired_assets = self.db.query(RobustAsset).filter(
            and_(
                RobustAsset.is_deleted == True,
                RobustAsset.purge_after.isnot(None),
                RobustAsset.purge_after <= now
            )
        ).all()
        
        if dry_run:
            self.logger.info(f"Would purge {len(expired_assets)} expired assets")
            return len(expired_assets)
        
        # Actually purge the assets
        purged_count = 0
        for asset in expired_assets:
            try:
                # Create final audit log
                final_audit = AssetAuditLog(
                    asset_id=asset.id,
                    action="purge",
                    table_name="assets_robust",
                    record_id=str(asset.id),
                    timestamp=now,
                    change_reason="Automatic purge due to retention policy",
                    before_snapshot=self._create_asset_snapshot(asset)
                )
                self.db.add(final_audit)
                
                # Delete related records first
                self._delete_asset_related_records(asset.id)
                
                # Delete the asset
                self.db.delete(asset)
                purged_count += 1
                
                self.logger.info(f"Purged expired asset: {asset.name} (ID: {asset.id})")
                
            except Exception as e:
                self.logger.error(f"Failed to purge asset {asset.id}: {e}")
        
        return purged_count
    
    def _archive_old_audit_logs(self, dry_run: bool = True) -> int:
        """Archive audit logs older than retention period."""
        # Archive logs older than 2 years for compliance
        archive_cutoff = datetime.utcnow() - timedelta(days=730)
        
        old_logs = self.db.query(AssetAuditLog).filter(
            AssetAuditLog.timestamp < archive_cutoff
        ).all()
        
        if dry_run:
            self.logger.info(f"Would archive {len(old_logs)} old audit logs")
            return len(old_logs)
        
        # In a real implementation, you would:
        # 1. Export logs to cold storage (S3 Glacier, etc.)
        # 2. Compress the data
        # 3. Delete from active database
        
        archived_count = 0
        for log in old_logs:
            try:
                # Here you would export to cold storage
                # For now, we'll just mark as archived
                log.is_archived = True  # Would need to add this field
                archived_count += 1
                
            except Exception as e:
                self.logger.error(f"Failed to archive audit log {log.id}: {e}")
        
        return archived_count
    
    def _compress_old_versions(self, dry_run: bool = True) -> int:
        """Compress old asset versions to save space."""
        # Compress versions older than 90 days
        compress_cutoff = datetime.utcnow() - timedelta(days=90)
        
        old_versions = self.db.query(AssetVersion).filter(
            and_(
                AssetVersion.created_at < compress_cutoff,
                AssetVersion.compression_type.is_(None)  # Not already compressed
            )
        ).all()
        
        if dry_run:
            self.logger.info(f"Would compress {len(old_versions)} old versions")
            return len(old_versions)
        
        compressed_count = 0
        for version in old_versions:
            try:
                # Compress the asset_data JSON
                import gzip
                import json
                
                original_data = json.dumps(version.asset_data)
                compressed_data = gzip.compress(original_data.encode('utf-8'))
                
                # Update version with compressed data
                version.asset_data = compressed_data.hex()  # Store as hex string
                version.compression_type = "gzip"
                
                compressed_count += 1
                self.logger.debug(f"Compressed version {version.id}")
                
            except Exception as e:
                self.logger.error(f"Failed to compress version {version.id}: {e}")
        
        return compressed_count
    
    def _clean_expired_locks(self, dry_run: bool = True) -> int:
        """Clean up expired asset locks."""
        now = datetime.utcnow()
        
        expired_locks = self.db.query(AssetLock).filter(
            and_(
                AssetLock.is_active == True,
                AssetLock.expires_at < now
            )
        ).all()
        
        if dry_run:
            self.logger.info(f"Would clean {len(expired_locks)} expired locks")
            return len(expired_locks)
        
        cleaned_count = 0
        for lock in expired_locks:
            try:
                lock.is_active = False
                lock.released_at = now
                lock.released_by = "system_cleanup"
                cleaned_count += 1
                
            except Exception as e:
                self.logger.error(f"Failed to clean expired lock {lock.id}: {e}")
        
        return cleaned_count
    
    def _remove_expired_backups(self, dry_run: bool = True) -> int:
        """Remove expired asset backups."""
        now = datetime.utcnow()
        
        expired_backups = self.db.query(AssetBackup).filter(
            and_(
                AssetBackup.expires_at.isnot(None),
                AssetBackup.expires_at < now
            )
        ).all()
        
        if dry_run:
            self.logger.info(f"Would remove {len(expired_backups)} expired backups")
            return len(expired_backups)
        
        removed_count = 0
        for backup in expired_backups:
            try:
                # In a real implementation, you would also delete the backup file
                # from storage (S3, filesystem, etc.)
                
                self.db.delete(backup)
                removed_count += 1
                self.logger.debug(f"Removed expired backup {backup.id}")
                
            except Exception as e:
                self.logger.error(f"Failed to remove expired backup {backup.id}: {e}")
        
        return removed_count
    
    def _delete_asset_related_records(self, asset_id) -> None:
        """Delete all records related to an asset."""
        # Delete in order to respect foreign key constraints
        
        # Delete asset validations
        self.db.query(AssetValidation).filter(
            AssetValidation.asset_id == asset_id
        ).delete()
        
        # Delete asset versions
        self.db.query(AssetVersion).filter(
            AssetVersion.asset_id == asset_id
        ).delete()
        
        # Delete asset backups
        self.db.query(AssetBackup).filter(
            AssetBackup.asset_id == asset_id
        ).delete()
        
        # Delete asset locks
        self.db.query(AssetLock).filter(
            AssetLock.asset_id == asset_id
        ).delete()
        
        # Note: Audit logs are kept for compliance even after asset deletion
    
    def _create_asset_snapshot(self, asset: RobustAsset) -> Dict:
        """Create asset snapshot for audit purposes."""
        return {
            "id": str(asset.id),
            "name": asset.name,
            "asset_type": asset.asset_type,
            "provider": asset.provider,
            "status": asset.status,
            "deleted_at": asset.deleted_at.isoformat() if asset.deleted_at else None,
            "retention_policy": asset.retention_policy.value if asset.retention_policy else None,
        }
    
    def get_retention_statistics(self) -> Dict[str, any]:
        """Get statistics about data retention and storage usage."""
        now = datetime.utcnow()
        
        stats = {}
        
        # Asset statistics
        stats["total_assets"] = self.db.query(RobustAsset).count()
        stats["active_assets"] = self.db.query(RobustAsset).filter(
            RobustAsset.is_deleted == False
        ).count()
        stats["soft_deleted_assets"] = self.db.query(RobustAsset).filter(
            RobustAsset.is_deleted == True
        ).count()
        stats["assets_pending_purge"] = self.db.query(RobustAsset).filter(
            and_(
                RobustAsset.is_deleted == True,
                RobustAsset.purge_after.isnot(None),
                RobustAsset.purge_after <= now
            )
        ).count()
        
        # Audit log statistics
        stats["total_audit_logs"] = self.db.query(AssetAuditLog).count()
        stats["audit_logs_last_30_days"] = self.db.query(AssetAuditLog).filter(
            AssetAuditLog.timestamp >= now - timedelta(days=30)
        ).count()
        
        # Version statistics
        stats["total_versions"] = self.db.query(AssetVersion).count()
        stats["compressed_versions"] = self.db.query(AssetVersion).filter(
            AssetVersion.compression_type.isnot(None)
        ).count()
        
        # Lock statistics
        stats["active_locks"] = self.db.query(AssetLock).filter(
            and_(
                AssetLock.is_active == True,
                AssetLock.expires_at > now
            )
        ).count()
        stats["expired_locks"] = self.db.query(AssetLock).filter(
            and_(
                AssetLock.is_active == True,
                AssetLock.expires_at <= now
            )
        ).count()
        
        # Backup statistics
        stats["total_backups"] = self.db.query(AssetBackup).count()
        stats["expired_backups"] = self.db.query(AssetBackup).filter(
            and_(
                AssetBackup.expires_at.isnot(None),
                AssetBackup.expires_at <= now
            )
        ).count()
        
        return stats
    
    def set_retention_policy(self, asset_id, policy: DataRetentionPolicy) -> bool:
        """Set retention policy for a specific asset."""
        try:
            asset = self.db.query(RobustAsset).filter(RobustAsset.id == asset_id).first()
            if not asset:
                return False
            
            asset.retention_policy = policy
            
            # Recalculate purge date if asset is deleted
            if asset.is_deleted and asset.deleted_at:
                if policy == DataRetentionPolicy.IMMEDIATE:
                    asset.purge_after = datetime.utcnow()
                elif policy == DataRetentionPolicy.SHORT_TERM:
                    asset.purge_after = asset.deleted_at + timedelta(days=30)
                elif policy == DataRetentionPolicy.MEDIUM_TERM:
                    asset.purge_after = asset.deleted_at + timedelta(days=90)
                elif policy == DataRetentionPolicy.LONG_TERM:
                    asset.purge_after = asset.deleted_at + timedelta(days=365)
                else:  # PERMANENT or COMPLIANCE
                    asset.purge_after = None
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to set retention policy for asset {asset_id}: {e}")
            return False
    
    def schedule_cleanup_job(self) -> Dict[str, any]:
        """Schedule automated cleanup job (would integrate with task scheduler)."""
        # This would typically integrate with Celery, APScheduler, or similar
        cleanup_config = {
            "enabled": True,
            "schedule": "0 2 * * *",  # Daily at 2 AM
            "dry_run": False,
            "retention_policies": {
                "audit_logs": "2_years",
                "asset_versions": "1_year",
                "expired_locks": "immediate",
                "expired_backups": "immediate"
            },
            "notification_settings": {
                "email_on_completion": True,
                "email_on_error": True,
                "slack_webhook": None
            }
        }
        
        return cleanup_config
