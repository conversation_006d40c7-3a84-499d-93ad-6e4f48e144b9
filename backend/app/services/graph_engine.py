"""
Graph Processing Engine for Attack Path Analysis

This module provides high-performance graph algorithms for analyzing attack paths
and calculating blast radius in security infrastructure.
"""

import logging
import networkx as nx
import numpy as np
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
import time

logger = logging.getLogger(__name__)


class AttackTechnique(str, Enum):
    """MITRE ATT&CK technique categories."""
    INITIAL_ACCESS = "initial_access"
    EXECUTION = "execution"
    PERSISTENCE = "persistence"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DEFENSE_EVASION = "defense_evasion"
    CREDENTIAL_ACCESS = "credential_access"
    DISCOVERY = "discovery"
    LATERAL_MOVEMENT = "lateral_movement"
    COLLECTION = "collection"
    COMMAND_AND_CONTROL = "command_and_control"
    EXFILTRATION = "exfiltration"
    IMPACT = "impact"


class PathType(str, Enum):
    """Types of attack paths."""
    DIRECT = "direct"
    LATERAL_MOVEMENT = "lateral_movement"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_EXFILTRATION = "data_exfiltration"
    SUPPLY_CHAIN = "supply_chain"
    INSIDER_THREAT = "insider_threat"


@dataclass
class AttackPath:
    """Represents a potential attack path through the infrastructure."""
    
    path_id: str
    source_asset_id: str
    target_asset_id: str
    path_nodes: List[str]
    path_edges: List[Tuple[str, str]]
    path_type: PathType
    attack_techniques: List[AttackTechnique]
    risk_score: float
    likelihood: float
    impact_score: float
    blast_radius: int
    estimated_time: int  # minutes
    required_privileges: List[str]
    detection_difficulty: float  # 0.0 to 1.0
    mitigation_cost: float
    path_length: int
    
    @property
    def criticality_score(self) -> float:
        """Calculate overall criticality score."""
        return (self.risk_score * 0.4 + 
                self.likelihood * 0.3 + 
                self.impact_score * 0.2 + 
                (self.blast_radius / 100) * 0.1)


@dataclass
class BlastRadiusResult:
    """Results of blast radius analysis."""
    
    source_asset_id: str
    affected_assets: Set[str]
    impact_by_degree: Dict[int, Set[str]]
    total_impact_score: float
    critical_assets_affected: Set[str]
    data_assets_affected: Set[str]
    service_disruption_score: float
    financial_impact: float
    compliance_impact: List[str]
    recovery_time_estimate: int  # hours


class GraphEngine:
    """High-performance graph processing engine for attack path analysis."""
    
    def __init__(self, max_workers: int = 4):
        self.graph = nx.DiGraph()
        self.asset_metadata = {}
        self.relationship_weights = {}
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Performance metrics
        self.metrics = {
            "total_nodes": 0,
            "total_edges": 0,
            "last_analysis_time": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        # Analysis cache
        self.path_cache = {}
        self.blast_radius_cache = {}
    
    def add_asset(self, asset_id: str, asset_data: Dict[str, Any]) -> None:
        """Add an asset node to the graph."""
        self.graph.add_node(asset_id, **asset_data)
        self.asset_metadata[asset_id] = asset_data
        self.metrics["total_nodes"] = self.graph.number_of_nodes()
        
        # Clear relevant caches
        self._invalidate_cache(asset_id)
        
        self.logger.debug(f"Added asset {asset_id} to graph")
    
    def add_relationship(self, source_id: str, target_id: str, 
                        relationship_data: Dict[str, Any]) -> None:
        """Add a relationship edge to the graph."""
        # Calculate edge weight based on relationship properties
        weight = self._calculate_edge_weight(relationship_data)
        
        self.graph.add_edge(source_id, target_id, weight=weight, **relationship_data)
        self.relationship_weights[(source_id, target_id)] = weight
        self.metrics["total_edges"] = self.graph.number_of_edges()
        
        # Clear relevant caches
        self._invalidate_cache(source_id)
        self._invalidate_cache(target_id)
        
        self.logger.debug(f"Added relationship {source_id} -> {target_id} with weight {weight}")
    
    def _calculate_edge_weight(self, relationship_data: Dict[str, Any]) -> float:
        """Calculate edge weight based on relationship properties."""
        base_weight = 1.0
        
        # Adjust weight based on relationship type
        rel_type = relationship_data.get("relationship_type", "")
        type_weights = {
            "depends_on": 0.8,
            "communicates_with": 0.6,
            "contains": 0.9,
            "manages": 0.7,
            "accesses": 0.5,
            "trusts": 0.4,
            "shares_credentials": 0.3,
            "same_network": 0.6,
            "same_subnet": 0.4,
            "administrative_access": 0.2
        }
        
        weight = base_weight * type_weights.get(rel_type, 1.0)
        
        # Adjust for security controls
        if relationship_data.get("encrypted", False):
            weight *= 1.5
        if relationship_data.get("authenticated", False):
            weight *= 1.3
        if relationship_data.get("monitored", False):
            weight *= 1.2
        
        # Adjust for network exposure
        if relationship_data.get("public_facing", False):
            weight *= 0.5
        
        return max(0.1, min(2.0, weight))  # Clamp between 0.1 and 2.0
    
    def _invalidate_cache(self, asset_id: str) -> None:
        """Invalidate caches related to an asset."""
        # Remove cached paths involving this asset
        keys_to_remove = [
            key for key in self.path_cache.keys() 
            if asset_id in key
        ]
        for key in keys_to_remove:
            del self.path_cache[key]
        
        # Remove cached blast radius results
        if asset_id in self.blast_radius_cache:
            del self.blast_radius_cache[asset_id]
    
    async def find_attack_paths(self, source_asset_id: str, target_asset_id: str,
                               max_path_length: int = 5, 
                               max_paths: int = 10) -> List[AttackPath]:
        """Find potential attack paths between two assets."""
        start_time = time.time()
        
        # Check cache first
        cache_key = (source_asset_id, target_asset_id, max_path_length, max_paths)
        if cache_key in self.path_cache:
            self.metrics["cache_hits"] += 1
            return self.path_cache[cache_key]
        
        self.metrics["cache_misses"] += 1
        
        try:
            # Find all simple paths up to max_path_length
            paths = list(nx.all_simple_paths(
                self.graph, 
                source_asset_id, 
                target_asset_id, 
                cutoff=max_path_length
            ))
            
            # Limit number of paths for performance
            if len(paths) > max_paths:
                # Sort by path length and take shortest paths
                paths = sorted(paths, key=len)[:max_paths]
            
            # Convert to AttackPath objects
            attack_paths = []
            for i, path in enumerate(paths):
                attack_path = await self._create_attack_path(
                    f"path_{i}_{source_asset_id}_{target_asset_id}",
                    path
                )
                attack_paths.append(attack_path)
            
            # Sort by criticality score
            attack_paths.sort(key=lambda x: x.criticality_score, reverse=True)
            
            # Cache results
            self.path_cache[cache_key] = attack_paths
            
            analysis_time = time.time() - start_time
            self.metrics["last_analysis_time"] = analysis_time
            
            self.logger.info(f"Found {len(attack_paths)} attack paths from {source_asset_id} to {target_asset_id} in {analysis_time:.2f}s")
            
            return attack_paths
            
        except nx.NetworkXNoPath:
            self.logger.info(f"No path found from {source_asset_id} to {target_asset_id}")
            return []
        except Exception as e:
            self.logger.error(f"Error finding attack paths: {e}")
            return []
    
    async def _create_attack_path(self, path_id: str, path_nodes: List[str]) -> AttackPath:
        """Create an AttackPath object from a list of nodes."""
        # Create edges from consecutive nodes
        path_edges = [(path_nodes[i], path_nodes[i+1]) for i in range(len(path_nodes)-1)]
        
        # Analyze path characteristics
        path_type = self._determine_path_type(path_nodes, path_edges)
        attack_techniques = self._identify_attack_techniques(path_nodes, path_edges)
        risk_score = self._calculate_path_risk_score(path_nodes, path_edges)
        likelihood = self._calculate_path_likelihood(path_nodes, path_edges)
        impact_score = self._calculate_path_impact(path_nodes, path_edges)
        blast_radius = await self._calculate_path_blast_radius(path_nodes[-1])
        estimated_time = self._estimate_attack_time(path_nodes, path_edges)
        required_privileges = self._identify_required_privileges(path_nodes, path_edges)
        detection_difficulty = self._calculate_detection_difficulty(path_nodes, path_edges)
        mitigation_cost = self._estimate_mitigation_cost(path_nodes, path_edges)
        
        return AttackPath(
            path_id=path_id,
            source_asset_id=path_nodes[0],
            target_asset_id=path_nodes[-1],
            path_nodes=path_nodes,
            path_edges=path_edges,
            path_type=path_type,
            attack_techniques=attack_techniques,
            risk_score=risk_score,
            likelihood=likelihood,
            impact_score=impact_score,
            blast_radius=blast_radius,
            estimated_time=estimated_time,
            required_privileges=required_privileges,
            detection_difficulty=detection_difficulty,
            mitigation_cost=mitigation_cost,
            path_length=len(path_nodes)
        )
    
    def _determine_path_type(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> PathType:
        """Determine the type of attack path based on assets and relationships."""
        # Analyze asset types and relationships to determine path type
        asset_types = [self.asset_metadata.get(node, {}).get("asset_type", "") for node in path_nodes]
        
        # Check for lateral movement patterns
        if any("server" in asset_type or "workstation" in asset_type for asset_type in asset_types):
            if len(set(asset_types)) > 1:
                return PathType.LATERAL_MOVEMENT
        
        # Check for privilege escalation patterns
        environments = [self.asset_metadata.get(node, {}).get("environment", "") for node in path_nodes]
        if "development" in environments and "production" in environments:
            return PathType.PRIVILEGE_ESCALATION
        
        # Check for data exfiltration patterns
        if any("database" in asset_type or "storage" in asset_type for asset_type in asset_types):
            return PathType.DATA_EXFILTRATION
        
        # Default to direct attack
        return PathType.DIRECT
    
    def _identify_attack_techniques(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> List[AttackTechnique]:
        """Identify MITRE ATT&CK techniques used in the attack path."""
        techniques = []
        
        # Analyze each edge for attack techniques
        for source, target in path_edges:
            edge_data = self.graph.get_edge_data(source, target, {})
            rel_type = edge_data.get("relationship_type", "")
            
            # Map relationship types to attack techniques
            if rel_type == "communicates_with":
                techniques.append(AttackTechnique.LATERAL_MOVEMENT)
            elif rel_type == "depends_on":
                techniques.append(AttackTechnique.PERSISTENCE)
            elif rel_type == "manages":
                techniques.append(AttackTechnique.PRIVILEGE_ESCALATION)
            elif rel_type == "accesses":
                techniques.append(AttackTechnique.CREDENTIAL_ACCESS)
            elif rel_type == "trusts":
                techniques.append(AttackTechnique.DEFENSE_EVASION)
        
        # Add initial access for first node
        if path_nodes:
            first_asset = self.asset_metadata.get(path_nodes[0], {})
            if first_asset.get("public_facing", False):
                techniques.insert(0, AttackTechnique.INITIAL_ACCESS)
        
        # Add impact for last node
        if path_nodes:
            last_asset = self.asset_metadata.get(path_nodes[-1], {})
            if last_asset.get("business_criticality") == "critical":
                techniques.append(AttackTechnique.IMPACT)
        
        return list(set(techniques))  # Remove duplicates
    
    def _calculate_path_risk_score(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> float:
        """Calculate overall risk score for the attack path."""
        if not path_nodes:
            return 0.0
        
        # Base risk from individual assets
        asset_risks = []
        for node in path_nodes:
            asset_data = self.asset_metadata.get(node, {})
            risk_score = asset_data.get("risk_score", 50)
            asset_risks.append(risk_score)
        
        # Calculate weighted average (later nodes have higher weight)
        weights = [i + 1 for i in range(len(asset_risks))]
        weighted_risk = sum(risk * weight for risk, weight in zip(asset_risks, weights)) / sum(weights)
        
        # Adjust for path length (longer paths are riskier)
        length_multiplier = 1.0 + (len(path_nodes) - 1) * 0.1
        
        # Adjust for edge security
        security_factor = 1.0
        for source, target in path_edges:
            edge_data = self.graph.get_edge_data(source, target, {})
            if edge_data.get("encrypted", False):
                security_factor *= 0.9
            if edge_data.get("authenticated", False):
                security_factor *= 0.9
            if edge_data.get("monitored", False):
                security_factor *= 0.8
        
        final_risk = min(100.0, weighted_risk * length_multiplier * security_factor)
        return final_risk
    
    def _calculate_path_likelihood(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> float:
        """Calculate likelihood of successful attack along this path."""
        if not path_edges:
            return 0.0
        
        # Start with base likelihood
        likelihood = 0.8
        
        # Reduce likelihood for each hop
        for source, target in path_edges:
            edge_data = self.graph.get_edge_data(source, target, {})
            hop_difficulty = 1.0
            
            # Security controls reduce likelihood
            if edge_data.get("encrypted", False):
                hop_difficulty *= 0.7
            if edge_data.get("authenticated", False):
                hop_difficulty *= 0.6
            if edge_data.get("monitored", False):
                hop_difficulty *= 0.5
            if edge_data.get("firewall_protected", False):
                hop_difficulty *= 0.8
            
            likelihood *= hop_difficulty
        
        return max(0.01, min(1.0, likelihood))
    
    def _calculate_path_impact(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> float:
        """Calculate potential impact of successful attack."""
        if not path_nodes:
            return 0.0
        
        # Impact is primarily determined by target asset
        target_asset = self.asset_metadata.get(path_nodes[-1], {})
        
        base_impact = 50.0
        
        # Business criticality
        criticality = target_asset.get("business_criticality", "medium")
        criticality_multipliers = {
            "low": 0.5,
            "medium": 1.0,
            "high": 1.5,
            "critical": 2.0
        }
        base_impact *= criticality_multipliers.get(criticality, 1.0)
        
        # Data classification
        data_class = target_asset.get("data_classification", "internal")
        data_multipliers = {
            "public": 0.3,
            "internal": 1.0,
            "confidential": 1.5,
            "restricted": 2.0
        }
        base_impact *= data_multipliers.get(data_class, 1.0)
        
        # PII and compliance
        if target_asset.get("contains_pii", False):
            base_impact *= 1.3
        if target_asset.get("gdpr_applicable", False):
            base_impact *= 1.2
        
        return min(100.0, base_impact)
    
    async def _calculate_path_blast_radius(self, target_asset_id: str) -> int:
        """Calculate blast radius from target asset."""
        blast_result = await self.calculate_blast_radius(target_asset_id, max_degrees=3)
        return len(blast_result.affected_assets)
    
    def _estimate_attack_time(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> int:
        """Estimate time required to execute attack path (in minutes)."""
        base_time = 30  # Base time for initial access
        
        # Add time for each hop
        for source, target in path_edges:
            edge_data = self.graph.get_edge_data(source, target, {})
            hop_time = 15  # Base time per hop
            
            # Security controls increase time
            if edge_data.get("encrypted", False):
                hop_time *= 2
            if edge_data.get("authenticated", False):
                hop_time *= 1.5
            if edge_data.get("monitored", False):
                hop_time *= 1.3
            
            base_time += hop_time
        
        return int(base_time)
    
    def _identify_required_privileges(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> List[str]:
        """Identify privileges required for attack path."""
        privileges = set()
        
        for source, target in path_edges:
            edge_data = self.graph.get_edge_data(source, target, {})
            rel_type = edge_data.get("relationship_type", "")
            
            if rel_type == "manages":
                privileges.add("administrative")
            elif rel_type == "accesses":
                privileges.add("user")
            elif rel_type == "trusts":
                privileges.add("trusted_user")
            else:
                privileges.add("network_access")
        
        return list(privileges)
    
    def _calculate_detection_difficulty(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> float:
        """Calculate how difficult this attack would be to detect."""
        base_difficulty = 0.5
        
        # Monitored connections are easier to detect
        monitored_edges = sum(1 for source, target in path_edges 
                            if self.graph.get_edge_data(source, target, {}).get("monitored", False))
        
        if monitored_edges > 0:
            base_difficulty *= (1.0 - (monitored_edges / len(path_edges)) * 0.5)
        
        return max(0.1, min(1.0, base_difficulty))
    
    def _estimate_mitigation_cost(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> float:
        """Estimate cost to mitigate this attack path."""
        base_cost = 1000.0  # Base cost in USD
        
        # Cost increases with path length
        base_cost *= len(path_edges)
        
        # Cost varies by asset types
        for node in path_nodes:
            asset_data = self.asset_metadata.get(node, {})
            asset_type = asset_data.get("asset_type", "")
            
            if "database" in asset_type:
                base_cost *= 1.5
            elif "server" in asset_type:
                base_cost *= 1.2
            elif "network" in asset_type:
                base_cost *= 1.3
        
        return base_cost

    async def calculate_blast_radius(self, source_asset_id: str,
                                   max_degrees: int = 5) -> BlastRadiusResult:
        """Calculate blast radius from a compromised asset."""
        start_time = time.time()

        # Check cache first
        cache_key = (source_asset_id, max_degrees)
        if cache_key in self.blast_radius_cache:
            self.metrics["cache_hits"] += 1
            return self.blast_radius_cache[cache_key]

        self.metrics["cache_misses"] += 1

        try:
            # Find all nodes within max_degrees
            affected_assets = set()
            impact_by_degree = {}

            # BFS to find nodes by degree
            current_level = {source_asset_id}
            visited = {source_asset_id}

            for degree in range(max_degrees + 1):
                if not current_level:
                    break

                impact_by_degree[degree] = current_level.copy()
                affected_assets.update(current_level)

                # Find next level
                next_level = set()
                for node in current_level:
                    # Get all neighbors (both incoming and outgoing)
                    neighbors = set(self.graph.successors(node)) | set(self.graph.predecessors(node))
                    for neighbor in neighbors:
                        if neighbor not in visited:
                            next_level.add(neighbor)
                            visited.add(neighbor)

                current_level = next_level

            # Analyze impact
            total_impact_score = self._calculate_total_impact_score(affected_assets)
            critical_assets_affected = self._identify_critical_assets(affected_assets)
            data_assets_affected = self._identify_data_assets(affected_assets)
            service_disruption_score = self._calculate_service_disruption(affected_assets)
            financial_impact = self._estimate_financial_impact(affected_assets)
            compliance_impact = self._assess_compliance_impact(affected_assets)
            recovery_time_estimate = self._estimate_recovery_time(affected_assets)

            result = BlastRadiusResult(
                source_asset_id=source_asset_id,
                affected_assets=affected_assets,
                impact_by_degree=impact_by_degree,
                total_impact_score=total_impact_score,
                critical_assets_affected=critical_assets_affected,
                data_assets_affected=data_assets_affected,
                service_disruption_score=service_disruption_score,
                financial_impact=financial_impact,
                compliance_impact=compliance_impact,
                recovery_time_estimate=recovery_time_estimate
            )

            # Cache result
            self.blast_radius_cache[cache_key] = result

            analysis_time = time.time() - start_time
            self.logger.info(f"Calculated blast radius for {source_asset_id}: {len(affected_assets)} assets affected in {analysis_time:.2f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error calculating blast radius: {e}")
            return BlastRadiusResult(
                source_asset_id=source_asset_id,
                affected_assets=set(),
                impact_by_degree={},
                total_impact_score=0.0,
                critical_assets_affected=set(),
                data_assets_affected=set(),
                service_disruption_score=0.0,
                financial_impact=0.0,
                compliance_impact=[],
                recovery_time_estimate=0
            )

    def _calculate_total_impact_score(self, affected_assets: Set[str]) -> float:
        """Calculate total impact score for affected assets."""
        total_score = 0.0

        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})

            # Base impact from risk score
            risk_score = asset_data.get("risk_score", 50)

            # Multiply by business criticality
            criticality = asset_data.get("business_criticality", "medium")
            criticality_multipliers = {
                "low": 0.5,
                "medium": 1.0,
                "high": 1.5,
                "critical": 2.0
            }

            asset_impact = risk_score * criticality_multipliers.get(criticality, 1.0)
            total_score += asset_impact

        return total_score

    def _identify_critical_assets(self, affected_assets: Set[str]) -> Set[str]:
        """Identify critical assets in the affected set."""
        critical_assets = set()

        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})
            if asset_data.get("business_criticality") == "critical":
                critical_assets.add(asset_id)

        return critical_assets

    def _identify_data_assets(self, affected_assets: Set[str]) -> Set[str]:
        """Identify data-containing assets in the affected set."""
        data_assets = set()

        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})
            asset_type = asset_data.get("asset_type", "")

            if any(data_type in asset_type.lower() for data_type in
                   ["database", "storage", "backup", "archive"]):
                data_assets.add(asset_id)

            if asset_data.get("contains_pii", False):
                data_assets.add(asset_id)

        return data_assets

    def _calculate_service_disruption(self, affected_assets: Set[str]) -> float:
        """Calculate service disruption score."""
        disruption_score = 0.0

        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})
            asset_type = asset_data.get("asset_type", "")

            # Service assets have higher disruption impact
            if any(service_type in asset_type.lower() for service_type in
                   ["server", "service", "application", "api"]):
                disruption_score += 10.0

            # Critical assets cause more disruption
            if asset_data.get("business_criticality") == "critical":
                disruption_score += 20.0

        return min(100.0, disruption_score)

    def _estimate_financial_impact(self, affected_assets: Set[str]) -> float:
        """Estimate financial impact in USD."""
        financial_impact = 0.0

        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})

            # Base cost per asset
            base_cost = 1000.0

            # Adjust by business criticality
            criticality = asset_data.get("business_criticality", "medium")
            criticality_multipliers = {
                "low": 0.5,
                "medium": 1.0,
                "high": 2.0,
                "critical": 5.0
            }

            asset_cost = base_cost * criticality_multipliers.get(criticality, 1.0)

            # Additional costs for data assets
            if asset_data.get("contains_pii", False):
                asset_cost *= 2.0
            if asset_data.get("gdpr_applicable", False):
                asset_cost *= 1.5

            financial_impact += asset_cost

        return financial_impact

    def _assess_compliance_impact(self, affected_assets: Set[str]) -> List[str]:
        """Assess compliance frameworks impacted."""
        compliance_frameworks = set()

        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})

            if asset_data.get("contains_pii", False):
                compliance_frameworks.add("GDPR")
                compliance_frameworks.add("CCPA")

            if asset_data.get("data_classification") == "restricted":
                compliance_frameworks.add("SOX")
                compliance_frameworks.add("HIPAA")

            # Check asset-specific compliance requirements
            asset_compliance = asset_data.get("compliance_frameworks", [])
            compliance_frameworks.update(asset_compliance)

        return list(compliance_frameworks)

    def _estimate_recovery_time(self, affected_assets: Set[str]) -> int:
        """Estimate recovery time in hours."""
        base_time = 4  # Base recovery time

        # Add time per affected asset
        recovery_time = base_time + len(affected_assets) * 0.5

        # Critical assets take longer to recover
        for asset_id in affected_assets:
            asset_data = self.asset_metadata.get(asset_id, {})
            if asset_data.get("business_criticality") == "critical":
                recovery_time += 8

        return int(recovery_time)

    async def find_shortest_attack_paths(self, source_asset_id: str,
                                       target_assets: List[str],
                                       max_path_length: int = 5) -> Dict[str, AttackPath]:
        """Find shortest attack paths to multiple target assets."""
        results = {}

        # Use asyncio to find paths concurrently
        tasks = []
        for target_id in target_assets:
            task = self.find_attack_paths(source_asset_id, target_id, max_path_length, 1)
            tasks.append((target_id, task))

        # Wait for all tasks to complete
        for target_id, task in tasks:
            paths = await task
            if paths:
                results[target_id] = paths[0]  # Take shortest path

        return results

    def get_graph_statistics(self) -> Dict[str, Any]:
        """Get comprehensive graph statistics."""
        stats = {
            "nodes": self.graph.number_of_nodes(),
            "edges": self.graph.number_of_edges(),
            "density": nx.density(self.graph),
            "is_connected": nx.is_weakly_connected(self.graph),
            "number_of_components": nx.number_weakly_connected_components(self.graph),
            "average_clustering": nx.average_clustering(self.graph.to_undirected()),
            "performance_metrics": self.metrics.copy()
        }

        # Add centrality measures for top nodes
        if self.graph.number_of_nodes() > 0:
            try:
                # Calculate centrality measures (limit to top 10 for performance)
                degree_centrality = nx.degree_centrality(self.graph)
                betweenness_centrality = nx.betweenness_centrality(self.graph, k=min(100, self.graph.number_of_nodes()))

                stats["top_degree_centrality"] = sorted(
                    degree_centrality.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10]

                stats["top_betweenness_centrality"] = sorted(
                    betweenness_centrality.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10]

            except Exception as e:
                self.logger.warning(f"Error calculating centrality measures: {e}")

        return stats

    def export_graph(self, format: str = "gexf") -> str:
        """Export graph in various formats."""
        if format.lower() == "gexf":
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.gexf', delete=False) as f:
                nx.write_gexf(self.graph, f.name)
                return f.name
        elif format.lower() == "graphml":
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.graphml', delete=False) as f:
                nx.write_graphml(self.graph, f.name)
                return f.name
        else:
            raise ValueError(f"Unsupported export format: {format}")

    def clear_cache(self) -> None:
        """Clear all caches."""
        self.path_cache.clear()
        self.blast_radius_cache.clear()
        self.logger.info("Cleared all caches")

    def get_asset_neighbors(self, asset_id: str, direction: str = "both") -> List[str]:
        """Get neighboring assets."""
        if direction == "out":
            return list(self.graph.successors(asset_id))
        elif direction == "in":
            return list(self.graph.predecessors(asset_id))
        else:  # both
            return list(set(self.graph.successors(asset_id)) | set(self.graph.predecessors(asset_id)))
