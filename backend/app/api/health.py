"""
Blast-Radius Security Tool - Comprehensive Health Check API
Implements detailed health monitoring for all system components
"""

import asyncio
import time
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.database import get_db
from app.cache import get_redis
from app.core.resilience import (
    check_database_health,
    check_redis_health,
    check_external_api_health
)
from app.core.performance import monitor
from app.core.data_integrity import IntegrityMonitor

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/health", tags=["health"])


class HealthStatus(Enum):
    """Health check status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ComponentHealth:
    """Individual component health information"""
    
    def __init__(
        self,
        name: str,
        status: HealthStatus = HealthStatus.UNKNOWN,
        response_time: Optional[float] = None,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ):
        self.name = name
        self.status = status
        self.response_time = response_time
        self.details = details or {}
        self.error = error
        self.checked_at = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "name": self.name,
            "status": self.status.value,
            "response_time_ms": round(self.response_time * 1000, 2) if self.response_time else None,
            "details": self.details,
            "error": self.error,
            "checked_at": self.checked_at.isoformat()
        }


class SystemHealthChecker:
    """
    Comprehensive system health checker
    """
    
    def __init__(self):
        self.component_checkers = {
            "database": self._check_database_health,
            "redis": self._check_redis_health,
            "neo4j": self._check_neo4j_health,
            "external_apis": self._check_external_apis_health,
            "file_system": self._check_file_system_health,
            "memory": self._check_memory_health,
            "data_integrity": self._check_data_integrity_health
        }
        self.last_full_check: Optional[datetime] = None
        self.cached_results: Dict[str, ComponentHealth] = {}
    
    async def check_all_components(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Check health of all system components
        """
        start_time = time.time()
        
        # Use cached results if recent and not forcing refresh
        if (not force_refresh and 
            self.last_full_check and 
            (datetime.now(timezone.utc) - self.last_full_check).seconds < 30):
            return self._compile_health_report(use_cache=True)
        
        # Run all health checks concurrently
        health_tasks = {
            component: self._run_component_check(component, checker)
            for component, checker in self.component_checkers.items()
        }
        
        # Wait for all checks to complete
        component_results = await asyncio.gather(
            *health_tasks.values(),
            return_exceptions=True
        )
        
        # Process results
        for i, (component, result) in enumerate(zip(health_tasks.keys(), component_results)):
            if isinstance(result, Exception):
                self.cached_results[component] = ComponentHealth(
                    name=component,
                    status=HealthStatus.UNHEALTHY,
                    error=str(result)
                )
            else:
                self.cached_results[component] = result
        
        self.last_full_check = datetime.now(timezone.utc)
        total_time = time.time() - start_time
        
        return self._compile_health_report(total_check_time=total_time)
    
    async def _run_component_check(self, component: str, checker: callable) -> ComponentHealth:
        """Run individual component health check with timing"""
        start_time = time.time()
        
        try:
            result = await checker()
            result.response_time = time.time() - start_time
            return result
        except Exception as e:
            return ComponentHealth(
                name=component,
                status=HealthStatus.UNHEALTHY,
                response_time=time.time() - start_time,
                error=str(e)
            )
    
    async def _check_database_health(self) -> ComponentHealth:
        """Check PostgreSQL database health"""
        try:
            from app.database import get_db
            
            async with get_db() as db:
                # Test basic connectivity
                result = await db.execute(text("SELECT 1"))
                
                # Check connection pool status
                pool_status = {}
                if hasattr(db.bind, 'pool'):
                    pool = db.bind.pool
                    pool_status = {
                        "pool_size": pool.size(),
                        "checked_out": pool.checkedout(),
                        "overflow": pool.overflow(),
                        "checked_in": pool.checkedin()
                    }
                
                # Check database size and performance
                db_stats = await db.execute(text("""
                    SELECT 
                        pg_database_size(current_database()) as db_size,
                        (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                        (SELECT count(*) FROM pg_stat_activity) as total_connections
                """))
                stats = db_stats.fetchone()
                
                return ComponentHealth(
                    name="database",
                    status=HealthStatus.HEALTHY,
                    details={
                        "type": "postgresql",
                        "database_size_bytes": stats[0] if stats else 0,
                        "active_connections": stats[1] if stats else 0,
                        "total_connections": stats[2] if stats else 0,
                        "pool_status": pool_status
                    }
                )
                
        except Exception as e:
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )
    
    async def _check_redis_health(self) -> ComponentHealth:
        """Check Redis cache health"""
        try:
            redis = await get_redis()
            
            # Test basic connectivity
            await redis.ping()
            
            # Get Redis info
            info = await redis.info()
            memory_info = await redis.info("memory")
            
            # Calculate memory usage percentage
            used_memory = memory_info.get("used_memory", 0)
            max_memory = memory_info.get("maxmemory", 0)
            memory_usage_pct = (used_memory / max_memory * 100) if max_memory > 0 else 0
            
            status = HealthStatus.HEALTHY
            if memory_usage_pct > 90:
                status = HealthStatus.DEGRADED
            elif memory_usage_pct > 95:
                status = HealthStatus.UNHEALTHY
            
            return ComponentHealth(
                name="redis",
                status=status,
                details={
                    "version": info.get("redis_version"),
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": memory_info.get("used_memory_human"),
                    "memory_usage_percent": round(memory_usage_pct, 2),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0)
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="redis",
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )
    
    async def _check_neo4j_health(self) -> ComponentHealth:
        """Check Neo4j graph database health"""
        try:
            from app.graph import get_neo4j_driver
            
            driver = await get_neo4j_driver()
            
            # Test connectivity and get basic stats
            async with driver.session() as session:
                # Test basic query
                result = await session.run("RETURN 1 as test")
                await result.single()
                
                # Get database stats
                stats_result = await session.run("""
                    CALL dbms.queryJmx("org.neo4j:instance=kernel#0,name=Store file sizes")
                    YIELD attributes
                    RETURN attributes.TotalStoreSize.value as total_size
                """)
                
                stats = await stats_result.single()
                total_size = stats["total_size"] if stats else 0
                
                return ComponentHealth(
                    name="neo4j",
                    status=HealthStatus.HEALTHY,
                    details={
                        "type": "neo4j",
                        "total_store_size_bytes": total_size,
                        "connection_status": "active"
                    }
                )
                
        except Exception as e:
            return ComponentHealth(
                name="neo4j",
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )
    
    async def _check_external_apis_health(self) -> ComponentHealth:
        """Check external API dependencies health"""
        external_apis = [
            {"name": "mitre_attack", "url": "https://attack.mitre.org/api/"},
            {"name": "nvd", "url": "https://services.nvd.nist.gov/rest/json/cves/2.0"},
        ]
        
        api_results = {}
        overall_status = HealthStatus.HEALTHY
        
        for api in external_apis:
            try:
                is_healthy = await check_external_api_health(api["url"])
                api_results[api["name"]] = {
                    "status": "healthy" if is_healthy else "unhealthy",
                    "url": api["url"]
                }
                
                if not is_healthy:
                    overall_status = HealthStatus.DEGRADED
                    
            except Exception as e:
                api_results[api["name"]] = {
                    "status": "unhealthy",
                    "url": api["url"],
                    "error": str(e)
                }
                overall_status = HealthStatus.DEGRADED
        
        return ComponentHealth(
            name="external_apis",
            status=overall_status,
            details={"apis": api_results}
        )
    
    async def _check_file_system_health(self) -> ComponentHealth:
        """Check file system health"""
        import shutil
        import os
        
        try:
            # Check disk space
            disk_usage = shutil.disk_usage("/")
            free_space_pct = (disk_usage.free / disk_usage.total) * 100
            
            # Check log directory
            log_dir = "/app/logs"
            log_dir_exists = os.path.exists(log_dir)
            log_dir_writable = os.access(log_dir, os.W_OK) if log_dir_exists else False
            
            status = HealthStatus.HEALTHY
            if free_space_pct < 10:
                status = HealthStatus.UNHEALTHY
            elif free_space_pct < 20:
                status = HealthStatus.DEGRADED
            
            return ComponentHealth(
                name="file_system",
                status=status,
                details={
                    "disk_total_gb": round(disk_usage.total / (1024**3), 2),
                    "disk_free_gb": round(disk_usage.free / (1024**3), 2),
                    "disk_free_percent": round(free_space_pct, 2),
                    "log_directory_exists": log_dir_exists,
                    "log_directory_writable": log_dir_writable
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="file_system",
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )
    
    async def _check_memory_health(self) -> ComponentHealth:
        """Check memory usage health"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            memory_usage_pct = memory.percent
            
            status = HealthStatus.HEALTHY
            if memory_usage_pct > 90:
                status = HealthStatus.UNHEALTHY
            elif memory_usage_pct > 80:
                status = HealthStatus.DEGRADED
            
            return ComponentHealth(
                name="memory",
                status=status,
                details={
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": round(memory_usage_pct, 2),
                    "swap_percent": round(psutil.swap_memory().percent, 2)
                }
            )
            
        except ImportError:
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNKNOWN,
                details={"note": "psutil not available for memory monitoring"}
            )
        except Exception as e:
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )
    
    async def _check_data_integrity_health(self) -> ComponentHealth:
        """Check data integrity health"""
        try:
            from app.database import get_db
            
            async with get_db() as db:
                integrity_monitor = IntegrityMonitor(db)
                
                # Register basic integrity checks
                integrity_monitor.register_check(
                    "referential_integrity",
                    integrity_monitor.check_referential_integrity,
                    integrity_monitor.IntegrityCheckType.REFERENTIAL
                )
                
                # Run integrity checks
                integrity_results = await integrity_monitor.run_integrity_checks()
                
                status = HealthStatus.HEALTHY
                if integrity_results["failed_checks"] > 0:
                    if integrity_results["failed_checks"] > integrity_results["passed_checks"]:
                        status = HealthStatus.UNHEALTHY
                    else:
                        status = HealthStatus.DEGRADED
                
                return ComponentHealth(
                    name="data_integrity",
                    status=status,
                    details={
                        "total_checks": integrity_results["total_checks"],
                        "passed_checks": integrity_results["passed_checks"],
                        "failed_checks": integrity_results["failed_checks"],
                        "violation_count": len(integrity_results["violations"])
                    }
                )
                
        except Exception as e:
            return ComponentHealth(
                name="data_integrity",
                status=HealthStatus.UNHEALTHY,
                error=str(e)
            )
    
    def _compile_health_report(
        self,
        use_cache: bool = False,
        total_check_time: Optional[float] = None
    ) -> Dict[str, Any]:
        """Compile comprehensive health report"""
        
        # Calculate overall system health
        component_statuses = [comp.status for comp in self.cached_results.values()]
        
        if all(status == HealthStatus.HEALTHY for status in component_statuses):
            overall_status = HealthStatus.HEALTHY
        elif any(status == HealthStatus.UNHEALTHY for status in component_statuses):
            overall_status = HealthStatus.UNHEALTHY
        else:
            overall_status = HealthStatus.DEGRADED
        
        # Get performance metrics
        performance_summary = monitor.get_performance_summary()
        
        return {
            "status": overall_status.value,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "check_duration_ms": round(total_check_time * 1000, 2) if total_check_time else None,
            "cached_result": use_cache,
            "components": {
                name: component.to_dict()
                for name, component in self.cached_results.items()
            },
            "performance": performance_summary,
            "summary": {
                "total_components": len(self.cached_results),
                "healthy_components": sum(
                    1 for comp in self.cached_results.values()
                    if comp.status == HealthStatus.HEALTHY
                ),
                "degraded_components": sum(
                    1 for comp in self.cached_results.values()
                    if comp.status == HealthStatus.DEGRADED
                ),
                "unhealthy_components": sum(
                    1 for comp in self.cached_results.values()
                    if comp.status == HealthStatus.UNHEALTHY
                )
            }
        }


# Global health checker instance
health_checker = SystemHealthChecker()


@router.get("/")
async def basic_health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "blast-radius-security-tool"
    }


@router.get("/detailed")
async def detailed_health_check(force_refresh: bool = False):
    """Detailed health check of all system components"""
    try:
        health_report = await health_checker.check_all_components(force_refresh=force_refresh)
        
        # Set appropriate HTTP status code based on health
        if health_report["status"] == "unhealthy":
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        elif health_report["status"] == "degraded":
            status_code = status.HTTP_200_OK  # Still operational
        else:
            status_code = status.HTTP_200_OK
        
        return health_report
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )


@router.get("/ready")
async def readiness_check():
    """Kubernetes readiness probe endpoint"""
    try:
        # Quick check of critical components
        db_healthy = await check_database_health()
        redis_healthy = await check_redis_health()
        
        if db_healthy and redis_healthy:
            return {"status": "ready", "timestamp": datetime.now(timezone.utc).isoformat()}
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service not ready"
            )
            
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready"
        )


@router.get("/live")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": time.time() - monitor.start_time
    }
