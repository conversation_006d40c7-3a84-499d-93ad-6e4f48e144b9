"""Asset management API endpoints."""

import logging
import math
from typing import Any, Dict, List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.db.models.user import User
from app.db.session import get_db
from app.dependencies import get_current_active_user
from app.schemas.asset import (
    AssetCreate,
    AssetUpdate,
    AssetResponse,
    AssetListResponse,
    AssetSearchRequest,
    AssetRelationshipCreate,
    AssetRelationshipUpdate,
    AssetRelationshipResponse,
    DiscoveryJobCreate,
    DiscoveryJobUpdate,
    DiscoveryJobResponse,
    DiscoveryJobListResponse,
    AssetTagCreate,
    AssetTagUpdate,
    AssetTagResponse,
    AssetStatistics,
    AssetHealthCheck,
)
from app.services.asset_service import (
    AssetService,
    AssetNotFoundError,
    AssetAlreadyExistsError,
    RelationshipNotFoundError,
    DiscoveryJobNotFoundError,
)

logger = logging.getLogger(__name__)

router = APIRouter()


# Asset endpoints
@router.post("/", response_model=AssetResponse, status_code=status.HTTP_201_CREATED)
async def create_asset(
    asset_data: AssetCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Create a new asset."""
    try:
        asset_service = AssetService(db)
        asset = asset_service.create_asset(asset_data)
        return asset
    except AssetAlreadyExistsError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating asset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create asset"
        )


@router.get("/{asset_id}", response_model=AssetResponse)
async def get_asset(
    asset_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Get an asset by ID."""
    try:
        asset_service = AssetService(db)
        asset = asset_service.get_asset(asset_id)
        return asset
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


@router.put("/{asset_id}", response_model=AssetResponse)
async def update_asset(
    asset_id: uuid.UUID,
    asset_data: AssetUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Update an asset."""
    try:
        asset_service = AssetService(db)
        asset = asset_service.update_asset(asset_id, asset_data)
        return asset
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    except Exception as e:
        logger.error(f"Error updating asset {asset_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update asset"
        )


@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(
    asset_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> None:
    """Delete an asset."""
    try:
        asset_service = AssetService(db)
        asset_service.delete_asset(asset_id)
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    except Exception as e:
        logger.error(f"Error deleting asset {asset_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete asset"
        )


@router.post("/search", response_model=AssetListResponse)
async def search_assets(
    search_request: AssetSearchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Search assets with filters and pagination."""
    try:
        asset_service = AssetService(db)
        assets, total = asset_service.search_assets(search_request)
        
        pages = math.ceil(total / search_request.size) if total > 0 else 0
        
        return AssetListResponse(
            assets=assets,
            total=total,
            page=search_request.page,
            size=search_request.size,
            pages=pages
        )
    except Exception as e:
        logger.error(f"Error searching assets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search assets"
        )


@router.patch("/{asset_id}/last-seen", response_model=AssetResponse)
async def update_asset_last_seen(
    asset_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Update the last seen timestamp for an asset."""
    try:
        asset_service = AssetService(db)
        asset = asset_service.update_asset_last_seen(asset_id)
        return asset
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


# Asset Relationship endpoints
@router.post("/relationships", response_model=AssetRelationshipResponse, status_code=status.HTTP_201_CREATED)
async def create_asset_relationship(
    relationship_data: AssetRelationshipCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Create a new asset relationship."""
    try:
        asset_service = AssetService(db)
        relationship = asset_service.create_relationship(relationship_data)
        return relationship
    except AssetNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating asset relationship: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create asset relationship"
        )


@router.get("/{asset_id}/relationships", response_model=List[AssetRelationshipResponse])
async def get_asset_relationships(
    asset_id: uuid.UUID,
    relationship_type: Optional[str] = Query(None, description="Filter by relationship type"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Get all relationships for an asset."""
    try:
        asset_service = AssetService(db)
        
        # Verify asset exists
        asset_service.get_asset(asset_id)
        
        # Convert string to enum if provided
        rel_type = None
        if relationship_type:
            from app.db.models.asset import RelationshipType
            try:
                rel_type = RelationshipType(relationship_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid relationship type: {relationship_type}"
                )
        
        relationships = asset_service.get_asset_relationships(asset_id, rel_type)
        return relationships
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


@router.put("/relationships/{relationship_id}", response_model=AssetRelationshipResponse)
async def update_asset_relationship(
    relationship_id: uuid.UUID,
    relationship_data: AssetRelationshipUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Update an asset relationship."""
    try:
        asset_service = AssetService(db)
        relationship = asset_service.update_relationship(relationship_id, relationship_data)
        return relationship
    except RelationshipNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Relationship not found"
        )
    except Exception as e:
        logger.error(f"Error updating relationship {relationship_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update relationship"
        )


@router.delete("/relationships/{relationship_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_relationship(
    relationship_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> None:
    """Delete an asset relationship."""
    try:
        asset_service = AssetService(db)
        asset_service.delete_relationship(relationship_id)
    except RelationshipNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Relationship not found"
        )
    except Exception as e:
        logger.error(f"Error deleting relationship {relationship_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete relationship"
        )


# Asset Tag endpoints
@router.post("/{asset_id}/tags", response_model=AssetTagResponse, status_code=status.HTTP_201_CREATED)
async def add_asset_tag(
    asset_id: uuid.UUID,
    tag_data: AssetTagCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Add a tag to an asset."""
    try:
        # Ensure asset_id matches the URL parameter
        tag_data.asset_id = asset_id
        
        asset_service = AssetService(db)
        tag = asset_service.add_asset_tag(tag_data)
        return tag
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    except Exception as e:
        logger.error(f"Error adding tag to asset {asset_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add asset tag"
        )


@router.get("/{asset_id}/tags", response_model=List[AssetTagResponse])
async def get_asset_tags(
    asset_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Get all tags for an asset."""
    try:
        asset_service = AssetService(db)
        
        # Verify asset exists
        asset_service.get_asset(asset_id)
        
        tags = asset_service.get_asset_tags(asset_id)
        return tags
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


@router.put("/tags/{tag_id}", response_model=AssetTagResponse)
async def update_asset_tag(
    tag_id: uuid.UUID,
    tag_data: AssetTagUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Update an asset tag."""
    try:
        asset_service = AssetService(db)
        tag = asset_service.update_asset_tag(tag_id, tag_data)
        return tag
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )
    except Exception as e:
        logger.error(f"Error updating tag {tag_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tag"
        )


@router.delete("/tags/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_tag(
    tag_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> None:
    """Delete an asset tag."""
    try:
        asset_service = AssetService(db)
        asset_service.delete_asset_tag(tag_id)
    except AssetNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )
    except Exception as e:
        logger.error(f"Error deleting tag {tag_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete tag"
        )


# Statistics endpoint
@router.get("/statistics", response_model=AssetStatistics)
async def get_asset_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Get asset statistics and analytics."""
    try:
        asset_service = AssetService(db)
        stats = asset_service.get_asset_statistics()
        return AssetStatistics(**stats)
    except Exception as e:
        logger.error(f"Error getting asset statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get asset statistics"
        )
