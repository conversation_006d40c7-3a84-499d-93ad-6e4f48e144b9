"""User management API endpoints."""

import logging
from typing import Any
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.db.models.user import User
from app.db.session import get_db
from app.dependencies import (
    require_admin,
    require_user_create,
    require_user_delete,
    require_user_read,
    require_user_role_management,
    require_user_update,
)
from app.schemas.user import (
    UserAPIKeyCreate,
    UserAPIKeyCreateResponse,
    UserAPIKeyResponse,
    UserCreate,
    UserListResponse,
    UserResponse,
    UserRoleAssignment,
    UserSessionResponse,
    UserUpdate,
)
from app.services.user_service import (
    UserAlreadyExistsError,
    UserNotFoundError,
    UserService,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: User<PERSON>reate,
    current_user: User = Depends(require_user_create),
    db: Session = Depends(get_db),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Create a new user.

    Args:
        user_data: User creation data
        current_user: Current authenticated user
        db: Database session
        user_service: User service

    Returns:
        Created user information

    Raises:
        HTTPException: If user creation fails
    """
    try:
        user = user_service.create_user(user_data, current_user.id)
        return UserResponse.from_orm(user)

    except UserAlreadyExistsError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"User creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User creation failed",
        )


@router.get("/", response_model=UserListResponse)
async def list_users(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search: str | None = Query(None, description="Search term"),
    role_filter: str | None = Query(None, description="Filter by role"),
    department_filter: str | None = Query(None, description="Filter by department"),
    active_only: bool = Query(True, description="Only active users"),
    current_user: User = Depends(require_user_read),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    List users with pagination and filtering.

    Args:
        page: Page number
        size: Page size
        search: Search term
        role_filter: Role filter
        department_filter: Department filter
        active_only: Only active users
        current_user: Current authenticated user
        user_service: User service

    Returns:
        Paginated list of users
    """
    try:
        users, total = user_service.list_users(
            page=page,
            size=size,
            search=search,
            role_filter=role_filter,
            active_only=active_only,
            department_filter=department_filter,
        )

        user_responses = [UserResponse.from_orm(user) for user in users]
        pages = (total + size - 1) // size

        return UserListResponse(
            users=user_responses, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"User listing error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve users",
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    current_user: User = Depends(require_user_read),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Get user by ID.

    Args:
        user_id: User ID
        current_user: Current authenticated user
        user_service: User service

    Returns:
        User information

    Raises:
        HTTPException: If user not found
    """
    try:
        user = user_service.get_user_by_id(uuid.UUID(user_id))

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        return UserResponse.from_orm(user)

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format"
        )
    except Exception as e:
        logger.error(f"Get user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve user",
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(require_user_update),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Update user information.

    Args:
        user_id: User ID
        user_data: User update data
        current_user: Current authenticated user
        user_service: User service

    Returns:
        Updated user information

    Raises:
        HTTPException: If user not found or update fails
    """
    try:
        user = user_service.update_user(
            user_id=uuid.UUID(user_id),
            user_data=user_data,
            updated_by_user_id=current_user.id,
        )

        return UserResponse.from_orm(user)

    except UserNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    except UserAlreadyExistsError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format"
        )
    except Exception as e:
        logger.error(f"User update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User update failed",
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: User = Depends(require_user_delete),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Delete user (soft delete).

    Args:
        user_id: User ID
        current_user: Current authenticated user
        user_service: User service

    Returns:
        Success message

    Raises:
        HTTPException: If user not found or deletion fails
    """
    try:
        # Prevent self-deletion
        if str(current_user.id) == user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own account",
            )

        success = user_service.delete_user(
            user_id=uuid.UUID(user_id), deleted_by_user_id=current_user.id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        return {"message": "User deleted successfully"}

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User deletion error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User deletion failed",
        )


@router.post("/{user_id}/roles")
async def assign_user_roles(
    user_id: str,
    role_assignment: UserRoleAssignment,
    current_user: User = Depends(require_user_role_management),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Assign roles to user.

    Args:
        user_id: User ID
        role_assignment: Role assignment data
        current_user: Current authenticated user
        user_service: User service

    Returns:
        Success message

    Raises:
        HTTPException: If user not found or role assignment fails
    """
    try:
        success = user_service.assign_roles(
            user_id=uuid.UUID(user_id),
            role_names=role_assignment.roles,
            assigned_by_user_id=current_user.id,
            expires_at=role_assignment.expires_at,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        return {"message": "Roles assigned successfully"}

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format"
        )
    except Exception as e:
        logger.error(f"Role assignment error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Role assignment failed",
        )


@router.get("/{user_id}/sessions", response_model=list[UserSessionResponse])
async def get_user_sessions(
    user_id: str,
    current_user: User = Depends(require_admin),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Get user sessions (admin only).

    Args:
        user_id: User ID
        current_user: Current authenticated user (must be admin)
        user_service: User service

    Returns:
        List of user sessions

    Raises:
        HTTPException: If user not found
    """
    try:
        sessions = user_service.get_user_sessions(uuid.UUID(user_id))
        return [UserSessionResponse.from_orm(session) for session in sessions]

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format"
        )
    except Exception as e:
        logger.error(f"Get user sessions error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve user sessions",
        )


@router.post("/{user_id}/api-keys", response_model=UserAPIKeyCreateResponse)
async def create_user_api_key(
    user_id: str,
    api_key_data: UserAPIKeyCreate,
    current_user: User = Depends(require_admin),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Create API key for user.

    Args:
        user_id: User ID
        api_key_data: API key creation data
        current_user: Current authenticated user
        user_service: User service

    Returns:
        Created API key information with secret

    Raises:
        HTTPException: If user not found or API key creation fails
    """
    try:
        api_key, secret_key = user_service.create_api_key(
            user_id=uuid.UUID(user_id),
            name=api_key_data.name,
            description=api_key_data.description,
            scopes=api_key_data.scopes,
            allowed_ips=api_key_data.allowed_ips,
            rate_limit=api_key_data.rate_limit,
            expires_at=api_key_data.expires_at,
            created_by_user_id=current_user.id,
        )

        return UserAPIKeyCreateResponse(
            api_key=UserAPIKeyResponse.from_orm(api_key), secret_key=secret_key
        )

    except UserNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user ID format"
        )
    except Exception as e:
        logger.error(f"API key creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key creation failed",
        )


@router.delete("/api-keys/{api_key_id}")
async def revoke_api_key(
    api_key_id: str,
    current_user: User = Depends(require_admin),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Revoke API key.

    Args:
        api_key_id: API key ID
        current_user: Current authenticated user
        user_service: User service

    Returns:
        Success message

    Raises:
        HTTPException: If API key not found
    """
    try:
        success = user_service.revoke_api_key(
            api_key_id=uuid.UUID(api_key_id), revoked_by_user_id=current_user.id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="API key not found"
            )

        return {"message": "API key revoked successfully"}

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid API key ID format"
        )
    except Exception as e:
        logger.error(f"API key revocation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key revocation failed",
        )


@router.get("/statistics")
async def get_user_statistics(
    current_user: User = Depends(require_admin),
    user_service: UserService = Depends(UserService),
) -> Any:
    """
    Get user statistics (admin only).

    Args:
        current_user: Current authenticated user (must be admin)
        user_service: User service

    Returns:
        User statistics
    """
    try:
        stats = user_service.get_user_statistics()
        return stats

    except Exception as e:
        logger.error(f"Get user statistics error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve user statistics",
        )
