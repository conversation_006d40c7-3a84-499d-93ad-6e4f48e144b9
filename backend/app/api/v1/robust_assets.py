"""Enhanced robust asset management API endpoints."""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_active_user
from app.db.models.user import User
from app.db.models.asset_robust import DataRetentionPolicy
from app.services.robust_asset_service import (
    RobustAssetService,
    AssetLockError,
    AssetValidationError,
    AssetNotFoundError,
)
from app.services.data_retention_service import DataRetentionService

router = APIRouter()


@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_robust_asset(
    asset_data: Dict[str, Any],
    change_reason: Optional[str] = None,
    skip_validation: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Create a new asset with full audit trail and validation."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        asset = service.create_asset(
            asset_data=asset_data,
            change_reason=change_reason,
            skip_validation=skip_validation
        )
        
        return {
            "id": str(asset.id),
            "name": asset.name,
            "asset_type": asset.asset_type,
            "provider": asset.provider,
            "status": asset.status,
            "version": asset.version,
            "created_at": asset.created_at.isoformat(),
            "checksum": asset.checksum,
            "message": "Asset created successfully with full audit trail"
        }
        
    except AssetValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Asset validation failed: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create asset: {str(e)}"
        )


@router.get("/{asset_id}")
async def get_robust_asset(
    asset_id: uuid.UUID,
    include_deleted: bool = Query(False, description="Include soft-deleted assets"),
    include_relationships: bool = Query(False, description="Include asset relationships"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Get asset by ID with optional inclusion of soft-deleted records."""
    try:
        service = RobustAssetService(db=db, user_id=current_user.username)
        
        asset = service.get_asset(
            asset_id=asset_id,
            include_deleted=include_deleted,
            include_relationships=include_relationships
        )
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Asset {asset_id} not found"
            )
        
        return {
            "id": str(asset.id),
            "name": asset.name,
            "asset_type": asset.asset_type,
            "provider": asset.provider,
            "status": asset.status,
            "version": asset.version,
            "is_deleted": asset.is_deleted,
            "deleted_at": asset.deleted_at.isoformat() if asset.deleted_at else None,
            "created_at": asset.created_at.isoformat(),
            "updated_at": asset.updated_at.isoformat(),
            "checksum": asset.checksum,
            "risk_score": asset.risk_score,
            "risk_level": asset.risk_level,
            "configuration": asset.configuration,
            "properties": asset.properties,
            "tags": asset.tags,
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get asset: {str(e)}"
        )


@router.put("/{asset_id}")
async def update_robust_asset(
    asset_id: uuid.UUID,
    update_data: Dict[str, Any],
    change_reason: Optional[str] = None,
    force_update: bool = Query(False, description="Force update even if locked"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Update asset with comprehensive audit trail and locking."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        asset = service.update_asset(
            asset_id=asset_id,
            update_data=update_data,
            change_reason=change_reason,
            force_update=force_update
        )
        
        return {
            "id": str(asset.id),
            "name": asset.name,
            "version": asset.version,
            "change_count": asset.change_count,
            "updated_at": asset.updated_at.isoformat(),
            "updated_by": asset.updated_by,
            "checksum": asset.checksum,
            "message": "Asset updated successfully with audit trail"
        }
        
    except AssetLockError as e:
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail=str(e)
        )
    except AssetValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except AssetNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update asset: {str(e)}"
        )


@router.delete("/{asset_id}")
async def soft_delete_asset(
    asset_id: uuid.UUID,
    reason: Optional[str] = None,
    retention_policy: DataRetentionPolicy = DataRetentionPolicy.LONG_TERM,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Soft delete asset with configurable retention policy."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        success = service.soft_delete_asset(
            asset_id=asset_id,
            reason=reason,
            retention_policy=retention_policy
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Asset is already deleted or cannot be deleted"
            )
        
        return {
            "asset_id": str(asset_id),
            "deleted_at": datetime.utcnow().isoformat(),
            "deleted_by": current_user.username,
            "retention_policy": retention_policy.value,
            "message": "Asset soft deleted successfully"
        }
        
    except AssetNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete asset: {str(e)}"
        )


@router.post("/{asset_id}/restore")
async def restore_asset(
    asset_id: uuid.UUID,
    reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Restore soft-deleted asset."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        success = service.restore_asset(asset_id=asset_id, reason=reason)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Asset is not deleted or cannot be restored"
            )
        
        return {
            "asset_id": str(asset_id),
            "restored_at": datetime.utcnow().isoformat(),
            "restored_by": current_user.username,
            "message": "Asset restored successfully"
        }
        
    except AssetNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to restore asset: {str(e)}"
        )


@router.delete("/{asset_id}/permanent")
async def hard_delete_asset(
    asset_id: uuid.UUID,
    confirmation_token: str,
    reason: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Permanently delete asset (requires confirmation token)."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        success = service.hard_delete_asset(
            asset_id=asset_id,
            confirmation_token=confirmation_token,
            reason=reason
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Asset cannot be permanently deleted"
            )
        
        return {
            "asset_id": str(asset_id),
            "permanently_deleted_at": datetime.utcnow().isoformat(),
            "deleted_by": current_user.username,
            "message": "Asset permanently deleted"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except AssetNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to permanently delete asset: {str(e)}"
        )


@router.post("/{asset_id}/lock")
async def acquire_asset_lock(
    asset_id: uuid.UUID,
    lock_type: str = "write",
    duration_minutes: int = Query(30, ge=1, le=1440),  # 1 minute to 24 hours
    operation: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Acquire a lock on an asset."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        lock_id = service.acquire_lock(
            asset_id=asset_id,
            lock_type=lock_type,
            duration_minutes=duration_minutes,
            operation=operation
        )
        
        return {
            "lock_id": lock_id,
            "asset_id": str(asset_id),
            "lock_type": lock_type,
            "locked_by": current_user.username,
            "expires_in_minutes": duration_minutes,
            "message": "Lock acquired successfully"
        }
        
    except AssetLockError as e:
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to acquire lock: {str(e)}"
        )


@router.delete("/locks/{lock_id}")
async def release_asset_lock(
    lock_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Release an asset lock."""
    try:
        service = RobustAssetService(
            db=db,
            user_id=current_user.username,
            session_id=f"api-{datetime.utcnow().timestamp()}"
        )
        
        success = service.release_lock(lock_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lock not found"
            )
        
        return {
            "lock_id": lock_id,
            "released_by": current_user.username,
            "released_at": datetime.utcnow().isoformat(),
            "message": "Lock released successfully"
        }
        
    except AssetLockError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to release lock: {str(e)}"
        )


@router.get("/retention/statistics")
async def get_retention_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Get data retention and storage statistics."""
    try:
        retention_service = DataRetentionService(db=db)
        stats = retention_service.get_retention_statistics()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": stats,
            "message": "Retention statistics retrieved successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get retention statistics: {str(e)}"
        )


@router.post("/retention/cleanup")
async def cleanup_expired_data(
    dry_run: bool = Query(True, description="Perform dry run without actual deletion"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """Cleanup expired data based on retention policies."""
    try:
        retention_service = DataRetentionService(db=db)
        cleanup_stats = retention_service.cleanup_expired_data(dry_run=dry_run)
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "dry_run": dry_run,
            "cleanup_statistics": cleanup_stats,
            "message": f"Data cleanup {'simulation' if dry_run else 'execution'} completed successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup expired data: {str(e)}"
        )
