"""Authentication API endpoints."""

import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from app.db.models.user import User
from app.db.session import get_db
from app.dependencies import get_current_active_user, get_current_user
from app.schemas.auth import (
    ChangePasswordRequest,
    LoginRequest,
    LoginResponse,
    LogoutRequest,
    PasswordResetConfirm,
    PasswordResetRequest,
    RefreshTokenRequest,
    SessionListResponse,
    TokenResponse,
    UserInfo,
)
from app.services.auth_service import AuthenticationError, AuthService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
async def login(
    request: Request,
    login_data: LoginRequest,
    db: Session = Depends(get_db),
    auth_service: AuthService = Depends(AuthService),
) -> Any:
    """
    User login endpoint.

    Args:
        request: FastAPI request object
        login_data: Login credentials
        db: Database session
        auth_service: Authentication service

    Returns:
        Login response with user info and tokens

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Get client information
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")

        # Authenticate user
        user, auth_result = auth_service.authenticate_user(
            username=login_data.username,
            password=login_data.password,
            ip_address=client_ip,
            user_agent=user_agent,
            mfa_code=login_data.mfa_code,
        )

        if not auth_result["success"]:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=auth_result.get("error", "Authentication failed"),
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Create tokens
        session = (
            auth_service.db.query(auth_service.db.models.user.UserSession)
            .filter(
                auth_service.db.models.user.UserSession.session_token
                == auth_result["session_id"]
            )
            .first()
        )

        tokens = auth_service.create_tokens(user, session)

        # Prepare user info
        user_info = UserInfo(
            id=str(user.id),
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            roles=[role.name for role in user.roles if role.is_active],
            permissions=user.get_permissions(),
            is_active=user.is_active,
            is_verified=user.is_verified,
            mfa_enabled=user.mfa_enabled,
            last_login_at=user.last_login_at,
        )

        return LoginResponse(
            user=user_info,
            tokens=TokenResponse(**tokens),
            requires_mfa=auth_result.get("requires_mfa", False),
            mfa_methods=auth_result.get("mfa_methods", []),
            session_id=auth_result["session_id"],
        )

    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service unavailable",
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest, auth_service: AuthService = Depends(AuthService)
) -> Any:
    """
    Refresh access token.

    Args:
        refresh_data: Refresh token data
        auth_service: Authentication service

    Returns:
        New token response

    Raises:
        HTTPException: If refresh fails
    """
    try:
        tokens = auth_service.refresh_access_token(refresh_data.refresh_token)

        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return TokenResponse(**tokens)

    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/logout")
async def logout(
    logout_data: LogoutRequest,
    current_user: User = Depends(get_current_user),
    auth_service: AuthService = Depends(AuthService),
) -> Any:
    """
    User logout endpoint.

    Args:
        logout_data: Logout options
        current_user: Current authenticated user
        auth_service: Authentication service

    Returns:
        Success message
    """
    try:
        # Get session token from current user context
        # Extract session token from JWT token (implementation needed)
        session_token = current_user.session_token if hasattr(current_user, 'session_token') else None
        if not session_token:
            raise HTTPException(status_code=400, detail="No active session found")

        success = auth_service.logout_user(
            session_token=session_token, all_sessions=logout_data.all_sessions
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Logout failed"
            )

        return {"message": "Successfully logged out"}

    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout service unavailable",
        )


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get current user information.

    Args:
        current_user: Current authenticated user

    Returns:
        Current user information
    """
    return UserInfo(
        id=str(current_user.id),
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        roles=[role.name for role in current_user.roles if role.is_active],
        permissions=current_user.get_permissions(),
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        mfa_enabled=current_user.mfa_enabled,
        last_login_at=current_user.last_login_at,
    )


@router.get("/sessions", response_model=SessionListResponse)
async def get_user_sessions(
    current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)
) -> Any:
    """
    Get current user's sessions.

    Args:
        current_user: Current authenticated user
        db: Database session

    Returns:
        List of user sessions
    """
    try:
        from app.services.user_service import UserService

        user_service = UserService(db)

        sessions = user_service.get_user_sessions(current_user.id)

        session_info = []
        for session in sessions:
            session_info.append(
                {
                    "session_id": session.session_token,
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent,
                    "created_at": session.created_at,
                    "last_activity_at": session.last_activity_at,
                    "expires_at": session.expires_at,
                    "is_current": True,  # Would determine this from current session
                }
            )

        return SessionListResponse(sessions=session_info, total=len(session_info))

    except Exception as e:
        logger.error(f"Get sessions error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve sessions",
        )


@router.post("/password/reset")
async def request_password_reset(
    reset_data: PasswordResetRequest, db: Session = Depends(get_db)
) -> Any:
    """
    Request password reset.

    Args:
        reset_data: Password reset request data
        db: Database session

    Returns:
        Success message
    """
    try:
        from app.services.user_service import UserService

        user_service = UserService(db)

        # Find user by email
        user = user_service.get_user_by_email(reset_data.email)

        if user:
            # Create password reset token
            from app.core.security import generate_password_reset_token
            from app.db.models.auth import PasswordResetToken

            token = generate_password_reset_token(user.email)

            # Store token in database
            reset_token = PasswordResetToken.create_token(
                user_id=user.id,
                token_hash=token,  # In real implementation, hash this
                expires_in_hours=24,
            )

            db.add(reset_token)
            db.commit()

            # In real implementation, send email with reset link
            logger.info(f"Password reset requested for user: {user.email}")

        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}

    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset service unavailable",
        )


@router.post("/password/reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm, db: Session = Depends(get_db)
) -> Any:
    """
    Confirm password reset with token.

    Args:
        reset_data: Password reset confirmation data
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If token is invalid or expired
    """
    try:
        from app.db.models.auth import PasswordResetToken
        from app.services.user_service import UserService

        # Find and validate token
        reset_token = (
            db.query(PasswordResetToken)
            .filter(PasswordResetToken.token_hash == reset_data.token)
            .first()
        )

        if not reset_token or not reset_token.is_valid():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token",
            )

        # Update user password
        user_service = UserService(db)
        user = user_service.get_user_by_id(reset_token.user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="User not found"
            )

        # Set new password
        user.set_password(reset_data.new_password)

        # Mark token as used
        reset_token.use_token()

        db.commit()

        logger.info(f"Password reset completed for user: {user.email}")
        return {"message": "Password reset successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset confirm error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset service unavailable",
        )


@router.post("/password/change")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Change user password.

    Args:
        password_data: Password change data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If current password is incorrect
    """
    try:
        # Verify current password
        if not current_user.check_password(password_data.current_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect",
            )

        # Set new password
        current_user.set_password(password_data.new_password)
        db.commit()

        logger.info(f"Password changed for user: {current_user.username}")
        return {"message": "Password changed successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change service unavailable",
        )
