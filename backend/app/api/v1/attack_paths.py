"""
Attack Path Analysis API Endpoints

Provides REST API endpoints for attack path analysis, blast radius calculation,
and MITRE ATT&CK framework integration.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_db, get_current_active_user
from app.db.models.user import User
from app.services.attack_path_analyzer import AttackPathAnalyzer, AttackScenario
from app.services.graph_engine import AttackPath, BlastRadiusResult, PathType, AttackTechnique

router = APIRouter()


# Pydantic models for API
class AttackPathRequest(BaseModel):
    source_asset_id: str = Field(..., description="Source asset ID for attack path analysis")
    target_asset_ids: Optional[List[str]] = Field(None, description="Target asset IDs (if None, analyzes all high-value targets)")
    max_path_length: int = Field(5, ge=1, le=10, description="Maximum path length to analyze")
    max_paths_per_target: int = Field(5, ge=1, le=20, description="Maximum paths per target")


class BlastRadiusRequest(BaseModel):
    source_asset_id: str = Field(..., description="Source asset ID for blast radius calculation")
    max_degrees: int = Field(5, ge=1, le=10, description="Maximum degrees of separation")


class AttackScenarioRequest(BaseModel):
    scenario_name: str = Field(..., description="Name of the attack scenario")
    threat_actor: str = Field(..., description="Threat actor or group")
    entry_points: List[str] = Field(..., description="List of potential entry point asset IDs")
    objectives: List[str] = Field(..., description="List of target objective asset IDs")
    description: Optional[str] = Field(None, description="Scenario description")


class AttackPathResponse(BaseModel):
    path_id: str
    source_asset_id: str
    target_asset_id: str
    path_nodes: List[str]
    path_type: str
    attack_techniques: List[str]
    risk_score: float
    likelihood: float
    impact_score: float
    blast_radius: int
    estimated_time: int
    required_privileges: List[str]
    detection_difficulty: float
    mitigation_cost: float
    path_length: int
    criticality_score: float
    criticality_level: str
    
    @classmethod
    def from_attack_path(cls, attack_path: AttackPath) -> "AttackPathResponse":
        return cls(
            path_id=attack_path.path_id,
            source_asset_id=attack_path.source_asset_id,
            target_asset_id=attack_path.target_asset_id,
            path_nodes=attack_path.path_nodes,
            path_type=attack_path.path_type.value,
            attack_techniques=[t.value for t in attack_path.attack_techniques],
            risk_score=attack_path.risk_score,
            likelihood=attack_path.likelihood,
            impact_score=attack_path.impact_score,
            blast_radius=attack_path.blast_radius,
            estimated_time=attack_path.estimated_time,
            required_privileges=attack_path.required_privileges,
            detection_difficulty=attack_path.detection_difficulty,
            mitigation_cost=attack_path.mitigation_cost,
            path_length=attack_path.path_length,
            criticality_score=attack_path.criticality_score,
            criticality_level="CRITICAL" if attack_path.criticality_score >= 80 else
                            "HIGH" if attack_path.criticality_score >= 60 else
                            "MEDIUM" if attack_path.criticality_score >= 40 else "LOW"
        )


class BlastRadiusResponse(BaseModel):
    source_asset_id: str
    affected_assets: List[str]
    impact_by_degree: Dict[int, List[str]]
    total_impact_score: float
    critical_assets_affected: List[str]
    data_assets_affected: List[str]
    service_disruption_score: float
    financial_impact: float
    compliance_impact: List[str]
    recovery_time_estimate: int
    
    @classmethod
    def from_blast_radius_result(cls, result: BlastRadiusResult) -> "BlastRadiusResponse":
        return cls(
            source_asset_id=result.source_asset_id,
            affected_assets=list(result.affected_assets),
            impact_by_degree={k: list(v) for k, v in result.impact_by_degree.items()},
            total_impact_score=result.total_impact_score,
            critical_assets_affected=list(result.critical_assets_affected),
            data_assets_affected=list(result.data_assets_affected),
            service_disruption_score=result.service_disruption_score,
            financial_impact=result.financial_impact,
            compliance_impact=result.compliance_impact,
            recovery_time_estimate=result.recovery_time_estimate
        )


class AttackScenarioResponse(BaseModel):
    scenario_id: str
    name: str
    description: str
    threat_actor: str
    attack_paths: List[AttackPathResponse]
    total_risk_score: float
    likelihood: float
    impact_score: float
    estimated_duration: int
    required_resources: List[str]
    detection_probability: float
    mitigation_strategies: List[str]
    criticality_level: str
    created_at: datetime
    
    @classmethod
    def from_attack_scenario(cls, scenario: AttackScenario) -> "AttackScenarioResponse":
        return cls(
            scenario_id=scenario.scenario_id,
            name=scenario.name,
            description=scenario.description,
            threat_actor=scenario.threat_actor,
            attack_paths=[AttackPathResponse.from_attack_path(path) for path in scenario.attack_paths],
            total_risk_score=scenario.total_risk_score,
            likelihood=scenario.likelihood,
            impact_score=scenario.impact_score,
            estimated_duration=scenario.estimated_duration,
            required_resources=scenario.required_resources,
            detection_probability=scenario.detection_probability,
            mitigation_strategies=scenario.mitigation_strategies,
            criticality_level=scenario.criticality_level,
            created_at=scenario.created_at
        )


# Global analyzer instance (will be initialized on startup)
analyzer: Optional[AttackPathAnalyzer] = None


async def get_analyzer(db: Session = Depends(get_db)) -> AttackPathAnalyzer:
    """Get or create attack path analyzer instance."""
    global analyzer
    if analyzer is None:
        analyzer = AttackPathAnalyzer(db)
        await analyzer.initialize_from_database()
    return analyzer


@router.post("/analyze", response_model=List[AttackPathResponse])
async def analyze_attack_paths(
    request: AttackPathRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Analyze attack paths from source to target assets."""
    try:
        # Validate source asset exists
        if request.source_asset_id not in analyzer.graph_engine.asset_metadata:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Source asset {request.source_asset_id} not found"
            )
        
        # Validate target assets if provided
        if request.target_asset_ids:
            for target_id in request.target_asset_ids:
                if target_id not in analyzer.graph_engine.asset_metadata:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Target asset {target_id} not found"
                    )
        
        # Perform analysis
        attack_paths = await analyzer.analyze_attack_paths(
            source_asset_id=request.source_asset_id,
            target_asset_ids=request.target_asset_ids,
            max_path_length=request.max_path_length,
            max_paths_per_target=request.max_paths_per_target
        )
        
        # Convert to response format
        response_paths = [AttackPathResponse.from_attack_path(path) for path in attack_paths]
        
        return response_paths
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Attack path analysis failed: {str(e)}"
        )


@router.post("/blast-radius", response_model=BlastRadiusResponse)
async def calculate_blast_radius(
    request: BlastRadiusRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Calculate blast radius from a compromised asset."""
    try:
        # Validate source asset exists
        if request.source_asset_id not in analyzer.graph_engine.asset_metadata:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Source asset {request.source_asset_id} not found"
            )
        
        # Calculate blast radius
        blast_result = await analyzer.calculate_blast_radius(
            source_asset_id=request.source_asset_id,
            max_degrees=request.max_degrees
        )
        
        # Convert to response format
        response = BlastRadiusResponse.from_blast_radius_result(blast_result)
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Blast radius calculation failed: {str(e)}"
        )


@router.post("/scenarios", response_model=AttackScenarioResponse)
async def create_attack_scenario(
    request: AttackScenarioRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Create and analyze a comprehensive attack scenario."""
    try:
        # Validate entry points
        for entry_point in request.entry_points:
            if entry_point not in analyzer.graph_engine.asset_metadata:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Entry point asset {entry_point} not found"
                )
        
        # Validate objectives
        for objective in request.objectives:
            if objective not in analyzer.graph_engine.asset_metadata:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Objective asset {objective} not found"
                )
        
        # Create attack scenario
        scenario = await analyzer.create_attack_scenario(
            scenario_name=request.scenario_name,
            threat_actor=request.threat_actor,
            entry_points=request.entry_points,
            objectives=request.objectives
        )
        
        # Convert to response format
        response = AttackScenarioResponse.from_attack_scenario(scenario)
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Attack scenario creation failed: {str(e)}"
        )


@router.get("/scenarios/{scenario_id}", response_model=AttackScenarioResponse)
async def get_attack_scenario(
    scenario_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Get a specific attack scenario by ID."""
    if scenario_id not in analyzer.scenario_cache:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Attack scenario {scenario_id} not found"
        )
    
    scenario = analyzer.scenario_cache[scenario_id]
    return AttackScenarioResponse.from_attack_scenario(scenario)


@router.get("/mitre-mapping/{path_id}")
async def get_mitre_attack_mapping(
    path_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Get MITRE ATT&CK framework mapping for an attack path."""
    # Find attack path in cached scenarios
    attack_path = None
    for scenario in analyzer.scenario_cache.values():
        for path in scenario.attack_paths:
            if path.path_id == path_id:
                attack_path = path
                break
        if attack_path:
            break
    
    if not attack_path:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Attack path {path_id} not found"
        )
    
    mitre_mapping = analyzer.get_mitre_attack_mapping(attack_path)
    return mitre_mapping


@router.get("/graph/statistics")
async def get_graph_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Get comprehensive graph statistics."""
    stats = analyzer.graph_engine.get_graph_statistics()
    return {
        "timestamp": datetime.utcnow().isoformat(),
        "statistics": stats,
        "message": "Graph statistics retrieved successfully"
    }


@router.post("/graph/refresh")
async def refresh_graph(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Refresh graph data from database."""
    global analyzer
    
    def refresh_task():
        global analyzer
        analyzer = AttackPathAnalyzer(db)
        # Note: This would need to be made async-safe in production
    
    background_tasks.add_task(refresh_task)
    
    return {
        "message": "Graph refresh initiated",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/export")
async def export_analysis_results(
    format: str = Query("json", description="Export format (json)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Export analysis results in various formats."""
    try:
        export_data = analyzer.export_analysis_results(format)
        
        return {
            "format": format,
            "data": export_data,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "Analysis results exported successfully"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


@router.delete("/cache")
async def clear_analysis_cache(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Clear all analysis caches."""
    analyzer.clear_cache()
    
    return {
        "message": "Analysis cache cleared successfully",
        "timestamp": datetime.utcnow().isoformat()
    }
