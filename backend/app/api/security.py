"""
Blast-Radius Security Tool - Security Management API
Advanced security controls, compliance, and threat management endpoints
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.database import get_db
from app.core.advanced_security import (
    SecurityLevel, ThreatLevel, threat_detector, audit_logger, data_classifier
)
from app.core.secrets_manager import (
    secrets_manager, SecretType, get_secret, store_secret_by_name
)
from app.core.compliance import (
    compliance_manager, ComplianceFramework, ControlStatus
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/security", tags=["security"])
security = HTTPBearer()


# Pydantic models
class ThreatAnalysisResponse(BaseModel):
    threat_detected: bool
    threat_level: str
    threats: List[Dict[str, Any]]
    risk_score: float
    recommendations: List[str]


class SecurityEventCreate(BaseModel):
    event_type: str
    description: str
    severity: str = "low"
    metadata: Optional[Dict[str, Any]] = None


class SecurityEventResponse(BaseModel):
    event_id: str
    event_type: str
    user_id: Optional[str]
    ip_address: str
    timestamp: str
    severity: str
    description: str
    resolved: bool


class SecretCreate(BaseModel):
    name: str
    value: str
    secret_type: str = "api_key"
    security_level: str = "confidential"
    expires_at: Optional[str] = None
    rotation_interval_days: Optional[int] = None
    tags: Optional[List[str]] = None
    description: str = ""


class SecretResponse(BaseModel):
    secret_id: str
    name: str
    secret_type: str
    security_level: str
    created_at: str
    updated_at: str
    expires_at: Optional[str]
    last_accessed: Optional[str]
    access_count: int
    tags: List[str]
    description: str
    is_expired: bool
    needs_rotation: bool


class ComplianceAssessmentCreate(BaseModel):
    framework: str
    scope: str = "full_system"


class ComplianceAssessmentResponse(BaseModel):
    assessment_id: str
    framework: str
    assessed_at: str
    assessor: str
    scope: str
    total_controls: int
    implemented_controls: int
    verified_controls: int
    non_compliant_controls: int
    compliance_score: float
    overall_status: str


class DataClassificationRequest(BaseModel):
    data: str


class DataClassificationResponse(BaseModel):
    highest_level: str
    classifications: List[Dict[str, Any]]
    sensitive_data_found: bool
    redaction_required: bool
    redacted_data: Optional[str] = None


# Security endpoints
@router.post("/threat-analysis", response_model=ThreatAnalysisResponse)
async def analyze_threat(request: Request):
    """
    Analyze incoming request for security threats
    """
    try:
        analysis_result = await threat_detector.analyze_request(request)
        
        return ThreatAnalysisResponse(
            threat_detected=analysis_result["threat_detected"],
            threat_level=analysis_result["threat_level"].value,
            threats=analysis_result["threats"],
            risk_score=analysis_result["risk_score"],
            recommendations=analysis_result.get("recommendations", [])
        )
        
    except Exception as e:
        logger.error(f"Threat analysis failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Threat analysis failed"
        )


@router.post("/events", response_model=SecurityEventResponse)
async def log_security_event(
    event_data: SecurityEventCreate,
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Log a security event
    """
    try:
        # Extract user info from token (simplified for demo)
        user_id = "current_user"  # Would extract from JWT token
        
        severity_map = {
            "minimal": ThreatLevel.MINIMAL,
            "low": ThreatLevel.LOW,
            "medium": ThreatLevel.MEDIUM,
            "high": ThreatLevel.HIGH,
            "critical": ThreatLevel.CRITICAL
        }
        
        severity = severity_map.get(event_data.severity.lower(), ThreatLevel.LOW)
        
        event = await audit_logger.log_security_event(
            event_type=event_data.event_type,
            description=event_data.description,
            severity=severity,
            user_id=user_id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", ""),
            metadata=event_data.metadata
        )
        
        return SecurityEventResponse(
            event_id=event.event_id,
            event_type=event.event_type,
            user_id=event.user_id,
            ip_address=event.ip_address,
            timestamp=event.timestamp.isoformat(),
            severity=event.severity.value,
            description=event.description,
            resolved=event.resolved
        )
        
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to log security event"
        )


@router.get("/events")
async def get_security_events(
    event_type: Optional[str] = Query(None),
    severity: Optional[str] = Query(None),
    user_id: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Get security events with optional filtering
    """
    try:
        events = audit_logger.events
        
        # Apply filters
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if severity:
            severity_enum = ThreatLevel(severity.lower())
            events = [e for e in events if e.severity == severity_enum]
        
        if user_id:
            events = [e for e in events if e.user_id == user_id]
        
        # Sort by timestamp (most recent first) and limit
        events.sort(key=lambda x: x.timestamp, reverse=True)
        events = events[:limit]
        
        return [
            SecurityEventResponse(
                event_id=event.event_id,
                event_type=event.event_type,
                user_id=event.user_id,
                ip_address=event.ip_address,
                timestamp=event.timestamp.isoformat(),
                severity=event.severity.value,
                description=event.description,
                resolved=event.resolved
            )
            for event in events
        ]
        
    except Exception as e:
        logger.error(f"Failed to get security events: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get security events"
        )


@router.get("/events/summary")
async def get_security_summary(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Get security events summary
    """
    try:
        return audit_logger.get_security_summary()
    except Exception as e:
        logger.error(f"Failed to get security summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get security summary"
        )


# Secrets management endpoints
@router.post("/secrets", response_model=Dict[str, str])
async def create_secret(
    secret_data: SecretCreate,
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Store a new secret
    """
    try:
        user_id = "current_user"  # Would extract from JWT token
        
        # Parse optional datetime
        expires_at = None
        if secret_data.expires_at:
            expires_at = datetime.fromisoformat(secret_data.expires_at)
        
        # Parse rotation interval
        rotation_interval = None
        if secret_data.rotation_interval_days:
            rotation_interval = timedelta(days=secret_data.rotation_interval_days)
        
        secret_id = await secrets_manager.store_secret(
            name=secret_data.name,
            value=secret_data.value,
            secret_type=SecretType(secret_data.secret_type),
            security_level=SecurityLevel(secret_data.security_level),
            expires_at=expires_at,
            rotation_interval=rotation_interval,
            tags=secret_data.tags,
            description=secret_data.description,
            user_id=user_id
        )
        
        return {"secret_id": secret_id, "message": "Secret stored successfully"}
        
    except Exception as e:
        logger.error(f"Failed to create secret: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create secret"
        )


@router.get("/secrets", response_model=List[SecretResponse])
async def list_secrets(
    secret_type: Optional[str] = Query(None),
    security_level: Optional[str] = Query(None),
    tags: Optional[str] = Query(None),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    List secrets (metadata only)
    """
    try:
        user_id = "current_user"  # Would extract from JWT token
        
        filters = {}
        if secret_type:
            filters["secret_type"] = SecretType(secret_type)
        if security_level:
            filters["security_level"] = SecurityLevel(security_level)
        if tags:
            filters["tags"] = tags.split(",")
        
        secrets_list = await secrets_manager.list_secrets(user_id, filters)
        
        return [SecretResponse(**secret) for secret in secrets_list]
        
    except Exception as e:
        logger.error(f"Failed to list secrets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list secrets"
        )


@router.get("/secrets/{secret_id}/value")
async def get_secret_value(
    secret_id: str,
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Retrieve secret value (requires proper authorization)
    """
    try:
        user_id = "current_user"  # Would extract from JWT token
        
        secret_value = await secrets_manager.retrieve_secret(
            secret_id=secret_id,
            user_id=user_id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", "")
        )
        
        if secret_value is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Secret not found or access denied"
            )
        
        return {"value": secret_value}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get secret value: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get secret value"
        )


@router.post("/secrets/{secret_id}/rotate")
async def rotate_secret(
    secret_id: str,
    new_value: str,
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Rotate a secret value
    """
    try:
        user_id = "current_user"  # Would extract from JWT token
        
        success = await secrets_manager.rotate_secret(
            secret_id=secret_id,
            new_value=new_value,
            user_id=user_id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", "")
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Secret not found or rotation failed"
            )
        
        return {"message": "Secret rotated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to rotate secret: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to rotate secret"
        )


# Compliance endpoints
@router.post("/compliance/assessments", response_model=ComplianceAssessmentResponse)
async def create_compliance_assessment(
    assessment_data: ComplianceAssessmentCreate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Conduct compliance assessment
    """
    try:
        assessor = "current_user"  # Would extract from JWT token
        
        framework = ComplianceFramework(assessment_data.framework)
        
        assessment = await compliance_manager.assess_compliance(
            framework=framework,
            assessor=assessor,
            scope=assessment_data.scope
        )
        
        return ComplianceAssessmentResponse(
            assessment_id=assessment.assessment_id,
            framework=assessment.framework.value,
            assessed_at=assessment.assessed_at.isoformat(),
            assessor=assessment.assessor,
            scope=assessment.scope,
            total_controls=assessment.total_controls,
            implemented_controls=assessment.implemented_controls,
            verified_controls=assessment.verified_controls,
            non_compliant_controls=assessment.non_compliant_controls,
            compliance_score=assessment.compliance_score,
            overall_status=assessment.overall_status
        )
        
    except Exception as e:
        logger.error(f"Failed to create compliance assessment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create compliance assessment"
        )


@router.get("/compliance/dashboard")
async def get_compliance_dashboard(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Get compliance dashboard data
    """
    try:
        return compliance_manager.get_compliance_dashboard()
    except Exception as e:
        logger.error(f"Failed to get compliance dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get compliance dashboard"
        )


@router.get("/compliance/reports/{framework}")
async def get_compliance_report(
    framework: str,
    assessment_id: Optional[str] = Query(None),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Generate compliance report
    """
    try:
        framework_enum = ComplianceFramework(framework)
        
        report = compliance_manager.generate_compliance_report(
            framework=framework_enum,
            assessment_id=assessment_id
        )
        
        return report
        
    except Exception as e:
        logger.error(f"Failed to generate compliance report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate compliance report"
        )


# Data classification endpoints
@router.post("/data-classification", response_model=DataClassificationResponse)
async def classify_data(
    request_data: DataClassificationRequest,
    include_redacted: bool = Query(False),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Classify data and optionally return redacted version
    """
    try:
        classification_result = data_classifier.classify_data(request_data.data)
        
        response = DataClassificationResponse(
            highest_level=classification_result["highest_level"].value,
            classifications=classification_result["classifications"],
            sensitive_data_found=classification_result["sensitive_data_found"],
            redaction_required=classification_result["redaction_required"]
        )
        
        if include_redacted and classification_result["redaction_required"]:
            response.redacted_data = data_classifier.redact_sensitive_data(request_data.data)
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to classify data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to classify data"
        )


@router.post("/ip-block")
async def block_ip_address(
    ip_address: str,
    reason: str = "Security violation",
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Block IP address
    """
    try:
        await threat_detector.block_ip(ip_address, reason)
        
        # Log the action
        await audit_logger.log_security_event(
            event_type="ip_blocked",
            description=f"IP {ip_address} blocked: {reason}",
            severity=ThreatLevel.HIGH,
            user_id="current_user",
            metadata={"blocked_ip": ip_address, "reason": reason}
        )
        
        return {"message": f"IP {ip_address} blocked successfully"}
        
    except Exception as e:
        logger.error(f"Failed to block IP: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to block IP address"
        )


@router.delete("/ip-block/{ip_address}")
async def unblock_ip_address(
    ip_address: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Unblock IP address
    """
    try:
        await threat_detector.unblock_ip(ip_address)
        
        # Log the action
        await audit_logger.log_security_event(
            event_type="ip_unblocked",
            description=f"IP {ip_address} unblocked",
            severity=ThreatLevel.LOW,
            user_id="current_user",
            metadata={"unblocked_ip": ip_address}
        )
        
        return {"message": f"IP {ip_address} unblocked successfully"}
        
    except Exception as e:
        logger.error(f"Failed to unblock IP: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unblock IP address"
        )
