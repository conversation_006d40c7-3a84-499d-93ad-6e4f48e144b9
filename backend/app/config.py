"""Configuration management for Blast-Radius Security Tool."""

import os
import tempfile

from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application Settings
    PROJECT_NAME: str = "Blast-Radius Security Tool"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    ENVIRONMENT: str = "development"

    # Security Settings
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Database Configuration
    DATABASE_URL: str
    DATABASE_TEST_URL: str | None = None

    # Redis Configuration
    REDIS_URL: str
    REDIS_TEST_URL: str | None = None

    # Neo4j Configuration
    NEO4J_URL: str
    NEO4J_USER: str
    NEO4J_PASSWORD: str

    # Azure Identity Configuration
    AZURE_CLIENT_ID: str | None = None
    AZURE_CLIENT_SECRET: str | None = None
    AZURE_TENANT_ID: str | None = None
    AZURE_AUTHORITY: str | None = None

    # CORS Configuration
    CORS_ORIGINS: list[AnyHttpUrl] = []
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list[str] = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    CORS_ALLOW_HEADERS: list[str] = ["*"]

    # API Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 100
    RATE_LIMIT_BURST: int = 20

    # Celery Configuration
    CELERY_BROKER_URL: str | None = None
    CELERY_RESULT_BACKEND: str | None = None

    # Monitoring and Observability
    PROMETHEUS_ENABLED: bool = True
    PROMETHEUS_PORT: int = 9090
    JAEGER_ENABLED: bool = False
    JAEGER_ENDPOINT: str | None = None

    # File Upload Configuration
    MAX_UPLOAD_SIZE: int = 10485760  # 10MB
    ALLOWED_FILE_TYPES: list[str] = ["json", "csv", "xml", "yaml"]
    UPLOAD_PATH: str = os.path.join(tempfile.gettempdir(), "blast_radius_uploads")

    # External API Configuration
    THREAT_INTEL_API_KEY: str | None = None
    SERVICENOW_INSTANCE_URL: str | None = None
    SERVICENOW_USERNAME: str | None = None
    SERVICENOW_PASSWORD: str | None = None

    # Cloud Provider Configuration
    AWS_ACCESS_KEY_ID: str | None = None
    AWS_SECRET_ACCESS_KEY: str | None = None
    AWS_DEFAULT_REGION: str = "us-east-1"

    AZURE_SUBSCRIPTION_ID: str | None = None
    AZURE_RESOURCE_GROUP: str | None = None

    GCP_PROJECT_ID: str | None = None
    GCP_SERVICE_ACCOUNT_KEY_PATH: str | None = None

    # Graph Analysis Configuration
    MAX_GRAPH_NODES: int = ********
    MAX_ATTACK_PATH_DEPTH: int = 5
    GRAPH_ANALYSIS_TIMEOUT: int = 300  # seconds
    PARALLEL_PROCESSING_WORKERS: int = 4

    # Caching Configuration
    CACHE_TTL_SECONDS: int = 3600
    CACHE_MAX_SIZE: int = 1000
    ENABLE_QUERY_CACHE: bool = True

    # Session Configuration
    SESSION_TIMEOUT_MINUTES: int = 60
    SESSION_CLEANUP_INTERVAL_MINUTES: int = 15
    MAX_CONCURRENT_SESSIONS_PER_USER: int = 5

    # Audit and Compliance
    AUDIT_LOG_ENABLED: bool = True
    AUDIT_LOG_RETENTION_DAYS: int = 365
    COMPLIANCE_MODE: str = "SOC2"
    GDPR_ENABLED: bool = True

    # Performance Tuning
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 3600

    # Development and Testing
    TESTING: bool = False
    MOCK_EXTERNAL_APIS: bool = False
    ENABLE_DEBUG_TOOLBAR: bool = False
    PROFILING_ENABLED: bool = False

    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str] | str:
        """Parse CORS origins from environment variable."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        if isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @validator("SECRET_KEY", pre=True)
    def validate_secret_key(cls, v: str) -> str:
        """Validate secret key is provided and secure."""
        if not v:
            raise ValueError("SECRET_KEY must be provided")
        # Allow shorter keys for testing
        if not os.getenv("TESTING") and len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v

    @validator("DATABASE_URL", pre=True)
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL format."""
        if not v:
            raise ValueError("DATABASE_URL must be provided")
        if not v.startswith(("postgresql://", "postgresql+asyncpg://")):
            raise ValueError("DATABASE_URL must be a PostgreSQL URL")
        return v

    @validator("REDIS_URL", pre=True)
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL format."""
        if not v:
            raise ValueError("REDIS_URL must be provided")
        if not v.startswith("redis://"):
            raise ValueError("REDIS_URL must be a Redis URL")
        return v

    @validator("NEO4J_URL", pre=True)
    def validate_neo4j_url(cls, v: str) -> str:
        """Validate Neo4j URL format."""
        if not v:
            raise ValueError("NEO4J_URL must be provided")
        if not v.startswith(("bolt://", "neo4j://", "neo4j+s://", "bolt+s://")):
            raise ValueError("NEO4J_URL must be a valid Neo4j URL")
        return v

    class Config:
        """Pydantic configuration."""

        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings instance."""
    return settings


# Database URL for different environments
def get_database_url() -> str:
    """Get database URL based on environment."""
    if settings.TESTING and settings.DATABASE_TEST_URL:
        return settings.DATABASE_TEST_URL
    return settings.DATABASE_URL


def get_redis_url() -> str:
    """Get Redis URL based on environment."""
    if settings.TESTING and settings.REDIS_TEST_URL:
        return settings.REDIS_TEST_URL
    return settings.REDIS_URL


# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
        "json": {
            "format": '{"timestamp": "%(asctime)s", "name": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}',
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": settings.LOG_LEVEL,
            "formatter": "default",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": settings.LOG_LEVEL,
            "formatter": "detailed",
            "filename": "logs/app.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "": {
            "level": settings.LOG_LEVEL,
            "handlers": ["console", "file"],
        },
        "uvicorn": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
        "sqlalchemy.engine": {
            "level": "WARNING" if not settings.DEBUG else "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
    },
}
