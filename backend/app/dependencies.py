"""FastAPI dependencies for authentication and authorization."""

import logging
import uuid

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.core.permissions import Permission, has_permission
from app.core.security import verify_token
from app.db.models.user import User
from app.db.session import get_db, set_audit_context
from app.services.auth_service import AuthService
from app.services.user_service import UserService

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """
    Get authentication service instance.

    Args:
        db: Database session

    Returns:
        AuthService instance
    """
    return AuthService(db)


def get_user_service(db: Session = Depends(get_db)) -> UserService:
    """
    Get user service instance.

    Args:
        db: Database session

    Returns:
        UserService instance
    """
    return UserService(db)


async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
    auth_service: AuthService = Depends(get_auth_service),
) -> User:
    """
    Get current authenticated user from JWT token.

    Args:
        request: FastAPI request object
        credentials: HTTP authorization credentials
        db: Database session
        auth_service: Authentication service

    Returns:
        Current user object

    Raises:
        HTTPException: If authentication fails
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Verify JWT token
        token_data = verify_token(credentials.credentials)
        if token_data is None:
            raise credentials_exception

        # Extract user info from token
        if isinstance(token_data, dict):
            user_id = token_data.get("user_id")
            session_id = token_data.get("session_id")
        else:
            user_id = token_data
            session_id = None

        if user_id is None:
            raise credentials_exception

        # Validate session if session_id is present
        if session_id:
            user = auth_service.validate_session(session_id)
            if user is None:
                raise credentials_exception
        else:
            # Fallback to user lookup by ID
            from app.services.user_service import UserService

            user_service = UserService(db)
            user = user_service.get_user_by_id(uuid.UUID(user_id))
            if user is None or not user.is_active:
                raise credentials_exception

        # Set audit context
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        request_id = request.headers.get("x-request-id")

        set_audit_context(
            user_id=user.id,
            user_ip=client_ip,
            user_agent=user_agent,
            session_id=session_id,
            request_id=request_id,
            api_endpoint=str(request.url.path),
            http_method=request.method,
        )

        return user

    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise credentials_exception


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current active user.

    Args:
        current_user: Current user from authentication

    Returns:
        Current active user

    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )
    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """
    Get current verified user.

    Args:
        current_user: Current active user

    Returns:
        Current verified user

    Raises:
        HTTPException: If user is not verified
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email not verified"
        )
    return current_user


def require_permissions(required_permissions: list[Permission]):
    """
    Dependency factory for requiring specific permissions.

    Args:
        required_permissions: List of required permissions

    Returns:
        Dependency function
    """

    async def check_permissions(
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """
        Check if current user has required permissions.

        Args:
            current_user: Current active user

        Returns:
            Current user if permissions are satisfied

        Raises:
            HTTPException: If user lacks required permissions
        """
        user_roles = [role.name for role in current_user.roles if role.is_active]

        for permission in required_permissions:
            if not has_permission(user_roles, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required: {permission.value}",
                )

        return current_user

    return check_permissions


def require_roles(required_roles: list[str]):
    """
    Dependency factory for requiring specific roles.

    Args:
        required_roles: List of required role names

    Returns:
        Dependency function
    """

    async def check_roles(
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """
        Check if current user has required roles.

        Args:
            current_user: Current active user

        Returns:
            Current user if roles are satisfied

        Raises:
            HTTPException: If user lacks required roles
        """
        user_roles = [role.name for role in current_user.roles if role.is_active]

        for required_role in required_roles:
            if required_role not in user_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required role: {required_role}",
                )

        return current_user

    return check_roles


def require_admin():
    """
    Dependency for requiring admin role.

    Returns:
        Dependency function
    """
    return require_roles(["admin"])


def require_soc_operator():
    """
    Dependency for requiring SOC operator role.

    Returns:
        Dependency function
    """
    return require_roles(["soc_operator", "admin"])


def require_security_architect():
    """
    Dependency for requiring security architect role.

    Returns:
        Dependency function
    """
    return require_roles(["security_architect", "admin"])


def require_red_team():
    """
    Dependency for requiring red team member role.

    Returns:
        Dependency function
    """
    return require_roles(["red_team_member", "admin"])


def require_purple_team():
    """
    Dependency for requiring purple team member role.

    Returns:
        Dependency function
    """
    return require_roles(["purple_team_member", "admin"])


# Common permission dependencies
require_user_read = require_permissions([Permission.READ_USER])
require_user_create = require_permissions([Permission.CREATE_USER])
require_user_update = require_permissions([Permission.UPDATE_USER])
require_user_delete = require_permissions([Permission.DELETE_USER])
require_user_role_management = require_permissions([Permission.MANAGE_USER_ROLES])

require_asset_read = require_permissions([Permission.READ_ASSET])
require_asset_create = require_permissions([Permission.CREATE_ASSET])
require_asset_update = require_permissions([Permission.UPDATE_ASSET])
require_asset_delete = require_permissions([Permission.DELETE_ASSET])

require_incident_view = require_permissions([Permission.VIEW_SECURITY_EVENTS])
require_incident_create = require_permissions([Permission.CREATE_INCIDENT])
require_incident_update = require_permissions([Permission.UPDATE_INCIDENT])

require_attack_path_view = require_permissions([Permission.VIEW_ATTACK_PATHS])
require_attack_scenario_create = require_permissions(
    [Permission.CREATE_ATTACK_SCENARIO]
)

require_threat_intel_view = require_permissions([Permission.VIEW_THREAT_INTEL])
require_threat_intel_manage = require_permissions([Permission.MANAGE_THREAT_INTEL])

require_dashboard_view = require_permissions([Permission.VIEW_DASHBOARD])
require_dashboard_create = require_permissions([Permission.CREATE_DASHBOARD])

require_reports_view = require_permissions([Permission.VIEW_REPORTS])
require_reports_create = require_permissions([Permission.CREATE_REPORTS])

require_system_config = require_permissions([Permission.MANAGE_SYSTEM_CONFIG])
require_audit_logs = require_permissions([Permission.VIEW_AUDIT_LOGS])
