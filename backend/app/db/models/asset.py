"""Asset management database models."""

import enum
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    Index,
)
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class AssetType(str, enum.Enum):
    """Asset type enumeration."""

    # Cloud Infrastructure
    CLOUD_INSTANCE = "cloud_instance"
    CLOUD_STORAGE = "cloud_storage"
    CLOUD_DATABASE = "cloud_database"
    CLOUD_FUNCTION = "cloud_function"
    CLOUD_NETWORK = "cloud_network"
    CLOUD_LOAD_BALANCER = "cloud_load_balancer"
    CLOUD_CDN = "cloud_cdn"
    CLOUD_QUEUE = "cloud_queue"
    CLOUD_CACHE = "cloud_cache"
    CLOUD_DNS = "cloud_dns"
    CLOUD_IAM_ROLE = "cloud_iam_role"
    CLOUD_IAM_USER = "cloud_iam_user"
    CLOUD_IAM_POLICY = "cloud_iam_policy"
    CLOUD_SECURITY_GROUP = "cloud_security_group"
    CLOUD_VPC = "cloud_vpc"
    CLOUD_SUBNET = "cloud_subnet"

    # Network Infrastructure
    NETWORK_DEVICE = "network_device"
    ROUTER = "router"
    SWITCH = "switch"
    FIREWALL = "firewall"
    LOAD_BALANCER = "load_balancer"
    PROXY = "proxy"
    VPN_GATEWAY = "vpn_gateway"
    WIRELESS_ACCESS_POINT = "wireless_access_point"

    # Compute Resources
    SERVER = "server"
    VIRTUAL_MACHINE = "virtual_machine"
    CONTAINER = "container"
    KUBERNETES_RESOURCE = "kubernetes_resource"
    KUBERNETES_CLUSTER = "kubernetes_cluster"
    KUBERNETES_NODE = "kubernetes_node"
    KUBERNETES_POD = "kubernetes_pod"
    KUBERNETES_SERVICE = "kubernetes_service"

    # Applications and Services
    APPLICATION = "application"
    SERVICE = "service"
    API_ENDPOINT = "api_endpoint"
    WEB_APPLICATION = "web_application"
    DATABASE = "database"
    MESSAGE_QUEUE = "message_queue"
    CACHE_SERVICE = "cache_service"

    # Security and Identity
    CERTIFICATE = "certificate"
    SECRET = "secret"  # nosec B105 - Enum value for secret management assets, not a hardcoded secret
    API_KEY = "api_key"
    IDENTITY_PROVIDER = "identity_provider"

    # Monitoring and Logging
    MONITORING_AGENT = "monitoring_agent"
    LOG_COLLECTOR = "log_collector"
    METRICS_ENDPOINT = "metrics_endpoint"

    # Data and Storage
    FILE_SHARE = "file_share"
    BACKUP = "backup"
    SNAPSHOT = "snapshot"

    # IoT and Edge
    IOT_DEVICE = "iot_device"
    EDGE_DEVICE = "edge_device"
    SENSOR = "sensor"

    # Unknown/Other
    UNKNOWN = "unknown"
    OTHER = "other"


class AssetProvider(str, enum.Enum):
    """Asset provider enumeration."""

    # Major Cloud Providers
    AWS = "aws"
    AZURE = "azure"
    GCP = "gcp"
    ALIBABA_CLOUD = "alibaba_cloud"
    ORACLE_CLOUD = "oracle_cloud"
    IBM_CLOUD = "ibm_cloud"
    DIGITALOCEAN = "digitalocean"
    LINODE = "linode"
    VULTR = "vultr"

    # Container Platforms
    KUBERNETES = "kubernetes"
    DOCKER = "docker"
    OPENSHIFT = "openshift"
    RANCHER = "rancher"

    # Virtualization Platforms
    VMWARE = "vmware"
    HYPER_V = "hyper_v"
    CITRIX = "citrix"
    PROXMOX = "proxmox"

    # On-Premises and Physical
    ON_PREMISES = "on_premises"
    PHYSICAL = "physical"
    BARE_METAL = "bare_metal"

    # Edge and IoT
    EDGE = "edge"
    IOT = "iot"

    # SaaS and External
    SAAS = "saas"
    EXTERNAL = "external"
    THIRD_PARTY = "third_party"

    # Unknown
    UNKNOWN = "unknown"


class AssetStatus(str, enum.Enum):
    """Asset status enumeration."""
    
    ACTIVE = "active"
    INACTIVE = "inactive"
    TERMINATED = "terminated"
    PENDING = "pending"
    ERROR = "error"
    UNKNOWN = "unknown"


class DiscoverySource(str, enum.Enum):
    """Discovery source enumeration."""

    # Cloud APIs
    CLOUD_API = "cloud_api"
    AWS_API = "aws_api"
    AZURE_API = "azure_api"
    GCP_API = "gcp_api"

    # Network Discovery
    NETWORK_SCAN = "network_scan"
    NMAP = "nmap"
    MASSCAN = "masscan"
    ZMAP = "zmap"

    # Agent-based Discovery
    AGENT = "agent"
    OSQUERY = "osquery"
    WAZUH = "wazuh"
    DATADOG_AGENT = "datadog_agent"
    NEW_RELIC_AGENT = "new_relic_agent"

    # API Discovery Tools
    API_DISCOVERY = "api_discovery"
    AKTO = "akto"
    KITERUNNER = "kiterunner"
    BURP_SUITE = "burp_suite"
    OWASP_ZAP = "owasp_zap"
    POSTMAN = "postman"

    # Container and Orchestration
    DOCKER_API = "docker_api"
    KUBERNETES_API = "kubernetes_api"
    HELM = "helm"

    # Configuration Management
    ANSIBLE = "ansible"
    TERRAFORM = "terraform"
    PUPPET = "puppet"
    CHEF = "chef"

    # Vulnerability Scanners
    NESSUS = "nessus"
    OPENVAS = "openvas"
    QUALYS = "qualys"
    RAPID7 = "rapid7"

    # SIEM and Log Analysis
    SPLUNK = "splunk"
    ELASTIC = "elastic"
    SUMO_LOGIC = "sumo_logic"

    # Manual and Import
    MANUAL = "manual"
    IMPORT = "import"
    CSV_IMPORT = "csv_import"
    JSON_IMPORT = "json_import"

    # Unknown
    UNKNOWN = "unknown"


class RiskLevel(str, enum.Enum):
    """Risk level enumeration."""
    
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class Asset(Base):
    """Asset model for infrastructure and application components."""

    __tablename__ = "assets"

    # Basic asset information
    name = Column(String(255), nullable=False, index=True)
    asset_type = Column(Enum(AssetType), nullable=False, index=True)
    provider = Column(Enum(AssetProvider), nullable=False, index=True)
    status = Column(Enum(AssetStatus), default=AssetStatus.ACTIVE, nullable=False, index=True)

    # Provider-specific identifiers
    provider_id = Column(String(255), nullable=True, index=True)  # AWS instance ID, Azure resource ID, etc.
    provider_region = Column(String(100), nullable=True, index=True)
    provider_zone = Column(String(100), nullable=True)
    provider_account_id = Column(String(255), nullable=True, index=True)

    # Network information
    ip_addresses = Column(JSONB, nullable=True)  # List of IP addresses
    dns_names = Column(JSONB, nullable=True)  # List of DNS names
    ports = Column(JSONB, nullable=True)  # List of open ports
    protocols = Column(JSONB, nullable=True)  # List of protocols

    # Asset metadata
    description = Column(Text, nullable=True)
    environment = Column(String(50), nullable=True, index=True)  # prod, staging, dev, etc.
    owner = Column(String(255), nullable=True, index=True)
    team = Column(String(255), nullable=True, index=True)
    cost_center = Column(String(100), nullable=True)

    # Configuration and properties
    configuration = Column(JSONB, nullable=True)  # Asset-specific configuration
    properties = Column(JSONB, nullable=True)  # Additional properties
    tags = Column(JSONB, nullable=True)  # Key-value tags

    # Discovery information
    discovery_source = Column(Enum(DiscoverySource), nullable=False, index=True)
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    last_seen = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    discovery_job_id = Column(UUID(as_uuid=True), ForeignKey("discovery_jobs.id"), nullable=True)

    # Security and compliance
    risk_score = Column(Integer, default=0, nullable=False, index=True)
    risk_level = Column(Enum(RiskLevel), default=RiskLevel.INFO, nullable=False, index=True)
    compliance_status = Column(String(50), default="unknown", nullable=False, index=True)
    vulnerabilities = Column(JSONB, nullable=True)  # List of vulnerabilities
    security_groups = Column(JSONB, nullable=True)  # Security group information

    # Monitoring and health
    is_monitored = Column(Boolean, default=False, nullable=False)
    health_status = Column(String(50), default="unknown", nullable=True)
    last_health_check = Column(DateTime, nullable=True)
    metrics = Column(JSONB, nullable=True)  # Performance and health metrics

    # Change tracking
    configuration_hash = Column(String(64), nullable=True, index=True)  # SHA256 hash of configuration
    last_configuration_change = Column(DateTime, nullable=True)
    change_count = Column(Integer, default=0, nullable=False)

    # Relationships
    discovery_job = relationship("DiscoveryJob", back_populates="assets")
    asset_tags = relationship("AssetTag", back_populates="asset", cascade="all, delete-orphan")
    
    # Self-referential relationships for parent-child assets
    parent_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=True)
    parent = relationship("Asset", remote_side="Asset.id", backref="children")

    # Indexes for performance
    __table_args__ = (
        Index("idx_asset_provider_type", "provider", "asset_type"),
        Index("idx_asset_discovery", "discovery_source", "discovered_at"),
        Index("idx_asset_risk", "risk_level", "risk_score"),
        Index("idx_asset_status_lastseen", "status", "last_seen"),
        UniqueConstraint("provider", "provider_id", name="uq_asset_provider_id"),
    )

    def __repr__(self) -> str:
        """String representation of the asset."""
        return f"<Asset(name={self.name}, type={self.asset_type}, provider={self.provider})>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert asset to dictionary representation."""
        return {
            "id": str(self.id),
            "name": self.name,
            "asset_type": self.asset_type.value,
            "provider": self.provider.value,
            "status": self.status.value,
            "provider_id": self.provider_id,
            "provider_region": self.provider_region,
            "ip_addresses": self.ip_addresses,
            "dns_names": self.dns_names,
            "environment": self.environment,
            "owner": self.owner,
            "team": self.team,
            "risk_score": self.risk_score,
            "risk_level": self.risk_level.value,
            "discovered_at": self.discovered_at.isoformat() if self.discovered_at else None,
            "last_seen": self.last_seen.isoformat() if self.last_seen else None,
            "tags": self.tags,
            "configuration": self.configuration,
        }


class RelationshipType(str, enum.Enum):
    """Asset relationship type enumeration."""
    
    DEPENDS_ON = "depends_on"
    COMMUNICATES_WITH = "communicates_with"
    CONTAINS = "contains"
    MANAGES = "manages"
    ROUTES_TO = "routes_to"
    AUTHENTICATES_WITH = "authenticates_with"
    STORES_DATA_IN = "stores_data_in"
    EXPOSES = "exposes"
    MONITORS = "monitors"
    BACKS_UP_TO = "backs_up_to"


class AssetRelationship(Base):
    """Asset relationship model for mapping connections between assets."""

    __tablename__ = "asset_relationships"

    # Relationship definition
    source_asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    target_asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    relationship_type = Column(Enum(RelationshipType), nullable=False, index=True)

    # Relationship metadata
    description = Column(Text, nullable=True)
    properties = Column(JSONB, nullable=True)  # Additional relationship properties
    
    # Network/communication details
    protocol = Column(String(20), nullable=True)  # TCP, UDP, HTTP, etc.
    port = Column(Integer, nullable=True)
    direction = Column(String(20), nullable=True)  # inbound, outbound, bidirectional

    # Discovery and confidence
    discovery_source = Column(Enum(DiscoverySource), nullable=False, index=True)
    confidence_score = Column(Float, default=1.0, nullable=False)  # 0.0 to 1.0
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    last_verified = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Status and validation
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False)
    verification_method = Column(String(100), nullable=True)

    # Relationships
    source_asset = relationship("Asset", foreign_keys=[source_asset_id])
    target_asset = relationship("Asset", foreign_keys=[target_asset_id])

    # Indexes for performance
    __table_args__ = (
        Index("idx_relationship_source_target", "source_asset_id", "target_asset_id"),
        Index("idx_relationship_type_active", "relationship_type", "is_active"),
        Index("idx_relationship_discovery", "discovery_source", "discovered_at"),
        UniqueConstraint(
            "source_asset_id", 
            "target_asset_id", 
            "relationship_type", 
            "protocol", 
            "port",
            name="uq_asset_relationship"
        ),
    )

    def __repr__(self) -> str:
        """String representation of the relationship."""
        return f"<AssetRelationship(source={self.source_asset_id}, target={self.target_asset_id}, type={self.relationship_type})>"


class DiscoveryJobStatus(str, enum.Enum):
    """Discovery job status enumeration."""
    
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL = "partial"


class DiscoveryJob(Base):
    """Discovery job model for tracking asset discovery operations."""

    __tablename__ = "discovery_jobs"

    # Job information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    job_type = Column(String(100), nullable=False, index=True)  # cloud_discovery, network_scan, api_discovery
    status = Column(Enum(DiscoveryJobStatus), default=DiscoveryJobStatus.PENDING, nullable=False, index=True)

    # Execution details
    started_at = Column(DateTime, nullable=True, index=True)
    completed_at = Column(DateTime, nullable=True, index=True)
    duration_seconds = Column(Integer, nullable=True)

    # Configuration and parameters
    configuration = Column(JSONB, nullable=True)  # Job-specific configuration
    parameters = Column(JSONB, nullable=True)  # Discovery parameters
    filters = Column(JSONB, nullable=True)  # Asset filters

    # Results and statistics
    assets_discovered = Column(Integer, default=0, nullable=False)
    assets_updated = Column(Integer, default=0, nullable=False)
    relationships_discovered = Column(Integer, default=0, nullable=False)
    errors_count = Column(Integer, default=0, nullable=False)
    
    # Execution details
    executor = Column(String(255), nullable=True)  # User or system that started the job
    execution_log = Column(JSONB, nullable=True)  # Execution log entries
    error_details = Column(JSONB, nullable=True)  # Error information

    # Scheduling
    is_scheduled = Column(Boolean, default=False, nullable=False)
    schedule_expression = Column(String(255), nullable=True)  # Cron expression
    next_run_at = Column(DateTime, nullable=True, index=True)

    # Relationships
    assets = relationship("Asset", back_populates="discovery_job")

    # Indexes for performance
    __table_args__ = (
        Index("idx_discovery_job_status_type", "status", "job_type"),
        Index("idx_discovery_job_schedule", "is_scheduled", "next_run_at"),
        Index("idx_discovery_job_execution", "started_at", "completed_at"),
    )

    def __repr__(self) -> str:
        """String representation of the discovery job."""
        return f"<DiscoveryJob(name={self.name}, type={self.job_type}, status={self.status})>"


class AssetTag(Base):
    """Asset tag model for flexible asset categorization."""

    __tablename__ = "asset_tags"

    # Tag information
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    key = Column(String(255), nullable=False, index=True)
    value = Column(String(1000), nullable=True, index=True)

    # Tag metadata
    source = Column(String(100), nullable=True)  # Where the tag came from
    is_system = Column(Boolean, default=False, nullable=False)  # System vs user tag
    is_inherited = Column(Boolean, default=False, nullable=False)  # Inherited from parent

    # Relationships
    asset = relationship("Asset", back_populates="asset_tags")

    # Indexes for performance
    __table_args__ = (
        Index("idx_asset_tag_key_value", "key", "value"),
        Index("idx_asset_tag_asset_key", "asset_id", "key"),
        UniqueConstraint("asset_id", "key", name="uq_asset_tag_key"),
    )

    def __repr__(self) -> str:
        """String representation of the asset tag."""
        return f"<AssetTag(asset_id={self.asset_id}, key={self.key}, value={self.value})>"
