"""Authentication-related database models."""

from datetime import datetime, timedelta
import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class LoginAttempt(Base):
    """Track login attempts for security monitoring."""

    __tablename__ = "login_attempts"

    # User information (may be null for failed attempts with invalid usernames)
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True
    )
    username = Column(String(255), nullable=False, index=True)
    email = Column(String(255), nullable=True, index=True)

    # Attempt details
    success = Column(Boolean, nullable=False, index=True)
    failure_reason = Column(
        String(100), nullable=True
    )  # invalid_credentials, account_locked, etc.

    # Request information
    ip_address = Column(String(45), nullable=True, index=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    device_fingerprint = Column(String(255), nullable=True)

    # Geographic information (if available)
    country = Column(String(2), nullable=True)  # ISO country code
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)

    # Security flags
    is_suspicious = Column(Boolean, default=False, nullable=False, index=True)
    risk_score = Column(Integer, default=0, nullable=False)  # 0-100 risk score

    # Additional context
    session_id = Column(String(255), nullable=True, index=True)
    request_id = Column(String(255), nullable=True, index=True)

    # Relationships
    user = relationship("User", backref="login_attempts")

    def __repr__(self) -> str:
        """String representation of the login attempt."""
        status = "SUCCESS" if self.success else "FAILED"
        return f"<LoginAttempt(username={self.username}, status={status}, ip={self.ip_address})>"

    @classmethod
    def create_attempt(
        cls,
        username: str,
        success: bool,
        ip_address: str | None = None,
        user_agent: str | None = None,
        user_id: uuid.UUID | None = None,
        failure_reason: str | None = None,
        **kwargs,
    ) -> "LoginAttempt":
        """
        Create a new login attempt record.

        Args:
            username: Username used in login attempt
            success: Whether the login was successful
            ip_address: IP address of the attempt
            user_agent: User agent string
            user_id: User ID if known
            failure_reason: Reason for failure if applicable
            **kwargs: Additional fields

        Returns:
            LoginAttempt instance
        """
        return cls(
            username=username,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            user_id=user_id,
            failure_reason=failure_reason,
            **kwargs,
        )

    def calculate_risk_score(self) -> int:
        """
        Calculate risk score for this login attempt.

        Returns:
            Risk score from 0-100
        """
        score = 0

        # Failed login increases risk
        if not self.success:
            score += 30

        # Suspicious flag increases risk
        if self.is_suspicious:
            score += 40

        # Unknown location increases risk (simplified)
        if not self.country:
            score += 20

        # Unusual user agent patterns (simplified)
        if self.user_agent and (
            "bot" in self.user_agent.lower() or "crawler" in self.user_agent.lower()
        ):
            score += 25

        return min(score, 100)


class PasswordResetToken(Base):
    """Password reset tokens for secure password recovery."""

    __tablename__ = "password_reset_tokens"

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    token_hash = Column(String(255), nullable=False, unique=True, index=True)

    # Token status
    is_used = Column(Boolean, default=False, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False, index=True)

    # Usage tracking
    used_at = Column(DateTime, nullable=True)
    used_ip = Column(String(45), nullable=True)

    # Request information
    requested_ip = Column(String(45), nullable=True)
    requested_user_agent = Column(Text, nullable=True)

    # Security
    max_attempts = Column(Integer, default=3, nullable=False)
    attempt_count = Column(Integer, default=0, nullable=False)

    # Relationships
    user = relationship("User", backref="password_reset_tokens")

    def __repr__(self) -> str:
        """String representation of the password reset token."""
        return f"<PasswordResetToken(user_id={self.user_id}, used={self.is_used})>"

    def is_expired(self) -> bool:
        """
        Check if token is expired.

        Returns:
            True if token is expired, False otherwise
        """
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """
        Check if token is valid for use.

        Returns:
            True if token is valid, False otherwise
        """
        return (
            not self.is_used
            and not self.is_expired()
            and self.attempt_count < self.max_attempts
        )

    def use_token(self, ip_address: str | None = None) -> bool:
        """
        Mark token as used.

        Args:
            ip_address: IP address where token was used

        Returns:
            True if token was successfully used, False if invalid
        """
        if not self.is_valid():
            return False

        self.is_used = True
        self.used_at = datetime.utcnow()
        self.used_ip = ip_address
        return True

    def increment_attempt(self) -> None:
        """Increment attempt counter."""
        self.attempt_count += 1

    @classmethod
    def create_token(
        cls,
        user_id: uuid.UUID,
        token_hash: str,
        expires_in_hours: int = 24,
        requested_ip: str | None = None,
        requested_user_agent: str | None = None,
    ) -> "PasswordResetToken":
        """
        Create a new password reset token.

        Args:
            user_id: User ID for the token
            token_hash: Hashed token value
            expires_in_hours: Hours until token expires
            requested_ip: IP address of the request
            requested_user_agent: User agent of the request

        Returns:
            PasswordResetToken instance
        """
        expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)

        return cls(
            user_id=user_id,
            token_hash=token_hash,
            expires_at=expires_at,
            requested_ip=requested_ip,
            requested_user_agent=requested_user_agent,
        )


class MFADevice(Base):
    """Multi-factor authentication devices."""

    __tablename__ = "mfa_devices"

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    device_name = Column(String(100), nullable=False)
    device_type = Column(
        String(50), nullable=False, index=True
    )  # totp, sms, email, hardware

    # Device configuration
    secret_key = Column(String(255), nullable=True)  # Encrypted TOTP secret
    phone_number = Column(String(20), nullable=True)  # For SMS
    email_address = Column(String(255), nullable=True)  # For email codes

    # Device status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_primary = Column(Boolean, default=False, nullable=False)

    # Usage tracking
    last_used_at = Column(DateTime, nullable=True)
    usage_count = Column(Integer, default=0, nullable=False)

    # Verification
    verified_at = Column(DateTime, nullable=True)
    verification_code = Column(String(10), nullable=True)  # Temporary verification code
    verification_expires_at = Column(DateTime, nullable=True)

    # Backup codes (for TOTP devices)
    backup_codes = Column(JSONB, nullable=True)  # Encrypted backup codes
    backup_codes_used = Column(JSONB, nullable=True)  # Used backup codes

    # Device metadata
    device_info = Column(JSONB, nullable=True)  # Additional device information

    # Relationships
    user = relationship("User", backref="mfa_devices")

    def __repr__(self) -> str:
        """String representation of the MFA device."""
        return f"<MFADevice(name={self.device_name}, type={self.device_type}, user_id={self.user_id})>"

    def is_verification_code_valid(self, code: str) -> bool:
        """
        Check if verification code is valid.

        Args:
            code: Verification code to check

        Returns:
            True if code is valid, False otherwise
        """
        if not self.verification_code or not self.verification_expires_at:
            return False

        if datetime.utcnow() > self.verification_expires_at:
            return False

        return self.verification_code == code

    def generate_verification_code(self, expires_in_minutes: int = 10) -> str:
        """
        Generate a new verification code.

        Args:
            expires_in_minutes: Minutes until code expires

        Returns:
            Generated verification code
        """
        import secrets
        import string

        # Generate 6-digit code
        code = "".join(secrets.choice(string.digits) for _ in range(6))

        self.verification_code = code
        self.verification_expires_at = datetime.utcnow() + timedelta(
            minutes=expires_in_minutes
        )

        return code

    def verify_device(self, code: str) -> bool:
        """
        Verify the device with a code.

        Args:
            code: Verification code

        Returns:
            True if verification successful, False otherwise
        """
        if self.is_verification_code_valid(code):
            self.is_verified = True
            self.verified_at = datetime.utcnow()
            self.verification_code = None
            self.verification_expires_at = None
            return True
        return False

    def record_usage(self) -> None:
        """Record device usage."""
        self.last_used_at = datetime.utcnow()
        self.usage_count += 1

    def generate_backup_codes(self, count: int = 10) -> list:
        """
        Generate backup codes for the device.

        Args:
            count: Number of backup codes to generate

        Returns:
            List of backup codes
        """
        import secrets
        import string

        codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = "".join(
                secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8)
            )
            codes.append(code)

        # Store encrypted codes (implementation would encrypt these)
        self.backup_codes = codes
        self.backup_codes_used = []

        return codes

    def use_backup_code(self, code: str) -> bool:
        """
        Use a backup code.

        Args:
            code: Backup code to use

        Returns:
            True if code was valid and used, False otherwise
        """
        if not self.backup_codes or code not in self.backup_codes:
            return False

        if not self.backup_codes_used:
            self.backup_codes_used = []

        if code in self.backup_codes_used:
            return False  # Code already used

        self.backup_codes_used.append(code)
        self.record_usage()
        return True

    def get_remaining_backup_codes(self) -> int:
        """
        Get count of remaining backup codes.

        Returns:
            Number of unused backup codes
        """
        if not self.backup_codes:
            return 0

        used_count = len(self.backup_codes_used) if self.backup_codes_used else 0
        return len(self.backup_codes) - used_count

    def deactivate(self) -> None:
        """Deactivate the MFA device."""
        self.is_active = False
        self.is_primary = False
