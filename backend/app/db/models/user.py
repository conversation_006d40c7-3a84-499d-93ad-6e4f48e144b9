"""User-related database models."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Table,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import Session, relationship

from app.core.permissions import User<PERSON>ole as UserRoleEnum
from app.core.permissions import get_role_permissions
from app.db.base import Base

# Association table for many-to-many relationship between users and roles
user_roles = Table(
    "user_roles",
    Base.metadata,
    Column("user_id", UUID(as_uuid=True), ForeignKey("users.id"), primary_key=True),
    Column(
        "role_id",
        UUID(as_uuid=True),
        Foreign<PERSON>ey("user_roles_table.id"),
        primary_key=True,
    ),
    Column("assigned_at", DateTime, default=datetime.utcnow, nullable=False),
    Column("assigned_by", UUID(as_uuid=True), nullable=True),
    Column("expires_at", DateTime, nullable=True),
    <PERSON>umn("is_active", Bo<PERSON>an, default=True, nullable=False),
)


class UserRole(Base):
    """User roles table for role-based access control."""

    __tablename__ = "user_roles_table"

    name = Column(String(50), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    is_system_role = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Role hierarchy
    parent_role_id = Column(
        UUID(as_uuid=True), ForeignKey("user_roles_table.id"), nullable=True
    )
    parent_role = relationship(
        "UserRole", remote_side="UserRole.id", backref="child_roles"
    )

    # Permissions (stored as JSON for flexibility)
    permissions = Column(JSONB, nullable=True)

    # Role configuration
    max_concurrent_sessions = Column(Integer, default=5, nullable=False)
    session_timeout_minutes = Column(Integer, default=60, nullable=False)
    require_mfa = Column(Boolean, default=False, nullable=False)

    def __repr__(self) -> str:
        """String representation of the role."""
        return f"<UserRole(name={self.name}, display_name={self.display_name})>"

    def get_all_permissions(self) -> list[str]:
        """
        Get all permissions for this role, including inherited permissions.

        Returns:
            List of permission strings
        """
        permissions = set(self.permissions or [])

        # Add permissions from parent roles
        if self.parent_role:
            permissions.update(self.parent_role.get_all_permissions())

        return list(permissions)

    @classmethod
    def create_system_roles(cls, session: Session) -> None:
        """
        Create default system roles.

        Args:
            session: Database session
        """
        system_roles = [
            {
                "name": UserRoleEnum.ADMIN.value,
                "display_name": "Administrator",
                "description": "Full system administrator with all permissions",
                "permissions": [
                    p.value for p in get_role_permissions(UserRoleEnum.ADMIN)
                ],
                "require_mfa": True,
            },
            {
                "name": UserRoleEnum.SOC_OPERATOR.value,
                "display_name": "SOC Operator",
                "description": "SOC operator for monitoring and incident response",
                "permissions": [
                    p.value for p in get_role_permissions(UserRoleEnum.SOC_OPERATOR)
                ],
                "require_mfa": True,
            },
            {
                "name": UserRoleEnum.SECURITY_ARCHITECT.value,
                "display_name": "Security Architect",
                "description": "Security architect for design and risk assessment",
                "permissions": [
                    p.value
                    for p in get_role_permissions(UserRoleEnum.SECURITY_ARCHITECT)
                ],
                "require_mfa": True,
            },
            {
                "name": UserRoleEnum.RED_TEAM_MEMBER.value,
                "display_name": "Red Team Member",
                "description": "Red team member for attack simulation and testing",
                "permissions": [
                    p.value for p in get_role_permissions(UserRoleEnum.RED_TEAM_MEMBER)
                ],
                "require_mfa": True,
            },
            {
                "name": UserRoleEnum.PURPLE_TEAM_MEMBER.value,
                "display_name": "Purple Team Member",
                "description": "Purple team member for collaborative security testing",
                "permissions": [
                    p.value
                    for p in get_role_permissions(UserRoleEnum.PURPLE_TEAM_MEMBER)
                ],
                "require_mfa": True,
            },
            {
                "name": UserRoleEnum.ANALYST.value,
                "display_name": "Security Analyst",
                "description": "Security analyst with read and analysis permissions",
                "permissions": [
                    p.value for p in get_role_permissions(UserRoleEnum.ANALYST)
                ],
                "require_mfa": False,
            },
            {
                "name": UserRoleEnum.VIEWER.value,
                "display_name": "Viewer",
                "description": "Read-only access to dashboards and reports",
                "permissions": [
                    p.value for p in get_role_permissions(UserRoleEnum.VIEWER)
                ],
                "require_mfa": False,
            },
        ]

        for role_data in system_roles:
            existing_role = (
                session.query(cls).filter(cls.name == role_data["name"]).first()
            )
            if not existing_role:
                role = cls(
                    name=role_data["name"],
                    display_name=role_data["display_name"],
                    description=role_data["description"],
                    permissions=role_data["permissions"],
                    is_system_role=True,
                    require_mfa=role_data["require_mfa"],
                )
                session.add(role)

        session.commit()


class User(Base):
    """User model for authentication and authorization."""

    __tablename__ = "users"

    # Basic user information
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    full_name = Column(String(255), nullable=False)

    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False)

    # Account status
    is_locked = Column(Boolean, default=False, nullable=False)
    locked_at = Column(DateTime, nullable=True)
    locked_until = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)

    # Profile information
    phone_number = Column(String(20), nullable=True)
    department = Column(String(100), nullable=True)
    job_title = Column(String(100), nullable=True)
    manager_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Security settings
    require_password_change = Column(Boolean, default=False, nullable=False)
    password_changed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_login_at = Column(DateTime, nullable=True)
    last_login_ip = Column(String(45), nullable=True)  # IPv6 support

    # Multi-factor authentication
    mfa_enabled = Column(Boolean, default=False, nullable=False)
    mfa_secret = Column(String(255), nullable=True)  # Encrypted TOTP secret
    backup_codes = Column(JSONB, nullable=True)  # Encrypted backup codes

    # Preferences and settings
    timezone = Column(String(50), default="UTC", nullable=False)
    language = Column(String(10), default="en", nullable=False)
    theme = Column(String(20), default="dark", nullable=False)
    preferences = Column(JSONB, nullable=True)

    # Azure AD integration
    azure_object_id = Column(String(255), nullable=True, unique=True, index=True)
    azure_tenant_id = Column(String(255), nullable=True)

    # Relationships
    roles = relationship("UserRole", secondary=user_roles, backref="users")
    manager = relationship("User", remote_side="User.id", backref="direct_reports")
    sessions = relationship(
        "UserSession", back_populates="user", cascade="all, delete-orphan"
    )
    api_keys = relationship(
        "UserAPIKey", back_populates="user", cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(username={self.username}, email={self.email})>"

    def check_password(self, password: str) -> bool:
        """
        Check if provided password matches the user's password.

        Args:
            password: Plain text password to check

        Returns:
            True if password matches, False otherwise
        """
        from app.core.security import verify_password

        return verify_password(password, self.hashed_password)

    def set_password(self, password: str) -> None:
        """
        Set user's password.

        Args:
            password: Plain text password to set
        """
        from app.core.security import get_password_hash

        self.hashed_password = get_password_hash(password)
        self.password_changed_at = datetime.utcnow()
        self.require_password_change = False

    def is_password_expired(self, max_age_days: int = 90) -> bool:
        """
        Check if user's password has expired.

        Args:
            max_age_days: Maximum age of password in days

        Returns:
            True if password is expired, False otherwise
        """
        if not self.password_changed_at:
            return True

        expiry_date = self.password_changed_at + timedelta(days=max_age_days)
        return datetime.utcnow() > expiry_date

    def lock_account(self, duration_minutes: int = 30) -> None:
        """
        Lock user account for specified duration.

        Args:
            duration_minutes: Duration to lock account in minutes
        """
        self.is_locked = True
        self.locked_at = datetime.utcnow()
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)

    def unlock_account(self) -> None:
        """Unlock user account."""
        self.is_locked = False
        self.locked_at = None
        self.locked_until = None
        self.failed_login_attempts = 0

    def is_account_locked(self) -> bool:
        """
        Check if account is currently locked.

        Returns:
            True if account is locked, False otherwise
        """
        if not self.is_locked:
            return False

        if self.locked_until and datetime.utcnow() > self.locked_until:
            # Auto-unlock expired locks
            self.unlock_account()
            return False

        return True

    def increment_failed_login(self, max_attempts: int = 5) -> None:
        """
        Increment failed login attempts and lock account if threshold reached.

        Args:
            max_attempts: Maximum failed attempts before locking
        """
        self.failed_login_attempts += 1

        if self.failed_login_attempts >= max_attempts:
            self.lock_account()

    def reset_failed_login_attempts(self) -> None:
        """Reset failed login attempts counter."""
        self.failed_login_attempts = 0

    def update_last_login(self, ip_address: str | None = None) -> None:
        """
        Update last login timestamp and IP.

        Args:
            ip_address: IP address of the login
        """
        self.last_login_at = datetime.utcnow()
        self.last_login_ip = ip_address
        self.reset_failed_login_attempts()

    def get_permissions(self) -> list[str]:
        """
        Get all permissions for this user based on their roles.

        Returns:
            List of permission strings
        """
        permissions = set()
        for role in self.roles:
            if role.is_active:
                permissions.update(role.get_all_permissions())
        return list(permissions)

    def has_permission(self, permission: str) -> bool:
        """
        Check if user has a specific permission.

        Args:
            permission: Permission to check

        Returns:
            True if user has permission, False otherwise
        """
        return permission in self.get_permissions()

    def has_role(self, role_name: str) -> bool:
        """
        Check if user has a specific role.

        Args:
            role_name: Role name to check

        Returns:
            True if user has role, False otherwise
        """
        return any(role.name == role_name and role.is_active for role in self.roles)

    def is_admin(self) -> bool:
        """
        Check if user is an administrator.

        Returns:
            True if user is admin, False otherwise
        """
        return self.has_role(UserRoleEnum.ADMIN.value)

    def to_dict(self, include_sensitive: bool = False) -> dict[str, Any]:
        """
        Convert user to dictionary.

        Args:
            include_sensitive: Whether to include sensitive information

        Returns:
            Dictionary representation of user
        """
        data = super().to_dict()

        # Remove sensitive fields unless explicitly requested
        if not include_sensitive:
            sensitive_fields = [
                "hashed_password",
                "mfa_secret",
                "backup_codes",
                "azure_object_id",
                "azure_tenant_id",
            ]
            for field in sensitive_fields:
                data.pop(field, None)

        # Add computed fields
        data["roles"] = [role.name for role in self.roles if role.is_active]
        data["permissions"] = self.get_permissions()
        data["is_locked"] = self.is_account_locked()

        return data


class UserSession(Base):
    """User session tracking for security and audit purposes."""

    __tablename__ = "user_sessions"

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=True, index=True)

    # Session information
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    device_fingerprint = Column(String(255), nullable=True)

    # Session status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False, index=True)
    last_activity_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Security flags
    is_suspicious = Column(Boolean, default=False, nullable=False)
    requires_verification = Column(Boolean, default=False, nullable=False)

    # Logout information
    logged_out_at = Column(DateTime, nullable=True)
    logout_reason = Column(
        String(100), nullable=True
    )  # manual, timeout, security, admin

    # Relationships
    user = relationship("User", back_populates="sessions")

    def __repr__(self) -> str:
        """String representation of the session."""
        return f"<UserSession(user_id={self.user_id}, active={self.is_active})>"

    def is_expired(self) -> bool:
        """
        Check if session is expired.

        Returns:
            True if session is expired, False otherwise
        """
        return datetime.utcnow() > self.expires_at

    def extend_session(self, minutes: int = 60) -> None:
        """
        Extend session expiration.

        Args:
            minutes: Minutes to extend session
        """
        self.expires_at = datetime.utcnow() + timedelta(minutes=minutes)
        self.last_activity_at = datetime.utcnow()

    def terminate_session(self, reason: str = "manual") -> None:
        """
        Terminate the session.

        Args:
            reason: Reason for termination
        """
        self.is_active = False
        self.logged_out_at = datetime.utcnow()
        self.logout_reason = reason


class UserAPIKey(Base):
    """API keys for programmatic access."""

    __tablename__ = "user_api_keys"

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # API key information
    key_hash = Column(String(255), nullable=False, unique=True, index=True)
    key_prefix = Column(
        String(10), nullable=False, index=True
    )  # First few chars for identification

    # Permissions and restrictions
    scopes = Column(JSONB, nullable=True)  # List of allowed scopes/permissions
    allowed_ips = Column(JSONB, nullable=True)  # List of allowed IP addresses
    rate_limit = Column(Integer, nullable=True)  # Requests per minute

    # Status and expiration
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=True, index=True)

    # Usage tracking
    last_used_at = Column(DateTime, nullable=True)
    last_used_ip = Column(String(45), nullable=True)
    usage_count = Column(Integer, default=0, nullable=False)

    # Relationships
    user = relationship("User", back_populates="api_keys")

    def __repr__(self) -> str:
        """String representation of the API key."""
        return f"<UserAPIKey(name={self.name}, user_id={self.user_id})>"

    def is_expired(self) -> bool:
        """
        Check if API key is expired.

        Returns:
            True if API key is expired, False otherwise
        """
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """
        Check if API key is valid for use.

        Returns:
            True if API key is valid, False otherwise
        """
        return self.is_active and not self.is_expired()

    def record_usage(self, ip_address: str | None = None) -> None:
        """
        Record API key usage.

        Args:
            ip_address: IP address of the request
        """
        self.last_used_at = datetime.utcnow()
        self.last_used_ip = ip_address
        self.usage_count += 1

    def revoke(self) -> None:
        """Revoke the API key."""
        self.is_active = False
