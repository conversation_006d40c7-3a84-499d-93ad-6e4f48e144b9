"""Enhanced robust asset models with soft-delete, audit trails, and enterprise features."""

import enum
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import (
    <PERSON>olean,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    Index,
    event,
    CheckConstraint,
)
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID, INET
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

from app.db.base import Base


class AuditAction(str, enum.Enum):
    """Audit action types."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    SOFT_DELETE = "soft_delete"
    RESTORE = "restore"
    ARCHIVE = "archive"
    MERGE = "merge"
    SPLIT = "split"


class DataRetentionPolicy(str, enum.Enum):
    """Data retention policy types."""
    IMMEDIATE = "immediate"  # Delete immediately
    SHORT_TERM = "short_term"  # 30 days
    MEDIUM_TERM = "medium_term"  # 90 days
    LONG_TERM = "long_term"  # 1 year
    PERMANENT = "permanent"  # Keep forever
    COMPLIANCE = "compliance"  # Based on compliance requirements


class SoftDeleteMixin:
    """Mixin for soft delete functionality."""
    
    # Soft delete fields
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime, nullable=True, index=True)
    deleted_by = Column(String(255), nullable=True)
    deletion_reason = Column(String(500), nullable=True)
    
    # Data retention
    retention_policy = Column(Enum(DataRetentionPolicy), default=DataRetentionPolicy.LONG_TERM, nullable=False)
    purge_after = Column(DateTime, nullable=True, index=True)  # When to permanently delete
    
    def soft_delete(self, deleted_by: str = None, reason: str = None):
        """Perform soft delete."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.deleted_by = deleted_by
        self.deletion_reason = reason
        
        # Calculate purge date based on retention policy
        if self.retention_policy == DataRetentionPolicy.IMMEDIATE:
            self.purge_after = datetime.utcnow()
        elif self.retention_policy == DataRetentionPolicy.SHORT_TERM:
            self.purge_after = datetime.utcnow() + timedelta(days=30)
        elif self.retention_policy == DataRetentionPolicy.MEDIUM_TERM:
            self.purge_after = datetime.utcnow() + timedelta(days=90)
        elif self.retention_policy == DataRetentionPolicy.LONG_TERM:
            self.purge_after = datetime.utcnow() + timedelta(days=365)
        # PERMANENT and COMPLIANCE don't set purge_after
    
    def restore(self, restored_by: str = None):
        """Restore from soft delete."""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        self.deletion_reason = None
        self.purge_after = None
    
    @property
    def is_active(self) -> bool:
        """Check if record is active (not soft deleted)."""
        return not self.is_deleted


class AuditMixin:
    """Mixin for audit trail functionality."""
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    created_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, index=True)
    updated_by = Column(String(255), nullable=True)
    
    # Version control
    version = Column(Integer, default=1, nullable=False)
    change_count = Column(Integer, default=0, nullable=False)
    
    # Data integrity
    checksum = Column(String(64), nullable=True)  # SHA256 of critical fields
    last_verified_at = Column(DateTime, nullable=True)
    verification_status = Column(String(50), default="unverified", nullable=False)


class AssetAuditLog(Base):
    """Comprehensive audit log for all asset changes."""
    
    __tablename__ = "asset_audit_log"
    
    # Audit record identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    asset_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # No FK to allow orphaned records
    
    # Audit metadata
    action = Column(Enum(AuditAction), nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    user_id = Column(String(255), nullable=True, index=True)
    session_id = Column(String(255), nullable=True)
    ip_address = Column(INET, nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # Change details
    table_name = Column(String(100), nullable=False, index=True)
    record_id = Column(String(100), nullable=False)
    field_name = Column(String(100), nullable=True, index=True)
    old_value = Column(JSONB, nullable=True)
    new_value = Column(JSONB, nullable=True)
    
    # Context and metadata
    change_reason = Column(String(500), nullable=True)
    change_source = Column(String(100), nullable=True)  # api, ui, system, import
    correlation_id = Column(String(100), nullable=True, index=True)  # Group related changes
    
    # Data snapshot (for major changes)
    before_snapshot = Column(JSONB, nullable=True)
    after_snapshot = Column(JSONB, nullable=True)
    
    # Compliance and retention
    retention_policy = Column(Enum(DataRetentionPolicy), default=DataRetentionPolicy.COMPLIANCE, nullable=False)
    is_sensitive = Column(Boolean, default=False, nullable=False)
    compliance_tags = Column(JSONB, nullable=True)  # GDPR, SOX, etc.
    
    __table_args__ = (
        Index("idx_audit_asset_timestamp", "asset_id", "timestamp"),
        Index("idx_audit_action_timestamp", "action", "timestamp"),
        Index("idx_audit_user_timestamp", "user_id", "timestamp"),
        Index("idx_audit_correlation", "correlation_id", "timestamp"),
        Index("idx_audit_table_record", "table_name", "record_id"),
    )


class AssetVersion(Base):
    """Asset version history for point-in-time recovery."""
    
    __tablename__ = "asset_versions"
    
    # Version identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    asset_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    version_number = Column(Integer, nullable=False)
    
    # Version metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    created_by = Column(String(255), nullable=True)
    change_reason = Column(String(500), nullable=True)
    change_type = Column(String(50), nullable=False)  # discovery, manual, import, merge
    
    # Asset data snapshot
    asset_data = Column(JSONB, nullable=False)  # Complete asset state
    configuration_data = Column(JSONB, nullable=True)  # Configuration snapshot
    relationships_data = Column(JSONB, nullable=True)  # Relationships snapshot
    
    # Version properties
    is_major_version = Column(Boolean, default=False, nullable=False)
    is_baseline = Column(Boolean, default=False, nullable=False)
    tags = Column(JSONB, nullable=True)  # Version tags
    
    # Data integrity
    checksum = Column(String(64), nullable=False)  # SHA256 of asset_data
    compression_type = Column(String(20), nullable=True)  # gzip, lz4, etc.
    
    __table_args__ = (
        Index("idx_version_asset_number", "asset_id", "version_number"),
        Index("idx_version_created", "created_at"),
        Index("idx_version_baseline", "is_baseline", "created_at"),
        UniqueConstraint("asset_id", "version_number", name="uq_asset_version"),
    )


class AssetLock(Base):
    """Asset locking mechanism for concurrent access control."""
    
    __tablename__ = "asset_locks"
    
    # Lock identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    asset_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Lock details
    lock_type = Column(String(50), nullable=False)  # read, write, exclusive, discovery
    locked_by = Column(String(255), nullable=False)
    locked_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False, index=True)
    
    # Lock metadata
    session_id = Column(String(255), nullable=True)
    operation = Column(String(100), nullable=True)  # discovery, update, delete
    reason = Column(String(500), nullable=True)
    
    # Lock status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    released_at = Column(DateTime, nullable=True)
    released_by = Column(String(255), nullable=True)
    
    __table_args__ = (
        Index("idx_lock_asset_active", "asset_id", "is_active"),
        Index("idx_lock_expires", "expires_at", "is_active"),
        Index("idx_lock_type_active", "lock_type", "is_active"),
    )


class AssetValidation(Base):
    """Asset validation and data quality tracking."""
    
    __tablename__ = "asset_validations"
    
    # Validation identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    
    # Validation details
    validation_type = Column(String(50), nullable=False, index=True)  # schema, business, security, compliance
    validator_name = Column(String(100), nullable=False)
    validation_rule = Column(String(500), nullable=False)
    
    # Validation results
    status = Column(String(50), nullable=False, index=True)  # passed, failed, warning, skipped
    score = Column(Float, nullable=True)  # 0.0 to 100.0
    message = Column(Text, nullable=True)
    details = Column(JSONB, nullable=True)
    
    # Validation metadata
    validated_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    validation_duration_ms = Column(Integer, nullable=True)
    
    # Remediation
    is_blocking = Column(Boolean, default=False, nullable=False)
    remediation_suggestion = Column(Text, nullable=True)
    remediation_priority = Column(String(20), nullable=True)  # low, medium, high, critical
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_validation_asset_type", "asset_id", "validation_type"),
        Index("idx_validation_status_priority", "status", "remediation_priority"),
        Index("idx_validation_timestamp", "validated_at"),
    )


class AssetBackup(Base):
    """Asset backup and recovery tracking."""
    
    __tablename__ = "asset_backups"
    
    # Backup identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    asset_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Backup details
    backup_type = Column(String(50), nullable=False)  # full, incremental, differential
    backup_method = Column(String(50), nullable=False)  # export, snapshot, clone
    backup_location = Column(String(500), nullable=False)
    
    # Backup metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    created_by = Column(String(255), nullable=True)
    size_bytes = Column(Integer, nullable=True)
    compression_ratio = Column(Float, nullable=True)
    
    # Backup status
    status = Column(String(50), nullable=False, index=True)  # creating, completed, failed, expired
    completion_percentage = Column(Float, default=0.0, nullable=False)
    error_message = Column(Text, nullable=True)
    
    # Recovery information
    is_recoverable = Column(Boolean, default=True, nullable=False)
    recovery_tested_at = Column(DateTime, nullable=True)
    recovery_test_status = Column(String(50), nullable=True)
    
    # Retention
    expires_at = Column(DateTime, nullable=True, index=True)
    retention_policy = Column(Enum(DataRetentionPolicy), default=DataRetentionPolicy.LONG_TERM, nullable=False)
    
    __table_args__ = (
        Index("idx_backup_asset_created", "asset_id", "created_at"),
        Index("idx_backup_status_expires", "status", "expires_at"),
        Index("idx_backup_type_created", "backup_type", "created_at"),
    )


# Enhanced Asset model with robustness features
class RobustAsset(Base, SoftDeleteMixin, AuditMixin):
    """Enhanced asset model with soft-delete, audit trails, and enterprise features."""
    
    __tablename__ = "assets_robust"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    
    # Asset classification (using existing enums)
    asset_type = Column(String(50), nullable=False, index=True)  # Will use AssetType enum
    provider = Column(String(50), nullable=False, index=True)  # Will use AssetProvider enum
    status = Column(String(50), nullable=False, index=True)  # Will use AssetStatus enum
    
    # Provider-specific identification
    provider_id = Column(String(255), nullable=True, index=True)
    provider_region = Column(String(100), nullable=True, index=True)
    provider_account_id = Column(String(100), nullable=True, index=True)
    provider_resource_group = Column(String(255), nullable=True)
    
    # Network and connectivity
    ip_addresses = Column(JSONB, nullable=True)
    dns_names = Column(JSONB, nullable=True)
    ports = Column(JSONB, nullable=True)
    protocols = Column(JSONB, nullable=True)
    
    # Asset metadata and context
    environment = Column(String(100), nullable=True, index=True)
    owner = Column(String(255), nullable=True, index=True)
    team = Column(String(255), nullable=True, index=True)
    cost_center = Column(String(100), nullable=True, index=True)
    business_unit = Column(String(255), nullable=True, index=True)
    
    # Security and risk assessment
    risk_score = Column(Integer, nullable=True, index=True)
    risk_level = Column(String(20), nullable=True, index=True)  # Will use RiskLevel enum
    security_score = Column(Float, nullable=True)
    compliance_score = Column(Float, nullable=True)
    
    # Discovery and tracking
    discovery_source = Column(String(50), nullable=False, index=True)  # Will use DiscoverySource enum
    discovery_job_id = Column(UUID(as_uuid=True), ForeignKey("discovery_jobs.id"), nullable=True, index=True)
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    last_seen = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # Configuration and properties
    configuration = Column(JSONB, nullable=True)
    properties = Column(JSONB, nullable=True)
    tags = Column(JSONB, nullable=True)
    
    # Health and monitoring
    health_status = Column(String(50), default="unknown", nullable=False, index=True)
    health_score = Column(Float, nullable=True)
    last_health_check = Column(DateTime, nullable=True)
    monitoring_enabled = Column(Boolean, default=False, nullable=False)
    
    # Data classification and privacy
    data_classification = Column(String(50), nullable=True, index=True)  # public, internal, confidential, restricted
    contains_pii = Column(Boolean, default=False, nullable=False, index=True)
    gdpr_applicable = Column(Boolean, default=False, nullable=False)
    
    # Business context
    business_criticality = Column(String(20), nullable=True, index=True)  # low, medium, high, critical
    revenue_impact = Column(String(20), nullable=True)  # none, low, medium, high
    customer_impact = Column(String(20), nullable=True)  # none, low, medium, high
    
    # Relationships
    discovery_job = relationship("DiscoveryJob", back_populates="assets")
    asset_tags = relationship("AssetTag", back_populates="asset", cascade="all, delete-orphan")
    validations = relationship("AssetValidation", back_populates="asset", cascade="all, delete-orphan")
    
    # Constraints and indexes
    __table_args__ = (
        # Performance indexes
        Index("idx_robust_asset_name_type", "name", "asset_type"),
        Index("idx_robust_asset_provider_type", "provider", "asset_type"),
        Index("idx_robust_asset_discovery", "discovery_source", "discovered_at"),
        Index("idx_robust_asset_risk", "risk_level", "risk_score"),
        Index("idx_robust_asset_status_lastseen", "status", "last_seen"),
        Index("idx_robust_asset_health", "health_status", "last_health_check"),
        Index("idx_robust_asset_business", "business_criticality", "revenue_impact"),
        Index("idx_robust_asset_compliance", "compliance_score", "gdpr_applicable"),
        Index("idx_robust_asset_pii", "contains_pii", "data_classification"),
        
        # Soft delete indexes
        Index("idx_robust_asset_deleted", "is_deleted", "deleted_at"),
        Index("idx_robust_asset_purge", "purge_after", "is_deleted"),
        
        # Audit indexes
        Index("idx_robust_asset_created", "created_at", "created_by"),
        Index("idx_robust_asset_updated", "updated_at", "updated_by"),
        Index("idx_robust_asset_version", "version", "change_count"),
        
        # Unique constraints
        UniqueConstraint("provider", "provider_id", name="uq_robust_asset_provider_id"),
        
        # Check constraints
        CheckConstraint("risk_score >= 0 AND risk_score <= 100", name="chk_risk_score_range"),
        CheckConstraint("security_score >= 0.0 AND security_score <= 100.0", name="chk_security_score_range"),
        CheckConstraint("compliance_score >= 0.0 AND compliance_score <= 100.0", name="chk_compliance_score_range"),
        CheckConstraint("health_score >= 0.0 AND health_score <= 100.0", name="chk_health_score_range"),
        CheckConstraint("version >= 1", name="chk_version_positive"),
        CheckConstraint("change_count >= 0", name="chk_change_count_non_negative"),
    )
    
    def __repr__(self) -> str:
        """String representation of the robust asset."""
        return f"<RobustAsset(name={self.name}, type={self.asset_type}, provider={self.provider}, deleted={self.is_deleted})>"
