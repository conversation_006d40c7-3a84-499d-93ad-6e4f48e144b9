"""Extended asset models for comprehensive metadata coverage."""

import enum
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    Index,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class VulnerabilitySeverity(str, enum.Enum):
    """Vulnerability severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class ComplianceFramework(str, enum.Enum):
    """Compliance framework types."""
    SOC2 = "soc2"
    ISO27001 = "iso27001"
    PCI_DSS = "pci_dss"
    HIPAA = "hipaa"
    GDPR = "gdpr"
    NIST = "nist"
    CIS = "cis"
    CUSTOM = "custom"


class AssetVulnerability(Base):
    """Asset vulnerability tracking model."""
    
    __tablename__ = "asset_vulnerabilities"
    
    # Basic vulnerability information
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    cve_id = Column(String(20), nullable=True, index=True)  # CVE-2023-1234
    vulnerability_id = Column(String(100), nullable=False, index=True)  # Internal or scanner ID
    
    # Vulnerability details
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    severity = Column(Enum(VulnerabilitySeverity), nullable=False, index=True)
    cvss_score = Column(Float, nullable=True)  # 0.0 to 10.0
    cvss_vector = Column(String(200), nullable=True)
    
    # Discovery and tracking
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    last_seen = Column(DateTime, default=datetime.utcnow, nullable=False)
    scanner_name = Column(String(100), nullable=True)
    scan_id = Column(String(100), nullable=True)
    
    # Status and remediation
    status = Column(String(50), default="open", nullable=False, index=True)  # open, fixed, mitigated, false_positive
    remediation_status = Column(String(50), default="pending", nullable=False)
    remediation_effort = Column(String(20), nullable=True)  # low, medium, high
    remediation_priority = Column(Integer, nullable=True)  # 1-5
    
    # Additional metadata
    affected_component = Column(String(200), nullable=True)
    exploit_available = Column(Boolean, default=False, nullable=False)
    patch_available = Column(Boolean, default=False, nullable=False)
    patch_complexity = Column(String(20), nullable=True)  # low, medium, high
    
    # Evidence and proof
    evidence = Column(JSONB, nullable=True)  # Scanner output, proof of concept
    references = Column(JSONB, nullable=True)  # URLs, advisories
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_vuln_asset_severity", "asset_id", "severity"),
        Index("idx_vuln_cve_status", "cve_id", "status"),
        Index("idx_vuln_discovery", "discovered_at", "scanner_name"),
        UniqueConstraint("asset_id", "vulnerability_id", name="uq_asset_vulnerability"),
    )


class AssetCompliance(Base):
    """Asset compliance tracking model."""
    
    __tablename__ = "asset_compliance"
    
    # Basic compliance information
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    framework = Column(Enum(ComplianceFramework), nullable=False, index=True)
    control_id = Column(String(100), nullable=False, index=True)  # SOC2-CC6.1, ISO-A.12.1.1
    
    # Compliance details
    control_name = Column(String(500), nullable=False)
    control_description = Column(Text, nullable=True)
    requirement = Column(Text, nullable=True)
    
    # Status and assessment
    status = Column(String(50), nullable=False, index=True)  # compliant, non_compliant, not_applicable, unknown
    compliance_score = Column(Float, nullable=True)  # 0.0 to 100.0
    last_assessment = Column(DateTime, nullable=True, index=True)
    next_assessment = Column(DateTime, nullable=True, index=True)
    
    # Assessment details
    assessor = Column(String(255), nullable=True)
    assessment_method = Column(String(100), nullable=True)  # automated, manual, hybrid
    evidence = Column(JSONB, nullable=True)
    findings = Column(JSONB, nullable=True)
    
    # Remediation
    remediation_plan = Column(Text, nullable=True)
    remediation_deadline = Column(DateTime, nullable=True)
    remediation_owner = Column(String(255), nullable=True)
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_compliance_asset_framework", "asset_id", "framework"),
        Index("idx_compliance_status_assessment", "status", "last_assessment"),
        Index("idx_compliance_control", "framework", "control_id"),
        UniqueConstraint("asset_id", "framework", "control_id", name="uq_asset_compliance"),
    )


class AssetMetrics(Base):
    """Asset performance and health metrics model."""
    
    __tablename__ = "asset_metrics"
    
    # Basic metric information
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_type = Column(String(50), nullable=False)  # gauge, counter, histogram
    
    # Metric values
    value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=True)  # bytes, percent, count, seconds
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # Metric metadata
    source = Column(String(100), nullable=True)  # monitoring system source
    labels = Column(JSONB, nullable=True)  # Additional metric labels
    
    # Thresholds and alerting
    warning_threshold = Column(Float, nullable=True)
    critical_threshold = Column(Float, nullable=True)
    is_alerting = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_metrics_asset_name", "asset_id", "metric_name"),
        Index("idx_metrics_timestamp", "timestamp"),
        Index("idx_metrics_alerting", "is_alerting", "timestamp"),
    )


class AssetDependency(Base):
    """Asset dependency tracking for detailed relationship mapping."""
    
    __tablename__ = "asset_dependencies"
    
    # Dependency relationship
    dependent_asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    dependency_asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    
    # Dependency details
    dependency_type = Column(String(50), nullable=False, index=True)  # runtime, build, data, network
    criticality = Column(String(20), nullable=False)  # critical, high, medium, low
    
    # Network/communication details
    protocol = Column(String(20), nullable=True)
    port = Column(Integer, nullable=True)
    endpoint = Column(String(500), nullable=True)
    
    # Dependency metadata
    description = Column(Text, nullable=True)
    version_requirement = Column(String(100), nullable=True)
    is_optional = Column(Boolean, default=False, nullable=False)
    
    # Discovery and validation
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_validated = Column(DateTime, nullable=True)
    validation_method = Column(String(100), nullable=True)
    is_healthy = Column(Boolean, default=True, nullable=False)
    
    # Impact analysis
    failure_impact = Column(String(20), nullable=True)  # service_down, degraded, none
    recovery_time = Column(Integer, nullable=True)  # seconds
    
    # Relationships
    dependent_asset = relationship("Asset", foreign_keys=[dependent_asset_id])
    dependency_asset = relationship("Asset", foreign_keys=[dependency_asset_id])
    
    __table_args__ = (
        Index("idx_dependency_dependent", "dependent_asset_id", "criticality"),
        Index("idx_dependency_target", "dependency_asset_id", "dependency_type"),
        Index("idx_dependency_health", "is_healthy", "last_validated"),
        UniqueConstraint(
            "dependent_asset_id", 
            "dependency_asset_id", 
            "dependency_type",
            "protocol",
            "port",
            name="uq_asset_dependency"
        ),
    )


class AssetConfiguration(Base):
    """Asset configuration tracking for change management."""
    
    __tablename__ = "asset_configurations"
    
    # Configuration tracking
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    configuration_key = Column(String(200), nullable=False, index=True)
    configuration_value = Column(Text, nullable=True)
    
    # Configuration metadata
    configuration_type = Column(String(50), nullable=False)  # system, security, network, application
    is_sensitive = Column(Boolean, default=False, nullable=False)
    is_encrypted = Column(Boolean, default=False, nullable=False)
    
    # Change tracking
    previous_value = Column(Text, nullable=True)
    changed_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    changed_by = Column(String(255), nullable=True)
    change_reason = Column(String(500), nullable=True)
    
    # Validation and compliance
    is_compliant = Column(Boolean, default=True, nullable=False)
    compliance_rules = Column(JSONB, nullable=True)
    validation_status = Column(String(50), default="unknown", nullable=False)
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_config_asset_key", "asset_id", "configuration_key"),
        Index("idx_config_type_sensitive", "configuration_type", "is_sensitive"),
        Index("idx_config_changes", "changed_at", "changed_by"),
        UniqueConstraint("asset_id", "configuration_key", name="uq_asset_configuration"),
    )


class AssetCertificate(Base):
    """Asset SSL/TLS certificate tracking."""
    
    __tablename__ = "asset_certificates"
    
    # Certificate identification
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    certificate_hash = Column(String(64), nullable=False, index=True)  # SHA256 fingerprint
    
    # Certificate details
    subject = Column(String(500), nullable=False)
    issuer = Column(String(500), nullable=False)
    serial_number = Column(String(100), nullable=False)
    
    # Validity period
    valid_from = Column(DateTime, nullable=False, index=True)
    valid_to = Column(DateTime, nullable=False, index=True)
    is_expired = Column(Boolean, default=False, nullable=False, index=True)
    days_until_expiry = Column(Integer, nullable=True)
    
    # Certificate properties
    key_algorithm = Column(String(50), nullable=True)
    key_size = Column(Integer, nullable=True)
    signature_algorithm = Column(String(50), nullable=True)
    
    # Subject Alternative Names
    san_dns_names = Column(JSONB, nullable=True)
    san_ip_addresses = Column(JSONB, nullable=True)
    
    # Certificate chain
    is_self_signed = Column(Boolean, default=False, nullable=False)
    chain_length = Column(Integer, nullable=True)
    root_ca = Column(String(500), nullable=True)
    
    # Discovery and validation
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_validated = Column(DateTime, nullable=True)
    validation_status = Column(String(50), default="unknown", nullable=False)
    
    # Security assessment
    is_trusted = Column(Boolean, default=True, nullable=False)
    trust_issues = Column(JSONB, nullable=True)
    security_score = Column(Float, nullable=True)  # 0.0 to 100.0
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_cert_asset_expiry", "asset_id", "valid_to"),
        Index("idx_cert_expiry_status", "is_expired", "days_until_expiry"),
        Index("idx_cert_validation", "validation_status", "last_validated"),
        UniqueConstraint("asset_id", "certificate_hash", name="uq_asset_certificate"),
    )


class AssetNetworkInterface(Base):
    """Asset network interface details."""
    
    __tablename__ = "asset_network_interfaces"
    
    # Interface identification
    asset_id = Column(UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False, index=True)
    interface_name = Column(String(100), nullable=False)
    interface_type = Column(String(50), nullable=False)  # ethernet, wifi, loopback, vpn
    
    # Network configuration
    ip_address = Column(INET, nullable=True, index=True)
    subnet_mask = Column(String(20), nullable=True)
    gateway = Column(INET, nullable=True)
    dns_servers = Column(JSONB, nullable=True)
    
    # Interface properties
    mac_address = Column(String(17), nullable=True, index=True)
    mtu = Column(Integer, nullable=True)
    speed = Column(String(20), nullable=True)  # 1Gbps, 10Gbps
    duplex = Column(String(20), nullable=True)  # full, half
    
    # Status and monitoring
    is_up = Column(Boolean, default=True, nullable=False)
    is_primary = Column(Boolean, default=False, nullable=False)
    last_seen = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Traffic statistics
    bytes_sent = Column(Integer, nullable=True)
    bytes_received = Column(Integer, nullable=True)
    packets_sent = Column(Integer, nullable=True)
    packets_received = Column(Integer, nullable=True)
    errors = Column(Integer, nullable=True)
    
    # Security
    is_public = Column(Boolean, default=False, nullable=False, index=True)
    security_groups = Column(JSONB, nullable=True)
    firewall_rules = Column(JSONB, nullable=True)
    
    # Relationships
    asset = relationship("Asset")
    
    __table_args__ = (
        Index("idx_netif_asset_name", "asset_id", "interface_name"),
        Index("idx_netif_ip_public", "ip_address", "is_public"),
        Index("idx_netif_mac", "mac_address"),
        UniqueConstraint("asset_id", "interface_name", name="uq_asset_network_interface"),
    )
