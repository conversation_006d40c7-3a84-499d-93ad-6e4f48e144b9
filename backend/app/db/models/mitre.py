"""Enhanced MITRE ATT&CK database models with comprehensive schema support and soft delete."""

import enum
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    Index,
    CheckConstraint,
    Table,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class MitreDomain(str, enum.Enum):
    """MITRE ATT&CK domains."""
    ENTERPRISE = "enterprise"
    MOBILE = "mobile"
    ICS = "ics"


class MitreEntityStatus(str, enum.Enum):
    """MITRE entity status."""
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    REVOKED = "revoked"


class MitreDataSourceType(str, enum.Enum):
    """MITRE data source types."""
    OFFICIAL = "official"
    COMMUNITY = "community"
    CUSTOM = "custom"


# Association tables for many-to-many relationships
technique_tactic_association = Table(
    'mitre_technique_tactic_associations',
    Base.metadata,
    Column('technique_id', UUID(as_uuid=True), ForeignKey('mitre_techniques.id'), primary_key=True),
    Column('tactic_id', UUID(as_uuid=True), ForeignKey('mitre_tactics.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now()),
    Column('association_metadata', JSONB, nullable=True),
    extend_existing=True
)

technique_mitigation_association = Table(
    'mitre_technique_mitigation_associations',
    Base.metadata,
    Column('technique_id', UUID(as_uuid=True), ForeignKey('mitre_techniques.id'), primary_key=True),
    Column('mitigation_id', UUID(as_uuid=True), ForeignKey('mitre_mitigations.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now()),
    Column('effectiveness', String(20), nullable=True),  # High, Medium, Low
    Column('association_metadata', JSONB, nullable=True),
    extend_existing=True
)

group_technique_association = Table(
    'mitre_group_technique_associations',
    Base.metadata,
    Column('group_id', UUID(as_uuid=True), ForeignKey('mitre_groups.id'), primary_key=True),
    Column('technique_id', UUID(as_uuid=True), ForeignKey('mitre_techniques.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now()),
    Column('procedure_examples', JSONB, nullable=True),
    Column('association_metadata', JSONB, nullable=True),
    extend_existing=True
)

group_software_association = Table(
    'mitre_group_software_associations',
    Base.metadata,
    Column('group_id', UUID(as_uuid=True), ForeignKey('mitre_groups.id'), primary_key=True),
    Column('software_id', UUID(as_uuid=True), ForeignKey('mitre_software.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now()),
    Column('usage_examples', JSONB, nullable=True),
    Column('association_metadata', JSONB, nullable=True),
    extend_existing=True
)


class MitreTechnique(Base):
    """MITRE ATT&CK Technique model with soft delete and audit capabilities."""

    __tablename__ = "mitre_techniques"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    technique_id = Column(String(20), nullable=False, unique=True, index=True)  # T1234, T1234.001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)
    
    # Sub-technique support
    is_subtechnique = Column(Boolean, nullable=False, default=False, index=True)
    parent_technique_id = Column(String(20), nullable=True, index=True)  # Parent technique ID for sub-techniques
    
    # Technical details
    platforms = Column(JSONB, nullable=True)  # List of platforms
    data_sources = Column(JSONB, nullable=True)  # List of data sources
    system_requirements = Column(JSONB, nullable=True)
    permissions_required = Column(JSONB, nullable=True)
    effective_permissions = Column(JSONB, nullable=True)
    defense_bypassed = Column(JSONB, nullable=True)
    supports_remote = Column(Boolean, nullable=True)
    impact_type = Column(JSONB, nullable=True)
    
    # External mappings
    capec_id = Column(String(20), nullable=True)
    mtc_id = Column(String(20), nullable=True)
    
    # Kill chain phases
    kill_chain_phases = Column(JSONB, nullable=True)
    
    # Detection and mitigation
    detection_methods = Column(JSONB, nullable=True)
    
    # References and metadata
    references = Column(JSONB, nullable=True)
    contributors = Column(JSONB, nullable=True)
    
    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")

    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)
    
    # Performance optimization
    search_vector = Column(Text, nullable=True)  # For full-text search
    
    # Relationships
    tactics = relationship("MitreTactic", secondary=technique_tactic_association, back_populates="techniques")
    mitigations = relationship("MitreMitigation", secondary=technique_mitigation_association, back_populates="techniques")
    groups = relationship("MitreGroup", secondary=group_technique_association, back_populates="techniques")
    
    # Constraints and indexes
    __table_args__ = (
        Index('idx_technique_domain_status', 'domain', 'status'),
        Index('idx_technique_parent', 'parent_technique_id'),
        Index('idx_technique_search', 'name', 'description'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_technique_version_format'),
        {'extend_existing': True}
    )
    
    def __repr__(self):
        return f"<MitreTechnique(id='{self.technique_id}', name='{self.name}')>"


class MitreTactic(Base):
    """MITRE ATT&CK Tactic model with soft delete and audit capabilities."""

    __tablename__ = "mitre_tactics"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tactic_id = Column(String(20), nullable=False, unique=True, index=True)  # TA0001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)
    
    # Tactical information
    short_name = Column(String(100), nullable=True)
    x_mitre_shortname = Column(String(100), nullable=True)
    
    # References and metadata
    references = Column(JSONB, nullable=True)
    
    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")

    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)
    
    # Relationships
    techniques = relationship("MitreTechnique", secondary=technique_tactic_association, back_populates="tactics")
    
    # Constraints and indexes
    __table_args__ = (
        Index('idx_tactic_domain_status', 'domain', 'status'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_tactic_version_format'),
        {'extend_existing': True}
    )
    
    def __repr__(self):
        return f"<MitreTactic(id='{self.tactic_id}', name='{self.name}')>"


class MitreGroup(Base):
    """MITRE ATT&CK Group (Threat Actor) model with soft delete and audit capabilities."""

    __tablename__ = "mitre_groups"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    group_id = Column(String(20), nullable=False, unique=True, index=True)  # G0001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)
    
    # Group details
    aliases = Column(JSONB, nullable=True)  # List of aliases
    associated_groups = Column(JSONB, nullable=True)  # Related groups
    
    # Attribution and context
    country = Column(String(100), nullable=True)
    motivation = Column(JSONB, nullable=True)  # List of motivations
    sophistication = Column(String(50), nullable=True)  # High, Medium, Low
    
    # Activity tracking
    first_seen = Column(DateTime, nullable=True)
    last_seen = Column(DateTime, nullable=True)
    
    # References and metadata
    references = Column(JSONB, nullable=True)
    
    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")

    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)
    
    # Performance optimization
    search_vector = Column(Text, nullable=True)  # For full-text search
    
    # Relationships
    techniques = relationship("MitreTechnique", secondary=group_technique_association, back_populates="groups")
    software = relationship("MitreSoftware", secondary=group_software_association, back_populates="groups")
    
    # Constraints and indexes
    __table_args__ = (
        Index('idx_group_domain_status', 'domain', 'status'),
        Index('idx_group_country', 'country'),
        Index('idx_group_search', 'name', 'description'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_group_version_format'),
        {'extend_existing': True}
    )
    
    def __repr__(self):
        return f"<MitreGroup(id='{self.group_id}', name='{self.name}')>"


class MitreSoftware(Base):
    """MITRE ATT&CK Software (Malware/Tool) model with soft delete and audit capabilities."""
    
    __tablename__ = "mitre_software"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    software_id = Column(String(20), nullable=False, unique=True, index=True)  # S0001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)
    
    # Software details
    software_type = Column(String(50), nullable=True)  # Malware, Tool
    aliases = Column(JSONB, nullable=True)  # List of aliases
    associated_software = Column(JSONB, nullable=True)  # Related software
    platforms = Column(JSONB, nullable=True)  # Supported platforms
    
    # Technical details
    labels = Column(JSONB, nullable=True)  # STIX labels
    
    # References and metadata
    references = Column(JSONB, nullable=True)
    
    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")
    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)
    
    # Performance optimization
    search_vector = Column(Text, nullable=True)  # For full-text search
    
    # Relationships
    groups = relationship("MitreGroup", secondary=group_software_association, back_populates="software")
    
    # Constraints and indexes
    __table_args__ = (
        Index('idx_software_domain_status', 'domain', 'status'),
        Index('idx_software_type', 'software_type'),
        Index('idx_software_search', 'name', 'description'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_software_version_format'),
        {'extend_existing': True}
    )
    
    def __repr__(self):
        return f"<MitreSoftware(id='{self.software_id}', name='{self.name}')>"


class MitreMitigation(Base):
    """MITRE ATT&CK Mitigation model with soft delete and audit capabilities."""
    
    __tablename__ = "mitre_mitigations"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    mitigation_id = Column(String(20), nullable=False, unique=True, index=True)  # M1001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)
    
    # Mitigation details
    mitigation_type = Column(String(100), nullable=True)
    
    # References and metadata
    references = Column(JSONB, nullable=True)
    
    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")
    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)
    
    # Performance optimization
    search_vector = Column(Text, nullable=True)  # For full-text search
    
    # Relationships
    techniques = relationship("MitreTechnique", secondary=technique_mitigation_association, back_populates="mitigations")
    
    # Constraints and indexes
    __table_args__ = (
        Index('idx_mitigation_domain_status', 'domain', 'status'),
        Index('idx_mitigation_search', 'name', 'description'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_mitigation_version_format'),
        {'extend_existing': True}
    )
    
    def __repr__(self):
        return f"<MitreMitigation(id='{self.mitigation_id}', name='{self.name}')>"


class MitreDataSourceModel(Base):
    """MITRE ATT&CK Data Source model with soft delete and audit capabilities."""

    __tablename__ = "mitre_data_sources"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    data_source_id = Column(String(20), nullable=False, unique=True, index=True)  # DS0001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)

    # Data source details
    collection_layers = Column(JSONB, nullable=True)  # Host, Network, Cloud, etc.
    platforms = Column(JSONB, nullable=True)  # Supported platforms
    data_components = Column(JSONB, nullable=True)  # List of data components

    # References and metadata
    references = Column(JSONB, nullable=True)

    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")
    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)

    # Performance optimization
    search_vector = Column(Text, nullable=True)  # For full-text search

    # Constraints and indexes
    __table_args__ = (
        Index('idx_data_source_domain_status', 'domain', 'status'),
        Index('idx_data_source_search', 'name', 'description'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_data_source_version_format'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<MitreDataSourceModel(id='{self.data_source_id}', name='{self.name}')>"


class MitreCampaign(Base):
    """MITRE ATT&CK Campaign model with soft delete and audit capabilities."""

    __tablename__ = "mitre_campaigns"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    campaign_id = Column(String(20), nullable=False, unique=True, index=True)  # C0001
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)

    # Campaign details
    aliases = Column(JSONB, nullable=True)  # List of aliases
    associated_groups = Column(JSONB, nullable=True)  # Associated threat groups

    # Timeline
    first_seen = Column(DateTime, nullable=True)
    last_seen = Column(DateTime, nullable=True)

    # References and metadata
    references = Column(JSONB, nullable=True)

    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")
    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)

    # Performance optimization
    search_vector = Column(Text, nullable=True)  # For full-text search

    # Constraints and indexes
    __table_args__ = (
        Index('idx_campaign_domain_status', 'domain', 'status'),
        Index('idx_campaign_timeline', 'first_seen', 'last_seen'),
        Index('idx_campaign_search', 'name', 'description'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_campaign_version_format'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<MitreCampaign(id='{self.campaign_id}', name='{self.name}')>"


class MitreMatrix(Base):
    """MITRE ATT&CK Matrix model with soft delete and audit capabilities."""

    __tablename__ = "mitre_matrices"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    matrix_id = Column(String(50), nullable=False, unique=True, index=True)  # enterprise-attack, mobile-attack, etc.
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # Classification
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    status = Column(Enum(MitreEntityStatus), nullable=False, default=MitreEntityStatus.ACTIVE, index=True)

    # Matrix configuration
    tactic_order = Column(JSONB, nullable=True)  # Ordered list of tactic IDs
    platforms = Column(JSONB, nullable=True)  # Supported platforms

    # References and metadata
    references = Column(JSONB, nullable=True)

    # Versioning and tracking
    version = Column(String(10), nullable=False, default="1.0")
    # Data source tracking
    data_source = Column(Enum(MitreDataSourceType), nullable=False, default=MitreDataSourceType.OFFICIAL)
    source_url = Column(String(500), nullable=True)

    # Constraints and indexes
    __table_args__ = (
        Index('idx_matrix_domain_status', 'domain', 'status'),
        CheckConstraint('version ~ \'^[0-9]+\.[0-9]+$\'', name='chk_matrix_version_format'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<MitreMatrix(id='{self.matrix_id}', name='{self.name}')>"


class MitreDataSync(Base):
    """MITRE data synchronization tracking model with soft delete and audit capabilities."""

    __tablename__ = "mitre_data_sync"

    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sync_id = Column(String(100), nullable=False, unique=True, index=True)

    # Sync details
    domain = Column(Enum(MitreDomain), nullable=False, index=True)
    sync_type = Column(String(50), nullable=False)  # full, incremental, manual
    status = Column(String(50), nullable=False, index=True)  # pending, running, completed, failed

    # Progress tracking
    total_entities = Column(Integer, nullable=True)
    processed_entities = Column(Integer, nullable=False, default=0)
    failed_entities = Column(Integer, nullable=False, default=0)

    # Timing
    started_at = Column(DateTime, nullable=False, default=func.now())
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)

    # Source information
    source_url = Column(String(500), nullable=True)
    source_version = Column(String(20), nullable=True)
    source_checksum = Column(String(64), nullable=True)

    # Results and errors
    error_message = Column(Text, nullable=True)
    error_details = Column(JSONB, nullable=True)
    sync_results = Column(JSONB, nullable=True)  # Summary of changes

    # Metadata
    triggered_by = Column(String(255), nullable=True)  # User or system
    sync_metadata = Column(JSONB, nullable=True)

    # Constraints and indexes
    __table_args__ = (
        Index('idx_sync_domain_status', 'domain', 'status'),
        Index('idx_sync_started_at', 'started_at'),
        CheckConstraint('processed_entities >= 0', name='chk_processed_entities_non_negative'),
        CheckConstraint('failed_entities >= 0', name='chk_failed_entities_non_negative'),
        CheckConstraint('duration_seconds >= 0', name='chk_duration_non_negative'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<MitreDataSync(id='{self.sync_id}', domain='{self.domain}', status='{self.status}')>"
