"""Base database models and utilities."""

from datetime import datetime
from typing import Any
import uuid

from sqlalchemy import <PERSON>SO<PERSON>, Boolean, Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import Session


class CustomBase:
    """Base class for all database models."""

    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        return cls.__name__.lower()

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
    )

    # Soft delete support
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime, nullable=True, index=True)
    deleted_by = Column(UUID(as_uuid=True), nullable=True)

    # Audit fields
    created_by = Column(UUID(as_uuid=True), nullable=True, index=True)
    updated_by = Column(UUID(as_uuid=True), nullable=True, index=True)

    # Metadata for extensibility
    metadata_json = Column(JSON, nullable=True)

    def soft_delete(self, deleted_by_user_id: uuid.UUID | None = None) -> None:
        """
        Perform soft delete on the record.

        Args:
            deleted_by_user_id: ID of user performing the deletion
        """
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.deleted_by = deleted_by_user_id

    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None

    def to_dict(self, exclude_deleted: bool = True) -> dict[str, Any]:
        """
        Convert model to dictionary.

        Args:
            exclude_deleted: Whether to exclude deleted records

        Returns:
            Dictionary representation of the model
        """
        if exclude_deleted and self.is_deleted:
            return {}

        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            elif isinstance(value, uuid.UUID):
                value = str(value)
            result[column.name] = value
        return result

    def update_from_dict(
        self, data: dict[str, Any], updated_by_user_id: uuid.UUID | None = None
    ) -> None:
        """
        Update model from dictionary.

        Args:
            data: Dictionary with update data
            updated_by_user_id: ID of user performing the update
        """
        for key, value in data.items():
            if hasattr(self, key) and key not in ["id", "created_at", "created_by"]:
                setattr(self, key, value)

        self.updated_at = datetime.utcnow()
        if updated_by_user_id:
            self.updated_by = updated_by_user_id

    @classmethod
    def get_active_query(cls, session: Session):
        """
        Get query for active (non-deleted) records.

        Args:
            session: Database session

        Returns:
            Query for active records
        """
        return session.query(cls).filter(cls.is_deleted == False)

    @classmethod
    def get_deleted_query(cls, session: Session):
        """
        Get query for deleted records.

        Args:
            session: Database session

        Returns:
            Query for deleted records
        """
        return session.query(cls).filter(cls.is_deleted == True)

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


# Create the base class
Base = declarative_base(cls=CustomBase)


class AuditLog(Base):
    """Audit log for tracking all database changes."""

    __tablename__ = "audit_logs"

    # Audit specific fields
    table_name = Column(String(255), nullable=False, index=True)
    record_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    action = Column(
        String(50), nullable=False, index=True
    )  # CREATE, UPDATE, DELETE, RESTORE
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    user_ip = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True, index=True)

    # Additional context
    request_id = Column(String(255), nullable=True, index=True)
    api_endpoint = Column(String(500), nullable=True)
    http_method = Column(String(10), nullable=True)

    def __repr__(self) -> str:
        """String representation of the audit log."""
        return f"<AuditLog(table={self.table_name}, action={self.action}, record_id={self.record_id})>"


class SystemConfiguration(Base):
    """System configuration settings."""

    __tablename__ = "system_configurations"

    key = Column(String(255), nullable=False, unique=True, index=True)
    value = Column(Text, nullable=True)
    value_type = Column(
        String(50), nullable=False, default="string"
    )  # string, int, float, bool, json
    description = Column(Text, nullable=True)
    is_sensitive = Column(Boolean, default=False, nullable=False)
    is_readonly = Column(Boolean, default=False, nullable=False)
    category = Column(String(100), nullable=True, index=True)

    def get_typed_value(self) -> Any:
        """
        Get the value with proper type conversion.

        Returns:
            Value converted to appropriate type
        """
        if self.value is None:
            return None

        if self.value_type == "int":
            return int(self.value)
        if self.value_type == "float":
            return float(self.value)
        if self.value_type == "bool":
            return self.value.lower() in ("true", "1", "yes", "on")
        if self.value_type == "json":
            import json

            return json.loads(self.value)
        return self.value

    def set_typed_value(self, value: Any) -> None:
        """
        Set the value with automatic type detection.

        Args:
            value: Value to set
        """
        if isinstance(value, bool):
            self.value_type = "bool"
            self.value = str(value).lower()
        elif isinstance(value, int):
            self.value_type = "int"
            self.value = str(value)
        elif isinstance(value, float):
            self.value_type = "float"
            self.value = str(value)
        elif isinstance(value, (dict, list)):
            self.value_type = "json"
            import json

            self.value = json.dumps(value)
        else:
            self.value_type = "string"
            self.value = str(value)

    def __repr__(self) -> str:
        """String representation of the configuration."""
        masked_value = "***" if self.is_sensitive else self.value
        return f"<SystemConfiguration(key={self.key}, value={masked_value})>"
