"""Soft delete mixin for database models."""

from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, String
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func


class SoftDeleteMixin:
    """Mixin to add soft delete functionality to models."""
    
    @declared_attr
    def is_deleted(cls):
        """Soft delete flag."""
        return Column(Boolean, nullable=False, default=False, index=True)
    
    @declared_attr
    def deleted_at(cls):
        """Timestamp when the record was soft deleted."""
        return Column(DateTime, nullable=True, index=True)
    
    @declared_attr
    def deleted_by(cls):
        """User who performed the soft delete."""
        return Column(String(255), nullable=True)
    
    @declared_attr
    def deletion_reason(cls):
        """Reason for deletion (optional)."""
        return Column(String(500), nullable=True)
    
    def soft_delete(self, deleted_by: Optional[str] = None, reason: Optional[str] = None):
        """Perform soft delete on the record."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.deleted_by = deleted_by
        self.deletion_reason = reason
    
    def restore(self):
        """Restore a soft deleted record."""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        self.deletion_reason = None
    
    @property
    def is_active(self) -> bool:
        """Check if the record is active (not soft deleted)."""
        return not self.is_deleted
    
    @classmethod
    def active_only(cls):
        """Query filter to get only active (non-deleted) records."""
        return cls.is_deleted == False
    
    @classmethod
    def deleted_only(cls):
        """Query filter to get only deleted records."""
        return cls.is_deleted == True


class AuditMixin:
    """Mixin to add audit trail functionality."""
    
    @declared_attr
    def created_by(cls):
        """User who created the record."""
        return Column(String(255), nullable=True)
    
    @declared_attr
    def modified_by(cls):
        """User who last modified the record."""
        return Column(String(255), nullable=True)
    
    @declared_attr
    def created_at(cls):
        """Timestamp when record was created."""
        return Column(DateTime, nullable=False, default=func.now())
    
    @declared_attr
    def modified_at(cls):
        """Timestamp when record was last modified."""
        return Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    
    def set_audit_fields(self, user_id: Optional[str] = None, is_creation: bool = False):
        """Set audit fields for the record."""
        if is_creation:
            self.created_by = user_id
        self.modified_by = user_id


# Utility functions for soft delete operations
def bulk_soft_delete(session, model_class, filter_criteria, user_id: Optional[str] = None, reason: Optional[str] = None):
    """Perform bulk soft delete operation."""
    records = session.query(model_class).filter(filter_criteria).all()
    
    for record in records:
        record.soft_delete(deleted_by=user_id, reason=reason)
    
    session.commit()
    return len(records)


def bulk_restore(session, model_class, filter_criteria, user_id: Optional[str] = None):
    """Perform bulk restore operation."""
    records = session.query(model_class).filter(
        model_class.is_deleted == True
    ).filter(filter_criteria).all()
    
    for record in records:
        record.restore()
        if hasattr(record, 'set_audit_fields'):
            record.set_audit_fields(user_id=user_id)
    
    session.commit()
    return len(records)


def get_deletion_stats(session, model_class):
    """Get statistics about deleted vs active records."""
    total_count = session.query(model_class).count()
    active_count = session.query(model_class).filter(model_class.is_deleted == False).count()
    deleted_count = session.query(model_class).filter(model_class.is_deleted == True).count()
    
    return {
        'total': total_count,
        'active': active_count,
        'deleted': deleted_count,
        'deletion_rate': (deleted_count / total_count * 100) if total_count > 0 else 0
    }
