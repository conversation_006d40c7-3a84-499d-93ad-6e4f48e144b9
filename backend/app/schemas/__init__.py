"""Pydantic schemas for API request/response models."""

from .auth import (
    LoginRequest,
    LoginResponse,
    MFABackupCodesResponse,
    MFASetupRequest,
    MFAVerifyRequest,
    PasswordResetConfirm,
    PasswordResetRequest,
    RefreshTokenRequest,
    TokenResponse,
)
from .user import (
    UserAPIKeyCreate,
    UserAPIKeyResponse,
    UserCreate,
    UserListResponse,
    UserResponse,
    UserRoleAssignment,
    UserSessionResponse,
    UserUpdate,
)
from .asset import (
    AssetCreate,
    AssetUpdate,
    AssetResponse,
    AssetListResponse,
    AssetSearchRequest,
    AssetRelationshipCreate,
    AssetRelationshipUpdate,
    AssetRelationshipResponse,
    DiscoveryJobCreate,
    DiscoveryJobUpdate,
    DiscoveryJobResponse,
    DiscoveryJobListResponse,
    AssetTagCreate,
    AssetTagUpdate,
    AssetTagResponse,
    AssetStatistics,
    AssetHealthCheck,
)
from .mitre import (
    MitreTechniqueBase,
    MitreTechniqueCreate,
    MitreTechniqueUpdate,
    MitreTechniqueResponse,
    MitreTechniqueListResponse,
    MitreTacticBase,
    MitreTacticCreate,
    MitreTacticUpdate,
    MitreTacticResponse,
    MitreTacticListResponse,
    MitreGroupBase,
    MitreGroupCreate,
    MitreGroupUpdate,
    MitreGroupResponse,
    MitreGroupListResponse,
    MitreSoftwareBase,
    MitreSoftwareCreate,
    MitreSoftwareUpdate,
    MitreSoftwareResponse,
    MitreMitigationBase,
    MitreMitigationCreate,
    MitreMitigationUpdate,
    MitreMitigationResponse,
    MitreDataSyncBase,
    MitreDataSyncCreate,
    MitreDataSyncUpdate,
    MitreDataSyncResponse,
    MitreSearchRequest,
    MitreSearchResponse,
)

__all__ = [
    # Authentication schemas
    "LoginRequest",
    "LoginResponse",
    "TokenResponse",
    "RefreshTokenRequest",
    "PasswordResetRequest",
    "PasswordResetConfirm",
    "MFASetupRequest",
    "MFAVerifyRequest",
    "MFABackupCodesResponse",
    # User schemas
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserListResponse",
    "UserRoleAssignment",
    "UserSessionResponse",
    "UserAPIKeyCreate",
    "UserAPIKeyResponse",
    # Asset schemas
    "AssetCreate",
    "AssetUpdate",
    "AssetResponse",
    "AssetListResponse",
    "AssetSearchRequest",
    "AssetRelationshipCreate",
    "AssetRelationshipUpdate",
    "AssetRelationshipResponse",
    "DiscoveryJobCreate",
    "DiscoveryJobUpdate",
    "DiscoveryJobResponse",
    "DiscoveryJobListResponse",
    "AssetTagCreate",
    "AssetTagUpdate",
    "AssetTagResponse",
    "AssetStatistics",
    "AssetHealthCheck",
    # MITRE schemas
    "MitreTechniqueBase",
    "MitreTechniqueCreate",
    "MitreTechniqueUpdate",
    "MitreTechniqueResponse",
    "MitreTechniqueListResponse",
    "MitreTacticBase",
    "MitreTacticCreate",
    "MitreTacticUpdate",
    "MitreTacticResponse",
    "MitreTacticListResponse",
    "MitreGroupBase",
    "MitreGroupCreate",
    "MitreGroupUpdate",
    "MitreGroupResponse",
    "MitreGroupListResponse",
    "MitreSoftwareBase",
    "MitreSoftwareCreate",
    "MitreSoftwareUpdate",
    "MitreSoftwareResponse",
    "MitreMitigationBase",
    "MitreMitigationCreate",
    "MitreMitigationUpdate",
    "MitreMitigationResponse",
    "MitreDataSyncBase",
    "MitreDataSyncCreate",
    "MitreDataSyncUpdate",
    "MitreDataSyncResponse",
    "MitreSearchRequest",
    "MitreSearchResponse",
]
