"""Pydantic schemas for MITRE ATT&CK models."""

import re
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, ConfigDict, field_validator, model_validator
from pydantic.functional_validators import BeforeValidator
from typing_extensions import Annotated

# Validator functions for ID normalization
def normalize_technique_id(v: str) -> str:
    """Normalize technique ID to uppercase."""
    return v.upper() if isinstance(v, str) else v

def normalize_tactic_id(v: str) -> str:
    """Normalize tactic ID to uppercase."""
    return v.upper() if isinstance(v, str) else v

def normalize_group_id(v: str) -> str:
    """Normalize group ID to uppercase."""
    return v.upper() if isinstance(v, str) else v

from app.db.models.mitre import MitreDomain, MitreEntityStatus, MitreDataSourceType


# Base schemas
class MitreBaseSchema(BaseModel):
    """Base schema for MITRE entities."""
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    domain: MitreDomain
    status: MitreEntityStatus = MitreEntityStatus.ACTIVE
    version: str = "1.0"
    created_date: datetime
    modified_date: datetime
    data_source: MitreDataSourceType = MitreDataSourceType.OFFICIAL
    source_url: Optional[str] = None


# Technique schemas
class MitreTechniqueBase(BaseModel):
    """Base schema for MITRE Technique."""
    technique_id: Annotated[str, BeforeValidator(normalize_technique_id), Field(
        description="MITRE technique ID (e.g., T1234, T1234.001)",
        pattern=r"^T\d{4}(\.\d{3})?$"
    )]
    name: Annotated[str, Field(description="Technique name", min_length=1, max_length=255)]
    description: Optional[Annotated[str, Field(max_length=10000)]] = None
    domain: MitreDomain
    status: MitreEntityStatus = MitreEntityStatus.ACTIVE
    is_subtechnique: bool = False
    parent_technique_id: Optional[Annotated[str, Field(
        pattern=r"^T\d{4}$",
        description="Parent technique ID for sub-techniques"
    )]] = None
    platforms: Optional[List[str]] = Field(None, description="Supported platforms")
    data_sources: Optional[List[str]] = Field(None, description="Data sources for detection")
    system_requirements: Optional[List[str]] = None
    permissions_required: Optional[List[str]] = None
    effective_permissions: Optional[List[str]] = None
    defense_bypassed: Optional[List[str]] = None
    supports_remote: Optional[bool] = None
    impact_type: Optional[List[str]] = None
    capec_id: Optional[Annotated[str, Field(pattern=r"^CAPEC-\d+$")]] = None
    mtc_id: Optional[str] = None
    kill_chain_phases: Optional[List[Dict[str, Any]]] = None
    detection_methods: Optional[List[Dict[str, Any]]] = None
    references: Optional[List[Dict[str, Any]]] = None
    contributors: Optional[List[str]] = None

    @field_validator('technique_id')
    @classmethod
    def validate_technique_id(cls, v: str) -> str:
        """Validate MITRE technique ID format."""
        if not re.match(r"^T\d{4}(\.\d{3})?$", v):
            raise ValueError("Technique ID must be in format T1234 or T1234.001")
        return v.upper()

    @field_validator('platforms')
    @classmethod
    def validate_platforms(cls, v: Optional[List[str]]) -> Optional[List[str]]:
        """Validate platform names."""
        if v is None:
            return v

        valid_platforms = {
            "Windows", "Linux", "macOS", "Android", "iOS",
            "Network", "PRE", "Office 365", "Azure AD", "Google Workspace",
            "SaaS", "IaaS", "Containers", "Kubernetes"
        }

        for platform in v:
            if platform not in valid_platforms:
                raise ValueError(f"Invalid platform: {platform}")

        return v

    @model_validator(mode='after')
    def validate_subtechnique_relationship(self) -> 'MitreTechniqueBase':
        """Validate sub-technique relationships."""
        if self.is_subtechnique:
            if not self.parent_technique_id:
                raise ValueError("Sub-techniques must have a parent_technique_id")
            if not self.technique_id.startswith(self.parent_technique_id + "."):
                raise ValueError("Sub-technique ID must start with parent technique ID")
        elif self.parent_technique_id:
            raise ValueError("Only sub-techniques can have a parent_technique_id")

        return self


class MitreTechniqueCreate(MitreTechniqueBase):
    """Schema for creating a MITRE Technique."""
    pass


class MitreTechniqueUpdate(BaseModel):
    """Schema for updating a MITRE Technique."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[MitreEntityStatus] = None
    platforms: Optional[List[str]] = None
    data_sources: Optional[List[str]] = None
    detection_methods: Optional[List[Dict[str, Any]]] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreTechniqueResponse(MitreBaseSchema):
    """Schema for MITRE Technique response."""
    technique_id: str
    is_subtechnique: bool
    parent_technique_id: Optional[str] = None
    platforms: Optional[List[str]] = None
    data_sources: Optional[List[str]] = None
    system_requirements: Optional[List[str]] = None
    permissions_required: Optional[List[str]] = None
    effective_permissions: Optional[List[str]] = None
    defense_bypassed: Optional[List[str]] = None
    supports_remote: Optional[bool] = None
    impact_type: Optional[List[str]] = None
    capec_id: Optional[str] = None
    mtc_id: Optional[str] = None
    kill_chain_phases: Optional[List[Dict[str, Any]]] = None
    detection_methods: Optional[List[Dict[str, Any]]] = None
    references: Optional[List[Dict[str, Any]]] = None
    contributors: Optional[List[str]] = None


# Tactic schemas
class MitreTacticBase(BaseModel):
    """Base schema for MITRE Tactic."""
    tactic_id: Annotated[str, BeforeValidator(normalize_tactic_id), Field(
        description="MITRE tactic ID (e.g., TA0001)",
        pattern=r"^TA\d{4}$"
    )]
    name: Annotated[str, Field(description="Tactic name", min_length=1, max_length=255)]
    description: Optional[Annotated[str, Field(max_length=10000)]] = None
    domain: MitreDomain
    status: MitreEntityStatus = MitreEntityStatus.ACTIVE
    short_name: Optional[Annotated[str, Field(max_length=100)]] = None
    x_mitre_shortname: Optional[Annotated[str, Field(max_length=100)]] = None
    references: Optional[List[Dict[str, Any]]] = None

    @field_validator('tactic_id')
    @classmethod
    def validate_tactic_id(cls, v: str) -> str:
        """Validate MITRE tactic ID format."""
        if not re.match(r"^TA\d{4}$", v):
            raise ValueError("Tactic ID must be in format TA0001")
        return v.upper()

    @field_validator('short_name', 'x_mitre_shortname')
    @classmethod
    def validate_short_names(cls, v: Optional[str]) -> Optional[str]:
        """Validate short name format."""
        if v is None:
            return v

        # Short names should be lowercase with hyphens
        if not re.match(r"^[a-z0-9-]+$", v):
            raise ValueError("Short names must be lowercase with hyphens only")

        return v


class MitreTacticCreate(MitreTacticBase):
    """Schema for creating a MITRE Tactic."""
    pass


class MitreTacticUpdate(BaseModel):
    """Schema for updating a MITRE Tactic."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[MitreEntityStatus] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreTacticResponse(MitreBaseSchema):
    """Schema for MITRE Tactic response."""
    tactic_id: str
    short_name: Optional[str] = None
    x_mitre_shortname: Optional[str] = None
    references: Optional[List[Dict[str, Any]]] = None


# Group schemas
class MitreGroupBase(BaseModel):
    """Base schema for MITRE Group."""
    group_id: Annotated[str, BeforeValidator(normalize_group_id), Field(
        description="MITRE group ID (e.g., G0001)",
        pattern=r"^G\d{4}$"
    )]
    name: Annotated[str, Field(description="Group name", min_length=1, max_length=255)]
    description: Optional[Annotated[str, Field(max_length=10000)]] = None
    domain: MitreDomain
    status: MitreEntityStatus = MitreEntityStatus.ACTIVE
    aliases: Optional[List[str]] = Field(None, description="Alternative names for the group")
    associated_groups: Optional[List[str]] = None
    country: Optional[Annotated[str, Field(max_length=100)]] = None
    motivation: Optional[List[str]] = None
    sophistication: Optional[str] = Field(None, description="High, Medium, Low")
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    references: Optional[List[Dict[str, Any]]] = None

    @field_validator('group_id')
    @classmethod
    def validate_group_id(cls, v: str) -> str:
        """Validate MITRE group ID format."""
        if not re.match(r"^G\d{4}$", v):
            raise ValueError("Group ID must be in format G0001")
        return v.upper()

    @field_validator('sophistication')
    @classmethod
    def validate_sophistication(cls, v: Optional[str]) -> Optional[str]:
        """Validate sophistication level."""
        if v is None:
            return v

        valid_levels = {"High", "Medium", "Low"}
        if v not in valid_levels:
            raise ValueError(f"Sophistication must be one of: {', '.join(valid_levels)}")

        return v

    @field_validator('motivation')
    @classmethod
    def validate_motivation(cls, v: Optional[List[str]]) -> Optional[List[str]]:
        """Validate motivation types."""
        if v is None:
            return v

        valid_motivations = {
            "Financial", "Espionage", "Sabotage", "Ideology",
            "Hacktivism", "Dominance", "Personal"
        }

        for motivation in v:
            if motivation not in valid_motivations:
                raise ValueError(f"Invalid motivation: {motivation}")

        return v

    @model_validator(mode='after')
    def validate_timeline(self) -> 'MitreGroupBase':
        """Validate timeline consistency."""
        if self.first_seen and self.last_seen:
            if self.first_seen > self.last_seen:
                raise ValueError("first_seen cannot be after last_seen")

        return self


class MitreGroupCreate(MitreGroupBase):
    """Schema for creating a MITRE Group."""
    pass


class MitreGroupUpdate(BaseModel):
    """Schema for updating a MITRE Group."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[MitreEntityStatus] = None
    aliases: Optional[List[str]] = None
    country: Optional[str] = None
    motivation: Optional[List[str]] = None
    sophistication: Optional[str] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreGroupResponse(MitreBaseSchema):
    """Schema for MITRE Group response."""
    group_id: str
    aliases: Optional[List[str]] = None
    associated_groups: Optional[List[str]] = None
    country: Optional[str] = None
    motivation: Optional[List[str]] = None
    sophistication: Optional[str] = None
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    references: Optional[List[Dict[str, Any]]] = None


# Software schemas
class MitreSoftwareBase(BaseModel):
    """Base schema for MITRE Software."""
    software_id: str = Field(..., description="MITRE software ID (e.g., S0001)")
    name: str = Field(..., description="Software name")
    description: Optional[str] = None
    domain: MitreDomain
    status: MitreEntityStatus = MitreEntityStatus.ACTIVE
    software_type: Optional[str] = None
    aliases: Optional[List[str]] = None
    associated_software: Optional[List[str]] = None
    platforms: Optional[List[str]] = None
    labels: Optional[List[str]] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreSoftwareCreate(MitreSoftwareBase):
    """Schema for creating a MITRE Software."""
    pass


class MitreSoftwareUpdate(BaseModel):
    """Schema for updating a MITRE Software."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[MitreEntityStatus] = None
    software_type: Optional[str] = None
    aliases: Optional[List[str]] = None
    platforms: Optional[List[str]] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreSoftwareResponse(MitreBaseSchema):
    """Schema for MITRE Software response."""
    software_id: str
    software_type: Optional[str] = None
    aliases: Optional[List[str]] = None
    associated_software: Optional[List[str]] = None
    platforms: Optional[List[str]] = None
    labels: Optional[List[str]] = None
    references: Optional[List[Dict[str, Any]]] = None


# Mitigation schemas
class MitreMitigationBase(BaseModel):
    """Base schema for MITRE Mitigation."""
    mitigation_id: str = Field(..., description="MITRE mitigation ID (e.g., M1001)")
    name: str = Field(..., description="Mitigation name")
    description: Optional[str] = None
    domain: MitreDomain
    status: MitreEntityStatus = MitreEntityStatus.ACTIVE
    mitigation_type: Optional[str] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreMitigationCreate(MitreMitigationBase):
    """Schema for creating a MITRE Mitigation."""
    pass


class MitreMitigationUpdate(BaseModel):
    """Schema for updating a MITRE Mitigation."""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[MitreEntityStatus] = None
    mitigation_type: Optional[str] = None
    references: Optional[List[Dict[str, Any]]] = None


class MitreMitigationResponse(MitreBaseSchema):
    """Schema for MITRE Mitigation response."""
    mitigation_id: str
    mitigation_type: Optional[str] = None
    references: Optional[List[Dict[str, Any]]] = None


# Data Sync schemas
class MitreDataSyncBase(BaseModel):
    """Base schema for MITRE Data Sync."""
    sync_id: str = Field(..., description="Unique sync identifier")
    domain: MitreDomain
    sync_type: str = Field(..., description="Type of sync (full, incremental, manual)")
    status: str = Field(..., description="Sync status")
    total_entities: Optional[int] = None
    processed_entities: int = 0
    failed_entities: int = 0
    source_url: Optional[str] = None
    source_version: Optional[str] = None
    triggered_by: Optional[str] = None


class MitreDataSyncCreate(MitreDataSyncBase):
    """Schema for creating a MITRE Data Sync."""
    pass


class MitreDataSyncUpdate(BaseModel):
    """Schema for updating a MITRE Data Sync."""
    status: Optional[str] = None
    processed_entities: Optional[int] = None
    failed_entities: Optional[int] = None
    error_message: Optional[str] = None
    sync_results: Optional[Dict[str, Any]] = None


class MitreDataSyncResponse(BaseModel):
    """Schema for MITRE Data Sync response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID
    sync_id: str
    domain: MitreDomain
    sync_type: str
    status: str
    total_entities: Optional[int] = None
    processed_entities: int
    failed_entities: int
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    source_url: Optional[str] = None
    source_version: Optional[str] = None
    source_checksum: Optional[str] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    sync_results: Optional[Dict[str, Any]] = None
    triggered_by: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# List response schemas
class MitreTechniqueListResponse(BaseModel):
    """Schema for MITRE Technique list response."""
    techniques: List[MitreTechniqueResponse]
    total: int
    page: int
    size: int


class MitreTacticListResponse(BaseModel):
    """Schema for MITRE Tactic list response."""
    tactics: List[MitreTacticResponse]
    total: int
    page: int
    size: int


class MitreGroupListResponse(BaseModel):
    """Schema for MITRE Group list response."""
    groups: List[MitreGroupResponse]
    total: int
    page: int
    size: int


# Search and filter schemas
class MitreSearchRequest(BaseModel):
    """Schema for MITRE search request."""
    query: Annotated[str, Field(description="Search query")]
    domains: Optional[List[MitreDomain]] = None
    entity_types: Optional[List[str]] = None  # technique, tactic, group, software, mitigation
    status: Optional[List[MitreEntityStatus]] = None
    limit: Annotated[int, Field(default=50, le=1000)]
    offset: Annotated[int, Field(default=0, ge=0)]


class MitreSearchResponse(BaseModel):
    """Schema for MITRE search response."""
    results: List[Dict[str, Any]]
    total: int
    query: str
    execution_time_ms: float
