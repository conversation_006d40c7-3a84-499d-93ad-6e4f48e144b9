"""Authentication-related Pydantic schemas."""

from datetime import datetime

from pydantic import BaseModel, EmailStr, Field, validator


class LoginRequest(BaseModel):
    """Login request schema."""

    username: str = Field(
        ..., min_length=1, max_length=50, description="Username or email"
    )
    password: str = Field(..., min_length=1, description="User password")
    remember_me: bool = Field(
        default=False, description="Remember login for extended session"
    )
    mfa_code: str | None = Field(
        None, min_length=6, max_length=6, description="MFA verification code"
    )

    @validator("username")
    def validate_username(cls, v):
        """Validate username format."""
        if not v or not v.strip():
            raise ValueError("Username cannot be empty")
        return v.strip().lower()

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "username": "<EMAIL>",
                "password": "SecurePassword123!",
                "remember_me": False,
                "mfa_code": "123456",
            }
        }


class TokenResponse(BaseModel):
    """Token response schema."""

    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 1800,
            }
        }


class UserInfo(BaseModel):
    """User information schema."""

    id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: EmailStr = Field(..., description="Email address")
    full_name: str = Field(..., description="Full name")
    roles: list[str] = Field(..., description="User roles")
    permissions: list[str] = Field(..., description="User permissions")
    is_active: bool = Field(..., description="Account active status")
    is_verified: bool = Field(..., description="Email verification status")
    mfa_enabled: bool = Field(..., description="MFA enabled status")
    last_login_at: datetime | None = Field(None, description="Last login timestamp")

    class Config:
        """Pydantic configuration."""

        orm_mode = True


class LoginResponse(BaseModel):
    """Login response schema."""

    user: UserInfo = Field(..., description="User information")
    tokens: TokenResponse = Field(..., description="Authentication tokens")
    requires_mfa: bool = Field(default=False, description="Whether MFA is required")
    mfa_methods: list[str] = Field(default=[], description="Available MFA methods")
    session_id: str = Field(..., description="Session ID")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "user": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "username": "john.doe",
                    "email": "<EMAIL>",
                    "full_name": "John Doe",
                    "roles": ["soc_operator"],
                    "permissions": ["view_security_events", "create_incident"],
                    "is_active": True,
                    "is_verified": True,
                    "mfa_enabled": True,
                    "last_login_at": "2024-01-15T10:30:00Z",
                },
                "tokens": {
                    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    "token_type": "bearer",
                    "expires_in": 1800,
                },
                "requires_mfa": False,
                "mfa_methods": ["totp", "sms"],
                "session_id": "sess_123456789",
            }
        }


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema."""

    refresh_token: str = Field(..., description="Refresh token")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {"refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}
        }


class LogoutRequest(BaseModel):
    """Logout request schema."""

    all_sessions: bool = Field(default=False, description="Logout from all sessions")

    class Config:
        """Pydantic configuration."""

        schema_extra = {"example": {"all_sessions": False}}


class PasswordResetRequest(BaseModel):
    """Password reset request schema."""

    email: EmailStr = Field(..., description="Email address for password reset")

    class Config:
        """Pydantic configuration."""

        schema_extra = {"example": {"email": "<EMAIL>"}}


class PasswordResetConfirm(BaseModel):
    """Password reset confirmation schema."""

    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")

    @validator("confirm_password")
    def passwords_match(cls, v, values):
        """Validate that passwords match."""
        if "new_password" in values and v != values["new_password"]:
            raise ValueError("Passwords do not match")
        return v

    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        if not all([has_upper, has_lower, has_digit, has_special]):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )

        return v

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "token": "reset_token_123456789",
                "new_password": "NewSecurePassword123!",
                "confirm_password": "NewSecurePassword123!",
            }
        }


class ChangePasswordRequest(BaseModel):
    """Change password request schema."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")

    @validator("confirm_password")
    def passwords_match(cls, v, values):
        """Validate that passwords match."""
        if "new_password" in values and v != values["new_password"]:
            raise ValueError("Passwords do not match")
        return v

    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        if not all([has_upper, has_lower, has_digit, has_special]):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )

        return v

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "current_password": "CurrentPassword123!",
                "new_password": "NewSecurePassword123!",
                "confirm_password": "NewSecurePassword123!",
            }
        }


class MFASetupRequest(BaseModel):
    """MFA setup request schema."""

    device_name: str = Field(
        ..., min_length=1, max_length=100, description="Device name"
    )
    device_type: str = Field(..., description="Device type (totp, sms, email)")
    phone_number: str | None = Field(None, description="Phone number for SMS")
    email_address: EmailStr | None = Field(None, description="Email for email codes")

    @validator("device_type")
    def validate_device_type(cls, v):
        """Validate device type."""
        allowed_types = ["totp", "sms", "email", "hardware"]
        if v not in allowed_types:
            raise ValueError(f"Device type must be one of: {', '.join(allowed_types)}")
        return v

    @validator("phone_number")
    def validate_phone_number(cls, v, values):
        """Validate phone number for SMS devices."""
        if values.get("device_type") == "sms" and not v:
            raise ValueError("Phone number is required for SMS devices")
        return v

    @validator("email_address")
    def validate_email_address(cls, v, values):
        """Validate email address for email devices."""
        if values.get("device_type") == "email" and not v:
            raise ValueError("Email address is required for email devices")
        return v

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "device_name": "My Phone",
                "device_type": "totp",
                "phone_number": "+1234567890",
                "email_address": "<EMAIL>",
            }
        }


class MFASetupResponse(BaseModel):
    """MFA setup response schema."""

    device_id: str = Field(..., description="Device ID")
    device_name: str = Field(..., description="Device name")
    device_type: str = Field(..., description="Device type")
    qr_code: str | None = Field(None, description="QR code for TOTP setup")
    secret_key: str | None = Field(None, description="Secret key for manual entry")
    backup_codes: list[str] | None = Field(None, description="Backup codes")
    verification_required: bool = Field(
        ..., description="Whether verification is required"
    )

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "device_id": "mfa_123456789",
                "device_name": "My Phone",
                "device_type": "totp",
                "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
                "secret_key": "JBSWY3DPEHPK3PXP",
                "backup_codes": ["ABCD1234", "EFGH5678"],
                "verification_required": True,
            }
        }


class MFAVerifyRequest(BaseModel):
    """MFA verification request schema."""

    device_id: str = Field(..., description="Device ID")
    code: str = Field(..., min_length=6, max_length=8, description="Verification code")

    class Config:
        """Pydantic configuration."""

        schema_extra = {"example": {"device_id": "mfa_123456789", "code": "123456"}}


class MFABackupCodesResponse(BaseModel):
    """MFA backup codes response schema."""

    backup_codes: list[str] = Field(..., description="Backup codes")
    remaining_codes: int = Field(..., description="Number of remaining codes")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "backup_codes": ["ABCD1234", "EFGH5678", "IJKL9012"],
                "remaining_codes": 8,
            }
        }


class SessionInfo(BaseModel):
    """Session information schema."""

    session_id: str = Field(..., description="Session ID")
    ip_address: str | None = Field(None, description="IP address")
    user_agent: str | None = Field(None, description="User agent")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity_at: datetime = Field(..., description="Last activity time")
    expires_at: datetime = Field(..., description="Session expiration time")
    is_current: bool = Field(..., description="Whether this is the current session")

    class Config:
        """Pydantic configuration."""

        orm_mode = True


class SessionListResponse(BaseModel):
    """Session list response schema."""

    sessions: list[SessionInfo] = Field(..., description="List of sessions")
    total: int = Field(..., description="Total number of sessions")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "sessions": [
                    {
                        "session_id": "sess_123456789",
                        "ip_address": "*************",
                        "user_agent": "Mozilla/5.0...",
                        "created_at": "2024-01-15T10:30:00Z",
                        "last_activity_at": "2024-01-15T11:45:00Z",
                        "expires_at": "2024-01-15T22:30:00Z",
                        "is_current": True,
                    }
                ],
                "total": 1,
            }
        }
