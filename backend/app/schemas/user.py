"""User-related Pydantic schemas."""

from datetime import datetime
from typing import Any

from pydantic import BaseModel, EmailStr, Field, validator

from app.core.permissions import UserRole


class UserBase(BaseModel):
    """Base user schema with common fields."""

    username: str = Field(..., min_length=3, max_length=50, description="Username")
    email: EmailStr = Field(..., description="Email address")
    full_name: str = Field(..., min_length=1, max_length=255, description="Full name")
    phone_number: str | None = Field(None, max_length=20, description="Phone number")
    department: str | None = Field(None, max_length=100, description="Department")
    job_title: str | None = Field(None, max_length=100, description="Job title")
    timezone: str = Field(default="UTC", description="User timezone")
    language: str = Field(default="en", description="Preferred language")
    theme: str = Field(default="dark", description="UI theme preference")

    @validator("username")
    def validate_username(cls, v):
        """Validate username format."""
        if not v.replace("_", "").replace(".", "").replace("-", "").isalnum():
            raise ValueError(
                "Username can only contain letters, numbers, underscores, dots, and hyphens"
            )
        return v.lower()

    @validator("theme")
    def validate_theme(cls, v):
        """Validate theme value."""
        allowed_themes = ["light", "dark", "auto"]
        if v not in allowed_themes:
            raise ValueError(f"Theme must be one of: {', '.join(allowed_themes)}")
        return v


class UserCreate(UserBase):
    """Schema for creating a new user."""

    password: str = Field(..., min_length=8, description="User password")
    roles: list[str] = Field(default=[], description="User roles")
    is_active: bool = Field(default=True, description="Account active status")
    require_password_change: bool = Field(
        default=True, description="Require password change on first login"
    )
    manager_id: str | None = Field(None, description="Manager user ID")

    @validator("password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)

        if not all([has_upper, has_lower, has_digit, has_special]):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )

        return v

    @validator("roles")
    def validate_roles(cls, v):
        """Validate user roles."""
        valid_roles = [role.value for role in UserRole]
        for role in v:
            if role not in valid_roles:
                raise ValueError(
                    f"Invalid role: {role}. Valid roles are: {', '.join(valid_roles)}"
                )
        return v

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "username": "john.doe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "password": "SecurePassword123!",
                "phone_number": "+**********",
                "department": "Security Operations",
                "job_title": "SOC Analyst",
                "roles": ["soc_operator"],
                "timezone": "America/New_York",
                "language": "en",
                "theme": "dark",
            }
        }


class UserUpdate(BaseModel):
    """Schema for updating user information."""

    email: EmailStr | None = Field(None, description="Email address")
    full_name: str | None = Field(
        None, min_length=1, max_length=255, description="Full name"
    )
    phone_number: str | None = Field(None, max_length=20, description="Phone number")
    department: str | None = Field(None, max_length=100, description="Department")
    job_title: str | None = Field(None, max_length=100, description="Job title")
    timezone: str | None = Field(None, description="User timezone")
    language: str | None = Field(None, description="Preferred language")
    theme: str | None = Field(None, description="UI theme preference")
    is_active: bool | None = Field(None, description="Account active status")
    manager_id: str | None = Field(None, description="Manager user ID")
    preferences: dict[str, Any] | None = Field(None, description="User preferences")

    @validator("theme")
    def validate_theme(cls, v):
        """Validate theme value."""
        if v is not None:
            allowed_themes = ["light", "dark", "auto"]
            if v not in allowed_themes:
                raise ValueError(f"Theme must be one of: {', '.join(allowed_themes)}")
        return v

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "full_name": "John Smith",
                "phone_number": "+**********",
                "department": "Cybersecurity",
                "job_title": "Senior SOC Analyst",
                "timezone": "America/New_York",
                "theme": "dark",
                "preferences": {
                    "dashboard_layout": "compact",
                    "notifications_enabled": True,
                },
            }
        }


class UserResponse(BaseModel):
    """Schema for user response."""

    id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: EmailStr = Field(..., description="Email address")
    full_name: str = Field(..., description="Full name")
    phone_number: str | None = Field(None, description="Phone number")
    department: str | None = Field(None, description="Department")
    job_title: str | None = Field(None, description="Job title")
    timezone: str = Field(..., description="User timezone")
    language: str = Field(..., description="Preferred language")
    theme: str = Field(..., description="UI theme preference")

    # Status fields
    is_active: bool = Field(..., description="Account active status")
    is_verified: bool = Field(..., description="Email verification status")
    is_locked: bool = Field(..., description="Account locked status")

    # Security fields
    mfa_enabled: bool = Field(..., description="MFA enabled status")
    require_password_change: bool = Field(..., description="Requires password change")

    # Timestamps
    created_at: datetime = Field(..., description="Account creation time")
    updated_at: datetime = Field(..., description="Last update time")
    last_login_at: datetime | None = Field(None, description="Last login time")
    password_changed_at: datetime = Field(..., description="Password last changed")

    # Relationships
    roles: list[str] = Field(..., description="User roles")
    permissions: list[str] = Field(..., description="User permissions")
    manager_id: str | None = Field(None, description="Manager user ID")

    # Preferences
    preferences: dict[str, Any] | None = Field(None, description="User preferences")

    class Config:
        """Pydantic configuration."""

        orm_mode = True
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "username": "john.doe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "phone_number": "+**********",
                "department": "Security Operations",
                "job_title": "SOC Analyst",
                "timezone": "America/New_York",
                "language": "en",
                "theme": "dark",
                "is_active": True,
                "is_verified": True,
                "is_locked": False,
                "mfa_enabled": True,
                "require_password_change": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "last_login_at": "2024-01-15T10:30:00Z",
                "password_changed_at": "2024-01-01T00:00:00Z",
                "roles": ["soc_operator"],
                "permissions": ["view_security_events", "create_incident"],
                "manager_id": "456e7890-e89b-12d3-a456-426614174001",
                "preferences": {
                    "dashboard_layout": "compact",
                    "notifications_enabled": True,
                },
            }
        }


class UserListResponse(BaseModel):
    """Schema for user list response."""

    users: list[UserResponse] = Field(..., description="List of users")
    total: int = Field(..., description="Total number of users")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "users": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "username": "john.doe",
                        "email": "<EMAIL>",
                        "full_name": "John Doe",
                        "roles": ["soc_operator"],
                        "is_active": True,
                    }
                ],
                "total": 1,
                "page": 1,
                "size": 20,
                "pages": 1,
            }
        }


class UserRoleAssignment(BaseModel):
    """Schema for user role assignment."""

    user_id: str = Field(..., description="User ID")
    roles: list[str] = Field(..., description="Roles to assign")
    expires_at: datetime | None = Field(None, description="Role expiration time")

    @validator("roles")
    def validate_roles(cls, v):
        """Validate user roles."""
        valid_roles = [role.value for role in UserRole]
        for role in v:
            if role not in valid_roles:
                raise ValueError(
                    f"Invalid role: {role}. Valid roles are: {', '.join(valid_roles)}"
                )
        return v

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "user_id": "123e4567-e89b-12d3-a456-************",
                "roles": ["soc_operator", "analyst"],
                "expires_at": "2024-12-31T23:59:59Z",
            }
        }


class UserSessionResponse(BaseModel):
    """Schema for user session response."""

    id: str = Field(..., description="Session ID")
    user_id: str = Field(..., description="User ID")
    ip_address: str | None = Field(None, description="IP address")
    user_agent: str | None = Field(None, description="User agent")
    device_fingerprint: str | None = Field(None, description="Device fingerprint")
    is_active: bool = Field(..., description="Session active status")
    is_suspicious: bool = Field(..., description="Suspicious activity flag")
    created_at: datetime = Field(..., description="Session creation time")
    expires_at: datetime = Field(..., description="Session expiration time")
    last_activity_at: datetime = Field(..., description="Last activity time")
    logged_out_at: datetime | None = Field(None, description="Logout time")
    logout_reason: str | None = Field(None, description="Logout reason")

    class Config:
        """Pydantic configuration."""

        orm_mode = True


class UserAPIKeyCreate(BaseModel):
    """Schema for creating user API key."""

    name: str = Field(..., min_length=1, max_length=100, description="API key name")
    description: str | None = Field(None, description="API key description")
    scopes: list[str] | None = Field(None, description="API key scopes/permissions")
    allowed_ips: list[str] | None = Field(None, description="Allowed IP addresses")
    rate_limit: int | None = Field(
        None, gt=0, description="Rate limit (requests per minute)"
    )
    expires_at: datetime | None = Field(None, description="Expiration time")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "name": "API Integration Key",
                "description": "Key for automated security scanning integration",
                "scopes": ["read_assets", "create_incident"],
                "allowed_ips": ["*************", "*********"],
                "rate_limit": 1000,
                "expires_at": "2024-12-31T23:59:59Z",
            }
        }


class UserAPIKeyResponse(BaseModel):
    """Schema for user API key response."""

    id: str = Field(..., description="API key ID")
    name: str = Field(..., description="API key name")
    description: str | None = Field(None, description="API key description")
    key_prefix: str = Field(..., description="API key prefix (for identification)")
    scopes: list[str] | None = Field(None, description="API key scopes/permissions")
    allowed_ips: list[str] | None = Field(None, description="Allowed IP addresses")
    rate_limit: int | None = Field(None, description="Rate limit (requests per minute)")
    is_active: bool = Field(..., description="API key active status")
    created_at: datetime = Field(..., description="Creation time")
    expires_at: datetime | None = Field(None, description="Expiration time")
    last_used_at: datetime | None = Field(None, description="Last used time")
    usage_count: int = Field(..., description="Usage count")

    class Config:
        """Pydantic configuration."""

        orm_mode = True
        schema_extra = {
            "example": {
                "id": "key_123456789",
                "name": "API Integration Key",
                "description": "Key for automated security scanning integration",
                "key_prefix": "br_abcd",
                "scopes": ["read_assets", "create_incident"],
                "allowed_ips": ["*************"],
                "rate_limit": 1000,
                "is_active": True,
                "created_at": "2024-01-01T00:00:00Z",
                "expires_at": "2024-12-31T23:59:59Z",
                "last_used_at": "2024-01-15T10:30:00Z",
                "usage_count": 1250,
            }
        }


class UserAPIKeyCreateResponse(BaseModel):
    """Schema for API key creation response."""

    api_key: UserAPIKeyResponse = Field(..., description="API key information")
    secret_key: str = Field(..., description="Secret API key (only shown once)")

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "api_key": {
                    "id": "key_123456789",
                    "name": "API Integration Key",
                    "key_prefix": "br_abcd",
                    "is_active": True,
                },
                "secret_key": "br_abcd**********abcdef**********abcdef12",
            }
        }
