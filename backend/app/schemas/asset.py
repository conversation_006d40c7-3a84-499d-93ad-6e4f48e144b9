"""Pydantic schemas for asset management API."""

from datetime import datetime
from typing import Any, Dict, List, Optional
import uuid

from pydantic import BaseModel, Field, validator

from app.db.models.asset import (
    AssetProvider,
    AssetStatus,
    AssetType,
    DiscoveryJobStatus,
    DiscoverySource,
    RelationshipType,
    RiskLevel,
)


# Base schemas
class AssetBase(BaseModel):
    """Base asset schema."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Asset name")
    asset_type: AssetType = Field(..., description="Type of asset")
    provider: AssetProvider = Field(..., description="Asset provider")
    status: AssetStatus = Field(default=AssetStatus.ACTIVE, description="Asset status")
    
    provider_id: Optional[str] = Field(None, max_length=255, description="Provider-specific ID")
    provider_region: Optional[str] = Field(None, max_length=100, description="Provider region")
    provider_zone: Optional[str] = Field(None, max_length=100, description="Provider zone")
    provider_account_id: Optional[str] = Field(None, max_length=255, description="Provider account ID")
    
    ip_addresses: Optional[List[str]] = Field(None, description="List of IP addresses")
    dns_names: Optional[List[str]] = Field(None, description="List of DNS names")
    ports: Optional[List[int]] = Field(None, description="List of open ports")
    protocols: Optional[List[str]] = Field(None, description="List of protocols")
    
    description: Optional[str] = Field(None, description="Asset description")
    environment: Optional[str] = Field(None, max_length=50, description="Environment (prod, staging, dev)")
    owner: Optional[str] = Field(None, max_length=255, description="Asset owner")
    team: Optional[str] = Field(None, max_length=255, description="Owning team")
    cost_center: Optional[str] = Field(None, max_length=100, description="Cost center")
    
    configuration: Optional[Dict[str, Any]] = Field(None, description="Asset configuration")
    properties: Optional[Dict[str, Any]] = Field(None, description="Additional properties")
    tags: Optional[Dict[str, str]] = Field(None, description="Asset tags")
    
    discovery_source: DiscoverySource = Field(..., description="How the asset was discovered")
    
    risk_score: int = Field(default=0, ge=0, le=100, description="Risk score (0-100)")
    risk_level: RiskLevel = Field(default=RiskLevel.INFO, description="Risk level")
    compliance_status: str = Field(default="unknown", max_length=50, description="Compliance status")
    
    is_monitored: bool = Field(default=False, description="Whether asset is monitored")
    health_status: Optional[str] = Field(None, max_length=50, description="Health status")
    
    parent_id: Optional[uuid.UUID] = Field(None, description="Parent asset ID")

    @validator('ip_addresses')
    def validate_ip_addresses(cls, v):
        """Validate IP addresses format."""
        if v is not None:
            import ipaddress
            for ip in v:
                try:
                    ipaddress.ip_address(ip)
                except ValueError:
                    raise ValueError(f"Invalid IP address: {ip}")
        return v

    @validator('ports')
    def validate_ports(cls, v):
        """Validate port numbers."""
        if v is not None:
            for port in v:
                if not (1 <= port <= 65535):
                    raise ValueError(f"Invalid port number: {port}")
        return v


class AssetCreate(AssetBase):
    """Schema for creating a new asset."""
    pass


class AssetUpdate(BaseModel):
    """Schema for updating an asset."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    status: Optional[AssetStatus] = None
    description: Optional[str] = None
    environment: Optional[str] = Field(None, max_length=50)
    owner: Optional[str] = Field(None, max_length=255)
    team: Optional[str] = Field(None, max_length=255)
    cost_center: Optional[str] = Field(None, max_length=100)
    
    configuration: Optional[Dict[str, Any]] = None
    properties: Optional[Dict[str, Any]] = None
    tags: Optional[Dict[str, str]] = None
    
    risk_score: Optional[int] = Field(None, ge=0, le=100)
    risk_level: Optional[RiskLevel] = None
    compliance_status: Optional[str] = Field(None, max_length=50)
    
    is_monitored: Optional[bool] = None
    health_status: Optional[str] = Field(None, max_length=50)


class AssetResponse(AssetBase):
    """Schema for asset response."""
    
    id: uuid.UUID
    discovered_at: datetime
    last_seen: datetime
    created_at: datetime
    updated_at: datetime
    
    discovery_job_id: Optional[uuid.UUID] = None
    vulnerabilities: Optional[List[Dict[str, Any]]] = None
    security_groups: Optional[List[Dict[str, Any]]] = None
    metrics: Optional[Dict[str, Any]] = None
    
    configuration_hash: Optional[str] = None
    last_configuration_change: Optional[datetime] = None
    change_count: int = 0
    last_health_check: Optional[datetime] = None

    class Config:
        from_attributes = True


class AssetListResponse(BaseModel):
    """Schema for asset list response."""
    
    assets: List[AssetResponse]
    total: int
    page: int
    size: int
    pages: int


class AssetSearchRequest(BaseModel):
    """Schema for asset search request."""
    
    query: Optional[str] = Field(None, description="Search query")
    asset_types: Optional[List[AssetType]] = Field(None, description="Filter by asset types")
    providers: Optional[List[AssetProvider]] = Field(None, description="Filter by providers")
    statuses: Optional[List[AssetStatus]] = Field(None, description="Filter by statuses")
    environments: Optional[List[str]] = Field(None, description="Filter by environments")
    owners: Optional[List[str]] = Field(None, description="Filter by owners")
    teams: Optional[List[str]] = Field(None, description="Filter by teams")
    risk_levels: Optional[List[RiskLevel]] = Field(None, description="Filter by risk levels")
    
    tags: Optional[Dict[str, str]] = Field(None, description="Filter by tags")
    
    discovered_after: Optional[datetime] = Field(None, description="Filter by discovery date")
    discovered_before: Optional[datetime] = Field(None, description="Filter by discovery date")
    
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")
    sort_by: str = Field(default="name", description="Sort field")
    sort_order: str = Field(default="asc", pattern="^(asc|desc)$", description="Sort order")


# Asset Relationship schemas
class AssetRelationshipBase(BaseModel):
    """Base asset relationship schema."""
    
    source_asset_id: uuid.UUID = Field(..., description="Source asset ID")
    target_asset_id: uuid.UUID = Field(..., description="Target asset ID")
    relationship_type: RelationshipType = Field(..., description="Relationship type")
    
    description: Optional[str] = Field(None, description="Relationship description")
    properties: Optional[Dict[str, Any]] = Field(None, description="Additional properties")
    
    protocol: Optional[str] = Field(None, max_length=20, description="Communication protocol")
    port: Optional[int] = Field(None, ge=1, le=65535, description="Port number")
    direction: Optional[str] = Field(None, pattern="^(inbound|outbound|bidirectional)$", description="Direction")
    
    discovery_source: DiscoverySource = Field(..., description="How the relationship was discovered")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score")
    
    is_active: bool = Field(default=True, description="Whether relationship is active")
    is_verified: bool = Field(default=False, description="Whether relationship is verified")
    verification_method: Optional[str] = Field(None, max_length=100, description="Verification method")


class AssetRelationshipCreate(AssetRelationshipBase):
    """Schema for creating a new asset relationship."""
    pass


class AssetRelationshipUpdate(BaseModel):
    """Schema for updating an asset relationship."""
    
    description: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None
    protocol: Optional[str] = Field(None, max_length=20)
    port: Optional[int] = Field(None, ge=1, le=65535)
    direction: Optional[str] = Field(None, pattern="^(inbound|outbound|bidirectional)$")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    verification_method: Optional[str] = Field(None, max_length=100)


class AssetRelationshipResponse(AssetRelationshipBase):
    """Schema for asset relationship response."""
    
    id: uuid.UUID
    discovered_at: datetime
    last_verified: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Discovery Job schemas
class DiscoveryJobBase(BaseModel):
    """Base discovery job schema."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Job name")
    description: Optional[str] = Field(None, description="Job description")
    job_type: str = Field(..., max_length=100, description="Job type")
    
    configuration: Optional[Dict[str, Any]] = Field(None, description="Job configuration")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Discovery parameters")
    filters: Optional[Dict[str, Any]] = Field(None, description="Asset filters")
    
    is_scheduled: bool = Field(default=False, description="Whether job is scheduled")
    schedule_expression: Optional[str] = Field(None, max_length=255, description="Cron expression")


class DiscoveryJobCreate(DiscoveryJobBase):
    """Schema for creating a new discovery job."""
    pass


class DiscoveryJobUpdate(BaseModel):
    """Schema for updating a discovery job."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    filters: Optional[Dict[str, Any]] = None
    is_scheduled: Optional[bool] = None
    schedule_expression: Optional[str] = Field(None, max_length=255)


class DiscoveryJobResponse(DiscoveryJobBase):
    """Schema for discovery job response."""
    
    id: uuid.UUID
    status: DiscoveryJobStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    
    assets_discovered: int = 0
    assets_updated: int = 0
    relationships_discovered: int = 0
    errors_count: int = 0
    
    executor: Optional[str] = None
    execution_log: Optional[List[Dict[str, Any]]] = None
    error_details: Optional[List[Dict[str, Any]]] = None
    
    next_run_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DiscoveryJobListResponse(BaseModel):
    """Schema for discovery job list response."""
    
    jobs: List[DiscoveryJobResponse]
    total: int
    page: int
    size: int
    pages: int


# Asset Tag schemas
class AssetTagBase(BaseModel):
    """Base asset tag schema."""
    
    key: str = Field(..., min_length=1, max_length=255, description="Tag key")
    value: Optional[str] = Field(None, max_length=1000, description="Tag value")
    source: Optional[str] = Field(None, max_length=100, description="Tag source")
    is_system: bool = Field(default=False, description="Whether tag is system-generated")
    is_inherited: bool = Field(default=False, description="Whether tag is inherited")


class AssetTagCreate(AssetTagBase):
    """Schema for creating a new asset tag."""
    asset_id: uuid.UUID = Field(..., description="Asset ID")


class AssetTagUpdate(BaseModel):
    """Schema for updating an asset tag."""
    
    value: Optional[str] = Field(None, max_length=1000)
    source: Optional[str] = Field(None, max_length=100)


class AssetTagResponse(AssetTagBase):
    """Schema for asset tag response."""
    
    id: uuid.UUID
    asset_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Statistics and analytics schemas
class AssetStatistics(BaseModel):
    """Schema for asset statistics."""
    
    total_assets: int
    assets_by_type: Dict[str, int]
    assets_by_provider: Dict[str, int]
    assets_by_status: Dict[str, int]
    assets_by_environment: Dict[str, int]
    assets_by_risk_level: Dict[str, int]
    
    discovery_sources: Dict[str, int]
    recent_discoveries: int  # Last 24 hours
    
    total_relationships: int
    relationships_by_type: Dict[str, int]
    
    compliance_summary: Dict[str, int]
    health_summary: Dict[str, int]


class AssetHealthCheck(BaseModel):
    """Schema for asset health check."""
    
    asset_id: uuid.UUID
    health_status: str
    last_check: datetime
    metrics: Dict[str, Any]
    issues: List[Dict[str, Any]]
