"""
Blast-Radius Security Tool - Data Integrity Framework
Implements data validation, consistency checks, and integrity monitoring
"""

import asyncio
import hashlib
import json
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from contextlib import asynccontextmanager

from sqlalchemy import text, and_, or_
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from pydantic import BaseModel, ValidationError, validator

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Data validation levels"""
    BASIC = "basic"
    STANDARD = "standard"
    STRICT = "strict"
    PARANOID = "paranoid"


class IntegrityCheckType(Enum):
    """Types of integrity checks"""
    REFERENTIAL = "referential"
    BUSINESS_RULE = "business_rule"
    DATA_QUALITY = "data_quality"
    CONSISTENCY = "consistency"
    COMPLETENESS = "completeness"


@dataclass
class ValidationRule:
    """Data validation rule definition"""
    name: str
    description: str
    validator_func: Callable
    level: ValidationLevel
    error_message: str
    fix_func: Optional[Callable] = None


@dataclass
class IntegrityViolation:
    """Data integrity violation record"""
    check_type: IntegrityCheckType
    table_name: str
    record_id: Optional[str]
    field_name: Optional[str]
    violation_description: str
    severity: str
    detected_at: datetime
    resolved_at: Optional[datetime] = None
    resolution_action: Optional[str] = None


class DataValidator:
    """
    Comprehensive data validation framework
    """
    
    def __init__(self):
        self.rules: Dict[str, List[ValidationRule]] = {}
        self.validation_stats = {
            "total_validations": 0,
            "passed_validations": 0,
            "failed_validations": 0,
            "auto_fixes": 0
        }
    
    def register_rule(self, entity_type: str, rule: ValidationRule):
        """Register validation rule for entity type"""
        if entity_type not in self.rules:
            self.rules[entity_type] = []
        self.rules[entity_type].append(rule)
        logger.info(f"Registered validation rule '{rule.name}' for {entity_type}")
    
    async def validate_entity(
        self,
        entity_type: str,
        data: Dict[str, Any],
        level: ValidationLevel = ValidationLevel.STANDARD
    ) -> Dict[str, Any]:
        """
        Validate entity data against registered rules
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "auto_fixes": []
        }
        
        if entity_type not in self.rules:
            logger.warning(f"No validation rules found for entity type: {entity_type}")
            return validation_result
        
        applicable_rules = [
            rule for rule in self.rules[entity_type]
            if self._should_apply_rule(rule.level, level)
        ]
        
        for rule in applicable_rules:
            try:
                self.validation_stats["total_validations"] += 1
                
                # Execute validation rule
                is_valid = await self._execute_rule(rule.validator_func, data)
                
                if is_valid:
                    self.validation_stats["passed_validations"] += 1
                else:
                    self.validation_stats["failed_validations"] += 1
                    validation_result["valid"] = False
                    
                    error_info = {
                        "rule": rule.name,
                        "message": rule.error_message,
                        "field": getattr(rule, 'field_name', None),
                        "severity": self._get_severity_for_level(rule.level)
                    }
                    
                    if rule.level in [ValidationLevel.BASIC, ValidationLevel.STANDARD]:
                        validation_result["errors"].append(error_info)
                    else:
                        validation_result["warnings"].append(error_info)
                    
                    # Attempt auto-fix if available
                    if rule.fix_func:
                        try:
                            fixed_data = await self._execute_rule(rule.fix_func, data)
                            if fixed_data:
                                validation_result["auto_fixes"].append({
                                    "rule": rule.name,
                                    "action": "auto_fixed",
                                    "original_value": data.get(getattr(rule, 'field_name', None)),
                                    "fixed_value": fixed_data
                                })
                                self.validation_stats["auto_fixes"] += 1
                        except Exception as fix_error:
                            logger.warning(f"Auto-fix failed for rule {rule.name}: {fix_error}")
                
            except Exception as e:
                logger.error(f"Validation rule {rule.name} failed: {e}")
                validation_result["errors"].append({
                    "rule": rule.name,
                    "message": f"Validation rule execution failed: {e}",
                    "severity": "error"
                })
        
        return validation_result
    
    def _should_apply_rule(self, rule_level: ValidationLevel, validation_level: ValidationLevel) -> bool:
        """Determine if rule should be applied based on validation level"""
        level_hierarchy = {
            ValidationLevel.BASIC: 1,
            ValidationLevel.STANDARD: 2,
            ValidationLevel.STRICT: 3,
            ValidationLevel.PARANOID: 4
        }
        return level_hierarchy[rule_level] <= level_hierarchy[validation_level]
    
    def _get_severity_for_level(self, level: ValidationLevel) -> str:
        """Get severity string for validation level"""
        severity_map = {
            ValidationLevel.BASIC: "error",
            ValidationLevel.STANDARD: "error",
            ValidationLevel.STRICT: "warning",
            ValidationLevel.PARANOID: "info"
        }
        return severity_map[level]
    
    async def _execute_rule(self, rule_func: Callable, data: Dict[str, Any]) -> Any:
        """Execute validation or fix rule"""
        if asyncio.iscoroutinefunction(rule_func):
            return await rule_func(data)
        else:
            return rule_func(data)
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics"""
        total = self.validation_stats["total_validations"]
        return {
            **self.validation_stats,
            "success_rate": (
                self.validation_stats["passed_validations"] / total
                if total > 0 else 0
            ),
            "auto_fix_rate": (
                self.validation_stats["auto_fixes"] / 
                self.validation_stats["failed_validations"]
                if self.validation_stats["failed_validations"] > 0 else 0
            )
        }


class IntegrityMonitor:
    """
    Database integrity monitoring and violation tracking
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.violations: List[IntegrityViolation] = []
        self.check_registry: Dict[str, Callable] = {}
    
    def register_check(self, name: str, check_func: Callable, check_type: IntegrityCheckType):
        """Register integrity check function"""
        self.check_registry[name] = {
            "func": check_func,
            "type": check_type
        }
        logger.info(f"Registered integrity check: {name}")
    
    async def run_integrity_checks(self) -> Dict[str, Any]:
        """Run all registered integrity checks"""
        results = {
            "total_checks": len(self.check_registry),
            "passed_checks": 0,
            "failed_checks": 0,
            "violations": [],
            "check_results": {}
        }
        
        for check_name, check_info in self.check_registry.items():
            try:
                logger.info(f"Running integrity check: {check_name}")
                
                check_result = await self._execute_check(
                    check_info["func"],
                    check_info["type"]
                )
                
                results["check_results"][check_name] = check_result
                
                if check_result["passed"]:
                    results["passed_checks"] += 1
                else:
                    results["failed_checks"] += 1
                    results["violations"].extend(check_result["violations"])
                    
            except Exception as e:
                logger.error(f"Integrity check {check_name} failed: {e}")
                results["failed_checks"] += 1
                results["check_results"][check_name] = {
                    "passed": False,
                    "error": str(e),
                    "violations": []
                }
        
        return results
    
    async def _execute_check(self, check_func: Callable, check_type: IntegrityCheckType) -> Dict[str, Any]:
        """Execute individual integrity check"""
        try:
            if asyncio.iscoroutinefunction(check_func):
                violations = await check_func(self.db)
            else:
                violations = check_func(self.db)
            
            # Convert violations to IntegrityViolation objects
            violation_objects = []
            for violation in violations:
                if isinstance(violation, dict):
                    violation_obj = IntegrityViolation(
                        check_type=check_type,
                        table_name=violation.get("table_name", "unknown"),
                        record_id=violation.get("record_id"),
                        field_name=violation.get("field_name"),
                        violation_description=violation.get("description", ""),
                        severity=violation.get("severity", "medium"),
                        detected_at=datetime.now(timezone.utc)
                    )
                    violation_objects.append(violation_obj)
                    self.violations.append(violation_obj)
            
            return {
                "passed": len(violations) == 0,
                "violations": violation_objects,
                "violation_count": len(violations)
            }
            
        except Exception as e:
            logger.error(f"Integrity check execution failed: {e}")
            raise
    
    async def check_referential_integrity(self) -> List[Dict[str, Any]]:
        """Check referential integrity across tables"""
        violations = []
        
        # Example: Check for orphaned records
        orphan_queries = [
            {
                "name": "orphaned_attack_paths",
                "query": """
                    SELECT ap.id, ap.source_asset_id, ap.target_asset_id
                    FROM attack_paths ap
                    LEFT JOIN assets sa ON ap.source_asset_id = sa.id
                    LEFT JOIN assets ta ON ap.target_asset_id = ta.id
                    WHERE sa.id IS NULL OR ta.id IS NULL
                """,
                "table": "attack_paths"
            },
            {
                "name": "orphaned_vulnerabilities",
                "query": """
                    SELECT v.id, v.asset_id
                    FROM vulnerabilities v
                    LEFT JOIN assets a ON v.asset_id = a.id
                    WHERE a.id IS NULL
                """,
                "table": "vulnerabilities"
            }
        ]
        
        for query_info in orphan_queries:
            try:
                result = await self.db.execute(text(query_info["query"]))
                orphaned_records = result.fetchall()
                
                for record in orphaned_records:
                    violations.append({
                        "table_name": query_info["table"],
                        "record_id": str(record[0]),
                        "description": f"Orphaned record in {query_info['table']}: {query_info['name']}",
                        "severity": "high"
                    })
                    
            except Exception as e:
                logger.error(f"Referential integrity check failed for {query_info['name']}: {e}")
        
        return violations
    
    async def check_business_rules(self) -> List[Dict[str, Any]]:
        """Check business rule violations"""
        violations = []
        
        # Example business rules
        business_rule_queries = [
            {
                "name": "invalid_risk_scores",
                "query": """
                    SELECT id, risk_score
                    FROM assets
                    WHERE risk_score < 0 OR risk_score > 10
                """,
                "table": "assets",
                "description": "Risk score must be between 0 and 10"
            },
            {
                "name": "future_discovery_dates",
                "query": """
                    SELECT id, discovered_at
                    FROM assets
                    WHERE discovered_at > NOW()
                """,
                "table": "assets",
                "description": "Discovery date cannot be in the future"
            }
        ]
        
        for rule in business_rule_queries:
            try:
                result = await self.db.execute(text(rule["query"]))
                violating_records = result.fetchall()
                
                for record in violating_records:
                    violations.append({
                        "table_name": rule["table"],
                        "record_id": str(record[0]),
                        "description": f"Business rule violation: {rule['description']}",
                        "severity": "medium"
                    })
                    
            except Exception as e:
                logger.error(f"Business rule check failed for {rule['name']}: {e}")
        
        return violations
    
    def get_violation_summary(self) -> Dict[str, Any]:
        """Get summary of integrity violations"""
        if not self.violations:
            return {"total_violations": 0}
        
        summary = {
            "total_violations": len(self.violations),
            "by_severity": {},
            "by_type": {},
            "by_table": {},
            "unresolved_count": 0
        }
        
        for violation in self.violations:
            # Count by severity
            severity = violation.severity
            summary["by_severity"][severity] = summary["by_severity"].get(severity, 0) + 1
            
            # Count by type
            check_type = violation.check_type.value
            summary["by_type"][check_type] = summary["by_type"].get(check_type, 0) + 1
            
            # Count by table
            table = violation.table_name
            summary["by_table"][table] = summary["by_table"].get(table, 0) + 1
            
            # Count unresolved
            if violation.resolved_at is None:
                summary["unresolved_count"] += 1
        
        return summary


class DataConsistencyChecker:
    """
    Advanced data consistency checking across distributed components
    """
    
    def __init__(self):
        self.consistency_rules: List[Callable] = []
        self.last_check_time: Optional[datetime] = None
        self.consistency_score: float = 1.0
    
    def add_consistency_rule(self, rule_func: Callable):
        """Add consistency checking rule"""
        self.consistency_rules.append(rule_func)
    
    async def check_cross_system_consistency(self) -> Dict[str, Any]:
        """Check consistency across different system components"""
        results = {
            "overall_consistent": True,
            "consistency_score": 1.0,
            "inconsistencies": [],
            "checks_performed": len(self.consistency_rules)
        }
        
        inconsistency_count = 0
        
        for rule_func in self.consistency_rules:
            try:
                if asyncio.iscoroutinefunction(rule_func):
                    inconsistencies = await rule_func()
                else:
                    inconsistencies = rule_func()
                
                if inconsistencies:
                    results["overall_consistent"] = False
                    results["inconsistencies"].extend(inconsistencies)
                    inconsistency_count += len(inconsistencies)
                    
            except Exception as e:
                logger.error(f"Consistency check failed: {e}")
                results["overall_consistent"] = False
                inconsistency_count += 1
        
        # Calculate consistency score
        if results["checks_performed"] > 0:
            results["consistency_score"] = max(
                0.0,
                1.0 - (inconsistency_count / results["checks_performed"])
            )
        
        self.consistency_score = results["consistency_score"]
        self.last_check_time = datetime.now(timezone.utc)
        
        return results


# Global instances
data_validator = DataValidator()
consistency_checker = DataConsistencyChecker()


# Utility functions for common validation patterns
def validate_email(email: str) -> bool:
    """Validate email format"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_ip_address(ip: str) -> bool:
    """Validate IP address format"""
    import ipaddress
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False


def validate_uuid(uuid_string: str) -> bool:
    """Validate UUID format"""
    import uuid
    try:
        uuid.UUID(uuid_string)
        return True
    except ValueError:
        return False


def calculate_data_hash(data: Dict[str, Any]) -> str:
    """Calculate hash for data integrity verification"""
    # Sort keys for consistent hashing
    sorted_data = json.dumps(data, sort_keys=True, default=str)
    return hashlib.sha256(sorted_data.encode()).hexdigest()


@asynccontextmanager
async def integrity_transaction(db_session: Session):
    """
    Context manager for database transactions with integrity checking
    """
    try:
        # Begin transaction
        db_session.begin()
        
        # Yield control to the calling code
        yield db_session
        
        # If we get here, commit the transaction
        db_session.commit()
        logger.info("Transaction committed successfully")
        
    except IntegrityError as e:
        # Handle integrity constraint violations
        db_session.rollback()
        logger.error(f"Integrity constraint violation: {e}")
        raise
        
    except SQLAlchemyError as e:
        # Handle other database errors
        db_session.rollback()
        logger.error(f"Database error in transaction: {e}")
        raise
        
    except Exception as e:
        # Handle any other errors
        db_session.rollback()
        logger.error(f"Unexpected error in transaction: {e}")
        raise
