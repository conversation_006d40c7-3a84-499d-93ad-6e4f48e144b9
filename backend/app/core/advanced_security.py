"""
Blast-Radius Security Tool - Advanced Security Framework
Implements zero-trust architecture, advanced authentication, and security controls
"""

import asyncio
import hashlib
import hmac
import secrets
import time
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import ipaddress
import re

import jwt
import bcrypt
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

from fastapi import HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security clearance levels - these are classification labels, not actual secrets"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    SECRET = "secret"  # nosec B105 - This is a classification level, not a hardcoded secret
    TOP_SECRET = "top_secret"  # nosec B105 - This is a classification level, not a hardcoded secret


class ThreatLevel(Enum):
    """Threat assessment levels"""
    MINIMAL = "minimal"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityEvent:
    """Security event for audit logging"""
    event_id: str
    event_type: str
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    timestamp: datetime
    severity: ThreatLevel
    description: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False


class ThreatDetector:
    """
    Advanced threat detection and prevention system
    """
    
    def __init__(self):
        self.threat_patterns = {
            "sql_injection": [
                r"(\bunion\b.*\bselect\b)",
                r"(\bselect\b.*\bfrom\b.*\bwhere\b)",
                r"(\bdrop\b.*\btable\b)",
                r"(\binsert\b.*\binto\b)",
                r"(\bdelete\b.*\bfrom\b)"
            ],
            "xss": [
                r"<script[^>]*>.*?</script>",
                r"javascript:",
                r"on\w+\s*=",
                r"<iframe[^>]*>.*?</iframe>"
            ],
            "command_injection": [
                r"[;&|`]",
                r"\$\([^)]*\)",
                r"`[^`]*`",
                r"\|\s*\w+"
            ],
            "path_traversal": [
                r"\.\./",
                r"\.\.\\",
                r"%2e%2e%2f",
                r"%2e%2e%5c"
            ]
        }
        
        self.suspicious_patterns = {
            "brute_force": r"(admin|root|administrator|test|guest)",
            "enumeration": r"(\d+\.\d+\.\d+\.\d+)",
            "scanner": r"(nmap|nikto|sqlmap|burp|zap)"
        }
        
        self.blocked_ips: set = set()
        self.rate_limits: Dict[str, List[float]] = {}
        self.failed_attempts: Dict[str, int] = {}
    
    async def analyze_request(self, request: Request) -> Dict[str, Any]:
        """
        Analyze incoming request for threats
        """
        analysis_result = {
            "threat_detected": False,
            "threat_level": ThreatLevel.MINIMAL,
            "threats": [],
            "risk_score": 0.0,
            "recommendations": []
        }
        
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        
        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            analysis_result["threat_detected"] = True
            analysis_result["threat_level"] = ThreatLevel.CRITICAL
            analysis_result["threats"].append({
                "type": "blocked_ip",
                "description": f"Request from blocked IP: {client_ip}"
            })
            return analysis_result
        
        # Rate limiting check
        rate_limit_result = await self._check_rate_limit(client_ip)
        if rate_limit_result["exceeded"]:
            analysis_result["threat_detected"] = True
            analysis_result["threat_level"] = ThreatLevel.HIGH
            analysis_result["threats"].append({
                "type": "rate_limit_exceeded",
                "description": f"Rate limit exceeded for IP: {client_ip}"
            })
        
        # Analyze request content
        request_data = await self._extract_request_data(request)
        
        # Check for injection attacks
        injection_threats = await self._detect_injection_attacks(request_data)
        if injection_threats:
            analysis_result["threat_detected"] = True
            analysis_result["threat_level"] = ThreatLevel.HIGH
            analysis_result["threats"].extend(injection_threats)
        
        # Check user agent for suspicious patterns
        ua_threats = await self._analyze_user_agent(user_agent)
        if ua_threats:
            analysis_result["threat_detected"] = True
            analysis_result["threats"].extend(ua_threats)
        
        # Geolocation analysis
        geo_analysis = await self._analyze_geolocation(client_ip)
        if geo_analysis["suspicious"]:
            analysis_result["threats"].append({
                "type": "suspicious_geolocation",
                "description": geo_analysis["reason"]
            })
        
        # Calculate overall risk score
        analysis_result["risk_score"] = self._calculate_risk_score(analysis_result["threats"])
        
        # Update threat level based on risk score
        if analysis_result["risk_score"] > 0.8:
            analysis_result["threat_level"] = ThreatLevel.CRITICAL
        elif analysis_result["risk_score"] > 0.6:
            analysis_result["threat_level"] = ThreatLevel.HIGH
        elif analysis_result["risk_score"] > 0.4:
            analysis_result["threat_level"] = ThreatLevel.MEDIUM
        elif analysis_result["risk_score"] > 0.2:
            analysis_result["threat_level"] = ThreatLevel.LOW
        
        return analysis_result
    
    async def _check_rate_limit(self, ip: str, limit: int = 100, window: int = 60) -> Dict[str, Any]:
        """Check rate limiting for IP address"""
        current_time = time.time()
        
        if ip not in self.rate_limits:
            self.rate_limits[ip] = []
        
        # Remove old requests outside the window
        self.rate_limits[ip] = [
            req_time for req_time in self.rate_limits[ip]
            if current_time - req_time < window
        ]
        
        # Add current request
        self.rate_limits[ip].append(current_time)
        
        exceeded = len(self.rate_limits[ip]) > limit
        
        return {
            "exceeded": exceeded,
            "current_count": len(self.rate_limits[ip]),
            "limit": limit,
            "window": window
        }
    
    async def _extract_request_data(self, request: Request) -> Dict[str, Any]:
        """Extract data from request for analysis"""
        data = {
            "url": str(request.url),
            "method": request.method,
            "headers": dict(request.headers),
            "query_params": dict(request.query_params),
            "body": ""
        }
        
        # Try to get request body
        try:
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await request.body()
                data["body"] = body.decode("utf-8", errors="ignore")
        except Exception:
            pass
        
        return data
    
    async def _detect_injection_attacks(self, request_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect various injection attacks"""
        threats = []
        
        # Combine all text data for analysis
        text_data = " ".join([
            request_data.get("url", ""),
            request_data.get("body", ""),
            " ".join(request_data.get("query_params", {}).values())
        ]).lower()
        
        for attack_type, patterns in self.threat_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_data, re.IGNORECASE):
                    threats.append({
                        "type": attack_type,
                        "description": f"Potential {attack_type} attack detected",
                        "pattern": pattern,
                        "severity": "high"
                    })
        
        return threats
    
    async def _analyze_user_agent(self, user_agent: str) -> List[Dict[str, Any]]:
        """Analyze user agent for suspicious patterns"""
        threats = []
        
        if not user_agent:
            threats.append({
                "type": "missing_user_agent",
                "description": "Missing or empty user agent",
                "severity": "medium"
            })
            return threats
        
        # Check for scanner signatures
        for pattern_name, pattern in self.suspicious_patterns.items():
            if re.search(pattern, user_agent.lower()):
                threats.append({
                    "type": f"suspicious_user_agent_{pattern_name}",
                    "description": f"User agent matches {pattern_name} pattern",
                    "severity": "high"
                })
        
        return threats
    
    async def _analyze_geolocation(self, ip: str) -> Dict[str, Any]:
        """Analyze IP geolocation for suspicious activity"""
        # This would integrate with a geolocation service
        # For now, return a basic analysis
        
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # Check for private/local IPs
            if ip_obj.is_private or ip_obj.is_loopback:
                return {"suspicious": False, "reason": "Local/private IP"}
            
            # Check for known malicious IP ranges (placeholder)
            # In production, this would check against threat intelligence feeds
            
            return {"suspicious": False, "reason": "No geolocation concerns"}
            
        except ValueError:
            return {"suspicious": True, "reason": "Invalid IP address format"}
    
    def _calculate_risk_score(self, threats: List[Dict[str, Any]]) -> float:
        """Calculate overall risk score from threats"""
        if not threats:
            return 0.0
        
        severity_weights = {
            "low": 0.2,
            "medium": 0.4,
            "high": 0.8,
            "critical": 1.0
        }
        
        total_score = 0.0
        for threat in threats:
            severity = threat.get("severity", "medium")
            total_score += severity_weights.get(severity, 0.4)
        
        # Normalize score to 0-1 range
        return min(total_score / len(threats), 1.0)
    
    async def block_ip(self, ip: str, reason: str = "Security violation"):
        """Block IP address"""
        self.blocked_ips.add(ip)
        logger.warning(f"Blocked IP {ip}: {reason}")
    
    async def unblock_ip(self, ip: str):
        """Unblock IP address"""
        self.blocked_ips.discard(ip)
        logger.info(f"Unblocked IP {ip}")


class SecurityAuditLogger:
    """
    Comprehensive security audit logging system
    """
    
    def __init__(self):
        self.events: List[SecurityEvent] = []
        self.event_handlers: Dict[str, List[Callable]] = {}
    
    async def log_security_event(
        self,
        event_type: str,
        description: str,
        severity: ThreatLevel = ThreatLevel.LOW,
        user_id: Optional[str] = None,
        ip_address: str = "unknown",
        user_agent: str = "unknown",
        metadata: Optional[Dict[str, Any]] = None
    ) -> SecurityEvent:
        """Log a security event"""
        
        event = SecurityEvent(
            event_id=secrets.token_urlsafe(16),
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            timestamp=datetime.now(timezone.utc),
            severity=severity,
            description=description,
            metadata=metadata or {}
        )
        
        self.events.append(event)
        
        # Trigger event handlers
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    await self._execute_handler(handler, event)
                except Exception as e:
                    logger.error(f"Event handler failed: {e}")
        
        # Log to standard logger
        log_level = {
            ThreatLevel.MINIMAL: logging.INFO,
            ThreatLevel.LOW: logging.INFO,
            ThreatLevel.MEDIUM: logging.WARNING,
            ThreatLevel.HIGH: logging.ERROR,
            ThreatLevel.CRITICAL: logging.CRITICAL
        }.get(severity, logging.INFO)
        
        logger.log(
            log_level,
            f"Security Event [{event_type}]: {description} "
            f"(User: {user_id}, IP: {ip_address})"
        )
        
        return event
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register handler for specific event type"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    async def _execute_handler(self, handler: Callable, event: SecurityEvent):
        """Execute event handler"""
        if asyncio.iscoroutinefunction(handler):
            await handler(event)
        else:
            handler(event)
    
    def get_events_by_severity(self, severity: ThreatLevel) -> List[SecurityEvent]:
        """Get events by severity level"""
        return [event for event in self.events if event.severity == severity]
    
    def get_events_by_user(self, user_id: str) -> List[SecurityEvent]:
        """Get events for specific user"""
        return [event for event in self.events if event.user_id == user_id]
    
    def get_events_by_ip(self, ip_address: str) -> List[SecurityEvent]:
        """Get events for specific IP address"""
        return [event for event in self.events if event.ip_address == ip_address]
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security events summary"""
        total_events = len(self.events)
        
        if total_events == 0:
            return {"total_events": 0}
        
        severity_counts = {}
        event_type_counts = {}
        
        for event in self.events:
            # Count by severity
            severity = event.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Count by type
            event_type = event.event_type
            event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
        
        return {
            "total_events": total_events,
            "severity_distribution": severity_counts,
            "event_type_distribution": event_type_counts,
            "unresolved_events": len([e for e in self.events if not e.resolved]),
            "last_event_time": max(e.timestamp for e in self.events).isoformat()
        }


class DataClassificationManager:
    """
    Data classification and protection manager
    """
    
    def __init__(self):
        self.classification_rules: Dict[str, SecurityLevel] = {
            "password": SecurityLevel.SECRET,
            "api_key": SecurityLevel.CONFIDENTIAL,
            "token": SecurityLevel.CONFIDENTIAL,
            "ssn": SecurityLevel.SECRET,
            "credit_card": SecurityLevel.SECRET,
            "email": SecurityLevel.INTERNAL,
            "phone": SecurityLevel.INTERNAL,
            "address": SecurityLevel.INTERNAL
        }
        
        self.sensitive_patterns = {
            "ssn": r"\b\d{3}-\d{2}-\d{4}\b",
            "credit_card": r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b",
            "email": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
            "phone": r"\b\d{3}[-.]?\d{3}[-.]?\d{4}\b",
            "api_key": r"\b[A-Za-z0-9]{32,}\b"
        }
    
    def classify_data(self, data: str) -> Dict[str, Any]:
        """Classify data based on content"""
        classification_result = {
            "highest_level": SecurityLevel.PUBLIC,
            "classifications": [],
            "sensitive_data_found": False,
            "redaction_required": False
        }
        
        for data_type, pattern in self.sensitive_patterns.items():
            matches = re.findall(pattern, data, re.IGNORECASE)
            if matches:
                classification_level = self.classification_rules.get(
                    data_type, SecurityLevel.INTERNAL
                )
                
                classification_result["classifications"].append({
                    "type": data_type,
                    "level": classification_level,
                    "matches": len(matches)
                })
                
                classification_result["sensitive_data_found"] = True
                
                # Update highest level
                if self._is_higher_level(classification_level, classification_result["highest_level"]):
                    classification_result["highest_level"] = classification_level
        
        # Determine if redaction is required
        classification_result["redaction_required"] = (
            classification_result["highest_level"] in [
                SecurityLevel.CONFIDENTIAL,
                SecurityLevel.SECRET,
                SecurityLevel.TOP_SECRET
            ]
        )
        
        return classification_result
    
    def redact_sensitive_data(self, data: str) -> str:
        """Redact sensitive data from string"""
        redacted_data = data
        
        for data_type, pattern in self.sensitive_patterns.items():
            if data_type in ["ssn", "credit_card"]:
                # Full redaction for highly sensitive data
                redacted_data = re.sub(pattern, "[REDACTED]", redacted_data, flags=re.IGNORECASE)
            elif data_type == "email":
                # Partial redaction for emails
                redacted_data = re.sub(
                    pattern,
                    lambda m: f"{m.group(0)[:3]}***@{m.group(0).split('@')[1]}",
                    redacted_data,
                    flags=re.IGNORECASE
                )
            else:
                # Mask other sensitive data
                redacted_data = re.sub(pattern, "[MASKED]", redacted_data, flags=re.IGNORECASE)
        
        return redacted_data
    
    def _is_higher_level(self, level1: SecurityLevel, level2: SecurityLevel) -> bool:
        """Check if level1 is higher than level2"""
        level_hierarchy = {
            SecurityLevel.PUBLIC: 1,
            SecurityLevel.INTERNAL: 2,
            SecurityLevel.CONFIDENTIAL: 3,
            SecurityLevel.SECRET: 4,
            SecurityLevel.TOP_SECRET: 5
        }
        return level_hierarchy[level1] > level_hierarchy[level2]


# Global security instances
threat_detector = ThreatDetector()
audit_logger = SecurityAuditLogger()
data_classifier = DataClassificationManager()


# Security middleware
async def security_middleware(request: Request, call_next):
    """Security middleware for threat detection"""
    
    # Analyze request for threats
    threat_analysis = await threat_detector.analyze_request(request)
    
    if threat_analysis["threat_detected"]:
        # Log security event
        await audit_logger.log_security_event(
            event_type="threat_detected",
            description=f"Threat detected: {threat_analysis['threats']}",
            severity=threat_analysis["threat_level"],
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent", "")
        )
        
        # Block critical threats
        if threat_analysis["threat_level"] == ThreatLevel.CRITICAL:
            await threat_detector.block_ip(
                request.client.host,
                f"Critical threat detected: {threat_analysis['threats']}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Request blocked due to security violation"
            )
    
    # Continue with request
    response = await call_next(request)
    return response


# Security decorators
def require_security_level(level: SecurityLevel):
    """Decorator to require minimum security level"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Security level check would be implemented here
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def audit_security_event(event_type: str, description: str = ""):
    """Decorator to audit security events"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                await audit_logger.log_security_event(
                    event_type=event_type,
                    description=description or f"Function {func.__name__} executed",
                    severity=ThreatLevel.LOW
                )
                return result
            except Exception as e:
                await audit_logger.log_security_event(
                    event_type=f"{event_type}_failed",
                    description=f"Function {func.__name__} failed: {str(e)}",
                    severity=ThreatLevel.MEDIUM
                )
                raise
        return wrapper
    return decorator
