"""
Blast-Radius Security Tool - Performance Optimization Framework
Implements caching, connection pooling, and performance monitoring
"""

import asyncio
import time
import logging
from typing import Any, Dict, Optional, Callable, Union, List
from functools import wraps
from dataclasses import dataclass
from contextlib import asynccontextmanager
import hashlib
import json
import pickle
from datetime import datetime, timedelta

import aioredis
from sqlalchemy.pool import QueuePool
from sqlalchemy.engine import create_engine
from prometheus_client import Counter, Histogram, Gauge

logger = logging.getLogger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter('blast_radius_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('blast_radius_request_duration_seconds', 'Request duration', ['method', 'endpoint'])
CACHE_HITS = Counter('blast_radius_cache_hits_total', 'Cache hits', ['cache_type'])
CACHE_MISSES = Counter('blast_radius_cache_misses_total', 'Cache misses', ['cache_type'])
ACTIVE_CONNECTIONS = Gauge('blast_radius_active_connections', 'Active database connections')
QUERY_DURATION = Histogram('blast_radius_query_duration_seconds', 'Database query duration', ['query_type'])


@dataclass
class CacheConfig:
    """Configuration for caching"""
    ttl: int = 3600  # Time to live in seconds
    max_size: int = 1000  # Maximum cache size
    serialize_method: str = "json"  # json, pickle, or raw
    key_prefix: str = "blast_radius"
    compression: bool = False


class PerformanceCache:
    """
    High-performance caching layer with multiple backends
    """
    
    def __init__(self, redis_client: Optional[aioredis.Redis] = None):
        self.redis = redis_client
        self.local_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
    
    def _generate_key(self, key: str, prefix: str = "blast_radius") -> str:
        """Generate cache key with prefix"""
        return f"{prefix}:{key}"
    
    def _serialize_value(self, value: Any, method: str = "json") -> bytes:
        """Serialize value for storage"""
        if method == "json":
            return json.dumps(value, default=str).encode()
        elif method == "pickle":
            # SECURITY: Pickle serialization is disabled for security reasons
            # Use JSON serialization instead for cache data
            logger.warning("Pickle serialization is disabled for security. Using JSON fallback.")
            return json.dumps(value, default=str).encode()
        else:
            return str(value).encode()
    
    def _deserialize_value(self, data: bytes, method: str = "json") -> Any:
        """Deserialize value from storage"""
        if method == "json":
            return json.loads(data.decode())
        elif method == "pickle":
            # SECURITY: Pickle deserialization is disabled for security reasons
            # Use JSON serialization instead for cache data
            logger.warning("Pickle deserialization is disabled for security. Using JSON fallback.")
            try:
                return json.loads(data.decode())
            except (json.JSONDecodeError, UnicodeDecodeError):
                logger.error("Failed to deserialize cache data as JSON")
                return None
        else:
            return data.decode()
    
    async def get(self, key: str, config: CacheConfig = CacheConfig()) -> Optional[Any]:
        """Get value from cache"""
        cache_key = self._generate_key(key, config.key_prefix)
        
        # Try Redis first
        if self.redis:
            try:
                data = await self.redis.get(cache_key)
                if data:
                    self.cache_stats["hits"] += 1
                    CACHE_HITS.labels(cache_type="redis").inc()
                    return self._deserialize_value(data, config.serialize_method)
            except Exception as e:
                logger.warning(f"Redis cache get failed: {e}")
        
        # Try local cache
        if cache_key in self.local_cache:
            cache_entry = self.local_cache[cache_key]
            if cache_entry["expires"] > time.time():
                self.cache_stats["hits"] += 1
                CACHE_HITS.labels(cache_type="local").inc()
                return cache_entry["value"]
            else:
                # Expired, remove from local cache
                del self.local_cache[cache_key]
        
        self.cache_stats["misses"] += 1
        CACHE_MISSES.labels(cache_type="all").inc()
        return None
    
    async def set(
        self,
        key: str,
        value: Any,
        config: CacheConfig = CacheConfig()
    ) -> bool:
        """Set value in cache"""
        cache_key = self._generate_key(key, config.key_prefix)
        serialized_value = self._serialize_value(value, config.serialize_method)
        
        # Set in Redis
        if self.redis:
            try:
                await self.redis.setex(cache_key, config.ttl, serialized_value)
            except Exception as e:
                logger.warning(f"Redis cache set failed: {e}")
        
        # Set in local cache
        if len(self.local_cache) >= config.max_size:
            # Remove oldest entries
            oldest_keys = sorted(
                self.local_cache.keys(),
                key=lambda k: self.local_cache[k]["created"]
            )[:len(self.local_cache) - config.max_size + 1]
            for old_key in oldest_keys:
                del self.local_cache[old_key]
        
        self.local_cache[cache_key] = {
            "value": value,
            "created": time.time(),
            "expires": time.time() + config.ttl
        }
        
        self.cache_stats["sets"] += 1
        return True
    
    async def delete(self, key: str, config: CacheConfig = CacheConfig()) -> bool:
        """Delete value from cache"""
        cache_key = self._generate_key(key, config.key_prefix)
        
        # Delete from Redis
        if self.redis:
            try:
                await self.redis.delete(cache_key)
            except Exception as e:
                logger.warning(f"Redis cache delete failed: {e}")
        
        # Delete from local cache
        if cache_key in self.local_cache:
            del self.local_cache[cache_key]
        
        self.cache_stats["deletes"] += 1
        return True
    
    async def clear(self, pattern: str = "*") -> int:
        """Clear cache entries matching pattern"""
        cleared = 0
        
        # Clear Redis
        if self.redis:
            try:
                keys = await self.redis.keys(pattern)
                if keys:
                    cleared += await self.redis.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis cache clear failed: {e}")
        
        # Clear local cache
        keys_to_delete = [k for k in self.local_cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self.local_cache[key]
            cleared += 1
        
        return cleared
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            **self.cache_stats,
            "local_cache_size": len(self.local_cache),
            "hit_rate": (
                self.cache_stats["hits"] / 
                (self.cache_stats["hits"] + self.cache_stats["misses"])
                if (self.cache_stats["hits"] + self.cache_stats["misses"]) > 0 else 0
            )
        }


def cache_result(
    ttl: int = 3600,
    key_func: Optional[Callable] = None,
    serialize_method: str = "json"
):
    """
    Decorator for caching function results
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                # Use SHA-256 for secure hashing (not for cryptographic security, just cache keys)
                cache_key = hashlib.sha256(":".join(key_parts).encode()).hexdigest()
            
            # Try to get from cache
            from app.cache import get_cache
            cache = await get_cache()
            config = CacheConfig(ttl=ttl, serialize_method=serialize_method)
            
            cached_result = await cache.get(cache_key, config)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result, config)
            
            return result
        return wrapper
    return decorator


class ConnectionPool:
    """
    Advanced connection pool manager
    """
    
    def __init__(self, database_url: str, **kwargs):
        self.database_url = database_url
        self.pool_config = {
            "pool_size": kwargs.get("pool_size", 20),
            "max_overflow": kwargs.get("max_overflow", 30),
            "pool_timeout": kwargs.get("pool_timeout", 30),
            "pool_recycle": kwargs.get("pool_recycle", 3600),
            "pool_pre_ping": kwargs.get("pool_pre_ping", True)
        }
        self.engine = None
        self._setup_engine()
    
    def _setup_engine(self):
        """Setup database engine with connection pooling"""
        self.engine = create_engine(
            self.database_url,
            poolclass=QueuePool,
            **self.pool_config,
            echo=False
        )
    
    def get_pool_status(self) -> Dict[str, Any]:
        """Get connection pool status"""
        if not self.engine or not hasattr(self.engine.pool, 'size'):
            return {"status": "not_initialized"}
        
        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }


class PerformanceMonitor:
    """
    Performance monitoring and metrics collection
    """
    
    def __init__(self):
        self.metrics = {
            "request_times": [],
            "query_times": [],
            "cache_stats": {},
            "error_counts": {}
        }
        self.start_time = time.time()
    
    def record_request(self, method: str, endpoint: str, duration: float, status: int):
        """Record HTTP request metrics"""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)
        
        self.metrics["request_times"].append({
            "timestamp": time.time(),
            "method": method,
            "endpoint": endpoint,
            "duration": duration,
            "status": status
        })
    
    def record_query(self, query_type: str, duration: float):
        """Record database query metrics"""
        QUERY_DURATION.labels(query_type=query_type).observe(duration)
        
        self.metrics["query_times"].append({
            "timestamp": time.time(),
            "query_type": query_type,
            "duration": duration
        })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        current_time = time.time()
        uptime = current_time - self.start_time
        
        # Calculate request statistics
        recent_requests = [
            r for r in self.metrics["request_times"]
            if current_time - r["timestamp"] < 300  # Last 5 minutes
        ]
        
        avg_response_time = (
            sum(r["duration"] for r in recent_requests) / len(recent_requests)
            if recent_requests else 0
        )
        
        # Calculate query statistics
        recent_queries = [
            q for q in self.metrics["query_times"]
            if current_time - q["timestamp"] < 300  # Last 5 minutes
        ]
        
        avg_query_time = (
            sum(q["duration"] for q in recent_queries) / len(recent_queries)
            if recent_queries else 0
        )
        
        return {
            "uptime_seconds": uptime,
            "total_requests": len(self.metrics["request_times"]),
            "recent_requests": len(recent_requests),
            "avg_response_time": avg_response_time,
            "total_queries": len(self.metrics["query_times"]),
            "recent_queries": len(recent_queries),
            "avg_query_time": avg_query_time,
            "timestamp": current_time
        }


def performance_monitor(operation_type: str = "request"):
    """
    Decorator for performance monitoring
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Record metrics based on operation type
                if operation_type == "query":
                    monitor.record_query(func.__name__, duration)
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Performance monitored function {func.__name__} failed in {duration:.3f}s: {e}")
                raise
                
        return wrapper
    return decorator


# Global instances
cache = PerformanceCache()
monitor = PerformanceMonitor()


@asynccontextmanager
async def performance_context(operation_name: str):
    """
    Context manager for performance monitoring
    """
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        logger.info(f"Operation {operation_name} completed in {duration:.3f}s")


# Utility functions for performance optimization
async def batch_process(
    items: List[Any],
    processor: Callable,
    batch_size: int = 100,
    max_concurrency: int = 10
) -> List[Any]:
    """
    Process items in batches with controlled concurrency
    """
    results = []
    semaphore = asyncio.Semaphore(max_concurrency)
    
    async def process_batch(batch):
        async with semaphore:
            return await processor(batch)
    
    # Split items into batches
    batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
    
    # Process batches concurrently
    batch_results = await asyncio.gather(*[process_batch(batch) for batch in batches])
    
    # Flatten results
    for batch_result in batch_results:
        if isinstance(batch_result, list):
            results.extend(batch_result)
        else:
            results.append(batch_result)
    
    return results


def optimize_query(query_hint: str = ""):
    """
    Decorator for query optimization hints
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Add query optimization logic here
            # This could include query plan analysis, index hints, etc.
            return await func(*args, **kwargs)
        return wrapper
    return decorator
