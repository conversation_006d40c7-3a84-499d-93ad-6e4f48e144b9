"""
Blast-Radius Security Tool - Secrets Management System
Secure storage, rotation, and access control for sensitive data
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from dataclasses import dataclass, field
from contextlib import asynccontextmanager

import aiofiles
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import secrets

from app.core.advanced_security import SecurityLevel, audit_logger, ThreatLevel

logger = logging.getLogger(__name__)


class SecretType(Enum):
    """Types of secrets - these are type identifiers, not actual secret values"""
    API_KEY = "api_key"  # nosec B105 - This is a type identifier, not a hardcoded secret
    DATABASE_PASSWORD = "database_password"  # nosec B105 - This is a type identifier, not a hardcoded secret
    ENCRYPTION_KEY = "encryption_key"  # nosec B105 - This is a type identifier, not a hardcoded secret
    JWT_SECRET = "jwt_secret"  # nosec B105 - This is a type identifier, not a hardcoded secret
    OAUTH_SECRET = "oauth_secret"  # nosec B105 - This is a type identifier, not a hardcoded secret
    CERTIFICATE = "certificate"  # nosec B105 - This is a type identifier, not a hardcoded secret
    PRIVATE_KEY = "private_key"  # nosec B105 - This is a type identifier, not a hardcoded secret
    WEBHOOK_SECRET = "webhook_secret"  # nosec B105 - This is a type identifier, not a hardcoded secret


@dataclass
class SecretMetadata:
    """Metadata for a secret"""
    secret_id: str
    name: str
    secret_type: SecretType
    security_level: SecurityLevel
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    rotation_interval: Optional[timedelta]
    last_accessed: Optional[datetime]
    access_count: int = 0
    tags: List[str] = field(default_factory=list)
    description: str = ""
    
    def is_expired(self) -> bool:
        """Check if secret is expired"""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    def needs_rotation(self) -> bool:
        """Check if secret needs rotation"""
        if not self.rotation_interval:
            return False
        return datetime.now(timezone.utc) > (self.updated_at + self.rotation_interval)


@dataclass
class SecretAccessLog:
    """Access log entry for a secret"""
    secret_id: str
    user_id: str
    access_time: datetime
    access_type: str  # read, write, rotate, delete
    ip_address: str
    user_agent: str
    success: bool
    reason: Optional[str] = None


class SecretEncryption:
    """
    Advanced encryption for secrets storage
    """
    
    def __init__(self, master_key: Optional[bytes] = None):
        if master_key:
            self.master_key = master_key
        else:
            self.master_key = self._derive_master_key()
        
        self.fernet = Fernet(base64.urlsafe_b64encode(self.master_key[:32]))
    
    def _derive_master_key(self) -> bytes:
        """Derive master key from environment or generate new one"""
        # In production, this would come from a secure key management service
        master_key_env = os.getenv("BLAST_RADIUS_MASTER_KEY")
        
        if master_key_env:
            return base64.urlsafe_b64decode(master_key_env.encode())
        
        # Generate new key (for development only)
        password = os.getenv("BLAST_RADIUS_KEY_PASSWORD", "default_dev_password").encode()
        salt = os.getenv("BLAST_RADIUS_KEY_SALT", "default_dev_salt").encode()
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        return kdf.derive(password)
    
    def encrypt_secret(self, secret_value: str) -> str:
        """Encrypt secret value"""
        encrypted = self.fernet.encrypt(secret_value.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt_secret(self, encrypted_value: str) -> str:
        """Decrypt secret value"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_value.encode())
        decrypted = self.fernet.decrypt(encrypted_bytes)
        return decrypted.decode()
    
    def rotate_encryption_key(self) -> bytes:
        """Rotate encryption key"""
        new_key = Fernet.generate_key()
        # In production, this would involve re-encrypting all secrets
        logger.info("Encryption key rotation initiated")
        return new_key


class SecretsManager:
    """
    Comprehensive secrets management system
    """
    
    def __init__(self, storage_path: str = "/app/secrets"):
        self.storage_path = storage_path
        self.encryption = SecretEncryption()
        self.secrets_metadata: Dict[str, SecretMetadata] = {}
        self.access_logs: List[SecretAccessLog] = []
        self.access_policies: Dict[str, Dict[str, Any]] = {}
        
        # Ensure storage directory exists
        os.makedirs(storage_path, exist_ok=True)
        
        # Load existing secrets metadata
        asyncio.create_task(self._load_metadata())
    
    async def store_secret(
        self,
        name: str,
        value: str,
        secret_type: SecretType,
        security_level: SecurityLevel = SecurityLevel.CONFIDENTIAL,
        expires_at: Optional[datetime] = None,
        rotation_interval: Optional[timedelta] = None,
        tags: Optional[List[str]] = None,
        description: str = "",
        user_id: str = "system"
    ) -> str:
        """Store a new secret"""
        
        secret_id = f"{name}_{secrets.token_urlsafe(8)}"
        
        # Create metadata
        metadata = SecretMetadata(
            secret_id=secret_id,
            name=name,
            secret_type=secret_type,
            security_level=security_level,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            expires_at=expires_at,
            rotation_interval=rotation_interval,
            last_accessed=None,
            tags=tags or [],
            description=description
        )
        
        # Encrypt and store secret
        encrypted_value = self.encryption.encrypt_secret(value)
        
        secret_file_path = os.path.join(self.storage_path, f"{secret_id}.enc")
        async with aiofiles.open(secret_file_path, 'w') as f:
            await f.write(encrypted_value)
        
        # Store metadata
        self.secrets_metadata[secret_id] = metadata
        await self._save_metadata()
        
        # Log access
        await self._log_access(
            secret_id=secret_id,
            user_id=user_id,
            access_type="write",
            success=True,
            ip_address="internal",
            user_agent="secrets_manager"
        )
        
        # Audit log
        await audit_logger.log_security_event(
            event_type="secret_stored",
            description=f"Secret '{name}' stored with ID {secret_id}",
            severity=ThreatLevel.LOW,
            user_id=user_id,
            metadata={"secret_type": secret_type.value, "security_level": security_level.value}
        )
        
        logger.info(f"Secret '{name}' stored with ID {secret_id}")
        return secret_id
    
    async def retrieve_secret(
        self,
        secret_id: str,
        user_id: str,
        ip_address: str = "unknown",
        user_agent: str = "unknown"
    ) -> Optional[str]:
        """Retrieve a secret value"""
        
        if secret_id not in self.secrets_metadata:
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="read",
                success=False,
                reason="Secret not found",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return None
        
        metadata = self.secrets_metadata[secret_id]
        
        # Check if secret is expired
        if metadata.is_expired():
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="read",
                success=False,
                reason="Secret expired",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return None
        
        # Check access policy
        if not await self._check_access_policy(secret_id, user_id, "read"):
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="read",
                success=False,
                reason="Access denied by policy",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return None
        
        try:
            # Read encrypted secret
            secret_file_path = os.path.join(self.storage_path, f"{secret_id}.enc")
            async with aiofiles.open(secret_file_path, 'r') as f:
                encrypted_value = await f.read()
            
            # Decrypt secret
            secret_value = self.encryption.decrypt_secret(encrypted_value)
            
            # Update access metadata
            metadata.last_accessed = datetime.now(timezone.utc)
            metadata.access_count += 1
            await self._save_metadata()
            
            # Log successful access
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="read",
                success=True,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return secret_value
            
        except Exception as e:
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="read",
                success=False,
                reason=str(e),
                ip_address=ip_address,
                user_agent=user_agent
            )
            logger.error(f"Failed to retrieve secret {secret_id}: {e}")
            return None
    
    async def rotate_secret(
        self,
        secret_id: str,
        new_value: str,
        user_id: str,
        ip_address: str = "unknown",
        user_agent: str = "unknown"
    ) -> bool:
        """Rotate a secret value"""
        
        if secret_id not in self.secrets_metadata:
            return False
        
        metadata = self.secrets_metadata[secret_id]
        
        # Check access policy
        if not await self._check_access_policy(secret_id, user_id, "rotate"):
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="rotate",
                success=False,
                reason="Access denied by policy",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return False
        
        try:
            # Encrypt new value
            encrypted_value = self.encryption.encrypt_secret(new_value)
            
            # Store new encrypted value
            secret_file_path = os.path.join(self.storage_path, f"{secret_id}.enc")
            async with aiofiles.open(secret_file_path, 'w') as f:
                await f.write(encrypted_value)
            
            # Update metadata
            metadata.updated_at = datetime.now(timezone.utc)
            await self._save_metadata()
            
            # Log rotation
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="rotate",
                success=True,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Audit log
            await audit_logger.log_security_event(
                event_type="secret_rotated",
                description=f"Secret {secret_id} rotated",
                severity=ThreatLevel.LOW,
                user_id=user_id,
                metadata={"secret_name": metadata.name}
            )
            
            logger.info(f"Secret {secret_id} rotated by user {user_id}")
            return True
            
        except Exception as e:
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="rotate",
                success=False,
                reason=str(e),
                ip_address=ip_address,
                user_agent=user_agent
            )
            logger.error(f"Failed to rotate secret {secret_id}: {e}")
            return False
    
    async def delete_secret(
        self,
        secret_id: str,
        user_id: str,
        ip_address: str = "unknown",
        user_agent: str = "unknown"
    ) -> bool:
        """Delete a secret"""
        
        if secret_id not in self.secrets_metadata:
            return False
        
        metadata = self.secrets_metadata[secret_id]
        
        # Check access policy
        if not await self._check_access_policy(secret_id, user_id, "delete"):
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="delete",
                success=False,
                reason="Access denied by policy",
                ip_address=ip_address,
                user_agent=user_agent
            )
            return False
        
        try:
            # Delete encrypted file
            secret_file_path = os.path.join(self.storage_path, f"{secret_id}.enc")
            if os.path.exists(secret_file_path):
                os.remove(secret_file_path)
            
            # Remove metadata
            del self.secrets_metadata[secret_id]
            await self._save_metadata()
            
            # Log deletion
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="delete",
                success=True,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Audit log
            await audit_logger.log_security_event(
                event_type="secret_deleted",
                description=f"Secret {secret_id} deleted",
                severity=ThreatLevel.MEDIUM,
                user_id=user_id,
                metadata={"secret_name": metadata.name}
            )
            
            logger.info(f"Secret {secret_id} deleted by user {user_id}")
            return True
            
        except Exception as e:
            await self._log_access(
                secret_id=secret_id,
                user_id=user_id,
                access_type="delete",
                success=False,
                reason=str(e),
                ip_address=ip_address,
                user_agent=user_agent
            )
            logger.error(f"Failed to delete secret {secret_id}: {e}")
            return False
    
    async def list_secrets(
        self,
        user_id: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """List secrets (metadata only) with optional filters"""
        
        secrets_list = []
        
        for secret_id, metadata in self.secrets_metadata.items():
            # Check if user has read access
            if not await self._check_access_policy(secret_id, user_id, "read"):
                continue
            
            # Apply filters
            if filters:
                if "secret_type" in filters and metadata.secret_type != filters["secret_type"]:
                    continue
                if "security_level" in filters and metadata.security_level != filters["security_level"]:
                    continue
                if "tags" in filters:
                    if not any(tag in metadata.tags for tag in filters["tags"]):
                        continue
            
            secrets_list.append({
                "secret_id": secret_id,
                "name": metadata.name,
                "secret_type": metadata.secret_type.value,
                "security_level": metadata.security_level.value,
                "created_at": metadata.created_at.isoformat(),
                "updated_at": metadata.updated_at.isoformat(),
                "expires_at": metadata.expires_at.isoformat() if metadata.expires_at else None,
                "last_accessed": metadata.last_accessed.isoformat() if metadata.last_accessed else None,
                "access_count": metadata.access_count,
                "tags": metadata.tags,
                "description": metadata.description,
                "is_expired": metadata.is_expired(),
                "needs_rotation": metadata.needs_rotation()
            })
        
        return secrets_list
    
    async def get_secrets_requiring_rotation(self) -> List[str]:
        """Get list of secrets that need rotation"""
        return [
            secret_id for secret_id, metadata in self.secrets_metadata.items()
            if metadata.needs_rotation()
        ]
    
    async def set_access_policy(
        self,
        secret_id: str,
        policy: Dict[str, Any]
    ):
        """Set access policy for a secret"""
        self.access_policies[secret_id] = policy
        logger.info(f"Access policy set for secret {secret_id}")
    
    async def _check_access_policy(
        self,
        secret_id: str,
        user_id: str,
        operation: str
    ) -> bool:
        """Check if user has permission for operation on secret"""
        
        if secret_id not in self.access_policies:
            # Default policy: allow all operations for now
            return True
        
        policy = self.access_policies[secret_id]
        
        # Check user-specific permissions
        if "users" in policy:
            user_perms = policy["users"].get(user_id, [])
            return operation in user_perms
        
        # Check role-based permissions (would integrate with user management)
        # For now, return True
        return True
    
    async def _log_access(
        self,
        secret_id: str,
        user_id: str,
        access_type: str,
        success: bool,
        ip_address: str,
        user_agent: str,
        reason: Optional[str] = None
    ):
        """Log secret access"""
        
        access_log = SecretAccessLog(
            secret_id=secret_id,
            user_id=user_id,
            access_time=datetime.now(timezone.utc),
            access_type=access_type,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            reason=reason
        )
        
        self.access_logs.append(access_log)
        
        # Keep only recent logs (last 1000 entries)
        if len(self.access_logs) > 1000:
            self.access_logs = self.access_logs[-1000:]
    
    async def _load_metadata(self):
        """Load secrets metadata from storage"""
        metadata_file = os.path.join(self.storage_path, "metadata.json")
        
        if not os.path.exists(metadata_file):
            return
        
        try:
            async with aiofiles.open(metadata_file, 'r') as f:
                metadata_json = await f.read()
            
            metadata_dict = json.loads(metadata_json)
            
            for secret_id, metadata_data in metadata_dict.items():
                metadata = SecretMetadata(
                    secret_id=metadata_data["secret_id"],
                    name=metadata_data["name"],
                    secret_type=SecretType(metadata_data["secret_type"]),
                    security_level=SecurityLevel(metadata_data["security_level"]),
                    created_at=datetime.fromisoformat(metadata_data["created_at"]),
                    updated_at=datetime.fromisoformat(metadata_data["updated_at"]),
                    expires_at=datetime.fromisoformat(metadata_data["expires_at"]) if metadata_data.get("expires_at") else None,
                    rotation_interval=timedelta(seconds=metadata_data["rotation_interval"]) if metadata_data.get("rotation_interval") else None,
                    last_accessed=datetime.fromisoformat(metadata_data["last_accessed"]) if metadata_data.get("last_accessed") else None,
                    access_count=metadata_data.get("access_count", 0),
                    tags=metadata_data.get("tags", []),
                    description=metadata_data.get("description", "")
                )
                
                self.secrets_metadata[secret_id] = metadata
            
            logger.info(f"Loaded metadata for {len(self.secrets_metadata)} secrets")
            
        except Exception as e:
            logger.error(f"Failed to load secrets metadata: {e}")
    
    async def _save_metadata(self):
        """Save secrets metadata to storage"""
        metadata_file = os.path.join(self.storage_path, "metadata.json")
        
        metadata_dict = {}
        for secret_id, metadata in self.secrets_metadata.items():
            metadata_dict[secret_id] = {
                "secret_id": metadata.secret_id,
                "name": metadata.name,
                "secret_type": metadata.secret_type.value,
                "security_level": metadata.security_level.value,
                "created_at": metadata.created_at.isoformat(),
                "updated_at": metadata.updated_at.isoformat(),
                "expires_at": metadata.expires_at.isoformat() if metadata.expires_at else None,
                "rotation_interval": metadata.rotation_interval.total_seconds() if metadata.rotation_interval else None,
                "last_accessed": metadata.last_accessed.isoformat() if metadata.last_accessed else None,
                "access_count": metadata.access_count,
                "tags": metadata.tags,
                "description": metadata.description
            }
        
        try:
            async with aiofiles.open(metadata_file, 'w') as f:
                await f.write(json.dumps(metadata_dict, indent=2))
        except Exception as e:
            logger.error(f"Failed to save secrets metadata: {e}")
    
    def get_access_logs(
        self,
        secret_id: Optional[str] = None,
        user_id: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get access logs with optional filtering"""
        
        filtered_logs = self.access_logs
        
        if secret_id:
            filtered_logs = [log for log in filtered_logs if log.secret_id == secret_id]
        
        if user_id:
            filtered_logs = [log for log in filtered_logs if log.user_id == user_id]
        
        # Sort by access time (most recent first) and limit
        filtered_logs.sort(key=lambda x: x.access_time, reverse=True)
        filtered_logs = filtered_logs[:limit]
        
        return [
            {
                "secret_id": log.secret_id,
                "user_id": log.user_id,
                "access_time": log.access_time.isoformat(),
                "access_type": log.access_type,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "success": log.success,
                "reason": log.reason
            }
            for log in filtered_logs
        ]


# Global secrets manager instance
secrets_manager = SecretsManager()


# Utility functions
async def get_secret(name: str, user_id: str = "system") -> Optional[str]:
    """Convenience function to get secret by name"""
    # Find secret by name
    for secret_id, metadata in secrets_manager.secrets_metadata.items():
        if metadata.name == name:
            return await secrets_manager.retrieve_secret(secret_id, user_id)
    return None


async def store_secret_by_name(
    name: str,
    value: str,
    secret_type: SecretType = SecretType.API_KEY,
    **kwargs
) -> str:
    """Convenience function to store secret by name"""
    return await secrets_manager.store_secret(
        name=name,
        value=value,
        secret_type=secret_type,
        **kwargs
    )
