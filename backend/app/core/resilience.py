"""
Blast-Radius Security Tool - Resilience and Fault Tolerance Framework
Implements circuit breakers, retry mechanisms, and graceful degradation patterns
"""

import asyncio
import time
import logging
import secrets
from enum import Enum
from typing import Any, Callable, Dict, Optional, Union
from functools import wraps
from dataclasses import dataclass, field
from contextlib import asynccontextmanager

import aioredis
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: int = 30
    expected_exception: tuple = (Exception,)
    name: str = "default"


@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics"""
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[float] = None
    state: CircuitState = CircuitState.CLOSED
    total_requests: int = 0
    
    def reset(self):
        """Reset statistics"""
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.total_requests = 0


class CircuitBreaker:
    """
    Circuit breaker implementation for fault tolerance
    
    Prevents cascading failures by monitoring service health
    and temporarily blocking requests to failing services.
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.stats = CircuitBreakerStats()
        self._lock = asyncio.Lock()
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._check_state()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if exc_type is None:
            await self._record_success()
        elif isinstance(exc_val, self.config.expected_exception):
            await self._record_failure()
        return False
    
    async def _check_state(self):
        """Check and update circuit breaker state"""
        async with self._lock:
            current_time = time.time()
            
            if self.stats.state == CircuitState.OPEN:
                if (self.stats.last_failure_time and 
                    current_time - self.stats.last_failure_time >= self.config.recovery_timeout):
                    self.stats.state = CircuitState.HALF_OPEN
                    logger.info(f"Circuit breaker {self.config.name} moved to HALF_OPEN")
                else:
                    raise CircuitBreakerOpenException(
                        f"Circuit breaker {self.config.name} is OPEN"
                    )
            
            self.stats.total_requests += 1
    
    async def _record_success(self):
        """Record successful operation"""
        async with self._lock:
            self.stats.success_count += 1
            
            if self.stats.state == CircuitState.HALF_OPEN:
                self.stats.state = CircuitState.CLOSED
                self.stats.failure_count = 0
                logger.info(f"Circuit breaker {self.config.name} moved to CLOSED")
    
    async def _record_failure(self):
        """Record failed operation"""
        async with self._lock:
            self.stats.failure_count += 1
            self.stats.last_failure_time = time.time()
            
            if (self.stats.state == CircuitState.CLOSED and 
                self.stats.failure_count >= self.config.failure_threshold):
                self.stats.state = CircuitState.OPEN
                logger.warning(f"Circuit breaker {self.config.name} moved to OPEN")
            elif self.stats.state == CircuitState.HALF_OPEN:
                self.stats.state = CircuitState.OPEN
                logger.warning(f"Circuit breaker {self.config.name} moved back to OPEN")


class CircuitBreakerOpenException(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class RetryConfig:
    """Configuration for retry mechanism"""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


async def retry_with_backoff(
    func: Callable,
    config: RetryConfig,
    *args,
    **kwargs
) -> Any:
    """
    Retry function with exponential backoff and jitter
    """
    
    last_exception = None
    
    for attempt in range(config.max_attempts):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            
            if attempt == config.max_attempts - 1:
                break
            
            # Calculate delay with exponential backoff
            delay = min(
                config.base_delay * (config.exponential_base ** attempt),
                config.max_delay
            )
            
            # Add jitter to prevent thundering herd
            if config.jitter:
                # Use secrets.SystemRandom for cryptographically secure randomness
                secure_random = secrets.SystemRandom()
                delay *= (0.5 + secure_random.random() * 0.5)
            
            logger.warning(
                f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s: {e}"
            )
            await asyncio.sleep(delay)
    
    raise last_exception


def circuit_breaker(config: CircuitBreakerConfig):
    """
    Decorator for circuit breaker pattern
    """
    breaker = CircuitBreaker(config)
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            async with breaker:
                return await func(*args, **kwargs)
        return wrapper
    return decorator


def retry(config: RetryConfig):
    """
    Decorator for retry pattern
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await retry_with_backoff(func, config, *args, **kwargs)
        return wrapper
    return decorator


class GracefulDegradation:
    """
    Graceful degradation manager for maintaining service availability
    during partial system failures
    """
    
    def __init__(self):
        self.service_health: Dict[str, bool] = {}
        self.fallback_handlers: Dict[str, Callable] = {}
        self.cache_handlers: Dict[str, Callable] = {}
    
    def register_service(self, name: str, health_check: Callable):
        """Register a service with health check"""
        self.service_health[name] = True
        # Store health check for periodic monitoring
    
    def register_fallback(self, service: str, handler: Callable):
        """Register fallback handler for service"""
        self.fallback_handlers[service] = handler
    
    def register_cache_handler(self, service: str, handler: Callable):
        """Register cache handler for service"""
        self.cache_handlers[service] = handler
    
    async def call_with_fallback(
        self,
        service: str,
        primary_func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """
        Call service with fallback to cached data or degraded functionality
        """
        try:
            # Try primary service
            result = await primary_func(*args, **kwargs)
            self.service_health[service] = True
            return result
            
        except Exception as e:
            logger.warning(f"Service {service} failed: {e}")
            self.service_health[service] = False
            
            # Try cache handler first
            if service in self.cache_handlers:
                try:
                    cached_result = await self.cache_handlers[service](*args, **kwargs)
                    logger.info(f"Serving cached data for {service}")
                    return cached_result
                except Exception as cache_error:
                    logger.warning(f"Cache handler failed for {service}: {cache_error}")
            
            # Fall back to degraded functionality
            if service in self.fallback_handlers:
                try:
                    fallback_result = await self.fallback_handlers[service](*args, **kwargs)
                    logger.info(f"Using fallback handler for {service}")
                    return fallback_result
                except Exception as fallback_error:
                    logger.error(f"Fallback handler failed for {service}: {fallback_error}")
            
            # If all else fails, raise the original exception
            raise e
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        total_services = len(self.service_health)
        healthy_services = sum(1 for health in self.service_health.values() if health)
        
        return {
            "overall_health": healthy_services / total_services if total_services > 0 else 1.0,
            "healthy_services": healthy_services,
            "total_services": total_services,
            "service_status": self.service_health.copy(),
            "degraded_mode": healthy_services < total_services
        }


# Global instances
degradation_manager = GracefulDegradation()

# Circuit breaker configurations for different services
DATABASE_CIRCUIT_CONFIG = CircuitBreakerConfig(
    failure_threshold=5,
    recovery_timeout=30,
    expected_exception=(SQLAlchemyError, ConnectionError),
    name="database"
)

REDIS_CIRCUIT_CONFIG = CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=15,
    expected_exception=(aioredis.RedisError, ConnectionError),
    name="redis"
)

EXTERNAL_API_CIRCUIT_CONFIG = CircuitBreakerConfig(
    failure_threshold=10,
    recovery_timeout=60,
    expected_exception=(HTTPException, ConnectionError, TimeoutError),
    name="external_api"
)

# Retry configurations
DEFAULT_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=30.0
)

EXTERNAL_API_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=2.0,
    max_delay=60.0
)


@asynccontextmanager
async def resilient_operation(
    operation_name: str,
    circuit_config: CircuitBreakerConfig,
    retry_config: Optional[RetryConfig] = None
):
    """
    Context manager for resilient operations with circuit breaker and retry
    """
    breaker = CircuitBreaker(circuit_config)
    
    async def execute_with_retry(func):
        if retry_config:
            return await retry_with_backoff(func, retry_config)
        else:
            return await func()
    
    try:
        async with breaker:
            yield execute_with_retry
    except CircuitBreakerOpenException:
        logger.error(f"Circuit breaker open for {operation_name}")
        raise
    except Exception as e:
        logger.error(f"Operation {operation_name} failed: {e}")
        raise


# Health check utilities
async def check_database_health() -> bool:
    """Check database connectivity"""
    try:
        from app.database import get_db
        async with get_db() as db:
            await db.execute("SELECT 1")
        return True
    except Exception:
        return False


async def check_redis_health() -> bool:
    """Check Redis connectivity"""
    try:
        from app.cache import get_redis
        redis = await get_redis()
        await redis.ping()
        return True
    except Exception:
        return False


async def check_external_api_health(url: str) -> bool:
    """Check external API health"""
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                return response.status < 500
    except Exception:
        return False
