# Database Migration Guide

## 🎯 Overview

This guide covers the database migration process for the Blast-Radius Security Tool, including the new robust asset models with soft-delete capabilities, audit trails, and extended metadata.

## 📋 Migration Summary

### **New Database Models Added**

1. **`assets_robust`** - Enhanced asset model with soft-delete and audit capabilities
2. **`asset_audit_log`** - Comprehensive audit trail for all changes
3. **`asset_versions`** - Version control and point-in-time recovery
4. **`asset_locks`** - Concurrent access control
5. **`asset_validations`** - Data quality assurance
6. **`asset_backups`** - Backup and recovery tracking
7. **`asset_vulnerabilities`** - Vulnerability management
8. **`asset_compliance`** - Multi-framework compliance tracking
9. **`asset_metrics`** - Performance and health metrics
10. **`asset_dependencies`** - Asset dependency mapping
11. **`asset_configurations`** - Configuration change management
12. **`asset_certificates`** - SSL/TLS certificate lifecycle
13. **`asset_network_interfaces`** - Network interface details

### **Migration Files**

- **`001_add_robust_asset_models.py`** - Core robust asset models
- **`002_add_extended_asset_models.py`** - Extended metadata models
- **`003_add_performance_indexes.py`** - Performance optimization indexes

## 🚀 Quick Start

### **1. Security Updates**
```bash
# Update dependencies to latest secure versions
./scripts/security_update.sh install-tools
./scripts/security_update.sh full
```

### **2. Database Migration**
```bash
# Check current migration status
./scripts/migrate_database.sh status

# Run migrations (includes automatic backup)
./scripts/migrate_database.sh migrate
```

## 📊 Detailed Migration Process

### **Step 1: Pre-Migration Checklist**

#### ✅ **Environment Preparation**
```bash
# 1. Verify database connection
./scripts/migrate_database.sh status

# 2. Create manual backup (recommended)
./scripts/migrate_database.sh backup

# 3. Check disk space (migrations add ~50MB+ for indexes)
df -h

# 4. Verify Python dependencies
cd backend && pip install -r requirements.txt
```

#### ✅ **Security Updates**
```bash
# Update all dependencies to latest secure versions
./scripts/security_update.sh update

# Run security scan
./scripts/security_update.sh scan
```

### **Step 2: Migration Execution**

#### **Option A: Automated Migration (Recommended)**
```bash
# Run complete migration with automatic backup
./scripts/migrate_database.sh migrate
```

#### **Option B: Manual Step-by-Step Migration**
```bash
# 1. Create backup
./scripts/migrate_database.sh backup

# 2. Check migration status
./scripts/migrate_database.sh status

# 3. Validate migration files
./scripts/migrate_database.sh validate

# 4. Run migrations
cd backend
alembic upgrade head

# 5. Verify completion
./scripts/migrate_database.sh status
```

### **Step 3: Post-Migration Verification**

#### ✅ **Database Verification**
```bash
# Check migration status
./scripts/migrate_database.sh status

# Verify table creation
psql -d blast_radius -c "\dt"

# Check indexes
psql -d blast_radius -c "\di"

# Verify constraints
psql -d blast_radius -c "SELECT conname, contype FROM pg_constraint WHERE contype IN ('c', 'f', 'u');"
```

#### ✅ **Application Testing**
```bash
# Run application tests
cd backend
python -m pytest tests/ -v

# Test API endpoints
curl -X GET http://localhost:8000/api/v1/health

# Test robust asset endpoints
curl -X GET http://localhost:8000/api/v1/robust-assets/retention/statistics
```

## 🔧 Migration Details

### **Database Schema Changes**

#### **New Enum Types**
- `auditaction` - Audit action types (create, update, delete, etc.)
- `dataretentionpolicy` - Data retention policies (immediate, short_term, etc.)
- `vulnerabilityseverity` - Vulnerability severity levels
- `complianceframework` - Compliance frameworks (SOC2, ISO27001, etc.)

#### **New Tables Structure**

##### **`assets_robust`** (Enhanced Asset Model)
- **Soft Delete**: `is_deleted`, `deleted_at`, `deleted_by`, `deletion_reason`
- **Retention**: `retention_policy`, `purge_after`
- **Audit**: `created_at`, `updated_at`, `version`, `change_count`, `checksum`
- **Business Context**: `business_criticality`, `revenue_impact`, `customer_impact`
- **Data Classification**: `data_classification`, `contains_pii`, `gdpr_applicable`

##### **`asset_audit_log`** (Comprehensive Audit Trail)
- **Change Tracking**: Field-level old/new values
- **User Attribution**: `user_id`, `session_id`, `ip_address`
- **Context**: `change_reason`, `change_source`, `correlation_id`
- **Snapshots**: `before_snapshot`, `after_snapshot`
- **Compliance**: `compliance_tags`, `is_sensitive`

##### **Performance Indexes** (60+ Strategic Indexes)
- **Query Optimization**: Asset lookups, filtering, sorting
- **Audit Performance**: Time-based queries, user tracking
- **Compliance Queries**: Framework-specific searches
- **Monitoring**: Metrics and health status queries

### **Data Migration Considerations**

#### **Existing Data Compatibility**
- ✅ **Backward Compatible**: Existing `assets` table remains unchanged
- ✅ **Parallel Operation**: New `assets_robust` table operates alongside existing tables
- ✅ **Gradual Migration**: Can migrate data incrementally

#### **Storage Requirements**
- **Base Tables**: ~10MB for schema
- **Indexes**: ~50MB for performance optimization
- **Audit Data**: Grows with usage (estimate 1KB per change)
- **Version Data**: Compressed storage for historical versions

## 🛠️ Troubleshooting

### **Common Migration Issues**

#### **Issue 1: Database Connection Failed**
```bash
# Check database status
systemctl status postgresql

# Verify connection string
echo $DATABASE_URL

# Test connection manually
psql $DATABASE_URL -c "SELECT 1"
```

#### **Issue 2: Migration Timeout**
```bash
# Increase statement timeout
psql -d blast_radius -c "SET statement_timeout = '10min';"

# Run migration in parts
alembic upgrade 001_robust_assets
alembic upgrade 002_extended_assets
alembic upgrade 003_performance_indexes
```

#### **Issue 3: Insufficient Disk Space**
```bash
# Check available space
df -h

# Clean up old backups
find . -name "backup_*.sql" -mtime +7 -delete

# Compress old audit logs (if needed)
psql -d blast_radius -c "UPDATE asset_audit_log SET compression_type = 'gzip' WHERE timestamp < NOW() - INTERVAL '90 days';"
```

#### **Issue 4: Index Creation Slow**
```bash
# Create indexes concurrently (PostgreSQL)
psql -d blast_radius -c "CREATE INDEX CONCURRENTLY idx_name ON table_name (column);"

# Monitor index creation progress
psql -d blast_radius -c "SELECT * FROM pg_stat_progress_create_index;"
```

### **Rollback Procedures**

#### **Emergency Rollback**
```bash
# Rollback to previous state
./scripts/migrate_database.sh rollback 001_robust_assets

# Restore from backup (if needed)
psql -d blast_radius < backup_YYYYMMDD_HHMMSS.sql
```

#### **Partial Rollback**
```bash
# Rollback specific migration
alembic downgrade 002_extended_assets

# Verify state
./scripts/migrate_database.sh status
```

## 📊 Performance Impact

### **Expected Performance Changes**

#### **Positive Impacts**
- ✅ **60+ Strategic Indexes**: Faster queries for common operations
- ✅ **Optimized Joins**: Improved relationship queries
- ✅ **Efficient Filtering**: Better performance for asset searches
- ✅ **Audit Queries**: Fast access to change history

#### **Considerations**
- ⚠️ **Initial Migration**: 5-15 minutes depending on data size
- ⚠️ **Storage Increase**: ~50MB for indexes, growing with audit data
- ⚠️ **Write Performance**: Slight overhead for audit logging
- ⚠️ **Memory Usage**: Additional memory for index caching

### **Performance Monitoring**
```bash
# Monitor query performance
psql -d blast_radius -c "SELECT * FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"

# Check index usage
psql -d blast_radius -c "SELECT * FROM pg_stat_user_indexes WHERE idx_scan = 0;"

# Monitor table sizes
psql -d blast_radius -c "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

## 🎯 Post-Migration Tasks

### **1. Application Configuration**
```python
# Update application to use robust asset service
from app.services.robust_asset_service import RobustAssetService

# Configure data retention policies
retention_service = DataRetentionService(db)
retention_service.schedule_cleanup_job()
```

### **2. Monitoring Setup**
```bash
# Set up automated cleanup
crontab -e
# Add: 0 2 * * * /path/to/scripts/data_retention_cleanup.sh

# Configure alerting for failed migrations
# Set up monitoring for audit log growth
```

### **3. Documentation Updates**
- ✅ Update API documentation with new endpoints
- ✅ Train team on new audit capabilities
- ✅ Document data retention policies
- ✅ Update backup procedures

## 🎉 Success Criteria

### **Migration Complete When:**
- ✅ All 3 migration files applied successfully
- ✅ 60+ indexes created without errors
- ✅ Application tests pass
- ✅ API endpoints respond correctly
- ✅ Audit logging functional
- ✅ Soft-delete operations working
- ✅ Performance within acceptable ranges

### **Verification Commands**
```bash
# Check migration status
./scripts/migrate_database.sh status

# Verify table count
psql -d blast_radius -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"

# Test robust asset creation
curl -X POST http://localhost:8000/api/v1/robust-assets/ -H "Content-Type: application/json" -d '{"name": "test-asset", "asset_type": "server", "provider": "aws", "status": "active"}'

# Check audit logging
psql -d blast_radius -c "SELECT COUNT(*) FROM asset_audit_log;"
```

## 📞 Support

### **Getting Help**
- 📧 **Issues**: Create GitHub issue with migration logs
- 📚 **Documentation**: Check README.md and API docs
- 🔧 **Scripts**: Use provided migration and security scripts
- 🧪 **Testing**: Run comprehensive test suite before production

### **Emergency Contacts**
- **Database Issues**: Check PostgreSQL logs
- **Application Errors**: Check application logs in `logs/`
- **Performance Issues**: Monitor system resources and query performance
