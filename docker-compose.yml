version: '3.8'

networks:
  blastradius_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  traefik_network:
    external: true

services:
  # PostgreSQL Database
  postgresql:
    build:
      context: .dockerwrapper/postgresql
      dockerfile: Dockerfile
    container_name: blastradius_postgresql
    environment:
      POSTGRES_DB: blastradius
      POSTGRES_USER: blastradius
      POSTGRES_PASSWORD: blastradius_secure_password_2024
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - postgresql_logs:/var/log/postgresql
    networks:
      - blastradius_network
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U blastradius -d blastradius"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis Cache
  redis:
    build:
      context: .dockerwrapper/redis
      dockerfile: Dockerfile
    container_name: blastradius_redis
    volumes:
      - redis_data:/data
    networks:
      - blastradius_network
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Neo4j Graph Database
  neo4j:
    build:
      context: .dockerwrapper/neo4j
      dockerfile: Dockerfile
    container_name: blastradius_neo4j
    environment:
      NEO4J_AUTH: neo4j/blastradius_neo4j_secure_password_2024
      NEO4J_PLUGINS: '["apoc", "graph-data-science"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*,gds.*
      NEO4J_dbms_security_procedures_allowlist: apoc.*,gds.*
      NEO4J_server_memory_heap_initial__size: 1G
      NEO4J_server_memory_heap_max__size: 2G
      NEO4J_server_memory_pagecache_size: 1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_plugins:/var/lib/neo4j/plugins
    networks:
      - blastradius_network
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "blastradius_neo4j_secure_password_2024", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: blastradius_backend
    environment:
      # Application
      ENVIRONMENT: ${ENVIRONMENT:-production}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-info}

      # Database
      DATABASE_URL: *************************************************************************/blastradius

      # Redis
      REDIS_URL: redis://redis:6379/0

      # Security
      SECRET_KEY: ${SECRET_KEY:-change-this-in-production}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-7}

      # CORS
      CORS_ORIGINS: ${CORS_ORIGINS:-["http://localhost:3000","https://blastradius.local"]}
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    networks:
      - blastradius_network
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.blastradius.local`) || PathPrefix(`/api`)"
      - "traefik.http.routers.backend.entrypoints=web,websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"

  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: blastradius_traefik
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --log.level=INFO
      - --accesslog=true
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_letsencrypt:/letsencrypt
    networks:
      - traefik_network
      - blastradius_network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.blastradius.local`)"
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.entrypoints=web"

volumes:
  postgresql_data:
    driver: local
  postgresql_logs:
    driver: local
  redis_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_plugins:
    driver: local
  traefik_letsencrypt:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
