/**
 * Cucumber configuration
 */

module.exports = {
  default: {
    require: [
      'features/step-definitions/**/*.ts'
    ],
    requireModule: [
      'ts-node/register'
    ],
    format: [
      'progress-bar',
      'html:reports/cucumber-report.html',
      'json:reports/cucumber-report.json',
      'junit:reports/cucumber-report.xml'
    ],
    formatOptions: {
      snippetInterface: 'async-await'
    },
    publishQuiet: true,
    dryRun: false,
    failFast: false,
    strict: true,
    worldParameters: {
      headless: process.env.CI === 'true',
      slowMo: process.env.CI === 'true' ? 0 : 100
    }
  }
};
