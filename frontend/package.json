{"name": "blast-radius-frontend", "version": "0.0.1", "description": "Blast-Radius Security Tool Frontend", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@reduxjs/toolkit": "^1.9.7", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "d3": "^7.8.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-redux": "^8.1.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.2", "web-vitals": "^3.5.0"}, "devDependencies": {"@cucumber/cucumber": "^10.0.1", "@cucumber/playwright": "^1.0.0", "@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/d3": "^7.4.3", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:e2e": "playwright test", "test:bdd": "cucumber-js features/**/*.feature --require-module ts-node/register --require features/step-definitions/**/*.ts", "test:bdd:report": "cucumber-js features/**/*.feature --require-module ts-node/register --require features/step-definitions/**/*.ts --format html:reports/cucumber-report.html", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/reportWebVitals.ts"], "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}}, "proxy": "http://localhost:8000"}