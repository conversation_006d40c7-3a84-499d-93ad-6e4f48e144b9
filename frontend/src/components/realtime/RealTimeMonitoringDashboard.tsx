/**
 * Real-Time Monitoring Dashboard
 * 
 * Main dashboard component providing real-time security monitoring with:
 * - Live threat visualization
 * - Attack timeline
 * - Risk heatmaps
 * - MITRE ATT&CK integration
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon
} from '@mui/icons-material';

import { ThreatMapVisualization } from './ThreatMapVisualization';
import { AttackTimelineVisualization } from './AttackTimelineVisualization';
import { RiskHeatmapVisualization } from './RiskHeatmapVisualization';
import { MitreVisualization } from './MitreVisualization';
import { RealTimeEventsFeed } from './RealTimeEventsFeed';
import { DashboardMetrics } from './DashboardMetrics';
import { useWebSocket } from '../../hooks/useWebSocket';
import { useRealTimeData } from '../../hooks/useRealTimeData';

interface DashboardOverview {
  total_assets: number;
  high_risk_assets: number;
  active_threats: number;
  mitre_techniques_detected: number;
  last_updated: string;
}

interface RealTimeEvent {
  id: string;
  event_type: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  asset_id?: number;
  asset_name?: string;
  mitre_technique_id?: string;
  risk_score?: number;
  metadata?: Record<string, any>;
}

export const RealTimeMonitoringDashboard: React.FC = () => {
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [recentEvents, setRecentEvents] = useState<RealTimeEvent[]>([]);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // WebSocket connection for real-time updates
  const {
    isConnected,
    connectionError,
    sendMessage,
    lastMessage
  } = useWebSocket('/api/v1/realtime/ws');
  
  // Real-time data hooks
  const {
    threatMapData,
    riskHeatmapData,
    attackTimelineData,
    mitreVisualizationData,
    refreshData
  } = useRealTimeData();

  // Handle incoming WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    try {
      const message = JSON.parse(lastMessage);
      
      switch (message.type) {
        case 'initial_data':
          setOverview(message.data.overview);
          setRecentEvents(message.data.recent_events || []);
          setIsLoading(false);
          break;
          
        case 'realtime_event':
          if (isRealTimeEnabled) {
            const newEvent = message.data;
            setRecentEvents(prev => [newEvent, ...prev.slice(0, 49)]); // Keep last 50 events
            setLastUpdated(new Date());
          }
          break;
          
        case 'dashboard_overview':
          setOverview(message.data.overview);
          setRecentEvents(message.data.recent_events || []);
          break;
          
        case 'ping':
          // Respond to server ping
          sendMessage(JSON.stringify({ type: 'pong' }));
          break;
          
        case 'error':
          setError(message.message);
          break;
      }
    } catch (err) {
      console.error('Error parsing WebSocket message:', err);
    }
  }, [lastMessage, isRealTimeEnabled, sendMessage]);

  // Request initial data when connected
  useEffect(() => {
    if (isConnected) {
      sendMessage(JSON.stringify({ type: 'get_dashboard_overview' }));
      setError(null);
    }
  }, [isConnected, sendMessage]);

  // Handle connection errors
  useEffect(() => {
    if (connectionError) {
      setError(`Connection error: ${connectionError}`);
      setIsLoading(false);
    }
  }, [connectionError]);

  // Auto-refresh data periodically
  useEffect(() => {
    if (isRealTimeEnabled && isConnected) {
      refreshIntervalRef.current = setInterval(() => {
        sendMessage(JSON.stringify({ type: 'get_dashboard_overview' }));
        refreshData();
      }, 30000); // Refresh every 30 seconds
    } else {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [isRealTimeEnabled, isConnected, sendMessage, refreshData]);

  const handleManualRefresh = useCallback(() => {
    setIsLoading(true);
    sendMessage(JSON.stringify({ type: 'get_dashboard_overview' }));
    refreshData();
  }, [sendMessage, refreshData]);

  const handleRealTimeToggle = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setIsRealTimeEnabled(event.target.checked);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  if (isLoading && !overview) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading Real-Time Monitoring Dashboard...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Dashboard Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Real-Time Security Monitoring
        </Typography>
        
        <Box display="flex" alignItems="center" gap={2}>
          {/* Connection Status */}
          <Chip
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
          
          {/* Real-time Toggle */}
          <FormControlLabel
            control={
              <Switch
                checked={isRealTimeEnabled}
                onChange={handleRealTimeToggle}
                color="primary"
              />
            }
            label="Real-time Updates"
          />
          
          {/* Control Buttons */}
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleManualRefresh} disabled={isLoading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Settings">
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Fullscreen">
            <IconButton>
              <FullscreenIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Dashboard Metrics */}
      {overview && (
        <DashboardMetrics
          overview={overview}
          lastUpdated={lastUpdated}
          isRealTime={isRealTimeEnabled}
        />
      )}

      {/* Main Dashboard Grid */}
      <Grid container spacing={3}>
        {/* Threat Map */}
        <Grid item xs={12} lg={8}>
          <Card sx={{ height: '500px' }}>
            <CardContent sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Global Threat Map
              </Typography>
              <ThreatMapVisualization data={threatMapData} />
            </CardContent>
          </Card>
        </Grid>

        {/* Real-Time Events Feed */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '500px' }}>
            <CardContent sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Live Security Events
              </Typography>
              <RealTimeEventsFeed
                events={recentEvents}
                isRealTime={isRealTimeEnabled}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Attack Timeline */}
        <Grid item xs={12} lg={8}>
          <Card sx={{ height: '400px' }}>
            <CardContent sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Attack Timeline (24h)
              </Typography>
              <AttackTimelineVisualization data={attackTimelineData} />
            </CardContent>
          </Card>
        </Grid>

        {/* Risk Heatmap */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '400px' }}>
            <CardContent sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Asset Risk Heatmap
              </Typography>
              <RiskHeatmapVisualization data={riskHeatmapData} />
            </CardContent>
          </Card>
        </Grid>

        {/* MITRE ATT&CK Visualization */}
        <Grid item xs={12}>
          <Card sx={{ height: '600px' }}>
            <CardContent sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                MITRE ATT&CK Technique Activity
              </Typography>
              <MitreVisualization data={mitreVisualizationData} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Footer with Last Updated */}
      <Box mt={3} textAlign="center">
        <Typography variant="caption" color="textSecondary">
          Last updated: {lastUpdated.toLocaleString()} | 
          Real-time monitoring: {isRealTimeEnabled ? 'Enabled' : 'Disabled'} |
          Connection: {isConnected ? 'Active' : 'Inactive'}
        </Typography>
      </Box>
    </Box>
  );
};

export default RealTimeMonitoringDashboard;
