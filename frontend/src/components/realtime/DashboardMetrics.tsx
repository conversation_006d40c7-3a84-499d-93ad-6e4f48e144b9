/**
 * Dashboard Metrics Component
 * 
 * Displays key security metrics and KPIs for the real-time monitoring dashboard.
 * Shows asset counts, threat levels, MITRE technique activity, and system status.
 */

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Security as SecurityIcon,
  Warning as WarningIcon,
  Shield as ShieldIcon,
  Timeline as TimelineIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface DashboardOverview {
  total_assets: number;
  high_risk_assets: number;
  active_threats: number;
  mitre_techniques_detected: number;
  last_updated: string;
}

interface DashboardMetricsProps {
  overview: DashboardOverview;
  lastUpdated: Date;
  isRealTime: boolean;
}

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  subtitle?: string;
  trend?: 'up' | 'down' | 'stable';
  percentage?: number;
  tooltip?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  color,
  subtitle,
  trend,
  percentage,
  tooltip
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon sx={{ fontSize: 16, color: 'error.main' }} />;
      case 'down':
        return <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main', transform: 'rotate(180deg)' }} />;
      case 'stable':
        return <TrendingUpIcon sx={{ fontSize: 16, color: 'info.main', transform: 'rotate(90deg)' }} />;
      default:
        return null;
    }
  };

  const getColorValue = (colorName: string) => {
    const colorMap = {
      primary: '#1976d2',
      secondary: '#dc004e',
      error: '#d32f2f',
      warning: '#ed6c02',
      info: '#0288d1',
      success: '#2e7d32'
    };
    return colorMap[colorName as keyof typeof colorMap] || colorMap.primary;
  };

  return (
    <Card sx={{ height: '100%', position: 'relative' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
          <Box display="flex" alignItems="center">
            <Box
              sx={{
                p: 1,
                borderRadius: 1,
                backgroundColor: `${color}.light`,
                color: `${color}.contrastText`,
                mr: 2
              }}
            >
              {icon}
            </Box>
            <Typography variant="h6" component="div" fontWeight="medium">
              {title}
            </Typography>
          </Box>
          
          {tooltip && (
            <Tooltip title={tooltip}>
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        <Box display="flex" alignItems="baseline" mb={1}>
          <Typography variant="h3" component="div" fontWeight="bold" color={`${color}.main`}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </Typography>
          
          {trend && (
            <Box ml={1} display="flex" alignItems="center">
              {getTrendIcon()}
            </Box>
          )}
        </Box>

        {subtitle && (
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {subtitle}
          </Typography>
        )}

        {percentage !== undefined && (
          <Box mt={2}>
            <Box display="flex" justifyContent="space-between" mb={0.5}>
              <Typography variant="caption" color="text.secondary">
                Risk Level
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {percentage}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={percentage}
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: getColorValue(color),
                  borderRadius: 3
                }
              }}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export const DashboardMetrics: React.FC<DashboardMetricsProps> = ({
  overview,
  lastUpdated,
  isRealTime
}) => {
  // Calculate risk percentage
  const riskPercentage = overview.total_assets > 0 
    ? Math.round((overview.high_risk_assets / overview.total_assets) * 100)
    : 0;

  // Calculate threat activity level
  const threatActivityLevel = overview.active_threats > 10 ? 'High' : 
                             overview.active_threats > 5 ? 'Medium' : 'Low';

  // Calculate MITRE coverage
  const mitreCoverage = Math.min(Math.round((overview.mitre_techniques_detected / 100) * 100), 100);

  const metrics = [
    {
      title: 'Total Assets',
      value: overview.total_assets,
      icon: <VisibilityIcon />,
      color: 'primary' as const,
      subtitle: 'Monitored infrastructure',
      trend: 'stable' as const,
      tooltip: 'Total number of assets under monitoring'
    },
    {
      title: 'High Risk Assets',
      value: overview.high_risk_assets,
      icon: <WarningIcon />,
      color: 'warning' as const,
      subtitle: `${riskPercentage}% of total assets`,
      trend: overview.high_risk_assets > 10 ? 'up' as const : 'stable' as const,
      percentage: riskPercentage,
      tooltip: 'Assets with risk score ≥ 7.0'
    },
    {
      title: 'Active Threats',
      value: overview.active_threats,
      icon: <SecurityIcon />,
      color: overview.active_threats > 10 ? 'error' : overview.active_threats > 5 ? 'warning' : 'success',
      subtitle: `${threatActivityLevel} activity level`,
      trend: overview.active_threats > 15 ? 'up' as const : 
             overview.active_threats < 5 ? 'down' as const : 'stable' as const,
      tooltip: 'Currently detected security threats'
    },
    {
      title: 'MITRE Techniques',
      value: overview.mitre_techniques_detected,
      icon: <ShieldIcon />,
      color: 'info' as const,
      subtitle: `${mitreCoverage}% coverage`,
      trend: 'stable' as const,
      percentage: mitreCoverage,
      tooltip: 'Detected MITRE ATT&CK techniques'
    }
  ];

  return (
    <Box mb={3}>
      {/* Status Bar */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Box display="flex" alignItems="center" gap={1}>
          <Chip
            icon={<TimelineIcon />}
            label={isRealTime ? 'Real-time Active' : 'Real-time Paused'}
            color={isRealTime ? 'success' : 'default'}
            size="small"
          />
          <Typography variant="caption" color="text.secondary">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
        </Box>
        
        <Box display="flex" alignItems="center" gap={1}>
          <Chip
            label={`${threatActivityLevel} Threat Activity`}
            color={
              threatActivityLevel === 'High' ? 'error' :
              threatActivityLevel === 'Medium' ? 'warning' : 'success'
            }
            size="small"
          />
        </Box>
      </Box>

      {/* Metrics Grid */}
      <Grid container spacing={3}>
        {metrics.map((metric, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <MetricCard {...metric} />
          </Grid>
        ))}
      </Grid>

      {/* Additional Status Information */}
      <Box mt={2} p={2} bgcolor="grey.50" borderRadius={1}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <Typography variant="body2" color="text.secondary">
              <strong>System Status:</strong> All monitoring systems operational
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body2" color="text.secondary">
              <strong>Data Freshness:</strong> {isRealTime ? 'Live' : 'Cached'}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body2" color="text.secondary">
              <strong>Coverage:</strong> {overview.total_assets} assets monitored
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default DashboardMetrics;
