/**
 * Attack Timeline Visualization Component
 * 
 * Displays a chronological timeline of attack events with MITRE ATT&CK context.
 */

import React from 'react';
import {
  Box,
  Typography,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  Paper,
  Chip
} from '@mui/material';

interface AttackTimelineData {
  timestamp: string;
  event_id: string;
  technique_id: string;
  technique_name: string;
  tactic: string;
  asset_name: string;
  severity: string;
  description: string;
}

interface AttackTimelineVisualizationProps {
  data: AttackTimelineData[];
}

export const AttackTimelineVisualization: React.FC<AttackTimelineVisualizationProps> = ({
  data
}) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'grey';
    }
  };

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      {data.length === 0 ? (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="100%"
          textAlign="center"
        >
          <Typography variant="body1" color="text.secondary">
            No attack events in the selected timeframe
          </Typography>
        </Box>
      ) : (
        <Timeline>
          {data.slice(0, 10).map((event, index) => (
            <TimelineItem key={event.event_id}>
              <TimelineSeparator>
                <TimelineDot color={getSeverityColor(event.severity)} />
                {index < Math.min(data.length, 10) - 1 && <TimelineConnector />}
              </TimelineSeparator>
              <TimelineContent>
                <Paper elevation={1} sx={{ p: 2, mb: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {event.technique_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </Typography>
                  </Box>
                  
                  <Box display="flex" gap={1} mb={1}>
                    <Chip label={event.technique_id} size="small" variant="outlined" />
                    <Chip label={event.tactic} size="small" color="primary" />
                    <Chip 
                      label={event.severity} 
                      size="small" 
                      color={getSeverityColor(event.severity)}
                    />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Target: {event.asset_name}
                  </Typography>
                  
                  <Typography variant="body2">
                    {event.description}
                  </Typography>
                </Paper>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      )}
    </Box>
  );
};

export default AttackTimelineVisualization;
