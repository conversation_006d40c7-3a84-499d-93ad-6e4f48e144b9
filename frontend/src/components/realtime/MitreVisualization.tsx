/**
 * MITRE ATT&CK Visualization Component
 * 
 * Displays MITRE ATT&CK technique activity in a matrix format.
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';

interface MitreVisualizationData {
  matrix: Record<string, {
    tactic_id: string;
    techniques: Array<{
      id: string;
      name: string;
      usage_count: number;
      risk_level: string;
    }>;
  }>;
  total_techniques: number;
  active_techniques: number;
  top_techniques: Array<[string, number]>;
}

interface MitreVisualizationProps {
  data: MitreVisualizationData | null;
}

export const MitreVisualization: React.FC<MitreVisualizationProps> = ({ data }) => {
  const [selectedTactic, setSelectedTactic] = useState<string | null>(null);

  if (!data) {
    return (
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        height="100%"
        textAlign="center"
      >
        <Typography variant="body1" color="text.secondary">
          Loading MITRE ATT&CK data...
        </Typography>
      </Box>
    );
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const tactics = Object.keys(data.matrix);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Summary Stats */}
      <Box mb={2}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" fontWeight="bold">
                {data.total_techniques}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Techniques
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {data.active_techniques}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Techniques
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {Math.round((data.active_techniques / data.total_techniques) * 100)}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Coverage
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* MITRE Matrix */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {tactics.map((tacticName) => {
          const tactic = data.matrix[tacticName];
          const activeTechniques = tactic.techniques.filter(t => t.usage_count > 0);
          
          return (
            <Accordion
              key={tacticName}
              expanded={selectedTactic === tacticName}
              onChange={() => setSelectedTactic(selectedTactic === tacticName ? null : tacticName)}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                  <Typography variant="h6">{tacticName}</Typography>
                  <Box display="flex" gap={1} mr={2}>
                    <Chip
                      label={`${activeTechniques.length}/${tactic.techniques.length}`}
                      color="primary"
                      size="small"
                    />
                    <Chip
                      label={tactic.tactic_id}
                      variant="outlined"
                      size="small"
                    />
                  </Box>
                </Box>
              </AccordionSummary>
              
              <AccordionDetails>
                <Grid container spacing={1}>
                  {tactic.techniques.map((technique) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={technique.id}>
                      <Card
                        sx={{
                          height: 80,
                          opacity: technique.usage_count > 0 ? 1 : 0.3,
                          border: technique.usage_count > 0 ? `2px solid` : '1px solid',
                          borderColor: technique.usage_count > 0 
                            ? `${getRiskColor(technique.risk_level)}.main`
                            : 'grey.300'
                        }}
                      >
                        <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={0.5}>
                            <Typography variant="caption" fontWeight="bold">
                              {technique.id}
                            </Typography>
                            {technique.usage_count > 0 && (
                              <Chip
                                label={technique.usage_count}
                                color={getRiskColor(technique.risk_level)}
                                size="small"
                                sx={{ height: 16, fontSize: '0.6rem' }}
                              />
                            )}
                          </Box>
                          
                          <Typography
                            variant="caption"
                            sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              lineHeight: 1.2
                            }}
                          >
                            {technique.name}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Box>

      {/* Top Techniques */}
      <Box mt={2}>
        <Typography variant="h6" gutterBottom>
          Most Active Techniques
        </Typography>
        <Box display="flex" flexWrap="wrap" gap={1}>
          {data.top_techniques.slice(0, 10).map(([techniqueId, count]) => (
            <Chip
              key={techniqueId}
              label={`${techniqueId} (${count})`}
              color="primary"
              variant="outlined"
              size="small"
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default MitreVisualization;
