/**
 * Real-Time Events Feed Component
 * 
 * Displays a live feed of security events with real-time updates.
 */

import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Typography,
  Avatar,
  Divider
} from '@mui/material';
import {
  Security as SecurityIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

interface RealTimeEvent {
  id: string;
  event_type: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  asset_name?: string;
  mitre_technique_id?: string;
}

interface RealTimeEventsFeedProps {
  events: RealTimeEvent[];
  isRealTime: boolean;
}

export const RealTimeEventsFeed: React.FC<RealTimeEventsFeedProps> = ({
  events,
  isRealTime
}) => {
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <ErrorIcon />;
      case 'high': return <WarningIcon />;
      case 'medium': return <InfoIcon />;
      case 'low': return <SecurityIcon />;
      default: return <InfoIcon />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="subtitle2">
          Recent Events ({events.length})
        </Typography>
        <Chip
          label={isRealTime ? 'Live' : 'Paused'}
          color={isRealTime ? 'success' : 'default'}
          size="small"
        />
      </Box>
      
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List dense>
          {events.length === 0 ? (
            <ListItem>
              <ListItemText
                primary="No recent events"
                secondary="Monitoring for new security events..."
              />
            </ListItem>
          ) : (
            events.map((event, index) => (
              <React.Fragment key={event.id}>
                <ListItem alignItems="flex-start">
                  <ListItemIcon>
                    <Avatar
                      sx={{
                        bgcolor: `${getSeverityColor(event.severity)}.main`,
                        width: 32,
                        height: 32
                      }}
                    >
                      {getSeverityIcon(event.severity)}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="body2" fontWeight="medium">
                          {event.title}
                        </Typography>
                        {event.mitre_technique_id && (
                          <Chip
                            label={event.mitre_technique_id}
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(event.timestamp).toLocaleTimeString()}
                          {event.asset_name && ` • ${event.asset_name}`}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 0.5 }}>
                          {event.description}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < events.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))
          )}
        </List>
      </Box>
    </Box>
  );
};

export default RealTimeEventsFeed;
