/**
 * Threat Map Visualization Component
 * 
 * Displays a geographic visualization of global threats with:
 * - Country-based threat distribution
 * - Severity indicators
 * - Top MITRE techniques by region
 * - Interactive threat details
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
  Tooltip,
  Grid,
  Paper
} from '@mui/material';
import {
  Public as PublicIcon,
  Warning as WarningIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

interface ThreatMapData {
  country: string;
  country_code: string;
  threat_count: number;
  severity_distribution: Record<string, number>;
  top_techniques: string[];
  coordinates: { lat: number; lng: number };
}

interface ThreatMapVisualizationProps {
  data: ThreatMapData[];
}

interface ThreatMarkerProps {
  threat: ThreatMapData;
  isSelected: boolean;
  onClick: () => void;
}

const ThreatMarker: React.FC<ThreatMarkerProps> = ({ threat, isSelected, onClick }) => {
  const getThreatLevel = () => {
    const { critical = 0, high = 0, medium = 0, low = 0 } = threat.severity_distribution;
    
    if (critical > 0) return { level: 'critical', color: '#d32f2f' };
    if (high > 0) return { level: 'high', color: '#ed6c02' };
    if (medium > 0) return { level: 'medium', color: '#0288d1' };
    return { level: 'low', color: '#2e7d32' };
  };

  const { level, color } = getThreatLevel();
  const size = Math.min(Math.max(threat.threat_count * 2, 20), 60);

  return (
    <Tooltip title={`${threat.country}: ${threat.threat_count} threats`}>
      <Box
        onClick={onClick}
        sx={{
          position: 'absolute',
          width: size,
          height: size,
          borderRadius: '50%',
          backgroundColor: color,
          opacity: isSelected ? 1 : 0.7,
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '12px',
          fontWeight: 'bold',
          border: isSelected ? '3px solid #fff' : '2px solid rgba(255,255,255,0.5)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'scale(1.1)',
            opacity: 1,
            zIndex: 10
          }
        }}
      >
        {threat.threat_count}
      </Box>
    </Tooltip>
  );
};

const WorldMapSVG: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Box
    sx={{
      position: 'relative',
      width: '100%',
      height: '300px',
      backgroundColor: '#f5f5f5',
      borderRadius: 1,
      overflow: 'hidden',
      backgroundImage: `
        radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.1) 0%, transparent 50%)
      `
    }}
  >
    {/* Simple world map representation */}
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 800 400"
      style={{ position: 'absolute', top: 0, left: 0 }}
    >
      {/* Simplified continent shapes */}
      <g fill="#e0e0e0" stroke="#bdbdbd" strokeWidth="1">
        {/* North America */}
        <path d="M 50 80 L 200 60 L 220 120 L 180 180 L 80 160 Z" />
        {/* South America */}
        <path d="M 150 200 L 200 180 L 220 280 L 180 320 L 160 300 Z" />
        {/* Europe */}
        <path d="M 350 60 L 420 50 L 440 100 L 400 120 L 360 110 Z" />
        {/* Africa */}
        <path d="M 380 120 L 450 110 L 470 220 L 420 280 L 390 260 Z" />
        {/* Asia */}
        <path d="M 450 40 L 650 30 L 680 150 L 600 180 L 480 160 Z" />
        {/* Australia */}
        <path d="M 580 250 L 650 240 L 670 280 L 620 300 L 590 290 Z" />
      </g>
    </svg>
    
    {/* Threat markers */}
    {children}
  </Box>
);

export const ThreatMapVisualization: React.FC<ThreatMapVisualizationProps> = ({ data }) => {
  const [selectedThreat, setSelectedThreat] = useState<ThreatMapData | null>(null);

  // Position threats on the map (simplified positioning)
  const getMarkerPosition = (threat: ThreatMapData) => {
    const positions: Record<string, { top: string; left: string }> = {
      'US': { top: '25%', left: '15%' },
      'CN': { top: '30%', left: '70%' },
      'RU': { top: '20%', left: '60%' },
      'DE': { top: '22%', left: '48%' },
      'GB': { top: '20%', left: '45%' },
      'JP': { top: '35%', left: '80%' },
      'AU': { top: '70%', left: '75%' },
      'BR': { top: '60%', left: '25%' }
    };
    
    return positions[threat.country_code] || { top: '50%', left: '50%' };
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const totalThreats = data.reduce((sum, threat) => sum + threat.threat_count, 0);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Map Container */}
      <Box sx={{ flex: 1, position: 'relative', mb: 2 }}>
        <WorldMapSVG>
          {data.map((threat, index) => {
            const position = getMarkerPosition(threat);
            return (
              <Box
                key={threat.country_code}
                sx={{
                  position: 'absolute',
                  ...position,
                  transform: 'translate(-50%, -50%)'
                }}
              >
                <ThreatMarker
                  threat={threat}
                  isSelected={selectedThreat?.country_code === threat.country_code}
                  onClick={() => setSelectedThreat(threat)}
                />
              </Box>
            );
          })}
        </WorldMapSVG>
      </Box>

      {/* Threat Details */}
      <Grid container spacing={2}>
        {/* Summary Stats */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Box display="flex" alignItems="center" mb={1}>
              <PublicIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Global Overview</Typography>
            </Box>
            <Typography variant="h4" color="primary.main" fontWeight="bold">
              {totalThreats}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total threats detected across {data.length} regions
            </Typography>
          </Paper>
        </Grid>

        {/* Selected Threat Details */}
        <Grid item xs={12} md={8}>
          {selectedThreat ? (
            <Paper sx={{ p: 2, height: '100%' }}>
              <Box display="flex" alignItems="center" justify="space-between" mb={2}>
                <Typography variant="h6">
                  {selectedThreat.country} Threat Details
                </Typography>
                <Chip
                  icon={<SecurityIcon />}
                  label={`${selectedThreat.threat_count} threats`}
                  color="primary"
                  size="small"
                />
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Severity Distribution
                  </Typography>
                  <Box>
                    {Object.entries(selectedThreat.severity_distribution).map(([severity, count]) => (
                      <Box key={severity} display="flex" justifyContent="space-between" mb={0.5}>
                        <Chip
                          label={severity.charAt(0).toUpperCase() + severity.slice(1)}
                          color={getSeverityColor(severity)}
                          size="small"
                          sx={{ minWidth: 80 }}
                        />
                        <Typography variant="body2">{count}</Typography>
                      </Box>
                    ))}
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Top MITRE Techniques
                  </Typography>
                  <Box>
                    {selectedThreat.top_techniques.slice(0, 3).map((technique, index) => (
                      <Chip
                        key={technique}
                        label={technique}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          ) : (
            <Paper sx={{ p: 2, height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Box textAlign="center">
                <PublicIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                <Typography variant="body1" color="text.secondary">
                  Click on a threat marker to view details
                </Typography>
              </Box>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ThreatMapVisualization;
