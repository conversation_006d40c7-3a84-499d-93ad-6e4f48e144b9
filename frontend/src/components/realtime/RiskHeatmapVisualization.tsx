/**
 * Risk Heatmap Visualization Component
 * 
 * Displays asset risk levels in a heatmap format with interactive details.
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Tooltip,
  Paper
} from '@mui/material';

interface RiskHeatmapData {
  asset_id: number;
  asset_name: string;
  asset_type: string;
  risk_score: number;
  threat_count: number;
  mitre_techniques: string[];
  last_updated: string;
}

interface RiskHeatmapVisualizationProps {
  data: RiskHeatmapData[];
}

export const RiskHeatmapVisualization: React.FC<RiskHeatmapVisualizationProps> = ({
  data
}) => {
  const [selectedAsset, setSelectedAsset] = useState<RiskHeatmapData | null>(null);

  const getRiskColor = (riskScore: number) => {
    if (riskScore >= 8) return '#d32f2f'; // Critical - Red
    if (riskScore >= 6) return '#ed6c02'; // High - Orange
    if (riskScore >= 4) return '#0288d1'; // Medium - Blue
    return '#2e7d32'; // Low - Green
  };

  const getRiskLevel = (riskScore: number) => {
    if (riskScore >= 8) return 'Critical';
    if (riskScore >= 6) return 'High';
    if (riskScore >= 4) return 'Medium';
    return 'Low';
  };

  const getRiskChipColor = (riskScore: number) => {
    if (riskScore >= 8) return 'error';
    if (riskScore >= 6) return 'warning';
    if (riskScore >= 4) return 'info';
    return 'success';
  };

  // Sort data by risk score for better visualization
  const sortedData = [...data].sort((a, b) => b.risk_score - a.risk_score);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Heatmap Grid */}
      <Box sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
        <Grid container spacing={1}>
          {sortedData.slice(0, 20).map((asset) => (
            <Grid item xs={6} sm={4} md={3} key={asset.asset_id}>
              <Tooltip
                title={
                  <Box>
                    <Typography variant="subtitle2">{asset.asset_name}</Typography>
                    <Typography variant="caption">
                      Risk Score: {asset.risk_score.toFixed(1)}<br/>
                      Threats: {asset.threat_count}<br/>
                      Type: {asset.asset_type}
                    </Typography>
                  </Box>
                }
              >
                <Card
                  sx={{
                    height: 60,
                    cursor: 'pointer',
                    backgroundColor: getRiskColor(asset.risk_score),
                    color: 'white',
                    opacity: selectedAsset?.asset_id === asset.asset_id ? 1 : 0.8,
                    border: selectedAsset?.asset_id === asset.asset_id ? '2px solid #fff' : 'none',
                    '&:hover': {
                      opacity: 1,
                      transform: 'scale(1.05)',
                      transition: 'all 0.2s ease'
                    }
                  }}
                  onClick={() => setSelectedAsset(asset)}
                >
                  <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                    <Typography variant="caption" noWrap>
                      {asset.asset_name}
                    </Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {asset.risk_score.toFixed(1)}
                    </Typography>
                  </CardContent>
                </Card>
              </Tooltip>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Asset Details */}
      {selectedAsset ? (
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            {selectedAsset.asset_name}
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Typography variant="body2">Risk Level:</Typography>
                <Chip
                  label={`${getRiskLevel(selectedAsset.risk_score)} (${selectedAsset.risk_score.toFixed(1)})`}
                  color={getRiskChipColor(selectedAsset.risk_score)}
                  size="small"
                />
              </Box>
              
              <Typography variant="body2" gutterBottom>
                <strong>Type:</strong> {selectedAsset.asset_type}
              </Typography>
              
              <Typography variant="body2" gutterBottom>
                <strong>Threats:</strong> {selectedAsset.threat_count}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" gutterBottom>
                <strong>MITRE Techniques:</strong>
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={0.5}>
                {selectedAsset.mitre_techniques.map((technique) => (
                  <Chip
                    key={technique}
                    label={technique}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
          
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Last updated: {new Date(selectedAsset.last_updated).toLocaleString()}
          </Typography>
        </Paper>
      ) : (
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Click on an asset tile to view details
          </Typography>
        </Paper>
      )}

      {/* Legend */}
      <Box mt={1}>
        <Typography variant="caption" color="text.secondary" gutterBottom>
          Risk Levels:
        </Typography>
        <Box display="flex" gap={1}>
          <Chip label="Low (0-4)" color="success" size="small" />
          <Chip label="Medium (4-6)" color="info" size="small" />
          <Chip label="High (6-8)" color="warning" size="small" />
          <Chip label="Critical (8+)" color="error" size="small" />
        </Box>
      </Box>
    </Box>
  );
};

export default RiskHeatmapVisualization;
