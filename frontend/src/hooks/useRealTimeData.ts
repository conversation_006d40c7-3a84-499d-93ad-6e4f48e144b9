/**
 * Real-Time Data Hook
 * 
 * Manages real-time data fetching and state management for the monitoring dashboard.
 * Provides data for threat maps, risk heatmaps, attack timelines, and MITRE visualizations.
 */

import { useState, useEffect, useCallback } from 'react';

// Type definitions
interface ThreatMapData {
  country: string;
  country_code: string;
  threat_count: number;
  severity_distribution: Record<string, number>;
  top_techniques: string[];
  coordinates: { lat: number; lng: number };
}

interface RiskHeatmapData {
  asset_id: number;
  asset_name: string;
  asset_type: string;
  risk_score: number;
  threat_count: number;
  mitre_techniques: string[];
  last_updated: string;
}

interface AttackTimelineData {
  timestamp: string;
  event_id: string;
  technique_id: string;
  technique_name: string;
  tactic: string;
  asset_name: string;
  severity: string;
  description: string;
}

interface MitreVisualizationData {
  matrix: Record<string, {
    tactic_id: string;
    techniques: Array<{
      id: string;
      name: string;
      usage_count: number;
      risk_level: string;
    }>;
  }>;
  total_techniques: number;
  active_techniques: number;
  top_techniques: Array<[string, number]>;
}

interface UseRealTimeDataReturn {
  threatMapData: ThreatMapData[];
  riskHeatmapData: RiskHeatmapData[];
  attackTimelineData: AttackTimelineData[];
  mitreVisualizationData: MitreVisualizationData | null;
  isLoading: boolean;
  error: string | null;
  refreshData: () => void;
  lastUpdated: Date | null;
}

// API base URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export const useRealTimeData = (): UseRealTimeDataReturn => {
  const [threatMapData, setThreatMapData] = useState<ThreatMapData[]>([]);
  const [riskHeatmapData, setRiskHeatmapData] = useState<RiskHeatmapData[]>([]);
  const [attackTimelineData, setAttackTimelineData] = useState<AttackTimelineData[]>([]);
  const [mitreVisualizationData, setMitreVisualizationData] = useState<MitreVisualizationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Generic API fetch function
  const fetchData = useCallback(async <T>(endpoint: string): Promise<T> => {
    const token = localStorage.getItem('access_token');
    
    const response = await fetch(`${API_BASE_URL}/api/v1/realtime${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'API request failed');
    }

    return result.data;
  }, []);

  // Fetch threat map data
  const fetchThreatMapData = useCallback(async () => {
    try {
      const data = await fetchData<ThreatMapData[]>('/threat-map');
      setThreatMapData(data);
    } catch (err) {
      console.error('Error fetching threat map data:', err);
      throw err;
    }
  }, [fetchData]);

  // Fetch risk heatmap data
  const fetchRiskHeatmapData = useCallback(async () => {
    try {
      const data = await fetchData<RiskHeatmapData[]>('/risk-heatmap');
      setRiskHeatmapData(data);
    } catch (err) {
      console.error('Error fetching risk heatmap data:', err);
      throw err;
    }
  }, [fetchData]);

  // Fetch attack timeline data
  const fetchAttackTimelineData = useCallback(async (hours: number = 24) => {
    try {
      const data = await fetchData<AttackTimelineData[]>(`/attack-timeline?hours=${hours}`);
      setAttackTimelineData(data);
    } catch (err) {
      console.error('Error fetching attack timeline data:', err);
      throw err;
    }
  }, [fetchData]);

  // Fetch MITRE visualization data
  const fetchMitreVisualizationData = useCallback(async () => {
    try {
      const data = await fetchData<MitreVisualizationData>('/mitre-visualization');
      setMitreVisualizationData(data);
    } catch (err) {
      console.error('Error fetching MITRE visualization data:', err);
      throw err;
    }
  }, [fetchData]);

  // Refresh all data
  const refreshData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchThreatMapData(),
        fetchRiskHeatmapData(),
        fetchAttackTimelineData(),
        fetchMitreVisualizationData()
      ]);
      
      setLastUpdated(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error refreshing real-time data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [fetchThreatMapData, fetchRiskHeatmapData, fetchAttackTimelineData, fetchMitreVisualizationData]);

  // Initial data load
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [refreshData]);

  return {
    threatMapData,
    riskHeatmapData,
    attackTimelineData,
    mitreVisualizationData,
    isLoading,
    error,
    refreshData,
    lastUpdated
  };
};
