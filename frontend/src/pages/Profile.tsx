/**
 * User Profile page component
 */

import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Work as WorkIcon,
  Schedule as ScheduleIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch } from '@/store';
import { setPageTitle } from '@/store/uiSlice';
import { authService } from '@/services/authService';

const Profile: React.FC = () => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Profile'));
  }, [dispatch]);

  const formatRoleName = (role: string): string => {
    const roleNames: Record<string, string> = {
      admin: 'Administrator',
      soc_operator: 'SOC Operator',
      security_architect: 'Security Architect',
      red_team_member: 'Red Team Member',
      purple_team_member: 'Purple Team Member',
      analyst: 'Analyst',
      viewer: 'Viewer',
    };
    return roleNames[role] || role;
  };

  const getUserRoleColor = (role: string) => {
    const colors: Record<string, 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success'> = {
      admin: 'error',
      soc_operator: 'primary',
      security_architect: 'secondary',
      red_team_member: 'warning',
      purple_team_member: 'info',
      analyst: 'success',
      viewer: 'secondary',
    };
    return colors[role] || 'secondary';
  };

  if (!user) {
    return (
      <Box sx={{ flexGrow: 1, p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
        <Typography variant="h6">Loading profile...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={4}>
        <PersonIcon sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
        <Box>
          <Typography variant="h4" gutterBottom>
            User Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and manage your account information
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Overview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  width: 100,
                  height: 100,
                  mx: 'auto',
                  mb: 2,
                  fontSize: '2.5rem',
                }}
              >
                {authService.getUserInitials(user)}
              </Avatar>
              
              <Typography variant="h5" gutterBottom>
                {user.full_name || user.username}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {user.email}
              </Typography>

              <Box display="flex" justifyContent="center" flexWrap="wrap" gap={1} mt={2}>
                {user.roles.map((role) => (
                  <Chip
                    key={role}
                    label={formatRoleName(role)}
                    color={getUserRoleColor(role)}
                    size="small"
                  />
                ))}
              </Box>

              <Box mt={2}>
                <Chip
                  label={user.is_active ? 'Active' : 'Inactive'}
                  color={user.is_active ? 'success' : 'error'}
                  size="small"
                />
                {user.is_verified && (
                  <Chip
                    label="Verified"
                    color="info"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                )}
                {user.mfa_enabled && (
                  <Chip
                    label="MFA Enabled"
                    color="success"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Profile Details */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Profile Information
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Full Name"
                    secondary={user.full_name || 'Not provided'}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary={user.email}
                  />
                </ListItem>

                {user.phone_number && (
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone Number"
                      secondary={user.phone_number}
                    />
                  </ListItem>
                )}

                {user.department && (
                  <ListItem>
                    <ListItemIcon>
                      <BusinessIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Department"
                      secondary={user.department}
                    />
                  </ListItem>
                )}

                {user.job_title && (
                  <ListItem>
                    <ListItemIcon>
                      <WorkIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Job Title"
                      secondary={user.job_title}
                    />
                  </ListItem>
                )}

                <ListItem>
                  <ListItemIcon>
                    <ScheduleIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Timezone"
                    secondary={user.timezone}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <LanguageIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Language"
                    secondary={user.language}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <PaletteIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Theme"
                    secondary={user.theme}
                  />
                </ListItem>
              </List>

              <Divider sx={{ my: 2 }} />

              <Typography variant="h6" gutterBottom>
                Account Information
              </Typography>

              <List>
                <ListItem>
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Login"
                    secondary={user.last_login_at ? new Date(user.last_login_at).toLocaleString() : 'Never'}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Permissions"
                    secondary={`${user.permissions.length} permissions assigned`}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Permissions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Permissions
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Your current permissions based on assigned roles:
              </Typography>

              <Box display="flex" flexWrap="wrap" gap={1} mt={2}>
                {user.permissions.map((permission) => (
                  <Chip
                    key={permission}
                    label={permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Profile;
