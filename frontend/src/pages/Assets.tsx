import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CloudQueue as CloudIcon,
  Computer as ServerIcon,
  Api as ApiIcon,
  Storage as DatabaseIcon,
  Security as SecurityIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';

import AssetForm from '../components/AssetForm';

// Types
interface Asset {
  id: string;
  name: string;
  asset_type: string;
  provider: string;
  status: string;
  environment: string;
  owner: string;
  team: string;
  risk_level: string;
  risk_score: number;
  ip_addresses: string[];
  discovered_at: string;
  last_seen: string;
  tags: Record<string, string>;
}

interface AssetSearchFilters {
  query: string;
  asset_types: string[];
  providers: string[];
  environments: string[];
  risk_levels: string[];
  page: number;
  size: number;
}

interface AssetStatistics {
  total_assets: number;
  assets_by_type: Record<string, number>;
  assets_by_provider: Record<string, number>;
  assets_by_environment: Record<string, number>;
  assets_by_risk_level: Record<string, number>;
  recent_discoveries: number;
}

const AssetsPage: React.FC = () => {
  // State
  const [assets, setAssets] = useState<Asset[]>([]);
  const [statistics, setStatistics] = useState<AssetStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<'view' | 'edit' | 'create'>('view');
  
  // Search and pagination
  const [searchFilters, setSearchFilters] = useState<AssetSearchFilters>({
    query: '',
    asset_types: [],
    providers: [],
    environments: [],
    risk_levels: [],
    page: 0,
    size: 20,
  });
  const [totalAssets, setTotalAssets] = useState(0);

  // Load assets and statistics
  useEffect(() => {
    loadAssets();
    loadStatistics();
  }, [searchFilters]);

  const loadAssets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/v1/assets/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          ...searchFilters,
          page: searchFilters.page + 1, // API uses 1-based pagination
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to load assets');
      }

      const data = await response.json();
      setAssets(data.assets);
      setTotalAssets(data.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load assets');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/v1/assets/statistics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load statistics');
      }

      const data = await response.json();
      setStatistics(data);
    } catch (err) {
      console.error('Failed to load statistics:', err);
    }
  };

  // Asset type icon mapping
  const getAssetTypeIcon = (assetType: string) => {
    switch (assetType) {
      case 'server':
        return <ServerIcon />;
      case 'cloud_instance':
        return <CloudIcon />;
      case 'api_endpoint':
        return <ApiIcon />;
      case 'cloud_database':
        return <DatabaseIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  // Risk level color mapping
  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  // Provider color mapping
  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'aws':
        return '#FF9900';
      case 'azure':
        return '#0078D4';
      case 'gcp':
        return '#4285F4';
      default:
        return '#666666';
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchFilters(prev => ({
      ...prev,
      query,
      page: 0,
    }));
  };

  // Handle filter changes
  const handleFilterChange = (filterType: keyof AssetSearchFilters, value: any) => {
    setSearchFilters(prev => ({
      ...prev,
      [filterType]: value,
      page: 0,
    }));
  };

  // Handle pagination
  const handlePageChange = (event: unknown, newPage: number) => {
    setSearchFilters(prev => ({
      ...prev,
      page: newPage,
    }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchFilters(prev => ({
      ...prev,
      size: parseInt(event.target.value, 10),
      page: 0,
    }));
  };

  // Handle asset actions
  const handleViewAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setDialogType('view');
    setDialogOpen(true);
  };

  const handleEditAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setDialogType('edit');
    setDialogOpen(true);
  };

  const handleCreateAsset = () => {
    setSelectedAsset(null);
    setDialogType('create');
    setDialogOpen(true);
  };

  const handleDeleteAsset = async (assetId: string) => {
    if (!window.confirm('Are you sure you want to delete this asset?')) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/assets/${assetId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete asset');
      }

      // Reload assets
      loadAssets();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete asset');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedAsset(null);
  };

  const handleSaveAsset = () => {
    // Reload assets after save
    loadAssets();
    handleCloseDialog();
  };

  if (loading && assets.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Asset Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateAsset}
        >
          Add Asset
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {statistics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <SecurityIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{statistics.total_assets}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Assets
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TimelineIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{statistics.recent_discoveries}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Recent Discoveries
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CloudIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">
                      {Object.keys(statistics.assets_by_provider).length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cloud Providers
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <ServerIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">
                      {Object.keys(statistics.assets_by_type).length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Asset Types
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search assets..."
              value={searchFilters.query}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Provider</InputLabel>
              <Select
                multiple
                value={searchFilters.providers}
                onChange={(e) => handleFilterChange('providers', e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => (
                      <Chip key={value} label={value.toUpperCase()} size="small" />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="aws">AWS</MenuItem>
                <MenuItem value="azure">Azure</MenuItem>
                <MenuItem value="gcp">GCP</MenuItem>
                <MenuItem value="on_premises">On-Premises</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Asset Type</InputLabel>
              <Select
                multiple
                value={searchFilters.asset_types}
                onChange={(e) => handleFilterChange('asset_types', e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => (
                      <Chip key={value} label={value.replace('_', ' ')} size="small" />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="server">Server</MenuItem>
                <MenuItem value="cloud_instance">Cloud Instance</MenuItem>
                <MenuItem value="cloud_database">Database</MenuItem>
                <MenuItem value="api_endpoint">API Endpoint</MenuItem>
                <MenuItem value="cloud_function">Function</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Environment</InputLabel>
              <Select
                multiple
                value={searchFilters.environments}
                onChange={(e) => handleFilterChange('environments', e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="production">Production</MenuItem>
                <MenuItem value="staging">Staging</MenuItem>
                <MenuItem value="development">Development</MenuItem>
                <MenuItem value="testing">Testing</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Risk Level</InputLabel>
              <Select
                multiple
                value={searchFilters.risk_levels}
                onChange={(e) => handleFilterChange('risk_levels', e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => (
                      <Chip 
                        key={value} 
                        label={value} 
                        size="small"
                        color={getRiskLevelColor(value) as any}
                      />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="critical">Critical</MenuItem>
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="low">Low</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Assets Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Asset</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Provider</TableCell>
              <TableCell>Environment</TableCell>
              <TableCell>Risk Level</TableCell>
              <TableCell>Owner</TableCell>
              <TableCell>Last Seen</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {assets.map((asset) => (
              <TableRow key={asset.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center">
                    {getAssetTypeIcon(asset.asset_type)}
                    <Box ml={2}>
                      <Typography variant="subtitle2">{asset.name}</Typography>
                      {asset.ip_addresses && asset.ip_addresses.length > 0 && (
                        <Typography variant="caption" color="text.secondary">
                          {asset.ip_addresses[0]}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={asset.asset_type.replace('_', ' ')} 
                    size="small" 
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip 
                    label={asset.provider.toUpperCase()} 
                    size="small"
                    sx={{ 
                      backgroundColor: getProviderColor(asset.provider),
                      color: 'white'
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Chip 
                    label={asset.environment || 'Unknown'} 
                    size="small" 
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip 
                    label={asset.risk_level} 
                    size="small"
                    color={getRiskLevelColor(asset.risk_level) as any}
                  />
                </TableCell>
                <TableCell>{asset.owner || 'Unassigned'}</TableCell>
                <TableCell>
                  <Tooltip title={new Date(asset.last_seen).toLocaleString()}>
                    <Typography variant="body2">
                      {new Date(asset.last_seen).toLocaleDateString()}
                    </Typography>
                  </Tooltip>
                </TableCell>
                <TableCell align="right">
                  <IconButton 
                    size="small" 
                    onClick={() => handleViewAsset(asset)}
                    title="View Details"
                  >
                    <ViewIcon />
                  </IconButton>
                  <IconButton 
                    size="small" 
                    onClick={() => handleEditAsset(asset)}
                    title="Edit Asset"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton 
                    size="small" 
                    onClick={() => handleDeleteAsset(asset.id)}
                    title="Delete Asset"
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[10, 20, 50]}
          component="div"
          count={totalAssets}
          rowsPerPage={searchFilters.size}
          page={searchFilters.page}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      </TableContainer>

      {/* Asset Dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'create' ? 'Create Asset' : 
           dialogType === 'edit' ? 'Edit Asset' : 'Asset Details'}
        </DialogTitle>
        <DialogContent>
          <AssetForm
            asset={selectedAsset}
            mode={dialogType}
            onSave={handleSaveAsset}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          {dialogType !== 'view' && (
            <Button onClick={handleSaveAsset} variant="contained">
              Save
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AssetsPage;
