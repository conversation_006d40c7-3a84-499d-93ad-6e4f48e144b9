/**
 * Settings page component (Admin only)
 */

import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Alert,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  Cloud as CloudIcon,
  Notifications as NotificationsIcon,
  AdminPanelSettings as AdminIcon,
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch } from '@/store';
import { setPageTitle } from '@/store/uiSlice';

const Settings: React.FC = () => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Settings'));
  }, [dispatch]);

  const isAdmin = user?.roles.includes('admin');

  if (!isAdmin) {
    return (
      <Box sx={{ flexGrow: 1, p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
        <Alert severity="error">
          Access denied. Administrator privileges required.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={4}>
        <SettingsIcon sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
        <Box>
          <Typography variant="h4" gutterBottom>
            System Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure system-wide settings and preferences
          </Typography>
        </Box>
      </Box>

      {/* Admin Access Indicator */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <AdminIcon sx={{ mr: 1, color: 'error.main' }} />
            <Typography variant="h6">
              Administrator Access
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary">
            You have full administrative access to system settings.
          </Typography>
          
          <Box mt={2}>
            <Chip
              label="Admin"
              color="error"
              size="small"
            />
          </Box>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Security Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SecurityIcon sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">
                  Security Settings
                </Typography>
              </Box>
              
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Authentication"
                    secondary="Configure login policies and MFA requirements"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Password Policy"
                    secondary="Set password complexity and expiration rules"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Session Management"
                    secondary="Configure session timeouts and concurrent sessions"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="API Security"
                    secondary="Manage API keys and rate limiting"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* System Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <StorageIcon sx={{ mr: 1, color: 'info.main' }} />
                <Typography variant="h6">
                  System Configuration
                </Typography>
              </Box>
              
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Database Settings"
                    secondary="Configure database connections and performance"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Cache Configuration"
                    secondary="Redis cache settings and optimization"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Graph Database"
                    secondary="Neo4j configuration and graph analysis settings"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Logging & Monitoring"
                    secondary="Configure audit logs and system monitoring"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Integration Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <CloudIcon sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">
                  Integration Settings
                </Typography>
              </Box>
              
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Cloud Providers"
                    secondary="Configure AWS, Azure, and GCP integrations"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="ServiceNow CMDB"
                    secondary="Configure ServiceNow integration and sync settings"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="STIX/TAXII"
                    secondary="Threat intelligence feed configuration"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="SIEM Integration"
                    secondary="Configure SIEM connectors and data feeds"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <NotificationsIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">
                  Notification Settings
                </Typography>
              </Box>
              
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Email Configuration"
                    secondary="SMTP settings and email templates"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Alert Policies"
                    secondary="Configure alert thresholds and escalation"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Webhook Integration"
                    secondary="Configure external webhook notifications"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Slack/Teams"
                    secondary="Configure chat platform integrations"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* System Status */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                Settings interface will be implemented in the next development phase.
                This will include forms for configuring all system settings with validation and real-time updates.
              </Alert>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center" p={2} bgcolor="success.light" borderRadius={1}>
                    <Typography variant="h6" color="success.contrastText">
                      Database
                    </Typography>
                    <Typography variant="body2" color="success.contrastText">
                      Connected
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center" p={2} bgcolor="success.light" borderRadius={1}>
                    <Typography variant="h6" color="success.contrastText">
                      Cache
                    </Typography>
                    <Typography variant="body2" color="success.contrastText">
                      Active
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center" p={2} bgcolor="warning.light" borderRadius={1}>
                    <Typography variant="h6" color="warning.contrastText">
                      Graph DB
                    </Typography>
                    <Typography variant="body2" color="warning.contrastText">
                      Configuring
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center" p={2} bgcolor="info.light" borderRadius={1}>
                    <Typography variant="h6" color="info.contrastText">
                      Monitoring
                    </Typography>
                    <Typography variant="body2" color="info.contrastText">
                      Enabled
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;
