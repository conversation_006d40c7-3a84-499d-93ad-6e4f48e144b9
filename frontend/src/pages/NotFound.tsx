/**
 * 404 Not Found page component
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
} from '@mui/material';
import {
  Home as HomeIcon,
  ArrowBack as ArrowBackIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/dashboard');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="background.default"
      p={3}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          maxWidth: 500,
          textAlign: 'center',
          borderRadius: 2,
        }}
      >
        <Box mb={3}>
          <ErrorIcon
            sx={{
              fontSize: 80,
              color: 'error.main',
              mb: 2,
            }}
          />
        </Box>

        <Typography variant="h1" gutterBottom sx={{ fontSize: '4rem', fontWeight: 'bold' }}>
          404
        </Typography>

        <Typography variant="h4" gutterBottom color="text.primary">
          Page Not Found
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          The page you're looking for doesn't exist or has been moved.
        </Typography>

        <Typography variant="body2" color="text.secondary" paragraph>
          You might have mistyped the URL or the page may have been removed.
        </Typography>

        <Box mt={4} display="flex" gap={2} justifyContent="center" flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<HomeIcon />}
            onClick={handleGoHome}
            size="large"
          >
            Go to Dashboard
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleGoBack}
            size="large"
          >
            Go Back
          </Button>
        </Box>

        <Box mt={4} pt={3} borderTop={1} borderColor="divider">
          <Typography variant="caption" color="text.secondary">
            If you believe this is an error, please contact your system administrator.
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default NotFound;
