/**
 * Real-Time Monitoring Page
 * 
 * Main page component for the real-time security monitoring dashboard.
 */

import React from 'react';
import { Box, Container } from '@mui/material';
import RealTimeMonitoringDashboard from '../components/realtime/RealTimeMonitoringDashboard';

const RealTimeMonitoring: React.FC = () => {
  return (
    <Container maxWidth={false} sx={{ py: 3 }}>
      <RealTimeMonitoringDashboard />
    </Container>
  );
};

export default RealTimeMonitoring;
