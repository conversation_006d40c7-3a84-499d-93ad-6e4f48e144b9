import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Alert,
  CircularProgress,
  LinearProgress,
  Menu,
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  CloudQueue as CloudIcon,
  Api as ApiIcon,
  Computer as ServerIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

// Types
interface DiscoveryJob {
  id: string;
  name: string;
  description: string;
  job_type: string;
  status: string;
  started_at: string | null;
  completed_at: string | null;
  duration_seconds: number | null;
  assets_discovered: number;
  assets_updated: number;
  relationships_discovered: number;
  errors_count: number;
  executor: string | null;
  is_scheduled: boolean;
  schedule_expression: string | null;
  next_run_at: string | null;
  created_at: string;
  updated_at: string;
}

interface DiscoveryJobFilters {
  job_type: string;
  status: string;
  page: number;
  size: number;
}

const DiscoveryPage: React.FC = () => {
  // State
  const [jobs, setJobs] = useState<DiscoveryJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<DiscoveryJob | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<'view' | 'edit' | 'create'>('view');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuJobId, setMenuJobId] = useState<string | null>(null);
  
  // Filters and pagination
  const [filters, setFilters] = useState<DiscoveryJobFilters>({
    job_type: '',
    status: '',
    page: 0,
    size: 20,
  });
  const [totalJobs, setTotalJobs] = useState(0);

  // Load discovery jobs
  useEffect(() => {
    loadJobs();
  }, [filters]);

  const loadJobs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: (filters.page + 1).toString(),
        size: filters.size.toString(),
      });

      if (filters.job_type) {
        params.append('job_type', filters.job_type);
      }
      if (filters.status) {
        params.append('status', filters.status);
      }

      const response = await fetch(`/api/v1/discovery/jobs?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load discovery jobs');
      }

      const data = await response.json();
      setJobs(data.jobs);
      setTotalJobs(data.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load discovery jobs');
    } finally {
      setLoading(false);
    }
  };

  // Job status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'info';
      case 'failed':
        return 'error';
      case 'partial':
        return 'warning';
      case 'pending':
        return 'default';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  // Job type icon mapping
  const getJobTypeIcon = (jobType: string) => {
    if (jobType.includes('cloud') || jobType.includes('aws') || jobType.includes('azure') || jobType.includes('gcp')) {
      return <CloudIcon />;
    } else if (jobType.includes('api') || jobType.includes('akto') || jobType.includes('kiterunner')) {
      return <ApiIcon />;
    } else {
      return <ServerIcon />;
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterType: keyof DiscoveryJobFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value,
      page: 0,
    }));
  };

  // Handle pagination
  const handlePageChange = (event: unknown, newPage: number) => {
    setFilters(prev => ({
      ...prev,
      page: newPage,
    }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      size: parseInt(event.target.value, 10),
      page: 0,
    }));
  };

  // Handle job actions
  const handleViewJob = (job: DiscoveryJob) => {
    setSelectedJob(job);
    setDialogType('view');
    setDialogOpen(true);
  };

  const handleEditJob = (job: DiscoveryJob) => {
    setSelectedJob(job);
    setDialogType('edit');
    setDialogOpen(true);
  };

  const handleCreateJob = () => {
    setSelectedJob(null);
    setDialogType('create');
    setDialogOpen(true);
  };

  const handleStartJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/v1/discovery/jobs/${jobId}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to start job');
      }

      loadJobs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start job');
    }
  };

  const handleDeleteJob = async (jobId: string) => {
    if (!window.confirm('Are you sure you want to delete this discovery job?')) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/discovery/jobs/${jobId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete job');
      }

      loadJobs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete job');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedJob(null);
  };

  const handleSaveJob = () => {
    loadJobs();
    handleCloseDialog();
  };

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, jobId: string) => {
    setAnchorEl(event.currentTarget);
    setMenuJobId(jobId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuJobId(null);
  };

  // Quick discovery actions
  const handleQuickDiscovery = async (discoveryType: string) => {
    try {
      const config = getDefaultConfig(discoveryType);
      const response = await fetch(`/api/v1/discovery/${discoveryType}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`Failed to start ${discoveryType} discovery`);
      }

      loadJobs();
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to start ${discoveryType} discovery`);
    }
  };

  const getDefaultConfig = (discoveryType: string) => {
    switch (discoveryType) {
      case 'cloud/aws':
        return { region: 'us-east-1' };
      case 'cloud/azure':
        return { subscription_id: 'default' };
      case 'cloud/gcp':
        return { project_id: 'default' };
      case 'api/akto':
        return { target: 'https://api.example.com' };
      case 'api/kiterunner':
        return { target: 'https://api.example.com' };
      default:
        return {};
    }
  };

  if (loading && jobs.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Discovery Jobs
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadJobs}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateJob}
          >
            Create Job
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Quick Discovery Actions */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Discovery
        </Typography>
        <Grid container spacing={2}>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<CloudIcon />}
              onClick={() => handleQuickDiscovery('cloud/aws')}
            >
              AWS Discovery
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<CloudIcon />}
              onClick={() => handleQuickDiscovery('cloud/azure')}
            >
              Azure Discovery
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<CloudIcon />}
              onClick={() => handleQuickDiscovery('cloud/gcp')}
            >
              GCP Discovery
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<ApiIcon />}
              onClick={() => handleQuickDiscovery('api/akto')}
            >
              Akto API Discovery
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<ApiIcon />}
              onClick={() => handleQuickDiscovery('api/kiterunner')}
            >
              Kiterunner Discovery
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Job Type</InputLabel>
              <Select
                value={filters.job_type}
                onChange={(e) => handleFilterChange('job_type', e.target.value)}
              >
                <MenuItem value="">All Types</MenuItem>
                <MenuItem value="aws_discovery">AWS Discovery</MenuItem>
                <MenuItem value="azure_discovery">Azure Discovery</MenuItem>
                <MenuItem value="gcp_discovery">GCP Discovery</MenuItem>
                <MenuItem value="akto_discovery">Akto Discovery</MenuItem>
                <MenuItem value="kiterunner_discovery">Kiterunner Discovery</MenuItem>
                <MenuItem value="network_scan">Network Scan</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">All Statuses</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="running">Running</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
                <MenuItem value="partial">Partial</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Jobs Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Job</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Progress</TableCell>
              <TableCell>Results</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Created</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {jobs.map((job) => (
              <TableRow key={job.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center">
                    {getJobTypeIcon(job.job_type)}
                    <Box ml={2}>
                      <Typography variant="subtitle2">{job.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {job.description}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={job.job_type.replace('_', ' ')} 
                    size="small" 
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip 
                    label={job.status} 
                    size="small"
                    color={getStatusColor(job.status) as any}
                  />
                </TableCell>
                <TableCell>
                  {job.status === 'running' ? (
                    <Box sx={{ width: '100%' }}>
                      <LinearProgress />
                      <Typography variant="caption">In Progress...</Typography>
                    </Box>
                  ) : job.status === 'completed' ? (
                    <Typography variant="caption" color="success.main">
                      100% Complete
                    </Typography>
                  ) : job.status === 'failed' ? (
                    <Typography variant="caption" color="error.main">
                      Failed
                    </Typography>
                  ) : (
                    <Typography variant="caption" color="text.secondary">
                      {job.status}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      Assets: {job.assets_discovered}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Relationships: {job.relationships_discovered}
                    </Typography>
                    {job.errors_count > 0 && (
                      <Typography variant="caption" color="error.main">
                        Errors: {job.errors_count}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  {job.duration_seconds ? (
                    <Typography variant="body2">
                      {Math.floor(job.duration_seconds / 60)}m {job.duration_seconds % 60}s
                    </Typography>
                  ) : job.status === 'running' && job.started_at ? (
                    <Typography variant="body2" color="info.main">
                      Running...
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      -
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Tooltip title={new Date(job.created_at).toLocaleString()}>
                    <Typography variant="body2">
                      {new Date(job.created_at).toLocaleDateString()}
                    </Typography>
                  </Tooltip>
                </TableCell>
                <TableCell align="right">
                  {job.status === 'pending' && (
                    <IconButton 
                      size="small" 
                      onClick={() => handleStartJob(job.id)}
                      title="Start Job"
                      color="primary"
                    >
                      <StartIcon />
                    </IconButton>
                  )}
                  <IconButton 
                    size="small" 
                    onClick={() => handleViewJob(job)}
                    title="View Details"
                  >
                    <ViewIcon />
                  </IconButton>
                  <IconButton 
                    size="small" 
                    onClick={(e) => handleMenuOpen(e, job.id)}
                    title="More Actions"
                  >
                    <MoreIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[10, 20, 50]}
          component="div"
          count={totalJobs}
          rowsPerPage={filters.size}
          page={filters.page}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          if (menuJobId) {
            const job = jobs.find(j => j.id === menuJobId);
            if (job) handleEditJob(job);
          }
          handleMenuClose();
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => {
          if (menuJobId) handleDeleteJob(menuJobId);
          handleMenuClose();
        }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Job Dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'create' ? 'Create Discovery Job' : 
           dialogType === 'edit' ? 'Edit Discovery Job' : 'Job Details'}
        </DialogTitle>
        <DialogContent>
          <Typography>Discovery job form placeholder</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
          {dialogType !== 'view' && (
            <Button onClick={handleSaveJob} variant="contained">
              Save
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DiscoveryPage;
