name: Essential Checks

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  quick-checks:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety ruff

    - name: Quick Security Scan
      working-directory: ./backend
      run: |
        bandit -r app/ -ll
        safety check --short-report

    - name: Code Quality Check
      working-directory: ./backend
      run: |
        ruff check app/ tests/
    

  codeql-analysis:
    name: CodeQL Security Analysis
    runs-on: ubuntu-latest
    if: github.event_name == 'push'  # Only on push to main/master
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: python
        queries: security-extended

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
