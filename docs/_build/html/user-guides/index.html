

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>User Guides &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script>window.MathJax = {"options": {"processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
      <script defer="defer" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="SOC Operators Guide" href="soc-operators.html" />
    <link rel="prev" title="Quick Start Guide" href="../quick-start-guide.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="soc-operators.html">SOC Operators Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="security-architects.html">Security Architects Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="red-team-members.html">Red Team Members Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="purple-team-members.html">Purple Team Members Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="administrators.html">Administrators Guide</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="attack-path-analysis.html">Attack Path Analysis User Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="mitre-attack-integration.html">MITRE ATT&amp;CK Integration User Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#threat-modeling">Threat Modeling</a><ul>
<li class="toctree-l4"><a class="reference internal" href="threat-modeling.html">Threat Modeling User Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#asset-discovery-and-management">Asset Discovery and Management</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#threat-intelligence-integration">Threat Intelligence Integration</a><ul class="simple">
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">User Guides</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/user-guides/index.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="user-guides">
<h1>User Guides<a class="headerlink" href="#user-guides" title="Link to this heading"></a></h1>
<p>This section provides comprehensive user guides for different roles and use cases within the Blast-Radius Security Tool. Each guide is tailored to specific user personas and their typical workflows.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Blast-Radius Security Tool serves multiple security roles with specialized interfaces and capabilities:</p>
<ul class="simple">
<li><p><strong>SOC Operators</strong>: Real-time monitoring, incident response, and threat detection</p></li>
<li><p><strong>Security Architects</strong>: Infrastructure design validation and security control optimization</p></li>
<li><p><strong>Red Team Members</strong>: Attack simulation, penetration testing, and vulnerability assessment</p></li>
<li><p><strong>Purple Team Members</strong>: Collaborative security testing and validation</p></li>
<li><p><strong>Administrators</strong>: System configuration, user management, and maintenance</p></li>
<li><p><strong>Attack Path Analysts</strong>: Specialized threat modeling and risk assessment</p></li>
</ul>
</section>
<section id="role-based-guides">
<h2>Role-Based Guides<a class="headerlink" href="#role-based-guides" title="Link to this heading"></a></h2>
<section id="soc-operators">
<h3>SOC Operators<a class="headerlink" href="#soc-operators" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="soc-operators.html">SOC Operators Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#real-time-monitoring">Real-time Monitoring</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#incident-response">Incident Response</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#reporting-and-documentation">Reporting and Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#common-scenarios">Common Scenarios</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="soc-operators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
<p>Complete guide for Security Operations Center personnel covering:</p>
<ul class="simple">
<li><p><strong>Real-time Monitoring</strong>: Dashboard configuration and alert management</p></li>
<li><p><strong>Incident Response</strong>: Attack path analysis during security incidents</p></li>
<li><p><strong>Threat Detection</strong>: Automated threat correlation and IOC management</p></li>
<li><p><strong>Escalation Procedures</strong>: When and how to escalate security events</p></li>
<li><p><strong>Reporting</strong>: Generate executive and technical reports</p></li>
</ul>
<p><strong>Key Features for SOC Operators:</strong></p>
<ul class="simple">
<li><p>24/7 monitoring dashboards with real-time attack path updates</p></li>
<li><p>Automated alert correlation with MITRE ATT&amp;CK framework</p></li>
<li><p>Incident response playbooks with blast radius assessment</p></li>
<li><p>Integration with SIEM and SOAR platforms</p></li>
<li><p>Mobile-friendly interface for on-call response</p></li>
</ul>
</section>
<section id="security-architects">
<h3>Security Architects<a class="headerlink" href="#security-architects" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="security-architects.html">Security Architects Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#risk-assessment-and-analysis">Risk Assessment and Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#security-architecture-design">Security Architecture Design</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#compliance-and-governance">Compliance and Governance</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#advanced-analytics-and-reporting">Advanced Analytics and Reporting</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#architecture-planning-and-design">Architecture Planning and Design</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#best-practices-for-security-architects">Best Practices for Security Architects</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#common-use-cases-and-scenarios">Common Use Cases and Scenarios</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="security-architects.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
<p>Comprehensive guide for security architects and engineers:</p>
<ul class="simple">
<li><p><strong>Architecture Validation</strong>: Verify security design effectiveness</p></li>
<li><p><strong>Control Placement</strong>: Optimize security control deployment</p></li>
<li><p><strong>Risk Assessment</strong>: Quantify architectural security risks</p></li>
<li><p><strong>Compliance Mapping</strong>: Ensure regulatory compliance</p></li>
<li><p><strong>Design Recommendations</strong>: Data-driven security improvements</p></li>
</ul>
<p><strong>Key Features for Security Architects:</strong></p>
<ul class="simple">
<li><p>Attack path modeling for proposed architectures</p></li>
<li><p>Security control effectiveness analysis</p></li>
<li><p>Compliance framework mapping (SOC2, ISO27001, PCI-DSS)</p></li>
<li><p>Risk quantification with business impact assessment</p></li>
<li><p>Integration with architecture documentation tools</p></li>
</ul>
</section>
<section id="red-team-members">
<h3>Red Team Members<a class="headerlink" href="#red-team-members" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="red-team-members.html">Red Team Members Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#attack-path-discovery-and-analysis">Attack Path Discovery and Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#attack-simulation-and-testing">Attack Simulation and Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#campaign-management-and-planning">Campaign Management and Planning</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#advanced-red-team-techniques">Advanced Red Team Techniques</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#reporting-and-documentation">Reporting and Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#best-practices-for-red-team-operations">Best Practices for Red Team Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#common-red-team-scenarios">Common Red Team Scenarios</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="red-team-members.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
<p>Specialized guide for offensive security professionals:</p>
<ul class="simple">
<li><p><strong>Attack Planning</strong>: Use attack path analysis for realistic scenarios</p></li>
<li><p><strong>Target Identification</strong>: Find high-value targets and optimal attack routes</p></li>
<li><p><strong>Simulation Execution</strong>: Execute attacks following discovered paths</p></li>
<li><p><strong>Evasion Techniques</strong>: Understand detection capabilities and blind spots</p></li>
<li><p><strong>Reporting</strong>: Document attack paths and provide remediation guidance</p></li>
</ul>
<p><strong>Key Features for Red Team Members:</strong></p>
<ul class="simple">
<li><p>Attack path discovery with MITRE ATT&amp;CK technique mapping</p></li>
<li><p>Stealth assessment with detection probability calculation</p></li>
<li><p>Custom attack scenario creation and modeling</p></li>
<li><p>Integration with penetration testing tools</p></li>
<li><p>Detailed attack documentation and reporting</p></li>
</ul>
</section>
<section id="purple-team-members">
<h3>Purple Team Members<a class="headerlink" href="#purple-team-members" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="purple-team-members.html">Purple Team Members Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#collaborative-security-testing">Collaborative Security Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#threat-hunting-operations">Threat Hunting Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#communication-and-collaboration">Communication and Collaboration</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#metrics-and-performance-measurement">Metrics and Performance Measurement</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#advanced-purple-team-techniques">Advanced Purple Team Techniques</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#best-practices-for-purple-team-operations">Best Practices for Purple Team Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#common-purple-team-scenarios">Common Purple Team Scenarios</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="purple-team-members.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
<p>Guide for collaborative security testing teams:</p>
<ul class="simple">
<li><p><strong>Collaborative Planning</strong>: Joint red/blue team exercise planning</p></li>
<li><p><strong>Real-time Coordination</strong>: Coordinate attacks and defenses</p></li>
<li><p><strong>Detection Validation</strong>: Test and improve detection capabilities</p></li>
<li><p><strong>Response Testing</strong>: Validate incident response procedures</p></li>
<li><p><strong>Continuous Improvement</strong>: Iterative security enhancement</p></li>
</ul>
<p><strong>Key Features for Purple Team Members:</strong></p>
<ul class="simple">
<li><p>Shared attack scenario planning and execution</p></li>
<li><p>Real-time collaboration tools and communication</p></li>
<li><p>Detection effectiveness measurement and reporting</p></li>
<li><p>Response time analysis and optimization</p></li>
<li><p>Joint exercise documentation and lessons learned</p></li>
</ul>
</section>
<section id="administrators">
<h3>Administrators<a class="headerlink" href="#administrators" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="administrators.html">Administrators Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l2"><a class="reference internal" href="administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
<p>Complete administrative guide covering:</p>
<ul class="simple">
<li><p><strong>System Configuration</strong>: Initial setup and ongoing maintenance</p></li>
<li><p><strong>User Management</strong>: Role-based access control and permissions</p></li>
<li><p><strong>Integration Setup</strong>: Connect with existing security tools</p></li>
<li><p><strong>Performance Monitoring</strong>: System health and optimization</p></li>
<li><p><strong>Backup and Recovery</strong>: Data protection and disaster recovery</p></li>
</ul>
<p><strong>Key Features for Administrators:</strong></p>
<ul class="simple">
<li><p>Comprehensive system configuration and tuning</p></li>
<li><p>Advanced user and role management</p></li>
<li><p>Integration with enterprise authentication systems</p></li>
<li><p>Performance monitoring and capacity planning</p></li>
<li><p>Automated backup and recovery procedures</p></li>
</ul>
</section>
</section>
<section id="feature-specific-guides">
<h2>Feature-Specific Guides<a class="headerlink" href="#feature-specific-guides" title="Link to this heading"></a></h2>
<section id="attack-path-analysis">
<h3>Attack Path Analysis<a class="headerlink" href="#attack-path-analysis" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</div>
<p>Comprehensive guide to attack path analysis capabilities:</p>
<ul class="simple">
<li><p><strong>Graph-Based Analysis</strong>: Understanding the attack path engine</p></li>
<li><p><strong>Risk Assessment</strong>: Multi-factor risk scoring methodology</p></li>
<li><p><strong>Blast Radius Calculation</strong>: Impact assessment and containment planning</p></li>
<li><p><strong>MITRE ATT&amp;CK Integration</strong>: Framework-based threat modeling</p></li>
<li><p><strong>Scenario Modeling</strong>: Complex attack scenario creation and analysis</p></li>
</ul>
<p><strong>Key Capabilities:</strong></p>
<ul class="simple">
<li><p>Multi-hop attack path discovery with weighted relationships</p></li>
<li><p>Real-time blast radius calculation with financial impact</p></li>
<li><p>Complete MITRE ATT&amp;CK framework integration (14 tactics, 800+ techniques)</p></li>
<li><p>Advanced attack scenario modeling with threat actor profiling</p></li>
<li><p>Risk prioritization with business criticality weighting</p></li>
</ul>
</section>
<section id="mitre-att-ck-integration">
<h3>MITRE ATT&amp;CK Integration<a class="headerlink" href="#mitre-att-ck-integration" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="mitre-attack-integration.html">MITRE ATT&amp;CK Integration User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#mitre-att-ck-data-management">MITRE ATT&amp;CK Data Management</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#technique-correlation-engine">Technique Correlation Engine</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#advanced-analytics">Advanced Analytics</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#api-integration">API Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="mitre-attack-integration.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</div>
<p>Comprehensive guide to MITRE ATT&amp;CK framework integration:</p>
<ul class="simple">
<li><p><strong>Real-time Correlation</strong>: Automatic technique correlation from security events</p></li>
<li><p><strong>Threat Actor Attribution</strong>: Automated attribution with confidence scoring</p></li>
<li><p><strong>Attack Pattern Recognition</strong>: AI-powered pattern identification and analysis</p></li>
<li><p><strong>ATT&amp;CK Navigator Integration</strong>: Automated heat map generation and visualization</p></li>
<li><p><strong>Threat Intelligence Enrichment</strong>: IOC enhancement with ATT&amp;CK context</p></li>
</ul>
<p><strong>Key Capabilities:</strong></p>
<ul class="simple">
<li><p>Complete framework coverage (Enterprise, Mobile, ICS domains)</p></li>
<li><p>STIX 2.0/2.1 data parsing and management</p></li>
<li><p>Sub-second technique correlation from security events</p></li>
<li><p>Automated threat actor attribution with confidence scoring</p></li>
<li><p>Attack pattern recognition using machine learning</p></li>
<li><p>ATT&amp;CK Navigator heat map generation and export</p></li>
</ul>
</section>
<section id="threat-modeling">
<h3>Threat Modeling<a class="headerlink" href="#threat-modeling" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="threat-modeling.html">Threat Modeling User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#threat-actor-profiles">Threat Actor Profiles</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#attack-simulation">Attack Simulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#quantitative-risk-assessment">Quantitative Risk Assessment</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#mitigation-strategy-generation">Mitigation Strategy Generation</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#advanced-features">Advanced Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="threat-modeling.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</div>
<p>Advanced threat modeling and quantitative risk assessment:</p>
<ul class="simple">
<li><p><strong>Quantitative Risk Assessment</strong>: Mathematical risk calculation with business impact</p></li>
<li><p><strong>Attack Success Probability</strong>: Statistical modeling of attack likelihood</p></li>
<li><p><strong>Financial Impact Assessment</strong>: Monetary loss calculation and recovery costs</p></li>
<li><p><strong>Compliance Impact Analysis</strong>: Regulatory violation identification</p></li>
<li><p><strong>Mitigation Strategy Generation</strong>: AI-driven security control recommendations</p></li>
</ul>
<p><strong>Key Capabilities:</strong></p>
<ul class="simple">
<li><p>Pre-loaded threat actor profiles (APT29, APT28, FIN7, etc.)</p></li>
<li><p>Attack success probability modeling with confidence intervals</p></li>
<li><p>Financial impact assessment with regulatory compliance analysis</p></li>
<li><p>Time-to-compromise estimation based on security controls</p></li>
<li><p>Detection probability analysis with monitoring coverage assessment</p></li>
</ul>
</section>
<section id="asset-discovery-and-management">
<h3>Asset Discovery and Management<a class="headerlink" href="#asset-discovery-and-management" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
</div>
<p>Guide to comprehensive asset discovery and management:</p>
<ul class="simple">
<li><p><strong>Multi-Cloud Discovery</strong>: AWS, Azure, GCP asset enumeration</p></li>
<li><p><strong>Network Scanning</strong>: Active and passive network discovery</p></li>
<li><p><strong>Metadata Collection</strong>: Comprehensive asset attribute gathering</p></li>
<li><p><strong>Relationship Mapping</strong>: Dependency and communication analysis</p></li>
<li><p><strong>Inventory Management</strong>: Asset lifecycle and change tracking</p></li>
</ul>
</section>
<section id="threat-intelligence-integration">
<h3>Threat Intelligence Integration<a class="headerlink" href="#threat-intelligence-integration" title="Link to this heading"></a></h3>
<div class="toctree-wrapper compound">
</div>
<p>Guide to threat intelligence capabilities:</p>
<ul class="simple">
<li><p><strong>IOC Management</strong>: Indicator collection and correlation</p></li>
<li><p><strong>Threat Actor Profiling</strong>: Attribution and capability assessment</p></li>
<li><p><strong>Feed Integration</strong>: External threat intelligence sources</p></li>
<li><p><strong>Automated Correlation</strong>: Real-time threat matching</p></li>
<li><p><strong>Intelligence Reporting</strong>: Actionable threat intelligence</p></li>
</ul>
</section>
</section>
<section id="common-workflows">
<h2>Common Workflows<a class="headerlink" href="#common-workflows" title="Link to this heading"></a></h2>
<section id="security-assessment-workflow">
<h3>Security Assessment Workflow<a class="headerlink" href="#security-assessment-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Asset Discovery</strong></p>
<ul class="simple">
<li><p>Scan infrastructure for assets and relationships</p></li>
<li><p>Validate asset metadata and classifications</p></li>
<li><p>Map dependencies and communication flows</p></li>
</ul>
</li>
<li><p><strong>Attack Path Analysis</strong></p>
<ul class="simple">
<li><p>Identify potential entry points (public-facing assets)</p></li>
<li><p>Discover attack paths to high-value targets</p></li>
<li><p>Calculate risk scores and prioritize threats</p></li>
</ul>
</li>
<li><p><strong>Risk Assessment</strong></p>
<ul class="simple">
<li><p>Evaluate business impact of identified attack paths</p></li>
<li><p>Assess compliance implications</p></li>
<li><p>Generate risk reports for stakeholders</p></li>
</ul>
</li>
<li><p><strong>Mitigation Planning</strong></p>
<ul class="simple">
<li><p>Review recommended security controls</p></li>
<li><p>Plan implementation based on risk priority</p></li>
<li><p>Estimate costs and timelines for improvements</p></li>
</ul>
</li>
</ol>
</section>
<section id="incident-response-workflow">
<h3>Incident Response Workflow<a class="headerlink" href="#incident-response-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Initial Assessment</strong></p>
<ul class="simple">
<li><p>Identify compromised assets</p></li>
<li><p>Calculate blast radius from compromise point</p></li>
<li><p>Assess potential impact and scope</p></li>
</ul>
</li>
<li><p><strong>Containment Planning</strong></p>
<ul class="simple">
<li><p>Analyze attack paths for isolation points</p></li>
<li><p>Prioritize containment actions</p></li>
<li><p>Coordinate response team activities</p></li>
</ul>
</li>
<li><p><strong>Investigation</strong></p>
<ul class="simple">
<li><p>Model likely attack paths used</p></li>
<li><p>Identify systems requiring forensic analysis</p></li>
<li><p>Document attack progression</p></li>
</ul>
</li>
<li><p><strong>Recovery and Lessons Learned</strong></p>
<ul class="simple">
<li><p>Plan recovery sequence based on dependencies</p></li>
<li><p>Update security controls based on findings</p></li>
<li><p>Improve detection and response capabilities</p></li>
</ul>
</li>
</ol>
</section>
<section id="red-team-exercise-workflow">
<h3>Red Team Exercise Workflow<a class="headerlink" href="#red-team-exercise-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Planning Phase</strong></p>
<ul class="simple">
<li><p>Define exercise objectives and scope</p></li>
<li><p>Create realistic attack scenarios</p></li>
<li><p>Plan attack paths and techniques</p></li>
</ul>
</li>
<li><p><strong>Execution Phase</strong></p>
<ul class="simple">
<li><p>Follow discovered attack paths</p></li>
<li><p>Test detection and response capabilities</p></li>
<li><p>Document successful attack vectors</p></li>
</ul>
</li>
<li><p><strong>Analysis Phase</strong></p>
<ul class="simple">
<li><p>Compare predicted vs. actual attack paths</p></li>
<li><p>Analyze detection gaps and response times</p></li>
<li><p>Generate improvement recommendations</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="general-guidelines">
<h3>General Guidelines<a class="headerlink" href="#general-guidelines" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Start with Discovery</strong>: Ensure comprehensive asset discovery before analysis</p></li>
<li><p><strong>Regular Updates</strong>: Keep asset data and relationships current</p></li>
<li><p><strong>Collaborative Approach</strong>: Involve multiple teams in security analysis</p></li>
<li><p><strong>Continuous Improvement</strong>: Regularly review and update security posture</p></li>
<li><p><strong>Documentation</strong>: Maintain detailed records of analysis and decisions</p></li>
</ol>
</section>
<section id="role-specific-tips">
<h3>Role-Specific Tips<a class="headerlink" href="#role-specific-tips" title="Link to this heading"></a></h3>
<p><strong>For SOC Operators:</strong></p>
<ul class="simple">
<li><p>Configure dashboards for your specific monitoring needs</p></li>
<li><p>Set up automated alerts for critical attack paths</p></li>
<li><p>Practice incident response procedures regularly</p></li>
<li><p>Maintain situational awareness of current threat landscape</p></li>
</ul>
<p><strong>For Security Architects:</strong></p>
<ul class="simple">
<li><p>Model security controls before implementation</p></li>
<li><p>Validate architectural changes with attack path analysis</p></li>
<li><p>Consider business impact in security design decisions</p></li>
<li><p>Document security architecture decisions and rationale</p></li>
</ul>
<p><strong>For Red Team Members:</strong></p>
<ul class="simple">
<li><p>Use realistic attack scenarios based on current threats</p></li>
<li><p>Focus on high-impact attack paths for maximum effect</p></li>
<li><p>Coordinate with blue team for effective exercises</p></li>
<li><p>Provide actionable recommendations for improvement</p></li>
</ul>
<p><strong>For Administrators:</strong></p>
<ul class="simple">
<li><p>Monitor system performance and capacity regularly</p></li>
<li><p>Keep integrations and data sources current</p></li>
<li><p>Implement proper backup and recovery procedures</p></li>
<li><p>Plan for system growth and scaling needs</p></li>
</ul>
</section>
</section>
<section id="getting-help">
<h2>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<section id="support-resources">
<h3>Support Resources<a class="headerlink" href="#support-resources" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Documentation</strong>: Comprehensive guides and API reference</p></li>
<li><p><strong>Community Forum</strong>: User community for questions and best practices</p></li>
<li><p><strong>Support Portal</strong>: Technical support for enterprise customers</p></li>
<li><p><strong>Training</strong>: Role-based training programs and certification</p></li>
</ul>
<p><strong>Contact Information:</strong></p>
<ul class="simple">
<li><p><strong>Technical Support</strong>: <a class="reference external" href="mailto:support&#37;&#52;&#48;blast-radius&#46;com">support<span>&#64;</span>blast-radius<span>&#46;</span>com</a></p></li>
<li><p><strong>Sales and Licensing</strong>: <a class="reference external" href="mailto:sales&#37;&#52;&#48;blast-radius&#46;com">sales<span>&#64;</span>blast-radius<span>&#46;</span>com</a></p></li>
<li><p><strong>Community Forum</strong>: <a class="reference external" href="https://community.blast-radius.com">https://community.blast-radius.com</a></p></li>
<li><p><strong>Documentation</strong>: <a class="reference external" href="https://docs.blast-radius.com">https://docs.blast-radius.com</a></p></li>
</ul>
</section>
<section id="training-and-certification">
<h3>Training and Certification<a class="headerlink" href="#training-and-certification" title="Link to this heading"></a></h3>
<p><strong>Available Training Programs:</strong></p>
<ul class="simple">
<li><p><strong>Fundamentals</strong>: Basic concepts and navigation (2 hours)</p></li>
<li><p><strong>Attack Path Analysis</strong>: Advanced threat modeling (4 hours)</p></li>
<li><p><strong>Administration</strong>: System setup and management (6 hours)</p></li>
<li><p><strong>Integration</strong>: Connecting with existing tools (3 hours)</p></li>
</ul>
<p><strong>Certification Levels:</strong></p>
<ul class="simple">
<li><p><strong>Certified User</strong>: Basic proficiency in core features</p></li>
<li><p><strong>Certified Analyst</strong>: Advanced attack path analysis skills</p></li>
<li><p><strong>Certified Administrator</strong>: System administration expertise</p></li>
</ul>
</section>
<section id="feedback-and-contributions">
<h3>Feedback and Contributions<a class="headerlink" href="#feedback-and-contributions" title="Link to this heading"></a></h3>
<p>We welcome feedback and contributions from our user community:</p>
<ul class="simple">
<li><p><strong>Feature Requests</strong>: Submit ideas for new capabilities</p></li>
<li><p><strong>Bug Reports</strong>: Report issues and problems</p></li>
<li><p><strong>Documentation</strong>: Contribute to user guides and examples</p></li>
<li><p><strong>Community</strong>: Share best practices and use cases</p></li>
</ul>
<p><strong>Contributing Guidelines:</strong></p>
<ol class="arabic simple">
<li><p>Check existing documentation and issues before submitting</p></li>
<li><p>Provide detailed descriptions and examples</p></li>
<li><p>Follow community guidelines and code of conduct</p></li>
<li><p>Participate constructively in discussions</p></li>
</ol>
<p>This comprehensive user guide collection provides everything needed to effectively use the Blast-Radius Security Tool across all security roles and use cases.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../quick-start-guide.html" class="btn btn-neutral float-left" title="Quick Start Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="soc-operators.html" class="btn btn-neutral float-right" title="SOC Operators Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>