

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Attack Path Analysis User Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="MITRE ATT&amp;CK Integration User Guide" href="mitre-attack-integration.html" />
    <link rel="prev" title="Administrators Guide" href="administrators.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">User Guides</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#role-based-guides">Role-Based Guides</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#feature-specific-guides">Feature-Specific Guides</a><ul class="current">
<li class="toctree-l3 current"><a class="reference internal" href="index.html#attack-path-analysis">Attack Path Analysis</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Attack Path Analysis User Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-workflows">Common Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">User Guides</a></li>
      <li class="breadcrumb-item active">Attack Path Analysis User Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/user-guides/attack-path-analysis.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="attack-path-analysis-user-guide">
<h1>Attack Path Analysis User Guide<a class="headerlink" href="#attack-path-analysis-user-guide" title="Link to this heading"></a></h1>
<p>This comprehensive guide covers how to use the enhanced attack path analysis features of the Blast-Radius Security Tool for advanced threat modeling, quantitative risk assessment, and incident response.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This guide covers the enhanced attack path analysis engine with MITRE ATT&amp;CK integration,
threat actor modeling, and quantitative risk assessment capabilities introduced in v1.0.0.</p>
</div>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The attack path analysis engine provides:</p>
<ul class="simple">
<li><p><strong>🕸️ Attack Path Discovery</strong>: Find multi-hop attack paths through your infrastructure</p></li>
<li><p><strong>💥 Blast Radius Calculation</strong>: Assess impact from compromised assets with financial modeling</p></li>
<li><p><strong>🎭 Attack Scenario Modeling</strong>: Model complex threat actor campaigns with success probability</p></li>
<li><p><strong>🛡️ MITRE ATT&amp;CK Integration</strong>: Framework-based threat analysis with 300+ techniques</p></li>
<li><p><strong>📊 Quantitative Risk Assessment</strong>: Mathematical risk scoring with business impact analysis</p></li>
<li><p><strong>🧠 Threat Actor Intelligence</strong>: Pre-loaded APT profiles with sophistication modeling</p></li>
<li><p><strong>⚡ Performance Optimized</strong>: Sub-second analysis for 10M+ node infrastructures</p></li>
<li><p><strong>🔒 Compliance Integration</strong>: Automatic regulatory violation identification (GDPR, HIPAA, SOX)</p></li>
</ul>
</section>
<section id="getting-started">
<h2>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<p>Before using attack path analysis, ensure you have:</p>
<ol class="arabic simple">
<li><p><strong>Asset Discovery Completed</strong>: Your infrastructure assets are discovered and mapped</p></li>
<li><p><strong>Relationships Defined</strong>: Asset relationships and dependencies are established</p></li>
<li><p><strong>Proper Permissions</strong>: Access to attack path analysis features</p></li>
<li><p><strong>Graph Data Populated</strong>: The graph engine has current asset and relationship data</p></li>
</ol>
</section>
<section id="quick-start-example">
<h3>Quick Start Example<a class="headerlink" href="#quick-start-example" title="Link to this heading"></a></h3>
<p>Here’s a simple example to get you started:</p>
<ol class="arabic">
<li><p><strong>Navigate to Attack Path Analysis</strong></p>
<p>Go to the Attack Path Analysis section in the web interface or use the API directly.</p>
</li>
<li><p><strong>Select Source Asset</strong></p>
<p>Choose a potential entry point (e.g., public-facing web server, user workstation).</p>
</li>
<li><p><strong>Define Targets</strong></p>
<p>Select high-value targets (e.g., databases, critical servers, sensitive data stores).</p>
</li>
<li><p><strong>Run Analysis</strong></p>
<p>Execute the attack path analysis to discover potential attack routes.</p>
</li>
<li><p><strong>Review Results</strong></p>
<p>Examine discovered paths, risk scores, and recommended mitigations.</p>
</li>
</ol>
</section>
</section>
<section id="core-concepts">
<h2>Core Concepts<a class="headerlink" href="#core-concepts" title="Link to this heading"></a></h2>
<section id="attack-path-types">
<h3>Attack Path Types<a class="headerlink" href="#attack-path-types" title="Link to this heading"></a></h3>
<p>The system identifies six types of attack paths:</p>
<dl>
<dt><strong>1. Direct Attacks</strong></dt><dd><p>Simple point-to-point attacks with minimal hops.</p>
<p><em>Example</em>: Internet → Web Server → Database</p>
</dd>
<dt><strong>2. Lateral Movement</strong></dt><dd><p>Multi-hop attacks across network segments.</p>
<p><em>Example</em>: Workstation → File Server → Domain Controller → Database</p>
</dd>
<dt><strong>3. Privilege Escalation</strong></dt><dd><p>Attacks that gain progressively higher privileges.</p>
<p><em>Example</em>: User Account → Service Account → Admin Account → Critical System</p>
</dd>
<dt><strong>4. Data Exfiltration</strong></dt><dd><p>Attacks specifically targeting data assets.</p>
<p><em>Example</em>: Compromised Endpoint → Network Share → Backup Server → Cloud Storage</p>
</dd>
<dt><strong>5. Supply Chain</strong></dt><dd><p>Attacks through third-party dependencies.</p>
<p><em>Example</em>: Vendor System → Management Interface → Production Environment</p>
</dd>
<dt><strong>6. Insider Threat</strong></dt><dd><p>Attacks from internal actors with existing access.</p>
<p><em>Example</em>: Employee Workstation → Admin Tools → Critical Infrastructure</p>
</dd>
</dl>
</section>
<section id="risk-scoring-methodology">
<h3>Risk Scoring Methodology<a class="headerlink" href="#risk-scoring-methodology" title="Link to this heading"></a></h3>
<p>Attack paths are scored using multiple factors:</p>
<dl class="simple">
<dt><strong>Risk Score (0-100)</strong></dt><dd><ul class="simple">
<li><p>Asset risk scores (weighted average)</p></li>
<li><p>Path complexity (length and difficulty)</p></li>
<li><p>Security controls (encryption, authentication, monitoring)</p></li>
<li><p>Business criticality of target assets</p></li>
</ul>
</dd>
<dt><strong>Likelihood (0-1)</strong></dt><dd><ul class="simple">
<li><p>Base probability of success</p></li>
<li><p>Security control effectiveness</p></li>
<li><p>Monitoring and detection capabilities</p></li>
<li><p>Authentication requirements</p></li>
</ul>
</dd>
<dt><strong>Impact Score (0-100)</strong></dt><dd><ul class="simple">
<li><p>Business criticality of target</p></li>
<li><p>Data classification level</p></li>
<li><p>PII and compliance considerations</p></li>
<li><p>Financial and operational impact</p></li>
</ul>
</dd>
<dt><strong>Criticality Levels</strong></dt><dd><ul class="simple">
<li><p><strong>CRITICAL</strong> (80-100): Immediate attention required</p></li>
<li><p><strong>HIGH</strong> (60-79): High priority for remediation</p></li>
<li><p><strong>MEDIUM</strong> (40-59): Moderate risk, plan remediation</p></li>
<li><p><strong>LOW</strong> (0-39): Lower priority, monitor</p></li>
</ul>
</dd>
</dl>
</section>
</section>
<section id="using-the-web-interface">
<h2>Using the Web Interface<a class="headerlink" href="#using-the-web-interface" title="Link to this heading"></a></h2>
<section id="attack-path-discovery">
<h3>Attack Path Discovery<a class="headerlink" href="#attack-path-discovery" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Access the Interface</strong></p>
<p>Navigate to <strong>Security Analysis</strong> → <strong>Attack Paths</strong> in the main menu.</p>
</li>
<li><p><strong>Configure Analysis</strong></p>
<ul class="simple">
<li><p><strong>Source Asset</strong>: Select or search for the starting point</p></li>
<li><p><strong>Target Assets</strong>: Choose specific targets or use “All High-Value Targets”</p></li>
<li><p><strong>Max Path Length</strong>: Set maximum number of hops (default: 5)</p></li>
<li><p><strong>Max Paths</strong>: Limit number of results per target (default: 5)</p></li>
</ul>
</li>
<li><p><strong>Execute Analysis</strong></p>
<p>Click <strong>“Analyze Attack Paths”</strong> to start the analysis.</p>
</li>
<li><p><strong>Review Results</strong></p>
<p>Results are displayed with:
* Visual path representation
* Risk scores and criticality levels
* MITRE ATT&amp;CK technique mappings
* Estimated attack time and resources
* Recommended mitigations</p>
</li>
</ol>
</section>
<section id="blast-radius-analysis">
<h3>Blast Radius Analysis<a class="headerlink" href="#blast-radius-analysis" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Select Compromised Asset</strong></p>
<p>Choose an asset to simulate compromise.</p>
</li>
<li><p><strong>Configure Parameters</strong></p>
<ul class="simple">
<li><p><strong>Max Degrees</strong>: Set propagation depth (default: 5)</p></li>
<li><p><strong>Impact Factors</strong>: Choose what to include in impact calculation</p></li>
</ul>
</li>
<li><p><strong>Run Calculation</strong></p>
<p>Execute blast radius analysis.</p>
</li>
<li><p><strong>Analyze Results</strong></p>
<p>Review:
* Total affected assets
* Impact by degree of separation
* Critical and data assets affected
* Financial impact estimation
* Recovery time estimates
* Compliance implications</p>
</li>
</ol>
</section>
<section id="attack-scenario-modeling">
<h3>Attack Scenario Modeling<a class="headerlink" href="#attack-scenario-modeling" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Create New Scenario</strong></p>
<p>Click <strong>“New Attack Scenario”</strong> and provide:
* Scenario name and description
* Threat actor or group
* Entry points (multiple allowed)
* Objectives (target assets)</p>
</li>
<li><p><strong>Configure Threat Actor</strong></p>
<p>Select or define:
* Capability level
* Available resources
* Typical techniques used
* Persistence requirements</p>
</li>
<li><p><strong>Generate Scenario</strong></p>
<p>The system will:
* Find all possible attack paths
* Calculate scenario risk and likelihood
* Estimate required resources and time
* Generate MITRE ATT&amp;CK mappings
* Suggest detection and mitigation strategies</p>
</li>
<li><p><strong>Review and Export</strong></p>
<ul class="simple">
<li><p>Examine complete scenario analysis</p></li>
<li><p>Export reports for stakeholders</p></li>
<li><p>Save scenarios for future reference</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="using-the-api">
<h2>Using the API<a class="headerlink" href="#using-the-api" title="Link to this heading"></a></h2>
<section id="basic-attack-path-analysis">
<h3>Basic Attack Path Analysis<a class="headerlink" href="#basic-attack-path-analysis" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>

<span class="c1"># Configure API client</span>
<span class="n">api_base</span> <span class="o">=</span> <span class="s2">&quot;https://your-blast-radius-instance.com/api/v1&quot;</span>
<span class="n">headers</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer your-jwt-token&quot;</span><span class="p">}</span>

<span class="c1"># Analyze attack paths</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">api_base</span><span class="si">}</span><span class="s2">/attack-paths/analyze&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;source_asset_id&quot;</span><span class="p">:</span> <span class="s2">&quot;web_server_001&quot;</span><span class="p">,</span>
        <span class="s2">&quot;target_asset_ids&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span> <span class="s2">&quot;backup_server_001&quot;</span><span class="p">],</span>
        <span class="s2">&quot;max_path_length&quot;</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span>
        <span class="s2">&quot;max_paths_per_target&quot;</span><span class="p">:</span> <span class="mi">3</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">paths</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

<span class="c1"># Process results</span>
<span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">paths</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Path: </span><span class="si">{</span><span class="s1">&#39; → &#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">path</span><span class="p">[</span><span class="s1">&#39;path_nodes&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Risk Score: </span><span class="si">{</span><span class="n">path</span><span class="p">[</span><span class="s1">&#39;risk_score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Criticality: </span><span class="si">{</span><span class="n">path</span><span class="p">[</span><span class="s1">&#39;criticality_level&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Techniques: </span><span class="si">{</span><span class="s1">&#39;, &#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">path</span><span class="p">[</span><span class="s1">&#39;attack_techniques&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;---&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="blast-radius-calculation">
<h3>Blast Radius Calculation<a class="headerlink" href="#blast-radius-calculation" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Calculate blast radius</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">api_base</span><span class="si">}</span><span class="s2">/attack-paths/blast-radius&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;source_asset_id&quot;</span><span class="p">:</span> <span class="s2">&quot;compromised_server&quot;</span><span class="p">,</span>
        <span class="s2">&quot;max_degrees&quot;</span><span class="p">:</span> <span class="mi">3</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">blast_result</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Affected Assets: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">blast_result</span><span class="p">[</span><span class="s1">&#39;affected_assets&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Critical Assets: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">blast_result</span><span class="p">[</span><span class="s1">&#39;critical_assets_affected&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Financial Impact: $</span><span class="si">{</span><span class="n">blast_result</span><span class="p">[</span><span class="s1">&#39;financial_impact&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Recovery Time: </span><span class="si">{</span><span class="n">blast_result</span><span class="p">[</span><span class="s1">&#39;recovery_time_estimate&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2"> hours&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="attack-scenario-creation">
<h3>Attack Scenario Creation<a class="headerlink" href="#attack-scenario-creation" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create attack scenario</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">api_base</span><span class="si">}</span><span class="s2">/attack-paths/scenarios&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;scenario_name&quot;</span><span class="p">:</span> <span class="s2">&quot;APT Campaign Simulation&quot;</span><span class="p">,</span>
        <span class="s2">&quot;threat_actor&quot;</span><span class="p">:</span> <span class="s2">&quot;APT29&quot;</span><span class="p">,</span>
        <span class="s2">&quot;entry_points&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;internet_gateway&quot;</span><span class="p">,</span> <span class="s2">&quot;email_server&quot;</span><span class="p">],</span>
        <span class="s2">&quot;objectives&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span> <span class="s2">&quot;backup_server_001&quot;</span><span class="p">]</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">scenario</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Scenario: </span><span class="si">{</span><span class="n">scenario</span><span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Risk Score: </span><span class="si">{</span><span class="n">scenario</span><span class="p">[</span><span class="s1">&#39;total_risk_score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Criticality: </span><span class="si">{</span><span class="n">scenario</span><span class="p">[</span><span class="s1">&#39;criticality_level&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Attack Paths: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">scenario</span><span class="p">[</span><span class="s1">&#39;attack_paths&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Mitigation Strategies: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">scenario</span><span class="p">[</span><span class="s1">&#39;mitigation_strategies&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="use-cases-and-workflows">
<h2>Use Cases and Workflows<a class="headerlink" href="#use-cases-and-workflows" title="Link to this heading"></a></h2>
<section id="security-assessment">
<h3>Security Assessment<a class="headerlink" href="#security-assessment" title="Link to this heading"></a></h3>
<p><strong>Objective</strong>: Evaluate security posture and identify critical vulnerabilities.</p>
<p><strong>Workflow</strong>:</p>
<ol class="arabic simple">
<li><p><strong>Baseline Analysis</strong></p>
<ul class="simple">
<li><p>Run attack path analysis from all external entry points</p></li>
<li><p>Identify shortest paths to critical assets</p></li>
<li><p>Document current risk levels</p></li>
</ul>
</li>
<li><p><strong>Vulnerability Impact Assessment</strong></p>
<ul class="simple">
<li><p>Model attack paths from newly discovered vulnerabilities</p></li>
<li><p>Calculate blast radius from compromised systems</p></li>
<li><p>Prioritize remediation based on attack path analysis</p></li>
</ul>
</li>
<li><p><strong>Architecture Review</strong></p>
<ul class="simple">
<li><p>Analyze attack paths in proposed architecture changes</p></li>
<li><p>Validate security control effectiveness</p></li>
<li><p>Identify potential security gaps</p></li>
</ul>
</li>
</ol>
</section>
<section id="incident-response">
<h3>Incident Response<a class="headerlink" href="#incident-response" title="Link to this heading"></a></h3>
<p><strong>Objective</strong>: Assess scope and impact during security incidents.</p>
<p><strong>Workflow</strong>:</p>
<ol class="arabic simple">
<li><p><strong>Initial Assessment</strong></p>
<ul class="simple">
<li><p>Calculate blast radius from confirmed compromised assets</p></li>
<li><p>Identify potentially affected systems</p></li>
<li><p>Estimate business impact</p></li>
</ul>
</li>
<li><p><strong>Containment Planning</strong></p>
<ul class="simple">
<li><p>Analyze attack paths to determine isolation points</p></li>
<li><p>Prioritize containment actions based on blast radius</p></li>
<li><p>Plan recovery sequence</p></li>
</ul>
</li>
<li><p><strong>Forensic Analysis</strong></p>
<ul class="simple">
<li><p>Model likely attack paths used by threat actor</p></li>
<li><p>Identify systems requiring forensic examination</p></li>
<li><p>Document attack progression for lessons learned</p></li>
</ul>
</li>
</ol>
</section>
<section id="red-team-exercises">
<h3>Red Team Exercises<a class="headerlink" href="#red-team-exercises" title="Link to this heading"></a></h3>
<p><strong>Objective</strong>: Plan and execute realistic attack simulations.</p>
<p><strong>Workflow</strong>:</p>
<ol class="arabic simple">
<li><p><strong>Attack Planning</strong></p>
<ul class="simple">
<li><p>Create attack scenarios based on threat intelligence</p></li>
<li><p>Identify optimal attack paths for objectives</p></li>
<li><p>Plan attack sequence and timing</p></li>
</ul>
</li>
<li><p><strong>Exercise Execution</strong></p>
<ul class="simple">
<li><p>Follow discovered attack paths during exercises</p></li>
<li><p>Test detection and response capabilities</p></li>
<li><p>Document successful attack vectors</p></li>
</ul>
</li>
<li><p><strong>Post-Exercise Analysis</strong></p>
<ul class="simple">
<li><p>Compare actual attack paths with predicted paths</p></li>
<li><p>Analyze detection gaps and response effectiveness</p></li>
<li><p>Generate recommendations for improvement</p></li>
</ul>
</li>
</ol>
</section>
<section id="purple-team-collaboration">
<h3>Purple Team Collaboration<a class="headerlink" href="#purple-team-collaboration" title="Link to this heading"></a></h3>
<p><strong>Objective</strong>: Collaborative security testing and validation.</p>
<p><strong>Workflow</strong>:</p>
<ol class="arabic simple">
<li><p><strong>Scenario Development</strong></p>
<ul class="simple">
<li><p>Red team creates attack scenarios using the tool</p></li>
<li><p>Blue team reviews scenarios and prepares defenses</p></li>
<li><p>Agree on exercise parameters and success criteria</p></li>
</ul>
</li>
<li><p><strong>Collaborative Testing</strong></p>
<ul class="simple">
<li><p>Execute attacks following predicted paths</p></li>
<li><p>Blue team monitors and responds in real-time</p></li>
<li><p>Document detection and response effectiveness</p></li>
</ul>
</li>
<li><p><strong>Joint Analysis</strong></p>
<ul class="simple">
<li><p>Review attack path predictions vs. actual results</p></li>
<li><p>Identify gaps in detection and response</p></li>
<li><p>Develop joint improvement recommendations</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="analysis-configuration">
<h3>Analysis Configuration<a class="headerlink" href="#analysis-configuration" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Start Conservative</strong></p>
<ul class="simple">
<li><p>Begin with shorter path lengths (3-4 hops)</p></li>
<li><p>Limit number of paths initially</p></li>
<li><p>Gradually increase scope as needed</p></li>
</ul>
</li>
<li><p><strong>Focus on High-Value Targets</strong></p>
<ul class="simple">
<li><p>Prioritize critical business assets</p></li>
<li><p>Include data stores with sensitive information</p></li>
<li><p>Consider compliance-critical systems</p></li>
</ul>
</li>
<li><p><strong>Regular Updates</strong></p>
<ul class="simple">
<li><p>Refresh analysis after infrastructure changes</p></li>
<li><p>Update after new vulnerabilities are discovered</p></li>
<li><p>Rerun analysis after security control changes</p></li>
</ul>
</li>
</ol>
</section>
<section id="interpretation-guidelines">
<h3>Interpretation Guidelines<a class="headerlink" href="#interpretation-guidelines" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Risk Prioritization</strong></p>
<ul class="simple">
<li><p>Focus on CRITICAL and HIGH criticality paths first</p></li>
<li><p>Consider business context in addition to technical risk</p></li>
<li><p>Balance likelihood and impact in decision making</p></li>
</ul>
</li>
<li><p><strong>Mitigation Planning</strong></p>
<ul class="simple">
<li><p>Address shortest paths to critical assets first</p></li>
<li><p>Implement controls that affect multiple attack paths</p></li>
<li><p>Consider cost-effectiveness of mitigations</p></li>
</ul>
</li>
<li><p><strong>Continuous Monitoring</strong></p>
<ul class="simple">
<li><p>Set up alerts for new high-risk attack paths</p></li>
<li><p>Monitor changes in blast radius over time</p></li>
<li><p>Track mitigation effectiveness</p></li>
</ul>
</li>
</ol>
</section>
<section id="performance-optimization">
<h3>Performance Optimization<a class="headerlink" href="#performance-optimization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Graph Management</strong></p>
<ul class="simple">
<li><p>Keep asset and relationship data current</p></li>
<li><p>Remove obsolete assets and relationships</p></li>
<li><p>Optimize graph structure for analysis performance</p></li>
</ul>
</li>
<li><p><strong>Analysis Tuning</strong></p>
<ul class="simple">
<li><p>Use appropriate path length limits</p></li>
<li><p>Leverage caching for repeated analysis</p></li>
<li><p>Clear cache periodically to free memory</p></li>
</ul>
</li>
<li><p><strong>Resource Planning</strong></p>
<ul class="simple">
<li><p>Plan for increased resource usage during large analysis</p></li>
<li><p>Consider running intensive analysis during off-peak hours</p></li>
<li><p>Monitor system performance during analysis</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>No Attack Paths Found</strong></p>
<ul class="simple">
<li><p>Verify source and target assets exist in the graph</p></li>
<li><p>Check that relationships exist between assets</p></li>
<li><p>Increase max_path_length parameter</p></li>
<li><p>Verify assets are not isolated</p></li>
</ul>
<p><strong>Performance Issues</strong></p>
<ul class="simple">
<li><p>Reduce max_path_length for large graphs</p></li>
<li><p>Limit max_paths_per_target parameter</p></li>
<li><p>Clear analysis cache if memory usage is high</p></li>
<li><p>Consider running analysis during off-peak hours</p></li>
</ul>
<p><strong>Unexpected Results</strong></p>
<ul class="simple">
<li><p>Verify asset metadata is current and accurate</p></li>
<li><p>Check relationship definitions and weights</p></li>
<li><p>Review security control configurations</p></li>
<li><p>Validate business criticality assignments</p></li>
</ul>
</section>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Documentation</strong>: Refer to API documentation for detailed parameter descriptions</p></li>
<li><p><strong>Support</strong>: Contact support team for complex analysis issues</p></li>
<li><p><strong>Community</strong>: Join user community for best practices and tips</p></li>
<li><p><strong>Training</strong>: Attend training sessions for advanced features</p></li>
</ul>
</section>
</section>
<section id="conclusion">
<h2>Conclusion<a class="headerlink" href="#conclusion" title="Link to this heading"></a></h2>
<p>The attack path analysis capabilities provide powerful tools for:</p>
<ul class="simple">
<li><p><strong>Proactive Security</strong>: Identify and address attack paths before they’re exploited</p></li>
<li><p><strong>Risk Assessment</strong>: Quantify and prioritize security risks</p></li>
<li><p><strong>Incident Response</strong>: Rapidly assess scope and impact during incidents</p></li>
<li><p><strong>Security Testing</strong>: Plan and execute realistic attack simulations</p></li>
</ul>
<p>By following the guidelines and best practices in this guide, you can effectively leverage these capabilities to strengthen your organization’s security posture.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="administrators.html" class="btn btn-neutral float-left" title="Administrators Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="mitre-attack-integration.html" class="btn btn-neutral float-right" title="MITRE ATT&amp;CK Integration User Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>