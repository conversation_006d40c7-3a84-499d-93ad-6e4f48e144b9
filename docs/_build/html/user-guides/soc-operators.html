

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SOC Operators Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Security Architects Guide" href="security-architects.html" />
    <link rel="prev" title="User Guides" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">User Guides</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#role-based-guides">Role-Based Guides</a><ul class="current">
<li class="toctree-l3 current"><a class="reference internal" href="index.html#soc-operators">SOC Operators</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">SOC Operators Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#security-architects">Security Architects</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#red-team-members">Red Team Members</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#purple-team-members">Purple Team Members</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#administrators">Administrators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#feature-specific-guides">Feature-Specific Guides</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-workflows">Common Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">User Guides</a></li>
      <li class="breadcrumb-item active">SOC Operators Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/user-guides/soc-operators.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="soc-operators-guide">
<h1>SOC Operators Guide<a class="headerlink" href="#soc-operators-guide" title="Link to this heading"></a></h1>
<p>This guide is specifically designed for <code class="user-role docutils literal notranslate"><span class="pre">SOC</span> <span class="pre">Operators</span></code> who use the Blast-Radius Security Tool for real-time monitoring, incident response, and threat analysis.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>As a SOC Operator, you’ll primarily use the Blast-Radius Security Tool to:</p>
<ul class="simple">
<li><p>Monitor real-time security events and attack paths</p></li>
<li><p>Investigate security incidents and their potential impact</p></li>
<li><p>Coordinate incident response activities</p></li>
<li><p>Generate reports for management and stakeholders</p></li>
</ul>
</section>
<section id="dashboard-overview">
<h2>Dashboard Overview<a class="headerlink" href="#dashboard-overview" title="Link to this heading"></a></h2>
<section id="soc-operator-dashboard-features">
<h3>SOC Operator Dashboard Features<a class="headerlink" href="#soc-operator-dashboard-features" title="Link to this heading"></a></h3>
<p>The SOC Operator dashboard provides:</p>
<ul class="simple">
<li><p><strong>Real-time Threat Feed</strong>: Live updates of security events and alerts</p></li>
<li><p><strong>Attack Path Visualization</strong>: Interactive graphs showing potential attack vectors</p></li>
<li><p><strong>Incident Queue</strong>: Prioritized list of security incidents requiring attention</p></li>
<li><p><strong>Asset Status</strong>: Current security posture of monitored assets</p></li>
<li><p><strong>Threat Intelligence</strong>: Contextual information about detected threats</p></li>
</ul>
</section>
<section id="key-permissions">
<h3>Key Permissions<a class="headerlink" href="#key-permissions" title="Link to this heading"></a></h3>
<p>As a <code class="user-role docutils literal notranslate"><span class="pre">SOC</span> <span class="pre">Operator</span></code>, you have the following permissions:</p>
<ul class="simple">
<li><p><code class="permission docutils literal notranslate"><span class="pre">view_security_events</span></code> - View all security events and alerts</p></li>
<li><p><code class="permission docutils literal notranslate"><span class="pre">investigate_incidents</span></code> - Access incident details and investigation tools</p></li>
<li><p><code class="permission docutils literal notranslate"><span class="pre">update_incident_status</span></code> - Update incident status and add notes</p></li>
<li><p><code class="permission docutils literal notranslate"><span class="pre">view_attack_paths</span></code> - Visualize attack paths and blast radius</p></li>
<li><p><code class="permission docutils literal notranslate"><span class="pre">generate_reports</span></code> - Create and export security reports</p></li>
</ul>
</section>
</section>
<section id="getting-started">
<h2>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<section id="initial-setup">
<h3>Initial Setup<a class="headerlink" href="#initial-setup" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Login</strong>: Use your corporate credentials to access the platform</p></li>
<li><p><strong>Dashboard Configuration</strong>: Customize your dashboard layout and widgets</p></li>
<li><p><strong>Alert Preferences</strong>: Set up notification preferences for different alert types</p></li>
<li><p><strong>Integration Setup</strong>: Configure integrations with your existing security tools</p></li>
</ol>
</section>
<section id="daily-workflow">
<h3>Daily Workflow<a class="headerlink" href="#daily-workflow" title="Link to this heading"></a></h3>
<section id="morning-routine">
<h4>Morning Routine<a class="headerlink" href="#morning-routine" title="Link to this heading"></a></h4>
<ol class="arabic simple">
<li><p><strong>Review Overnight Alerts</strong>: Check for any critical incidents that occurred overnight</p></li>
<li><p><strong>Threat Intelligence Update</strong>: Review new threat intelligence feeds</p></li>
<li><p><strong>System Health Check</strong>: Verify all monitoring systems are operational</p></li>
<li><p><strong>Priority Assessment</strong>: Triage and prioritize incidents for the day</p></li>
</ol>
</section>
<section id="incident-investigation">
<h4>Incident Investigation<a class="headerlink" href="#incident-investigation" title="Link to this heading"></a></h4>
<p>When investigating a security incident:</p>
<ol class="arabic simple">
<li><p><strong>Initial Assessment</strong>:</p>
<ul class="simple">
<li><p>Review the alert details and severity</p></li>
<li><p>Identify affected assets and systems</p></li>
<li><p>Determine potential blast radius</p></li>
</ul>
</li>
<li><p><strong>Attack Path Analysis</strong>:</p>
<ul class="simple">
<li><p>Use the graph visualization to understand attack vectors</p></li>
<li><p>Identify critical paths that could lead to high-value assets</p></li>
<li><p>Assess the current containment status</p></li>
</ul>
</li>
<li><p><strong>Evidence Collection</strong>:</p>
<ul class="simple">
<li><p>Gather relevant logs and forensic data</p></li>
<li><p>Document timeline of events</p></li>
<li><p>Preserve evidence for potential legal proceedings</p></li>
</ul>
</li>
<li><p><strong>Impact Assessment</strong>:</p>
<ul class="simple">
<li><p>Determine scope of compromise</p></li>
<li><p>Identify data or systems at risk</p></li>
<li><p>Calculate potential business impact</p></li>
</ul>
</li>
<li><p><strong>Response Coordination</strong>:</p>
<ul class="simple">
<li><p>Notify relevant stakeholders</p></li>
<li><p>Coordinate with incident response team</p></li>
<li><p>Implement containment measures</p></li>
</ul>
</li>
</ol>
</section>
</section>
</section>
<section id="real-time-monitoring">
<h2>Real-time Monitoring<a class="headerlink" href="#real-time-monitoring" title="Link to this heading"></a></h2>
<section id="alert-management">
<h3>Alert Management<a class="headerlink" href="#alert-management" title="Link to this heading"></a></h3>
<p>The platform provides several types of alerts:</p>
<ul class="simple">
<li><p><strong>Critical Alerts</strong>: Immediate threats requiring urgent response</p></li>
<li><p><strong>High Priority</strong>: Significant security events needing prompt attention</p></li>
<li><p><strong>Medium Priority</strong>: Notable events for investigation</p></li>
<li><p><strong>Low Priority</strong>: Informational events for awareness</p></li>
</ul>
<p>Alert Workflow:</p>
<ol class="arabic simple">
<li><p><strong>Alert Reception</strong>: New alerts appear in the incident queue</p></li>
<li><p><strong>Initial Triage</strong>: Assess alert severity and assign priority</p></li>
<li><p><strong>Investigation</strong>: Conduct detailed analysis of the security event</p></li>
<li><p><strong>Response</strong>: Take appropriate action based on findings</p></li>
<li><p><strong>Documentation</strong>: Record investigation results and actions taken</p></li>
<li><p><strong>Closure</strong>: Close resolved incidents with proper documentation</p></li>
</ol>
</section>
<section id="attack-path-visualization">
<h3>Attack Path Visualization<a class="headerlink" href="#attack-path-visualization" title="Link to this heading"></a></h3>
<p>Understanding Attack Paths:</p>
<ul class="simple">
<li><p><strong>Nodes</strong>: Represent assets, users, or security controls</p></li>
<li><p><strong>Edges</strong>: Show relationships and potential attack vectors</p></li>
<li><p><strong>Colors</strong>: Indicate risk levels and current security status</p></li>
<li><p><strong>Paths</strong>: Highlight potential routes an attacker might take</p></li>
</ul>
<p>Using the Visualization:</p>
<ol class="arabic simple">
<li><p><strong>Zoom and Pan</strong>: Navigate large network graphs</p></li>
<li><p><strong>Filter Options</strong>: Focus on specific asset types or risk levels</p></li>
<li><p><strong>Path Highlighting</strong>: Trace specific attack scenarios</p></li>
<li><p><strong>Risk Assessment</strong>: Identify highest-risk attack paths</p></li>
</ol>
</section>
</section>
<section id="incident-response">
<h2>Incident Response<a class="headerlink" href="#incident-response" title="Link to this heading"></a></h2>
<section id="response-procedures">
<h3>Response Procedures<a class="headerlink" href="#response-procedures" title="Link to this heading"></a></h3>
<p>Standard Incident Response Process:</p>
<ol class="arabic simple">
<li><p><strong>Detection and Analysis</strong>:</p>
<ul class="simple">
<li><p>Validate the security event</p></li>
<li><p>Determine if it’s a genuine incident</p></li>
<li><p>Assess initial impact and scope</p></li>
</ul>
</li>
<li><p><strong>Containment</strong>:</p>
<ul class="simple">
<li><p>Implement immediate containment measures</p></li>
<li><p>Prevent further spread of the incident</p></li>
<li><p>Preserve evidence for investigation</p></li>
</ul>
</li>
<li><p><strong>Eradication</strong>:</p>
<ul class="simple">
<li><p>Remove the threat from the environment</p></li>
<li><p>Address root causes and vulnerabilities</p></li>
<li><p>Implement additional security controls</p></li>
</ul>
</li>
<li><p><strong>Recovery</strong>:</p>
<ul class="simple">
<li><p>Restore affected systems and services</p></li>
<li><p>Monitor for signs of persistent threats</p></li>
<li><p>Validate system integrity</p></li>
</ul>
</li>
<li><p><strong>Post-Incident Activities</strong>:</p>
<ul class="simple">
<li><p>Document lessons learned</p></li>
<li><p>Update procedures and controls</p></li>
<li><p>Conduct post-incident review</p></li>
</ul>
</li>
</ol>
</section>
<section id="escalation-procedures">
<h3>Escalation Procedures<a class="headerlink" href="#escalation-procedures" title="Link to this heading"></a></h3>
<p>When to Escalate:</p>
<ul class="simple">
<li><p><strong>Critical Infrastructure Impact</strong>: Core business systems affected</p></li>
<li><p><strong>Data Breach Suspected</strong>: Potential unauthorized data access</p></li>
<li><p><strong>Advanced Persistent Threat</strong>: Sophisticated, ongoing attack</p></li>
<li><p><strong>Regulatory Implications</strong>: Incidents requiring compliance reporting</p></li>
<li><p><strong>Resource Limitations</strong>: Incident exceeds team capabilities</p></li>
</ul>
<p>Escalation Contacts:</p>
<ul class="simple">
<li><p><strong>Incident Response Manager</strong>: For complex or high-impact incidents</p></li>
<li><p><strong>Security Architect</strong>: For technical guidance on containment</p></li>
<li><p><strong>Legal Team</strong>: For incidents with potential legal implications</p></li>
<li><p><strong>Executive Team</strong>: For business-critical incidents</p></li>
</ul>
</section>
</section>
<section id="reporting-and-documentation">
<h2>Reporting and Documentation<a class="headerlink" href="#reporting-and-documentation" title="Link to this heading"></a></h2>
<section id="incident-reports">
<h3>Incident Reports<a class="headerlink" href="#incident-reports" title="Link to this heading"></a></h3>
<p>Required Documentation:</p>
<ul class="simple">
<li><p><strong>Incident Summary</strong>: Brief overview of the security event</p></li>
<li><p><strong>Timeline</strong>: Chronological sequence of events</p></li>
<li><p><strong>Impact Assessment</strong>: Business and technical impact</p></li>
<li><p><strong>Response Actions</strong>: Steps taken to address the incident</p></li>
<li><p><strong>Lessons Learned</strong>: Improvements for future incidents</p></li>
</ul>
<p>Report Templates:</p>
<ul class="simple">
<li><p><strong>Executive Summary</strong>: High-level overview for management</p></li>
<li><p><strong>Technical Report</strong>: Detailed technical analysis</p></li>
<li><p><strong>Compliance Report</strong>: Regulatory reporting requirements</p></li>
<li><p><strong>Post-Incident Review</strong>: Comprehensive incident analysis</p></li>
</ul>
</section>
<section id="metrics-and-kpis">
<h3>Metrics and KPIs<a class="headerlink" href="#metrics-and-kpis" title="Link to this heading"></a></h3>
<p>Key Performance Indicators for SOC Operations:</p>
<ul class="simple">
<li><p><strong>Mean Time to Detection (MTTD)</strong>: Average time to detect incidents</p></li>
<li><p><strong>Mean Time to Response (MTTR)</strong>: Average time to respond to incidents</p></li>
<li><p><strong>False Positive Rate</strong>: Percentage of alerts that are false positives</p></li>
<li><p><strong>Incident Volume</strong>: Number of incidents handled per period</p></li>
<li><p><strong>Escalation Rate</strong>: Percentage of incidents requiring escalation</p></li>
</ul>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="operational-excellence">
<h3>Operational Excellence<a class="headerlink" href="#operational-excellence" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Stay Current</strong>: Keep up with latest threat intelligence and attack techniques</p></li>
<li><p><strong>Continuous Learning</strong>: Participate in training and certification programs</p></li>
<li><p><strong>Tool Proficiency</strong>: Master the platform’s features and capabilities</p></li>
<li><p><strong>Communication</strong>: Maintain clear communication with team members and stakeholders</p></li>
<li><p><strong>Documentation</strong>: Keep detailed records of all activities and decisions</p></li>
</ul>
</section>
<section id="threat-hunting">
<h3>Threat Hunting<a class="headerlink" href="#threat-hunting" title="Link to this heading"></a></h3>
<p>Proactive threat hunting activities:</p>
<ul class="simple">
<li><p><strong>Hypothesis-Driven Hunting</strong>: Develop and test threat hypotheses</p></li>
<li><p><strong>IOC Hunting</strong>: Search for known indicators of compromise</p></li>
<li><p><strong>Behavioral Analysis</strong>: Look for anomalous user or system behavior</p></li>
<li><p><strong>Threat Intelligence Integration</strong>: Use intelligence to guide hunting activities</p></li>
</ul>
</section>
</section>
<section id="common-scenarios">
<h2>Common Scenarios<a class="headerlink" href="#common-scenarios" title="Link to this heading"></a></h2>
<section id="scenario-1-malware-detection">
<h3>Scenario 1: Malware Detection<a class="headerlink" href="#scenario-1-malware-detection" title="Link to this heading"></a></h3>
<p><strong>Situation</strong>: Antivirus alert indicates potential malware on user workstation</p>
<p><strong>Response Steps</strong>:</p>
<ol class="arabic simple">
<li><p>Isolate the affected workstation from the network</p></li>
<li><p>Analyze the malware sample and its behavior</p></li>
<li><p>Determine if other systems are affected</p></li>
<li><p>Implement containment and eradication measures</p></li>
<li><p>Monitor for signs of persistence or lateral movement</p></li>
</ol>
</section>
<section id="scenario-2-suspicious-network-activity">
<h3>Scenario 2: Suspicious Network Activity<a class="headerlink" href="#scenario-2-suspicious-network-activity" title="Link to this heading"></a></h3>
<p><strong>Situation</strong>: Network monitoring detects unusual outbound connections</p>
<p><strong>Response Steps</strong>:</p>
<ol class="arabic simple">
<li><p>Analyze network traffic patterns and destinations</p></li>
<li><p>Identify source systems and user accounts</p></li>
<li><p>Correlate with threat intelligence feeds</p></li>
<li><p>Determine if it’s legitimate business activity or malicious</p></li>
<li><p>Take appropriate containment or monitoring actions</p></li>
</ol>
</section>
<section id="scenario-3-privileged-account-compromise">
<h3>Scenario 3: Privileged Account Compromise<a class="headerlink" href="#scenario-3-privileged-account-compromise" title="Link to this heading"></a></h3>
<p><strong>Situation</strong>: Unusual activity detected on administrative account</p>
<p><strong>Response Steps</strong>:</p>
<ol class="arabic simple">
<li><p>Immediately disable or restrict the compromised account</p></li>
<li><p>Analyze account activity and access patterns</p></li>
<li><p>Identify systems and data accessed by the account</p></li>
<li><p>Assess potential data exposure or system compromise</p></li>
<li><p>Implement additional monitoring and security controls</p></li>
</ol>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Dashboard Not Loading</strong>: Check network connectivity and browser compatibility</p></li>
<li><p><strong>Missing Alerts</strong>: Verify data source connections and alert rules</p></li>
<li><p><strong>Slow Performance</strong>: Clear browser cache or contact system administrator</p></li>
<li><p><strong>Access Denied</strong>: Verify user permissions and role assignments</p></li>
</ul>
</section>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Internal Documentation</strong>: Check the platform’s built-in help system</p></li>
<li><p><strong>Team Knowledge Base</strong>: Consult shared documentation and procedures</p></li>
<li><p><strong>Escalation</strong>: Contact senior analysts or incident response team</p></li>
<li><p><strong>Technical Support</strong>: Reach out to platform administrators or vendors</p></li>
</ul>
</section>
</section>
<section id="conclusion">
<h2>Conclusion<a class="headerlink" href="#conclusion" title="Link to this heading"></a></h2>
<p>As a SOC Operator, you play a critical role in maintaining organizational security. The Blast-Radius Security Tool provides powerful capabilities to help you detect, investigate, and respond to security threats effectively. Regular practice with the platform’s features and continuous learning about emerging threats will enhance your effectiveness in protecting the organization.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="User Guides" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="security-architects.html" class="btn btn-neutral float-right" title="Security Architects Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>