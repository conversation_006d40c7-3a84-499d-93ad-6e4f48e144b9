

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Threat Modeling User Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script>window.MathJax = {"options": {"processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
      <script defer="defer" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="API Reference" href="../api/index.html" />
    <link rel="prev" title="MITRE ATT&amp;CK Integration User Guide" href="mitre-attack-integration.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">User Guides</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#role-based-guides">Role-Based Guides</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#feature-specific-guides">Feature-Specific Guides</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#attack-path-analysis">Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#threat-modeling">Threat Modeling</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Threat Modeling User Guide</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-workflows">Common Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">User Guides</a></li>
      <li class="breadcrumb-item active">Threat Modeling User Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/user-guides/threat-modeling.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="threat-modeling-user-guide">
<h1>Threat Modeling User Guide<a class="headerlink" href="#threat-modeling-user-guide" title="Link to this heading"></a></h1>
<p>This guide covers the advanced threat modeling capabilities of the Blast-Radius Security Tool, including threat actor simulation, quantitative risk assessment, and attack success probability modeling.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The threat modeling features provide enterprise-grade threat analysis with pre-loaded
threat actor profiles, statistical modeling, and regulatory compliance impact assessment.</p>
</div>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The threat modeling engine provides sophisticated capabilities for:</p>
<ul class="simple">
<li><p><strong>🎭 Threat Actor Simulation</strong>: Model attacks from known threat actors (APT29, APT28, FIN7, etc.)</p></li>
<li><p><strong>📊 Quantitative Risk Assessment</strong>: Mathematical risk calculation with business impact</p></li>
<li><p><strong>🎯 Attack Success Probability</strong>: Statistical modeling of attack likelihood</p></li>
<li><p><strong>⏱️ Time-to-Compromise Estimation</strong>: Realistic attack timeline modeling</p></li>
<li><p><strong>🔍 Detection Probability Analysis</strong>: Time-to-detection based on monitoring coverage</p></li>
<li><p><strong>💰 Financial Impact Assessment</strong>: Monetary loss calculation and recovery cost estimation</p></li>
<li><p><strong>📋 Compliance Impact Analysis</strong>: Automatic regulatory violation identification</p></li>
<li><p><strong>🛡️ Mitigation Strategy Generation</strong>: AI-driven security control recommendations</p></li>
</ul>
</section>
<section id="getting-started">
<h2>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<p>Before using threat modeling features, ensure you have:</p>
<ol class="arabic simple">
<li><p><strong>Asset Discovery Completed</strong>: Your infrastructure assets are discovered and cataloged</p></li>
<li><p><strong>Attack Path Analysis Configured</strong>: Basic attack path analysis is functional</p></li>
<li><p><strong>Appropriate Permissions</strong>: User role with threat modeling permissions</p></li>
<li><p><strong>MITRE ATT&amp;CK Data</strong>: Framework data is loaded and up-to-date</p></li>
</ol>
</section>
<section id="basic-threat-modeling-workflow">
<h3>Basic Threat Modeling Workflow<a class="headerlink" href="#basic-threat-modeling-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Select Threat Actor</strong>: Choose from pre-loaded threat actor profiles</p></li>
<li><p><strong>Define Target Assets</strong>: Specify high-value assets to protect</p></li>
<li><p><strong>Run Attack Simulation</strong>: Execute threat actor simulation</p></li>
<li><p><strong>Analyze Results</strong>: Review success probability, detection time, and impact</p></li>
<li><p><strong>Generate Mitigations</strong>: Create security control recommendations</p></li>
<li><p><strong>Track Risk Over Time</strong>: Monitor risk posture changes</p></li>
</ol>
</section>
</section>
<section id="threat-actor-profiles">
<h2>Threat Actor Profiles<a class="headerlink" href="#threat-actor-profiles" title="Link to this heading"></a></h2>
<section id="pre-loaded-threat-actors">
<h3>Pre-loaded Threat Actors<a class="headerlink" href="#pre-loaded-threat-actors" title="Link to this heading"></a></h3>
<p>The system includes detailed profiles for major threat actors:</p>
<p><strong>APT29 (Cozy Bear)</strong></p>
<ul class="simple">
<li><p><strong>Sophistication Level</strong>: 0.9 (Very High)</p></li>
<li><p><strong>Resource Level</strong>: 0.8 (High)</p></li>
<li><p><strong>Persistence Level</strong>: 0.85 (Very High)</p></li>
<li><p><strong>Primary Motivation</strong>: Espionage</p></li>
<li><p><strong>Geographic Origin</strong>: Russia</p></li>
<li><p><strong>Target Sectors</strong>: Government, Technology, Healthcare</p></li>
<li><p><strong>Preferred Techniques</strong>: T1566 (Phishing), T1078 (Valid Accounts), T1055 (Process Injection)</p></li>
<li><p><strong>Known Tools</strong>: Cobalt Strike, PowerShell Empire, WinRAR</p></li>
</ul>
<p><strong>APT28 (Fancy Bear)</strong></p>
<ul class="simple">
<li><p><strong>Sophistication Level</strong>: 0.85 (High)</p></li>
<li><p><strong>Resource Level</strong>: 0.8 (High)</p></li>
<li><p><strong>Persistence Level</strong>: 0.8 (High)</p></li>
<li><p><strong>Primary Motivation</strong>: Espionage, Disruption</p></li>
<li><p><strong>Geographic Origin</strong>: Russia</p></li>
<li><p><strong>Target Sectors</strong>: Government, Military, Media</p></li>
<li><p><strong>Preferred Techniques</strong>: T1566 (Phishing), T1190 (Exploit Public-Facing Application)</p></li>
<li><p><strong>Known Tools</strong>: X-Agent, Sofacy, Zebrocy</p></li>
</ul>
<p><strong>FIN7</strong></p>
<ul class="simple">
<li><p><strong>Sophistication Level</strong>: 0.75 (High)</p></li>
<li><p><strong>Resource Level</strong>: 0.7 (Medium-High)</p></li>
<li><p><strong>Persistence Level</strong>: 0.7 (Medium-High)</p></li>
<li><p><strong>Primary Motivation</strong>: Financial</p></li>
<li><p><strong>Geographic Origin</strong>: Unknown</p></li>
<li><p><strong>Target Sectors</strong>: Retail, Restaurant, Hospitality</p></li>
<li><p><strong>Preferred Techniques</strong>: T1566 (Phishing), T1059 (Command and Scripting Interpreter)</p></li>
<li><p><strong>Known Tools</strong>: Carbanak, GRIFFON, POWERSOURCE</p></li>
</ul>
<p><strong>Insider Threat</strong></p>
<ul class="simple">
<li><p><strong>Sophistication Level</strong>: 0.6 (Medium)</p></li>
<li><p><strong>Resource Level</strong>: 0.8 (High - Internal Access)</p></li>
<li><p><strong>Persistence Level</strong>: 0.9 (Very High)</p></li>
<li><p><strong>Primary Motivation</strong>: Financial, Ideology, Revenge</p></li>
<li><p><strong>Geographic Origin</strong>: Internal</p></li>
<li><p><strong>Target Sectors</strong>: All</p></li>
<li><p><strong>Preferred Techniques</strong>: T1078 (Valid Accounts), T1005 (Data from Local System)</p></li>
<li><p><strong>Known Tools</strong>: Native OS tools, Legitimate applications</p></li>
</ul>
</section>
</section>
<section id="attack-simulation">
<h2>Attack Simulation<a class="headerlink" href="#attack-simulation" title="Link to this heading"></a></h2>
<section id="running-attack-simulations">
<h3>Running Attack Simulations<a class="headerlink" href="#running-attack-simulations" title="Link to this heading"></a></h3>
<p><strong>Basic Attack Simulation</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Python SDK example</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">blast_radius</span><span class="w"> </span><span class="kn">import</span> <span class="n">BlastRadiusClient</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">BlastRadiusClient</span><span class="p">(</span>
    <span class="n">base_url</span><span class="o">=</span><span class="s2">&quot;https://your-instance.com&quot;</span><span class="p">,</span>
    <span class="n">api_token</span><span class="o">=</span><span class="s2">&quot;your-api-token&quot;</span>
<span class="p">)</span>

<span class="c1"># Run threat actor simulation</span>
<span class="n">simulation</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">simulate_attack</span><span class="p">(</span>
    <span class="n">threat_actor_id</span><span class="o">=</span><span class="s2">&quot;APT29&quot;</span><span class="p">,</span>
    <span class="n">target_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span> <span class="s2">&quot;web_server_001&quot;</span><span class="p">],</span>
    <span class="n">scenario_name</span><span class="o">=</span><span class="s2">&quot;APT29 Campaign Simulation&quot;</span>
<span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Success Probability: </span><span class="si">{</span><span class="n">simulation</span><span class="o">.</span><span class="n">success_probability</span><span class="si">:</span><span class="s2">.2%</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Detection Probability: </span><span class="si">{</span><span class="n">simulation</span><span class="o">.</span><span class="n">detection_probability</span><span class="si">:</span><span class="s2">.2%</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Time to Compromise: </span><span class="si">{</span><span class="n">simulation</span><span class="o">.</span><span class="n">estimated_time_to_compromise</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2"> hours&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Financial Impact: $</span><span class="si">{</span><span class="n">simulation</span><span class="o">.</span><span class="n">financial_impact</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Advanced Simulation with Custom Parameters</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Advanced simulation with custom threat actor</span>
<span class="n">custom_threat_actor</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;actor_id&quot;</span><span class="p">:</span> <span class="s2">&quot;CUSTOM_APT&quot;</span><span class="p">,</span>
    <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Custom Advanced Persistent Threat&quot;</span><span class="p">,</span>
    <span class="s2">&quot;sophistication_level&quot;</span><span class="p">:</span> <span class="mf">0.8</span><span class="p">,</span>
    <span class="s2">&quot;resource_level&quot;</span><span class="p">:</span> <span class="mf">0.7</span><span class="p">,</span>
    <span class="s2">&quot;persistence_level&quot;</span><span class="p">:</span> <span class="mf">0.75</span><span class="p">,</span>
    <span class="s2">&quot;primary_motivation&quot;</span><span class="p">:</span> <span class="s2">&quot;espionage&quot;</span><span class="p">,</span>
    <span class="s2">&quot;preferred_techniques&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;T1566&quot;</span><span class="p">,</span> <span class="s2">&quot;T1078&quot;</span><span class="p">,</span> <span class="s2">&quot;T1055&quot;</span><span class="p">],</span>
    <span class="s2">&quot;target_sectors&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;technology&quot;</span><span class="p">,</span> <span class="s2">&quot;finance&quot;</span><span class="p">]</span>
<span class="p">}</span>

<span class="n">simulation</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">simulate_attack_custom</span><span class="p">(</span>
    <span class="n">threat_actor</span><span class="o">=</span><span class="n">custom_threat_actor</span><span class="p">,</span>
    <span class="n">target_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;critical_database&quot;</span><span class="p">,</span> <span class="s2">&quot;payment_system&quot;</span><span class="p">],</span>
    <span class="n">scenario_name</span><span class="o">=</span><span class="s2">&quot;Custom APT Simulation&quot;</span><span class="p">,</span>
    <span class="n">simulation_parameters</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;max_attack_duration&quot;</span><span class="p">:</span> <span class="mi">168</span><span class="p">,</span>  <span class="c1"># 1 week</span>
        <span class="s2">&quot;detection_enhancement&quot;</span><span class="p">:</span> <span class="mf">1.2</span><span class="p">,</span>  <span class="c1"># 20% better detection</span>
        <span class="s2">&quot;security_controls_effectiveness&quot;</span><span class="p">:</span> <span class="mf">0.85</span>
    <span class="p">}</span>
<span class="p">)</span>
</pre></div>
</div>
<p><strong>API Usage</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># REST API example</span>
curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span><span class="s2">&quot;https://api.blast-radius.com/api/v1/threat-modeling/simulate&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer your-token&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;threat_actor_id&quot;: &quot;APT29&quot;,</span>
<span class="s1">    &quot;target_assets&quot;: [&quot;database_001&quot;, &quot;web_server_001&quot;],</span>
<span class="s1">    &quot;scenario_name&quot;: &quot;Q4 Risk Assessment&quot;,</span>
<span class="s1">    &quot;simulation_parameters&quot;: {</span>
<span class="s1">      &quot;include_insider_threat&quot;: true,</span>
<span class="s1">      &quot;consider_supply_chain&quot;: true,</span>
<span class="s1">      &quot;regulatory_context&quot;: [&quot;GDPR&quot;, &quot;SOX&quot;]</span>
<span class="s1">    }</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
</section>
</section>
<section id="quantitative-risk-assessment">
<h2>Quantitative Risk Assessment<a class="headerlink" href="#quantitative-risk-assessment" title="Link to this heading"></a></h2>
<section id="risk-calculation-methodology">
<h3>Risk Calculation Methodology<a class="headerlink" href="#risk-calculation-methodology" title="Link to this heading"></a></h3>
<p>The system uses a sophisticated risk calculation model that considers:</p>
<p><strong>Attack Success Probability Factors</strong></p>
<ul class="simple">
<li><p><strong>Threat Actor Sophistication</strong>: Technical capabilities and resources</p></li>
<li><p><strong>Target Asset Security Posture</strong>: Security controls and configuration</p></li>
<li><p><strong>Attack Path Complexity</strong>: Number of hops and security boundaries</p></li>
<li><p><strong>Monitoring Coverage</strong>: Detection capabilities and response time</p></li>
<li><p><strong>Historical Attack Data</strong>: Success rates for similar scenarios</p></li>
</ul>
<p><strong>Financial Impact Calculation</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Financial impact assessment</span>
<span class="n">financial_impact</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">assess_financial_impact</span><span class="p">(</span>
    <span class="n">affected_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span> <span class="s2">&quot;web_server_001&quot;</span><span class="p">],</span>
    <span class="n">attack_scenario</span><span class="o">=</span><span class="s2">&quot;data_breach&quot;</span><span class="p">,</span>
    <span class="n">business_context</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;annual_revenue&quot;</span><span class="p">:</span> <span class="mi">100000000</span><span class="p">,</span>  <span class="c1"># $100M</span>
        <span class="s2">&quot;customer_count&quot;</span><span class="p">:</span> <span class="mi">50000</span><span class="p">,</span>
        <span class="s2">&quot;data_sensitivity&quot;</span><span class="p">:</span> <span class="s2">&quot;PII&quot;</span><span class="p">,</span>
        <span class="s2">&quot;regulatory_requirements&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;GDPR&quot;</span><span class="p">,</span> <span class="s2">&quot;CCPA&quot;</span><span class="p">],</span>
        <span class="s2">&quot;business_criticality&quot;</span><span class="p">:</span> <span class="s2">&quot;high&quot;</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Direct Costs: $</span><span class="si">{</span><span class="n">financial_impact</span><span class="o">.</span><span class="n">direct_costs</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Indirect Costs: $</span><span class="si">{</span><span class="n">financial_impact</span><span class="o">.</span><span class="n">indirect_costs</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Regulatory Fines: $</span><span class="si">{</span><span class="n">financial_impact</span><span class="o">.</span><span class="n">regulatory_fines</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Business Disruption: $</span><span class="si">{</span><span class="n">financial_impact</span><span class="o">.</span><span class="n">business_disruption</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total Impact: $</span><span class="si">{</span><span class="n">financial_impact</span><span class="o">.</span><span class="n">total_impact</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Risk Score Calculation</strong></p>
<p>The overall risk score is calculated using:</p>
<div class="math notranslate nohighlight">
\[Risk Score = (Threat Likelihood × Asset Value × Vulnerability Score) / Control Effectiveness\]</div>
<p>Where:
- <strong>Threat Likelihood</strong>: Probability of attack success (0.0 - 1.0)
- <strong>Asset Value</strong>: Business criticality and data sensitivity (0.0 - 1.0)
- <strong>Vulnerability Score</strong>: Security weaknesses and exposure (0.0 - 1.0)
- <strong>Control Effectiveness</strong>: Security controls and monitoring (0.0 - 1.0)</p>
</section>
</section>
<section id="compliance-impact-analysis">
<h2>Compliance Impact Analysis<a class="headerlink" href="#compliance-impact-analysis" title="Link to this heading"></a></h2>
<section id="regulatory-compliance-assessment">
<h3>Regulatory Compliance Assessment<a class="headerlink" href="#regulatory-compliance-assessment" title="Link to this heading"></a></h3>
<p>The system automatically identifies potential regulatory violations:</p>
<p><strong>GDPR (General Data Protection Regulation)</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># GDPR compliance assessment</span>
<span class="n">gdpr_impact</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">assess_gdpr_impact</span><span class="p">(</span>
    <span class="n">affected_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;customer_database&quot;</span><span class="p">,</span> <span class="s2">&quot;web_application&quot;</span><span class="p">],</span>
    <span class="n">data_types</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;personal_data&quot;</span><span class="p">,</span> <span class="s2">&quot;sensitive_personal_data&quot;</span><span class="p">],</span>
    <span class="n">data_subjects_affected</span><span class="o">=</span><span class="mi">10000</span><span class="p">,</span>
    <span class="n">breach_scenario</span><span class="o">=</span><span class="s2">&quot;unauthorized_access&quot;</span>
<span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GDPR Violation Risk: </span><span class="si">{</span><span class="n">gdpr_impact</span><span class="o">.</span><span class="n">violation_probability</span><span class="si">:</span><span class="s2">.2%</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Potential Fine Range: €</span><span class="si">{</span><span class="n">gdpr_impact</span><span class="o">.</span><span class="n">min_fine</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2"> - €</span><span class="si">{</span><span class="n">gdpr_impact</span><span class="o">.</span><span class="n">max_fine</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Notification Requirements: </span><span class="si">{</span><span class="n">gdpr_impact</span><span class="o">.</span><span class="n">notification_timeline</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Data Subject Rights Impact: </span><span class="si">{</span><span class="n">gdpr_impact</span><span class="o">.</span><span class="n">data_subject_impact</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>HIPAA (Health Insurance Portability and Accountability Act)</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># HIPAA compliance assessment</span>
<span class="n">hipaa_impact</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">assess_hipaa_impact</span><span class="p">(</span>
    <span class="n">affected_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;patient_records&quot;</span><span class="p">,</span> <span class="s2">&quot;billing_system&quot;</span><span class="p">],</span>
    <span class="n">phi_records_affected</span><span class="o">=</span><span class="mi">5000</span><span class="p">,</span>
    <span class="n">breach_type</span><span class="o">=</span><span class="s2">&quot;unauthorized_disclosure&quot;</span>
<span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;HIPAA Violation Risk: </span><span class="si">{</span><span class="n">hipaa_impact</span><span class="o">.</span><span class="n">violation_probability</span><span class="si">:</span><span class="s2">.2%</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;OCR Reporting Required: </span><span class="si">{</span><span class="n">hipaa_impact</span><span class="o">.</span><span class="n">ocr_reporting_required</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Patient Notification Required: </span><span class="si">{</span><span class="n">hipaa_impact</span><span class="o">.</span><span class="n">patient_notification_required</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Potential Penalties: $</span><span class="si">{</span><span class="n">hipaa_impact</span><span class="o">.</span><span class="n">potential_penalties</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>SOX (Sarbanes-Oxley Act)</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># SOX compliance assessment</span>
<span class="n">sox_impact</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">assess_sox_impact</span><span class="p">(</span>
    <span class="n">affected_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;financial_reporting_system&quot;</span><span class="p">,</span> <span class="s2">&quot;audit_database&quot;</span><span class="p">],</span>
    <span class="n">financial_data_affected</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">internal_controls_compromised</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;access_controls&quot;</span><span class="p">,</span> <span class="s2">&quot;data_integrity&quot;</span><span class="p">]</span>
<span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SOX Violation Risk: </span><span class="si">{</span><span class="n">sox_impact</span><span class="o">.</span><span class="n">violation_probability</span><span class="si">:</span><span class="s2">.2%</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Material Weakness: </span><span class="si">{</span><span class="n">sox_impact</span><span class="o">.</span><span class="n">material_weakness</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;CEO/CFO Certification Impact: </span><span class="si">{</span><span class="n">sox_impact</span><span class="o">.</span><span class="n">certification_impact</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Audit Implications: </span><span class="si">{</span><span class="n">sox_impact</span><span class="o">.</span><span class="n">audit_implications</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="mitigation-strategy-generation">
<h2>Mitigation Strategy Generation<a class="headerlink" href="#mitigation-strategy-generation" title="Link to this heading"></a></h2>
<section id="ai-driven-security-recommendations">
<h3>AI-Driven Security Recommendations<a class="headerlink" href="#ai-driven-security-recommendations" title="Link to this heading"></a></h3>
<p>The system generates targeted mitigation strategies based on:</p>
<ul class="simple">
<li><p><strong>Attack Path Analysis</strong>: Specific vulnerabilities in attack chains</p></li>
<li><p><strong>Threat Actor Techniques</strong>: MITRE ATT&amp;CK technique-specific mitigations</p></li>
<li><p><strong>Asset Criticality</strong>: Priority-based security control recommendations</p></li>
<li><p><strong>Cost-Benefit Analysis</strong>: ROI-optimized security investments</p></li>
<li><p><strong>Implementation Feasibility</strong>: Practical deployment considerations</p></li>
</ul>
<p><strong>Example Mitigation Report</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Generate mitigation strategies</span>
<span class="n">mitigations</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">generate_mitigations</span><span class="p">(</span>
    <span class="n">simulation_id</span><span class="o">=</span><span class="s2">&quot;sim_001&quot;</span><span class="p">,</span>
    <span class="n">budget_constraints</span><span class="o">=</span><span class="mi">500000</span><span class="p">,</span>  <span class="c1"># $500K budget</span>
    <span class="n">implementation_timeline</span><span class="o">=</span><span class="mi">90</span><span class="p">,</span>  <span class="c1"># 90 days</span>
    <span class="n">risk_tolerance</span><span class="o">=</span><span class="s2">&quot;medium&quot;</span>
<span class="p">)</span>

<span class="k">for</span> <span class="n">mitigation</span> <span class="ow">in</span> <span class="n">mitigations</span><span class="o">.</span><span class="n">recommendations</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Control: </span><span class="si">{</span><span class="n">mitigation</span><span class="o">.</span><span class="n">control_name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;MITRE Techniques Addressed: </span><span class="si">{</span><span class="n">mitigation</span><span class="o">.</span><span class="n">mitre_techniques</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Risk Reduction: </span><span class="si">{</span><span class="n">mitigation</span><span class="o">.</span><span class="n">risk_reduction</span><span class="si">:</span><span class="s2">.1%</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Implementation Cost: $</span><span class="si">{</span><span class="n">mitigation</span><span class="o">.</span><span class="n">cost</span><span class="si">:</span><span class="s2">,.0f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Implementation Time: </span><span class="si">{</span><span class="n">mitigation</span><span class="o">.</span><span class="n">implementation_days</span><span class="si">}</span><span class="s2"> days&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;ROI: </span><span class="si">{</span><span class="n">mitigation</span><span class="o">.</span><span class="n">roi</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">x&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;---&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="advanced-features">
<h2>Advanced Features<a class="headerlink" href="#advanced-features" title="Link to this heading"></a></h2>
<section id="continuous-risk-monitoring">
<h3>Continuous Risk Monitoring<a class="headerlink" href="#continuous-risk-monitoring" title="Link to this heading"></a></h3>
<p>Set up continuous monitoring to track risk changes over time:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Set up continuous risk monitoring</span>
<span class="n">monitoring</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">setup_continuous_monitoring</span><span class="p">(</span>
    <span class="n">threat_actors</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;APT29&quot;</span><span class="p">,</span> <span class="s2">&quot;APT28&quot;</span><span class="p">,</span> <span class="s2">&quot;FIN7&quot;</span><span class="p">],</span>
    <span class="n">target_assets</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;critical_assets&quot;</span><span class="p">],</span>
    <span class="n">monitoring_frequency</span><span class="o">=</span><span class="s2">&quot;daily&quot;</span><span class="p">,</span>
    <span class="n">alert_thresholds</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;risk_score_increase&quot;</span><span class="p">:</span> <span class="mf">0.1</span><span class="p">,</span>  <span class="c1"># 10% increase</span>
        <span class="s2">&quot;new_attack_paths&quot;</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span>
        <span class="s2">&quot;compliance_risk_change&quot;</span><span class="p">:</span> <span class="mf">0.05</span>
    <span class="p">}</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="threat-intelligence-integration">
<h3>Threat Intelligence Integration<a class="headerlink" href="#threat-intelligence-integration" title="Link to this heading"></a></h3>
<p>Integrate with external threat intelligence feeds:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Integrate threat intelligence</span>
<span class="n">threat_intel</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">threat_modeling</span><span class="o">.</span><span class="n">integrate_threat_intelligence</span><span class="p">(</span>
    <span class="n">feeds</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;MISP&quot;</span><span class="p">,</span> <span class="s2">&quot;STIX/TAXII&quot;</span><span class="p">,</span> <span class="s2">&quot;commercial_feeds&quot;</span><span class="p">],</span>
    <span class="n">update_frequency</span><span class="o">=</span><span class="s2">&quot;hourly&quot;</span><span class="p">,</span>
    <span class="n">threat_actor_updates</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">technique_updates</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">ioc_correlation</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="threat-modeling-workflow">
<h3>Threat Modeling Workflow<a class="headerlink" href="#threat-modeling-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Regular Assessment</strong>: Run threat modeling assessments quarterly</p></li>
<li><p><strong>Scenario Planning</strong>: Model multiple threat actor scenarios</p></li>
<li><p><strong>Stakeholder Involvement</strong>: Include business stakeholders in risk discussions</p></li>
<li><p><strong>Mitigation Tracking</strong>: Track implementation of recommended controls</p></li>
<li><p><strong>Continuous Improvement</strong>: Update threat models based on new intelligence</p></li>
</ol>
</section>
<section id="performance-optimization">
<h3>Performance Optimization<a class="headerlink" href="#performance-optimization" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Batch Processing</strong>: Group similar simulations for efficiency</p></li>
<li><p><strong>Caching</strong>: Leverage cached results for repeated scenarios</p></li>
<li><p><strong>Incremental Updates</strong>: Update only changed assets and relationships</p></li>
<li><p><strong>Parallel Processing</strong>: Run multiple simulations concurrently</p></li>
</ul>
<p>For detailed API reference and advanced configuration options, see the <a class="reference internal" href="../api/threat-modeling.html"><span class="doc">Threat Modeling API Reference</span></a> documentation.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="mitre-attack-integration.html" class="btn btn-neutral float-left" title="MITRE ATT&amp;CK Integration User Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../api/index.html" class="btn btn-neutral float-right" title="API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>