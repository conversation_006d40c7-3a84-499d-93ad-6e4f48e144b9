Search.setIndex({"alltitles": {"AI-Driven Security Recommendations": [[26, "ai-driven-security-recommendations"]], "API Development": [[15, "api-development"]], "API Documentation": [[9, "api-documentation"]], "API Documentation Section": [[0, "api-documentation-section"]], "API Endpoints": [[4, "api-endpoints"], [5, "api-endpoints"]], "API Integration": [[21, "api-integration"]], "API Reference": [[5, null], [9, null]], "API Reference (api/)": [[2, "api-reference-api"]], "ATT&CK Data Management": [[6, "att-ck-data-management"]], "ATT&CK Navigator Integration": [[6, "att-ck-navigator-integration"], [21, "att-ck-navigator-integration"]], "Access Control Settings": [[8, "access-control-settings"]], "Adding New Documentation": [[2, "adding-new-documentation"]], "Additional Documentation (Future)": [[0, "additional-documentation-future"]], "Administrative Workflow": [[18, "administrative-workflow"]], "Administrator Dashboard Features": [[18, "administrator-dashboard-features"]], "Administrators": [[20, "administrators"]], "Administrators Guide": [[18, null]], "Advanced Analytics": [[21, "advanced-analytics"]], "Advanced Analytics and Reporting": [[24, "advanced-analytics-and-reporting"]], "Advanced Attack Path Features": [[23, "advanced-attack-path-features"]], "Advanced Configuration": [[11, "advanced-configuration"]], "Advanced Features": [[26, "advanced-features"]], "Advanced Purple Team Techniques": [[22, "advanced-purple-team-techniques"]], "Advanced Red Team Techniques": [[23, "advanced-red-team-techniques"]], "Advanced Threat Hunting": [[22, "advanced-threat-hunting"]], "Adversary Emulation": [[22, "adversary-emulation"]], "Alert Management": [[25, "alert-management"]], "Analysis Configuration": [[19, "analysis-configuration"]], "Analyze Attack Paths": [[3, "analyze-attack-paths"]], "Analyze Attack Patterns": [[6, "analyze-attack-patterns"]], "Application Monitoring": [[1, "application-monitoring"]], "Application Settings": [[8, "application-settings"]], "Architecture Assessment Workflow": [[24, "architecture-assessment-workflow"]], "Architecture Planning and Design": [[24, "architecture-planning-and-design"]], "Architecture Validation": [[24, "architecture-validation"]], "Architecture and Design": [[15, "architecture-and-design"]], "Assess Risk": [[7, "assess-risk"]], "Asset Data Models": [[15, "asset-data-models"]], "Asset Discovery and Management": [[20, "asset-discovery-and-management"]], "Asset Management": [[5, "asset-management"], [11, "asset-management"]], "Attack Path Analysis": [[5, "attack-path-analysis"], [17, "attack-path-analysis"], [20, "attack-path-analysis"]], "Attack Path Analysis API": [[3, null]], "Attack Path Analysis Architecture": [[12, null]], "Attack Path Analysis Flows": [[13, null]], "Attack Path Analysis Schema": [[14, "attack-path-analysis-schema"]], "Attack Path Analysis Tables": [[14, "attack-path-analysis-tables"]], "Attack Path Analysis User Guide": [[19, null]], "Attack Path Discovery": [[19, "attack-path-discovery"]], "Attack Path Discovery Flow": [[13, "attack-path-discovery-flow"]], "Attack Path Discovery and Analysis": [[23, "attack-path-discovery-and-analysis"]], "Attack Path Types": [[19, "attack-path-types"]], "Attack Path Visualization": [[25, "attack-path-visualization"]], "Attack Pattern Analysis": [[6, "attack-pattern-analysis"], [21, "attack-pattern-analysis"]], "Attack Scenario Creation": [[19, "attack-scenario-creation"]], "Attack Scenario Creation Flow": [[13, "attack-scenario-creation-flow"]], "Attack Scenario Modeling": [[19, "attack-scenario-modeling"]], "Attack Simulation": [[26, "attack-simulation"]], "Attack Simulation and Testing": [[23, "attack-simulation-and-testing"]], "Attack Surface Analysis": [[24, "attack-surface-analysis"]], "AttackPathAnalyzer": [[12, "attackpathanalyzer"]], "Attribute Threat Actor": [[6, "attribute-threat-actor"]], "Authentication": [[3, "authentication"], [5, "authentication"], [6, "authentication"], [7, "authentication"]], "Authentication & User Management": [[5, "authentication-user-management"]], "Authentication API": [[4, null]], "Authentication Endpoints": [[4, "authentication-endpoints"]], "Authentication Flow": [[4, "authentication-flow"]], "Authentication Settings": [[8, "authentication-settings"]], "Automated Attribution Engine": [[21, "automated-attribution-engine"]], "Automated Security Scanning": [[1, "automated-security-scanning"]], "Automated Visualization": [[21, "automated-visualization"]], "Automatic Data Synchronization": [[21, "automatic-data-synchronization"]], "Backend Tests": [[1, "backend-tests"]], "Backup and Disaster Recovery": [[18, "backup-and-disaster-recovery"]], "Base URL": [[3, "base-url"], [5, "base-url"], [6, "base-url"], [7, "base-url"]], "Basic ATT&CK Integration Workflow": [[21, "basic-att-ck-integration-workflow"]], "Basic Attack Path Analysis": [[19, "basic-attack-path-analysis"]], "Basic Commands": [[2, "basic-commands"]], "Basic Configuration": [[11, "basic-configuration"]], "Basic Threat Modeling Workflow": [[26, "basic-threat-modeling-workflow"]], "Batch Correlate Events": [[6, "batch-correlate-events"]], "Behavioral Analytics": [[21, "behavioral-analytics"]], "Best Practices": [[1, "best-practices"], [3, "best-practices"], [4, "best-practices"], [5, "best-practices"], [8, "best-practices"], [19, "best-practices"], [20, "best-practices"], [21, "best-practices"], [25, "best-practices"], [26, "best-practices"]], "Best Practices for Platform Administration": [[18, "best-practices-for-platform-administration"]], "Best Practices for Purple Team Operations": [[22, "best-practices-for-purple-team-operations"]], "Best Practices for Red Team Operations": [[23, "best-practices-for-red-team-operations"]], "Best Practices for Security Architects": [[24, "best-practices-for-security-architects"]], "Blast Radius Algorithm": [[12, "blast-radius-algorithm"]], "Blast Radius Analysis": [[19, "blast-radius-analysis"]], "Blast Radius Calculation": [[19, "blast-radius-calculation"]], "Blast Radius Calculation Flow": [[13, "blast-radius-calculation-flow"]], "Blast-Radius Security Tool - Complete User Guide Documentation": [[0, null]], "Blast-Radius Security Tool Documentation": [[2, null], [9, null]], "Blue Team Enhancement": [[22, "blue-team-enhancement"]], "Build Images": [[1, "build-images"]], "Business Metrics": [[16, "business-metrics"]], "Business Risks": [[16, "business-risks"]], "CR-001: Data Protection": [[16, "cr-001-data-protection"]], "CR-002: Security Frameworks": [[16, "cr-002-security-frameworks"]], "Caching Strategy": [[12, "caching-strategy"]], "Caching Strategy Decision Flow": [[13, "caching-strategy-decision-flow"]], "Calculate Blast Radius": [[3, "calculate-blast-radius"]], "Campaign Management and Planning": [[23, "campaign-management-and-planning"]], "Can I customize risk scoring?": [[17, "can-i-customize-risk-scoring"]], "Can I run this in an air-gapped environment?": [[17, "can-i-run-this-in-an-air-gapped-environment"]], "Can I use the API for automation?": [[17, "can-i-use-the-api-for-automation"]], "Can\u2019t Access Web Interface": [[11, "can-t-access-web-interface"]], "Change Password": [[4, "change-password"]], "Clear Analysis Cache": [[3, "clear-analysis-cache"]], "Cloud Provider Integration": [[11, "cloud-provider-integration"]], "Cloud Provider Settings": [[8, "cloud-provider-settings"]], "Code Examples": [[2, "code-examples"]], "Collaboration with Purple Team": [[23, "collaboration-with-purple-team"]], "Collaborative Security Testing": [[22, "collaborative-security-testing"]], "Common Administrative Issues": [[18, "common-administrative-issues"]], "Common Architecture Challenges": [[24, "common-architecture-challenges"]], "Common Challenges": [[22, "common-challenges"], [23, "common-challenges"]], "Common Issues": [[1, "common-issues"], [10, "common-issues"], [19, "common-issues"], [25, "common-issues"]], "Common Purple Team Scenarios": [[22, "common-purple-team-scenarios"]], "Common Quick Start Issues": [[11, "common-quick-start-issues"]], "Common Red Team Scenarios": [[23, "common-red-team-scenarios"]], "Common Response Formats": [[5, "common-response-formats"]], "Common Scenarios": [[25, "common-scenarios"]], "Common Use Cases and Scenarios": [[24, "common-use-cases-and-scenarios"]], "Common Workflows": [[20, "common-workflows"]], "Communication and Collaboration": [[22, "communication-and-collaboration"]], "Communication and Leadership": [[22, "communication-and-leadership"], [24, "communication-and-leadership"]], "Compliance Framework Management": [[24, "compliance-framework-management"]], "Compliance Impact Analysis": [[7, "compliance-impact-analysis"], [26, "compliance-impact-analysis"]], "Compliance Requirements": [[16, "compliance-requirements"]], "Compliance and Governance": [[24, "compliance-and-governance"]], "Comprehensive Coverage": [[0, "comprehensive-coverage"]], "Comprehensive Risk Assessment": [[24, "comprehensive-risk-assessment"]], "Conclusion": [[18, "conclusion"], [19, "conclusion"], [22, "conclusion"], [23, "conclusion"], [24, "conclusion"], [25, "conclusion"]], "Configuration": [[10, "configuration"]], "Configuration Guide": [[8, null]], "Configuration Validation": [[8, "configuration-validation"]], "Configuration and Usage": [[17, "configuration-and-usage"]], "Configuring Integrations": [[10, "configuring-integrations"]], "Congratulations!": [[11, "congratulations"]], "Container Debugging": [[1, "container-debugging"]], "Container Issues": [[1, "container-issues"]], "Content Quality": [[0, "content-quality"]], "Content Volume": [[0, "content-volume"]], "Continuous Integration": [[2, "continuous-integration"]], "Continuous Monitoring": [[7, "continuous-monitoring"]], "Continuous Risk Monitoring": [[26, "continuous-risk-monitoring"]], "Contributing": [[2, "contributing"]], "Contributing Guidelines": [[15, "contributing-guidelines"]], "Contributing and Development": [[15, "contributing-and-development"]], "Core Components": [[12, "core-components"]], "Core Concepts": [[19, "core-concepts"]], "Core Configuration": [[8, "core-configuration"]], "Core Entity Relationship Diagram": [[14, "core-entity-relationship-diagram"]], "Core System Settings": [[18, "core-system-settings"]], "Core Tables": [[14, "core-tables"]], "Core Value Proposition": [[16, "core-value-proposition"]], "Correlate Security Event": [[6, "correlate-security-event"]], "Correlation Accuracy": [[21, "correlation-accuracy"]], "Coverage Completeness": [[0, "coverage-completeness"]], "Create Attack Scenario": [[3, "create-attack-scenario"]], "Creating Admin User": [[10, "creating-admin-user"]], "Cross-References": [[2, "cross-references"]], "Custom Integrations": [[15, "custom-integrations"]], "Daily Development Cycle": [[1, "daily-development-cycle"]], "Daily Workflow": [[25, "daily-workflow"]], "Dashboard Overview": [[11, "dashboard-overview"], [18, "dashboard-overview"], [22, "dashboard-overview"], [23, "dashboard-overview"], [24, "dashboard-overview"], [25, "dashboard-overview"]], "Data Models and Schemas": [[15, "data-models-and-schemas"]], "Data Retention and Archival": [[14, "data-retention-and-archival"]], "Database Architecture": [[14, "database-architecture"]], "Database Configuration": [[8, "database-configuration"]], "Database Design": [[15, "database-design"]], "Database Design and Schema": [[14, null]], "Database Issues": [[1, "database-issues"]], "Database Maintenance": [[14, "database-maintenance"]], "Debug Mode": [[1, "debug-mode"]], "Deployment": [[15, "deployment"]], "Detection Validation and Improvement": [[22, "detection-validation-and-improvement"]], "Development": [[9, "development"]], "Development Commands": [[2, "development-commands"]], "Development Environment": [[8, "development-environment"], [15, "development-environment"]], "Development Tools": [[1, "development-tools"]], "Development and Integration": [[15, "development-and-integration"]], "Docker Commands": [[2, "docker-commands"]], "Docker Deployment": [[2, "docker-deployment"]], "Docker Development": [[2, "docker-development"]], "Documentation": [[1, "documentation"]], "Documentation Build and Deployment": [[0, "documentation-build-and-deployment"]], "Documentation Completion Summary": [[0, "documentation-completion-summary"]], "Documentation Maintenance": [[0, "documentation-maintenance"]], "Documentation Metrics": [[2, "documentation-metrics"]], "Enable Debug Logging": [[1, "enable-debug-logging"]], "Enable MFA": [[4, "enable-mfa"]], "Encryption Settings": [[8, "encryption-settings"]], "End-to-End Tests": [[1, "end-to-end-tests"]], "Endpoints": [[3, "endpoints"]], "Enrich IOCs": [[6, "enrich-iocs"]], "Enumeration Types": [[14, "enumeration-types"]], "Environment Configurations": [[1, "environment-configurations"]], "Environment Variables": [[1, "environment-variables"], [10, "environment-variables"]], "Environment-Specific Configuration": [[8, "environment-specific-configuration"]], "Error Handling": [[6, "error-handling"], [7, "error-handling"]], "Error Handling and Recovery Flow": [[13, "error-handling-and-recovery-flow"]], "Error Response": [[5, "error-response"]], "Error Responses": [[3, "error-responses"]], "Escalation Procedures": [[25, "escalation-procedures"]], "Essential Features Tour": [[11, "essential-features-tour"]], "Examples": [[3, "examples"], [5, "examples"]], "Executive Reporting": [[24, "executive-reporting"]], "Executive Summary": [[16, "executive-summary"]], "Exercise Coordination": [[22, "exercise-coordination"]], "Export Analysis Results": [[3, "export-analysis-results"]], "Export Navigator Layer": [[6, "export-navigator-layer"]], "FR-001: Attack Path Discovery": [[16, "fr-001-attack-path-discovery"]], "FR-002: Blast Radius Calculation": [[16, "fr-002-blast-radius-calculation"]], "FR-003: MITRE ATT&CK Integration": [[16, "fr-003-mitre-att-ck-integration"]], "FR-004: Attack Scenario Modeling": [[16, "fr-004-attack-scenario-modeling"]], "FR-005: Asset Discovery and Management": [[16, "fr-005-asset-discovery-and-management"]], "Feature Coverage": [[0, "feature-coverage"]], "Feature Development": [[1, "feature-development"]], "Feature-Specific Guides": [[20, "feature-specific-guides"]], "Features by Use Case": [[9, "features-by-use-case"]], "Feedback and Contributions": [[20, "feedback-and-contributions"]], "Field Selection": [[5, "field-selection"]], "Filtering and Sorting": [[5, "filtering-and-sorting"]], "Financial Impact Assessment": [[7, "financial-impact-assessment"]], "First Login and Setup": [[11, "first-login-and-setup"]], "Framework Mapping": [[12, "framework-mapping"]], "Frequently Asked Questions (FAQ)": [[17, null]], "Frontend Tests": [[1, "frontend-tests"]], "Full Pipeline (Recommended)": [[1, "full-pipeline-recommended"]], "Functional Requirements": [[16, "functional-requirements"]], "Future Roadmap": [[16, "future-roadmap"]], "GDPR Impact Assessment": [[7, "gdpr-impact-assessment"]], "General Guidelines": [[20, "general-guidelines"]], "General Questions": [[17, "general-questions"]], "Generate Mitigations": [[7, "generate-mitigations"]], "Generate Navigator Layer": [[6, "generate-navigator-layer"]], "Get Attack Scenario": [[3, "get-attack-scenario"]], "Get Data Status": [[6, "get-data-status"]], "Get Event Context": [[6, "get-event-context"]], "Get Simulation Results": [[7, "get-simulation-results"]], "Get Technique Information": [[6, "get-technique-information"]], "Get Threat Actor Profile": [[6, "get-threat-actor-profile"]], "Getting Help": [[2, "getting-help"], [10, "getting-help"], [11, "getting-help"], [19, "getting-help"], [20, "getting-help"], [22, "getting-help"], [23, "getting-help"], [24, "getting-help"], [25, "getting-help"]], "Getting Help and Support": [[18, "getting-help-and-support"]], "Getting Started": [[9, null], [18, "getting-started"], [19, "getting-started"], [21, "getting-started"], [22, "getting-started"], [23, "getting-started"], [24, "getting-started"], [25, "getting-started"], [26, "getting-started"]], "Getting Started Section": [[0, "getting-started-section"]], "Graph Algorithms": [[12, "graph-algorithms"]], "Graph Analysis Engine": [[15, "graph-analysis-engine"]], "Graph Data Structures": [[15, "graph-data-structures"]], "Graph Statistics": [[3, "graph-statistics"]], "GraphEngine": [[12, "graphengine"]], "HIPAA Impact Assessment": [[7, "hipaa-impact-assessment"]], "HTTP Status Codes": [[5, "http-status-codes"]], "Health Checks": [[1, "health-checks"], [10, "health-checks"]], "How can I contribute?": [[17, "how-can-i-contribute"]], "How do I add new users?": [[17, "how-do-i-add-new-users"]], "How do I backup my data?": [[17, "how-do-i-backup-my-data"]], "How do I configure audit logging?": [[17, "how-do-i-configure-audit-logging"]], "How do I configure cloud provider integrations?": [[17, "how-do-i-configure-cloud-provider-integrations"]], "How do I enable Multi-Factor Authentication (MFA)?": [[17, "how-do-i-enable-multi-factor-authentication-mfa"]], "How do I get API credentials?": [[17, "how-do-i-get-api-credentials"]], "How do I get help?": [[17, "how-do-i-get-help"]], "How do I install the Blast-Radius Security Tool?": [[17, "how-do-i-install-the-blast-radius-security-tool"]], "How do I integrate with my SIEM?": [[17, "how-do-i-integrate-with-my-siem"]], "How do I reset the admin password?": [[17, "how-do-i-reset-the-admin-password"]], "How do I update to a new version?": [[17, "how-do-i-update-to-a-new-version"]], "How does attack path analysis work?": [[17, "how-does-attack-path-analysis-work"]], "How long does attack path analysis take?": [[17, "how-long-does-attack-path-analysis-take"]], "IOC Enhancement": [[21, "ioc-enhancement"]], "IR-001: SIEM Integration": [[16, "ir-001-siem-integration"]], "IR-002: SOAR Integration": [[16, "ir-002-soar-integration"]], "IR-003: Cloud Platform Integration": [[16, "ir-003-cloud-platform-integration"]], "Immediate Actions": [[11, "immediate-actions"]], "Incident Investigation": [[25, "incident-investigation"]], "Incident Reports": [[25, "incident-reports"]], "Incident Response": [[19, "incident-response"], [25, "incident-response"]], "Incident Response Workflow": [[20, "incident-response-workflow"]], "Indexes and Performance Optimization": [[14, "indexes-and-performance-optimization"]], "Indices and Tables": [[9, "indices-and-tables"]], "Initial Login": [[11, "initial-login"]], "Initial Platform Setup": [[18, "initial-platform-setup"]], "Initial Setup": [[10, "initial-setup"], [22, "initial-setup"], [23, "initial-setup"], [24, "initial-setup"], [25, "initial-setup"]], "Installation Guide": [[10, null]], "Installation Methods": [[10, "installation-methods"]], "Installation and Setup": [[17, "installation-and-setup"]], "Integration Configuration": [[8, "integration-configuration"]], "Integration Examples": [[4, "integration-examples"]], "Integration Management": [[18, "integration-management"]], "Integration Requirements": [[16, "integration-requirements"]], "Integration Setup": [[11, "integration-setup"]], "Integration and API": [[17, "integration-and-api"]], "Integrations": [[5, "integrations"]], "Interactive Debugging": [[1, "interactive-debugging"]], "Interactive Elements": [[2, "interactive-elements"]], "Interpretation Guidelines": [[19, "interpretation-guidelines"]], "Is my data secure?": [[17, "is-my-data-secure"]], "Is there a community or forum?": [[17, "is-there-a-community-or-forum"]], "JWT Token Structure": [[4, "jwt-token-structure"]], "JavaScript/TypeScript": [[4, "javascript-typescript"]], "Key Features": [[9, "key-features"]], "Key Permissions": [[18, "key-permissions"], [22, "key-permissions"], [23, "key-permissions"], [24, "key-permissions"], [25, "key-permissions"]], "Learning Resources": [[11, "learning-resources"]], "License": [[9, "license"]], "List Threat Actors": [[7, "list-threat-actors"]], "Local Container Management": [[1, "local-container-management"]], "Local Deployment": [[2, "local-deployment"]], "Local Development": [[2, "local-development"]], "Local Development & CI/CD Guide": [[1, null]], "Local Performance Testing": [[1, "local-performance-testing"]], "Logging Configuration": [[8, "logging-configuration"]], "Login": [[4, "login"]], "Login Issues": [[11, "login-issues"]], "Logout": [[4, "logout"]], "Logs and Metrics": [[1, "logs-and-metrics"]], "MITRE ATT&CK Data Management": [[21, "mitre-att-ck-data-management"]], "MITRE ATT&CK Integration": [[5, "mitre-att-ck-integration"], [12, "mitre-att-ck-integration"], [20, "mitre-att-ck-integration"]], "MITRE ATT&CK Integration API Reference": [[6, null]], "MITRE ATT&CK Integration User Guide": [[21, null]], "MITRE ATT&CK Mapping": [[3, "mitre-att-ck-mapping"]], "MITRE ATT&CK Mapping Decision Tree": [[13, "mitre-att-ck-mapping-decision-tree"]], "Mermaid Diagrams": [[2, "mermaid-diagrams"], [2, "id1"]], "Method 1: Quick Start with Docker (Recommended)": [[10, "method-1-quick-start-with-docker-recommended"]], "Method 2: Development Installation": [[10, "method-2-development-installation"]], "Method 3: Production Installation": [[10, "method-3-production-installation"]], "Metrics and KPIs": [[23, "metrics-and-kpis"], [25, "metrics-and-kpis"]], "Metrics and Performance Measurement": [[22, "metrics-and-performance-measurement"]], "Mission Statement": [[16, "mission-statement"]], "Mitigation Strategies": [[7, "mitigation-strategies"]], "Mitigation Strategy Generation": [[26, "mitigation-strategy-generation"]], "Monitoring": [[15, "monitoring"]], "Monitoring & Dashboards": [[5, "monitoring-dashboards"]], "Monitoring Settings": [[8, "monitoring-settings"]], "Monitoring and Logging": [[8, "monitoring-and-logging"]], "Monitoring and Maintenance": [[18, "monitoring-and-maintenance"]], "Morning Routine": [[25, "morning-routine"]], "Multi-Environment Support": [[1, "multi-environment-support"]], "Multi-Factor Authentication": [[4, "multi-factor-authentication"]], "Multi-Factor Authentication (Recommended)": [[11, "multi-factor-authentication-recommended"]], "Multi-Factor Risk Assessment": [[12, "multi-factor-risk-assessment"]], "NFR-001: Performance": [[16, "nfr-001-performance"]], "NFR-002: Scalability": [[16, "nfr-002-scalability"]], "NFR-003: Security": [[16, "nfr-003-security"]], "NFR-004: Reliability": [[16, "nfr-004-reliability"]], "NFR-005: Usability": [[16, "nfr-005-usability"]], "Navigation": [[2, "navigation"]], "New Feature Workflow": [[1, "new-feature-workflow"]], "Next Steps": [[10, "next-steps"], [11, "next-steps"]], "Non-Functional Requirements": [[16, "non-functional-requirements"]], "One-Command Setup": [[1, "one-command-setup"]], "Operational Excellence": [[18, "operational-excellence"], [22, "operational-excellence"], [25, "operational-excellence"]], "Operational Security": [[23, "operational-security"]], "Overview": [[3, "overview"], [4, "overview"], [5, "overview"], [6, "overview"], [7, "overview"], [8, "overview"], [9, "overview"], [11, "overview"], [14, "overview"], [15, "overview"], [18, "overview"], [19, "overview"], [20, "overview"], [21, "overview"], [22, "overview"], [23, "overview"], [24, "overview"], [25, "overview"], [26, "overview"]], "Pagination": [[5, "pagination"]], "Parallel Processing": [[12, "parallel-processing"]], "Password Change": [[11, "password-change"]], "Password Management": [[4, "password-management"]], "Path Discovery Algorithm": [[12, "path-discovery-algorithm"]], "Pattern Recognition Engine": [[21, "pattern-recognition-engine"]], "Performance & Scalability": [[9, "performance-scalability"]], "Performance Best Practices": [[8, "performance-best-practices"]], "Performance Considerations": [[3, "performance-considerations"]], "Performance Issues": [[11, "performance-issues"]], "Performance Monitoring": [[1, "performance-monitoring"], [14, "performance-monitoring"]], "Performance Optimization": [[18, "performance-optimization"], [19, "performance-optimization"], [21, "performance-optimization"], [26, "performance-optimization"]], "Performance Optimization Flow": [[13, "performance-optimization-flow"]], "Performance Optimizations": [[12, "performance-optimizations"]], "Performance Testing": [[10, "performance-testing"]], "Performance Tuning": [[15, "performance-tuning"]], "Performance and Operations": [[15, "performance-and-operations"]], "Performance and Troubleshooting": [[17, "performance-and-troubleshooting"]], "Permission Issues": [[1, "permission-issues"]], "Permission System": [[4, "permission-system"]], "Persistence and Privilege Escalation": [[23, "persistence-and-privilege-escalation"]], "Phase 2: Threat Intelligence Integration (Q2 2025)": [[16, "phase-2-threat-intelligence-integration-q2-2025"]], "Phase 3: Advanced Analytics (Q3 2025)": [[16, "phase-3-advanced-analytics-q3-2025"]], "Phase 4: Automation and Orchestration (Q4 2025)": [[16, "phase-4-automation-and-orchestration-q4-2025"]], "Pipeline Commands": [[1, "pipeline-commands"]], "Pipeline Options": [[1, "pipeline-options"]], "Pipeline Overview": [[1, "pipeline-overview"]], "Platform Updates and Maintenance": [[18, "platform-updates-and-maintenance"]], "Platform Won\u2019t Start": [[11, "platform-won-t-start"]], "Port Conflicts": [[1, "port-conflicts"]], "Practical Focus": [[0, "practical-focus"]], "Pre-loaded Threat Actors": [[26, "pre-loaded-threat-actors"]], "Prerequisites": [[1, "prerequisites"], [2, "prerequisites"], [10, "prerequisites"], [11, "prerequisites"], [19, "prerequisites"], [21, "prerequisites"], [26, "prerequisites"]], "Product Overview": [[16, "product-overview"]], "Product Requirements Document": [[15, "product-requirements-document"]], "Product Requirements Document (PRD)": [[16, null]], "Production Deployment": [[2, "production-deployment"]], "Production Environment": [[8, "production-environment"]], "Purple Team Collaboration": [[19, "purple-team-collaboration"]], "Purple Team Dashboard Features": [[22, "purple-team-dashboard-features"]], "Purple Team KPIs": [[22, "purple-team-kpis"]], "Purple Team Members": [[20, "purple-team-members"]], "Purple Team Members Guide": [[22, null]], "Purple Team Methodology": [[22, "purple-team-methodology"]], "Python": [[4, "python"]], "Quality Assurance": [[2, "quality-assurance"], [2, "id2"], [15, "quality-assurance"]], "Quantitative Risk Assessment": [[26, "quantitative-risk-assessment"]], "Quick Installation": [[11, "quick-installation"]], "Quick Start": [[9, "quick-start"]], "Quick Start Example": [[19, "quick-start-example"]], "Quick Start Guide": [[11, null]], "RESTful API": [[21, "restful-api"]], "Rate Limiting": [[3, "rate-limiting"], [5, "rate-limiting"], [6, "rate-limiting"], [7, "rate-limiting"]], "Real-Time Monitoring": [[11, "real-time-monitoring"]], "Real-time Event Correlation": [[21, "real-time-event-correlation"]], "Real-time Monitoring": [[25, "real-time-monitoring"]], "Red Team Campaign Lifecycle": [[23, "red-team-campaign-lifecycle"]], "Red Team Dashboard Features": [[23, "red-team-dashboard-features"]], "Red Team Exercise Workflow": [[20, "red-team-exercise-workflow"]], "Red Team Exercises": [[19, "red-team-exercises"]], "Red Team Members": [[20, "red-team-members"]], "Red Team Members Guide": [[23, null]], "Red Team Methodology": [[23, "red-team-methodology"]], "Red Team Report Generation": [[23, "red-team-report-generation"]], "Refresh Graph Data": [[3, "refresh-graph-data"]], "Regulatory Compliance Assessment": [[26, "regulatory-compliance-assessment"]], "Release Notes": [[9, "release-notes"]], "Release Notes Section": [[0, "release-notes-section"]], "Reporting and Documentation": [[22, "reporting-and-documentation"], [23, "reporting-and-documentation"], [25, "reporting-and-documentation"]], "Resource Monitoring": [[1, "resource-monitoring"]], "Response Procedures": [[25, "response-procedures"]], "Responsive Design": [[2, "responsive-design"]], "Risk Assessment": [[7, "risk-assessment"], [16, "risk-assessment"]], "Risk Assessment and Analysis": [[24, "risk-assessment-and-analysis"]], "Risk Calculation Methodology": [[26, "risk-calculation-methodology"]], "Risk Governance": [[24, "risk-governance"]], "Risk Scoring Decision Flow": [[13, "risk-scoring-decision-flow"]], "Risk Scoring Methodology": [[12, "risk-scoring-methodology"], [19, "risk-scoring-methodology"]], "Role-Based Access Control (RBAC)": [[18, "role-based-access-control-rbac"]], "Role-Based Guides": [[20, "role-based-guides"]], "Role-Specific Tips": [[20, "role-specific-tips"]], "Run Containers": [[1, "run-containers"]], "Running Attack Simulations": [[26, "running-attack-simulations"]], "Running Tests": [[1, "running-tests"]], "SDK and Libraries": [[5, "sdk-and-libraries"]], "SOC Operator Dashboard Features": [[25, "soc-operator-dashboard-features"]], "SOC Operators": [[20, "soc-operators"]], "SOC Operators Guide": [[25, null]], "Scenario 1: APT Detection Validation": [[22, "scenario-1-apt-detection-validation"]], "Scenario 1: Cloud Migration Security Assessment": [[24, "scenario-1-cloud-migration-security-assessment"]], "Scenario 1: External Penetration Testing": [[23, "scenario-1-external-penetration-testing"]], "Scenario 1: Malware Detection": [[25, "scenario-1-malware-detection"]], "Scenario 2: Insider Threat Exercise": [[22, "scenario-2-insider-threat-exercise"]], "Scenario 2: Insider Threat Simulation": [[23, "scenario-2-insider-threat-simulation"]], "Scenario 2: Merger and Acquisition Security Integration": [[24, "scenario-2-merger-and-acquisition-security-integration"]], "Scenario 2: Suspicious Network Activity": [[25, "scenario-2-suspicious-network-activity"]], "Scenario 3: Advanced Persistent Threat (APT) Simulation": [[23, "scenario-3-advanced-persistent-threat-apt-simulation"]], "Scenario 3: Privileged Account Compromise": [[25, "scenario-3-privileged-account-compromise"]], "Scenario 3: Ransomware Response Exercise": [[22, "scenario-3-ransomware-response-exercise"]], "Scenario 3: Zero Trust Architecture Implementation": [[24, "scenario-3-zero-trust-architecture-implementation"]], "Scenario Development and Management": [[22, "scenario-development-and-management"]], "Search Features": [[2, "search-features"]], "Security & Compliance": [[9, "security-compliance"]], "Security & Compliance Section": [[0, "security-compliance-section"]], "Security Administration": [[18, "security-administration"]], "Security Architect Dashboard Features": [[24, "security-architect-dashboard-features"]], "Security Architects": [[20, "security-architects"]], "Security Architects Guide": [[24, null]], "Security Architecture Blueprints": [[24, "security-architecture-blueprints"]], "Security Architecture Design": [[24, "security-architecture-design"]], "Security Assessment": [[19, "security-assessment"]], "Security Assessment Workflow": [[20, "security-assessment-workflow"]], "Security Best Practices": [[8, "security-best-practices"]], "Security Commands": [[1, "security-commands"]], "Security Configuration": [[8, "security-configuration"]], "Security Control Framework": [[24, "security-control-framework"]], "Security Model": [[15, "security-model"]], "Security Recommendations": [[4, "security-recommendations"]], "Security Standards": [[1, "security-standards"]], "Security and Authorization": [[4, "security-and-authorization"]], "Security and Compliance": [[15, "security-and-compliance"], [17, "security-and-compliance"], [18, "security-and-compliance"], [18, "id1"]], "ServiceNow Integration": [[8, "servicenow-integration"], [11, "servicenow-integration"]], "Setup Monitoring": [[7, "setup-monitoring"]], "Simulate Attack": [[7, "simulate-attack"]], "Simulation Capabilities": [[23, "simulation-capabilities"]], "Software Dependencies": [[10, "software-dependencies"]], "Specific Stages": [[1, "specific-stages"]], "Staging Environment": [[8, "staging-environment"]], "Standard Authentication Flow": [[4, "standard-authentication-flow"]], "Stealth and Evasion": [[23, "stealth-and-evasion"]], "Step 1: Add Sample Assets": [[11, "step-1-add-sample-assets"]], "Step 1: Get the Code": [[11, "step-1-get-the-code"]], "Step 2: Run Attack Path Analysis": [[11, "step-2-run-attack-path-analysis"]], "Step 2: Start the Platform": [[11, "step-2-start-the-platform"]], "Step 3: Access the Platform": [[11, "step-3-access-the-platform"]], "Step 3: View Results": [[11, "step-3-view-results"]], "Strategic Planning": [[24, "strategic-planning"]], "Structure and Organization": [[0, "structure-and-organization"]], "Style Guide": [[2, "style-guide"]], "Success Metrics and KPIs": [[16, "success-metrics-and-kpis"]], "Success Response": [[5, "success-response"]], "Support": [[9, "support"]], "Support Resources": [[20, "support-resources"]], "Support and Community": [[17, "support-and-community"]], "Support and Resources": [[5, "support-and-resources"], [15, "support-and-resources"]], "Sync ATT&CK Data": [[6, "sync-att-ck-data"]], "System Architecture": [[15, "system-architecture"]], "System Configuration and Management": [[18, "system-configuration-and-management"]], "System Monitoring": [[18, "system-monitoring"]], "System Overview": [[12, "system-overview"]], "System Requirements": [[10, "system-requirements"]], "TAR-001: Graph Processing Engine": [[16, "tar-001-graph-processing-engine"]], "TAR-002: Database Architecture": [[16, "tar-002-database-architecture"]], "TAR-003: API Architecture": [[16, "tar-003-api-architecture"]], "TAR-004: Security Architecture": [[16, "tar-004-security-architecture"]], "Table Definitions": [[14, "table-definitions"]], "Target Users": [[9, "target-users"], [16, "target-users"]], "Team Facilitation": [[22, "team-facilitation"]], "Technical Analysis": [[24, "technical-analysis"]], "Technical Architecture Requirements": [[16, "technical-architecture-requirements"]], "Technical Coverage": [[0, "technical-coverage"]], "Technical Docs": [[9, null]], "Technical Documentation": [[9, "technical-documentation"], [15, null]], "Technical Documentation (technical/)": [[2, "technical-documentation-technical"]], "Technical Documentation Section": [[0, "technical-documentation-section"]], "Technical Excellence": [[23, "technical-excellence"], [24, "technical-excellence"]], "Technical Metrics": [[16, "technical-metrics"]], "Technical Risks": [[16, "technical-risks"]], "Technical Support": [[15, "technical-support"]], "Technique Correlation": [[6, "technique-correlation"]], "Technique Correlation Engine": [[21, "technique-correlation-engine"]], "Technology Integration Planning": [[24, "technology-integration-planning"]], "Test Categories": [[1, "test-categories"]], "Testing & Quality Assurance": [[9, "testing-quality-assurance"]], "Testing Framework": [[15, "testing-framework"]], "Testing and Development": [[5, "testing-and-development"]], "Testing and Quality Assurance": [[15, "testing-and-quality-assurance"]], "Threat Actor Attribution": [[6, "threat-actor-attribution"], [21, "threat-actor-attribution"]], "Threat Actor Profiles": [[26, "threat-actor-profiles"]], "Threat Actor Simulation": [[7, "threat-actor-simulation"]], "Threat Hunting": [[25, "threat-hunting"]], "Threat Hunting Operations": [[22, "threat-hunting-operations"]], "Threat Intelligence": [[5, "threat-intelligence"], [11, "threat-intelligence"]], "Threat Intelligence Enrichment": [[6, "threat-intelligence-enrichment"], [21, "threat-intelligence-enrichment"]], "Threat Intelligence Integration": [[20, "threat-intelligence-integration"], [26, "threat-intelligence-integration"]], "Threat Intelligence Settings": [[8, "threat-intelligence-settings"]], "Threat Modeling": [[20, "threat-modeling"]], "Threat Modeling & Risk Assessment": [[5, "threat-modeling-risk-assessment"]], "Threat Modeling API Reference": [[7, null]], "Threat Modeling User Guide": [[26, null]], "Threat Modeling Workflow": [[26, "threat-modeling-workflow"]], "Token Refresh": [[4, "token-refresh"]], "Track Campaigns": [[6, "track-campaigns"]], "Training and Certification": [[20, "training-and-certification"]], "Troubleshooting": [[8, "troubleshooting"], [9, "troubleshooting"], [9, null], [10, "troubleshooting"], [19, "troubleshooting"], [25, "troubleshooting"]], "Troubleshooting Section": [[0, "troubleshooting-section"]], "Troubleshooting and Support": [[18, "troubleshooting-and-support"], [22, "troubleshooting-and-support"], [23, "troubleshooting-and-support"], [24, "troubleshooting-and-support"]], "Understanding the Visualization": [[11, "understanding-the-visualization"]], "Update Management": [[18, "update-management"]], "Usability Features": [[0, "usability-features"]], "Use Cases Section": [[0, "use-cases-section"]], "Use Cases and Workflows": [[19, "use-cases-and-workflows"]], "User Account Management": [[18, "user-account-management"]], "User Adoption Metrics": [[16, "user-adoption-metrics"]], "User Guides": [[9, "user-guides"], [9, null], [20, null]], "User Guides (user-guides/)": [[2, "user-guides-user-guides"]], "User Guides Section": [[0, "user-guides-section"]], "User Profile Setup": [[11, "user-profile-setup"]], "User Role Coverage": [[0, "user-role-coverage"]], "User and Access Management": [[18, "user-and-access-management"]], "User and Role Management": [[11, "user-and-role-management"]], "User-Friendly Format": [[0, "user-friendly-format"]], "Using the API": [[19, "using-the-api"]], "Using the Web Interface": [[19, "using-the-web-interface"]], "Verification": [[10, "verification"]], "Vision Statement": [[16, "vision-statement"]], "Vulnerability Exploitation": [[23, "vulnerability-exploitation"]], "Webhooks": [[5, "webhooks"]], "What are the default login credentials?": [[17, "what-are-the-default-login-credentials"]], "What are the system requirements?": [[17, "what-are-the-system-requirements"]], "What is the Blast-Radius Security Tool?": [[17, "what-is-the-blast-radius-security-tool"]], "What\u2019s the roadmap for future features?": [[17, "what-s-the-roadmap-for-future-features"]], "Who should use this tool?": [[17, "who-should-use-this-tool"]], "Why am I not seeing any attack paths?": [[17, "why-am-i-not-seeing-any-attack-paths"]], "Why can\u2019t I access the web interface?": [[17, "why-can-t-i-access-the-web-interface"]], "Why is the platform running slowly?": [[17, "why-is-the-platform-running-slowly"]], "Your First Attack Path Analysis": [[11, "your-first-attack-path-analysis"]], "\u2705 Completed Documentation Files": [[0, "completed-documentation-files"]], "\ud83c\udf89 Conclusion": [[0, "conclusion"]], "\ud83c\udfa8 Features": [[2, "features"]], "\ud83c\udfaf Key Documentation Features": [[0, "key-documentation-features"]], "\ud83c\udfd7\ufe0f Development Workflow": [[1, "development-workflow"]], "\ud83d\udc33 Container Development": [[1, "container-development"]], "\ud83d\udcca Analytics and Monitoring": [[2, "analytics-and-monitoring"]], "\ud83d\udcca Documentation Metrics": [[0, "documentation-metrics"]], "\ud83d\udcca Monitoring & Debugging": [[1, "monitoring-debugging"]], "\ud83d\udccb Documentation Coverage": [[0, "documentation-coverage"]], "\ud83d\udcd6 Documentation Quality": [[0, "documentation-quality"]], "\ud83d\udcd6 Documentation Sections": [[2, "documentation-sections"]], "\ud83d\udcda Additional Resources": [[1, "additional-resources"]], "\ud83d\udcda Documentation Structure": [[0, "documentation-structure"], [2, "documentation-structure"]], "\ud83d\udcde Support": [[2, "support"]], "\ud83d\udd0d Search and Navigation": [[2, "search-and-navigation"]], "\ud83d\udd12 Security-First Development": [[1, "security-first-development"]], "\ud83d\udd27 Build Commands": [[2, "build-commands"]], "\ud83d\udd27 Troubleshooting": [[1, "troubleshooting"]], "\ud83d\ude80 Deployment": [[2, "deployment"]], "\ud83d\ude80 Next Steps": [[0, "next-steps"]], "\ud83d\ude80 Quick Start": [[1, "quick-start"], [2, "quick-start"]], "\ud83d\udee0\ufe0f Development": [[2, "development"]], "\ud83d\udee0\ufe0f Local CI/CD Pipeline": [[1, "local-ci-cd-pipeline"]], "\ud83e\uddea Testing Strategy": [[1, "testing-strategy"]]}, "docnames": ["DOCUMENTATION_SUMMARY", "LOCAL-DEVELOPMENT", "README", "api/attack-path-analysis", "api/authentication", "api/index", "api/mitre-attack-integration", "api/threat-modeling", "configuration", "index", "installation", "quick-start-guide", "technical/attack-path-architecture", "technical/attack-path-flows", "technical/database-design", "technical/index", "technical/product-requirements", "troubleshooting/faq", "user-guides/administrators", "user-guides/attack-path-analysis", "user-guides/index", "user-guides/mitre-attack-integration", "user-guides/purple-team-members", "user-guides/red-team-members", "user-guides/security-architects", "user-guides/soc-operators", "user-guides/threat-modeling"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["DOCUMENTATION_SUMMARY.md", "LOCAL-DEVELOPMENT.md", "README.md", "api/attack-path-analysis.rst", "api/authentication.rst", "api/index.rst", "api/mitre-attack-integration.rst", "api/threat-modeling.rst", "configuration.rst", "index.rst", "installation.rst", "quick-start-guide.rst", "technical/attack-path-architecture.rst", "technical/attack-path-flows.rst", "technical/database-design.rst", "technical/index.rst", "technical/product-requirements.rst", "troubleshooting/faq.rst", "user-guides/administrators.rst", "user-guides/attack-path-analysis.rst", "user-guides/index.rst", "user-guides/mitre-attack-integration.rst", "user-guides/purple-team-members.rst", "user-guides/red-team-members.rst", "user-guides/security-architects.rst", "user-guides/soc-operators.rst", "user-guides/threat-modeling.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [4, 10, 11, 18, 19, 22, 23, 24, 25], "0": [3, 5, 6, 8, 9, 10, 12, 14, 17, 19, 20, 21, 26], "000": 17, "001": [6, 21], "0044": 3, "00z": [3, 4, 5, 21], "01": [4, 12, 21], "04": 10, "05": 26, "0f": [19, 26], "1": [1, 2, 3, 5, 6, 8, 9, 12, 14, 15, 16, 17, 19, 20, 21, 26], "10": [3, 5, 8, 10, 11, 12, 14, 16, 17, 26], "100": [0, 5, 7, 8, 12, 14, 15, 16, 19, 21], "1000": [3, 5, 6, 7, 8, 12, 15, 16, 21], "10000": [6, 7, 26], "100000000": 26, "100gb": [10, 17], "100m": [14, 15, 16, 26], "100mb": 8, "10485760": 8, "10m": [9, 16, 19], "10mb": 8, "10x": 16, "11": [1, 2, 10, 17], "11223344": 4, "11t22": [3, 5], "12": [3, 5, 8, 11, 12, 16], "120": 3, "123456": 4, "12345678": 4, "123e4567": 4, "1250": [3, 5], "125000": 3, "12d3": 4, "13": 16, "14": [17, 20, 21], "145": 3, "15": [0, 3, 10, 17], "15000": 3, "15t10": [4, 21], "16": 10, "1639234567": [3, 5], "1642248000": 7, "168": [21, 26], "16gb": [10, 17], "1705312200": 4, "1705314000": 4, "18": [1, 3, 10, 17], "1800": [4, 8], "192": 21, "1f": [19, 26], "2": [1, 2, 3, 5, 6, 9, 12, 14, 17, 19, 20, 21, 26], "20": [3, 5, 8, 10, 12, 14, 16, 26], "200": [4, 5, 11], "2000": 6, "200m": [15, 16], "201": 5, "2024": [3, 4, 5, 21], "22": 10, "23": 3, "24": [3, 5, 8, 20], "245": 5, "24h": 21, "25": [0, 3], "255": 14, "256": [8, 10, 15, 16, 17], "27001": [16, 17, 24], "27002": 24, "285": 3, "2gb": 16, "3": [1, 2, 3, 5, 8, 12, 14, 15, 17, 19, 20, 21], "30": [3, 4, 5, 8, 10, 11, 12, 14, 16, 21], "300": [0, 3, 5, 8, 16, 19], "3000": [8, 10, 11, 17], "3001": 10, "30d": 21, "32gb": 10, "34": 16, "3420": 3, "3600": [8, 12], "39": 19, "4": [1, 3, 5, 8, 10, 12, 14, 17, 19, 20], "40": [12, 14, 16, 19], "400": [3, 5], "401": 5, "403": 5, "404": [3, 5], "409": 5, "422": 5, "423": [3, 5], "426614174000": 4, "429": 5, "45": 3, "480": 8, "5": [0, 1, 3, 5, 8, 10, 11, 12, 14, 16, 17, 19, 24, 26], "50": [0, 8, 11, 12, 14, 16], "500": [3, 5, 6], "5000": 26, "50000": 26, "500000": 26, "500gb": 10, "500k": 26, "503": 5, "50gb": [10, 17], "5432": [1, 8, 10], "58": 16, "59": 19, "6": [3, 8, 16, 19, 20, 26], "60": [3, 5, 8, 14, 15, 16, 17, 19], "6379": [8, 10], "65535": 14, "6m": 21, "7": [2, 8, 10, 14, 17, 20, 21, 26], "75": [3, 8, 26], "7687": [8, 10], "79": 19, "7d": 21, "8": [3, 8, 10, 12, 17, 21, 26], "80": [14, 16, 19, 21], "800": [20, 21], "8000": [1, 2, 8, 10, 11, 17], "8080": 2, "85": [3, 26], "86400": 8, "87": 3, "87654321": 4, "88": [3, 17], "8gb": [10, 11, 17], "9": [1, 2, 10, 12, 14, 16, 26], "90": [0, 8, 14, 15, 16, 17, 26], "9000": 2, "9090": [2, 8], "90d": 21, "92": 3, "95": [0, 3, 5, 15, 16], "99": 16, "999": 7, "A": [2, 5, 14, 24], "AND": 14, "AS": 14, "As": [18, 22, 23, 24, 25], "BY": 14, "By": 19, "FOR": 14, "For": [1, 2, 4, 6, 7, 8, 9, 10, 11, 12, 17, 20, 21, 25, 26], "IF": 14, "IN": 14, "IT": [22, 24], "If": [3, 10, 11], "In": [2, 9, 11, 12], "It": 17, "NOT": 14, "No": [2, 17, 19], "Not": [3, 5, 25], "ON": 14, "OR": 14, "On": [4, 15], "One": 7, "Or": [1, 17], "THEN": 14, "The": [0, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "These": 13, "To": [11, 16], "WITH": 14, "_": 14, "__init__": [4, 12], "_analyze_single_target": 12, "_build": 2, "_calculate_edge_likelihood": 12, "_calculate_impact_metr": 12, "_calculate_propagation_prob": 12, "_load_mitre_map": 12, "_rank_path": 12, "_static": 2, "_templat": 2, "a1b2c3d4e5f6": 21, "a456": 4, "abil": [22, 23, 24], "about": [4, 6, 17, 23, 25], "abus": [3, 15, 23], "accept": [15, 16, 18, 24], "access": [2, 3, 4, 5, 9, 10, 12, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26], "access_all_team_data": 22, "access_control": 26, "access_token": [4, 5], "access_vulnerability_data": 23, "accomplish": 23, "accord": 18, "account": [3, 4, 5, 8, 10, 11, 17, 19, 26], "accur": [0, 16, 19, 23], "accuraci": [0, 16, 22, 24], "achiev": [16, 22, 23, 24], "acid": 16, "acknowledg": 23, "acquir": 24, "across": [0, 2, 11, 16, 19, 20, 21, 22, 24], "act": 26, "action": [0, 2, 15, 16, 17, 19, 20, 22, 25], "activ": [1, 4, 5, 10, 11, 14, 15, 16, 18, 20, 22, 23, 24], "actor": [3, 5, 9, 11, 12, 16, 17, 18, 19, 20, 22, 23, 24], "actor_id": 26, "actor_prob": 21, "actual": [17, 19, 20], "ad": [0, 18], "adapt": [16, 23], "add": [1, 2, 12, 25], "add_asset": 12, "add_relationship": 12, "addit": [10, 11, 12, 17, 19, 23, 25], "additional_context": 21, "address": [0, 2, 11, 16, 19, 25, 26], "adher": [23, 24], "adjust": [8, 17, 18, 21, 22, 23], "admin": [3, 8, 11, 19], "admin_workst": 3, "administr": [0, 2, 4, 8, 9, 10, 11, 15, 17, 23, 24, 25], "adopt": [0, 22, 24], "advanc": [0, 3, 4, 5, 9, 17, 19, 20, 25], "adversari": 3, "ae": [8, 15, 16, 17], "affect": [16, 17, 19, 21, 25], "affected_asset": [3, 12, 19, 21, 26], "after": [10, 11, 17, 19, 23, 24], "ag": 10, "against": [7, 21, 22, 24], "agent": 26, "aggreg": 12, "agre": 19, "ai": [6, 7, 17, 20, 21], "air": 15, "alert": [4, 5, 11, 15, 16, 18, 19, 20, 22, 24], "alert_threshold": 26, "algorithm": [2, 3, 13, 15, 16, 17], "alias": 21, "align": [16, 17, 22, 24], "all": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26], "alloc": [17, 18, 22, 23, 24], "allow": [1, 8, 17, 19], "alreadi": [0, 7], "altern": [18, 22, 23], "alwai": [4, 5, 14, 23], "an": [3, 7, 9, 16, 18, 19, 25], "analys": [11, 16], "analysi": [0, 1, 2, 8, 9, 10, 16, 18, 22, 25], "analyst": [14, 16, 20, 21, 22, 25], "analyt": [9, 17, 20, 22], "analyz": [2, 5, 12, 14, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "analyze_attack_path": [12, 24], "analyze_attack_pattern": 21, "analyze_behavior": 21, "analyze_multiple_target": 12, "analyze_technique_trend": 21, "ani": [4, 9, 25], "annual": 24, "annual_revenu": 26, "anomal": [22, 25], "anomali": [12, 16, 21, 22], "anomaly_scor": 21, "answer": 17, "anti": 23, "antiviru": [11, 25], "apach": 2, "api": [1, 8, 10, 11, 14, 18, 20, 23, 24, 26], "api_bas": [5, 19], "api_rate_limit": 8, "api_token": [2, 5, 21, 26], "api_v1_str": 8, "apitoken": 5, "app": [1, 8, 10, 11, 17], "app_server_001": 3, "appear": 25, "append": 12, "appli": [12, 14, 17], "applic": [3, 4, 6, 7, 10, 11, 14, 16, 17, 18, 21, 22, 23, 26], "approach": [1, 16, 20, 22, 24], "appropri": [0, 2, 5, 8, 11, 18, 19, 21, 22, 25, 26], "approv": 18, "apt": [3, 5, 10, 19, 26], "apt28": [7, 20, 21, 26], "apt29": [3, 5, 7, 19, 20, 21, 26], "ar": [3, 5, 6, 7, 11, 18, 19, 25, 26], "architect": [0, 2, 9, 11, 16, 17, 18, 25], "architectur": [0, 1, 2, 9, 11, 17, 18, 19, 20], "archiv": [15, 17, 18], "area": [5, 24], "arrai": [3, 5], "arrow": 11, "artifact": [22, 24], "asc": 5, "ask": 9, "aspect": [15, 18], "assess": [0, 2, 9, 11, 15, 17, 18, 21, 22, 23, 25], "assess_financial_impact": 26, "assess_gdpr_impact": 26, "assess_hipaa_impact": 26, "assess_risk": [4, 24], "assess_sox_impact": 26, "asset": [0, 2, 3, 7, 8, 9, 12, 13, 14, 17, 19, 21, 23, 24, 25, 26], "asset_data": 12, "asset_id": 12, "asset_metadata": 12, "asset_not_found": 7, "asset_relationship": 14, "asset_relationships_confidence_rang": 14, "asset_relationships_no_self_refer": 14, "asset_relationships_port_rang": 14, "asset_risk": 12, "asset_status_enum": 14, "asset_typ": [5, 14], "asset_type_enum": 14, "assets_risk_score_rang": 14, "assets_soft_delet": 14, "assign": [11, 17, 18, 19, 23, 25], "assist": [10, 15, 22], "associ": [21, 24], "async": [4, 12], "asyncio": 12, "atmospher": 22, "att": [2, 9, 15, 17, 19, 23, 24, 26], "attack": [0, 2, 4, 8, 9, 15, 18, 22], "attack_path": [2, 3, 5, 12, 14, 19], "attack_paths_detection_difficulty_rang": 14, "attack_paths_impact_score_rang": 14, "attack_paths_likelihood_rang": 14, "attack_paths_path_length_posit": 14, "attack_paths_risk_score_rang": 14, "attack_scenario": [14, 26], "attack_scenarios_detection_probability_rang": 14, "attack_scenarios_impact_score_rang": 14, "attack_scenarios_likelihood_rang": 14, "attack_scenarios_risk_score_rang": 14, "attack_sophist": 21, "attack_techniqu": [3, 12, 14, 19], "attackpath": [5, 12], "attackscenario": 12, "attackscenariorequest": 5, "attacktechniqu": 12, "attempt": [8, 22, 23], "attend": 19, "attent": [11, 19, 24, 25], "attribut": [5, 15, 16, 18, 20, 23], "attribute_threat_actor": 21, "attributed_group": 21, "attribution_fail": 6, "attribution_threshold": 21, "aud": 4, "audit": [1, 4, 8, 9, 14, 15, 16, 18, 23, 24, 26], "audit_configuration_chang": 8, "audit_data_access": 8, "audit_databas": 26, "audit_en": 8, "audit_impl": 26, "audit_log": 14, "audit_log_all_request": 8, "audit_log_path": 8, "audit_log_sensitive_data": 8, "audit_login_ev": 8, "audit_permission_chang": 8, "auditor": 24, "auth": [4, 5, 11], "auth_rate_limit": 8, "auth_respons": 5, "authclient": 4, "authent": [0, 2, 9, 12, 14, 15, 16, 18, 19, 20, 21, 24], "authi": 11, "author": [0, 3, 5, 6, 7, 9, 14, 15, 16, 17, 18, 19, 21, 23, 24, 26], "authservic": 4, "auto": [15, 18], "autocomplet": 2, "autom": [2, 5, 6, 9, 11, 14, 15, 18, 20, 22, 23, 24], "automat": [2, 8, 16, 19, 20, 26], "autonom": 16, "av": 2, "avail": [3, 4, 5, 7, 10, 11, 15, 16, 17, 18, 19, 20, 23], "available_actor": 7, "averag": [14, 19, 22, 23, 25], "average_clust": 3, "avoid": [12, 23, 24], "aw": [0, 8, 10, 11, 16, 17, 18, 20], "await": [4, 5, 12, 21], "awar": [20, 22, 24, 25], "aws_access_key_id": [8, 10], "aws_default_region": [8, 10], "aws_enable_ec2": 8, "aws_enable_iam": 8, "aws_enable_rd": 8, "aws_enable_s3": 8, "aws_enable_vpc": 8, "aws_max_retri": 8, "aws_poll_interv": 8, "aws_retry_delai": 8, "aws_secret_access_kei": [8, 10], "aws_session_token": 8, "azur": [0, 8, 10, 11, 16, 17, 18, 20], "azure_client_id": [8, 10], "azure_client_secret": [8, 10], "azure_enable_comput": 8, "azure_enable_network": 8, "azure_enable_secur": 8, "azure_enable_storag": 8, "azure_max_retri": 8, "azure_poll_interv": 8, "azure_subscription_id": 8, "azure_tenant_id": [8, 10], "b": [1, 2], "backdoor": 23, "backend": [8, 10, 11, 17], "background": [3, 16], "backoff": 5, "backs_up": 14, "backup": [10, 11, 16, 19, 20, 22, 24], "backup_cod": 4, "backup_server_001": [3, 19], "backup_system": 14, "backward": [15, 16], "bad": [3, 5], "balanc": [18, 19, 22, 23, 24], "bandit": 1, "bandwidth": [10, 17], "base": [0, 1, 2, 4, 8, 9, 12, 15, 16, 17, 19, 21, 22, 23, 24, 25, 26], "base64": 4, "base64_payload": 21, "base_url": [4, 5, 21, 26], "baselin": [18, 19, 21], "baseline_scor": 21, "baseurl": [4, 5], "bash": [0, 1, 10], "basic": [8, 9, 10, 20], "batch": [3, 15, 21, 26], "bcrypt": 8, "bear": [3, 26], "bearer": [3, 4, 5, 6, 7, 17, 19, 21, 26], "becom": 11, "befor": [1, 4, 5, 9, 11, 14, 16, 17, 19, 20, 21, 26], "begin": [3, 14, 19, 24], "behavior": [3, 9, 12, 16, 17, 22, 25], "behavior_analysi": 21, "behavioral_scor": 21, "benchmark": [15, 18, 24], "benefit": [3, 24, 26], "best": [0, 2, 9, 10, 11, 15], "better": [3, 26], "between": [0, 2, 11, 19, 22], "beyond": 23, "bf": 12, "bi": [11, 16, 17, 18], "billing_system": 26, "bin": [1, 10], "bit": [8, 10], "blast": [1, 4, 5, 6, 7, 8, 10, 11, 14, 15, 18, 20, 21, 22, 23, 24, 25, 26], "blast_neo4j": 10, "blast_neo4j_password": 8, "blast_pass": [8, 10], "blast_radiu": [2, 3, 5, 8, 10, 14, 17, 21, 26], "blast_radius_cach": 12, "blast_radius_sess": 8, "blast_respons": 5, "blast_result": 19, "blast_us": [8, 10, 17], "blastradiu": [4, 5, 10, 11, 17], "blastradius2024": [10, 11, 17], "blastradiuscli": [2, 5, 21, 26], "blastradiusresult": 12, "blind": 20, "block": 2, "blue": [18, 19, 20, 23], "bodi": [3, 4], "bolt": [8, 10], "boolean": 14, "boot": 23, "both": [22, 24], "bottleneck": 18, "boundari": [22, 23, 24, 26], "branch": [1, 2, 15], "brand": 18, "breach": 25, "breach_scenario": 26, "breach_typ": 26, "breadcrumb": 2, "breadth": 12, "break": 12, "bridg": [14, 22], "brief": [22, 25], "broader": 22, "broken": 2, "brows": 11, "browser": [11, 17, 25], "brute": 18, "budget": [24, 26], "budget_constraint": 26, "bug": [9, 15, 17, 20], "build": 22, "builder": 2, "building_system": 14, "built": [2, 9, 12, 21, 25], "bulk": [15, 18], "busi": [7, 9, 12, 15, 17, 18, 19, 20, 23, 24, 25, 26], "business_context": 26, "business_crit": [12, 26], "business_disrupt": 26, "business_multipli": 12, "bypass": 23, "bypass_analysi": 23, "c": [1, 2, 10], "ca": 8, "cach": [1, 2, 8, 9, 14, 15, 16, 17, 18, 19, 21, 23, 25, 26], "cache_attack_path": 12, "cache_hit": 3, "cache_kei": 12, "cache_miss": 3, "cache_result": 12, "cache_ttl": 8, "calcul": [5, 7, 9, 12, 15, 17, 20, 21, 23, 25], "calculate_blast_radiu": 12, "calculate_path_risk_scor": 12, "calculateblastradiu": 5, "california": 16, "call": [8, 14, 20], "campaign": [5, 19, 20, 21, 22, 26], "campaign_correl": 21, "campaign_id": 21, "can": [2, 8, 16, 19], "capabl": [3, 4, 6, 7, 10, 11, 12, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26], "capac": [16, 18, 20], "captur": [22, 23], "carbanak": 26, "card": [16, 24], "care": 16, "cascad": [9, 14], "case": [11, 14, 20], "catalog": 26, "categori": [16, 22, 23, 24], "caus": [17, 22, 25], "ccpa": [16, 26], "cd": [0, 2, 8, 10, 11, 15, 17], "cdn": 8, "cef": 17, "center": [18, 20], "cento": 10, "central": [15, 18, 22], "central1": 8, "centric": 24, "ceo": 26, "cert": 8, "cert_non": 8, "cert_opt": 8, "cert_requir": 8, "certbot": 10, "certif": [8, 10, 11, 15, 18, 22, 23, 24, 25, 26], "certifi": 20, "certificate_author": 14, "certification_impact": 26, "certonli": 10, "cfo": 26, "chain": [16, 19, 21, 22, 23, 24, 26], "challeng": 4, "chang": [0, 1, 2, 5, 8, 10, 15, 16, 17, 18, 19, 20, 22, 24, 26], "changelog": [0, 2, 5, 17], "channel": [11, 15, 17, 22, 23], "charact": 11, "chat": 17, "check": [2, 6, 8, 11, 14, 15, 16, 17, 18, 19, 20, 21, 24, 25], "checkout": [1, 2], "chmod": 1, "choos": [19, 22, 26], "chown": [1, 10], "chrome": [11, 17], "chronolog": 25, "ci": [2, 15, 16], "cicd": 1, "ck": [2, 9, 15, 17, 19, 23, 24, 26], "claim": 4, "class": [4, 12], "classif": [15, 16, 18, 19, 20, 23], "clean": [1, 2], "cleanup": [14, 17, 18], "cleanup_attack_path": 14, "cleanup_audit_log": 14, "clear": [2, 16, 17, 18, 19, 22, 23, 25], "clearli": 23, "cli": [8, 10, 11, 17], "click": [11, 17, 19], "client": [2, 4, 5, 8, 10, 15, 17, 19, 21, 26], "clone": [1, 10, 11, 17], "close": 25, "closur": [18, 25], "cloud": [0, 1, 5, 9, 10, 12, 15, 18, 19, 20, 22], "cloud_secur": 14, "cloud_servic": 14, "cloudtrail": 18, "cmdb": [8, 11, 17], "cobalt": 26, "code": [0, 1, 3, 6, 7, 15, 17, 20], "collabor": [0, 2, 9, 11, 16, 17, 18, 20, 24], "collaborate_purple_team": 23, "collect": [2, 5, 8, 9, 15, 20, 22, 23, 25], "color": 25, "color_schem": 21, "column": 14, "com": [1, 3, 4, 5, 6, 7, 8, 10, 11, 17, 19, 20, 21, 26], "combin": [12, 24], "command": [10, 17, 18, 21, 23, 26], "command_lin": 21, "commercial_fe": 26, "commit": [1, 2], "common": [0, 2, 6, 7, 8, 9, 14, 15, 17, 21], "common_techniqu": 21, "commonli": 17, "commun": [2, 5, 9, 11, 15, 16, 18, 19, 20, 23, 25], "communicates_with": 14, "compar": [19, 20, 21], "compare_threat_actor": 21, "comparison": [21, 24], "compat": [15, 16, 25], "competit": [16, 22], "complet": [2, 5, 6, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 26], "complex": [0, 2, 3, 12, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26], "complexity_factor": 12, "compli": 23, "complianc": [5, 12, 14, 19, 20, 23, 25], "compliance_impact": 3, "compliance_risk_chang": 26, "compliant": 17, "compon": [0, 1, 2, 8, 15, 18, 24], "compos": [1, 2, 10, 11, 15, 17], "composit": 14, "comprehens": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25], "compress": 8, "compromis": [3, 6, 12, 16, 19, 20, 21, 22, 23, 26], "compromised_serv": [3, 5, 19], "comput": 17, "concept": [20, 23], "conclus": 20, "concurr": [8, 14, 15, 16, 18, 26], "conduct": [18, 20, 22, 23, 24, 25], "conduct_threat_hunt": 22, "conf": 2, "confer": 22, "confid": [6, 14, 20, 21], "confidence_bas": 21, "confidence_scor": 21, "config": [1, 5, 8, 10, 18], "configur": [0, 2, 4, 5, 7, 9, 14, 15, 16, 20, 21, 22, 23, 24, 25, 26], "configure_system": [4, 18], "confirm": [11, 18, 19, 22], "confirm_password": 4, "conflict": [5, 10, 22], "congratul": 9, "connect": [8, 10, 11, 17, 18, 20, 21, 22, 23, 25], "connector": 15, "conserv": 19, "consid": [0, 3, 10, 11, 17, 19, 20, 22, 24, 26], "consider": [5, 9, 12, 19, 22, 23, 24, 26], "consider_supply_chain": 26, "consist": [0, 2, 5, 16, 18, 23], "consol": 18, "consolid": 24, "const": [4, 5], "constraint": [14, 15, 22, 24], "construct": [20, 22], "consult": [10, 18, 22, 24, 25], "contact": [11, 18, 19, 20, 22, 25], "contain": [2, 5, 11, 14, 17, 19, 20, 22, 24, 25], "container": [2, 15], "content": [2, 3, 4, 6, 7, 21, 26], "context": [16, 19, 20, 21, 22], "contextu": [6, 21, 25], "conting": [18, 23], "continu": [0, 5, 9, 12, 15, 16, 18, 19, 20, 22, 23, 24, 25], "contribut": 9, "contributor": [0, 9], "control": [0, 1, 3, 4, 5, 7, 9, 10, 11, 12, 15, 16, 17, 19, 20, 21, 22, 23, 25, 26], "control_nam": 26, "convent": 15, "cooki": 4, "coordin": [18, 20, 23, 24, 25], "coordinate_exercis": 22, "copi": [2, 10, 12, 17], "cor": [8, 10, 18], "core": [0, 9, 10, 15, 17, 20, 25], "corner": 11, "corpor": 25, "correct": [11, 16], "correl": [5, 9, 11, 15, 16, 17, 18, 20, 23, 25], "correlate_ev": 21, "correlation_fail": 6, "correlation_window": 21, "cors_allow_credenti": 8, "cors_allow_head": 8, "cors_allow_method": 8, "cors_max_ag": 8, "cors_origin": [8, 10], "cost": [7, 18, 19, 20, 24, 26], "could": 25, "count": 21, "coupl": 15, "cov": 1, "cover": [0, 2, 4, 8, 11, 15, 17, 19, 20, 21, 24, 26], "coverag": [1, 2, 5, 6, 9, 15, 16, 20, 21, 22, 23, 24, 26], "covert": 23, "cozi": [3, 26], "cp": [10, 17], "cpu": [10, 11, 15, 17, 18], "creat": [0, 1, 2, 4, 5, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "create_attack_scenario": 12, "created_at": [3, 14], "created_bi": 14, "createscenario": 5, "creation": [5, 12, 15, 16, 18, 20], "credenti": [4, 8, 10, 11, 18, 22, 23, 25], "credential_harvesting_tool": 3, "creep": 23, "crisi": 22, "criteria": [15, 16, 19, 22, 23, 24], "critic": [1, 3, 5, 8, 9, 12, 14, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26], "critical_asset": 26, "critical_assets_affect": [3, 19], "critical_databas": 26, "criticality_level": [3, 5, 14, 19], "criticality_level_enum": 14, "criticality_scor": [3, 14], "cron": 17, "cross": [0, 8, 21, 22, 23, 24], "crown": [23, 24], "crown_jewel": 17, "crucial": 23, "crud": 17, "css": 2, "csv": 18, "cultur": 22, "curl": [1, 3, 6, 7, 10, 17, 21, 26], "currenc": 16, "current": [0, 3, 4, 6, 12, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "current_level": 12, "current_password": 4, "current_scor": 21, "current_timestamp": 14, "cursor": 5, "custodi": 23, "custom": [0, 1, 2, 10, 11, 16, 18, 20, 21, 23, 24, 25, 26], "custom_apt": 26, "custom_threat_actor": 26, "customer_count": 26, "customer_databas": 26, "cut": 9, "cve": 23, "cvss": 17, "cybersecur": [16, 24], "cycl": 12, "cypher": 10, "d": [1, 2, 3, 10, 11, 17, 21, 26], "dai": [14, 16, 21, 23, 25, 26], "daili": [16, 18, 24, 26], "damag": 23, "dark": 2, "dashboard": [0, 10, 15, 17, 20], "data": [0, 4, 5, 7, 8, 9, 10, 11, 18, 19, 20, 22, 23, 24, 25, 26], "data_assets_affect": 3, "data_breach": 26, "data_exfiltr": 14, "data_integr": 26, "data_loss_prevent": 14, "data_sensit": 26, "data_sourc": [12, 21], "data_subject_impact": 26, "data_subjects_affect": 26, "data_sync_in_progress": 6, "data_typ": 26, "databas": [2, 3, 5, 10, 11, 12, 17, 18, 19, 23], "database_001": [3, 5, 19, 26], "database_command_timeout": 8, "database_connect_timeout": 8, "database_echo": 8, "database_max_overflow": [8, 10], "database_pool_recycl": 8, "database_pool_s": [8, 10], "database_pool_timeout": 8, "database_ssl_mod": 8, "database_url": [8, 10], "dataset": [5, 15, 17, 21], "date": [1, 18, 26], "db": [1, 10, 12], "dbeaver": 1, "dbname": 10, "ddo": 18, "de": 22, "deactiv": 18, "debug": [8, 10, 15, 18], "debugg": 1, "deception_technologi": 14, "decim": 14, "decis": [2, 15, 18, 19, 20, 22, 24, 25], "decomposit": 15, "dedic": [10, 17], "deep": [15, 17], "def": [4, 12], "default": [2, 3, 5, 8, 10, 11, 12, 14, 18, 19], "defens": [15, 19, 20, 22, 23, 24], "defin": [17, 18, 19, 20, 22, 23, 24, 26], "definit": [15, 18, 19, 22, 23], "degrad": 16, "degre": [3, 12, 16, 17, 19], "deleg": [14, 18], "delet": [3, 8, 9, 14, 15, 16, 18], "deleted_at": 14, "deliv": 22, "deliveri": 23, "demonstr": [16, 22], "deni": 25, "densiti": 3, "depend": [1, 2, 11, 15, 17, 18, 19, 20, 22, 24], "depends_on": 14, "deploi": [0, 1, 2, 10, 23], "deploy": [1, 8, 9, 10, 11, 14, 16, 18, 20, 23, 26], "deprovis": 18, "depth": [2, 9, 15, 17, 19, 24], "desc": [5, 14], "descript": [2, 3, 8, 14, 16, 19, 20, 21, 23], "design": [0, 9, 11, 12, 16, 17, 18, 20, 22, 23, 25], "design_security_control": [4, 24], "desktop": 1, "destin": 25, "destruct": 18, "detail": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "detect": [0, 5, 11, 12, 15, 16, 19, 20, 21, 23, 24, 26], "detection_difficulti": [3, 14], "detection_enhanc": 26, "detection_method": [3, 12], "detection_prob": [3, 14, 26], "determin": [12, 19, 25], "dev": [1, 2, 10], "develop": [0, 16, 17, 18, 19, 23, 24, 25], "devic": 2, "df": 11, "diagram": [0, 13, 15], "dict": [4, 12], "differ": [0, 2, 9, 10, 11, 17, 18, 20, 22, 25], "difficulti": [12, 19], "digraph": 12, "direct": [2, 11, 14, 16, 17, 18, 19, 26], "direct_cost": 26, "direction_enum": 14, "directli": 19, "directori": [2, 10, 18, 23], "disabl": [8, 17, 18, 25], "disagr": 22, "disast": [11, 15, 16, 20], "discord": 17, "discov": [3, 5, 16, 17, 19, 20, 23, 26], "discover_attack_path": 23, "discoveri": [3, 5, 9, 11, 15, 17, 22, 24, 26], "discovery_respons": 5, "discuss": [2, 5, 9, 15, 17, 20, 22, 26], "displai": [8, 19], "dispos": [18, 23], "disrupt": [16, 26], "distinct": 14, "distribut": 12, "distributedcach": 12, "dive": 15, "divers": [14, 15, 24], "dmz": 11, "dn": 23, "doc": [0, 2, 5, 8, 10, 11, 17, 20], "docker": [0, 1, 11, 15, 17], "docker_contain": 14, "docs_url": 8, "document": [4, 5, 6, 7, 10, 11, 12, 13, 14, 17, 18, 19, 20, 21, 24, 26], "doe": [4, 6, 21], "domain": [6, 8, 10, 11, 19, 20, 21, 23, 24], "domain_control": 14, "down": [1, 11, 17], "download": [5, 10, 11, 17], "drift": [11, 24], "driven": [7, 20, 22, 25], "driver": 23, "dss": [16, 20, 24], "dump": [12, 17], "duplic": 5, "durat": [21, 23], "duration_dai": 21, "dure": [10, 16, 19, 20, 22, 23], "duti": 24, "dynam": [15, 22], "e": [5, 6, 12, 19], "e2": 1, "e89b": 4, "each": [0, 2, 8, 9, 12, 14, 18, 20, 22], "easi": [0, 5], "east": [8, 10], "eastern_europ": 21, "ec2": 17, "edg": [3, 9, 11, 12, 15, 25], "edge_data": 12, "edge_likelihood": 12, "edit": [1, 10], "edr": [21, 22, 23], "educ": 23, "effect": [2, 8, 9, 12, 17, 18, 19, 20, 22, 23, 24, 25, 26], "effici": [12, 15, 18, 22, 24, 26], "element": 0, "els": 14, "elsif": 14, "email": [4, 10, 11, 14, 17], "email_secur": 14, "email_serv": 19, "emerg": [24, 25], "empir": 26, "emploi": 22, "employe": [18, 19], "empow": 16, "enabl": [6, 7, 8, 9, 11, 16, 18, 24], "enc": 21, "encapsul": 23, "encount": [10, 11], "encrypt": [10, 12, 14, 15, 16, 17, 18, 19, 23, 24], "encryption_algorithm": 8, "encryption_system": 14, "end": [14, 15, 16], "endpoint": [0, 2, 6, 7, 9, 15, 17, 19, 21, 24], "endpoint_detect": 21, "endpoint_protect": 14, "enforc": [15, 18], "engag": [16, 22, 23, 24], "engin": [3, 12, 13, 17, 19, 20, 22, 23, 24, 26], "enhanc": [2, 6, 16, 17, 19, 20, 23, 24, 25], "enrich": [5, 20], "enrich_ioc": 21, "enriched_ioc": 21, "ensur": [0, 6, 7, 9, 11, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26], "enter": [3, 11, 17], "enterpris": [3, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 20, 21, 24, 26], "enterprise_count": 21, "entiti": [2, 5, 15], "entity_id": 21, "entity_typ": 21, "entri": [12, 19, 20, 23], "entry_point": [3, 14, 16, 19], "entrypoint": 5, "enum": 14, "enumer": [15, 20, 23], "env": [1, 8, 10, 17], "environ": [0, 4, 5, 9, 11, 14, 16, 18, 19, 21, 23, 24, 25], "environment": 21, "erad": [22, 25], "err": 5, "error": [2, 4, 8, 10, 11, 12, 15, 16, 17, 18, 24], "escal": [16, 18, 19, 20, 22, 24], "especi": 4, "espionag": 26, "essenti": 9, "establish": [0, 18, 19, 22, 23], "estim": [16, 19, 20, 26], "estimated_dur": [3, 14], "estimated_tim": [3, 14], "estimated_time_to_compromis": 26, "etc": [2, 10, 20, 24, 26], "ethic": 23, "eu": 16, "evalu": [10, 19, 20, 22, 23, 24], "evas": [16, 20], "event": [4, 5, 8, 11, 15, 16, 17, 20, 22, 25], "event_count": 21, "event_data": 21, "event_id": 21, "event_typ": 21, "everi": [1, 15], "everyth": [0, 1, 2, 5, 20], "evict": 16, "evid": [11, 21, 22, 23, 24, 25], "evolut": [15, 16], "evt_12345": 21, "evt_67890": 21, "ex": 21, "examin": 19, "exampl": [0, 6, 7, 9, 10, 17, 20, 21, 26], "exce": 25, "exceed": [5, 12], "excel": 16, "except": [12, 18], "exec": [1, 11, 17], "execut": [1, 12, 14, 15, 18, 19, 20, 22, 23, 25, 26], "executor": 12, "exercis": [16, 23], "exfiltr": [3, 16, 19, 22, 23, 24], "exist": [0, 6, 10, 11, 17, 19, 20, 22, 24, 25], "exit": 11, "exp": 4, "expand": [2, 11], "expans": 24, "expect": [17, 22], "expert": 24, "expertis": [18, 20, 22, 24], "expir": [4, 17], "expires_in": 4, "exploit": [16, 17, 19, 24, 26], "explor": [11, 12, 23], "exponenti": 5, "export": [1, 4, 11, 12, 15, 18, 19, 20, 21, 25], "export_navigator_lay": 21, "expos": 5, "exposur": [23, 24, 25, 26], "extend": [12, 14, 17], "extens": [1, 2, 12, 14, 15], "extern": [0, 4, 8, 11, 15, 17, 18, 19, 20, 21, 24, 26], "extract": 23, "eyjhbgcioijiuzi1niisinr5cci6ikpxvcj9": 4, "eyjpzci6mtizndu2nzg5mh0": 5, "f": [1, 3, 4, 5, 10, 12, 19, 21, 26], "face": [19, 20, 23, 24, 26], "facet": 2, "facilitate_commun": 22, "factor": [2, 3, 8, 15, 16, 18, 19, 20, 22, 23, 26], "fail": [1, 3, 4, 6, 7, 11, 12, 24], "failov": [15, 18], "failur": [11, 22, 24], "fair": [6, 7], "fairli": 22, "fals": [4, 8, 10, 12, 14, 21, 22, 23, 24, 25], "familiar": 11, "fanci": 26, "faq": [0, 9], "fast": 15, "faster": 1, "fastest": 10, "fault": 9, "feasibl": 26, "feat": 1, "featur": [7, 12, 15, 16, 19, 21], "feder": 14, "feed": [11, 15, 16, 17, 18, 20, 21, 22, 25, 26], "feedback": [0, 2, 22, 23], "fetch": 4, "field": [8, 9, 11], "field_encryption_kei": 8, "file": [1, 2, 8, 9, 10, 17, 18, 19, 23], "fileless": 23, "fill": 17, "filter": [2, 9, 11, 17, 25], "fin7": [7, 20, 26], "financ": 26, "financi": [3, 12, 16, 19, 20, 26], "financial_data_affect": 26, "financial_impact": [3, 19, 26], "financial_reporting_system": 26, "find": [12, 17, 18, 19, 20, 22, 23, 25], "find_attack_path": 12, "fine": [4, 17, 18, 26], "firefox": [11, 17], "firewal": [11, 14, 17, 18, 24], "firmwar": 14, "first": [9, 10, 12, 17, 18, 19, 21], "first_nam": [4, 14], "first_seen": 21, "fix": 1, "flexibl": [14, 15, 16], "float": 12, "flow": [0, 2, 5, 15, 20, 24], "flushal": 17, "focu": [11, 14, 19, 20, 22, 25], "focus": [0, 11, 24], "follow": [0, 2, 5, 8, 11, 13, 18, 19, 20, 22, 23, 24, 25], "foothold": [3, 23], "forbidden": 5, "forc": [18, 21], "force_upd": 21, "forecast": 24, "forens": [19, 20, 23, 25], "fork": [2, 15], "forkrul": [1, 10, 11, 17], "formal": 18, "format": [2, 3, 6, 9, 12, 15, 16, 17, 18, 21], "forum": [5, 11, 15, 18, 20, 22, 24], "forward": [17, 18], "foster": 22, "found": [3, 5, 7, 19], "foundat": [0, 12, 14, 16], "framework": [3, 5, 6, 9, 17, 19, 20, 21, 22, 23, 26], "free": [3, 10, 19], "frequenc": 22, "frequent": [9, 21], "friendli": [2, 20], "from": [0, 2, 3, 4, 5, 6, 7, 8, 11, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "frontend": [10, 11, 17], "full": [2, 11, 17, 18], "function": [1, 2, 5, 14, 15, 17, 18, 24, 26], "fundament": 20, "further": 25, "futur": [15, 19, 22, 24, 25], "g": [5, 6, 19], "g0016": 21, "g0028": 21, "gain": [3, 19, 23], "gap": [15, 19, 20, 22, 23, 24], "gather": [20, 23, 25], "gcm": 8, "gcp": [0, 8, 10, 11, 16, 17, 20], "gcp_default_region": 8, "gcp_enable_comput": 8, "gcp_enable_iam": 8, "gcp_enable_network": 8, "gcp_enable_storag": 8, "gcp_poll_interv": 8, "gcp_project_id": 8, "gdpr": [3, 16, 17, 19, 24, 26], "gdpr_impact": 26, "gen_random_uuid": 14, "gener": [0, 2, 4, 5, 8, 9, 14, 15, 16, 18, 19, 21, 22, 24, 25], "generate_executive_report": 24, "generate_improvement_report": 22, "generate_mitig": 26, "generate_navigator_lay": 21, "generate_report": [4, 25], "generate_scenario": 23, "generate_visu": 21, "genuin": 25, "geograph": [18, 26], "geographic_region": 21, "get": [5, 8, 12, 16], "get_attack_path": 12, "get_cached_result": 12, "get_correlation_rul": 21, "get_data_statu": 21, "get_edge_data": 12, "get_event_context": 21, "get_event_loop": 12, "get_mitre_attack_map": 12, "get_threat_actor_profil": 21, "gh": 2, "gin": 14, "git": [1, 10, 11, 15, 17], "github": [0, 1, 2, 5, 9, 10, 11, 15, 17], "github_token": 2, "go": [5, 11, 15, 16, 17, 19], "goal": [22, 24], "good": 11, "googl": [8, 11, 18], "google_application_credenti": [8, 10], "govern": [18, 20, 21, 26], "grace": [16, 17], "gracefulli": 5, "grade": [0, 6, 7, 9, 12, 15, 16, 21, 26], "gradual": [11, 19], "grain": [4, 17, 18], "granular": [15, 16, 18, 21], "graph": [2, 5, 8, 9, 10, 11, 17, 19, 20, 22, 23, 25], "graph_engin": 12, "graph_statist": 3, "graphql": 15, "green": [11, 18], "grep": [10, 11, 17], "griffon": 26, "group": [3, 6, 18, 19, 21, 22, 23, 26], "group1_uniqu": 21, "group2_uniqu": 21, "group_id": 21, "group_not_found": 6, "growth": [16, 18, 20, 21, 24], "growth_rat": 21, "gui": 1, "guid": [4, 6, 7, 15, 16, 17], "guidanc": [0, 16, 20, 22, 23, 25], "guidelin": [1, 17], "h": [3, 6, 7, 10, 17, 21, 26], "ha": [0, 19], "handl": [2, 5, 8, 15, 16, 17, 18, 23, 24, 25], "happi": 1, "hardcod": 8, "harden": [18, 24], "hardwar": [18, 23], "harmon": 24, "harvest": 23, "has_mor": 5, "hash": 21, "have": [9, 11, 18, 19, 21, 22, 23, 24, 25, 26], "head": 0, "header": [2, 3, 4, 5, 7, 17, 18, 19], "heal": 16, "health": [8, 11, 15, 16, 18, 20, 25, 26], "health_check_databas": 8, "health_check_en": 8, "health_check_external_api": 8, "health_check_interv": 8, "health_check_neo4j": 8, "health_check_redi": 8, "health_check_timeout": 8, "healthcar": [7, 16, 24, 26], "heat": [6, 20, 21], "heavili": 14, "help": 9, "here": [10, 11, 17, 19], "hierarch": [2, 8], "hierarchi": [0, 2, 18], "high": [3, 5, 10, 11, 12, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "higher": [10, 12, 19], "highest": [8, 23, 25], "highlight": [2, 15, 25], "hint": 22, "hipaa": [16, 19, 24, 26], "hipaa_impact": 26, "histor": [15, 24, 26], "histori": [0, 15], "hit": [12, 15], "hit_perc": 14, "hole": 23, "holist": 24, "hollow": 23, "hop": [3, 11, 16, 17, 19, 20, 23, 24, 26], "horizont": [15, 16], "hospit": 26, "host": [0, 1, 8, 10, 14, 23], "hour": [6, 7, 8, 12, 19, 20, 26], "hourli": 26, "housekeep": 18, "how": [19, 20], "hr": 18, "hs256": [8, 10], "html": [0, 2], "htop": 1, "http": [1, 2, 3, 4, 6, 7, 8, 10, 11, 17, 19, 20, 21, 26], "httponli": 4, "hub": 22, "human": [22, 23, 24], "hunt": [16, 20, 24], "hybrid": [15, 24], "hypothes": [22, 25], "hypothesi": [22, 25], "i": [0, 1, 3, 5, 7, 8, 9, 10, 11, 12, 14, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "iam": [17, 18], "iat": 4, "ic": [6, 20, 21], "icon": 11, "ics_count": 21, "id": [1, 3, 4, 5, 6, 8, 10, 12, 14, 15, 17, 23], "idea": 20, "ident": [8, 10, 24], "identif": [6, 7, 15, 16, 19, 20, 21, 22, 23, 24, 26], "identifi": [2, 5, 6, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "identity_provid": 14, "ideologi": 26, "ids_ip": 14, "idx_asset_relationships_act": 14, "idx_asset_relationships_sourc": 14, "idx_asset_relationships_target": 14, "idx_asset_relationships_typ": 14, "idx_assets_config_gin": 14, "idx_assets_created_at": 14, "idx_assets_environment_statu": 14, "idx_assets_properties_gin": 14, "idx_assets_risk_scor": 14, "idx_assets_soft_delet": 14, "idx_assets_type_env_statu": 14, "idx_assets_type_provid": 14, "idx_attack_paths_created_at": 14, "idx_attack_paths_crit": 14, "idx_attack_paths_nodes_gin": 14, "idx_attack_paths_path_typ": 14, "idx_attack_paths_risk_scor": 14, "idx_attack_paths_source_risk_crit": 14, "idx_attack_paths_source_target": 14, "idx_attack_paths_techniques_gin": 14, "idx_attack_scenarios_entry_points_gin": 14, "idx_attack_scenarios_objectives_gin": 14, "idx_relationships_source_type_act": 14, "ii": [16, 17], "illustr": 13, "imag": [2, 4, 11, 17], "immedi": [0, 10, 17, 19, 25], "impact": [3, 5, 9, 12, 15, 16, 17, 19, 20, 22, 23, 24, 25], "impact_by_degre": [3, 12], "impact_scor": [3, 14], "implant": 23, "implement": [0, 1, 4, 5, 8, 11, 12, 14, 15, 16, 18, 19, 20, 22, 25, 26], "implementation_dai": 26, "implementation_timelin": 26, "implic": [19, 20, 23, 25, 26], "import": [1, 2, 3, 4, 5, 11, 17, 18, 19, 21, 26], "improv": [0, 5, 12, 15, 16, 17, 19, 20, 23, 24, 25, 26], "incid": [0, 2, 4, 5, 7, 8, 9, 11, 15, 16, 17, 18, 22, 24], "includ": [1, 2, 3, 4, 5, 7, 8, 11, 12, 14, 15, 16, 17, 19, 21, 22, 26], "include_attack_path": 5, "include_baselin": 21, "include_blast_radiu": 5, "include_campaign": 21, "include_group": 21, "include_histor": 21, "include_insider_threat": 26, "include_legend": 21, "include_ongo": 21, "include_predict": 21, "include_sub_techniqu": 21, "include_techniqu": 21, "incorpor": [17, 22], "incorrect": 8, "increas": [3, 11, 17, 19, 22, 26], "increment": [21, 26], "independ": 15, "index": [0, 2, 8, 9, 15, 16, 17, 18], "indic": [2, 6, 20, 21, 22, 24, 25], "indirect": 26, "indirect_cost": 26, "individu": [1, 11, 12, 17, 22], "industri": [0, 16, 18, 21, 22, 24], "industrial_control": 14, "ineffect": [22, 24], "ineffici": 22, "inet": 14, "info": [8, 10], "inform": [4, 5, 7, 9, 10, 11, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25], "infrastructur": [2, 3, 10, 11, 15, 16, 18, 19, 20, 23, 24, 25, 26], "ingest": [18, 21], "inherit": [14, 18], "initi": [0, 1, 3, 9, 12, 13, 19, 20, 21], "initial_access": [3, 12], "inject": [23, 26], "innov": 16, "input": 5, "insid": [16, 19, 24, 26], "insider_threat": [7, 14], "insight": [22, 23], "instal": [0, 1, 2, 5, 9, 15, 23], "instanc": [5, 8, 10, 11, 19, 21, 26], "instead": [5, 8], "instruct": [0, 1, 2, 17], "insuffici": [5, 17], "insufficient_permiss": 7, "insur": 26, "int": 12, "integ": [3, 5, 14], "integr": [0, 1, 3, 9, 14, 19, 22, 23, 25], "integrate_threat_intellig": 26, "intellig": [0, 3, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25], "intelligentcach": 12, "intens": 19, "inter": 24, "interact": [5, 11, 15, 17, 22, 23, 24, 25], "interfac": [8, 15, 18, 20, 24], "intern": [0, 2, 3, 5, 11, 18, 19, 23, 24, 25, 26], "internal_controls_compromis": 26, "internet": [10, 11, 15, 19, 21, 24], "internet_gatewai": [3, 5, 19], "interpret": [21, 26], "interv": [14, 20], "introduc": 19, "intrus": 24, "invalid": [3, 4, 5, 6, 8, 11], "invalid_paramet": 7, "invalid_threat_actor": 7, "invalid_timefram": 6, "inventori": [5, 11, 14, 16, 17, 20, 23, 24], "invest": [16, 22, 23, 24, 26], "investig": [4, 20, 22], "investigate_incid": [4, 25], "invit": 17, "involv": [20, 26], "ioc": [5, 8, 11, 16, 18, 20, 22, 25], "ioc_auto_correl": 8, "ioc_confidence_threshold": 8, "ioc_correl": 26, "ioc_retention_dai": 8, "iot": 22, "iot_devic": 14, "ip": [8, 21, 23], "ip_address": 14, "is_act": 14, "is_connect": 3, "is_delet": 14, "is_verifi": 14, "iso": [16, 17, 24], "iso27001": 20, "isol": [19, 20, 25], "iss": 4, "issu": [0, 2, 5, 8, 9, 15, 17, 20], "item": [5, 11], "iter": [1, 20], "its": 25, "ivborw0kggoaaaansuheugaa": 4, "j": [1, 2, 10, 17], "javascript": [0, 5, 15, 16], "jbswy3dpehpk3pxp": 4, "jewel": [23, 24], "job": [2, 17, 18], "john": [4, 17, 21], "join": [2, 3, 5, 9, 17, 19, 21], "joint": [16, 19, 20, 23], "json": [3, 4, 5, 6, 7, 8, 10, 12, 16, 17, 19, 21, 26], "jsonb": [14, 16], "justif": 18, "jwt": [2, 3, 5, 8, 10, 16, 19], "jwt_algorithm": [8, 10], "jwt_audienc": 8, "jwt_expire_minut": [8, 10], "jwt_issuer": 8, "jwt_refresh_expire_dai": 8, "jwt_secret_kei": [8, 10], "keep": [0, 1, 8, 19, 20, 25], "keepalive_timeout": 8, "kei": [8, 10, 11, 12, 14, 15, 17, 20, 21], "kerbero": 23, "kernel": 23, "key_manag": 14, "keyword": [0, 21], "kill": [1, 10, 21], "knowledg": [17, 18, 22, 23, 25], "known": [7, 21, 22, 25, 26], "kpi": [15, 24], "kubernet": 15, "kubernetes_pod": 14, "label": [2, 15], "lack": 7, "lambda_funct": 14, "land": 23, "landscap": [20, 21, 22, 24], "languag": [0, 2, 5, 14], "larg": [3, 5, 10, 11, 15, 17, 19, 21, 25], "larger": 11, "last": [10, 17, 21], "last_analysis_tim": 3, "last_login": [4, 14], "last_nam": [4, 14], "last_upd": 21, "latenc": [17, 18], "later": [3, 12, 16, 19, 22, 23, 24, 25], "lateral_mov": [3, 14], "latest": [1, 2, 9, 10, 11, 23, 25], "lax": 8, "layer": [2, 12, 15, 21, 24], "layer_id": 21, "layer_json": 21, "layer_nam": 21, "layout": [24, 25], "ldap": 18, "lead": [22, 24, 25], "leader": 24, "learn": [16, 19, 20, 21, 22, 23, 24, 25], "least": [8, 18, 24], "leef": 17, "legaci": 24, "legal": [18, 23, 25], "legitim": [22, 23, 25, 26], "len": [12, 19, 21], "length": [3, 12, 14, 19, 23], "lesson": [19, 20, 22, 25], "let": 10, "letsencrypt": 10, "level": [8, 11, 12, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26], "leverag": [3, 5, 12, 19, 21, 22, 23, 24, 26], "librari": [9, 15, 22], "licens": 20, "lifecycl": [15, 18, 20, 22], "light": 2, "like": [1, 19, 20], "likelihood": [3, 7, 11, 12, 14, 16, 19, 20, 21, 24, 26], "limit": [2, 8, 9, 15, 16, 17, 18, 19, 23, 24, 25], "line": [11, 17, 23], "linear": 15, "link": [2, 21], "linkcheck": 2, "lint": [1, 15], "linux": [10, 12], "list": [5, 12, 22, 25], "live": [2, 10, 11, 23, 25], "ll": [11, 18, 22, 23, 24, 25], "load": [1, 8, 10, 11, 12, 15, 16, 17, 18, 19, 20, 25], "load_bal": 14, "load_balanc": 14, "local": [0, 10, 11, 15, 17, 18, 23, 26], "localhost": [1, 2, 8, 10, 11, 17], "localstorag": 4, "locat": [11, 17], "lock": [3, 5], "lockout": 18, "locust": 1, "locustfil": 1, "log": [2, 4, 9, 10, 11, 12, 14, 15, 16, 18, 21, 22, 23, 25], "log_backup_count": 8, "log_file_path": 8, "log_format": 8, "log_include_level": 8, "log_include_logger_nam": 8, "log_include_thread_id": 8, "log_include_timestamp": 8, "log_level": [1, 8, 10], "log_max_s": 8, "log_sourc": 21, "logger": 12, "logging_system": 14, "logic": [0, 2, 5], "login": [5, 8, 9, 10, 18, 25], "long": [3, 16, 18, 21, 22, 23], "look": 25, "loop": 12, "loos": 15, "loss": [7, 18, 20, 26], "low": [11, 12, 14, 19, 25], "lower": 19, "lowest": 8, "lru": [12, 16], "lrucach": 12, "lsof": 1, "m": [1, 8, 10, 11, 17, 24], "machin": [16, 20, 21, 22, 24], "maco": [10, 12], "mai": [8, 11, 17], "main": [1, 11, 17, 19], "maintain": [16, 18, 20, 22, 23, 24, 25], "mainten": [15, 16, 20, 22, 23], "maintenance_reindex": 14, "maintenance_vacuum_analyz": 14, "major": [0, 9, 13, 16, 21, 26], "make": [0, 1, 2, 10, 19, 24], "malici": [21, 22, 23, 25], "malwar": 23, "manag": [0, 2, 3, 8, 9, 14, 15, 17, 19], "manage_backup": 18, "manage_compliance_framework": 24, "manage_integr": [4, 18], "manage_rol": 18, "manage_scenario": 22, "manage_us": [4, 18], "mani": [5, 7], "manipul": 23, "manner": 22, "manual": [1, 17, 21, 23], "map": [2, 4, 6, 11, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], "map_attack_path": 12, "market": 16, "markup": 0, "master": [1, 25], "match": [20, 21, 22], "matching_techniqu": 21, "materi": 26, "material_weak": 26, "mathemat": [7, 19, 20, 26], "matur": 24, "max": [5, 11, 19], "max_attack_dur": 26, "max_concurrent_request": 8, "max_concurrent_sess": 8, "max_degre": [3, 5, 12, 16, 19], "max_fin": 26, "max_hop": 17, "max_length": 12, "max_path_length": [3, 5, 16, 19], "max_paths_per_target": [3, 16, 19], "max_request_s": 8, "max_siz": 12, "max_work": 12, "maxdegre": 5, "maxim": 24, "maximum": [3, 8, 19, 20, 22, 24], "mean": [22, 25], "mean_tim": 14, "meaning": [1, 22], "measur": [15, 17, 18, 20, 23, 25], "mechan": [15, 23, 24], "media": 26, "mediat": 22, "medical_devic": 14, "medium": [12, 14, 17, 19, 25, 26], "meet": 11, "member": [0, 2, 9, 11, 16, 17, 18, 25], "memori": [3, 8, 10, 12, 15, 16, 17, 18, 19, 23], "mentor": 24, "menu": [11, 19], "merg": [1, 15], "mermaid": 0, "messag": [1, 3, 4, 5, 6, 7, 8, 11, 16, 17], "met": 16, "metadata": [5, 9, 15, 16, 19, 20], "method": [0, 4, 5, 9, 16, 22, 23], "methodologi": [15, 20], "metric": [3, 5, 8, 11, 12, 15, 18, 20, 24], "metrics_en": 8, "metrics_endpoint": 8, "metrics_include_appl": 8, "metrics_include_system": 8, "mfa": [8, 11], "mfa_backup_codes_count": 8, "mfa_en": [4, 8], "mfa_require_for_admin": 8, "mfa_require_for_al": 8, "mfa_system": 14, "mfa_token": 4, "mfa_totp_digit": 8, "mfa_totp_issu": 8, "mfa_totp_period": 8, "mfatoken": 4, "micro": 24, "microservic": [14, 15], "microsoft": 11, "middlewar": 14, "might": 25, "migrat": [10, 15, 17], "mileston": [22, 23], "militari": 26, "min": 12, "min_confid": 21, "min_duration_dai": 21, "min_fin": 26, "minim": [19, 24], "minimum": [8, 10, 11, 17], "minut": [3, 5, 8, 11, 15, 16, 17], "misconfigur": 23, "misp": 26, "miss": [8, 12, 25], "mission": 15, "misus": 23, "mit": 9, "mitig": [3, 5, 11, 12, 13, 16, 19, 20, 24], "mitigation_cost": [3, 14], "mitigation_strategi": [3, 14, 19], "mitr": [2, 9, 15, 17, 19, 23, 24, 26], "mitre_attack": 21, "mitre_data": 12, "mitre_map": 12, "mitre_techniqu": 26, "mitreattackmap": 12, "mkdir": 10, "mobil": [2, 6, 20, 21], "mobile_count": 21, "mobile_devic": 14, "mock": 5, "mode": 24, "model": [2, 3, 4, 9, 12, 13, 22, 23, 24], "moder": [8, 11, 19, 22], "modern": [11, 17, 24], "modif": [17, 18, 23, 24], "modifi": [4, 12, 17, 18, 22, 24], "modul": [9, 21], "modular": 15, "momentum": 22, "monetari": [7, 20, 26], "monitor": [0, 3, 4, 6, 9, 10, 12, 16, 17, 19, 20, 21, 22, 23, 24], "monitoring_frequ": 26, "monitoring_system": 14, "month": 16, "monthli": 24, "more": [4, 7, 11, 14, 22], "most": [15, 16, 17], "motiv": 26, "move": 23, "movement": [3, 16, 19, 22, 23, 24, 25], "mttd": [22, 25], "mttr": [22, 25], "multi": [2, 3, 8, 9, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24], "multipl": [0, 2, 6, 12, 19, 20, 21, 24, 26], "must": [1, 5, 17], "mutual": 22, "name": [2, 3, 5, 10, 11, 12, 14, 17, 18, 19, 21, 26], "nano": 10, "narr": 22, "nation": 23, "nativ": [15, 16, 24, 26], "navig": [0, 5, 11, 17, 19, 20, 25], "necessari": 24, "need": [0, 2, 3, 5, 10, 18, 19, 20, 22, 25], "neg": 23, "neighbor": 12, "neo4j": [8, 10, 11, 17], "neo4j_connection_acquisition_timeout": 8, "neo4j_databas": 8, "neo4j_max_connection_lifetim": 8, "neo4j_max_connection_pool_s": 8, "neo4j_password": [8, 10], "neo4j_uri": [8, 10], "neo4j_us": [8, 10], "netstat": [10, 11, 17], "network": [3, 8, 10, 11, 12, 17, 18, 19, 20, 21, 22, 23, 24], "network_access": 3, "network_devic": 14, "network_segment": 14, "networkx": [12, 15, 16], "neutral": 22, "never": 5, "new": [4, 5, 10, 11, 14, 18, 19, 20, 22, 24, 25, 26], "new_attack_path": 26, "new_likelihood": 12, "new_password": 4, "newli": 19, "newpassword456": 4, "newsecurepassword123": 17, "next": [5, 9, 12, 21], "next_cursor": 5, "next_level": 12, "nginx": 2, "nist": [16, 24], "node": [1, 3, 9, 10, 11, 12, 15, 16, 17, 19, 25], "non": 15, "none": [4, 8, 12], "notabl": 25, "note": [4, 17, 18, 25], "notic": [11, 17], "notif": [5, 11, 15, 17, 18, 22, 25, 26], "notifi": 25, "notification_timelin": 26, "now": [0, 8, 10, 11], "npm": [1, 5, 10], "null": [3, 8, 14], "nullif": 14, "number": [5, 8, 17, 19, 22, 23, 25, 26], "number_of_compon": 3, "nx": 12, "o": 26, "obfusc": 23, "object": [3, 5, 12, 14, 16, 18, 19, 20, 22, 23, 24], "observ": [15, 22, 23], "obsolet": 19, "obtain": [4, 5], "occur": 25, "ocr": 26, "ocr_reporting_requir": 26, "off": [19, 23], "offens": [20, 22, 23], "offici": [5, 6, 15, 16, 21], "offlin": 17, "oidc": 4, "ok": [4, 5], "old": 14, "oldpassword123": 4, "onboard": 2, "one": 10, "ongo": [0, 7, 18, 20, 21, 22, 25], "onli": [1, 2, 8, 9, 17, 23, 26], "open": [11, 17], "openapi": [8, 15, 16, 17], "openapi_url": 8, "oper": [0, 2, 3, 5, 9, 10, 11, 12, 16, 17, 19, 24], "operating_system": 14, "opportun": [18, 22, 23, 24], "opt": 10, "optim": [0, 2, 5, 9, 10, 15, 16, 17, 20, 22, 23, 24], "option": [0, 2, 3, 4, 8, 12, 15, 16, 21, 25, 26], "orchestr": [15, 24], "order": [8, 14], "organ": [2, 5, 18, 19, 21, 22, 23, 24, 25], "organiz": [18, 22, 23, 24, 25], "organization_ev": 21, "origin": [1, 8, 17, 26], "ot": 22, "other": [2, 17, 21, 22, 24, 25], "our": [1, 9, 10, 17, 20], "out": [1, 4, 17, 25], "outbound": [14, 25], "outdat": [2, 22], "outlin": [16, 22], "outstand": 24, "over": [6, 19, 21, 22, 26], "overal": [22, 24, 26], "overflow": 17, "overlap": 22, "overnight": [24, 25], "overview": 2, "ownership": 18, "oxlei": 26, "p": [1, 10, 11, 17], "p0": 16, "p1": 16, "packag": 24, "page": [0, 2, 5, 9, 11], "pagin": 9, "pan": [11, 25], "parallel": [15, 16, 21, 26], "parallelanalyz": 12, "paramet": [3, 5, 17, 18, 19, 26], "parent_process": 21, "pars": [8, 18, 20], "parti": [5, 15, 19, 22, 23, 24], "particip": [15, 18, 20, 22, 24, 25], "pass": [1, 10], "passiv": [20, 23], "password": [5, 8, 10, 18, 23], "password_hash": 14, "password_hash_algorithm": 8, "password_hash_round": 8, "patch": [5, 24], "patch_manag": 14, "path": [0, 2, 4, 8, 9, 10, 15, 18, 22, 24, 26], "path_001": [3, 5], "path_001_web_server_001_database_001": 3, "path_cach": 12, "path_edg": [12, 14], "path_id": [3, 5, 14, 16], "path_length": [3, 14], "path_nod": [3, 12, 14, 19], "path_typ": [3, 14], "path_type_enum": 14, "pathfind": 15, "paths_respons": 5, "patient": 26, "patient_notification_requir": 26, "patient_record": 26, "pattern": [0, 4, 5, 15, 18, 20, 22, 23, 24, 25], "payload": [5, 23], "payment": [16, 24], "payment_system": 26, "pci": [16, 20, 24], "pdb": 1, "pdf": [2, 11], "peaceiri": 2, "peak": 19, "peer": [2, 15, 22, 23, 24], "pem": [8, 10], "penalti": [7, 12, 26], "penetr": [0, 1, 2, 9, 15, 16, 17, 18, 20], "per": [3, 5, 6, 7, 8, 15, 16, 19, 25], "percentag": [22, 23, 24, 25], "perform": [0, 2, 5, 6, 7, 20, 23, 24, 25], "performance_metr": 3, "period": [3, 17, 18, 19, 25], "permiss": [5, 7, 8, 15, 16, 17, 19, 20, 21, 26], "persist": [3, 19, 22, 25, 26], "persistence_level": 26, "person": 15, "persona": [20, 22], "personal_data": 26, "personnel": 20, "perspect": 22, "pg_dump": 17, "pg_size_pretti": 14, "pg_stat_act": 11, "pg_stat_stat": 14, "pg_tabl": 14, "pg_total_relation_s": 14, "pgadmin": 1, "phase": [20, 22, 23, 24], "phi_records_affect": 26, "phish": [23, 26], "phone_system": 14, "physic": 23, "pid": [1, 10], "pii": [19, 26], "ping": 10, "pip": [1, 2, 5], "pipelin": [15, 24], "placement": [16, 20, 24], "plai": [22, 23, 24, 25], "plan": [16, 17, 18, 19, 20, 22, 26], "plan_campaign": 23, "platform": [0, 5, 8, 9, 10, 12, 15, 20, 21, 22, 23, 24, 25], "playbook": [16, 20, 22], "pleas": [9, 17], "plpgsql": 14, "plugin": 15, "png": 4, "point": [15, 18, 19, 20, 23, 24], "polici": [4, 9, 14, 15, 17, 18, 23], "poll": [5, 8], "pool": [8, 18], "pop": 12, "popul": 19, "popular": [2, 5], "port": [2, 8, 10, 11, 14, 17], "portabl": 26, "portain": 1, "portal": 20, "portfolio": 24, "posit": [21, 22, 23, 24, 25], "possibl": [3, 19, 21], "post": [3, 4, 5, 8, 16, 17, 19, 21, 23, 24, 25, 26], "postgr": [11, 17], "postgresql": [1, 8, 10, 14, 16, 17], "postman": [1, 5], "postur": [11, 16, 17, 19, 20, 22, 23, 24, 25, 26], "potenti": [7, 11, 17, 19, 20, 22, 23, 24, 25, 26], "potential_penalti": 26, "power": [6, 11, 16, 17, 19, 20, 21, 22, 23, 24, 25], "powershel": [21, 23, 26], "powersourc": 26, "practic": [2, 9, 10, 11, 15], "practition": [22, 23], "prd": [2, 15], "pre": [17, 19, 20, 21, 22, 23], "predecessor": 12, "predefin": 18, "predict": [16, 19, 20, 21, 24], "predicted_count": 21, "prefer": [1, 8, 11, 18, 25, 26], "preferred_techniqu": 26, "premis": [11, 15, 24], "premium": [3, 5, 6, 7], "prepar": [10, 19, 22, 23, 24], "prerequisit": 9, "present": 17, "preserv": 25, "prevent": [3, 8, 15, 18, 24, 25], "previous": 7, "primari": [8, 14, 16, 21, 26], "primarili": [18, 22, 23, 24, 25], "primary_motiv": 26, "primary_techniqu": 21, "princip": 18, "principl": [15, 18, 24], "print": [2, 3, 19, 21, 26], "printer": 14, "priorit": [8, 11, 16, 18, 19, 20, 22, 23, 24, 25], "prioriti": [8, 11, 16, 19, 20, 25, 26], "privaci": [16, 24], "privat": 4, "privileg": [3, 8, 11, 16, 18, 19, 22, 24], "privilege_escal": 14, "privileged_access": 14, "pro": 11, "proactiv": [9, 15, 16, 17, 18, 19, 22, 25], "probabl": [7, 9, 11, 12, 16, 19, 20, 23, 26], "problem": [0, 2, 18, 20], "procedur": [0, 11, 14, 15, 16, 17, 18, 20, 21, 22, 23], "proceed": 25, "process": [0, 1, 2, 8, 10, 13, 15, 17, 18, 19, 21, 22, 23, 24, 25, 26], "process_execut": 21, "process_nam": 21, "procur": 18, "prod": 10, "product": [0, 4, 5, 9, 11, 17, 19], "profession": [0, 10, 11, 15, 16, 17, 18, 20, 22], "profici": [20, 25], "profil": [3, 5, 7, 9, 16, 17, 19, 20, 21, 22], "program": [0, 5, 15, 16, 18, 20, 22, 25], "programmat": 5, "progress": [6, 19, 20, 22, 23], "project": [0, 8, 9, 15], "project_nam": [8, 10], "prometheu": 8, "prometheus_en": 8, "prometheus_namespac": 8, "prometheus_port": 8, "promot": 22, "prompt": [11, 25], "proof": 23, "propag": [12, 15, 16, 19], "propagation_prob": 12, "proper": [0, 4, 5, 8, 9, 11, 19, 20, 24, 25], "properli": [18, 23], "properti": [14, 15], "propos": [19, 20, 24], "proposit": 15, "protect": [0, 9, 14, 15, 18, 20, 24, 25, 26], "protocol": [14, 18, 22, 23, 24], "provid": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "provider_enum": 14, "provis": 18, "proxi": 14, "prune": [1, 11, 12], "psql": 10, "public": [14, 15, 19, 20, 26], "publish_dir": 2, "pull": [1, 2, 15, 17], "pull_request": 2, "purpl": [0, 2, 9, 11, 16, 17, 18], "purpos": [4, 18], "pursu": [22, 23, 24], "push": [1, 2], "put": [5, 8], "py": [1, 2], "pydant": 8, "pytest": 1, "python": [0, 1, 2, 3, 5, 8, 10, 11, 15, 16, 17, 21, 26], "python3": 10, "q4": [21, 26], "qr": [11, 17], "qr_code": 4, "qualit": 24, "qualiti": [22, 23], "quantif": 20, "quantifi": [19, 20], "quantit": [5, 7, 9, 19, 20, 24], "quarterli": [21, 24, 26], "queri": [2, 3, 5, 8, 14, 15, 16, 17, 18], "question": [0, 1, 9, 20], "queue": [12, 25], "quick": 0, "quickest": 17, "quickli": 11, "quota": 18, "r": [1, 2, 10], "radiu": [1, 4, 5, 6, 7, 8, 10, 11, 14, 15, 18, 20, 21, 22, 23, 24, 25, 26], "raise_for_statu": 4, "ram": [10, 11, 17], "rang": [6, 12, 26], "rapid": 16, "rapidli": 19, "rate": [2, 8, 9, 15, 16, 17, 18, 21, 22, 23, 24, 25, 26], "rate_limit_burst": 8, "rate_limit_en": 8, "rate_limit_exceed": 7, "rate_limit_request": 8, "rate_limit_requests_per_minut": 8, "rate_limit_storag": 8, "rate_limit_window": 8, "ratelimit": [3, 5, 7], "rational": 20, "rbac": [4, 15, 16], "re": [11, 19], "reach": [1, 17, 23, 25], "read": [0, 10, 17], "reader": 17, "readi": [0, 9, 11, 17, 18, 24], "real": [0, 2, 5, 6, 9, 10, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24], "realism": 22, "realist": [5, 16, 19, 20, 22, 23, 26], "rebuild": 1, "recent": 11, "recept": 25, "recognit": [6, 20], "recommend": [0, 3, 7, 15, 16, 17, 19, 20, 22, 23, 24], "reconnaiss": 23, "record": [16, 20, 22, 25], "recover": 15, "recoveri": [7, 11, 15, 16, 19, 20, 22, 24, 25, 26], "recovery_time_estim": [3, 19], "red": [0, 2, 9, 11, 16, 17, 18, 21, 22], "redi": [8, 10, 11, 12, 17], "redirect": 11, "redis_cli": 12, "redis_pool_max_connect": 8, "redis_pool_s": [8, 10], "redis_retry_on_timeout": 8, "redis_socket_connect_timeout": 8, "redis_socket_timeout": 8, "redis_url": [8, 10], "redoc": 8, "redoc_url": 8, "reduc": [5, 16, 19, 21, 22], "reduct": [16, 22, 24, 26], "redund": [15, 22, 24], "ref": 2, "refer": [0, 14, 15, 16, 19, 20, 21, 24, 26], "refin": [21, 22], "refresh": 19, "refresh_token": 4, "refus": 11, "region": 17, "registri": 23, "regress": 15, "regul": 26, "regular": [0, 4, 15, 16, 17, 18, 19, 20, 22, 24, 25, 26], "regularli": [8, 18, 20, 21, 22, 24], "regulatori": [5, 7, 14, 15, 16, 18, 19, 20, 23, 24, 25], "regulatory_context": 26, "regulatory_fin": 26, "regulatory_requir": 26, "reindex": 14, "rel": [6, 7], "rel_data": 12, "relat": [0, 2, 3, 4, 8, 21], "related_techniqu": 21, "relationship": [2, 11, 12, 15, 16, 17, 19, 20, 22, 23, 24, 25, 26], "relationship_typ": 14, "relationship_type_enum": 14, "relationship_weight": 12, "relax": 8, "releas": [2, 15, 16, 17, 18], "relev": [1, 22, 24, 25], "reliabl": [15, 18, 22, 23], "reload": 2, "remain": [3, 5, 7, 22], "remedi": [11, 17, 18, 19, 20, 23, 24], "rememb": 23, "remember_m": 4, "remot": 3, "remov": [19, 25], "repeat": [3, 19, 26], "replac": 14, "replic": [14, 22], "report": [0, 2, 4, 5, 9, 11, 15, 16, 17, 18, 19, 20, 21, 26], "report_respons": 5, "repositori": [2, 6, 9, 10, 11, 15, 17, 21, 22], "repres": [22, 25], "represent": [15, 19], "reproduc": 17, "reproduct": 23, "req_1639234567": 5, "request": [2, 3, 4, 5, 6, 7, 8, 9, 13, 15, 16, 17, 19, 20], "request_id": 5, "request_timeout": 8, "requir": [0, 2, 3, 4, 5, 6, 7, 8, 11, 18, 19, 20, 22, 23, 24, 25, 26], "required_privileg": [3, 14], "required_resourc": [3, 14], "rerun": 19, "research": 22, "reset": [1, 3, 5, 7, 11, 18], "residu": 24, "resili": 15, "resolut": [0, 15, 22], "resolv": 25, "resourc": [8, 9, 16, 17, 18, 19, 22, 23, 24, 25, 26], "resource_level": 26, "respect": 22, "respond": [19, 22, 24, 25], "respons": [0, 4, 7, 8, 9, 11, 12, 15, 16, 17, 18, 23, 24, 26], "rest": [2, 4, 5, 15, 16, 17, 18, 26], "restart": [1, 8, 11, 17], "restaur": 26, "restor": [18, 25], "restrict": [17, 18, 23, 25], "restructuredtext": 2, "result": [2, 4, 5, 12, 16, 17, 18, 19, 22, 24, 25, 26], "retail": 26, "retent": [9, 15, 17, 18], "retir": 22, "retri": 5, "retriev": [3, 5, 6, 7, 18], "return": [3, 4, 6, 7, 10, 12, 14, 16], "reveng": 26, "review": [0, 2, 10, 11, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "right": [11, 16, 26], "rigor": 1, "risk": [0, 2, 3, 9, 11, 15, 18, 20, 21, 22, 23, 25], "risk_level": [5, 21], "risk_reduct": 26, "risk_scor": [3, 5, 12, 14, 19], "risk_score_increas": 26, "risk_toler": 26, "rm": 2, "roadmap": [0, 15, 22, 24], "robust": [9, 12, 14, 15], "roi": [16, 22, 24, 26], "role": [2, 4, 9, 10, 14, 15, 16, 17, 21, 22, 23, 24, 25, 26], "roll": 18, "rollback": 18, "root": [22, 25], "rootkit": 23, "rotat": [4, 8, 18], "rout": [14, 17, 19, 20, 23, 24, 25], "router": 14, "routin": 18, "row": 14, "rpo": 18, "rst": [0, 2], "rsync": 2, "rto": 18, "rule": [15, 16, 18, 21, 22, 23, 25], "run": [2, 3, 7, 10, 15, 16, 19, 21, 23], "run_in_executor": 12, "runtim": 8, "russia": 26, "safari": [11, 17], "safeti": [1, 22], "sale": 20, "same": [1, 2, 3], "saml": 4, "sampl": 25, "sandbox": [5, 14], "sarban": 26, "satisfact": [2, 16], "save": [11, 17, 19], "scalabl": [12, 14, 15, 18, 24], "scale": [0, 2, 3, 10, 14, 15, 16, 18, 20, 21, 24], "scan": [5, 11, 15, 17, 18, 20, 24], "scanner": [14, 18, 23], "scenario": [0, 4, 5, 9, 10, 12, 14, 15, 20, 26], "scenario_1639234567": 3, "scenario_id": [3, 14], "scenario_nam": [3, 16, 19, 26], "scenarionam": 5, "schedul": [14, 18, 23, 24], "schema": [2, 9], "schemanam": 14, "scope": [17, 19, 20, 22, 23, 25], "score": [2, 3, 6, 9, 11, 15, 16, 18, 20, 21, 24, 26], "scorecard": 24, "screenshot": [0, 23], "script": [1, 2, 10, 15, 21, 23, 26], "sdk": [6, 7, 9, 15, 16, 21, 26], "seamless": 12, "search": [0, 5, 9, 10, 11, 12, 19, 25], "second": [3, 6, 8, 9, 11, 15, 16, 17, 19, 20, 21], "secondari": 16, "secret": [2, 4, 5, 8, 10, 17], "secret_kei": [8, 10], "secrets_manag": 14, "section": [1, 5, 15, 19, 20], "sector": [21, 26], "secur": [3, 5, 7, 10, 11, 12, 14, 21, 25], "secure_password": 5, "securepassword123": [4, 10], "security_analyst": 5, "security_architect": 4, "security_assess": 5, "security_controls_effect": 26, "security_devic": 14, "security_factor": 12, "see": [2, 4, 6, 7, 8, 9, 11, 21, 26], "seen": 21, "segment": [3, 11, 12, 17, 18, 19, 23, 24], "segreg": 24, "select": [9, 10, 11, 14, 19, 22, 23, 24, 26], "self": [4, 12, 16], "send": [5, 17], "senior": 25, "sensit": [16, 19, 22, 23, 26], "sensitive_personal_data": 26, "separ": [3, 16, 19, 24], "sequenc": [19, 20, 21, 25], "serial": 15, "serv": [2, 20], "server": [0, 2, 3, 4, 5, 8, 10, 11, 14, 17, 18, 19], "servic": [1, 5, 8, 10, 11, 15, 16, 17, 18, 19, 22, 23, 24, 25], "service_disruption_scor": 3, "servicenow": [10, 17], "servicenow_api_vers": [8, 10], "servicenow_batch_s": 8, "servicenow_create_incid": 8, "servicenow_incident_categori": 8, "servicenow_incident_subcategori": 8, "servicenow_inst": [8, 10], "servicenow_password": [8, 10], "servicenow_sync_en": 8, "servicenow_sync_interv": 8, "servicenow_timeout": 8, "servicenow_usernam": [8, 10], "sess_123456789": 4, "session": [4, 8, 10, 12, 18, 19, 22], "session_cookie_httponli": [8, 10], "session_cookie_max_ag": 8, "session_cookie_nam": 8, "session_cookie_samesit": [8, 10], "session_cookie_secur": [8, 10], "session_id": 4, "session_invalidate_on_logout": 8, "session_regenerate_on_login": 8, "session_ttl": 8, "set": [0, 1, 4, 10, 11, 12, 15, 17, 19, 20, 22, 23, 24, 25, 26], "set_trac": 1, "setex": 12, "setitem": 4, "setup": [0, 2, 9, 15, 20], "setup_continuous_monitor": 26, "sever": [5, 11, 25], "sh": [1, 2], "sha256": 5, "shall": 16, "share": [8, 16, 17, 18, 19, 20, 22, 23, 25], "shared_blks_hit": 14, "shared_blks_read": 14, "shell": [10, 23], "shorter": 19, "shortest": 19, "should": [1, 11], "show": [2, 11, 13, 25], "side": 5, "side_by_sid": 21, "siem": [5, 14, 15, 18, 20, 21, 22, 24], "sign": [4, 11, 18, 25], "signatur": [5, 22], "signific": [1, 25], "sim_001": 26, "similar": [21, 26], "simpl": 19, "simul": [0, 2, 5, 9, 11, 16, 17, 18, 19, 20, 22], "simulate_attack": [23, 26], "simulate_attack_custom": 26, "simulation_id": 26, "simulation_in_progress": 7, "simulation_paramet": 26, "simultan": [15, 16, 24], "singl": [4, 6, 15, 18], "situat": [20, 24, 25], "six": 19, "size": [2, 5, 14], "size_byt": 14, "skill": 20, "skip": 1, "sla": 15, "slow": [11, 14, 25], "slow_queri": 14, "small": [3, 11, 15, 17], "smaller": 3, "smart": 2, "soar": [5, 14, 15, 20], "soc": [0, 2, 9, 11, 16, 17, 18, 22, 24], "soc2": 20, "soc_oper": [4, 17], "social": [22, 23, 24], "sofaci": 26, "soft": [9, 14, 15, 16], "soft_delete_asset": 14, "softwar": [0, 14, 17, 18, 21, 23], "solid": 0, "solut": [0, 1, 9, 11, 15, 17], "some": [8, 17], "sophist": [19, 21, 23, 25, 26], "sophistication_level": 26, "sort": [9, 12], "sourc": [1, 2, 3, 10, 11, 12, 15, 16, 17, 19, 20, 21, 24, 25], "source_asset_id": [3, 5, 14, 16, 19], "sourceassetid": 5, "sox": [3, 16, 19, 26], "sox_impact": 26, "space": [10, 11], "span": 24, "spear": 23, "special": [20, 23], "specif": [0, 2, 3, 4, 5, 6, 7, 9, 11, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26], "specifi": [6, 7, 26], "speed": 22, "spell": 2, "sphinx": [0, 2], "spot": 20, "spread": 25, "sql": [8, 17], "ssd": 10, "ssl": [8, 10, 11, 18], "ssl_ca_cert_path": 8, "ssl_cert_path": 8, "ssl_key_path": 8, "ssl_verify_mod": 8, "sso": [4, 15, 18], "stack": 17, "stage": [10, 18, 23], "stai": [18, 23, 24, 25], "stakehold": [18, 19, 20, 22, 24, 25, 26], "standalon": 10, "standard": [3, 5, 6, 7, 15, 16, 18, 23, 24, 25], "start": [3, 8, 12, 17, 20], "startup": [8, 23], "stat": [1, 11, 17], "state": [23, 24], "static": [2, 8], "statist": [7, 14, 15, 20, 21, 22, 26], "statu": [1, 3, 4, 7, 10, 11, 14, 16, 18, 21, 24, 25], "stealth": [20, 22], "stealthi": 23, "step": [2, 9, 15, 17, 18, 22, 23, 25], "stix": [6, 8, 9, 16, 17, 18, 20, 21, 26], "stop": 17, "storag": [4, 10, 12, 14, 15, 16, 17, 18, 19], "store": [4, 5, 8, 14, 17, 19, 23], "str": [4, 12], "strateg": [15, 16, 23], "strategi": [5, 14, 15, 16, 18, 19, 20, 22, 24], "stream": 11, "streamlin": 22, "strength": 15, "strengthen": 19, "stress": [1, 15], "strict": [8, 10], "strictli": 23, "strike": 26, "string": [3, 4, 5, 8, 16, 18], "string_to_arrai": 14, "stringifi": 4, "strong": [4, 8, 11, 17, 18, 22, 24], "structur": [8, 12, 16, 18, 19], "studi": 22, "sub": [4, 6, 9, 15, 16, 19, 20, 21], "subject": 26, "submit": [2, 4, 15, 17, 20], "subscript": [8, 17], "subsequ": 4, "success": [7, 9, 10, 15, 18, 19, 20, 22, 23, 24, 26], "success_prob": 26, "successfulli": [0, 3, 4, 5, 11, 23], "successor": 12, "sudo": [1, 10, 17], "suggest": [2, 6, 7, 17, 19, 22], "sum": 12, "summar": 0, "summari": [15, 22, 23, 25], "super": [8, 10], "suppli": [16, 19, 22, 23, 24], "supply_chain": 14, "support": [0, 4, 6, 11, 12, 14, 16, 19, 21, 25], "surfac": 23, "survei": 16, "suspect": 25, "suspici": 4, "sustain": 22, "svg": 21, "swagger": 15, "switch": [2, 14], "symptom": 11, "sync": [8, 21], "synchron": [0, 6, 11, 15, 16, 17, 18], "syntax": [2, 8], "system": [0, 1, 2, 3, 5, 8, 9, 11, 13, 16, 19, 20, 21, 22, 23, 24, 25, 26], "system_mainten": 18, "systemat": [23, 24], "systemctl": 10, "t": 1, "t1003": 21, "t1005": 26, "t1055": [21, 26], "t1059": [21, 26], "t1078": [21, 26], "t1190": 26, "t1566": [6, 21, 26], "t9999": 6, "ta0001": [3, 12], "ta0008": 3, "tabl": [0, 2, 15], "table_s": 14, "tablenam": 14, "tactic": [3, 12, 16, 20, 21, 23, 24], "tag": [15, 17, 18], "tailor": 20, "take": [8, 11, 14, 25], "taken": 25, "tar": 17, "target": [3, 5, 7, 11, 12, 15, 17, 19, 20, 21, 22, 23, 24, 26], "target_asset": [12, 26], "target_asset_id": [3, 5, 14, 16, 19], "target_sector": [21, 26], "task": [3, 11, 12, 16, 18, 23], "taxii": [8, 16, 17, 18, 26], "taxii2": 8, "taxii_collection_id": 8, "taxii_password": 8, "taxii_server_url": 8, "taxii_usernam": 8, "td": 2, "tdd": 1, "team": [0, 1, 2, 9, 10, 11, 15, 16, 17, 18, 24, 25], "teamer": [9, 17], "technic": [11, 12, 17, 18, 19, 20, 22, 25, 26], "techniqu": [3, 5, 12, 13, 14, 15, 16, 19, 20, 25, 26], "technique_id": [6, 12, 21], "technique_map": 12, "technique_nam": 12, "technique_not_found": 6, "technique_upd": 26, "technologi": [16, 26], "templat": [0, 2, 22, 23, 24, 25], "tempor": 21, "temporari": 8, "temporarili": 5, "tenant": [8, 10, 17], "term": [2, 18, 21, 22, 23], "termin": 18, "test": [0, 2, 8, 11, 16, 17, 18, 19, 20, 25], "test_auth": 1, "test_e2": 1, "test_integration_": 1, "test_new_featur": 1, "text": [2, 5, 8, 14], "than": 14, "theft": 23, "thei": [16, 19], "them": 16, "theme": 2, "thi": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "thick": 11, "third": [5, 15, 19, 22, 23, 24], "thread": [12, 15, 16], "threadpoolexecutor": 12, "threat": [0, 3, 9, 13, 15, 17, 18, 19, 24], "threat_actor": [3, 14, 16, 19, 26], "threat_actor_id": 26, "threat_actor_upd": 26, "threat_group": 21, "threat_intel": 26, "threat_intel_en": 8, "threat_intel_update_interv": 8, "threat_intellig": 14, "threat_model": 26, "threatactor": 5, "threshold": [11, 12, 16, 21, 24], "throttl": [15, 16, 18], "through": [3, 4, 5, 8, 10, 13, 15, 16, 17, 19, 22, 23], "throughout": 0, "throughput": [11, 15, 18], "throw": 4, "ticket": 15, "tier": 5, "time": [2, 3, 5, 6, 9, 10, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 26], "timefram": 21, "timelin": [17, 20, 22, 23, 24, 25, 26], "timeout": [4, 11, 18], "timestamp": [3, 5, 14, 21, 23], "tip": [11, 19], "tl": [8, 15, 16, 17, 18], "toc": 2, "token": [2, 3, 5, 6, 7, 17, 19, 21, 23, 26], "token_typ": 4, "toler": [9, 24], "too": [5, 7], "tool": [4, 5, 6, 7, 8, 10, 11, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "top": [1, 11, 21], "top_degree_centr": 3, "top_trend": 21, "topic": 2, "topologi": 17, "total": [19, 26], "total_count": 5, "total_edg": 3, "total_impact": 26, "total_impact_scor": 3, "total_nod": 3, "total_risk_scor": [3, 14, 19], "total_tim": 14, "totp": 4, "tour": 9, "trace": 25, "track": [2, 5, 11, 15, 16, 18, 19, 20, 21, 22, 23, 24, 26], "track_campaign": 21, "traffic": [12, 14, 22, 23, 25], "trail": [9, 14, 15, 16, 18, 24], "train": [15, 16, 18, 19, 22, 23, 24, 25], "transfer": [17, 18, 22, 23, 24], "transform": 24, "transit": [15, 17, 18], "translat": 24, "transmiss": 15, "travers": [15, 17, 23], "treatment": 24, "tree": [2, 15], "trend": [15, 18, 21, 22, 24], "triag": [22, 25], "trigger": [14, 17, 23], "trigger_soft_delete_asset": 14, "troubleshoot": [5, 11, 15, 20], "true": [1, 3, 4, 5, 8, 10, 14, 21, 26], "trust": [14, 15, 16, 23], "try": 12, "ttl": [8, 12, 18], "ttp": [6, 21, 22], "tulpn": [10, 11, 17], "tune": [2, 8, 18, 19, 20, 21, 22, 23], "tunnel": [14, 23], "tupl": 12, "tutori": 0, "txt": 2, "type": [0, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 15, 16, 17, 18, 21, 22, 25, 26], "typic": [3, 15, 16, 17, 19, 20], "u": [8, 10, 17], "ubuntu": [2, 10], "ufw": 17, "ui": 15, "unauthor": [5, 23, 25], "unauthorized_access": 26, "unauthorized_disclosur": 26, "unavail": 5, "under": [3, 5, 9], "understand": [2, 16, 20, 25], "undetect": 23, "unexpect": 19, "unifi": [16, 24], "uniqu": [8, 14, 21], "unit": [1, 15], "unknown": [6, 7, 26], "unknown_apt": 7, "unlock": 18, "unnecessari": 18, "unnest": 14, "unprocess": 5, "unusu": [22, 25], "up": [1, 2, 10, 11, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26], "upcom": 17, "updat": [0, 1, 5, 8, 9, 11, 12, 14, 15, 16, 19, 20, 21, 22, 24, 25, 26], "update_attack_data": 21, "update_frequ": 26, "update_incident_statu": [4, 25], "updated_at": 14, "upgrad": [10, 15, 18], "uptim": 16, "urgent": 25, "url": [9, 17], "us": [1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 14, 15, 16, 18, 20, 21, 22, 23, 25, 26], "usag": [0, 1, 2, 5, 6, 7, 8, 9, 11, 15, 16, 18, 19, 22, 26], "user": [1, 3, 4, 6, 7, 8, 14, 15, 22, 24, 25], "user_role_enum": 14, "useradd": 10, "usermod": 10, "usernam": [4, 5, 10, 14], "users_email_format": 14, "users_username_length": 14, "util": [18, 21, 23], "uuid": 14, "uvicorn": 8, "uvicornwork": 8, "v": [1, 17, 19, 20, 21], "v1": [3, 4, 5, 6, 7, 8, 10, 16, 17, 19, 21, 26], "v3": 2, "v4": 2, "vacuum": 14, "valid": [0, 2, 4, 5, 7, 9, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26], "validate_detect": 22, "validation_error": 5, "valu": [0, 3, 8, 11, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26], "valuabl": 23, "var": [2, 8, 17], "varchar": 14, "variabl": [0, 8], "variou": [3, 6, 17, 22], "ve": 11, "vector": [9, 11, 16, 19, 20, 23, 24, 25], "vehicl": 14, "vendor": [18, 19, 22, 23, 24, 25], "venv": [1, 10], "veri": 26, "verif": [9, 11, 17, 18, 21, 24], "verifi": [1, 10, 11, 15, 17, 18, 19, 20, 25], "version": [0, 2, 8, 10, 15, 16, 18], "vertic": 15, "via": [17, 23], "video": 0, "view": [1, 2, 4, 14, 21, 22, 23, 24, 25], "view_all_asset": 24, "view_architecture_diagram": 24, "view_attack_path": [4, 25], "view_audit_log": [4, 18], "view_security_ev": [4, 25], "viewer": 17, "viewpoint": 22, "violat": [7, 19, 20, 26], "violation_prob": 26, "virtual": [10, 16], "virtual_machin": 14, "viru": 23, "visibl": [15, 16], "vision": 15, "visit": 12, "visual": [0, 2, 4, 5, 6, 13, 15, 16, 17, 19, 20, 22, 23, 24], "visualization_typ": 21, "vital": 22, "void": 14, "volum": 25, "vpc": [17, 18], "vpn": 14, "vpn_connect": 14, "vulner": [1, 9, 15, 16, 17, 18, 19, 20, 24, 25, 26], "vulnerability_scann": 14, "waf": 14, "wai": [10, 17], "wait": 12, "walk": 10, "warn": 8, "water": 23, "we": [11, 20], "weak": [22, 23, 24, 26], "weapon": 23, "web": [2, 10, 20, 23], "web_appl": 26, "web_secur": 14, "web_serv": 2, "web_server_001": [3, 5, 19, 26], "web_servic": 14, "webhook": [9, 15, 17], "week": [21, 26], "weekli": [18, 21, 24], "weight": [12, 15, 16, 17, 19, 20], "weighted_asset_risk": 12, "welcom": [9, 11, 20], "what": [10, 19, 24], "when": [1, 3, 14, 17, 20, 21, 25], "where": [14, 26], "while": [0, 12, 24], "who": [18, 22, 23, 24, 25], "wide": [18, 24], "widget": 25, "window": [10, 12, 18], "winrar": 26, "winword": 21, "wireless_access_point": 14, "within": [3, 16, 20, 23, 24], "without": [1, 8, 15, 23], "wizard": 11, "work": [2, 11, 15, 22, 23, 24], "worker": [8, 10, 16], "worker_class": 8, "workflow": [0, 2, 5, 9, 13, 15, 16, 17, 22, 23], "workshop": 22, "workspac": 22, "workstat": [14, 19, 25], "world": 0, "write": [1, 15], "www": 2, "x": [1, 3, 5, 7, 21, 26], "yarn": 10, "ye": [2, 17], "yellow": 11, "yml": [10, 17], "you": [9, 10, 11, 18, 19, 21, 22, 23, 24, 25, 26], "your": [2, 3, 5, 8, 9, 10, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "your_api_token": [6, 7], "yourapp": 8, "yourcompani": [10, 17], "z": 14, "z0": 14, "za": 14, "zebroci": 26, "zero": [1, 15, 16, 23], "zero_trust": 14, "zip": 12, "zone": [11, 14, 18], "zoom": [11, 25]}, "titles": ["Blast-Radius Security Tool - Complete User Guide Documentation", "Local Development &amp; CI/CD Guide", "Blast-Radius Security Tool Documentation", "Attack Path Analysis API", "Authentication API", "API Reference", "MITRE ATT&amp;CK Integration API Reference", "Threat Modeling API Reference", "Configuration Guide", "Blast-Radius Security Tool Documentation", "Installation Guide", "Quick Start Guide", "Attack Path Analysis Architecture", "Attack Path Analysis Flows", "Database Design and Schema", "Technical Documentation", "Product Requirements Document (PRD)", "Frequently Asked Questions (FAQ)", "Administrators Guide", "Attack Path Analysis User Guide", "User Guides", "MITRE ATT&amp;CK Integration User Guide", "Purple Team Members Guide", "Red Team Members Guide", "Security Architects Guide", "SOC Operators Guide", "Threat Modeling User Guide"], "titleterms": {"": 17, "001": 16, "002": 16, "003": 16, "004": 16, "005": 16, "1": [10, 11, 22, 23, 24, 25], "2": [10, 11, 16, 22, 23, 24, 25], "2025": 16, "3": [10, 11, 16, 22, 23, 24, 25], "4": 16, "One": 1, "access": [8, 11, 17, 18], "account": [18, 25], "accuraci": 21, "acquisit": 24, "action": 11, "activ": 25, "actor": [6, 7, 21, 26], "ad": 2, "add": [11, 17], "addit": [0, 1], "admin": [10, 17], "administr": [18, 20], "adopt": 16, "advanc": [11, 16, 21, 22, 23, 24, 26], "adversari": 22, "ai": 26, "air": 17, "alert": 25, "algorithm": 12, "am": 17, "an": 17, "analysi": [3, 5, 6, 7, 11, 12, 13, 14, 15, 17, 19, 20, 21, 23, 24, 26], "analyt": [2, 16, 21, 24], "analyz": [3, 6], "ani": 17, "api": [0, 2, 3, 4, 5, 6, 7, 9, 15, 16, 17, 19, 21], "applic": [1, 8], "apt": [22, 23], "ar": 17, "architect": [20, 24], "architectur": [12, 14, 15, 16, 24], "archiv": 14, "ask": 17, "assess": [5, 7, 12, 16, 19, 20, 24, 26], "asset": [5, 11, 15, 16, 20], "assur": [2, 9, 15], "att": [3, 5, 6, 12, 13, 16, 20, 21], "attack": [3, 5, 6, 7, 11, 12, 13, 14, 16, 17, 19, 20, 21, 23, 24, 25, 26], "attackpathanalyz": 12, "attribut": [6, 21], "audit": 17, "authent": [3, 4, 5, 6, 7, 8, 11, 17], "author": 4, "autom": [1, 16, 17, 21], "automat": 21, "backend": 1, "backup": [17, 18], "base": [3, 5, 6, 7, 18, 20], "basic": [2, 11, 19, 21, 26], "batch": 6, "behavior": 21, "best": [1, 3, 4, 5, 8, 18, 19, 20, 21, 22, 23, 24, 25, 26], "blast": [0, 2, 3, 9, 12, 13, 16, 17, 19], "blue": 22, "blueprint": 24, "build": [0, 1, 2], "busi": 16, "cach": [3, 12, 13], "calcul": [3, 13, 16, 19, 26], "campaign": [6, 23], "can": [11, 17], "capabl": 23, "case": [0, 9, 19, 24], "categori": 1, "cd": 1, "certif": 20, "challeng": [22, 23, 24], "chang": [4, 11], "check": [1, 10], "ci": 1, "ck": [3, 5, 6, 12, 13, 16, 20, 21], "clear": 3, "cloud": [8, 11, 16, 17, 24], "code": [2, 5, 11], "collabor": [19, 22, 23], "command": [1, 2], "common": [1, 5, 10, 11, 18, 19, 20, 22, 23, 24, 25], "commun": [17, 22, 24], "complet": 0, "complianc": [0, 7, 9, 15, 16, 17, 18, 24, 26], "compon": 12, "comprehens": [0, 24], "compromis": 25, "concept": 19, "conclus": [0, 18, 19, 22, 23, 24, 25], "configur": [1, 8, 10, 11, 17, 18, 19], "conflict": 1, "congratul": 11, "consider": 3, "contain": 1, "content": 0, "context": 6, "continu": [2, 7, 26], "contribut": [2, 15, 17, 20], "control": [8, 18, 24], "coordin": 22, "core": [8, 12, 14, 16, 18, 19], "correl": [6, 21], "coverag": 0, "cr": 16, "creat": [3, 10], "creation": [13, 19], "credenti": 17, "cross": 2, "custom": [15, 17], "cycl": 1, "daili": [1, 25], "dashboard": [5, 11, 18, 22, 23, 24, 25], "data": [3, 6, 14, 15, 16, 17, 21], "databas": [1, 8, 14, 15, 16], "debug": 1, "decis": 13, "default": 17, "definit": 14, "depend": 10, "deploy": [0, 2, 15], "design": [2, 14, 15, 24], "detect": [22, 25], "develop": [1, 2, 5, 8, 9, 10, 15, 22], "diagram": [2, 14], "disast": 18, "discoveri": [12, 13, 16, 19, 20, 23], "do": 17, "doc": 9, "docker": [2, 10], "document": [0, 1, 2, 9, 15, 16, 22, 23, 25], "doe": 17, "driven": 26, "element": 2, "emul": 22, "enabl": [1, 4, 17], "encrypt": 8, "end": 1, "endpoint": [3, 4, 5], "engin": [15, 16, 21], "enhanc": [21, 22], "enrich": [6, 21], "entiti": 14, "enumer": 14, "environ": [1, 8, 10, 15, 17], "error": [3, 5, 6, 7, 13], "escal": [23, 25], "essenti": 11, "evas": 23, "event": [6, 21], "exampl": [2, 3, 4, 5, 19], "excel": [18, 22, 23, 24, 25], "execut": [16, 24], "exercis": [19, 20, 22], "exploit": 23, "export": [3, 6], "extern": 23, "facilit": 22, "factor": [4, 11, 12, 17], "faq": 17, "featur": [0, 1, 2, 9, 11, 17, 18, 20, 22, 23, 24, 25, 26], "feedback": 20, "field": 5, "file": 0, "filter": 5, "financi": 7, "first": [1, 11], "flow": [4, 13], "focu": 0, "format": [0, 5], "forum": 17, "fr": 16, "framework": [12, 15, 16, 24], "frequent": 17, "friendli": 0, "frontend": 1, "full": 1, "function": 16, "futur": [0, 16, 17], "gap": 17, "gdpr": 7, "gener": [6, 7, 17, 20, 23, 26], "get": [0, 2, 3, 6, 7, 9, 10, 11, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "govern": 24, "graph": [3, 12, 15, 16], "graphengin": 12, "guid": [0, 1, 2, 8, 9, 10, 11, 18, 19, 20, 21, 22, 23, 24, 25, 26], "guidelin": [15, 19, 20], "handl": [6, 7, 13], "health": [1, 10], "help": [2, 10, 11, 17, 18, 19, 20, 22, 23, 24, 25], "hipaa": 7, "how": 17, "http": 5, "hunt": [22, 25], "i": 17, "imag": 1, "immedi": 11, "impact": [7, 26], "implement": 24, "improv": 22, "incid": [19, 20, 25], "index": 14, "indic": 9, "inform": 6, "initi": [10, 11, 18, 22, 23, 24, 25], "insid": [22, 23], "instal": [10, 11, 17], "integr": [2, 4, 5, 6, 8, 10, 11, 12, 15, 16, 17, 18, 20, 21, 24, 26], "intellig": [5, 6, 8, 11, 16, 20, 21, 26], "interact": [1, 2], "interfac": [11, 17, 19], "interpret": 19, "investig": 25, "ioc": [6, 21], "ir": 16, "issu": [1, 10, 11, 18, 19, 25], "javascript": 4, "jwt": 4, "kei": [0, 9, 18, 22, 23, 24, 25], "kpi": [16, 22, 23, 25], "layer": 6, "leadership": [22, 24], "learn": 11, "librari": 5, "licens": 9, "lifecycl": 23, "limit": [3, 5, 6, 7], "list": 7, "load": 26, "local": [1, 2], "log": [1, 8, 17], "login": [4, 11, 17], "logout": 4, "long": 17, "mainten": [0, 14, 18], "malwar": 25, "manag": [1, 4, 5, 6, 11, 16, 18, 20, 21, 22, 23, 24, 25], "map": [3, 12, 13], "measur": 22, "member": [20, 22, 23], "merger": 24, "mermaid": 2, "method": 10, "methodologi": [12, 19, 22, 23, 26], "metric": [0, 1, 2, 16, 22, 23, 25], "mfa": [4, 17], "migrat": 24, "mission": 16, "mitig": [7, 26], "mitr": [3, 5, 6, 12, 13, 16, 20, 21], "mode": 1, "model": [5, 7, 15, 16, 19, 20, 26], "monitor": [1, 2, 5, 7, 8, 11, 14, 15, 18, 25, 26], "morn": 25, "multi": [1, 4, 11, 12, 17], "my": 17, "navig": [2, 6, 21], "network": 25, "new": [1, 2, 17], "next": [0, 10, 11], "nfr": 16, "non": 16, "note": [0, 9], "oper": [15, 18, 20, 22, 23, 25], "optim": [12, 13, 14, 18, 19, 21, 26], "option": 1, "orchestr": 16, "organ": 0, "overview": [1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26], "pagin": 5, "parallel": 12, "password": [4, 11, 17], "path": [3, 5, 11, 12, 13, 14, 16, 17, 19, 20, 23, 25], "pattern": [6, 21], "penetr": 23, "perform": [1, 3, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 26], "permiss": [1, 4, 18, 22, 23, 24, 25], "persist": 23, "phase": 16, "pipelin": 1, "plan": [23, 24], "platform": [11, 16, 17, 18], "port": 1, "practic": [0, 1, 3, 4, 5, 8, 18, 19, 20, 21, 22, 23, 24, 25, 26], "prd": 16, "pre": 26, "prerequisit": [1, 2, 10, 11, 19, 21, 26], "privileg": [23, 25], "procedur": 25, "process": [12, 16], "product": [2, 8, 10, 15, 16], "profil": [6, 11, 26], "proposit": 16, "protect": 16, "provid": [8, 11, 17], "purpl": [19, 20, 22, 23], "python": 4, "q2": 16, "q3": 16, "q4": 16, "qualiti": [0, 2, 9, 15], "quantit": 26, "question": 17, "quick": [1, 2, 9, 10, 11, 19], "radiu": [0, 2, 3, 9, 12, 13, 16, 17, 19], "ransomwar": 22, "rate": [3, 5, 6, 7], "rbac": 18, "real": [11, 21, 25], "recognit": 21, "recommend": [1, 4, 10, 11, 26], "recoveri": [13, 18], "red": [19, 20, 23], "refer": [2, 5, 6, 7, 9], "refresh": [3, 4], "regulatori": 26, "relationship": 14, "releas": [0, 9], "reliabl": 16, "report": [22, 23, 24, 25], "requir": [10, 15, 16, 17], "reset": 17, "resourc": [1, 5, 11, 15, 20], "respons": [2, 3, 5, 19, 20, 22, 25], "rest": 21, "result": [3, 7, 11], "retent": 14, "risk": [5, 7, 12, 13, 16, 17, 19, 24, 26], "roadmap": [16, 17], "role": [0, 11, 18, 20], "routin": 25, "run": [1, 11, 17, 26], "sampl": 11, "scalabl": [9, 16], "scan": 1, "scenario": [3, 13, 16, 19, 22, 23, 24, 25], "schema": [14, 15], "score": [12, 13, 17, 19], "sdk": 5, "search": 2, "section": [0, 2], "secur": [0, 1, 2, 4, 6, 8, 9, 15, 16, 17, 18, 19, 20, 22, 23, 24, 26], "see": 17, "select": 5, "servicenow": [8, 11], "set": [8, 18], "setup": [1, 7, 10, 11, 17, 18, 22, 23, 24, 25], "should": 17, "siem": [16, 17], "simul": [7, 23, 26], "slowli": 17, "soar": 16, "soc": [20, 25], "softwar": 10, "sort": 5, "specif": [1, 8, 20], "stage": [1, 8], "standard": [1, 4], "start": [0, 1, 2, 9, 10, 11, 18, 19, 21, 22, 23, 24, 25, 26], "statement": 16, "statist": 3, "statu": [5, 6], "stealth": 23, "step": [0, 10, 11], "strateg": 24, "strategi": [1, 7, 12, 13, 26], "structur": [0, 2, 4, 15], "style": 2, "success": [5, 16], "summari": [0, 16], "support": [1, 2, 5, 9, 15, 17, 18, 20, 22, 23, 24], "surfac": 24, "suspici": 25, "sync": 6, "synchron": 21, "system": [4, 10, 12, 15, 17, 18], "t": [11, 17], "tabl": [9, 14], "take": 17, "tar": 16, "target": [9, 16], "team": [19, 20, 22, 23], "technic": [0, 2, 9, 15, 16, 23, 24], "techniqu": [6, 21, 22, 23], "technologi": 24, "test": [1, 5, 9, 10, 15, 22, 23], "thi": 17, "threat": [5, 6, 7, 8, 11, 16, 20, 21, 22, 23, 25, 26], "time": [11, 21, 25], "tip": 20, "token": 4, "tool": [0, 1, 2, 9, 17], "tour": 11, "track": 6, "train": 20, "tree": 13, "troubleshoot": [0, 1, 8, 9, 10, 17, 18, 19, 22, 23, 24, 25], "trust": 24, "tune": 15, "type": [14, 19], "typescript": 4, "understand": 11, "updat": [17, 18], "url": [3, 5, 6, 7], "us": [0, 9, 17, 19, 24], "usabl": [0, 16], "usag": 17, "user": [0, 2, 5, 9, 10, 11, 16, 17, 18, 19, 20, 21, 26], "valid": [8, 22, 24], "valu": 16, "variabl": [1, 10], "verif": 10, "version": 17, "view": 11, "vision": 16, "visual": [11, 21, 25], "volum": 0, "vulner": 23, "web": [11, 17, 19], "webhook": 5, "what": 17, "who": 17, "why": 17, "won": 11, "work": 17, "workflow": [1, 18, 19, 20, 21, 24, 25, 26], "your": 11, "zero": 24}})