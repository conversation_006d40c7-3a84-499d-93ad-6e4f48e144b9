Attack Path Analysis Architecture
===================================

This document provides a comprehensive technical overview of the attack path analysis engine architecture, including graph processing, algorithms, and performance optimizations.

System Overview
---------------

The attack path analysis engine is built on a multi-layered architecture designed for high performance, scalability, and extensibility:

.. mermaid::

    graph TB
        subgraph "Client Layer"
            WebUI[Web Interface]
            MobileApp[Mobile App]
            CLI[Command Line Interface]
            SDKs[Python/JS/Go SDKs]
        end
        
        subgraph "API Gateway"
            Gateway[API Gateway]
            Auth[Authentication]
            RateLimit[Rate Limiting]
            LoadBalancer[Load Balancer]
        end
        
        subgraph "Application Layer"
            subgraph "Core Services"
                AttackPathAPI[Attack Path API]
                AssetAPI[Asset Management API]
                UserAPI[User Management API]
                DiscoveryAPI[Discovery API]
            end
            
            subgraph "Analysis Services"
                GraphEngine[Graph Engine]
                AttackAnalyzer[Attack Path Analyzer]
                BlastRadius[Blast Radius Calculator]
                MitreMapper[MITRE ATT&CK Mapper]
            end
            
            subgraph "Background Services"
                TaskQueue[Task Queue]
                Scheduler[Job Scheduler]
                CacheManager[Cache Manager]
                MetricsCollector[Metrics Collector]
            end
        end
        
        subgraph "Data Layer"
            subgraph "Primary Storage"
                PostgreSQL[(PostgreSQL Database)]
                Redis[(Redis Cache)]
            end
            
            subgraph "External Integrations"
                CloudAPIs[Cloud Provider APIs]
                SIEM[SIEM Systems]
                ThreatIntel[Threat Intelligence]
                CMDB[Configuration Management DB]
            end
        end
        
        subgraph "Infrastructure"
            Monitoring[Monitoring & Alerting]
            Logging[Centralized Logging]
            Backup[Backup & Recovery]
            Security[Security Controls]
        end
        
        WebUI --> Gateway
        MobileApp --> Gateway
        CLI --> Gateway
        SDKs --> Gateway
        
        Gateway --> Auth
        Gateway --> RateLimit
        Gateway --> LoadBalancer
        
        LoadBalancer --> AttackPathAPI
        LoadBalancer --> AssetAPI
        LoadBalancer --> UserAPI
        LoadBalancer --> DiscoveryAPI
        
        AttackPathAPI --> GraphEngine
        AttackPathAPI --> AttackAnalyzer
        AttackPathAPI --> BlastRadius
        AttackPathAPI --> MitreMapper
        
        GraphEngine --> PostgreSQL
        GraphEngine --> Redis
        AttackAnalyzer --> TaskQueue
        BlastRadius --> CacheManager
        
        TaskQueue --> Scheduler
        CacheManager --> Redis
        MetricsCollector --> Monitoring
        
        AssetAPI --> CloudAPIs
        DiscoveryAPI --> SIEM
        AttackAnalyzer --> ThreatIntel
        AssetAPI --> CMDB
        
        PostgreSQL --> Backup
        Redis --> Backup

Core Components
---------------

GraphEngine
~~~~~~~~~~~

The ``GraphEngine`` is the core component responsible for graph processing and attack path discovery.

**Key Features:**

* **NetworkX Integration**: Leverages NetworkX for high-performance graph algorithms
* **Weighted Relationships**: Models security controls as edge weights
* **Intelligent Caching**: LRU cache for path and blast radius results
* **Parallel Processing**: Multi-threaded analysis for improved performance
* **Memory Optimization**: Efficient graph storage and operations

**Class Structure:**

.. code-block:: python

    class GraphEngine:
        def __init__(self, max_workers: int = 4):
            self.graph = nx.DiGraph()
            self.asset_metadata = {}
            self.relationship_weights = {}
            self.executor = ThreadPoolExecutor(max_workers)
            self.path_cache = {}
            self.blast_radius_cache = {}
        
        async def find_attack_paths(self, source: str, target: str) -> List[AttackPath]
        async def calculate_blast_radius(self, source: str) -> BlastRadiusResult
        def add_asset(self, asset_id: str, asset_data: Dict)
        def add_relationship(self, source: str, target: str, rel_data: Dict)

AttackPathAnalyzer
~~~~~~~~~~~~~~~~~~

The ``AttackPathAnalyzer`` provides high-level analysis capabilities with MITRE ATT&CK integration.

**Key Features:**

* **MITRE ATT&CK Mapping**: Complete framework integration with 12 tactics
* **Scenario Modeling**: Complex attack scenario creation and analysis
* **Risk Assessment**: Multi-factor risk scoring methodology
* **Database Integration**: Seamless integration with asset database
* **Export Capabilities**: Multiple format support for analysis results

**Class Structure:**

.. code-block:: python

    class AttackPathAnalyzer:
        def __init__(self, db: Session):
            self.db = db
            self.graph_engine = GraphEngine()
            self.mitre_mappings = self._load_mitre_mappings()
        
        async def analyze_attack_paths(self, source: str, targets: List[str]) -> List[AttackPath]
        async def create_attack_scenario(self, name: str, actor: str, entries: List[str], objectives: List[str]) -> AttackScenario
        def get_mitre_attack_mapping(self, path: AttackPath) -> Dict

Graph Algorithms
----------------

Path Discovery Algorithm
~~~~~~~~~~~~~~~~~~~~~~~~

The engine uses a modified breadth-first search with weighted edges for attack path discovery:

.. code-block:: python

    def find_attack_paths(self, source: str, target: str, max_length: int) -> List[List[str]]:
        """
        Modified BFS with weighted edges and security control consideration.
        
        Algorithm:
        1. Initialize queue with source node
        2. For each node, explore neighbors based on edge weights
        3. Apply security control penalties to path likelihood
        4. Prune paths exceeding max_length or low probability
        5. Return sorted paths by criticality score
        """
        paths = []
        queue = [(source, [source], 1.0)]  # (node, path, likelihood)
        
        while queue:
            current, path, likelihood = queue.pop(0)
            
            if current == target:
                paths.append((path, likelihood))
                continue
            
            if len(path) >= max_length:
                continue
            
            for neighbor in self.graph.successors(current):
                if neighbor not in path:  # Avoid cycles
                    edge_data = self.graph.get_edge_data(current, neighbor)
                    edge_likelihood = self._calculate_edge_likelihood(edge_data)
                    new_likelihood = likelihood * edge_likelihood
                    
                    if new_likelihood > 0.01:  # Prune low-probability paths
                        queue.append((neighbor, path + [neighbor], new_likelihood))
        
        return self._rank_paths(paths)

Blast Radius Algorithm
~~~~~~~~~~~~~~~~~~~~~~

Blast radius calculation uses degree-based propagation analysis:

.. code-block:: python

    def calculate_blast_radius(self, source: str, max_degrees: int) -> BlastRadiusResult:
        """
        Degree-based blast radius calculation with impact assessment.
        
        Algorithm:
        1. Start with compromised source asset
        2. For each degree (0 to max_degrees):
           - Find all assets at current degree
           - Calculate impact score for each asset
           - Determine propagation to next degree
        3. Aggregate impact by degree and asset type
        4. Calculate financial and compliance impact
        """
        affected_assets = set()
        impact_by_degree = {}
        current_level = {source}
        visited = {source}
        
        for degree in range(max_degrees + 1):
            if not current_level:
                break
            
            impact_by_degree[degree] = current_level.copy()
            affected_assets.update(current_level)
            
            # Calculate next level
            next_level = set()
            for asset in current_level:
                neighbors = set(self.graph.successors(asset)) | set(self.graph.predecessors(asset))
                for neighbor in neighbors:
                    if neighbor not in visited:
                        propagation_probability = self._calculate_propagation_probability(asset, neighbor)
                        if propagation_probability > 0.3:  # Threshold for propagation
                            next_level.add(neighbor)
                            visited.add(neighbor)
            
            current_level = next_level
        
        return self._calculate_impact_metrics(source, affected_assets, impact_by_degree)

Risk Scoring Methodology
------------------------

Multi-Factor Risk Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The risk scoring system uses multiple weighted factors:

.. code-block:: python

    def calculate_path_risk_score(self, path_nodes: List[str], path_edges: List[Tuple[str, str]]) -> float:
        """
        Multi-factor risk assessment for attack paths.
        
        Factors:
        - Asset Risk Scores (40%): Individual asset risk levels
        - Path Complexity (20%): Length and difficulty
        - Security Controls (30%): Effectiveness of controls
        - Business Impact (10%): Criticality of target assets
        """
        # Asset risk component (40%)
        asset_risks = [self.asset_metadata.get(node, {}).get("risk_score", 50) for node in path_nodes]
        weights = [i + 1 for i in range(len(asset_risks))]  # Later assets weighted higher
        weighted_asset_risk = sum(risk * weight for risk, weight in zip(asset_risks, weights)) / sum(weights)
        
        # Path complexity component (20%)
        complexity_factor = 1.0 + (len(path_nodes) - 1) * 0.1
        
        # Security controls component (30%)
        security_factor = 1.0
        for source, target in path_edges:
            edge_data = self.graph.get_edge_data(source, target, {})
            if edge_data.get("encrypted", False):
                security_factor *= 0.9
            if edge_data.get("authenticated", False):
                security_factor *= 0.9
            if edge_data.get("monitored", False):
                security_factor *= 0.8
        
        # Business impact component (10%)
        target_asset = self.asset_metadata.get(path_nodes[-1], {})
        business_multiplier = {
            "low": 0.5, "medium": 1.0, "high": 1.5, "critical": 2.0
        }.get(target_asset.get("business_criticality", "medium"), 1.0)
        
        # Combine factors
        risk_score = (
            weighted_asset_risk * 0.4 * complexity_factor * 0.2 * 
            security_factor * 0.3 * business_multiplier * 0.1
        )
        
        return min(100.0, risk_score)

MITRE ATT&CK Integration
------------------------

Framework Mapping
~~~~~~~~~~~~~~~~~

The system includes comprehensive MITRE ATT&CK framework integration:

.. code-block:: python

    class MitreAttackMapping:
        def __init__(self):
            self.technique_mappings = {
                AttackTechnique.INITIAL_ACCESS: {
                    "technique_id": "TA0001",
                    "technique_name": "Initial Access",
                    "tactic": "Initial Access",
                    "platforms": ["Windows", "Linux", "macOS", "Cloud"],
                    "data_sources": ["Network Traffic", "Authentication Logs"],
                    "mitigations": ["Network Segmentation", "Multi-factor Authentication"],
                    "detection_methods": ["Anomaly Detection", "Behavioral Analysis"]
                },
                # ... additional mappings for all 12 tactics
            }
        
        def map_attack_path(self, attack_path: AttackPath) -> Dict:
            """Map attack path to MITRE ATT&CK techniques."""
            mapping = {"tactics": [], "techniques": [], "mitigations": [], "detection_methods": []}
            
            for technique in attack_path.attack_techniques:
                if technique in self.technique_mappings:
                    mitre_data = self.technique_mappings[technique]
                    mapping["tactics"].append(mitre_data["tactic"])
                    mapping["techniques"].append({
                        "id": mitre_data["technique_id"],
                        "name": mitre_data["technique_name"]
                    })
                    mapping["mitigations"].extend(mitre_data["mitigations"])
                    mapping["detection_methods"].extend(mitre_data["detection_methods"])
            
            return mapping

Performance Optimizations
-------------------------

Caching Strategy
~~~~~~~~~~~~~~~~

The engine implements a multi-level caching strategy:

**Level 1: In-Memory LRU Cache**

.. code-block:: python

    class IntelligentCache:
        def __init__(self, max_size: int = 1000):
            self.path_cache = LRUCache(max_size)
            self.blast_radius_cache = LRUCache(max_size // 2)
            self.metrics = {"hits": 0, "misses": 0}
        
        def get_attack_paths(self, cache_key: Tuple) -> Optional[List[AttackPath]]:
            if cache_key in self.path_cache:
                self.metrics["hits"] += 1
                return self.path_cache[cache_key]
            self.metrics["misses"] += 1
            return None
        
        def cache_attack_paths(self, cache_key: Tuple, paths: List[AttackPath]):
            self.path_cache[cache_key] = paths

**Level 2: Redis Distributed Cache**

.. code-block:: python

    class DistributedCache:
        def __init__(self, redis_client):
            self.redis = redis_client
            self.ttl = 3600  # 1 hour TTL
        
        async def get_cached_result(self, key: str) -> Optional[Dict]:
            cached = await self.redis.get(f"attack_path:{key}")
            if cached:
                return json.loads(cached)
            return None
        
        async def cache_result(self, key: str, result: Dict):
            await self.redis.setex(f"attack_path:{key}", self.ttl, json.dumps(result, default=str))

Parallel Processing
~~~~~~~~~~~~~~~~~~~

The engine uses parallel processing for improved performance:

.. code-block:: python

    class ParallelAnalyzer:
        def __init__(self, max_workers: int = 4):
            self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        async def analyze_multiple_targets(self, source: str, targets: List[str]) -> Dict[str, List[AttackPath]]:
            """Analyze attack paths to multiple targets in parallel."""
            loop = asyncio.get_event_loop()
            
            # Create tasks for parallel execution
            tasks = []
            for target in targets:
                task = loop.run_in_executor(
                    self.executor,
                    self._analyze_single_target,
                    source,
                    target
                )
                tasks.append((target, task))
            
            # Wait for all tasks to complete
            results = {}
            for target, task in tasks:
                try:
                    paths = await task
                    results[target] = paths
                except Exception as e:
                    logger.error(f"Analysis failed for target {target}: {e}")
                    results[target] = []
            
            return results

This architecture provides a robust, scalable foundation for enterprise-grade attack path analysis with comprehensive monitoring, caching, and performance optimization.
