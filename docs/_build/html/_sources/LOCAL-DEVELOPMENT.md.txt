# Local Development & CI/CD Guide

This guide provides comprehensive instructions for local development, testing, and deployment of the Blast-Radius Security Tool using our preferred local CI/CD approach.

## 🚀 **Quick Start**

### **Prerequisites**
- Docker & Docker Compose
- Python 3.11+
- Node.js 18+
- Git

### **One-Command Setup**
```bash
# Clone and run full pipeline
git clone https://github.com/forkrul/blast-radius.git
cd blast-radius
./scripts/local-cicd.sh
```

## 🛠️ **Local CI/CD Pipeline**

### **Pipeline Overview**
Our local CI/CD pipeline provides the same rigor as cloud-based solutions with full local control:

```
Setup → Security Scan → Tests → Build → Deploy → Verify
```

### **Pipeline Commands**

#### **Full Pipeline (Recommended)**
```bash
./scripts/local-cicd.sh
```

#### **Specific Stages**
```bash
# Security scanning only
./scripts/local-cicd.sh security

# Run tests only
./scripts/local-cicd.sh test

# Build containers
./scripts/local-cicd.sh build

# Deploy to local environment
./scripts/local-cicd.sh deploy

# Clean up everything
./scripts/local-cicd.sh clean
```

#### **Pipeline Options**
```bash
# Skip tests for faster iteration
./scripts/local-cicd.sh --skip-tests

# Skip security scanning
./scripts/local-cicd.sh --skip-security

# Build without deployment
./scripts/local-cicd.sh --no-deploy

# Set environment
./scripts/local-cicd.sh --env dev
```

## 🔒 **Security-First Development**

### **Automated Security Scanning**
Every pipeline run includes:

- **Bandit**: Python security linting
- **Safety**: Dependency vulnerability scanning
- **npm audit**: Frontend security audit
- **Container scanning**: Docker image vulnerabilities

### **Security Commands**
```bash
# Run comprehensive security scan
./scripts/local-cicd.sh security

# Manual security checks
cd backend && bandit -r app/
cd backend && safety scan
cd frontend && npm audit
```

### **Security Standards**
- Zero critical vulnerabilities allowed
- All dependencies must be up-to-date
- Code must pass security linting
- Containers scanned before deployment

## 🧪 **Testing Strategy**

### **Test Categories**
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Service interaction testing
3. **Security Tests**: Vulnerability and penetration testing
4. **Performance Tests**: Load and stress testing

### **Running Tests**

#### **Backend Tests**
```bash
cd backend
source venv/bin/activate

# Unit tests with coverage
python -m pytest tests/ -v --cov=app

# Integration tests
python -m pytest tests/test_integration_*.py

# Specific test file
python -m pytest tests/test_auth.py -v
```

#### **Frontend Tests**
```bash
cd frontend

# Unit tests
npm run test:unit

# Linting
npm run lint

# Build test
npm run build
```

#### **End-to-End Tests**
```bash
# Start application
./scripts/local-cicd.sh deploy

# Run E2E tests
cd tests/e2e
python -m pytest test_e2e.py
```

## 🏗️ **Development Workflow**

### **Daily Development Cycle**

1. **Start Development**
   ```bash
   # Pull latest changes
   git pull origin master
   
   # Run quick pipeline
   ./scripts/local-cicd.sh --skip-tests deploy
   ```

2. **Make Changes**
   ```bash
   # Edit code
   # Add tests
   # Update documentation
   ```

3. **Test Changes**
   ```bash
   # Run relevant tests
   ./scripts/local-cicd.sh test
   
   # Or run specific tests
   cd backend && python -m pytest tests/test_new_feature.py
   ```

4. **Security Check**
   ```bash
   # Run security scan
   ./scripts/local-cicd.sh security
   ```

5. **Deploy and Verify**
   ```bash
   # Deploy changes
   ./scripts/local-cicd.sh deploy
   
   # Verify functionality
   curl http://localhost:8000/health
   ```

6. **Commit and Push**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature-branch
   ```

### **Feature Development**

#### **New Feature Workflow**
```bash
# 1. Create feature branch
git checkout -b feature/new-security-analysis

# 2. Develop with TDD
./scripts/local-cicd.sh test  # Should fail initially
# Write code
./scripts/local-cicd.sh test  # Should pass

# 3. Security and integration
./scripts/local-cicd.sh       # Full pipeline

# 4. Manual testing
./scripts/local-cicd.sh deploy
# Test manually at http://localhost:8000

# 5. Commit and merge
git commit -m "feat: implement new security analysis"
git push origin feature/new-security-analysis
```

## 🐳 **Container Development**

### **Local Container Management**

#### **Build Images**
```bash
# Build all images
./scripts/local-cicd.sh build

# Build specific image
docker build -t blast-radius-backend:dev ./backend
```

#### **Run Containers**
```bash
# Start all services
docker-compose up -d

# Start specific service
docker-compose up -d backend

# View logs
docker-compose logs -f backend
```

#### **Container Debugging**
```bash
# Execute into running container
docker-compose exec backend bash

# Check container health
docker-compose ps

# Restart service
docker-compose restart backend
```

### **Multi-Environment Support**

#### **Environment Configurations**
```bash
# Local development
./scripts/local-cicd.sh --env local

# Development environment
./scripts/local-cicd.sh --env dev

# Staging-like environment
./scripts/local-cicd.sh --env staging
```

#### **Environment Variables**
```bash
# Set environment variables
export ENVIRONMENT=dev
export DEBUG=true
export LOG_LEVEL=debug

# Run with custom config
./scripts/local-cicd.sh
```

## 📊 **Monitoring & Debugging**

### **Application Monitoring**

#### **Health Checks**
```bash
# Application health
curl http://localhost:8000/health

# Database health
curl http://localhost:8000/health/db

# Detailed status
curl http://localhost:8000/status
```

#### **Logs and Metrics**
```bash
# Application logs
docker-compose logs -f backend

# Database logs
docker-compose logs -f postgresql

# All service logs
docker-compose logs -f
```

### **Performance Monitoring**

#### **Local Performance Testing**
```bash
# Install performance tools
pip install locust

# Run load tests
cd tests/performance
locust -f locustfile.py --host=http://localhost:8000
```

#### **Resource Monitoring**
```bash
# Container resource usage
docker stats

# System resource usage
htop  # or top
```

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Port Conflicts**
```bash
# Check port usage
lsof -i :8000
lsof -i :5432

# Kill conflicting processes
sudo kill -9 <PID>
```

#### **Database Issues**
```bash
# Reset database
docker-compose down -v
docker-compose up -d postgresql
```

#### **Permission Issues**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/local-cicd.sh
```

#### **Container Issues**
```bash
# Clean Docker system
docker system prune -a

# Rebuild containers
docker-compose build --no-cache
```

### **Debug Mode**

#### **Enable Debug Logging**
```bash
export DEBUG=true
export LOG_LEVEL=debug
./scripts/local-cicd.sh deploy
```

#### **Interactive Debugging**
```bash
# Python debugger
cd backend
python -m pdb app/main.py

# Container debugging
docker-compose exec backend python -c "import pdb; pdb.set_trace()"
```

## 📚 **Additional Resources**

### **Documentation**
- [API Documentation](http://localhost:8000/docs) (when running)
- [Architecture Overview](../ARCHITECTURE.md)
- [Security Guidelines](../SECURITY.md)
- [Deployment Guide](../DEPLOYMENT.md)

### **Development Tools**
- **IDE Setup**: VS Code with Python and Docker extensions
- **Database GUI**: pgAdmin or DBeaver for PostgreSQL
- **API Testing**: Postman or curl
- **Container Management**: Docker Desktop or Portainer

### **Best Practices**
- Run security scans before every commit
- Write tests for all new features
- Use meaningful commit messages
- Keep dependencies up-to-date
- Monitor application performance
- Document significant changes

---

**Happy Coding!** 🚀

For questions or issues, check the troubleshooting section or reach out to the development team.
