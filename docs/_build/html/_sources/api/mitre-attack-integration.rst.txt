MITRE ATT&CK Integration API Reference
======================================

This document provides comprehensive API reference for the MITRE ATT&CK integration capabilities of the Blast-Radius Security Tool.

.. note::
   The MITRE ATT&CK Integration API provides enterprise-grade threat intelligence capabilities with 
   STIX 2.0/2.1 support, real-time correlation, and automated threat actor attribution.

Overview
--------

The MITRE ATT&CK Integration API enables:

* **Complete Framework Coverage**: Enterprise, Mobile, and ICS domains with 1000+ techniques
* **Real-time Technique Correlation**: Sub-second correlation from security events
* **Threat Actor Attribution**: Automated attribution with confidence scoring
* **Attack Pattern Recognition**: AI-powered pattern identification and analysis
* **ATT&CK Navigator Integration**: Automated heat map generation and visualization
* **Threat Intelligence Enrichment**: IOC enhancement with ATT&CK context

Base URL
--------

All API endpoints are relative to the base URL:

.. code-block:: text

    https://api.blast-radius.com/api/v1/mitre-attack

Authentication
--------------

All requests require authentication using Bearer tokens:

.. code-block:: bash

    curl -H "Authorization: Bearer YOUR_API_TOKEN" \
         -H "Content-Type: application/json" \
         https://api.blast-radius.com/api/v1/mitre-attack/

ATT&CK Data Management
---------------------

Get Data Status
~~~~~~~~~~~~~~

Get current status of MITRE ATT&CK data synchronization.

.. http:get:: /data/status

   **Response:**

   .. code-block:: json

      {
        "enterprise": {
          "technique_count": 800,
          "tactic_count": 14,
          "group_count": 150,
          "software_count": 700,
          "last_update": "2024-01-15T10:30:00Z",
          "version": "14.1"
        },
        "mobile": {
          "technique_count": 100,
          "tactic_count": 11,
          "group_count": 50,
          "software_count": 200,
          "last_update": "2024-01-15T10:30:00Z",
          "version": "14.1"
        },
        "ics": {
          "technique_count": 80,
          "tactic_count": 11,
          "group_count": 30,
          "software_count": 100,
          "last_update": "2024-01-15T10:30:00Z",
          "version": "14.1"
        }
      }

   :statuscode 200: Data status retrieved successfully

Sync ATT&CK Data
~~~~~~~~~~~~~~~

Synchronize MITRE ATT&CK data from official repositories.

.. http:post:: /data/sync

   **Request Body:**

   .. code-block:: json

      {
        "domains": ["enterprise", "mobile", "ics"],
        "force_update": true
      }

   **Response:**

   .. code-block:: json

      {
        "sync_id": "sync_12345",
        "status": "in_progress",
        "domains": ["enterprise", "mobile", "ics"],
        "estimated_completion": "2024-01-15T10:35:00Z"
      }

   :statuscode 202: Synchronization started
   :statuscode 400: Invalid request parameters

Get Technique Information
~~~~~~~~~~~~~~~~~~~~~~~

Retrieve detailed information about a specific ATT&CK technique.

.. http:get:: /techniques/(technique_id)

   **Response:**

   .. code-block:: json

      {
        "technique_id": "T1566.001",
        "name": "Spearphishing Attachment",
        "description": "Adversaries may send spearphishing emails with a malicious attachment...",
        "tactic": "Initial Access",
        "domain": "enterprise",
        "platforms": ["Linux", "macOS", "Windows"],
        "data_sources": ["Application Log", "Email Gateway", "File"],
        "mitigations": ["M1049", "M1031", "M1021"],
        "detection_methods": [
          "Monitor for suspicious email attachments",
          "Analyze file execution patterns"
        ],
        "sub_techniques": [],
        "kill_chain_phases": ["initial-access"],
        "external_references": [
          {
            "source_name": "mitre-attack",
            "url": "https://attack.mitre.org/techniques/T1566/001",
            "external_id": "T1566.001"
          }
        ],
        "created": "2020-03-02T19:05:18.150Z",
        "modified": "2023-10-03T20:10:35.462Z",
        "version": "1.4"
      }

   :statuscode 200: Technique information retrieved
   :statuscode 404: Technique not found

Technique Correlation
--------------------

Correlate Security Event
~~~~~~~~~~~~~~~~~~~~~~~

Correlate a security event with MITRE ATT&CK techniques.

.. http:post:: /correlate

   **Request Body:**

   .. code-block:: json

      {
        "event_id": "evt_12345",
        "event_data": {
          "source": "endpoint_detection",
          "event_type": "process_execution",
          "process_name": "powershell.exe",
          "command_line": "powershell -enc <base64_payload>",
          "parent_process": "winword.exe",
          "user": "john.doe",
          "timestamp": "2024-01-15T10:30:00Z",
          "host": "workstation-001"
        },
        "correlation_options": {
          "min_confidence": 0.5,
          "include_sub_techniques": true,
          "max_techniques": 10
        }
      }

   **Response:**

   .. code-block:: json

      {
        "correlation_id": "corr_67890",
        "event_id": "evt_12345",
        "techniques": [
          {
            "technique_id": "T1059.001",
            "name": "PowerShell",
            "confidence": 0.95,
            "confidence_level": "very_high",
            "evidence": [
              "PowerShell execution detected",
              "Base64 encoded command line",
              "Spawned from Office application"
            ],
            "context": {
              "tactic": "Execution",
              "parent_technique": "T1059",
              "detection_methods": ["Process monitoring", "Command line analysis"]
            }
          },
          {
            "technique_id": "T1566.001",
            "name": "Spearphishing Attachment",
            "confidence": 0.85,
            "confidence_level": "high",
            "evidence": [
              "Office application as parent process",
              "Suspicious PowerShell execution"
            ],
            "context": {
              "tactic": "Initial Access",
              "detection_methods": ["Email analysis", "Process monitoring"]
            }
          }
        ],
        "overall_confidence": 0.90,
        "timestamp": "2024-01-15T10:30:05Z"
      }

   :statuscode 200: Correlation completed successfully
   :statuscode 400: Invalid event data
   :statuscode 422: Correlation failed

Batch Correlate Events
~~~~~~~~~~~~~~~~~~~~~

Correlate multiple security events in a single request.

.. http:post:: /correlate/batch

   **Request Body:**

   .. code-block:: json

      {
        "events": [
          {
            "event_id": "evt_001",
            "event_data": { /* event data */ }
          },
          {
            "event_id": "evt_002", 
            "event_data": { /* event data */ }
          }
        ],
        "correlation_options": {
          "min_confidence": 0.6,
          "parallel_processing": true
        }
      }

   **Response:**

   .. code-block:: json

      {
        "batch_id": "batch_123",
        "correlations": [
          {
            "event_id": "evt_001",
            "techniques": [ /* correlation results */ ]
          },
          {
            "event_id": "evt_002",
            "techniques": [ /* correlation results */ ]
          }
        ],
        "processing_time_ms": 250,
        "total_events": 2,
        "successful_correlations": 2
      }

Threat Actor Attribution
-----------------------

Attribute Threat Actor
~~~~~~~~~~~~~~~~~~~~~

Perform automated threat actor attribution based on TTPs.

.. http:post:: /attribute

   **Request Body:**

   .. code-block:: json

      {
        "techniques": ["T1566.001", "T1078", "T1055", "T1003"],
        "timeframe": "7d",
        "context": {
          "target_sector": "government",
          "geographic_region": "eastern_europe",
          "attack_sophistication": "high"
        },
        "attribution_options": {
          "min_confidence": 0.7,
          "include_campaigns": true,
          "max_groups": 5
        }
      }

   **Response:**

   .. code-block:: json

      {
        "attribution_id": "attr_456",
        "attributed_groups": [
          {
            "group_id": "G0016",
            "name": "APT29",
            "aliases": ["Cozy Bear", "The Dukes"],
            "confidence_score": 0.92,
            "matching_techniques": ["T1566.001", "T1078", "T1055"],
            "behavioral_score": 0.88,
            "evidence": [
              "High overlap in technique usage",
              "Consistent with known APT29 campaigns",
              "Geographic and sector targeting match"
            ]
          },
          {
            "group_id": "G0028",
            "name": "APT28",
            "aliases": ["Fancy Bear", "Sofacy"],
            "confidence_score": 0.75,
            "matching_techniques": ["T1566.001", "T1003"],
            "behavioral_score": 0.70,
            "evidence": [
              "Partial technique overlap",
              "Similar targeting patterns"
            ]
          }
        ],
        "campaign_correlations": [
          {
            "campaign_id": "camp_789",
            "name": "Operation Ghost Writer",
            "confidence": 0.85,
            "attributed_group": "G0016"
          }
        ],
        "overall_confidence": 0.88,
        "timestamp": "2024-01-15T10:30:00Z"
      }

   :statuscode 200: Attribution completed successfully
   :statuscode 400: Invalid technique list

Get Threat Actor Profile
~~~~~~~~~~~~~~~~~~~~~~~

Retrieve detailed profile for a specific threat actor.

.. http:get:: /groups/(group_id)

   **Response:**

   .. code-block:: json

      {
        "group_id": "G0016",
        "name": "APT29",
        "description": "APT29 is threat group that has been attributed to Russia's Foreign Intelligence Service...",
        "aliases": ["Cozy Bear", "The Dukes", "Minidionis"],
        "techniques": ["T1566.001", "T1078", "T1055", "T1003", "T1071"],
        "software": ["S0154", "S0363", "S0482"],
        "associated_campaigns": ["C0021", "C0024"],
        "target_sectors": ["government", "technology", "healthcare"],
        "geographic_focus": ["north_america", "europe"],
        "sophistication_level": 0.9,
        "external_references": [
          {
            "source_name": "mitre-attack",
            "url": "https://attack.mitre.org/groups/G0016/",
            "external_id": "G0016"
          }
        ],
        "created": "2017-05-31T21:32:11.265Z",
        "modified": "2023-09-29T21:13:49.686Z"
      }

Attack Pattern Analysis
----------------------

Analyze Attack Patterns
~~~~~~~~~~~~~~~~~~~~~~

Identify and analyze attack patterns from security events.

.. http:post:: /patterns/analyze

   **Request Body:**

   .. code-block:: json

      {
        "timeframe": "30d",
        "analysis_options": {
          "min_confidence": 0.7,
          "include_sub_techniques": true,
          "pattern_types": ["sequence", "temporal", "multi_asset"],
          "min_event_count": 5
        },
        "filters": {
          "asset_types": ["endpoint", "server"],
          "user_groups": ["privileged_users"],
          "severity_levels": ["high", "critical"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "analysis_id": "analysis_123",
        "patterns": [
          {
            "pattern_id": "pattern_001",
            "name": "Credential Dumping Campaign",
            "techniques": ["T1566.001", "T1059.001", "T1003.001"],
            "sequence": ["T1566.001", "T1059.001", "T1003.001"],
            "confidence": 0.89,
            "first_seen": "2024-01-01T08:00:00Z",
            "last_seen": "2024-01-15T16:30:00Z",
            "event_count": 25,
            "affected_assets": ["ws-001", "ws-002", "srv-001"],
            "attributed_groups": ["G0016"],
            "campaign_id": "camp_456"
          }
        ],
        "summary": {
          "total_patterns": 1,
          "high_confidence_patterns": 1,
          "unique_techniques": 3,
          "affected_assets": 3
        }
      }

Track Campaigns
~~~~~~~~~~~~~~

Track and monitor threat campaigns over time.

.. http:get:: /campaigns

   **Query Parameters:**

   * ``timeframe`` (optional) - Time range for campaign tracking (default: 30d)
   * ``attribution_threshold`` (optional) - Minimum confidence for attribution (default: 0.8)
   * ``include_ongoing`` (optional) - Include ongoing campaigns (default: true)

   **Response:**

   .. code-block:: json

      {
        "campaigns": [
          {
            "campaign_id": "camp_789",
            "name": "Operation Ghost Writer",
            "attributed_groups": ["G0016"],
            "attribution_confidence": 0.92,
            "start_date": "2024-01-01T00:00:00Z",
            "end_date": null,
            "status": "ongoing",
            "techniques": ["T1566.001", "T1078", "T1055"],
            "affected_assets": ["ws-001", "ws-002", "srv-001"],
            "event_count": 45,
            "target_sectors": ["government"],
            "geographic_regions": ["north_america"]
          }
        ],
        "summary": {
          "total_campaigns": 1,
          "ongoing_campaigns": 1,
          "attributed_campaigns": 1
        }
      }

ATT&CK Navigator Integration
---------------------------

Generate Navigator Layer
~~~~~~~~~~~~~~~~~~~~~~~

Generate ATT&CK Navigator layer for visualization.

.. http:post:: /navigator/generate

   **Request Body:**

   .. code-block:: json

      {
        "layer_name": "Q4 Threat Landscape",
        "description": "Quarterly threat analysis for organization",
        "data_source": "organization_events",
        "timeframe": "90d",
        "layer_options": {
          "domain": "enterprise",
          "color_scheme": "red",
          "include_sub_techniques": true,
          "score_type": "frequency"
        },
        "filters": {
          "min_confidence": 0.7,
          "technique_categories": ["all"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "layer_id": "layer_123",
        "layer_name": "Q4 Threat Landscape",
        "navigator_layer": {
          "name": "Q4 Threat Landscape",
          "versions": {
            "attack": "14",
            "navigator": "4.9.1",
            "layer": "4.5"
          },
          "domain": "enterprise-attack",
          "description": "Quarterly threat analysis for organization",
          "techniques": [
            {
              "techniqueID": "T1566.001",
              "score": 85,
              "color": "#ff6b6b",
              "comment": "High frequency technique - 85 occurrences"
            }
          ]
        },
        "statistics": {
          "total_techniques": 45,
          "scored_techniques": 45,
          "max_score": 85,
          "avg_score": 12.3
        }
      }

Export Navigator Layer
~~~~~~~~~~~~~~~~~~~~

Export ATT&CK Navigator layer in various formats.

.. http:get:: /navigator/layers/(layer_id)/export

   **Query Parameters:**

   * ``format`` - Export format (json, svg, png)
   * ``include_legend`` (optional) - Include legend in visualization
   * ``resolution`` (optional) - Image resolution for PNG export

   **Response:**

   For JSON format:

   .. code-block:: json

      {
        "layer_data": { /* Navigator layer JSON */ },
        "export_format": "json",
        "generated_at": "2024-01-15T10:30:00Z"
      }

   For SVG/PNG formats, returns the binary image data.

Threat Intelligence Enrichment
-----------------------------

Enrich IOCs
~~~~~~~~~~

Enrich indicators of compromise with ATT&CK context.

.. http:post:: /enrich/iocs

   **Request Body:**

   .. code-block:: json

      {
        "iocs": [
          {
            "type": "domain",
            "value": "malicious-domain.com"
          },
          {
            "type": "hash",
            "value": "a1b2c3d4e5f6789..."
          },
          {
            "type": "ip",
            "value": "*************"
          }
        ],
        "enrichment_options": {
          "include_techniques": true,
          "include_groups": true,
          "include_campaigns": true,
          "confidence_threshold": 0.6
        }
      }

   **Response:**

   .. code-block:: json

      {
        "enrichment_id": "enrich_456",
        "enriched_iocs": [
          {
            "ioc": {
              "type": "domain",
              "value": "malicious-domain.com"
            },
            "enrichment": {
              "techniques": ["T1071.001", "T1573.002"],
              "threat_groups": ["G0016", "G0028"],
              "campaigns": ["camp_789"],
              "confidence": 0.85,
              "first_seen": "2024-01-01T00:00:00Z",
              "last_seen": "2024-01-15T10:30:00Z"
            }
          }
        ],
        "processing_time_ms": 150
      }

Get Event Context
~~~~~~~~~~~~~~~~

Get contextual analysis for a security event.

.. http:get:: /context/events/(event_id)

   **Query Parameters:**

   * ``include_historical`` (optional) - Include historical context
   * ``correlation_window`` (optional) - Time window for correlation (default: 24h)

   **Response:**

   .. code-block:: json

      {
        "event_id": "evt_67890",
        "context": {
          "primary_technique": "T1059.001",
          "related_techniques": ["T1566.001", "T1055"],
          "threat_actor_probabilities": {
            "G0016": 0.85,
            "G0028": 0.65
          },
          "campaign_correlation": {
            "campaign_id": "camp_789",
            "confidence": 0.78
          },
          "historical_context": {
            "similar_events_7d": 12,
            "technique_frequency": 0.15,
            "baseline_deviation": 2.3
          }
        }
      }

Error Handling
--------------

The API uses standard HTTP status codes and returns detailed error information:

.. code-block:: json

   {
     "error": {
       "code": "TECHNIQUE_NOT_FOUND",
       "message": "The specified technique 'T9999' does not exist",
       "details": {
         "technique_id": "T9999",
         "suggestion": "Check technique ID format (e.g., T1566.001)"
       }
     }
   }

**Common Error Codes:**

* ``TECHNIQUE_NOT_FOUND`` - Unknown technique ID
* ``GROUP_NOT_FOUND`` - Unknown threat actor group
* ``CORRELATION_FAILED`` - Event correlation failed
* ``ATTRIBUTION_FAILED`` - Threat actor attribution failed
* ``INVALID_TIMEFRAME`` - Invalid time range specified
* ``DATA_SYNC_IN_PROGRESS`` - ATT&CK data synchronization in progress

Rate Limiting
-------------

API requests are rate limited to ensure fair usage:

* **Standard Users**: 500 requests per hour
* **Premium Users**: 2000 requests per hour
* **Enterprise Users**: 10000 requests per hour

For detailed examples and SDK usage, see the :doc:`../user-guides/mitre-attack-integration` guide.
