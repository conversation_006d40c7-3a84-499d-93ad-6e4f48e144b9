Frequently Asked Questions (FAQ)
===================================

This document answers the most commonly asked questions about the Blast-Radius Security Tool.

General Questions
-----------------

What is the Blast-Radius Security Tool?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The Blast-Radius Security Tool is a comprehensive security platform designed for purple teams, SOC operators, security architects, and red teamers. It provides real-time attack path analysis, multi-cloud integration, and deep ServiceNow CMDB integration to enable proactive security posture management and incident response.

**Key Features:**

* Real-time attack path analysis with 5-degree mapping
* Multi-cloud integration (AWS, Azure, GCP)
* Role-based dashboards for different security roles
* Threat intelligence integration with STIX/TAXII
* Automated remediation workflows
* ServiceNow CMDB bi-directional synchronization

Who should use this tool?
~~~~~~~~~~~~~~~~~~~~~~~~

The platform is designed for various security professionals:

* **SOC Operators**: Real-time monitoring and incident response
* **Security Architects**: Risk assessment and security design
* **Red Team Members**: Attack simulation and path discovery
* **Purple Team Members**: Collaborative security testing and validation
* **Administrators**: Platform management and configuration

What are the system requirements?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Minimum Requirements:**

* 8GB RAM (16GB+ recommended for production)
* 4 CPU cores (8+ cores recommended)
* 50GB storage (100GB+ recommended)
* Modern web browser (Chrome 90+, Firefox 88+, Safari 14+)

**Software Requirements:**

* Docker and Docker Compose
* Python 3.11+ (for development)
* Node.js 18+ (for frontend development)
* PostgreSQL 15+, Redis 7+, Neo4j 5.15+

Installation and Setup
----------------------

How do I install the Blast-Radius Security Tool?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The quickest way is using Docker:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius
   
   # Start all services
   docker-compose up -d
   
   # Access the platform
   open http://localhost:3000

For detailed installation instructions, see the :doc:`../installation` guide.

What are the default login credentials?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Default Credentials:**

* **Email**: <EMAIL>
* **Password**: BlastRadius2024!

.. warning::
   **Security Notice**: Change the default password immediately after first login!

How do I reset the admin password?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Reset admin password
   docker-compose exec backend python -m app.cli reset-admin-password
   
   # Or create a new admin user
   docker-compose exec backend python -m app.cli create-admin \
     --email <EMAIL> \
     --password "NewSecurePassword123!"

Why can't I access the web interface?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Common Solutions:**

1. **Check if services are running**:
   
   .. code-block:: bash
   
      docker-compose ps

2. **Verify port availability**:
   
   .. code-block:: bash
   
      netstat -tulpn | grep :3000

3. **Check firewall settings**:
   
   .. code-block:: bash
   
      sudo ufw allow 3000
      sudo ufw allow 8000

4. **Review application logs**:
   
   .. code-block:: bash
   
      docker-compose logs frontend
      docker-compose logs backend

Configuration and Usage
-----------------------

How do I configure cloud provider integrations?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**AWS Integration:**

1. Navigate to "Integrations" → "Cloud Providers" → "AWS"
2. Enter your AWS credentials:
   
   * Access Key ID
   * Secret Access Key
   * Default Region

3. Test the connection and start asset discovery

**Azure Integration:**

1. Navigate to "Integrations" → "Cloud Providers" → "Azure"
2. Enter your Azure credentials:
   
   * Client ID
   * Client Secret
   * Tenant ID
   * Subscription ID

**Required Permissions:**

* AWS: EC2, IAM, VPC read permissions
* Azure: Reader role on subscription
* GCP: Compute Viewer, Security Reviewer roles

How do I add new users?
~~~~~~~~~~~~~~~~~~~~~~

**Via Web Interface:**

1. Navigate to "Administration" → "Users"
2. Click "Add User"
3. Fill in user details and assign role
4. Send invitation email to user

**Via Command Line:**

.. code-block:: bash

   docker-compose exec backend python -m app.cli create-user \
     --email <EMAIL> \
     --first-name "John" \
     --last-name "Doe" \
     --role "soc_operator"

How do I enable Multi-Factor Authentication (MFA)?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**For Individual Users:**

1. Go to "Profile Settings" → "Security"
2. Click "Enable MFA"
3. Scan QR code with authenticator app
4. Enter verification code
5. Save backup codes securely

**For All Users (Admin):**

1. Navigate to "Administration" → "Security Settings"
2. Enable "Require MFA for all users"
3. Set grace period for existing users

Attack Path Analysis
--------------------

How does attack path analysis work?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The attack path analysis engine uses graph algorithms to:

1. **Map Assets**: Discover and map all assets and their relationships
2. **Identify Vulnerabilities**: Correlate vulnerability data with assets
3. **Calculate Paths**: Use graph traversal to find potential attack routes
4. **Score Risk**: Apply MITRE ATT&CK framework for risk scoring
5. **Visualize Results**: Present interactive graphs and detailed reports

The analysis considers:

* Asset relationships and dependencies
* Vulnerability exploitability and impact
* Security control effectiveness
* Network topology and access controls

How long does attack path analysis take?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Typical Analysis Times:**

* **Small Environment** (< 1,000 assets): 5-15 seconds
* **Medium Environment** (1,000-10,000 assets): 15-60 seconds
* **Large Environment** (10,000+ assets): 1-5 minutes

**Factors Affecting Performance:**

* Number of assets and relationships
* Complexity of network topology
* Analysis depth (number of hops)
* System resources (CPU, memory)

Why am I not seeing any attack paths?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Common Causes:**

1. **Insufficient Asset Data**: Ensure asset discovery is complete
2. **No Vulnerabilities**: Check vulnerability data sources
3. **Restrictive Filters**: Review analysis parameters and filters
4. **Network Segmentation**: Strong segmentation may limit paths
5. **Analysis Scope**: Verify source and target configurations

**Troubleshooting Steps:**

1. Check asset inventory completeness
2. Verify vulnerability data is current
3. Adjust analysis parameters (increase hops, change filters)
4. Review network topology and relationships

Can I customize risk scoring?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, risk scoring can be customized:

**Available Customizations:**

* **Vulnerability Scoring**: Adjust CVSS score weighting
* **Asset Criticality**: Define business impact scores
* **Control Effectiveness**: Set security control ratings
* **Threat Intelligence**: Incorporate threat actor capabilities

**Configuration Location:**

Navigate to "Administration" → "Risk Configuration" to modify scoring parameters.

Performance and Troubleshooting
-------------------------------

Why is the platform running slowly?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Common Performance Issues:**

1. **Insufficient Resources**: Increase RAM and CPU allocation
2. **Database Performance**: Optimize queries and indexing
3. **Large Datasets**: Consider data archival and cleanup
4. **Network Latency**: Check network connectivity and bandwidth

**Performance Optimization:**

.. code-block:: bash

   # Check resource usage
   docker stats
   
   # Increase memory limits in docker-compose.yml
   # Clear cache
   docker-compose exec redis redis-cli FLUSHALL
   
   # Restart services
   docker-compose restart

How do I backup my data?
~~~~~~~~~~~~~~~~~~~~~~~

**Database Backup:**

.. code-block:: bash

   # PostgreSQL backup
   docker-compose exec postgres pg_dump -U blast_user blast_radius > backup.sql
   
   # Neo4j backup
   docker-compose exec neo4j neo4j-admin dump --database=neo4j --to=/backups/

**Configuration Backup:**

.. code-block:: bash

   # Backup configuration files
   cp .env .env.backup
   cp docker-compose.yml docker-compose.yml.backup

**Automated Backup:**

Set up automated backups using cron jobs or backup tools. See the :doc:`../user-guides/administrators` guide for detailed backup procedures.

How do I update to a new version?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Update Process:**

1. **Backup Data**: Create full backup before updating
2. **Stop Services**: ``docker-compose down``
3. **Update Code**: ``git pull origin main``
4. **Update Images**: ``docker-compose pull``
5. **Start Services**: ``docker-compose up -d``
6. **Verify Update**: Check version and functionality

**Migration Notes:**

Some updates may require database migrations. Check the :doc:`../releases/changelog` for specific update instructions.

Integration and API
-------------------

How do I integrate with my SIEM?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Log Forwarding:**

1. Navigate to "Integrations" → "SIEM"
2. Configure log forwarding settings:
   
   * SIEM endpoint URL
   * Authentication credentials
   * Log format (CEF, LEEF, JSON)
   * Event filters

**API Integration:**

Use the REST API to pull data into your SIEM:

.. code-block:: bash

   # Get security events
   curl -H "Authorization: Bearer <token>" \
     "https://api.blastradius.com/api/v1/events"

Can I use the API for automation?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, the platform provides a comprehensive REST API:

**API Features:**

* Complete CRUD operations for all resources
* Webhook support for real-time notifications
* Rate limiting and authentication
* OpenAPI 3.0 specification
* Interactive documentation at ``/docs``

**Example Automation:**

.. code-block:: python

   import requests
   
   # Trigger attack path analysis
   response = requests.post(
       "https://api.blastradius.com/api/v1/analysis",
       headers={"Authorization": "Bearer <token>"},
       json={
           "source": "external",
           "target": "crown_jewels",
           "max_hops": 5
       }
   )

How do I get API credentials?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**API Token Generation:**

1. Navigate to "Profile Settings" → "API Tokens"
2. Click "Generate New Token"
3. Set token permissions and expiration
4. Copy and securely store the token

**Service Account Tokens:**

For automation, create dedicated service accounts with API-only access.

Security and Compliance
-----------------------

Is my data secure?
~~~~~~~~~~~~~~~~~

**Security Measures:**

* **Encryption**: AES-256 for data at rest, TLS 1.3 for data in transit
* **Authentication**: Multi-factor authentication required
* **Authorization**: Role-based access control with fine-grained permissions
* **Audit Logging**: Comprehensive logging of all user actions
* **Network Security**: Secure network architecture with segmentation

**Compliance:**

* SOC 2 Type II compliance ready
* GDPR compliant data handling
* ISO 27001 security controls alignment
* Regular security assessments and penetration testing

How do I configure audit logging?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Audit Configuration:**

1. Navigate to "Administration" → "Audit Settings"
2. Enable audit logging for required events:
   
   * User authentication and authorization
   * Data access and modifications
   * Configuration changes
   * Administrative actions

3. Configure log retention and archival policies

**Log Access:**

Audit logs are available through:

* Web interface: "Administration" → "Audit Logs"
* API endpoint: ``/api/v1/audit/logs``
* Log files: ``/var/log/blast-radius/audit.log``

Can I run this in an air-gapped environment?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, with some limitations:

**Requirements:**

* All container images must be pre-loaded
* Threat intelligence feeds require manual updates
* Cloud integrations will not work
* External API integrations must be disabled

**Setup Process:**

1. Download all required container images
2. Transfer to air-gapped environment
3. Load images: ``docker load < images.tar``
4. Configure for offline operation
5. Set up manual threat intelligence updates

Support and Community
---------------------

How do I get help?
~~~~~~~~~~~~~~~~~

**Support Channels:**

1. **Documentation**: This comprehensive guide
2. **GitHub Issues**: https://github.com/forkrul/blast-radius/issues
3. **Community Forum**: https://community.blastradius.security
4. **Enterprise Support**: <EMAIL>

**When Reporting Issues:**

Include:

* System information and configuration
* Detailed error messages and logs
* Steps to reproduce the issue
* Expected vs. actual behavior

Is there a community or forum?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes! Join our community:

* **GitHub Discussions**: Feature requests and general discussion
* **Community Forum**: User discussions and knowledge sharing
* **Discord Server**: Real-time chat and support
* **Stack Overflow**: Technical questions (tag: blast-radius)

How can I contribute?
~~~~~~~~~~~~~~~~~~~~

**Ways to Contribute:**

* **Bug Reports**: Report issues and bugs
* **Feature Requests**: Suggest new features and improvements
* **Code Contributions**: Submit pull requests
* **Documentation**: Improve documentation and guides
* **Community Support**: Help other users in forums

See the :doc:`../development/contributing` guide for detailed contribution guidelines.

What's the roadmap for future features?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Upcoming Features:**

* **Version 1.1**: Enhanced threat intelligence and automated remediation
* **Version 1.2**: Advanced analytics and enterprise features
* **Version 2.0**: AI-powered security and extended integrations

See the :doc:`../releases/roadmap` for detailed feature plans and timelines.

For additional questions not covered here, please check the comprehensive documentation or reach out through our support channels.
