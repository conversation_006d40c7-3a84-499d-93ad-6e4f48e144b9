SOC Operators Guide
===================

This guide is specifically designed for :user-role:`SOC Operators` who use the Blast-Radius Security Tool for real-time monitoring, incident response, and threat analysis.

Overview
--------

As a SOC Operator, you'll primarily use the Blast-Radius Security Tool to:

* Monitor real-time security events and attack paths
* Investigate security incidents and their potential impact
* Coordinate incident response activities
* Generate reports for management and stakeholders

Dashboard Overview
------------------

SOC Operator Dashboard Features
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The SOC Operator dashboard provides:

* **Real-time Threat Feed**: Live updates of security events and alerts
* **Attack Path Visualization**: Interactive graphs showing potential attack vectors
* **Incident Queue**: Prioritized list of security incidents requiring attention
* **Asset Status**: Current security posture of monitored assets
* **Threat Intelligence**: Contextual information about detected threats

Key Permissions
~~~~~~~~~~~~~~~

As a :user-role:`SOC Operator`, you have the following permissions:

* :permission:`view_security_events` - View all security events and alerts
* :permission:`investigate_incidents` - Access incident details and investigation tools
* :permission:`update_incident_status` - Update incident status and add notes
* :permission:`view_attack_paths` - Visualize attack paths and blast radius
* :permission:`generate_reports` - Create and export security reports

Getting Started
---------------

Initial Setup
~~~~~~~~~~~~~

1. **Login**: Use your corporate credentials to access the platform
2. **Dashboard Configuration**: Customize your dashboard layout and widgets
3. **Alert Preferences**: Set up notification preferences for different alert types
4. **Integration Setup**: Configure integrations with your existing security tools

Daily Workflow
~~~~~~~~~~~~~~

Morning Routine
^^^^^^^^^^^^^^^

1. **Review Overnight Alerts**: Check for any critical incidents that occurred overnight
2. **Threat Intelligence Update**: Review new threat intelligence feeds
3. **System Health Check**: Verify all monitoring systems are operational
4. **Priority Assessment**: Triage and prioritize incidents for the day

Incident Investigation
^^^^^^^^^^^^^^^^^^^^^^

When investigating a security incident:

1. **Initial Assessment**:
   
   * Review the alert details and severity
   * Identify affected assets and systems
   * Determine potential blast radius

2. **Attack Path Analysis**:
   
   * Use the graph visualization to understand attack vectors
   * Identify critical paths that could lead to high-value assets
   * Assess the current containment status

3. **Evidence Collection**:
   
   * Gather relevant logs and forensic data
   * Document timeline of events
   * Preserve evidence for potential legal proceedings

4. **Impact Assessment**:
   
   * Determine scope of compromise
   * Identify data or systems at risk
   * Calculate potential business impact

5. **Response Coordination**:
   
   * Notify relevant stakeholders
   * Coordinate with incident response team
   * Implement containment measures

Real-time Monitoring
--------------------

Alert Management
~~~~~~~~~~~~~~~~

The platform provides several types of alerts:

* **Critical Alerts**: Immediate threats requiring urgent response
* **High Priority**: Significant security events needing prompt attention
* **Medium Priority**: Notable events for investigation
* **Low Priority**: Informational events for awareness

Alert Workflow:

1. **Alert Reception**: New alerts appear in the incident queue
2. **Initial Triage**: Assess alert severity and assign priority
3. **Investigation**: Conduct detailed analysis of the security event
4. **Response**: Take appropriate action based on findings
5. **Documentation**: Record investigation results and actions taken
6. **Closure**: Close resolved incidents with proper documentation

Attack Path Visualization
~~~~~~~~~~~~~~~~~~~~~~~~~

Understanding Attack Paths:

* **Nodes**: Represent assets, users, or security controls
* **Edges**: Show relationships and potential attack vectors
* **Colors**: Indicate risk levels and current security status
* **Paths**: Highlight potential routes an attacker might take

Using the Visualization:

1. **Zoom and Pan**: Navigate large network graphs
2. **Filter Options**: Focus on specific asset types or risk levels
3. **Path Highlighting**: Trace specific attack scenarios
4. **Risk Assessment**: Identify highest-risk attack paths

Incident Response
-----------------

Response Procedures
~~~~~~~~~~~~~~~~~~~

Standard Incident Response Process:

1. **Detection and Analysis**:
   
   * Validate the security event
   * Determine if it's a genuine incident
   * Assess initial impact and scope

2. **Containment**:
   
   * Implement immediate containment measures
   * Prevent further spread of the incident
   * Preserve evidence for investigation

3. **Eradication**:
   
   * Remove the threat from the environment
   * Address root causes and vulnerabilities
   * Implement additional security controls

4. **Recovery**:
   
   * Restore affected systems and services
   * Monitor for signs of persistent threats
   * Validate system integrity

5. **Post-Incident Activities**:
   
   * Document lessons learned
   * Update procedures and controls
   * Conduct post-incident review

Escalation Procedures
~~~~~~~~~~~~~~~~~~~~~

When to Escalate:

* **Critical Infrastructure Impact**: Core business systems affected
* **Data Breach Suspected**: Potential unauthorized data access
* **Advanced Persistent Threat**: Sophisticated, ongoing attack
* **Regulatory Implications**: Incidents requiring compliance reporting
* **Resource Limitations**: Incident exceeds team capabilities

Escalation Contacts:

* **Incident Response Manager**: For complex or high-impact incidents
* **Security Architect**: For technical guidance on containment
* **Legal Team**: For incidents with potential legal implications
* **Executive Team**: For business-critical incidents

Reporting and Documentation
---------------------------

Incident Reports
~~~~~~~~~~~~~~~~

Required Documentation:

* **Incident Summary**: Brief overview of the security event
* **Timeline**: Chronological sequence of events
* **Impact Assessment**: Business and technical impact
* **Response Actions**: Steps taken to address the incident
* **Lessons Learned**: Improvements for future incidents

Report Templates:

* **Executive Summary**: High-level overview for management
* **Technical Report**: Detailed technical analysis
* **Compliance Report**: Regulatory reporting requirements
* **Post-Incident Review**: Comprehensive incident analysis

Metrics and KPIs
~~~~~~~~~~~~~~~~~

Key Performance Indicators for SOC Operations:

* **Mean Time to Detection (MTTD)**: Average time to detect incidents
* **Mean Time to Response (MTTR)**: Average time to respond to incidents
* **False Positive Rate**: Percentage of alerts that are false positives
* **Incident Volume**: Number of incidents handled per period
* **Escalation Rate**: Percentage of incidents requiring escalation

Best Practices
--------------

Operational Excellence
~~~~~~~~~~~~~~~~~~~~~~

* **Stay Current**: Keep up with latest threat intelligence and attack techniques
* **Continuous Learning**: Participate in training and certification programs
* **Tool Proficiency**: Master the platform's features and capabilities
* **Communication**: Maintain clear communication with team members and stakeholders
* **Documentation**: Keep detailed records of all activities and decisions

Threat Hunting
~~~~~~~~~~~~~~

Proactive threat hunting activities:

* **Hypothesis-Driven Hunting**: Develop and test threat hypotheses
* **IOC Hunting**: Search for known indicators of compromise
* **Behavioral Analysis**: Look for anomalous user or system behavior
* **Threat Intelligence Integration**: Use intelligence to guide hunting activities

Common Scenarios
----------------

Scenario 1: Malware Detection
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Situation**: Antivirus alert indicates potential malware on user workstation

**Response Steps**:

1. Isolate the affected workstation from the network
2. Analyze the malware sample and its behavior
3. Determine if other systems are affected
4. Implement containment and eradication measures
5. Monitor for signs of persistence or lateral movement

Scenario 2: Suspicious Network Activity
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Situation**: Network monitoring detects unusual outbound connections

**Response Steps**:

1. Analyze network traffic patterns and destinations
2. Identify source systems and user accounts
3. Correlate with threat intelligence feeds
4. Determine if it's legitimate business activity or malicious
5. Take appropriate containment or monitoring actions

Scenario 3: Privileged Account Compromise
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Situation**: Unusual activity detected on administrative account

**Response Steps**:

1. Immediately disable or restrict the compromised account
2. Analyze account activity and access patterns
3. Identify systems and data accessed by the account
4. Assess potential data exposure or system compromise
5. Implement additional monitoring and security controls

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

* **Dashboard Not Loading**: Check network connectivity and browser compatibility
* **Missing Alerts**: Verify data source connections and alert rules
* **Slow Performance**: Clear browser cache or contact system administrator
* **Access Denied**: Verify user permissions and role assignments

Getting Help
~~~~~~~~~~~~

* **Internal Documentation**: Check the platform's built-in help system
* **Team Knowledge Base**: Consult shared documentation and procedures
* **Escalation**: Contact senior analysts or incident response team
* **Technical Support**: Reach out to platform administrators or vendors

Conclusion
----------

As a SOC Operator, you play a critical role in maintaining organizational security. The Blast-Radius Security Tool provides powerful capabilities to help you detect, investigate, and respond to security threats effectively. Regular practice with the platform's features and continuous learning about emerging threats will enhance your effectiveness in protecting the organization.
