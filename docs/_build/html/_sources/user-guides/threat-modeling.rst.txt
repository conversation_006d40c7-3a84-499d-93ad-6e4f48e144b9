Threat Modeling User Guide
===========================

This guide covers the advanced threat modeling capabilities of the Blast-Radius Security Tool, including threat actor simulation, quantitative risk assessment, and attack success probability modeling.

.. note::
   The threat modeling features provide enterprise-grade threat analysis with pre-loaded 
   threat actor profiles, statistical modeling, and regulatory compliance impact assessment.

Overview
--------

The threat modeling engine provides sophisticated capabilities for:

* **🎭 Threat Actor Simulation**: Model attacks from known threat actors (APT29, APT28, FIN7, etc.)
* **📊 Quantitative Risk Assessment**: Mathematical risk calculation with business impact
* **🎯 Attack Success Probability**: Statistical modeling of attack likelihood
* **⏱️ Time-to-Compromise Estimation**: Realistic attack timeline modeling
* **🔍 Detection Probability Analysis**: Time-to-detection based on monitoring coverage
* **💰 Financial Impact Assessment**: Monetary loss calculation and recovery cost estimation
* **📋 Compliance Impact Analysis**: Automatic regulatory violation identification
* **🛡️ Mitigation Strategy Generation**: AI-driven security control recommendations

Getting Started
---------------

Prerequisites
~~~~~~~~~~~~~

Before using threat modeling features, ensure you have:

1. **Asset Discovery Completed**: Your infrastructure assets are discovered and cataloged
2. **Attack Path Analysis Configured**: Basic attack path analysis is functional
3. **Appropriate Permissions**: User role with threat modeling permissions
4. **MITRE ATT&CK Data**: Framework data is loaded and up-to-date

Basic Threat Modeling Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Select Threat Actor**: Choose from pre-loaded threat actor profiles
2. **Define Target Assets**: Specify high-value assets to protect
3. **Run Attack Simulation**: Execute threat actor simulation
4. **Analyze Results**: Review success probability, detection time, and impact
5. **Generate Mitigations**: Create security control recommendations
6. **Track Risk Over Time**: Monitor risk posture changes

Threat Actor Profiles
---------------------

Pre-loaded Threat Actors
~~~~~~~~~~~~~~~~~~~~~~~~

The system includes detailed profiles for major threat actors:

**APT29 (Cozy Bear)**

* **Sophistication Level**: 0.9 (Very High)
* **Resource Level**: 0.8 (High)
* **Persistence Level**: 0.85 (Very High)
* **Primary Motivation**: Espionage
* **Geographic Origin**: Russia
* **Target Sectors**: Government, Technology, Healthcare
* **Preferred Techniques**: T1566 (Phishing), T1078 (Valid Accounts), T1055 (Process Injection)
* **Known Tools**: Cobalt Strike, PowerShell Empire, WinRAR

**APT28 (Fancy Bear)**

* **Sophistication Level**: 0.85 (High)
* **Resource Level**: 0.8 (High)
* **Persistence Level**: 0.8 (High)
* **Primary Motivation**: Espionage, Disruption
* **Geographic Origin**: Russia
* **Target Sectors**: Government, Military, Media
* **Preferred Techniques**: T1566 (Phishing), T1190 (Exploit Public-Facing Application)
* **Known Tools**: X-Agent, Sofacy, Zebrocy

**FIN7**

* **Sophistication Level**: 0.75 (High)
* **Resource Level**: 0.7 (Medium-High)
* **Persistence Level**: 0.7 (Medium-High)
* **Primary Motivation**: Financial
* **Geographic Origin**: Unknown
* **Target Sectors**: Retail, Restaurant, Hospitality
* **Preferred Techniques**: T1566 (Phishing), T1059 (Command and Scripting Interpreter)
* **Known Tools**: Carbanak, GRIFFON, POWERSOURCE

**Insider Threat**

* **Sophistication Level**: 0.6 (Medium)
* **Resource Level**: 0.8 (High - Internal Access)
* **Persistence Level**: 0.9 (Very High)
* **Primary Motivation**: Financial, Ideology, Revenge
* **Geographic Origin**: Internal
* **Target Sectors**: All
* **Preferred Techniques**: T1078 (Valid Accounts), T1005 (Data from Local System)
* **Known Tools**: Native OS tools, Legitimate applications

Attack Simulation
-----------------

Running Attack Simulations
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Basic Attack Simulation**

.. code-block:: python

    # Python SDK example
    from blast_radius import BlastRadiusClient
    
    client = BlastRadiusClient(
        base_url="https://your-instance.com",
        api_token="your-api-token"
    )
    
    # Run threat actor simulation
    simulation = client.threat_modeling.simulate_attack(
        threat_actor_id="APT29",
        target_assets=["database_001", "web_server_001"],
        scenario_name="APT29 Campaign Simulation"
    )
    
    print(f"Success Probability: {simulation.success_probability:.2%}")
    print(f"Detection Probability: {simulation.detection_probability:.2%}")
    print(f"Time to Compromise: {simulation.estimated_time_to_compromise:.1f} hours")
    print(f"Financial Impact: ${simulation.financial_impact:,.0f}")

**Advanced Simulation with Custom Parameters**

.. code-block:: python

    # Advanced simulation with custom threat actor
    custom_threat_actor = {
        "actor_id": "CUSTOM_APT",
        "name": "Custom Advanced Persistent Threat",
        "sophistication_level": 0.8,
        "resource_level": 0.7,
        "persistence_level": 0.75,
        "primary_motivation": "espionage",
        "preferred_techniques": ["T1566", "T1078", "T1055"],
        "target_sectors": ["technology", "finance"]
    }
    
    simulation = client.threat_modeling.simulate_attack_custom(
        threat_actor=custom_threat_actor,
        target_assets=["critical_database", "payment_system"],
        scenario_name="Custom APT Simulation",
        simulation_parameters={
            "max_attack_duration": 168,  # 1 week
            "detection_enhancement": 1.2,  # 20% better detection
            "security_controls_effectiveness": 0.85
        }
    )

**API Usage**

.. code-block:: bash

    # REST API example
    curl -X POST "https://api.blast-radius.com/api/v1/threat-modeling/simulate" \
      -H "Authorization: Bearer your-token" \
      -H "Content-Type: application/json" \
      -d '{
        "threat_actor_id": "APT29",
        "target_assets": ["database_001", "web_server_001"],
        "scenario_name": "Q4 Risk Assessment",
        "simulation_parameters": {
          "include_insider_threat": true,
          "consider_supply_chain": true,
          "regulatory_context": ["GDPR", "SOX"]
        }
      }'

Quantitative Risk Assessment
---------------------------

Risk Calculation Methodology
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The system uses a sophisticated risk calculation model that considers:

**Attack Success Probability Factors**

* **Threat Actor Sophistication**: Technical capabilities and resources
* **Target Asset Security Posture**: Security controls and configuration
* **Attack Path Complexity**: Number of hops and security boundaries
* **Monitoring Coverage**: Detection capabilities and response time
* **Historical Attack Data**: Success rates for similar scenarios

**Financial Impact Calculation**

.. code-block:: python

    # Financial impact assessment
    financial_impact = client.threat_modeling.assess_financial_impact(
        affected_assets=["database_001", "web_server_001"],
        attack_scenario="data_breach",
        business_context={
            "annual_revenue": 100000000,  # $100M
            "customer_count": 50000,
            "data_sensitivity": "PII",
            "regulatory_requirements": ["GDPR", "CCPA"],
            "business_criticality": "high"
        }
    )
    
    print(f"Direct Costs: ${financial_impact.direct_costs:,.0f}")
    print(f"Indirect Costs: ${financial_impact.indirect_costs:,.0f}")
    print(f"Regulatory Fines: ${financial_impact.regulatory_fines:,.0f}")
    print(f"Business Disruption: ${financial_impact.business_disruption:,.0f}")
    print(f"Total Impact: ${financial_impact.total_impact:,.0f}")

**Risk Score Calculation**

The overall risk score is calculated using:

.. math::

    Risk Score = (Threat Likelihood × Asset Value × Vulnerability Score) / Control Effectiveness

Where:
- **Threat Likelihood**: Probability of attack success (0.0 - 1.0)
- **Asset Value**: Business criticality and data sensitivity (0.0 - 1.0)
- **Vulnerability Score**: Security weaknesses and exposure (0.0 - 1.0)
- **Control Effectiveness**: Security controls and monitoring (0.0 - 1.0)

Compliance Impact Analysis
-------------------------

Regulatory Compliance Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The system automatically identifies potential regulatory violations:

**GDPR (General Data Protection Regulation)**

.. code-block:: python

    # GDPR compliance assessment
    gdpr_impact = client.threat_modeling.assess_gdpr_impact(
        affected_assets=["customer_database", "web_application"],
        data_types=["personal_data", "sensitive_personal_data"],
        data_subjects_affected=10000,
        breach_scenario="unauthorized_access"
    )
    
    print(f"GDPR Violation Risk: {gdpr_impact.violation_probability:.2%}")
    print(f"Potential Fine Range: €{gdpr_impact.min_fine:,.0f} - €{gdpr_impact.max_fine:,.0f}")
    print(f"Notification Requirements: {gdpr_impact.notification_timeline}")
    print(f"Data Subject Rights Impact: {gdpr_impact.data_subject_impact}")

**HIPAA (Health Insurance Portability and Accountability Act)**

.. code-block:: python

    # HIPAA compliance assessment
    hipaa_impact = client.threat_modeling.assess_hipaa_impact(
        affected_assets=["patient_records", "billing_system"],
        phi_records_affected=5000,
        breach_type="unauthorized_disclosure"
    )
    
    print(f"HIPAA Violation Risk: {hipaa_impact.violation_probability:.2%}")
    print(f"OCR Reporting Required: {hipaa_impact.ocr_reporting_required}")
    print(f"Patient Notification Required: {hipaa_impact.patient_notification_required}")
    print(f"Potential Penalties: ${hipaa_impact.potential_penalties:,.0f}")

**SOX (Sarbanes-Oxley Act)**

.. code-block:: python

    # SOX compliance assessment
    sox_impact = client.threat_modeling.assess_sox_impact(
        affected_assets=["financial_reporting_system", "audit_database"],
        financial_data_affected=True,
        internal_controls_compromised=["access_controls", "data_integrity"]
    )
    
    print(f"SOX Violation Risk: {sox_impact.violation_probability:.2%}")
    print(f"Material Weakness: {sox_impact.material_weakness}")
    print(f"CEO/CFO Certification Impact: {sox_impact.certification_impact}")
    print(f"Audit Implications: {sox_impact.audit_implications}")

Mitigation Strategy Generation
-----------------------------

AI-Driven Security Recommendations
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The system generates targeted mitigation strategies based on:

* **Attack Path Analysis**: Specific vulnerabilities in attack chains
* **Threat Actor Techniques**: MITRE ATT&CK technique-specific mitigations
* **Asset Criticality**: Priority-based security control recommendations
* **Cost-Benefit Analysis**: ROI-optimized security investments
* **Implementation Feasibility**: Practical deployment considerations

**Example Mitigation Report**

.. code-block:: python

    # Generate mitigation strategies
    mitigations = client.threat_modeling.generate_mitigations(
        simulation_id="sim_001",
        budget_constraints=500000,  # $500K budget
        implementation_timeline=90,  # 90 days
        risk_tolerance="medium"
    )
    
    for mitigation in mitigations.recommendations:
        print(f"Control: {mitigation.control_name}")
        print(f"MITRE Techniques Addressed: {mitigation.mitre_techniques}")
        print(f"Risk Reduction: {mitigation.risk_reduction:.1%}")
        print(f"Implementation Cost: ${mitigation.cost:,.0f}")
        print(f"Implementation Time: {mitigation.implementation_days} days")
        print(f"ROI: {mitigation.roi:.1f}x")
        print("---")

Advanced Features
----------------

Continuous Risk Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~

Set up continuous monitoring to track risk changes over time:

.. code-block:: python

    # Set up continuous risk monitoring
    monitoring = client.threat_modeling.setup_continuous_monitoring(
        threat_actors=["APT29", "APT28", "FIN7"],
        target_assets=["critical_assets"],
        monitoring_frequency="daily",
        alert_thresholds={
            "risk_score_increase": 0.1,  # 10% increase
            "new_attack_paths": 5,
            "compliance_risk_change": 0.05
        }
    )

Threat Intelligence Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Integrate with external threat intelligence feeds:

.. code-block:: python

    # Integrate threat intelligence
    threat_intel = client.threat_modeling.integrate_threat_intelligence(
        feeds=["MISP", "STIX/TAXII", "commercial_feeds"],
        update_frequency="hourly",
        threat_actor_updates=True,
        technique_updates=True,
        ioc_correlation=True
    )

Best Practices
--------------

Threat Modeling Workflow
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Regular Assessment**: Run threat modeling assessments quarterly
2. **Scenario Planning**: Model multiple threat actor scenarios
3. **Stakeholder Involvement**: Include business stakeholders in risk discussions
4. **Mitigation Tracking**: Track implementation of recommended controls
5. **Continuous Improvement**: Update threat models based on new intelligence

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

* **Batch Processing**: Group similar simulations for efficiency
* **Caching**: Leverage cached results for repeated scenarios
* **Incremental Updates**: Update only changed assets and relationships
* **Parallel Processing**: Run multiple simulations concurrently

For detailed API reference and advanced configuration options, see the :doc:`../api/threat-modeling` documentation.
