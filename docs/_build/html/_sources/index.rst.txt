Blast-Radius Security Tool Documentation
==========================================

Welcome to the comprehensive documentation for the Blast-Radius Security Tool, a cutting-edge security platform designed for purple teams, SOC operators, security architects, and red teamers.

.. image:: _static/logo.png
   :alt: Blast-Radius Security Tool Logo
   :align: center
   :width: 300px

Overview
--------

The Blast-Radius Security Tool is an enterprise-grade security platform that provides comprehensive attack path analysis, asset discovery, and blast radius calculation with MITRE ATT&CK framework integration. Built for security teams, it enables proactive threat modeling and incident response.

Key Features
~~~~~~~~~~~~

* **🕸️ Attack Path Analysis**: Advanced graph-based attack path discovery with MITRE ATT&CK integration
* **💥 Blast Radius Calculation**: Real-time impact assessment and cascading effect analysis
* **🔍 Asset Discovery**: Multi-cloud asset discovery with comprehensive metadata collection
* **🛡️ Robust Asset Management**: Enterprise-grade asset management with audit trails and soft-delete
* **📊 Risk Assessment**: Comprehensive risk scoring with business criticality and compliance impact
* **🎭 Attack Scenario Modeling**: Threat actor profiling and multi-vector attack simulation
* **📋 MITRE ATT&CK Integration**: Complete framework coverage with STIX 2.0/2.1 support and real-time correlation
* **🔒 Enterprise Security**: Role-based access control, audit logging, and data retention policies
* **🧠 Advanced Threat Modeling**: Quantitative risk assessment with success probability modeling
* **⚡ Performance Optimized**: Sub-second analysis for 10M+ node graphs with intelligent caching
* **🛡️ Security Framework**: Comprehensive vulnerability protection and penetration testing validation
* **🏗️ Production Ready**: Enterprise-grade robustness with fault tolerance and automated deployment
* **📊 Advanced Analytics**: Real-time threat intelligence correlation and behavioral analysis

Target Users
~~~~~~~~~~~~

* :user-role:`SOC Operators` - Real-time monitoring and incident response
* :user-role:`Security Architects` - Risk assessment and security design
* :user-role:`Red Team Members` - Attack simulation and path discovery
* :user-role:`Purple Team Members` - Collaborative security testing and validation

Quick Start
-----------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   installation
   configuration
   quick-start-guide

User Guides
-----------

Role-specific documentation for different user types:

.. toctree::
   :maxdepth: 2
   :caption: User Guides

   user-guides/index

API Documentation
-----------------

Comprehensive API reference and integration guides:

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/index

Technical Documentation
-----------------------

In-depth technical information for developers and system administrators:

.. toctree::
   :maxdepth: 2
   :caption: Technical Docs

   technical/index

Development
-----------

Information for contributors and developers:

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/setup
   development/workflow
   development/testing
   development/contributing
   development/code-standards

Features by Use Case
--------------------

Detailed documentation for each major use case:

.. toctree::
   :maxdepth: 2
   :caption: Use Cases

   use-cases/authentication-acl
   use-cases/asset-discovery
   use-cases/attack-path-analysis
   use-cases/monitoring-dashboard
   use-cases/threat-intelligence
   use-cases/automated-remediation
   use-cases/multi-cloud-integration
   use-cases/servicenow-integration

Security & Compliance
---------------------

Security considerations and compliance information:

.. toctree::
   :maxdepth: 2
   :caption: Security

   security/index
   security/framework
   security/access-control
   security/data-protection
   security/audit-logging
   security/compliance
   security/best-practices
   security/vulnerability-management
   security/penetration-testing

Performance & Scalability
-------------------------

Performance optimization and scalability documentation:

.. toctree::
   :maxdepth: 2
   :caption: Performance

   performance/index
   performance/optimization
   performance/scalability
   performance/monitoring
   performance/benchmarks

Testing & Quality Assurance
---------------------------

Comprehensive testing documentation and quality assurance:

.. toctree::
   :maxdepth: 2
   :caption: Testing

   testing/index
   testing/unit-tests
   testing/integration-tests
   testing/api-tests
   testing/security-tests
   testing/performance-tests
   testing/test-automation

Troubleshooting
---------------

Common issues and solutions:

.. toctree::
   :maxdepth: 2
   :caption: Troubleshooting

   troubleshooting/common-issues
   troubleshooting/performance
   troubleshooting/integration-issues
   troubleshooting/faq

Release Notes
-------------

.. toctree::
   :maxdepth: 1
   :caption: Releases

   releases/changelog
   releases/migration-guides
   releases/roadmap

Support
-------

* **GitHub Issues**: `Report bugs and request features <https://github.com/forkrul/blast-radius/issues>`_
* **Documentation**: This comprehensive guide
* **Community**: Join our community discussions

License
-------

This project is licensed under the MIT License. See the `LICENSE <https://github.com/forkrul/blast-radius/blob/master/LICENSE>`_ file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

.. note::
   This documentation is continuously updated. For the latest information, 
   please refer to the `GitHub repository <https://github.com/forkrul/blast-radius>`_.

.. warning::
   This tool is designed for authorized security testing and monitoring only. 
   Ensure you have proper authorization before using it in any environment.
