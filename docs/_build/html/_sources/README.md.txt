# Blast-Radius Security Tool Documentation

This directory contains the comprehensive documentation for the Blast-Radius Security Tool, built with Sphinx and featuring Mermaid diagrams for visual documentation.

## 📚 Documentation Structure

```
docs/
├── api/                    # API Reference Documentation
│   ├── index.rst          # API overview and navigation
│   └── attack-path-analysis.rst  # Attack path API reference
├── user-guides/           # User Guides for Different Roles
│   ├── index.rst          # User guides overview
│   └── attack-path-analysis.rst  # Attack path user guide
├── technical/             # Technical Documentation
│   ├── index.rst          # Technical docs overview
│   ├── attack-path-architecture.rst  # System architecture
│   ├── attack-path-flows.rst         # Process flows and diagrams
│   ├── database-design.rst           # Database schema and design
│   └── product-requirements.rst      # Product requirements document
├── releases/              # Release Documentation
│   └── changelog.rst      # Comprehensive changelog
├── _static/               # Static assets (CSS, JS, images)
├── _templates/            # Custom Sphinx templates
└── conf.py               # Sphinx configuration
```

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- pip
- (Optional) Docker and Docker Compose

### Local Development

1. **Install dependencies:**
   ```bash
   cd docs
   pip install -r requirements.txt
   ```

2. **Build documentation:**
   ```bash
   ./build.sh build
   ```

3. **Serve documentation locally:**
   ```bash
   ./build.sh serve
   ```

4. **Live reload development:**
   ```bash
   ./build.sh live
   ```

### Docker Development

1. **Build and serve with Docker:**
   ```bash
   cd docs
   docker-compose up docs
   ```

2. **Development with live reload:**
   ```bash
   docker-compose up docs-dev
   ```

3. **Build only:**
   ```bash
   docker-compose run --rm docs-builder
   ```

## 🔧 Build Commands

The `build.sh` script provides comprehensive build and development commands:

### Basic Commands

```bash
# Build HTML documentation
./build.sh build

# Clean build directory
./build.sh clean

# Build all formats and run checks
./build.sh all

# Show help
./build.sh help
```

### Development Commands

```bash
# Serve documentation (default port: 8000)
./build.sh serve

# Serve on custom port
./build.sh serve 9000

# Live reload server (default port: 8080)
./build.sh live

# Live reload on custom port
./build.sh live 9090
```

### Quality Assurance

```bash
# Check for broken links
./build.sh linkcheck

# Generate coverage report
./build.sh coverage

# Build PDF documentation
./build.sh pdf
```

### Docker Commands

```bash
# Build with Docker
./build.sh docker-build

# Serve with Docker
./build.sh docker-serve
```

## 🎨 Features

### Mermaid Diagrams

The documentation includes comprehensive Mermaid diagrams for:

- **System Architecture**: Multi-layered architecture visualization
- **Database Schema**: Entity relationship diagrams
- **Process Flows**: Attack path analysis workflows
- **Decision Trees**: MITRE ATT&CK mapping logic
- **Risk Assessment**: Multi-factor scoring algorithms

### Interactive Elements

- **Code Examples**: Syntax-highlighted code blocks in multiple languages
- **API Documentation**: Complete REST API reference with examples
- **Cross-References**: Comprehensive linking between related topics
- **Search Functionality**: Full-text search across all documentation

### Responsive Design

- **Mobile-Friendly**: Responsive design for all device sizes
- **Dark/Light Theme**: Theme switching support
- **Print-Friendly**: Optimized for PDF generation and printing

## 📖 Documentation Sections

### API Reference (`api/`)

Complete REST API documentation including:

- **Attack Path Analysis API**: 7 comprehensive endpoints
- **Request/Response Models**: Detailed schemas with examples
- **Authentication**: JWT-based authentication guide
- **Rate Limiting**: Usage limits and best practices
- **Error Handling**: Comprehensive error codes and responses

### User Guides (`user-guides/`)

Role-based documentation for:

- **SOC Operators**: Real-time monitoring and incident response
- **Security Architects**: Infrastructure design and validation
- **Red Team Members**: Attack simulation and penetration testing
- **Purple Team Members**: Collaborative security testing
- **Administrators**: System configuration and management

### Technical Documentation (`technical/`)

In-depth technical documentation covering:

- **System Architecture**: Multi-layered design and components
- **Attack Path Flows**: Complete workflow diagrams
- **Database Design**: Schema, indexes, and optimization
- **Product Requirements**: Comprehensive PRD with specifications
- **Performance Optimization**: Caching, scaling, and tuning

## 🔍 Search and Navigation

### Search Features

- **Full-Text Search**: Search across all documentation content
- **Faceted Search**: Filter by document type, section, or topic
- **Autocomplete**: Smart search suggestions
- **Highlighting**: Search term highlighting in results

### Navigation

- **Hierarchical Structure**: Logical content organization
- **Breadcrumbs**: Clear navigation path indication
- **Cross-References**: Extensive internal linking
- **Table of Contents**: Expandable TOC for each page

## 🛠️ Development

### Adding New Documentation

1. **Create new RST file** in appropriate directory
2. **Add to index.rst** in the same directory
3. **Build and test** locally
4. **Commit and push** changes

### Mermaid Diagrams

Add Mermaid diagrams using the `.. mermaid::` directive:

```rst
.. mermaid::

    graph TD
        A[Start] --> B{Decision}
        B -->|Yes| C[Action 1]
        B -->|No| D[Action 2]
```

### Code Examples

Include code examples with syntax highlighting:

```rst
.. code-block:: python

    # Python example
    from blast_radius import BlastRadiusClient
    
    client = BlastRadiusClient(api_token="your-token")
    paths = client.attack_paths.analyze(source="web_server")
```

### Cross-References

Link to other documentation sections:

```rst
See :doc:`attack-path-analysis` for detailed API documentation.
Reference :ref:`installation-guide` for setup instructions.
```

## 📊 Analytics and Monitoring

### Documentation Metrics

- **Page Views**: Track popular documentation sections
- **Search Queries**: Monitor common search terms
- **User Feedback**: Collect user satisfaction ratings
- **Link Analysis**: Identify broken or outdated links

### Quality Assurance

- **Automated Testing**: Link checking and validation
- **Spell Checking**: Automated spell checking
- **Style Guide**: Consistent formatting and style
- **Review Process**: Peer review for all changes

## 🚀 Deployment

### Local Deployment

The documentation can be served locally using:

```bash
# Python HTTP server
./build.sh serve

# Live reload for development
./build.sh live
```

### Production Deployment

For production deployment:

1. **Build documentation:**
   ```bash
   ./build.sh build
   ```

2. **Deploy to web server:**
   ```bash
   # Copy _build/html/* to web server
   rsync -av _build/html/ user@server:/var/www/docs/
   ```

3. **Configure web server** (nginx, Apache, etc.)

### Docker Deployment

Use Docker for containerized deployment:

```bash
# Build and run container
docker-compose up docs

# Access at http://localhost:8080
```

### Continuous Integration

The documentation can be automatically built and deployed using CI/CD:

```yaml
# Example GitHub Actions workflow
name: Build Documentation
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: pip install -r docs/requirements.txt
      - name: Build documentation
        run: cd docs && ./build.sh build
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/_build/html
```

## 📞 Support

### Getting Help

- **Documentation Issues**: Create GitHub issue with `documentation` label
- **Build Problems**: Check build logs and requirements
- **Feature Requests**: Submit enhancement requests
- **Community**: Join documentation discussions

### Contributing

1. **Fork repository** and create feature branch
2. **Make changes** following style guide
3. **Test locally** using build script
4. **Submit pull request** with clear description
5. **Address feedback** from reviewers

### Style Guide

- **RST Format**: Use reStructuredText for all documentation
- **Consistent Headers**: Follow header hierarchy (=, -, ~, ^)
- **Code Examples**: Include working examples for all features
- **Cross-References**: Link related content extensively
- **Mermaid Diagrams**: Use for complex workflows and architecture

This comprehensive documentation system provides everything needed for effective user onboarding, API integration, and technical understanding of the Blast-Radius Security Tool.
