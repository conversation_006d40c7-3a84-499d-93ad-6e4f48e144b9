Quick Start Guide
==================

Welcome to the Blast-Radius Security Tool! This guide will help you get up and running quickly, from initial setup to performing your first attack path analysis.

Overview
--------

The Blast-Radius Security Tool is designed to provide immediate value for security teams. This quick start guide covers:

* **5-minute setup** using Docker
* **Initial configuration** and user setup
* **First attack path analysis**
* **Basic dashboard navigation**
* **Essential security features**

Prerequisites
-------------

Before starting, ensure you have:

* **Docker and Docker Compose** installed
* **8GB+ RAM** available
* **Internet connection** for downloading images
* **Modern web browser** (Chrome, Firefox, Safari, Edge)

Quick Installation
------------------

Step 1: Get the Code
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius

Step 2: Start the Platform
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Start all services (this may take 2-3 minutes on first run)
   docker-compose up -d
   
   # Check that all services are running
   docker-compose ps

You should see all services in "Up" status:

.. code-block:: text

   NAME                    STATUS
   blast-radius-backend    Up
   blast-radius-frontend   Up
   blast-radius-postgres   Up
   blast-radius-redis      Up
   blast-radius-neo4j      Up

Step 3: Access the Platform
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Open your web browser and navigate to:

* **Main Dashboard**: http://localhost:3000
* **API Documentation**: http://localhost:8000/docs

**Default Login Credentials:**

* **Email**: <EMAIL>
* **Password**: BlastRadius2024!

.. warning::
   **Security Notice**: Change the default password immediately after first login!

First Login and Setup
---------------------

Initial Login
~~~~~~~~~~~~~

1. Navigate to http://localhost:3000
2. Enter the default credentials
3. Click "Sign In"
4. You'll be prompted to change your password

.. image:: _static/screenshots/login-screen.png
   :alt: Login Screen
   :align: center
   :width: 600px

Password Change
~~~~~~~~~~~~~~~

1. Enter a strong new password (minimum 12 characters)
2. Confirm the password
3. Click "Update Password"
4. You'll be redirected to the main dashboard

Dashboard Overview
~~~~~~~~~~~~~~~~~~

After login, you'll see the main dashboard with:

* **Navigation Menu** - Access to all platform features
* **Quick Stats** - Overview of your security posture
* **Recent Activity** - Latest security events and alerts
* **Action Items** - Tasks requiring attention

.. image:: _static/screenshots/main-dashboard.png
   :alt: Main Dashboard
   :align: center
   :width: 800px

Basic Configuration
-------------------

User Profile Setup
~~~~~~~~~~~~~~~~~~

1. Click your profile icon in the top-right corner
2. Select "Profile Settings"
3. Update your information:
   
   * **Full Name**
   * **Email Address**
   * **Time Zone**
   * **Notification Preferences**

4. Click "Save Changes"

Multi-Factor Authentication (Recommended)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. Go to "Profile Settings" → "Security"
2. Click "Enable MFA"
3. Scan the QR code with your authenticator app
4. Enter the verification code
5. Save your backup codes in a secure location

.. note::
   We recommend using Google Authenticator, Authy, or Microsoft Authenticator.

Your First Attack Path Analysis
-------------------------------

Step 1: Add Sample Assets
~~~~~~~~~~~~~~~~~~~~~~~~~

For this quick start, we'll use sample data:

1. Navigate to "Assets" → "Discovery"
2. Click "Load Sample Data"
3. Select "Small Enterprise Environment"
4. Click "Import"

This creates a sample environment with:

* **50 servers** (web, database, domain controllers)
* **200 user accounts** (including privileged accounts)
* **Network segments** (DMZ, internal, management)
* **Security controls** (firewalls, antivirus, monitoring)

Step 2: Run Attack Path Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. Navigate to "Analysis" → "Attack Paths"
2. Click "New Analysis"
3. Configure the analysis:
   
   * **Source**: Select "External Attacker"
   * **Target**: Select "Domain Controller"
   * **Max Hops**: 5
   * **Analysis Type**: "Comprehensive"

4. Click "Start Analysis"

The analysis will complete in 10-30 seconds.

Step 3: View Results
~~~~~~~~~~~~~~~~~~~~

The results page shows:

* **Attack Path Visualization** - Interactive graph of potential paths
* **Risk Assessment** - Severity and likelihood scores
* **Recommended Actions** - Prioritized remediation steps
* **Detailed Report** - Technical analysis and evidence

.. image:: _static/screenshots/attack-path-results.png
   :alt: Attack Path Analysis Results
   :align: center
   :width: 800px

Understanding the Visualization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The attack path graph uses:

* **Green Nodes** - Secure assets with good controls
* **Yellow Nodes** - Assets with moderate risk
* **Red Nodes** - High-risk assets requiring attention
* **Arrows** - Potential attack vectors between assets
* **Thick Lines** - High-probability attack paths

Interactive Features:

* **Zoom and Pan** - Navigate large graphs
* **Click Nodes** - View detailed asset information
* **Filter Paths** - Focus on specific risk levels
* **Export** - Save results as PDF or image

Essential Features Tour
-----------------------

Real-Time Monitoring
~~~~~~~~~~~~~~~~~~~~

1. Navigate to "Monitoring" → "Live Dashboard"
2. View real-time security events and alerts
3. Configure alert thresholds and notifications

Key monitoring features:

* **Security Event Stream** - Live feed of security events
* **Attack Path Changes** - Real-time updates to risk posture
* **System Health** - Platform and integration status
* **Performance Metrics** - Response times and throughput

Asset Management
~~~~~~~~~~~~~~~~

1. Navigate to "Assets" → "Inventory"
2. Browse your asset inventory
3. View asset details and relationships

Asset management features:

* **Automated Discovery** - Cloud and on-premises assets
* **Relationship Mapping** - Dependencies and connections
* **Configuration Tracking** - Changes and drift detection
* **Risk Scoring** - Individual asset risk assessment

User and Role Management
~~~~~~~~~~~~~~~~~~~~~~~~

1. Navigate to "Administration" → "Users"
2. Create additional user accounts
3. Assign appropriate roles

Available roles:

* **SOC Operator** - Monitoring and incident response
* **Security Architect** - Risk assessment and design
* **Red Team Member** - Attack simulation and testing
* **Purple Team Member** - Collaborative security testing
* **Administrator** - Full platform access

Threat Intelligence
~~~~~~~~~~~~~~~~~~~

1. Navigate to "Intelligence" → "Threat Feeds"
2. Configure threat intelligence sources
3. View IOC correlations and threat actor information

Integration Setup
-----------------

Cloud Provider Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

To connect your cloud environments:

1. Navigate to "Integrations" → "Cloud Providers"
2. Select your cloud provider (AWS, Azure, GCP)
3. Follow the setup wizard to configure API access
4. Test the connection and start asset discovery

ServiceNow Integration
~~~~~~~~~~~~~~~~~~~~~~

To integrate with ServiceNow CMDB:

1. Navigate to "Integrations" → "ServiceNow"
2. Enter your ServiceNow instance details
3. Configure field mappings
4. Enable bi-directional synchronization

Next Steps
----------

Now that you have the basics working, consider these next steps:

Immediate Actions
~~~~~~~~~~~~~~~~~

1. **Change default passwords** for all accounts
2. **Enable MFA** for all users
3. **Configure real integrations** with your cloud providers
4. **Set up proper SSL certificates** for production use
5. **Configure backup procedures** for your data

Learning Resources
~~~~~~~~~~~~~~~~~~

* **User Guides** - Role-specific documentation:
  
  * :doc:`user-guides/soc-operators`
  * :doc:`user-guides/security-architects`
  * :doc:`user-guides/red-team-members`
  * :doc:`user-guides/purple-team-members`

* **Technical Documentation**:
  
  * :doc:`technical/architecture`
  * :doc:`api/authentication`
  * :doc:`security/best-practices`

* **Use Case Guides**:
  
  * :doc:`use-cases/attack-path-analysis`
  * :doc:`use-cases/threat-intelligence`
  * :doc:`use-cases/multi-cloud-integration`

Advanced Configuration
~~~~~~~~~~~~~~~~~~~~~~

For production deployments:

1. **Review** :doc:`security/best-practices`
2. **Configure** :doc:`technical/monitoring` and alerting
3. **Set up** :doc:`technical/deployment` for high availability
4. **Implement** proper backup and disaster recovery

Common Quick Start Issues
-------------------------

Platform Won't Start
~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Docker containers fail to start or exit immediately

**Solutions**:

.. code-block:: bash

   # Check system resources
   docker system df
   docker system prune  # If low on space
   
   # Check logs
   docker-compose logs backend
   docker-compose logs frontend
   
   # Restart services
   docker-compose down
   docker-compose up -d

Can't Access Web Interface
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Browser shows "connection refused" or timeout

**Solutions**:

1. **Check if services are running**:
   
   .. code-block:: bash
   
      docker-compose ps

2. **Verify port availability**:
   
   .. code-block:: bash
   
      netstat -tulpn | grep :3000
      netstat -tulpn | grep :8000

3. **Check firewall settings** (if applicable)

Login Issues
~~~~~~~~~~~~

**Symptoms**: Invalid credentials or login failures

**Solutions**:

1. **Verify default credentials** are correct
2. **Reset admin password**:
   
   .. code-block:: bash
   
      docker-compose exec backend python -m app.cli reset-admin-password

3. **Check application logs**:
   
   .. code-block:: bash
   
      docker-compose logs backend | grep -i auth

Performance Issues
~~~~~~~~~~~~~~~~~~

**Symptoms**: Slow response times or timeouts

**Solutions**:

1. **Increase system resources** (RAM, CPU)
2. **Check database performance**:
   
   .. code-block:: bash
   
      docker-compose exec postgres pg_stat_activity

3. **Monitor resource usage**:
   
   .. code-block:: bash
   
      docker stats

Getting Help
------------

If you encounter issues not covered here:

1. **Check the logs** for error messages
2. **Review** :doc:`troubleshooting/common-issues`
3. **Search existing issues** on GitHub
4. **Create a new issue** with detailed information

**Support Channels**:

* **GitHub Issues**: https://github.com/forkrul/blast-radius/issues
* **Documentation**: This comprehensive guide
* **Community Forum**: https://community.blastradius.security

**Enterprise Support**:

For enterprise customers, contact <EMAIL> for priority support and professional services.

Congratulations!
----------------

You've successfully set up the Blast-Radius Security Tool and performed your first attack path analysis. You're now ready to explore the platform's advanced features and integrate it with your existing security infrastructure.

The platform provides powerful capabilities for understanding and mitigating security risks across your environment. Take time to explore the different user guides and configure the platform to meet your specific security requirements.

.. tip::
   **Pro Tip**: Start with small, focused analyses and gradually expand to larger environments as you become more familiar with the platform's capabilities.
