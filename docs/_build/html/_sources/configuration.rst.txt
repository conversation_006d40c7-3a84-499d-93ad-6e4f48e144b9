Configuration Guide
===================

This comprehensive guide covers all configuration options for the Blast-Radius Security Tool, including environment variables, security settings, integrations, and deployment configurations.

Overview
--------

The Blast-Radius Security Tool uses a hierarchical configuration system that prioritizes settings in the following order:

1. **Environment Variables** (highest priority)
2. **Configuration Files** (.env files)
3. **Database Settings** (runtime configuration)
4. **Default Values** (lowest priority)

Configuration is managed through Pydantic Settings, providing type validation, automatic environment variable parsing, and comprehensive error handling.

Core Configuration
------------------

Application Settings
~~~~~~~~~~~~~~~~~~~~

**Basic Application Configuration:**

.. code-block:: bash

   # Application Identity
   PROJECT_NAME="Blast-Radius Security Tool"
   VERSION="1.0.0"
   DESCRIPTION="Comprehensive security platform for attack path analysis"
   
   # Environment Configuration
   ENVIRONMENT="production"  # development, staging, production
   DEBUG=false
   LOG_LEVEL="info"  # debug, info, warning, error, critical
   
   # Server Configuration
   HOST="0.0.0.0"
   PORT=8000
   WORKERS=4  # Number of worker processes (production)
   WORKER_CLASS="uvicorn.workers.UvicornWorker"
   
   # API Configuration
   API_V1_STR="/api/v1"
   DOCS_URL="/docs"  # Set to null to disable in production
   REDOC_URL="/redoc"  # Set to null to disable in production
   OPENAPI_URL="/openapi.json"

**Performance Settings:**

.. code-block:: bash

   # Request Handling
   MAX_REQUEST_SIZE=10485760  # 10MB
   REQUEST_TIMEOUT=30  # seconds
   KEEPALIVE_TIMEOUT=5  # seconds
   
   # Concurrency Settings
   MAX_CONCURRENT_REQUESTS=1000
   RATE_LIMIT_REQUESTS=100  # requests per minute per IP
   RATE_LIMIT_WINDOW=60  # seconds

Database Configuration
~~~~~~~~~~~~~~~~~~~~~~

**PostgreSQL Settings:**

.. code-block:: bash

   # Primary Database
   DATABASE_URL="postgresql://blast_user:blast_pass@localhost:5432/blast_radius"
   DATABASE_POOL_SIZE=20
   DATABASE_MAX_OVERFLOW=30
   DATABASE_POOL_TIMEOUT=30
   DATABASE_POOL_RECYCLE=3600
   DATABASE_ECHO=false  # Set to true for SQL debugging
   
   # Connection Settings
   DATABASE_CONNECT_TIMEOUT=10
   DATABASE_COMMAND_TIMEOUT=60
   DATABASE_SSL_MODE="prefer"  # disable, allow, prefer, require

**Redis Configuration:**

.. code-block:: bash

   # Cache and Session Store
   REDIS_URL="redis://localhost:6379/0"
   REDIS_POOL_SIZE=10
   REDIS_POOL_MAX_CONNECTIONS=50
   REDIS_SOCKET_TIMEOUT=5
   REDIS_SOCKET_CONNECT_TIMEOUT=5
   REDIS_RETRY_ON_TIMEOUT=true
   
   # Cache Settings
   CACHE_TTL=3600  # Default cache TTL in seconds
   SESSION_TTL=1800  # Session TTL in seconds

**Neo4j Configuration:**

.. code-block:: bash

   # Graph Database
   NEO4J_URI="bolt://localhost:7687"
   NEO4J_USER="neo4j"
   NEO4J_PASSWORD="blast_neo4j_password"
   NEO4J_DATABASE="neo4j"
   NEO4J_MAX_CONNECTION_LIFETIME=3600
   NEO4J_MAX_CONNECTION_POOL_SIZE=100
   NEO4J_CONNECTION_ACQUISITION_TIMEOUT=60

Security Configuration
----------------------

Authentication Settings
~~~~~~~~~~~~~~~~~~~~~~~

**JWT Configuration:**

.. code-block:: bash

   # JWT Settings
   JWT_SECRET_KEY="your-super-secret-jwt-key-256-bits-minimum"
   JWT_ALGORITHM="HS256"
   JWT_EXPIRE_MINUTES=30
   JWT_REFRESH_EXPIRE_DAYS=7
   JWT_ISSUER="blast-radius-security-tool"
   JWT_AUDIENCE="blast-radius-users"

**Session Configuration:**

.. code-block:: bash

   # Session Management
   SESSION_COOKIE_NAME="blast_radius_session"
   SESSION_COOKIE_SECURE=true  # HTTPS only
   SESSION_COOKIE_HTTPONLY=true
   SESSION_COOKIE_SAMESITE="strict"  # strict, lax, none
   SESSION_COOKIE_MAX_AGE=1800  # 30 minutes
   
   # Session Security
   SESSION_REGENERATE_ON_LOGIN=true
   SESSION_INVALIDATE_ON_LOGOUT=true
   MAX_CONCURRENT_SESSIONS=3  # Per user

**Multi-Factor Authentication:**

.. code-block:: bash

   # MFA Settings
   MFA_ENABLED=true
   MFA_REQUIRE_FOR_ALL=false  # Require MFA for all users
   MFA_REQUIRE_FOR_ADMINS=true  # Require MFA for admin users
   MFA_BACKUP_CODES_COUNT=10
   MFA_TOTP_ISSUER="Blast-Radius Security Tool"
   MFA_TOTP_PERIOD=30  # seconds
   MFA_TOTP_DIGITS=6

Encryption Settings
~~~~~~~~~~~~~~~~~~~

**Data Encryption:**

.. code-block:: bash

   # Encryption Keys
   SECRET_KEY="your-super-secret-key-for-general-encryption"
   FIELD_ENCRYPTION_KEY="your-field-level-encryption-key"
   
   # Encryption Settings
   ENCRYPTION_ALGORITHM="AES-256-GCM"
   PASSWORD_HASH_ALGORITHM="bcrypt"
   PASSWORD_HASH_ROUNDS=12

**SSL/TLS Configuration:**

.. code-block:: bash

   # SSL Settings
   SSL_CERT_PATH="/path/to/ssl/cert.pem"
   SSL_KEY_PATH="/path/to/ssl/key.pem"
   SSL_CA_CERT_PATH="/path/to/ssl/ca-cert.pem"
   SSL_VERIFY_MODE="CERT_REQUIRED"  # CERT_NONE, CERT_OPTIONAL, CERT_REQUIRED

Access Control Settings
~~~~~~~~~~~~~~~~~~~~~~~

**CORS Configuration:**

.. code-block:: bash

   # Cross-Origin Resource Sharing
   CORS_ORIGINS='["https://your-domain.com", "https://app.your-domain.com"]'
   CORS_ALLOW_CREDENTIALS=true
   CORS_ALLOW_METHODS='["GET", "POST", "PUT", "DELETE", "OPTIONS"]'
   CORS_ALLOW_HEADERS='["*"]'
   CORS_MAX_AGE=86400  # 24 hours

**Rate Limiting:**

.. code-block:: bash

   # Rate Limiting
   RATE_LIMIT_ENABLED=true
   RATE_LIMIT_REQUESTS_PER_MINUTE=100
   RATE_LIMIT_BURST=20
   RATE_LIMIT_STORAGE="redis"  # memory, redis
   
   # API-specific rate limits
   AUTH_RATE_LIMIT=10  # Login attempts per minute
   API_RATE_LIMIT=1000  # API calls per minute

Integration Configuration
-------------------------

Cloud Provider Settings
~~~~~~~~~~~~~~~~~~~~~~~

**AWS Integration:**

.. code-block:: bash

   # AWS Configuration
   AWS_ACCESS_KEY_ID="your-aws-access-key"
   AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
   AWS_DEFAULT_REGION="us-east-1"
   AWS_SESSION_TOKEN=""  # For temporary credentials
   
   # AWS Services
   AWS_ENABLE_EC2=true
   AWS_ENABLE_S3=true
   AWS_ENABLE_RDS=true
   AWS_ENABLE_IAM=true
   AWS_ENABLE_VPC=true
   
   # AWS Polling Settings
   AWS_POLL_INTERVAL=300  # 5 minutes
   AWS_MAX_RETRIES=3
   AWS_RETRY_DELAY=5

**Azure Integration:**

.. code-block:: bash

   # Azure Configuration
   AZURE_CLIENT_ID="your-azure-client-id"
   AZURE_CLIENT_SECRET="your-azure-client-secret"
   AZURE_TENANT_ID="your-azure-tenant-id"
   AZURE_SUBSCRIPTION_ID="your-azure-subscription-id"
   
   # Azure Services
   AZURE_ENABLE_COMPUTE=true
   AZURE_ENABLE_STORAGE=true
   AZURE_ENABLE_NETWORK=true
   AZURE_ENABLE_SECURITY=true
   
   # Azure Polling Settings
   AZURE_POLL_INTERVAL=300  # 5 minutes
   AZURE_MAX_RETRIES=3

**Google Cloud Platform:**

.. code-block:: bash

   # GCP Configuration
   GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
   GCP_PROJECT_ID="your-gcp-project-id"
   GCP_DEFAULT_REGION="us-central1"
   
   # GCP Services
   GCP_ENABLE_COMPUTE=true
   GCP_ENABLE_STORAGE=true
   GCP_ENABLE_NETWORK=true
   GCP_ENABLE_IAM=true
   
   # GCP Polling Settings
   GCP_POLL_INTERVAL=300  # 5 minutes

ServiceNow Integration
~~~~~~~~~~~~~~~~~~~~~~

**ServiceNow CMDB:**

.. code-block:: bash

   # ServiceNow Configuration
   SERVICENOW_INSTANCE="your-instance.service-now.com"
   SERVICENOW_USERNAME="integration-user"
   SERVICENOW_PASSWORD="integration-password"
   SERVICENOW_API_VERSION="v1"
   SERVICENOW_TIMEOUT=30
   
   # CMDB Sync Settings
   SERVICENOW_SYNC_ENABLED=true
   SERVICENOW_SYNC_INTERVAL=3600  # 1 hour
   SERVICENOW_BATCH_SIZE=100
   
   # Incident Management
   SERVICENOW_CREATE_INCIDENTS=true
   SERVICENOW_INCIDENT_CATEGORY="Security"
   SERVICENOW_INCIDENT_SUBCATEGORY="Attack Path"

Threat Intelligence Settings
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**STIX/TAXII Configuration:**

.. code-block:: bash

   # Threat Intelligence
   THREAT_INTEL_ENABLED=true
   THREAT_INTEL_UPDATE_INTERVAL=3600  # 1 hour
   
   # TAXII Server Configuration
   TAXII_SERVER_URL="https://your-taxii-server.com/taxii2/"
   TAXII_USERNAME="taxii-user"
   TAXII_PASSWORD="taxii-password"
   TAXII_COLLECTION_ID="your-collection-id"
   
   # IOC Processing
   IOC_RETENTION_DAYS=90
   IOC_CONFIDENCE_THRESHOLD=75
   IOC_AUTO_CORRELATION=true

Monitoring and Logging
----------------------

Logging Configuration
~~~~~~~~~~~~~~~~~~~~~

**Application Logging:**

.. code-block:: bash

   # Logging Settings
   LOG_LEVEL="info"  # debug, info, warning, error, critical
   LOG_FORMAT="json"  # text, json
   LOG_FILE_PATH="/var/log/blast-radius/app.log"
   LOG_MAX_SIZE="100MB"
   LOG_BACKUP_COUNT=10
   
   # Structured Logging
   LOG_INCLUDE_TIMESTAMP=true
   LOG_INCLUDE_LEVEL=true
   LOG_INCLUDE_LOGGER_NAME=true
   LOG_INCLUDE_THREAD_ID=true

**Audit Logging:**

.. code-block:: bash

   # Audit Configuration
   AUDIT_ENABLED=true
   AUDIT_LOG_PATH="/var/log/blast-radius/audit.log"
   AUDIT_LOG_ALL_REQUESTS=false
   AUDIT_LOG_SENSITIVE_DATA=false
   
   # Audit Events
   AUDIT_LOGIN_EVENTS=true
   AUDIT_PERMISSION_CHANGES=true
   AUDIT_DATA_ACCESS=true
   AUDIT_CONFIGURATION_CHANGES=true

Monitoring Settings
~~~~~~~~~~~~~~~~~~~

**Health Checks:**

.. code-block:: bash

   # Health Check Configuration
   HEALTH_CHECK_ENABLED=true
   HEALTH_CHECK_INTERVAL=30  # seconds
   HEALTH_CHECK_TIMEOUT=10  # seconds
   
   # Component Health Checks
   HEALTH_CHECK_DATABASE=true
   HEALTH_CHECK_REDIS=true
   HEALTH_CHECK_NEO4J=true
   HEALTH_CHECK_EXTERNAL_APIS=true

**Metrics Collection:**

.. code-block:: bash

   # Metrics Configuration
   METRICS_ENABLED=true
   METRICS_ENDPOINT="/metrics"
   METRICS_INCLUDE_SYSTEM=true
   METRICS_INCLUDE_APPLICATION=true
   
   # Prometheus Integration
   PROMETHEUS_ENABLED=true
   PROMETHEUS_PORT=9090
   PROMETHEUS_NAMESPACE="blast_radius"

Environment-Specific Configuration
----------------------------------

Development Environment
~~~~~~~~~~~~~~~~~~~~~~~

**.env.development:**

.. code-block:: bash

   DEBUG=true
   LOG_LEVEL="debug"
   DATABASE_ECHO=true
   CORS_ORIGINS='["http://localhost:3000"]'
   DOCS_URL="/docs"
   REDOC_URL="/redoc"
   
   # Relaxed security for development
   JWT_EXPIRE_MINUTES=480  # 8 hours
   SESSION_COOKIE_SECURE=false
   MFA_REQUIRE_FOR_ALL=false

Staging Environment
~~~~~~~~~~~~~~~~~~~

**.env.staging:**

.. code-block:: bash

   DEBUG=false
   LOG_LEVEL="info"
   DATABASE_ECHO=false
   CORS_ORIGINS='["https://staging.yourapp.com"]'
   DOCS_URL="/docs"  # Keep docs in staging
   
   # Moderate security for staging
   JWT_EXPIRE_MINUTES=60
   SESSION_COOKIE_SECURE=true
   MFA_REQUIRE_FOR_ADMINS=true

Production Environment
~~~~~~~~~~~~~~~~~~~~~~

**.env.production:**

.. code-block:: bash

   DEBUG=false
   LOG_LEVEL="warning"
   DATABASE_ECHO=false
   CORS_ORIGINS='["https://yourapp.com"]'
   DOCS_URL=null  # Disable docs in production
   REDOC_URL=null
   
   # Maximum security for production
   SESSION_COOKIE_SECURE=true
   MFA_REQUIRE_FOR_ALL=true
   RATE_LIMIT_ENABLED=true
   AUDIT_ENABLED=true

Configuration Validation
-------------------------

The application automatically validates all configuration settings on startup. Invalid configurations will prevent the application from starting and display detailed error messages.

**Common Validation Errors:**

* Invalid database connection strings
* Missing required encryption keys
* Invalid JWT configuration
* Incorrect cloud provider credentials
* Invalid SSL certificate paths

**Configuration Testing:**

.. code-block:: bash

   # Test configuration
   cd backend
   python -m app.cli validate-config
   
   # Test specific components
   python -m app.cli test-database
   python -m app.cli test-redis
   python -m app.cli test-integrations

Best Practices
--------------

Security Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Use strong, unique encryption keys** for each environment
2. **Enable MFA** for all administrative accounts
3. **Regularly rotate** JWT secrets and encryption keys
4. **Use environment variables** instead of hardcoded secrets
5. **Enable audit logging** for all production environments
6. **Implement proper SSL/TLS** configuration
7. **Use least privilege** access for service accounts

Performance Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Tune database connection pools** based on load
2. **Configure appropriate cache TTL** values
3. **Enable compression** for API responses
4. **Use CDN** for static assets
5. **Monitor and adjust** rate limits based on usage
6. **Implement proper indexing** for database queries

Troubleshooting
---------------

For configuration-related issues, check:

1. **Application logs** for validation errors
2. **Environment variable** syntax and values
3. **File permissions** for configuration files
4. **Network connectivity** to external services
5. **SSL certificate** validity and paths

For detailed troubleshooting, see :doc:`troubleshooting/common-issues`.

.. note::
   Configuration changes may require application restart to take effect.
   Some settings can be updated through the admin interface without restart.
