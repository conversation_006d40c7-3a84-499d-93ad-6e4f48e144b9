

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Authentication API &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Attack Path Analysis API" href="attack-path-analysis.html" />
    <link rel="prev" title="API Reference" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">API Reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#api-endpoints">API Endpoints</a><ul class="current">
<li class="toctree-l3 current"><a class="reference internal" href="index.html#authentication-user-management">Authentication &amp; User Management</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Authentication API</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#attack-path-analysis">Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-response-formats">Common Response Formats</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Authentication API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/api/authentication.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="authentication-api">
<h1>Authentication API<a class="headerlink" href="#authentication-api" title="Link to this heading"></a></h1>
<p>The Blast-Radius Security Tool provides comprehensive authentication and authorization capabilities through a RESTful API. This document covers all authentication-related endpoints, security models, and integration patterns.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The authentication system supports:</p>
<ul class="simple">
<li><p><strong>JWT-based authentication</strong> with refresh tokens</p></li>
<li><p><strong>Multi-factor authentication</strong> (MFA) with TOTP</p></li>
<li><p><strong>Single Sign-On (SSO)</strong> integration with SAML and OIDC</p></li>
<li><p><strong>Role-based access control</strong> (RBAC) with fine-grained permissions</p></li>
<li><p><strong>Session management</strong> with configurable timeouts</p></li>
<li><p><strong>Audit logging</strong> for all authentication events</p></li>
</ul>
</section>
<section id="authentication-flow">
<h2>Authentication Flow<a class="headerlink" href="#authentication-flow" title="Link to this heading"></a></h2>
<section id="standard-authentication-flow">
<h3>Standard Authentication Flow<a class="headerlink" href="#standard-authentication-flow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Login Request</strong>: Client submits credentials to <code class="docutils literal notranslate"><span class="pre">/api/v1/auth/login</span></code></p></li>
<li><p><strong>Credential Validation</strong>: Server validates username/password</p></li>
<li><p><strong>MFA Challenge</strong> (if enabled): Server requests MFA token</p></li>
<li><p><strong>Token Generation</strong>: Server generates JWT access and refresh tokens</p></li>
<li><p><strong>Token Response</strong>: Server returns tokens and user information</p></li>
<li><p><strong>API Access</strong>: Client includes access token in subsequent requests</p></li>
<li><p><strong>Token Refresh</strong>: Client uses refresh token to obtain new access tokens</p></li>
</ol>
</section>
</section>
<section id="api-endpoints">
<h2>API Endpoints<a class="headerlink" href="#api-endpoints" title="Link to this heading"></a></h2>
<section id="authentication-endpoints">
<h3>Authentication Endpoints<a class="headerlink" href="#authentication-endpoints" title="Link to this heading"></a></h3>
<section id="login">
<h4>Login<a class="headerlink" href="#login" title="Link to this heading"></a></h4>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/auth/login</span></code></p>
<p>Authenticate user with username/password credentials.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;password&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;securepassword123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;mfa_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123456&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;remember_me&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;access_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;refresh_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;token_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bearer&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_in&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1800</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;user&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-426614174000&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;first_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;John&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;last_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Doe&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;soc_operator&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;view_security_events&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;investigate_incidents&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;mfa_enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;last_login&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="logout">
<h4>Logout<a class="headerlink" href="#logout" title="Link to this heading"></a></h4>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/auth/logout</span></code></p>
<p>Invalidate current session and tokens.</p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">&lt;access_token&gt;</span></code></p>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Successfully logged out&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="token-refresh">
<h4>Token Refresh<a class="headerlink" href="#token-refresh" title="Link to this heading"></a></h4>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/auth/refresh</span></code></p>
<p>Obtain new access token using refresh token.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;refresh_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;access_token&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;token_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bearer&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expires_in&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1800</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="password-management">
<h3>Password Management<a class="headerlink" href="#password-management" title="Link to this heading"></a></h3>
<section id="change-password">
<h4>Change Password<a class="headerlink" href="#change-password" title="Link to this heading"></a></h4>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/auth/change-password</span></code></p>
<p>Change user’s password (requires current password).</p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">&lt;access_token&gt;</span></code></p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;current_password&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;oldpassword123&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;new_password&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;newpassword456&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;confirm_password&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;newpassword456&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Password changed successfully&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="multi-factor-authentication">
<h3>Multi-Factor Authentication<a class="headerlink" href="#multi-factor-authentication" title="Link to this heading"></a></h3>
<section id="enable-mfa">
<h4>Enable MFA<a class="headerlink" href="#enable-mfa" title="Link to this heading"></a></h4>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/auth/mfa/enable</span></code></p>
<p>Enable MFA for current user.</p>
<p><strong>Headers:</strong>
<code class="docutils literal notranslate"><span class="pre">Authorization:</span> <span class="pre">Bearer</span> <span class="pre">&lt;access_token&gt;</span></code></p>
<p><strong>Response (200 OK):</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;qr_code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;secret&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;JBSWY3DPEHPK3PXP&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;backup_codes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;12345678&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;87654321&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;11223344&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="security-and-authorization">
<h2>Security and Authorization<a class="headerlink" href="#security-and-authorization" title="Link to this heading"></a></h2>
<section id="jwt-token-structure">
<h3>JWT Token Structure<a class="headerlink" href="#jwt-token-structure" title="Link to this heading"></a></h3>
<p><strong>Access Token Claims:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;sub&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123e4567-e89b-12d3-a456-426614174000&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;role&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;soc_operator&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;permissions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;view_security_events&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;investigate_incidents&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;sess_123456789&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;iat&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1705312200</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;exp&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1705314000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;iss&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;blast-radius-security-tool&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;aud&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;blast-radius-users&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="permission-system">
<h3>Permission System<a class="headerlink" href="#permission-system" title="Link to this heading"></a></h3>
<p><strong>Available Permissions:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">view_security_events</span></code> - View security events and alerts</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">investigate_incidents</span></code> - Access incident investigation tools</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">update_incident_status</span></code> - Modify incident status and notes</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">view_attack_paths</span></code> - Access attack path visualizations</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">generate_reports</span></code> - Create and export reports</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">manage_users</span></code> - Create and manage user accounts</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">configure_system</span></code> - Modify system settings</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">manage_integrations</span></code> - Configure external integrations</p></li>
</ul>
<p><strong>Role-Permission Mapping:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;soc_operator&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;view_security_events&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;investigate_incidents&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;update_incident_status&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;view_attack_paths&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;generate_reports&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;security_architect&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;view_security_events&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;view_attack_paths&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;generate_reports&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;assess_risks&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;design_security_controls&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;administrator&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;manage_users&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;configure_system&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;manage_integrations&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;view_audit_logs&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-examples">
<h2>Integration Examples<a class="headerlink" href="#integration-examples" title="Link to this heading"></a></h2>
<section id="javascript-typescript">
<h3>JavaScript/TypeScript<a class="headerlink" href="#javascript-typescript" title="Link to this heading"></a></h3>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">AuthService</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">baseUrl</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;https://api.blastradius.com/api/v1/auth&#39;</span><span class="p">;</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">login</span><span class="p">(</span><span class="nx">email</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">password</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">mfaToken?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">fetch</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">baseUrl</span><span class="si">}</span><span class="sb">/login`</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">headers</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="s1">&#39;Content-Type&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;application/json&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nx">body</span><span class="o">:</span><span class="w"> </span><span class="kt">JSON.stringify</span><span class="p">({</span>
<span class="w">        </span><span class="nx">email</span><span class="p">,</span>
<span class="w">        </span><span class="nx">password</span><span class="p">,</span>
<span class="w">        </span><span class="nx">mfa_token</span><span class="o">:</span><span class="w"> </span><span class="kt">mfaToken</span><span class="p">,</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">response</span><span class="p">.</span><span class="nx">ok</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">throw</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;Login failed&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">data</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">json</span><span class="p">();</span>
<span class="w">    </span><span class="nx">localStorage</span><span class="p">.</span><span class="nx">setItem</span><span class="p">(</span><span class="s1">&#39;access_token&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="p">.</span><span class="nx">access_token</span><span class="p">);</span>
<span class="w">    </span><span class="nx">localStorage</span><span class="p">.</span><span class="nx">setItem</span><span class="p">(</span><span class="s1">&#39;refresh_token&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="p">.</span><span class="nx">refresh_token</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">data</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="python">
<h3>Python<a class="headerlink" href="#python" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span>

<span class="k">class</span><span class="w"> </span><span class="nc">AuthClient</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">base_url</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">base_url</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">base_url</span><span class="si">}</span><span class="s2">/api/v1/auth&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">access_token</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">refresh_token</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">login</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">email</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">password</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">mfa_token</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
        <span class="n">data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;email&quot;</span><span class="p">:</span> <span class="n">email</span><span class="p">,</span>
            <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="n">password</span><span class="p">,</span>
        <span class="p">}</span>
        <span class="k">if</span> <span class="n">mfa_token</span><span class="p">:</span>
            <span class="n">data</span><span class="p">[</span><span class="s2">&quot;mfa_token&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">mfa_token</span>

        <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">base_url</span><span class="si">}</span><span class="s2">/login&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">data</span><span class="p">)</span>
        <span class="n">response</span><span class="o">.</span><span class="n">raise_for_status</span><span class="p">()</span>

        <span class="n">result</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">access_token</span> <span class="o">=</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;access_token&quot;</span><span class="p">]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">refresh_token</span> <span class="o">=</span> <span class="n">result</span><span class="p">[</span><span class="s2">&quot;refresh_token&quot;</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">result</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="security-recommendations">
<h3>Security Recommendations<a class="headerlink" href="#security-recommendations" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Always use HTTPS</strong> in production environments</p></li>
<li><p><strong>Store tokens securely</strong> (httpOnly cookies or secure storage)</p></li>
<li><p><strong>Implement token refresh</strong> before expiration</p></li>
<li><p><strong>Enable MFA</strong> for all users, especially administrators</p></li>
<li><p><strong>Monitor authentication events</strong> for suspicious activity</p></li>
<li><p><strong>Implement proper session management</strong> with timeouts</p></li>
<li><p><strong>Use strong password policies</strong> and regular rotation</p></li>
<li><p><strong>Log all authentication events</strong> for audit purposes</p></li>
</ol>
<p>For more information about specific authentication scenarios and advanced configurations, see the <span class="xref std std-doc">../security/access-control</span> and <a class="reference internal" href="../user-guides/administrators.html"><span class="doc">Administrators Guide</span></a> documentation.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="API Reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="attack-path-analysis.html" class="btn btn-neutral float-right" title="Attack Path Analysis API" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>