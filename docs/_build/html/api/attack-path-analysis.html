

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Attack Path Analysis API &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Threat Modeling API Reference" href="threat-modeling.html" />
    <link rel="prev" title="Authentication API" href="authentication.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">API Reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#api-endpoints">API Endpoints</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#authentication-user-management">Authentication &amp; User Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#asset-management">Asset Management</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#attack-path-analysis">Attack Path Analysis</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Attack Path Analysis API</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-response-formats">Common Response Formats</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Attack Path Analysis API</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/api/attack-path-analysis.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="attack-path-analysis-api">
<h1>Attack Path Analysis API<a class="headerlink" href="#attack-path-analysis-api" title="Link to this heading"></a></h1>
<p>The Attack Path Analysis API provides comprehensive graph-based security analysis capabilities, including attack path discovery, blast radius calculation, and MITRE ATT&amp;CK framework integration.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The attack path analysis engine uses advanced graph algorithms to:</p>
<ul class="simple">
<li><p>Discover multi-hop attack paths through your infrastructure</p></li>
<li><p>Calculate blast radius from compromised assets</p></li>
<li><p>Model complex attack scenarios with threat actor profiling</p></li>
<li><p>Integrate with MITRE ATT&amp;CK framework for standardized threat modeling</p></li>
<li><p>Provide risk scoring and mitigation recommendations</p></li>
</ul>
</section>
<section id="base-url">
<h2>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All attack path analysis endpoints are available under:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">attack</span><span class="o">-</span><span class="n">paths</span><span class="o">/</span>
</pre></div>
</div>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>All endpoints require authentication using Bearer tokens:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">Authorization</span><span class="p">:</span> <span class="n">Bearer</span> <span class="o">&lt;</span><span class="n">your</span><span class="o">-</span><span class="n">jwt</span><span class="o">-</span><span class="n">token</span><span class="o">&gt;</span>
</pre></div>
</div>
</section>
<section id="endpoints">
<h2>Endpoints<a class="headerlink" href="#endpoints" title="Link to this heading"></a></h2>
<section id="analyze-attack-paths">
<h3>Analyze Attack Paths<a class="headerlink" href="#analyze-attack-paths" title="Link to this heading"></a></h3>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/analyze</span></code></p>
<p>Discover attack paths from source to target assets.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;source_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;web_server_001&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;target_asset_ids&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;backup_server_001&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;max_path_length&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_paths_per_target&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">source_asset_id</span></code> (string, required): Source asset ID for attack path analysis</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">target_asset_ids</span></code> (array, optional): Target asset IDs. If null, analyzes all high-value targets</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_path_length</span></code> (integer, optional): Maximum path length to analyze (1-10, default: 5)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_paths_per_target</span></code> (integer, optional): Maximum paths per target (1-20, default: 5)</p></li>
</ul>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;path_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;path_001_web_server_001_database_001&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;source_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;web_server_001&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;target_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;path_nodes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;web_server_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;app_server_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;database_001&quot;</span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;path_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;lateral_movement&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;attack_techniques&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;initial_access&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;lateral_movement&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;impact&quot;</span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;risk_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">85.5</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;likelihood&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.75</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;impact_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">92.0</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;blast_radius&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;estimated_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">120</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;required_privileges&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;user&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;admin&quot;</span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;detection_difficulty&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.6</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;mitigation_cost&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">15000.0</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;path_length&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;criticality_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">87.2</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;criticality_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;HIGH&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="calculate-blast-radius">
<h3>Calculate Blast Radius<a class="headerlink" href="#calculate-blast-radius" title="Link to this heading"></a></h3>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/blast-radius</span></code></p>
<p>Calculate the blast radius and impact from a compromised asset.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;source_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;app_server_001&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_degrees&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">source_asset_id</span></code> (string, required): Source asset ID for blast radius calculation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_degrees</span></code> (integer, optional): Maximum degrees of separation (1-10, default: 5)</p></li>
</ul>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;source_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;app_server_001&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;affected_assets&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;app_server_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;backup_server_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;web_server_001&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;impact_by_degree&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;0&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;app_server_001&quot;</span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;1&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;web_server_001&quot;</span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;2&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;backup_server_001&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;total_impact_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">285.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;critical_assets_affected&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;data_assets_affected&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;backup_server_001&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;service_disruption_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">75.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;financial_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">125000.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;compliance_impact&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;GDPR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;SOX&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;recovery_time_estimate&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">24</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="create-attack-scenario">
<h3>Create Attack Scenario<a class="headerlink" href="#create-attack-scenario" title="Link to this heading"></a></h3>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/scenarios</span></code></p>
<p>Create and analyze a comprehensive attack scenario.</p>
<p><strong>Request Body:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;scenario_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;APT Financial Data Exfiltration&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;threat_actor&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;APT29 (Cozy Bear)&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;entry_points&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;internet_gateway&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;admin_workstation&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;objectives&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;backup_server_001&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Advanced persistent threat targeting financial data&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;scenario_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;scenario_1639234567&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;APT Financial Data Exfiltration&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Advanced persistent threat targeting financial data&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;threat_actor&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;APT29 (Cozy Bear)&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;attack_paths&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;path_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;path_001&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;source_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;internet_gateway&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;target_asset_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;risk_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">88.5</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;criticality_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CRITICAL&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;total_risk_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">88.5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;likelihood&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;impact_score&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">95.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;estimated_duration&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">6</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;required_resources&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;network_access&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;credential_harvesting_tools&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;detection_probability&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.4</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;mitigation_strategies&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Network Segmentation&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Multi-factor Authentication&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;criticality_level&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CRITICAL&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-11T22:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="get-attack-scenario">
<h3>Get Attack Scenario<a class="headerlink" href="#get-attack-scenario" title="Link to this heading"></a></h3>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/scenarios/{scenario_id}</span></code></p>
<p>Retrieve a specific attack scenario by ID.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">scenario_id</span></code> (string, required): Scenario ID to retrieve</p></li>
</ul>
<p><strong>Response:</strong> Same as Create Attack Scenario response.</p>
</section>
<section id="mitre-att-ck-mapping">
<h3>MITRE ATT&amp;CK Mapping<a class="headerlink" href="#mitre-att-ck-mapping" title="Link to this heading"></a></h3>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/mitre-mapping/{path_id}</span></code></p>
<p>Get MITRE ATT&amp;CK framework mapping for an attack path.</p>
<p><strong>Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">path_id</span></code> (string, required): Attack path ID</p></li>
</ul>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;tactics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;Initial Access&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Lateral Movement&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;Impact&quot;</span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;techniques&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TA0001&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Initial Access&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Techniques used to gain an initial foothold within a network&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TA0008&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lateral Movement&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Techniques that adversaries use to enter and control remote systems&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;mitigations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;Network Segmentation&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Multi-factor Authentication&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Privileged Account Management&quot;</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;detection_methods&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="s2">&quot;Network Monitoring&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Authentication Analysis&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="s2">&quot;Behavioral Analysis&quot;</span>
<span class="w">    </span><span class="p">]</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="graph-statistics">
<h3>Graph Statistics<a class="headerlink" href="#graph-statistics" title="Link to this heading"></a></h3>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/graph/statistics</span></code></p>
<p>Get comprehensive graph statistics and performance metrics.</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-11T22:30:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;statistics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;nodes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1250</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;edges&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3420</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;density&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.0044</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;is_connected&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;number_of_components&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;average_clustering&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.15</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;performance_metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;total_nodes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1250</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;total_edges&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3420</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;last_analysis_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.85</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;cache_hits&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">145</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;cache_misses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">23</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;top_degree_centrality&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">            </span><span class="p">[</span><span class="s2">&quot;app_server_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="mf">0.25</span><span class="p">],</span>
<span class="w">            </span><span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">,</span><span class="w"> </span><span class="mf">0.18</span><span class="p">]</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Graph statistics retrieved successfully&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="export-analysis-results">
<h3>Export Analysis Results<a class="headerlink" href="#export-analysis-results" title="Link to this heading"></a></h3>
<p><strong>GET</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/export</span></code></p>
<p>Export analysis results in various formats.</p>
<p><strong>Query Parameters:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">format</span></code> (string, optional): Export format (default: “json”)</p></li>
</ul>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;format&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;json&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;{\&quot;scenarios\&quot;: [...], \&quot;graph_statistics\&quot;: {...}}&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-11T22:30:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Analysis results exported successfully&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="refresh-graph-data">
<h3>Refresh Graph Data<a class="headerlink" href="#refresh-graph-data" title="Link to this heading"></a></h3>
<p><strong>POST</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/graph/refresh</span></code></p>
<p>Refresh graph data from the database (background task).</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Graph refresh initiated&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-11T22:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="clear-analysis-cache">
<h3>Clear Analysis Cache<a class="headerlink" href="#clear-analysis-cache" title="Link to this heading"></a></h3>
<p><strong>DELETE</strong> <code class="docutils literal notranslate"><span class="pre">/api/v1/attack-paths/cache</span></code></p>
<p>Clear all analysis caches to free memory.</p>
<p><strong>Response:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Analysis cache cleared successfully&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-12-11T22:30:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="error-responses">
<h2>Error Responses<a class="headerlink" href="#error-responses" title="Link to this heading"></a></h2>
<p>All endpoints return standard HTTP status codes and error responses:</p>
<p><strong>400 Bad Request:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;detail&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid request parameters&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>404 Not Found:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;detail&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Asset not found&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>423 Locked:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;detail&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Asset is currently locked for analysis&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>500 Internal Server Error:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;detail&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Attack path analysis failed: internal error&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p>Attack path analysis endpoints are rate limited to prevent abuse:</p>
<ul class="simple">
<li><p><strong>Standard users</strong>: 10 requests per minute</p></li>
<li><p><strong>Premium users</strong>: 60 requests per minute</p></li>
<li><p><strong>Enterprise users</strong>: 300 requests per minute</p></li>
</ul>
<p>Rate limit headers are included in responses:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">X</span><span class="o">-</span><span class="n">RateLimit</span><span class="o">-</span><span class="n">Limit</span><span class="p">:</span> <span class="mi">60</span>
<span class="n">X</span><span class="o">-</span><span class="n">RateLimit</span><span class="o">-</span><span class="n">Remaining</span><span class="p">:</span> <span class="mi">45</span>
<span class="n">X</span><span class="o">-</span><span class="n">RateLimit</span><span class="o">-</span><span class="n">Reset</span><span class="p">:</span> <span class="mi">1639234567</span>
</pre></div>
</div>
</section>
<section id="performance-considerations">
<h2>Performance Considerations<a class="headerlink" href="#performance-considerations" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>Path Analysis</strong>: Typical response time &lt;1 second for graphs with &lt;1000 assets</p></li>
<li><p><strong>Blast Radius</strong>: Response time scales with max_degrees parameter</p></li>
<li><p><strong>Large Graphs</strong>: Consider using smaller max_path_length for better performance</p></li>
<li><p><strong>Caching</strong>: Repeated queries benefit from intelligent caching system</p></li>
</ul>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Start Small</strong>: Begin with smaller max_path_length values and increase as needed</p></li>
<li><p><strong>Use Caching</strong>: Leverage the caching system for repeated analysis</p></li>
<li><p><strong>Monitor Performance</strong>: Use graph statistics endpoint to monitor performance</p></li>
<li><p><strong>Batch Operations</strong>: Group related analysis requests when possible</p></li>
<li><p><strong>Clear Cache</strong>: Periodically clear cache to free memory in long-running systems</p></li>
</ol>
</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading"></a></h2>
<p><strong>Python Example:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>

<span class="c1"># Analyze attack paths</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;https://api.blast-radius.com/api/v1/attack-paths/analyze&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer your-token&quot;</span><span class="p">},</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;source_asset_id&quot;</span><span class="p">:</span> <span class="s2">&quot;web_server_001&quot;</span><span class="p">,</span>
        <span class="s2">&quot;target_asset_ids&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;database_001&quot;</span><span class="p">],</span>
        <span class="s2">&quot;max_path_length&quot;</span><span class="p">:</span> <span class="mi">5</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">paths</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
<span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">paths</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Path: </span><span class="si">{</span><span class="s1">&#39; → &#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">path</span><span class="p">[</span><span class="s1">&#39;path_nodes&#39;</span><span class="p">])</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Risk Score: </span><span class="si">{</span><span class="n">path</span><span class="p">[</span><span class="s1">&#39;risk_score&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>cURL Example:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span><span class="s2">&quot;https://api.blast-radius.com/api/v1/attack-paths/blast-radius&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer your-token&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">    &quot;source_asset_id&quot;: &quot;compromised_server&quot;,</span>
<span class="s1">    &quot;max_degrees&quot;: 3</span>
<span class="s1">  }&#39;</span>
</pre></div>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="authentication.html" class="btn btn-neutral float-left" title="Authentication API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="threat-modeling.html" class="btn btn-neutral float-right" title="Threat Modeling API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>