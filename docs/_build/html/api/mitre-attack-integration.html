

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>MITRE ATT&amp;CK Integration API Reference &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Technical Documentation" href="../technical/index.html" />
    <link rel="prev" title="Threat Modeling API Reference" href="threat-modeling.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">API Reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#api-endpoints">API Endpoints</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#authentication-user-management">Authentication &amp; User Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#attack-path-analysis">Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">MITRE ATT&amp;CK Integration API Reference</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-response-formats">Common Response Formats</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">MITRE ATT&amp;CK Integration API Reference</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/api/mitre-attack-integration.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="mitre-att-ck-integration-api-reference">
<h1>MITRE ATT&amp;CK Integration API Reference<a class="headerlink" href="#mitre-att-ck-integration-api-reference" title="Link to this heading"></a></h1>
<p>This document provides comprehensive API reference for the MITRE ATT&amp;CK integration capabilities of the Blast-Radius Security Tool.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The MITRE ATT&amp;CK Integration API provides enterprise-grade threat intelligence capabilities with
STIX 2.0/2.1 support, real-time correlation, and automated threat actor attribution.</p>
</div>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The MITRE ATT&amp;CK Integration API enables:</p>
<ul class="simple">
<li><p><strong>Complete Framework Coverage</strong>: Enterprise, Mobile, and ICS domains with 1000+ techniques</p></li>
<li><p><strong>Real-time Technique Correlation</strong>: Sub-second correlation from security events</p></li>
<li><p><strong>Threat Actor Attribution</strong>: Automated attribution with confidence scoring</p></li>
<li><p><strong>Attack Pattern Recognition</strong>: AI-powered pattern identification and analysis</p></li>
<li><p><strong>ATT&amp;CK Navigator Integration</strong>: Automated heat map generation and visualization</p></li>
<li><p><strong>Threat Intelligence Enrichment</strong>: IOC enhancement with ATT&amp;CK context</p></li>
</ul>
</section>
<section id="base-url">
<h2>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All API endpoints are relative to the base URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>https://api.blast-radius.com/api/v1/mitre-attack
</pre></div>
</div>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>All requests require authentication using Bearer tokens:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer YOUR_API_TOKEN&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>https://api.blast-radius.com/api/v1/mitre-attack/
</pre></div>
</div>
</section>
<section id="att-ck-data-management">
<h2>ATT&amp;CK Data Management<a class="headerlink" href="#att-ck-data-management" title="Link to this heading"></a></h2>
<section id="get-data-status">
<h3>Get Data Status<a class="headerlink" href="#get-data-status" title="Link to this heading"></a></h3>
<p>Get current status of MITRE ATT&amp;CK data synchronization.</p>
</section>
<section id="sync-att-ck-data">
<h3>Sync ATT&amp;CK Data<a class="headerlink" href="#sync-att-ck-data" title="Link to this heading"></a></h3>
<p>Synchronize MITRE ATT&amp;CK data from official repositories.</p>
</section>
<section id="get-technique-information">
<h3>Get Technique Information<a class="headerlink" href="#get-technique-information" title="Link to this heading"></a></h3>
<p>Retrieve detailed information about a specific ATT&amp;CK technique.</p>
</section>
</section>
<section id="technique-correlation">
<h2>Technique Correlation<a class="headerlink" href="#technique-correlation" title="Link to this heading"></a></h2>
<section id="correlate-security-event">
<h3>Correlate Security Event<a class="headerlink" href="#correlate-security-event" title="Link to this heading"></a></h3>
<p>Correlate a security event with MITRE ATT&amp;CK techniques.</p>
</section>
<section id="batch-correlate-events">
<h3>Batch Correlate Events<a class="headerlink" href="#batch-correlate-events" title="Link to this heading"></a></h3>
<p>Correlate multiple security events in a single request.</p>
</section>
</section>
<section id="threat-actor-attribution">
<h2>Threat Actor Attribution<a class="headerlink" href="#threat-actor-attribution" title="Link to this heading"></a></h2>
<section id="attribute-threat-actor">
<h3>Attribute Threat Actor<a class="headerlink" href="#attribute-threat-actor" title="Link to this heading"></a></h3>
<p>Perform automated threat actor attribution based on TTPs.</p>
</section>
<section id="get-threat-actor-profile">
<h3>Get Threat Actor Profile<a class="headerlink" href="#get-threat-actor-profile" title="Link to this heading"></a></h3>
<p>Retrieve detailed profile for a specific threat actor.</p>
</section>
</section>
<section id="attack-pattern-analysis">
<h2>Attack Pattern Analysis<a class="headerlink" href="#attack-pattern-analysis" title="Link to this heading"></a></h2>
<section id="analyze-attack-patterns">
<h3>Analyze Attack Patterns<a class="headerlink" href="#analyze-attack-patterns" title="Link to this heading"></a></h3>
<p>Identify and analyze attack patterns from security events.</p>
</section>
<section id="track-campaigns">
<h3>Track Campaigns<a class="headerlink" href="#track-campaigns" title="Link to this heading"></a></h3>
<p>Track and monitor threat campaigns over time.</p>
</section>
</section>
<section id="att-ck-navigator-integration">
<h2>ATT&amp;CK Navigator Integration<a class="headerlink" href="#att-ck-navigator-integration" title="Link to this heading"></a></h2>
<section id="generate-navigator-layer">
<h3>Generate Navigator Layer<a class="headerlink" href="#generate-navigator-layer" title="Link to this heading"></a></h3>
<p>Generate ATT&amp;CK Navigator layer for visualization.</p>
</section>
<section id="export-navigator-layer">
<h3>Export Navigator Layer<a class="headerlink" href="#export-navigator-layer" title="Link to this heading"></a></h3>
<p>Export ATT&amp;CK Navigator layer in various formats.</p>
</section>
</section>
<section id="threat-intelligence-enrichment">
<h2>Threat Intelligence Enrichment<a class="headerlink" href="#threat-intelligence-enrichment" title="Link to this heading"></a></h2>
<section id="enrich-iocs">
<h3>Enrich IOCs<a class="headerlink" href="#enrich-iocs" title="Link to this heading"></a></h3>
<p>Enrich indicators of compromise with ATT&amp;CK context.</p>
</section>
<section id="get-event-context">
<h3>Get Event Context<a class="headerlink" href="#get-event-context" title="Link to this heading"></a></h3>
<p>Get contextual analysis for a security event.</p>
</section>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The API uses standard HTTP status codes and returns detailed error information:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TECHNIQUE_NOT_FOUND&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;The specified technique &#39;T9999&#39; does not exist&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;technique_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;T9999&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;suggestion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Check technique ID format (e.g., T1566.001)&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">TECHNIQUE_NOT_FOUND</span></code> - Unknown technique ID</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GROUP_NOT_FOUND</span></code> - Unknown threat actor group</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">CORRELATION_FAILED</span></code> - Event correlation failed</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ATTRIBUTION_FAILED</span></code> - Threat actor attribution failed</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_TIMEFRAME</span></code> - Invalid time range specified</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DATA_SYNC_IN_PROGRESS</span></code> - ATT&amp;CK data synchronization in progress</p></li>
</ul>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p>API requests are rate limited to ensure fair usage:</p>
<ul class="simple">
<li><p><strong>Standard Users</strong>: 500 requests per hour</p></li>
<li><p><strong>Premium Users</strong>: 2000 requests per hour</p></li>
<li><p><strong>Enterprise Users</strong>: 10000 requests per hour</p></li>
</ul>
<p>For detailed examples and SDK usage, see the <a class="reference internal" href="../user-guides/mitre-attack-integration.html"><span class="doc">MITRE ATT&amp;CK Integration User Guide</span></a> guide.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="threat-modeling.html" class="btn btn-neutral float-left" title="Threat Modeling API Reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../technical/index.html" class="btn btn-neutral float-right" title="Technical Documentation" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>