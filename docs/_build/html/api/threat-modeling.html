

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Threat Modeling API Reference &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="MITRE ATT&amp;CK Integration API Reference" href="mitre-attack-integration.html" />
    <link rel="prev" title="Attack Path Analysis API" href="attack-path-analysis.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">API Reference</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#api-endpoints">API Endpoints</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#authentication-user-management">Authentication &amp; User Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#attack-path-analysis">Attack Path Analysis</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Threat Modeling API Reference</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-response-formats">Common Response Formats</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">API Reference</a></li>
      <li class="breadcrumb-item active">Threat Modeling API Reference</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/api/threat-modeling.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="threat-modeling-api-reference">
<h1>Threat Modeling API Reference<a class="headerlink" href="#threat-modeling-api-reference" title="Link to this heading"></a></h1>
<p>This document provides comprehensive API reference for the threat modeling and quantitative risk assessment features of the Blast-Radius Security Tool.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The Threat Modeling API provides enterprise-grade threat analysis capabilities with
statistical modeling, threat actor simulation, and regulatory compliance assessment.</p>
</div>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Threat Modeling API enables:</p>
<ul class="simple">
<li><p><strong>Threat Actor Simulation</strong>: Model attacks from known threat actors</p></li>
<li><p><strong>Quantitative Risk Assessment</strong>: Mathematical risk calculation with business impact</p></li>
<li><p><strong>Attack Success Probability</strong>: Statistical modeling of attack likelihood</p></li>
<li><p><strong>Financial Impact Assessment</strong>: Monetary loss calculation and recovery costs</p></li>
<li><p><strong>Compliance Impact Analysis</strong>: Regulatory violation identification</p></li>
<li><p><strong>Mitigation Strategy Generation</strong>: AI-driven security control recommendations</p></li>
</ul>
</section>
<section id="base-url">
<h2>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All API endpoints are relative to the base URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>https://api.blast-radius.com/api/v1/threat-modeling
</pre></div>
</div>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>All requests require authentication using Bearer tokens:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer YOUR_API_TOKEN&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>https://api.blast-radius.com/api/v1/threat-modeling/
</pre></div>
</div>
</section>
<section id="threat-actor-simulation">
<h2>Threat Actor Simulation<a class="headerlink" href="#threat-actor-simulation" title="Link to this heading"></a></h2>
<section id="simulate-attack">
<h3>Simulate Attack<a class="headerlink" href="#simulate-attack" title="Link to this heading"></a></h3>
<p>Simulate an attack from a specific threat actor against target assets.</p>
</section>
<section id="get-simulation-results">
<h3>Get Simulation Results<a class="headerlink" href="#get-simulation-results" title="Link to this heading"></a></h3>
<p>Retrieve results from a previously run simulation.</p>
</section>
<section id="list-threat-actors">
<h3>List Threat Actors<a class="headerlink" href="#list-threat-actors" title="Link to this heading"></a></h3>
<p>Get available threat actor profiles.</p>
</section>
</section>
<section id="risk-assessment">
<h2>Risk Assessment<a class="headerlink" href="#risk-assessment" title="Link to this heading"></a></h2>
<section id="assess-risk">
<h3>Assess Risk<a class="headerlink" href="#assess-risk" title="Link to this heading"></a></h3>
<p>Perform comprehensive risk assessment for specified assets.</p>
</section>
<section id="financial-impact-assessment">
<h3>Financial Impact Assessment<a class="headerlink" href="#financial-impact-assessment" title="Link to this heading"></a></h3>
<p>Calculate detailed financial impact of potential security incidents.</p>
</section>
</section>
<section id="compliance-impact-analysis">
<h2>Compliance Impact Analysis<a class="headerlink" href="#compliance-impact-analysis" title="Link to this heading"></a></h2>
<section id="gdpr-impact-assessment">
<h3>GDPR Impact Assessment<a class="headerlink" href="#gdpr-impact-assessment" title="Link to this heading"></a></h3>
<p>Assess potential GDPR compliance violations and penalties.</p>
</section>
<section id="hipaa-impact-assessment">
<h3>HIPAA Impact Assessment<a class="headerlink" href="#hipaa-impact-assessment" title="Link to this heading"></a></h3>
<p>Assess potential HIPAA compliance violations for healthcare data.</p>
</section>
</section>
<section id="mitigation-strategies">
<h2>Mitigation Strategies<a class="headerlink" href="#mitigation-strategies" title="Link to this heading"></a></h2>
<section id="generate-mitigations">
<h3>Generate Mitigations<a class="headerlink" href="#generate-mitigations" title="Link to this heading"></a></h3>
<p>Generate AI-driven mitigation strategies based on risk assessment.</p>
</section>
</section>
<section id="continuous-monitoring">
<h2>Continuous Monitoring<a class="headerlink" href="#continuous-monitoring" title="Link to this heading"></a></h2>
<section id="setup-monitoring">
<h3>Setup Monitoring<a class="headerlink" href="#setup-monitoring" title="Link to this heading"></a></h3>
<p>Configure continuous threat monitoring for ongoing risk assessment.</p>
</section>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The API uses standard HTTP status codes and returns detailed error information:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;INVALID_THREAT_ACTOR&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;The specified threat actor &#39;UNKNOWN_APT&#39; is not available&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;available_actors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;APT29&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;APT28&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;FIN7&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;INSIDER_THREAT&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;suggestion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Use /threat-actors endpoint to list available actors&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Common Error Codes:</strong></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_THREAT_ACTOR</span></code> - Unknown threat actor specified</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ASSET_NOT_FOUND</span></code> - One or more assets not found</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INSUFFICIENT_PERMISSIONS</span></code> - User lacks required permissions</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SIMULATION_IN_PROGRESS</span></code> - Simulation already running</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">RATE_LIMIT_EXCEEDED</span></code> - Too many requests</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">INVALID_PARAMETERS</span></code> - Request validation failed</p></li>
</ul>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p>API requests are rate limited to ensure fair usage:</p>
<ul class="simple">
<li><p><strong>Standard Users</strong>: 100 requests per hour</p></li>
<li><p><strong>Premium Users</strong>: 1000 requests per hour</p></li>
<li><p><strong>Enterprise Users</strong>: 10000 requests per hour</p></li>
</ul>
<p>Rate limit headers are included in responses:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
</pre></div>
</div>
<p>For detailed examples and SDK usage, see the <a class="reference internal" href="../user-guides/threat-modeling.html"><span class="doc">Threat Modeling User Guide</span></a> guide.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="attack-path-analysis.html" class="btn btn-neutral float-left" title="Attack Path Analysis API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="mitre-attack-integration.html" class="btn btn-neutral float-right" title="MITRE ATT&amp;CK Integration API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>