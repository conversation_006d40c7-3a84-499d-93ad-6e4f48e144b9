

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Blast-Radius Security Tool Documentation &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
      <script>window.MathJax = {"options": {"processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
      <script defer="defer" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Installation Guide" href="installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="#" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Blast-Radius Security Tool Documentation</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/index.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="blast-radius-security-tool-documentation">
<h1>Blast-Radius Security Tool Documentation<a class="headerlink" href="#blast-radius-security-tool-documentation" title="Link to this heading"></a></h1>
<p>Welcome to the comprehensive documentation for the Blast-Radius Security Tool, a cutting-edge security platform designed for purple teams, SOC operators, security architects, and red teamers.</p>
<a class="reference internal image-reference" href="_static/logo.png"><img alt="Blast-Radius Security Tool Logo" class="align-center" src="_static/logo.png" style="width: 300px;" />
</a>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Blast-Radius Security Tool is an enterprise-grade security platform that provides comprehensive attack path analysis, asset discovery, and blast radius calculation with MITRE ATT&amp;CK framework integration. Built for security teams, it enables proactive threat modeling and incident response.</p>
<section id="key-features">
<h3>Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>🕸️ Attack Path Analysis</strong>: Advanced graph-based attack path discovery with MITRE ATT&amp;CK integration</p></li>
<li><p><strong>💥 Blast Radius Calculation</strong>: Real-time impact assessment and cascading effect analysis</p></li>
<li><p><strong>🔍 Asset Discovery</strong>: Multi-cloud asset discovery with comprehensive metadata collection</p></li>
<li><p><strong>🛡️ Robust Asset Management</strong>: Enterprise-grade asset management with audit trails and soft-delete</p></li>
<li><p><strong>📊 Risk Assessment</strong>: Comprehensive risk scoring with business criticality and compliance impact</p></li>
<li><p><strong>🎭 Attack Scenario Modeling</strong>: Threat actor profiling and multi-vector attack simulation</p></li>
<li><p><strong>📋 MITRE ATT&amp;CK Integration</strong>: Complete framework coverage with STIX 2.0/2.1 support and real-time correlation</p></li>
<li><p><strong>🔒 Enterprise Security</strong>: Role-based access control, audit logging, and data retention policies</p></li>
<li><p><strong>🧠 Advanced Threat Modeling</strong>: Quantitative risk assessment with success probability modeling</p></li>
<li><p><strong>⚡ Performance Optimized</strong>: Sub-second analysis for 10M+ node graphs with intelligent caching</p></li>
<li><p><strong>🛡️ Security Framework</strong>: Comprehensive vulnerability protection and penetration testing validation</p></li>
<li><p><strong>🏗️ Production Ready</strong>: Enterprise-grade robustness with fault tolerance and automated deployment</p></li>
<li><p><strong>📊 Advanced Analytics</strong>: Real-time threat intelligence correlation and behavioral analysis</p></li>
</ul>
</section>
<section id="target-users">
<h3>Target Users<a class="headerlink" href="#target-users" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><code class="user-role docutils literal notranslate"><span class="pre">SOC</span> <span class="pre">Operators</span></code> - Real-time monitoring and incident response</p></li>
<li><p><code class="user-role docutils literal notranslate"><span class="pre">Security</span> <span class="pre">Architects</span></code> - Risk assessment and security design</p></li>
<li><p><code class="user-role docutils literal notranslate"><span class="pre">Red</span> <span class="pre">Team</span> <span class="pre">Members</span></code> - Attack simulation and path discovery</p></li>
<li><p><code class="user-role docutils literal notranslate"><span class="pre">Purple</span> <span class="pre">Team</span> <span class="pre">Members</span></code> - Collaborative security testing and validation</p></li>
</ul>
</section>
</section>
<section id="quick-start">
<h2>Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#installation-methods">Installation Methods</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#initial-setup">Initial Setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#verification">Verification</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#core-configuration">Core Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#security-configuration">Security Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#integration-configuration">Integration Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#monitoring-and-logging">Monitoring and Logging</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#environment-specific-configuration">Environment-Specific Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#quick-installation">Quick Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#first-login-and-setup">First Login and Setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#basic-configuration">Basic Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#essential-features-tour">Essential Features Tour</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#integration-setup">Integration Setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#next-steps">Next Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="user-guides">
<h2>User Guides<a class="headerlink" href="#user-guides" title="Link to this heading"></a></h2>
<p>Role-specific documentation for different user types:</p>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="user-guides/index.html#role-based-guides">Role-Based Guides</a></li>
<li class="toctree-l2"><a class="reference internal" href="user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a></li>
<li class="toctree-l2"><a class="reference internal" href="user-guides/index.html#common-workflows">Common Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="user-guides/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="user-guides/index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="api-documentation">
<h2>API Documentation<a class="headerlink" href="#api-documentation" title="Link to this heading"></a></h2>
<p>Comprehensive API reference and integration guides:</p>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#common-response-formats">Common Response Formats</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="technical-documentation">
<h2>Technical Documentation<a class="headerlink" href="#technical-documentation" title="Link to this heading"></a></h2>
<p>In-depth technical information for developers and system administrators:</p>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#architecture-and-design">Architecture and Design</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#performance-and-operations">Performance and Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#development-and-integration">Development and Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#data-models-and-schemas">Data Models and Schemas</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#testing-and-quality-assurance">Testing and Quality Assurance</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#contributing-and-development">Contributing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="technical/index.html#support-and-resources">Support and Resources</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="development">
<h2>Development<a class="headerlink" href="#development" title="Link to this heading"></a></h2>
<p>Information for contributors and developers:</p>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="features-by-use-case">
<h2>Features by Use Case<a class="headerlink" href="#features-by-use-case" title="Link to this heading"></a></h2>
<p>Detailed documentation for each major use case:</p>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="security-compliance">
<h2>Security &amp; Compliance<a class="headerlink" href="#security-compliance" title="Link to this heading"></a></h2>
<p>Security considerations and compliance information:</p>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="performance-scalability">
<h2>Performance &amp; Scalability<a class="headerlink" href="#performance-scalability" title="Link to this heading"></a></h2>
<p>Performance optimization and scalability documentation:</p>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="testing-quality-assurance">
<h2>Testing &amp; Quality Assurance<a class="headerlink" href="#testing-quality-assurance" title="Link to this heading"></a></h2>
<p>Comprehensive testing documentation and quality assurance:</p>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<p>Common issues and solutions:</p>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#general-questions">General Questions</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#installation-and-setup">Installation and Setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#configuration-and-usage">Configuration and Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#attack-path-analysis">Attack Path Analysis</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#performance-and-troubleshooting">Performance and Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#integration-and-api">Integration and API</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting/faq.html#support-and-community">Support and Community</a></li>
</ul>
</li>
</ul>
</div>
</section>
<section id="release-notes">
<h2>Release Notes<a class="headerlink" href="#release-notes" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="support">
<h2>Support<a class="headerlink" href="#support" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>GitHub Issues</strong>: <a class="reference external" href="https://github.com/forkrul/blast-radius/issues">Report bugs and request features</a></p></li>
<li><p><strong>Documentation</strong>: This comprehensive guide</p></li>
<li><p><strong>Community</strong>: Join our community discussions</p></li>
</ul>
</section>
<section id="license">
<h2>License<a class="headerlink" href="#license" title="Link to this heading"></a></h2>
<p>This project is licensed under the MIT License. See the <a class="reference external" href="https://github.com/forkrul/blast-radius/blob/master/LICENSE">LICENSE</a> file for details.</p>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and Tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This documentation is continuously updated. For the latest information,
please refer to the <a class="reference external" href="https://github.com/forkrul/blast-radius">GitHub repository</a>.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This tool is designed for authorized security testing and monitoring only.
Ensure you have proper authorization before using it in any environment.</p>
</div>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="installation.html" class="btn btn-neutral float-right" title="Installation Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>