

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Local Development &amp; CI/CD Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Local Development &amp; CI/CD Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/LOCAL-DEVELOPMENT.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="local-development-ci-cd-guide">
<h1>Local Development &amp; CI/CD Guide<a class="headerlink" href="#local-development-ci-cd-guide" title="Link to this heading"></a></h1>
<p>This guide provides comprehensive instructions for local development, testing, and deployment of the Blast-Radius Security Tool using our preferred local CI/CD approach.</p>
<section id="quick-start">
<h2>🚀 <strong>Quick Start</strong><a class="headerlink" href="#quick-start" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3><strong>Prerequisites</strong><a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Docker &amp; Docker Compose</p></li>
<li><p>Python 3.11+</p></li>
<li><p>Node.js 18+</p></li>
<li><p>Git</p></li>
</ul>
</section>
<section id="one-command-setup">
<h3><strong>One-Command Setup</strong><a class="headerlink" href="#one-command-setup" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone and run full pipeline</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/blast-radius.git
<span class="nb">cd</span><span class="w"> </span>blast-radius
./scripts/local-cicd.sh
</pre></div>
</div>
</section>
</section>
<section id="local-ci-cd-pipeline">
<h2>🛠️ <strong>Local CI/CD Pipeline</strong><a class="headerlink" href="#local-ci-cd-pipeline" title="Link to this heading"></a></h2>
<section id="pipeline-overview">
<h3><strong>Pipeline Overview</strong><a class="headerlink" href="#pipeline-overview" title="Link to this heading"></a></h3>
<p>Our local CI/CD pipeline provides the same rigor as cloud-based solutions with full local control:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>Setup → Security Scan → Tests → Build → Deploy → Verify
</pre></div>
</div>
</section>
<section id="pipeline-commands">
<h3><strong>Pipeline Commands</strong><a class="headerlink" href="#pipeline-commands" title="Link to this heading"></a></h3>
<section id="full-pipeline-recommended">
<h4><strong>Full Pipeline (Recommended)</strong><a class="headerlink" href="#full-pipeline-recommended" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./scripts/local-cicd.sh
</pre></div>
</div>
</section>
<section id="specific-stages">
<h4><strong>Specific Stages</strong><a class="headerlink" href="#specific-stages" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Security scanning only</span>
./scripts/local-cicd.sh<span class="w"> </span>security

<span class="c1"># Run tests only</span>
./scripts/local-cicd.sh<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Build containers</span>
./scripts/local-cicd.sh<span class="w"> </span>build

<span class="c1"># Deploy to local environment</span>
./scripts/local-cicd.sh<span class="w"> </span>deploy

<span class="c1"># Clean up everything</span>
./scripts/local-cicd.sh<span class="w"> </span>clean
</pre></div>
</div>
</section>
<section id="pipeline-options">
<h4><strong>Pipeline Options</strong><a class="headerlink" href="#pipeline-options" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Skip tests for faster iteration</span>
./scripts/local-cicd.sh<span class="w"> </span>--skip-tests

<span class="c1"># Skip security scanning</span>
./scripts/local-cicd.sh<span class="w"> </span>--skip-security

<span class="c1"># Build without deployment</span>
./scripts/local-cicd.sh<span class="w"> </span>--no-deploy

<span class="c1"># Set environment</span>
./scripts/local-cicd.sh<span class="w"> </span>--env<span class="w"> </span>dev
</pre></div>
</div>
</section>
</section>
</section>
<section id="security-first-development">
<h2>🔒 <strong>Security-First Development</strong><a class="headerlink" href="#security-first-development" title="Link to this heading"></a></h2>
<section id="automated-security-scanning">
<h3><strong>Automated Security Scanning</strong><a class="headerlink" href="#automated-security-scanning" title="Link to this heading"></a></h3>
<p>Every pipeline run includes:</p>
<ul class="simple">
<li><p><strong>Bandit</strong>: Python security linting</p></li>
<li><p><strong>Safety</strong>: Dependency vulnerability scanning</p></li>
<li><p><strong>npm audit</strong>: Frontend security audit</p></li>
<li><p><strong>Container scanning</strong>: Docker image vulnerabilities</p></li>
</ul>
</section>
<section id="security-commands">
<h3><strong>Security Commands</strong><a class="headerlink" href="#security-commands" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run comprehensive security scan</span>
./scripts/local-cicd.sh<span class="w"> </span>security

<span class="c1"># Manual security checks</span>
<span class="nb">cd</span><span class="w"> </span>backend<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>bandit<span class="w"> </span>-r<span class="w"> </span>app/
<span class="nb">cd</span><span class="w"> </span>backend<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>safety<span class="w"> </span>scan
<span class="nb">cd</span><span class="w"> </span>frontend<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>npm<span class="w"> </span>audit
</pre></div>
</div>
</section>
<section id="security-standards">
<h3><strong>Security Standards</strong><a class="headerlink" href="#security-standards" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Zero critical vulnerabilities allowed</p></li>
<li><p>All dependencies must be up-to-date</p></li>
<li><p>Code must pass security linting</p></li>
<li><p>Containers scanned before deployment</p></li>
</ul>
</section>
</section>
<section id="testing-strategy">
<h2>🧪 <strong>Testing Strategy</strong><a class="headerlink" href="#testing-strategy" title="Link to this heading"></a></h2>
<section id="test-categories">
<h3><strong>Test Categories</strong><a class="headerlink" href="#test-categories" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Unit Tests</strong>: Individual component testing</p></li>
<li><p><strong>Integration Tests</strong>: Service interaction testing</p></li>
<li><p><strong>Security Tests</strong>: Vulnerability and penetration testing</p></li>
<li><p><strong>Performance Tests</strong>: Load and stress testing</p></li>
</ol>
</section>
<section id="running-tests">
<h3><strong>Running Tests</strong><a class="headerlink" href="#running-tests" title="Link to this heading"></a></h3>
<section id="backend-tests">
<h4><strong>Backend Tests</strong><a class="headerlink" href="#backend-tests" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>backend
<span class="nb">source</span><span class="w"> </span>venv/bin/activate

<span class="c1"># Unit tests with coverage</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/<span class="w"> </span>-v<span class="w"> </span>--cov<span class="o">=</span>app

<span class="c1"># Integration tests</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/test_integration_*.py

<span class="c1"># Specific test file</span>
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/test_auth.py<span class="w"> </span>-v
</pre></div>
</div>
</section>
<section id="frontend-tests">
<h4><strong>Frontend Tests</strong><a class="headerlink" href="#frontend-tests" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>frontend

<span class="c1"># Unit tests</span>
npm<span class="w"> </span>run<span class="w"> </span>test:unit

<span class="c1"># Linting</span>
npm<span class="w"> </span>run<span class="w"> </span>lint

<span class="c1"># Build test</span>
npm<span class="w"> </span>run<span class="w"> </span>build
</pre></div>
</div>
</section>
<section id="end-to-end-tests">
<h4><strong>End-to-End Tests</strong><a class="headerlink" href="#end-to-end-tests" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start application</span>
./scripts/local-cicd.sh<span class="w"> </span>deploy

<span class="c1"># Run E2E tests</span>
<span class="nb">cd</span><span class="w"> </span>tests/e2e
python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>test_e2e.py
</pre></div>
</div>
</section>
</section>
</section>
<section id="development-workflow">
<h2>🏗️ <strong>Development Workflow</strong><a class="headerlink" href="#development-workflow" title="Link to this heading"></a></h2>
<section id="daily-development-cycle">
<h3><strong>Daily Development Cycle</strong><a class="headerlink" href="#daily-development-cycle" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Start Development</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Pull latest changes</span>
git<span class="w"> </span>pull<span class="w"> </span>origin<span class="w"> </span>master

<span class="c1"># Run quick pipeline</span>
./scripts/local-cicd.sh<span class="w"> </span>--skip-tests<span class="w"> </span>deploy
</pre></div>
</div>
</li>
<li><p><strong>Make Changes</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Edit code</span>
<span class="c1"># Add tests</span>
<span class="c1"># Update documentation</span>
</pre></div>
</div>
</li>
<li><p><strong>Test Changes</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run relevant tests</span>
./scripts/local-cicd.sh<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Or run specific tests</span>
<span class="nb">cd</span><span class="w"> </span>backend<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>pytest<span class="w"> </span>tests/test_new_feature.py
</pre></div>
</div>
</li>
<li><p><strong>Security Check</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run security scan</span>
./scripts/local-cicd.sh<span class="w"> </span>security
</pre></div>
</div>
</li>
<li><p><strong>Deploy and Verify</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Deploy changes</span>
./scripts/local-cicd.sh<span class="w"> </span>deploy

<span class="c1"># Verify functionality</span>
curl<span class="w"> </span>http://localhost:8000/health
</pre></div>
</div>
</li>
<li><p><strong>Commit and Push</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add new feature&quot;</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature-branch
</pre></div>
</div>
</li>
</ol>
</section>
<section id="feature-development">
<h3><strong>Feature Development</strong><a class="headerlink" href="#feature-development" title="Link to this heading"></a></h3>
<section id="new-feature-workflow">
<h4><strong>New Feature Workflow</strong><a class="headerlink" href="#new-feature-workflow" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># 1. Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/new-security-analysis

<span class="c1"># 2. Develop with TDD</span>
./scripts/local-cicd.sh<span class="w"> </span><span class="nb">test</span><span class="w">  </span><span class="c1"># Should fail initially</span>
<span class="c1"># Write code</span>
./scripts/local-cicd.sh<span class="w"> </span><span class="nb">test</span><span class="w">  </span><span class="c1"># Should pass</span>

<span class="c1"># 3. Security and integration</span>
./scripts/local-cicd.sh<span class="w">       </span><span class="c1"># Full pipeline</span>

<span class="c1"># 4. Manual testing</span>
./scripts/local-cicd.sh<span class="w"> </span>deploy
<span class="c1"># Test manually at http://localhost:8000</span>

<span class="c1"># 5. Commit and merge</span>
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: implement new security analysis&quot;</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/new-security-analysis
</pre></div>
</div>
</section>
</section>
</section>
<section id="container-development">
<h2>🐳 <strong>Container Development</strong><a class="headerlink" href="#container-development" title="Link to this heading"></a></h2>
<section id="local-container-management">
<h3><strong>Local Container Management</strong><a class="headerlink" href="#local-container-management" title="Link to this heading"></a></h3>
<section id="build-images">
<h4><strong>Build Images</strong><a class="headerlink" href="#build-images" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Build all images</span>
./scripts/local-cicd.sh<span class="w"> </span>build

<span class="c1"># Build specific image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>blast-radius-backend:dev<span class="w"> </span>./backend
</pre></div>
</div>
</section>
<section id="run-containers">
<h4><strong>Run Containers</strong><a class="headerlink" href="#run-containers" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start all services</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Start specific service</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d<span class="w"> </span>backend

<span class="c1"># View logs</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>backend
</pre></div>
</div>
</section>
<section id="container-debugging">
<h4><strong>Container Debugging</strong><a class="headerlink" href="#container-debugging" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Execute into running container</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>backend<span class="w"> </span>bash

<span class="c1"># Check container health</span>
docker-compose<span class="w"> </span>ps

<span class="c1"># Restart service</span>
docker-compose<span class="w"> </span>restart<span class="w"> </span>backend
</pre></div>
</div>
</section>
</section>
<section id="multi-environment-support">
<h3><strong>Multi-Environment Support</strong><a class="headerlink" href="#multi-environment-support" title="Link to this heading"></a></h3>
<section id="environment-configurations">
<h4><strong>Environment Configurations</strong><a class="headerlink" href="#environment-configurations" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Local development</span>
./scripts/local-cicd.sh<span class="w"> </span>--env<span class="w"> </span><span class="nb">local</span>

<span class="c1"># Development environment</span>
./scripts/local-cicd.sh<span class="w"> </span>--env<span class="w"> </span>dev

<span class="c1"># Staging-like environment</span>
./scripts/local-cicd.sh<span class="w"> </span>--env<span class="w"> </span>staging
</pre></div>
</div>
</section>
<section id="environment-variables">
<h4><strong>Environment Variables</strong><a class="headerlink" href="#environment-variables" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Set environment variables</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">ENVIRONMENT</span><span class="o">=</span>dev
<span class="nb">export</span><span class="w"> </span><span class="nv">DEBUG</span><span class="o">=</span><span class="nb">true</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">LOG_LEVEL</span><span class="o">=</span>debug

<span class="c1"># Run with custom config</span>
./scripts/local-cicd.sh
</pre></div>
</div>
</section>
</section>
</section>
<section id="monitoring-debugging">
<h2>📊 <strong>Monitoring &amp; Debugging</strong><a class="headerlink" href="#monitoring-debugging" title="Link to this heading"></a></h2>
<section id="application-monitoring">
<h3><strong>Application Monitoring</strong><a class="headerlink" href="#application-monitoring" title="Link to this heading"></a></h3>
<section id="health-checks">
<h4><strong>Health Checks</strong><a class="headerlink" href="#health-checks" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Application health</span>
curl<span class="w"> </span>http://localhost:8000/health

<span class="c1"># Database health</span>
curl<span class="w"> </span>http://localhost:8000/health/db

<span class="c1"># Detailed status</span>
curl<span class="w"> </span>http://localhost:8000/status
</pre></div>
</div>
</section>
<section id="logs-and-metrics">
<h4><strong>Logs and Metrics</strong><a class="headerlink" href="#logs-and-metrics" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Application logs</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>backend

<span class="c1"># Database logs</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>postgresql

<span class="c1"># All service logs</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>-f
</pre></div>
</div>
</section>
</section>
<section id="performance-monitoring">
<h3><strong>Performance Monitoring</strong><a class="headerlink" href="#performance-monitoring" title="Link to this heading"></a></h3>
<section id="local-performance-testing">
<h4><strong>Local Performance Testing</strong><a class="headerlink" href="#local-performance-testing" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install performance tools</span>
pip<span class="w"> </span>install<span class="w"> </span>locust

<span class="c1"># Run load tests</span>
<span class="nb">cd</span><span class="w"> </span>tests/performance
locust<span class="w"> </span>-f<span class="w"> </span>locustfile.py<span class="w"> </span>--host<span class="o">=</span>http://localhost:8000
</pre></div>
</div>
</section>
<section id="resource-monitoring">
<h4><strong>Resource Monitoring</strong><a class="headerlink" href="#resource-monitoring" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Container resource usage</span>
docker<span class="w"> </span>stats

<span class="c1"># System resource usage</span>
htop<span class="w">  </span><span class="c1"># or top</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="troubleshooting">
<h2>🔧 <strong>Troubleshooting</strong><a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3><strong>Common Issues</strong><a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<section id="port-conflicts">
<h4><strong>Port Conflicts</strong><a class="headerlink" href="#port-conflicts" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check port usage</span>
lsof<span class="w"> </span>-i<span class="w"> </span>:8000
lsof<span class="w"> </span>-i<span class="w"> </span>:5432

<span class="c1"># Kill conflicting processes</span>
sudo<span class="w"> </span><span class="nb">kill</span><span class="w"> </span>-9<span class="w"> </span>&lt;PID&gt;
</pre></div>
</div>
</section>
<section id="database-issues">
<h4><strong>Database Issues</strong><a class="headerlink" href="#database-issues" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reset database</span>
docker-compose<span class="w"> </span>down<span class="w"> </span>-v
docker-compose<span class="w"> </span>up<span class="w"> </span>-d<span class="w"> </span>postgresql
</pre></div>
</div>
</section>
<section id="permission-issues">
<h4><strong>Permission Issues</strong><a class="headerlink" href="#permission-issues" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Fix file permissions</span>
sudo<span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span><span class="nv">$USER</span>:<span class="nv">$USER</span><span class="w"> </span>.
chmod<span class="w"> </span>+x<span class="w"> </span>scripts/local-cicd.sh
</pre></div>
</div>
</section>
<section id="container-issues">
<h4><strong>Container Issues</strong><a class="headerlink" href="#container-issues" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clean Docker system</span>
docker<span class="w"> </span>system<span class="w"> </span>prune<span class="w"> </span>-a

<span class="c1"># Rebuild containers</span>
docker-compose<span class="w"> </span>build<span class="w"> </span>--no-cache
</pre></div>
</div>
</section>
</section>
<section id="debug-mode">
<h3><strong>Debug Mode</strong><a class="headerlink" href="#debug-mode" title="Link to this heading"></a></h3>
<section id="enable-debug-logging">
<h4><strong>Enable Debug Logging</strong><a class="headerlink" href="#enable-debug-logging" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">export</span><span class="w"> </span><span class="nv">DEBUG</span><span class="o">=</span><span class="nb">true</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">LOG_LEVEL</span><span class="o">=</span>debug
./scripts/local-cicd.sh<span class="w"> </span>deploy
</pre></div>
</div>
</section>
<section id="interactive-debugging">
<h4><strong>Interactive Debugging</strong><a class="headerlink" href="#interactive-debugging" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Python debugger</span>
<span class="nb">cd</span><span class="w"> </span>backend
python<span class="w"> </span>-m<span class="w"> </span>pdb<span class="w"> </span>app/main.py

<span class="c1"># Container debugging</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>backend<span class="w"> </span>python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import pdb; pdb.set_trace()&quot;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="additional-resources">
<h2>📚 <strong>Additional Resources</strong><a class="headerlink" href="#additional-resources" title="Link to this heading"></a></h2>
<section id="documentation">
<h3><strong>Documentation</strong><a class="headerlink" href="#documentation" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><a class="reference external" href="http://localhost:8000/docs">API Documentation</a> (when running)</p></li>
<li><p><a class="reference internal" href="#../ARCHITECTURE.md"><span class="xref myst">Architecture Overview</span></a></p></li>
<li><p><a class="reference internal" href="#../SECURITY.md"><span class="xref myst">Security Guidelines</span></a></p></li>
<li><p><a class="reference internal" href="#../DEPLOYMENT.md"><span class="xref myst">Deployment Guide</span></a></p></li>
</ul>
</section>
<section id="development-tools">
<h3><strong>Development Tools</strong><a class="headerlink" href="#development-tools" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>IDE Setup</strong>: VS Code with Python and Docker extensions</p></li>
<li><p><strong>Database GUI</strong>: pgAdmin or DBeaver for PostgreSQL</p></li>
<li><p><strong>API Testing</strong>: Postman or curl</p></li>
<li><p><strong>Container Management</strong>: Docker Desktop or Portainer</p></li>
</ul>
</section>
<section id="best-practices">
<h3><strong>Best Practices</strong><a class="headerlink" href="#best-practices" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Run security scans before every commit</p></li>
<li><p>Write tests for all new features</p></li>
<li><p>Use meaningful commit messages</p></li>
<li><p>Keep dependencies up-to-date</p></li>
<li><p>Monitor application performance</p></li>
<li><p>Document significant changes</p></li>
</ul>
<hr class="docutils" />
<p><strong>Happy Coding!</strong> 🚀</p>
<p>For questions or issues, check the troubleshooting section or reach out to the development team.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>