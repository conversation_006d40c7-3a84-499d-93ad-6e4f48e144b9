

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Installation Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Configuration Guide" href="configuration.html" />
    <link rel="prev" title="Blast-Radius Security Tool Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Installation Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/installation.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="installation-guide">
<h1>Installation Guide<a class="headerlink" href="#installation-guide" title="Link to this heading"></a></h1>
<p>This comprehensive guide will walk you through installing and setting up the Blast-Radius Security Tool for different deployment scenarios.</p>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<section id="system-requirements">
<h3>System Requirements<a class="headerlink" href="#system-requirements" title="Link to this heading"></a></h3>
<p><strong>Minimum Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Operating System</strong>: Linux (Ubuntu 20.04+, CentOS 8+), macOS 10.15+, or Windows 10+</p></li>
<li><p><strong>Memory</strong>: 8GB RAM (16GB+ recommended for production)</p></li>
<li><p><strong>Storage</strong>: 50GB free space (100GB+ recommended for production)</p></li>
<li><p><strong>CPU</strong>: 4 cores (8+ cores recommended for production)</p></li>
<li><p><strong>Network</strong>: Internet connection for downloading dependencies</p></li>
</ul>
<p><strong>Production Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Memory</strong>: 32GB+ RAM for large-scale deployments</p></li>
<li><p><strong>Storage</strong>: 500GB+ SSD storage with backup capabilities</p></li>
<li><p><strong>CPU</strong>: 16+ cores for optimal performance</p></li>
<li><p><strong>Network</strong>: High-bandwidth connection for real-time data processing</p></li>
</ul>
</section>
<section id="software-dependencies">
<h3>Software Dependencies<a class="headerlink" href="#software-dependencies" title="Link to this heading"></a></h3>
<p><strong>Backend Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Python</strong>: 3.11 or higher</p></li>
<li><p><strong>PostgreSQL</strong>: 15.0 or higher</p></li>
<li><p><strong>Redis</strong>: 7.0 or higher</p></li>
<li><p><strong>Neo4j</strong>: 5.15 or higher (for graph analysis)</p></li>
</ul>
<p><strong>Frontend Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Node.js</strong>: 18.0 or higher</p></li>
<li><p><strong>npm</strong>: 8.0 or higher (or yarn 1.22+)</p></li>
</ul>
<p><strong>Infrastructure Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Docker</strong>: 20.10 or higher</p></li>
<li><p><strong>Docker Compose</strong>: 2.0 or higher</p></li>
<li><p><strong>Git</strong>: Latest version</p></li>
</ul>
</section>
</section>
<section id="installation-methods">
<h2>Installation Methods<a class="headerlink" href="#installation-methods" title="Link to this heading"></a></h2>
<section id="method-1-quick-start-with-docker-recommended">
<h3>Method 1: Quick Start with Docker (Recommended)<a class="headerlink" href="#method-1-quick-start-with-docker-recommended" title="Link to this heading"></a></h3>
<p>This is the fastest way to get Blast-Radius running for evaluation and development.</p>
<p><strong>Step 1: Clone the Repository</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/blast-radius.git
<span class="nb">cd</span><span class="w"> </span>blast-radius
</pre></div>
</div>
<p><strong>Step 2: Start the Platform</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start all services with one command</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
<p><strong>Step 3: Access the Platform</strong></p>
<ul class="simple">
<li><p><strong>Web Dashboard</strong>: <a class="reference external" href="http://localhost:3000">http://localhost:3000</a></p></li>
<li><p><strong>API Documentation</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
<li><p><strong>Monitoring</strong>: <a class="reference external" href="http://localhost:3001">http://localhost:3001</a></p></li>
</ul>
<p><strong>Default Credentials:</strong></p>
<ul class="simple">
<li><p><strong>Username</strong>: <a class="reference external" href="mailto:admin&#37;&#52;&#48;blastradius&#46;local">admin<span>&#64;</span>blastradius<span>&#46;</span>local</a></p></li>
<li><p><strong>Password</strong>: BlastRadius2024!</p></li>
</ul>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Change the default credentials immediately after first login.</p>
</div>
</section>
<section id="method-2-development-installation">
<h3>Method 2: Development Installation<a class="headerlink" href="#method-2-development-installation" title="Link to this heading"></a></h3>
<p>This method is recommended for development and customization.</p>
<p><strong>Step 1: Clone and Setup Backend</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/blast-radius.git
<span class="nb">cd</span><span class="w"> </span>blast-radius/backend

<span class="c1"># Create virtual environment</span>
python3.11<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># Windows: venv\Scripts\activate</span>

<span class="c1"># Install dependencies</span>
make<span class="w"> </span>install-dev

<span class="c1"># Copy environment configuration</span>
cp<span class="w"> </span>.env.example<span class="w"> </span>.env
</pre></div>
</div>
<p><strong>Step 2: Configure Environment</strong></p>
<p>Edit the <cite>.env</cite> file with your settings:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Database Configuration</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span><span class="s2">&quot;postgresql://blast_user:blast_pass@localhost:5432/blast_radius&quot;</span>

<span class="c1"># Redis Configuration</span>
<span class="nv">REDIS_URL</span><span class="o">=</span><span class="s2">&quot;redis://localhost:6379/0&quot;</span>

<span class="c1"># Neo4j Configuration</span>
<span class="nv">NEO4J_URI</span><span class="o">=</span><span class="s2">&quot;bolt://localhost:7687&quot;</span>
<span class="nv">NEO4J_USER</span><span class="o">=</span><span class="s2">&quot;neo4j&quot;</span>
<span class="nv">NEO4J_PASSWORD</span><span class="o">=</span><span class="s2">&quot;blast_neo4j&quot;</span>

<span class="c1"># Security Configuration</span>
<span class="nv">SECRET_KEY</span><span class="o">=</span><span class="s2">&quot;your-super-secret-key-here&quot;</span>
<span class="nv">JWT_SECRET_KEY</span><span class="o">=</span><span class="s2">&quot;your-jwt-secret-key-here&quot;</span>
</pre></div>
</div>
<p><strong>Step 3: Setup Databases</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start database services</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d<span class="w"> </span>postgresql<span class="w"> </span>redis<span class="w"> </span>neo4j

<span class="c1"># Run database migrations</span>
make<span class="w"> </span>migrate-upgrade

<span class="c1"># Create initial admin user</span>
make<span class="w"> </span>create-admin-user
</pre></div>
</div>
<p><strong>Step 4: Setup Frontend</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>../frontend

<span class="c1"># Install dependencies</span>
npm<span class="w"> </span>install

<span class="c1"># Start development server</span>
npm<span class="w"> </span>start
</pre></div>
</div>
<p><strong>Step 5: Start Backend</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>../backend
make<span class="w"> </span>run-dev
</pre></div>
</div>
</section>
<section id="method-3-production-installation">
<h3>Method 3: Production Installation<a class="headerlink" href="#method-3-production-installation" title="Link to this heading"></a></h3>
<p>This method is for production deployments with high availability and security.</p>
<p><strong>Step 1: Prepare Production Environment</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create dedicated user</span>
sudo<span class="w"> </span>useradd<span class="w"> </span>-m<span class="w"> </span>-s<span class="w"> </span>/bin/bash<span class="w"> </span>blastradius
sudo<span class="w"> </span>usermod<span class="w"> </span>-aG<span class="w"> </span>docker<span class="w"> </span>blastradius

<span class="c1"># Create application directories</span>
sudo<span class="w"> </span>mkdir<span class="w"> </span>-p<span class="w"> </span>/opt/blast-radius/<span class="o">{</span>data,logs,config,backups<span class="o">}</span>
sudo<span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>blastradius:blastradius<span class="w"> </span>/opt/blast-radius
</pre></div>
</div>
<p><strong>Step 2: Clone and Configure</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>sudo<span class="w"> </span>-u<span class="w"> </span>blastradius<span class="w"> </span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/blast-radius.git<span class="w"> </span>/opt/blast-radius/app
<span class="nb">cd</span><span class="w"> </span>/opt/blast-radius/app

<span class="c1"># Copy production configuration</span>
sudo<span class="w"> </span>-u<span class="w"> </span>blastradius<span class="w"> </span>cp<span class="w"> </span>.env.production<span class="w"> </span>.env

<span class="c1"># Edit configuration for your environment</span>
sudo<span class="w"> </span>-u<span class="w"> </span>blastradius<span class="w"> </span>nano<span class="w"> </span>.env
</pre></div>
</div>
<p><strong>Step 3: Setup SSL Certificates</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Using Let&#39;s Encrypt (recommended)</span>
sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>certbot
sudo<span class="w"> </span>certbot<span class="w"> </span>certonly<span class="w"> </span>--standalone<span class="w"> </span>-d<span class="w"> </span>your-domain.com

<span class="c1"># Copy certificates to application directory</span>
sudo<span class="w"> </span>cp<span class="w"> </span>/etc/letsencrypt/live/your-domain.com/*.pem<span class="w"> </span>/opt/blast-radius/config/
</pre></div>
</div>
<p><strong>Step 4: Deploy with Docker Compose</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start production services</span>
sudo<span class="w"> </span>-u<span class="w"> </span>blastradius<span class="w"> </span>docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.prod.yml<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Verify deployment</span>
sudo<span class="w"> </span>-u<span class="w"> </span>blastradius<span class="w"> </span>docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.prod.yml<span class="w"> </span>ps
</pre></div>
</div>
</section>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<section id="environment-variables">
<h3>Environment Variables<a class="headerlink" href="#environment-variables" title="Link to this heading"></a></h3>
<p><strong>Core Application Settings:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Application Identity</span>
<span class="nv">PROJECT_NAME</span><span class="o">=</span><span class="s2">&quot;Blast-Radius Security Tool&quot;</span>
<span class="nv">VERSION</span><span class="o">=</span><span class="s2">&quot;1.0.0&quot;</span>
<span class="nv">ENVIRONMENT</span><span class="o">=</span><span class="s2">&quot;production&quot;</span><span class="w">  </span><span class="c1"># development, staging, production</span>
<span class="nv">DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;info&quot;</span>

<span class="c1"># Server Configuration</span>
<span class="nv">HOST</span><span class="o">=</span><span class="s2">&quot;0.0.0.0&quot;</span>
<span class="nv">PORT</span><span class="o">=</span><span class="m">8000</span>
<span class="nv">WORKERS</span><span class="o">=</span><span class="m">4</span>
</pre></div>
</div>
<p><strong>Database Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># PostgreSQL</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span><span class="s2">&quot;********************************/dbname&quot;</span>
<span class="nv">DATABASE_POOL_SIZE</span><span class="o">=</span><span class="m">20</span>
<span class="nv">DATABASE_MAX_OVERFLOW</span><span class="o">=</span><span class="m">30</span>

<span class="c1"># Redis</span>
<span class="nv">REDIS_URL</span><span class="o">=</span><span class="s2">&quot;redis://host:6379/0&quot;</span>
<span class="nv">REDIS_POOL_SIZE</span><span class="o">=</span><span class="m">10</span>

<span class="c1"># Neo4j</span>
<span class="nv">NEO4J_URI</span><span class="o">=</span><span class="s2">&quot;bolt://host:7687&quot;</span>
<span class="nv">NEO4J_USER</span><span class="o">=</span><span class="s2">&quot;neo4j&quot;</span>
<span class="nv">NEO4J_PASSWORD</span><span class="o">=</span><span class="s2">&quot;password&quot;</span>
</pre></div>
</div>
<p><strong>Security Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Encryption Keys</span>
<span class="nv">SECRET_KEY</span><span class="o">=</span><span class="s2">&quot;your-256-bit-secret-key&quot;</span>
<span class="nv">JWT_SECRET_KEY</span><span class="o">=</span><span class="s2">&quot;your-jwt-secret-key&quot;</span>
<span class="nv">JWT_ALGORITHM</span><span class="o">=</span><span class="s2">&quot;HS256&quot;</span>
<span class="nv">JWT_EXPIRE_MINUTES</span><span class="o">=</span><span class="m">30</span>

<span class="c1"># Session Configuration</span>
<span class="nv">SESSION_COOKIE_SECURE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">SESSION_COOKIE_HTTPONLY</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">SESSION_COOKIE_SAMESITE</span><span class="o">=</span><span class="s2">&quot;strict&quot;</span>

<span class="c1"># CORS Configuration</span>
<span class="nv">CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;https://your-domain.com&quot;]&#39;</span>
</pre></div>
</div>
</section>
</section>
<section id="initial-setup">
<h2>Initial Setup<a class="headerlink" href="#initial-setup" title="Link to this heading"></a></h2>
<section id="creating-admin-user">
<h3>Creating Admin User<a class="headerlink" href="#creating-admin-user" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Using the CLI tool</span>
<span class="nb">cd</span><span class="w"> </span>backend
python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>create-admin<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--email<span class="w"> </span><EMAIL><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--password<span class="w"> </span><span class="s2">&quot;SecurePassword123!&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--first-name<span class="w"> </span><span class="s2">&quot;Admin&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--last-name<span class="w"> </span><span class="s2">&quot;User&quot;</span>
</pre></div>
</div>
</section>
<section id="configuring-integrations">
<h3>Configuring Integrations<a class="headerlink" href="#configuring-integrations" title="Link to this heading"></a></h3>
<p><strong>Cloud Provider Setup:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># AWS Configuration</span>
<span class="nv">AWS_ACCESS_KEY_ID</span><span class="o">=</span><span class="s2">&quot;your-access-key&quot;</span>
<span class="nv">AWS_SECRET_ACCESS_KEY</span><span class="o">=</span><span class="s2">&quot;your-secret-key&quot;</span>
<span class="nv">AWS_DEFAULT_REGION</span><span class="o">=</span><span class="s2">&quot;us-east-1&quot;</span>

<span class="c1"># Azure Configuration</span>
<span class="nv">AZURE_CLIENT_ID</span><span class="o">=</span><span class="s2">&quot;your-client-id&quot;</span>
<span class="nv">AZURE_CLIENT_SECRET</span><span class="o">=</span><span class="s2">&quot;your-client-secret&quot;</span>
<span class="nv">AZURE_TENANT_ID</span><span class="o">=</span><span class="s2">&quot;your-tenant-id&quot;</span>

<span class="c1"># GCP Configuration</span>
<span class="nv">GOOGLE_APPLICATION_CREDENTIALS</span><span class="o">=</span><span class="s2">&quot;/path/to/service-account.json&quot;</span>
</pre></div>
</div>
<p><strong>ServiceNow Integration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">SERVICENOW_INSTANCE</span><span class="o">=</span><span class="s2">&quot;your-instance.service-now.com&quot;</span>
<span class="nv">SERVICENOW_USERNAME</span><span class="o">=</span><span class="s2">&quot;integration-user&quot;</span>
<span class="nv">SERVICENOW_PASSWORD</span><span class="o">=</span><span class="s2">&quot;integration-password&quot;</span>
<span class="nv">SERVICENOW_API_VERSION</span><span class="o">=</span><span class="s2">&quot;v1&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="verification">
<h2>Verification<a class="headerlink" href="#verification" title="Link to this heading"></a></h2>
<section id="health-checks">
<h3>Health Checks<a class="headerlink" href="#health-checks" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check application health</span>
curl<span class="w"> </span>http://localhost:8000/health

<span class="c1"># Check database connectivity</span>
curl<span class="w"> </span>http://localhost:8000/health/db

<span class="c1"># Check all services</span>
curl<span class="w"> </span>http://localhost:8000/health/detailed
</pre></div>
</div>
</section>
<section id="performance-testing">
<h3>Performance Testing<a class="headerlink" href="#performance-testing" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run basic performance tests</span>
<span class="nb">cd</span><span class="w"> </span>backend
make<span class="w"> </span>test-performance

<span class="c1"># Load testing (requires additional setup)</span>
make<span class="w"> </span>test-load
</pre></div>
</div>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>Database Connection Issues:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check PostgreSQL status</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>status<span class="w"> </span>postgresql

<span class="c1"># Test connection</span>
psql<span class="w"> </span>-h<span class="w"> </span>localhost<span class="w"> </span>-U<span class="w"> </span>blast_user<span class="w"> </span>-d<span class="w"> </span>blast_radius<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;SELECT 1;&quot;</span>
</pre></div>
</div>
<p><strong>Redis Connection Issues:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check Redis status</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>status<span class="w"> </span>redis

<span class="c1"># Test connection</span>
redis-cli<span class="w"> </span>ping
</pre></div>
</div>
<p><strong>Neo4j Connection Issues:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check Neo4j status</span>
sudo<span class="w"> </span>systemctl<span class="w"> </span>status<span class="w"> </span>neo4j

<span class="c1"># Test connection</span>
cypher-shell<span class="w"> </span>-u<span class="w"> </span>neo4j<span class="w"> </span>-p<span class="w"> </span>password<span class="w"> </span><span class="s2">&quot;RETURN 1;&quot;</span>
</pre></div>
</div>
<p><strong>Port Conflicts:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check what&#39;s using port 8000</span>
sudo<span class="w"> </span>netstat<span class="w"> </span>-tulpn<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>:8000

<span class="c1"># Kill process if needed</span>
sudo<span class="w"> </span><span class="nb">kill</span><span class="w"> </span>-9<span class="w"> </span>&lt;PID&gt;
</pre></div>
</div>
</section>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<p>If you encounter issues during installation:</p>
<ol class="arabic simple">
<li><p>Check the <span class="xref std std-doc">troubleshooting/common-issues</span> guide</p></li>
<li><p>Review application logs in <cite>/opt/blast-radius/logs/</cite></p></li>
<li><p>Search existing issues on <a class="reference external" href="https://github.com/forkrul/blast-radius/issues">GitHub</a></p></li>
<li><p>Create a new issue with detailed error information</p></li>
</ol>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After successful installation:</p>
<ol class="arabic simple">
<li><p>Read the <a class="reference internal" href="quick-start-guide.html"><span class="doc">Quick Start Guide</span></a> for initial setup</p></li>
<li><p>Configure <a class="reference internal" href="user-guides/administrators.html"><span class="doc">Administrators Guide</span></a> settings</p></li>
<li><p>Set up user roles in <span class="xref std std-doc">security/access-control</span></p></li>
<li><p>Review <span class="xref std std-doc">security/best-practices</span> for production deployments</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For enterprise deployments, consider consulting with our professional services team
for customized installation and configuration assistance.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Blast-Radius Security Tool Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="configuration.html" class="btn btn-neutral float-right" title="Configuration Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>