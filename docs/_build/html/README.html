

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Blast-Radius Security Tool Documentation &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Blast-Radius Security Tool Documentation</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/README.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="blast-radius-security-tool-documentation">
<h1>Blast-Radius Security Tool Documentation<a class="headerlink" href="#blast-radius-security-tool-documentation" title="Link to this heading"></a></h1>
<p>This directory contains the comprehensive documentation for the Blast-Radius Security Tool, built with Sphinx and featuring Mermaid diagrams for visual documentation.</p>
<section id="documentation-structure">
<h2>📚 Documentation Structure<a class="headerlink" href="#documentation-structure" title="Link to this heading"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>docs/
├── api/                    # API Reference Documentation
│   ├── index.rst          # API overview and navigation
│   └── attack-path-analysis.rst  # Attack path API reference
├── user-guides/           # User Guides for Different Roles
│   ├── index.rst          # User guides overview
│   └── attack-path-analysis.rst  # Attack path user guide
├── technical/             # Technical Documentation
│   ├── index.rst          # Technical docs overview
│   ├── attack-path-architecture.rst  # System architecture
│   ├── attack-path-flows.rst         # Process flows and diagrams
│   ├── database-design.rst           # Database schema and design
│   └── product-requirements.rst      # Product requirements document
├── releases/              # Release Documentation
│   └── changelog.rst      # Comprehensive changelog
├── _static/               # Static assets (CSS, JS, images)
├── _templates/            # Custom Sphinx templates
└── conf.py               # Sphinx configuration
</pre></div>
</div>
</section>
<section id="quick-start">
<h2>🚀 Quick Start<a class="headerlink" href="#quick-start" title="Link to this heading"></a></h2>
<section id="prerequisites">
<h3>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Python 3.9+</p></li>
<li><p>pip</p></li>
<li><p>(Optional) Docker and Docker Compose</p></li>
</ul>
</section>
<section id="local-development">
<h3>Local Development<a class="headerlink" href="#local-development" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Install dependencies:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>docs
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt
</pre></div>
</div>
</li>
<li><p><strong>Build documentation:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./build.sh<span class="w"> </span>build
</pre></div>
</div>
</li>
<li><p><strong>Serve documentation locally:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./build.sh<span class="w"> </span>serve
</pre></div>
</div>
</li>
<li><p><strong>Live reload development:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./build.sh<span class="w"> </span>live
</pre></div>
</div>
</li>
</ol>
</section>
<section id="docker-development">
<h3>Docker Development<a class="headerlink" href="#docker-development" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Build and serve with Docker:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>docs
docker-compose<span class="w"> </span>up<span class="w"> </span>docs
</pre></div>
</div>
</li>
<li><p><strong>Development with live reload:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>up<span class="w"> </span>docs-dev
</pre></div>
</div>
</li>
<li><p><strong>Build only:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>run<span class="w"> </span>--rm<span class="w"> </span>docs-builder
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="build-commands">
<h2>🔧 Build Commands<a class="headerlink" href="#build-commands" title="Link to this heading"></a></h2>
<p>The <code class="docutils literal notranslate"><span class="pre">build.sh</span></code> script provides comprehensive build and development commands:</p>
<section id="basic-commands">
<h3>Basic Commands<a class="headerlink" href="#basic-commands" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Build HTML documentation</span>
./build.sh<span class="w"> </span>build

<span class="c1"># Clean build directory</span>
./build.sh<span class="w"> </span>clean

<span class="c1"># Build all formats and run checks</span>
./build.sh<span class="w"> </span>all

<span class="c1"># Show help</span>
./build.sh<span class="w"> </span><span class="nb">help</span>
</pre></div>
</div>
</section>
<section id="development-commands">
<h3>Development Commands<a class="headerlink" href="#development-commands" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Serve documentation (default port: 8000)</span>
./build.sh<span class="w"> </span>serve

<span class="c1"># Serve on custom port</span>
./build.sh<span class="w"> </span>serve<span class="w"> </span><span class="m">9000</span>

<span class="c1"># Live reload server (default port: 8080)</span>
./build.sh<span class="w"> </span>live

<span class="c1"># Live reload on custom port</span>
./build.sh<span class="w"> </span>live<span class="w"> </span><span class="m">9090</span>
</pre></div>
</div>
</section>
<section id="quality-assurance">
<h3>Quality Assurance<a class="headerlink" href="#quality-assurance" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check for broken links</span>
./build.sh<span class="w"> </span>linkcheck

<span class="c1"># Generate coverage report</span>
./build.sh<span class="w"> </span>coverage

<span class="c1"># Build PDF documentation</span>
./build.sh<span class="w"> </span>pdf
</pre></div>
</div>
</section>
<section id="docker-commands">
<h3>Docker Commands<a class="headerlink" href="#docker-commands" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Build with Docker</span>
./build.sh<span class="w"> </span>docker-build

<span class="c1"># Serve with Docker</span>
./build.sh<span class="w"> </span>docker-serve
</pre></div>
</div>
</section>
</section>
<section id="features">
<h2>🎨 Features<a class="headerlink" href="#features" title="Link to this heading"></a></h2>
<section id="mermaid-diagrams">
<h3>Mermaid Diagrams<a class="headerlink" href="#mermaid-diagrams" title="Link to this heading"></a></h3>
<p>The documentation includes comprehensive Mermaid diagrams for:</p>
<ul class="simple">
<li><p><strong>System Architecture</strong>: Multi-layered architecture visualization</p></li>
<li><p><strong>Database Schema</strong>: Entity relationship diagrams</p></li>
<li><p><strong>Process Flows</strong>: Attack path analysis workflows</p></li>
<li><p><strong>Decision Trees</strong>: MITRE ATT&amp;CK mapping logic</p></li>
<li><p><strong>Risk Assessment</strong>: Multi-factor scoring algorithms</p></li>
</ul>
</section>
<section id="interactive-elements">
<h3>Interactive Elements<a class="headerlink" href="#interactive-elements" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Code Examples</strong>: Syntax-highlighted code blocks in multiple languages</p></li>
<li><p><strong>API Documentation</strong>: Complete REST API reference with examples</p></li>
<li><p><strong>Cross-References</strong>: Comprehensive linking between related topics</p></li>
<li><p><strong>Search Functionality</strong>: Full-text search across all documentation</p></li>
</ul>
</section>
<section id="responsive-design">
<h3>Responsive Design<a class="headerlink" href="#responsive-design" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Mobile-Friendly</strong>: Responsive design for all device sizes</p></li>
<li><p><strong>Dark/Light Theme</strong>: Theme switching support</p></li>
<li><p><strong>Print-Friendly</strong>: Optimized for PDF generation and printing</p></li>
</ul>
</section>
</section>
<section id="documentation-sections">
<h2>📖 Documentation Sections<a class="headerlink" href="#documentation-sections" title="Link to this heading"></a></h2>
<section id="api-reference-api">
<h3>API Reference (<code class="docutils literal notranslate"><span class="pre">api/</span></code>)<a class="headerlink" href="#api-reference-api" title="Link to this heading"></a></h3>
<p>Complete REST API documentation including:</p>
<ul class="simple">
<li><p><strong>Attack Path Analysis API</strong>: 7 comprehensive endpoints</p></li>
<li><p><strong>Request/Response Models</strong>: Detailed schemas with examples</p></li>
<li><p><strong>Authentication</strong>: JWT-based authentication guide</p></li>
<li><p><strong>Rate Limiting</strong>: Usage limits and best practices</p></li>
<li><p><strong>Error Handling</strong>: Comprehensive error codes and responses</p></li>
</ul>
</section>
<section id="user-guides-user-guides">
<h3>User Guides (<code class="docutils literal notranslate"><span class="pre">user-guides/</span></code>)<a class="headerlink" href="#user-guides-user-guides" title="Link to this heading"></a></h3>
<p>Role-based documentation for:</p>
<ul class="simple">
<li><p><strong>SOC Operators</strong>: Real-time monitoring and incident response</p></li>
<li><p><strong>Security Architects</strong>: Infrastructure design and validation</p></li>
<li><p><strong>Red Team Members</strong>: Attack simulation and penetration testing</p></li>
<li><p><strong>Purple Team Members</strong>: Collaborative security testing</p></li>
<li><p><strong>Administrators</strong>: System configuration and management</p></li>
</ul>
</section>
<section id="technical-documentation-technical">
<h3>Technical Documentation (<code class="docutils literal notranslate"><span class="pre">technical/</span></code>)<a class="headerlink" href="#technical-documentation-technical" title="Link to this heading"></a></h3>
<p>In-depth technical documentation covering:</p>
<ul class="simple">
<li><p><strong>System Architecture</strong>: Multi-layered design and components</p></li>
<li><p><strong>Attack Path Flows</strong>: Complete workflow diagrams</p></li>
<li><p><strong>Database Design</strong>: Schema, indexes, and optimization</p></li>
<li><p><strong>Product Requirements</strong>: Comprehensive PRD with specifications</p></li>
<li><p><strong>Performance Optimization</strong>: Caching, scaling, and tuning</p></li>
</ul>
</section>
</section>
<section id="search-and-navigation">
<h2>🔍 Search and Navigation<a class="headerlink" href="#search-and-navigation" title="Link to this heading"></a></h2>
<section id="search-features">
<h3>Search Features<a class="headerlink" href="#search-features" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Full-Text Search</strong>: Search across all documentation content</p></li>
<li><p><strong>Faceted Search</strong>: Filter by document type, section, or topic</p></li>
<li><p><strong>Autocomplete</strong>: Smart search suggestions</p></li>
<li><p><strong>Highlighting</strong>: Search term highlighting in results</p></li>
</ul>
</section>
<section id="navigation">
<h3>Navigation<a class="headerlink" href="#navigation" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Hierarchical Structure</strong>: Logical content organization</p></li>
<li><p><strong>Breadcrumbs</strong>: Clear navigation path indication</p></li>
<li><p><strong>Cross-References</strong>: Extensive internal linking</p></li>
<li><p><strong>Table of Contents</strong>: Expandable TOC for each page</p></li>
</ul>
</section>
</section>
<section id="development">
<h2>🛠️ Development<a class="headerlink" href="#development" title="Link to this heading"></a></h2>
<section id="adding-new-documentation">
<h3>Adding New Documentation<a class="headerlink" href="#adding-new-documentation" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Create new RST file</strong> in appropriate directory</p></li>
<li><p><strong>Add to index.rst</strong> in the same directory</p></li>
<li><p><strong>Build and test</strong> locally</p></li>
<li><p><strong>Commit and push</strong> changes</p></li>
</ol>
</section>
<section id="id1">
<h3>Mermaid Diagrams<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<p>Add Mermaid diagrams using the <code class="docutils literal notranslate"><span class="pre">..</span> <span class="pre">mermaid::</span></code> directive:</p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">mermaid</span><span class="p">::</span>

    graph TD
        A[Start] --&gt; B{Decision}
        B --&gt;|Yes| C[Action 1]
        B --&gt;|No| D[Action 2]
</pre></div>
</div>
</section>
<section id="code-examples">
<h3>Code Examples<a class="headerlink" href="#code-examples" title="Link to this heading"></a></h3>
<p>Include code examples with syntax highlighting:</p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span><span class="p">..</span> <span class="ow">code-block</span><span class="p">::</span> <span class="k">python</span>

    <span class="c1"># Python example</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">blast_radius</span><span class="w"> </span><span class="kn">import</span> <span class="n">BlastRadiusClient</span>
    
    <span class="n">client</span> <span class="o">=</span> <span class="n">BlastRadiusClient</span><span class="p">(</span><span class="n">api_token</span><span class="o">=</span><span class="s2">&quot;your-token&quot;</span><span class="p">)</span>
    <span class="n">paths</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">attack_paths</span><span class="o">.</span><span class="n">analyze</span><span class="p">(</span><span class="n">source</span><span class="o">=</span><span class="s2">&quot;web_server&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="cross-references">
<h3>Cross-References<a class="headerlink" href="#cross-references" title="Link to this heading"></a></h3>
<p>Link to other documentation sections:</p>
<div class="highlight-rst notranslate"><div class="highlight"><pre><span></span>See <span class="na">:doc:</span><span class="nv">`attack-path-analysis`</span> for detailed API documentation.
Reference <span class="na">:ref:</span><span class="nv">`installation-guide`</span> for setup instructions.
</pre></div>
</div>
</section>
</section>
<section id="analytics-and-monitoring">
<h2>📊 Analytics and Monitoring<a class="headerlink" href="#analytics-and-monitoring" title="Link to this heading"></a></h2>
<section id="documentation-metrics">
<h3>Documentation Metrics<a class="headerlink" href="#documentation-metrics" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Page Views</strong>: Track popular documentation sections</p></li>
<li><p><strong>Search Queries</strong>: Monitor common search terms</p></li>
<li><p><strong>User Feedback</strong>: Collect user satisfaction ratings</p></li>
<li><p><strong>Link Analysis</strong>: Identify broken or outdated links</p></li>
</ul>
</section>
<section id="id2">
<h3>Quality Assurance<a class="headerlink" href="#id2" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Automated Testing</strong>: Link checking and validation</p></li>
<li><p><strong>Spell Checking</strong>: Automated spell checking</p></li>
<li><p><strong>Style Guide</strong>: Consistent formatting and style</p></li>
<li><p><strong>Review Process</strong>: Peer review for all changes</p></li>
</ul>
</section>
</section>
<section id="deployment">
<h2>🚀 Deployment<a class="headerlink" href="#deployment" title="Link to this heading"></a></h2>
<section id="local-deployment">
<h3>Local Deployment<a class="headerlink" href="#local-deployment" title="Link to this heading"></a></h3>
<p>The documentation can be served locally using:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Python HTTP server</span>
./build.sh<span class="w"> </span>serve

<span class="c1"># Live reload for development</span>
./build.sh<span class="w"> </span>live
</pre></div>
</div>
</section>
<section id="production-deployment">
<h3>Production Deployment<a class="headerlink" href="#production-deployment" title="Link to this heading"></a></h3>
<p>For production deployment:</p>
<ol class="arabic">
<li><p><strong>Build documentation:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./build.sh<span class="w"> </span>build
</pre></div>
</div>
</li>
<li><p><strong>Deploy to web server:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Copy _build/html/* to web server</span>
rsync<span class="w"> </span>-av<span class="w"> </span>_build/html/<span class="w"> </span>user@server:/var/www/docs/
</pre></div>
</div>
</li>
<li><p><strong>Configure web server</strong> (nginx, Apache, etc.)</p></li>
</ol>
</section>
<section id="docker-deployment">
<h3>Docker Deployment<a class="headerlink" href="#docker-deployment" title="Link to this heading"></a></h3>
<p>Use Docker for containerized deployment:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Build and run container</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>docs

<span class="c1"># Access at http://localhost:8080</span>
</pre></div>
</div>
</section>
<section id="continuous-integration">
<h3>Continuous Integration<a class="headerlink" href="#continuous-integration" title="Link to this heading"></a></h3>
<p>The documentation can be automatically built and deployed using CI/CD:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># Example GitHub Actions workflow</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build Documentation</span>
<span class="nt">on</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">push</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">pull_request</span><span class="p p-Indicator">]</span>
<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">build</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Setup Python</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-python@v4</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">python-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.11&#39;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Install dependencies</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">pip install -r docs/requirements.txt</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build documentation</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd docs &amp;&amp; ./build.sh build</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to GitHub Pages</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">peaceiris/actions-gh-pages@v3</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">github_token</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${{ secrets.GITHUB_TOKEN }}</span>
<span class="w">          </span><span class="nt">publish_dir</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./docs/_build/html</span>
</pre></div>
</div>
</section>
</section>
<section id="support">
<h2>📞 Support<a class="headerlink" href="#support" title="Link to this heading"></a></h2>
<section id="getting-help">
<h3>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Documentation Issues</strong>: Create GitHub issue with <code class="docutils literal notranslate"><span class="pre">documentation</span></code> label</p></li>
<li><p><strong>Build Problems</strong>: Check build logs and requirements</p></li>
<li><p><strong>Feature Requests</strong>: Submit enhancement requests</p></li>
<li><p><strong>Community</strong>: Join documentation discussions</p></li>
</ul>
</section>
<section id="contributing">
<h3>Contributing<a class="headerlink" href="#contributing" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Fork repository</strong> and create feature branch</p></li>
<li><p><strong>Make changes</strong> following style guide</p></li>
<li><p><strong>Test locally</strong> using build script</p></li>
<li><p><strong>Submit pull request</strong> with clear description</p></li>
<li><p><strong>Address feedback</strong> from reviewers</p></li>
</ol>
</section>
<section id="style-guide">
<h3>Style Guide<a class="headerlink" href="#style-guide" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>RST Format</strong>: Use reStructuredText for all documentation</p></li>
<li><p><strong>Consistent Headers</strong>: Follow header hierarchy (=, -, ~, ^)</p></li>
<li><p><strong>Code Examples</strong>: Include working examples for all features</p></li>
<li><p><strong>Cross-References</strong>: Link related content extensively</p></li>
<li><p><strong>Mermaid Diagrams</strong>: Use for complex workflows and architecture</p></li>
</ul>
<p>This comprehensive documentation system provides everything needed for effective user onboarding, API integration, and technical understanding of the Blast-Radius Security Tool.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>