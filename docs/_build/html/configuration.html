

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Configuration Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Quick Start Guide" href="quick-start-guide.html" />
    <link rel="prev" title="Installation Guide" href="installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Configuration Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/configuration.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="configuration-guide">
<h1>Configuration Guide<a class="headerlink" href="#configuration-guide" title="Link to this heading"></a></h1>
<p>This comprehensive guide covers all configuration options for the Blast-Radius Security Tool, including environment variables, security settings, integrations, and deployment configurations.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Blast-Radius Security Tool uses a hierarchical configuration system that prioritizes settings in the following order:</p>
<ol class="arabic simple">
<li><p><strong>Environment Variables</strong> (highest priority)</p></li>
<li><p><strong>Configuration Files</strong> (.env files)</p></li>
<li><p><strong>Database Settings</strong> (runtime configuration)</p></li>
<li><p><strong>Default Values</strong> (lowest priority)</p></li>
</ol>
<p>Configuration is managed through Pydantic Settings, providing type validation, automatic environment variable parsing, and comprehensive error handling.</p>
</section>
<section id="core-configuration">
<h2>Core Configuration<a class="headerlink" href="#core-configuration" title="Link to this heading"></a></h2>
<section id="application-settings">
<h3>Application Settings<a class="headerlink" href="#application-settings" title="Link to this heading"></a></h3>
<p><strong>Basic Application Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Application Identity</span>
<span class="nv">PROJECT_NAME</span><span class="o">=</span><span class="s2">&quot;Blast-Radius Security Tool&quot;</span>
<span class="nv">VERSION</span><span class="o">=</span><span class="s2">&quot;1.0.0&quot;</span>
<span class="nv">DESCRIPTION</span><span class="o">=</span><span class="s2">&quot;Comprehensive security platform for attack path analysis&quot;</span>

<span class="c1"># Environment Configuration</span>
<span class="nv">ENVIRONMENT</span><span class="o">=</span><span class="s2">&quot;production&quot;</span><span class="w">  </span><span class="c1"># development, staging, production</span>
<span class="nv">DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;info&quot;</span><span class="w">  </span><span class="c1"># debug, info, warning, error, critical</span>

<span class="c1"># Server Configuration</span>
<span class="nv">HOST</span><span class="o">=</span><span class="s2">&quot;0.0.0.0&quot;</span>
<span class="nv">PORT</span><span class="o">=</span><span class="m">8000</span>
<span class="nv">WORKERS</span><span class="o">=</span><span class="m">4</span><span class="w">  </span><span class="c1"># Number of worker processes (production)</span>
<span class="nv">WORKER_CLASS</span><span class="o">=</span><span class="s2">&quot;uvicorn.workers.UvicornWorker&quot;</span>

<span class="c1"># API Configuration</span>
<span class="nv">API_V1_STR</span><span class="o">=</span><span class="s2">&quot;/api/v1&quot;</span>
<span class="nv">DOCS_URL</span><span class="o">=</span><span class="s2">&quot;/docs&quot;</span><span class="w">  </span><span class="c1"># Set to null to disable in production</span>
<span class="nv">REDOC_URL</span><span class="o">=</span><span class="s2">&quot;/redoc&quot;</span><span class="w">  </span><span class="c1"># Set to null to disable in production</span>
<span class="nv">OPENAPI_URL</span><span class="o">=</span><span class="s2">&quot;/openapi.json&quot;</span>
</pre></div>
</div>
<p><strong>Performance Settings:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Request Handling</span>
<span class="nv">MAX_REQUEST_SIZE</span><span class="o">=</span><span class="m">10485760</span><span class="w">  </span><span class="c1"># 10MB</span>
<span class="nv">REQUEST_TIMEOUT</span><span class="o">=</span><span class="m">30</span><span class="w">  </span><span class="c1"># seconds</span>
<span class="nv">KEEPALIVE_TIMEOUT</span><span class="o">=</span><span class="m">5</span><span class="w">  </span><span class="c1"># seconds</span>

<span class="c1"># Concurrency Settings</span>
<span class="nv">MAX_CONCURRENT_REQUESTS</span><span class="o">=</span><span class="m">1000</span>
<span class="nv">RATE_LIMIT_REQUESTS</span><span class="o">=</span><span class="m">100</span><span class="w">  </span><span class="c1"># requests per minute per IP</span>
<span class="nv">RATE_LIMIT_WINDOW</span><span class="o">=</span><span class="m">60</span><span class="w">  </span><span class="c1"># seconds</span>
</pre></div>
</div>
</section>
<section id="database-configuration">
<h3>Database Configuration<a class="headerlink" href="#database-configuration" title="Link to this heading"></a></h3>
<p><strong>PostgreSQL Settings:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Primary Database</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span><span class="s2">&quot;postgresql://blast_user:blast_pass@localhost:5432/blast_radius&quot;</span>
<span class="nv">DATABASE_POOL_SIZE</span><span class="o">=</span><span class="m">20</span>
<span class="nv">DATABASE_MAX_OVERFLOW</span><span class="o">=</span><span class="m">30</span>
<span class="nv">DATABASE_POOL_TIMEOUT</span><span class="o">=</span><span class="m">30</span>
<span class="nv">DATABASE_POOL_RECYCLE</span><span class="o">=</span><span class="m">3600</span>
<span class="nv">DATABASE_ECHO</span><span class="o">=</span><span class="nb">false</span><span class="w">  </span><span class="c1"># Set to true for SQL debugging</span>

<span class="c1"># Connection Settings</span>
<span class="nv">DATABASE_CONNECT_TIMEOUT</span><span class="o">=</span><span class="m">10</span>
<span class="nv">DATABASE_COMMAND_TIMEOUT</span><span class="o">=</span><span class="m">60</span>
<span class="nv">DATABASE_SSL_MODE</span><span class="o">=</span><span class="s2">&quot;prefer&quot;</span><span class="w">  </span><span class="c1"># disable, allow, prefer, require</span>
</pre></div>
</div>
<p><strong>Redis Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Cache and Session Store</span>
<span class="nv">REDIS_URL</span><span class="o">=</span><span class="s2">&quot;redis://localhost:6379/0&quot;</span>
<span class="nv">REDIS_POOL_SIZE</span><span class="o">=</span><span class="m">10</span>
<span class="nv">REDIS_POOL_MAX_CONNECTIONS</span><span class="o">=</span><span class="m">50</span>
<span class="nv">REDIS_SOCKET_TIMEOUT</span><span class="o">=</span><span class="m">5</span>
<span class="nv">REDIS_SOCKET_CONNECT_TIMEOUT</span><span class="o">=</span><span class="m">5</span>
<span class="nv">REDIS_RETRY_ON_TIMEOUT</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># Cache Settings</span>
<span class="nv">CACHE_TTL</span><span class="o">=</span><span class="m">3600</span><span class="w">  </span><span class="c1"># Default cache TTL in seconds</span>
<span class="nv">SESSION_TTL</span><span class="o">=</span><span class="m">1800</span><span class="w">  </span><span class="c1"># Session TTL in seconds</span>
</pre></div>
</div>
<p><strong>Neo4j Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Graph Database</span>
<span class="nv">NEO4J_URI</span><span class="o">=</span><span class="s2">&quot;bolt://localhost:7687&quot;</span>
<span class="nv">NEO4J_USER</span><span class="o">=</span><span class="s2">&quot;neo4j&quot;</span>
<span class="nv">NEO4J_PASSWORD</span><span class="o">=</span><span class="s2">&quot;blast_neo4j_password&quot;</span>
<span class="nv">NEO4J_DATABASE</span><span class="o">=</span><span class="s2">&quot;neo4j&quot;</span>
<span class="nv">NEO4J_MAX_CONNECTION_LIFETIME</span><span class="o">=</span><span class="m">3600</span>
<span class="nv">NEO4J_MAX_CONNECTION_POOL_SIZE</span><span class="o">=</span><span class="m">100</span>
<span class="nv">NEO4J_CONNECTION_ACQUISITION_TIMEOUT</span><span class="o">=</span><span class="m">60</span>
</pre></div>
</div>
</section>
</section>
<section id="security-configuration">
<h2>Security Configuration<a class="headerlink" href="#security-configuration" title="Link to this heading"></a></h2>
<section id="authentication-settings">
<h3>Authentication Settings<a class="headerlink" href="#authentication-settings" title="Link to this heading"></a></h3>
<p><strong>JWT Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># JWT Settings</span>
<span class="nv">JWT_SECRET_KEY</span><span class="o">=</span><span class="s2">&quot;your-super-secret-jwt-key-256-bits-minimum&quot;</span>
<span class="nv">JWT_ALGORITHM</span><span class="o">=</span><span class="s2">&quot;HS256&quot;</span>
<span class="nv">JWT_EXPIRE_MINUTES</span><span class="o">=</span><span class="m">30</span>
<span class="nv">JWT_REFRESH_EXPIRE_DAYS</span><span class="o">=</span><span class="m">7</span>
<span class="nv">JWT_ISSUER</span><span class="o">=</span><span class="s2">&quot;blast-radius-security-tool&quot;</span>
<span class="nv">JWT_AUDIENCE</span><span class="o">=</span><span class="s2">&quot;blast-radius-users&quot;</span>
</pre></div>
</div>
<p><strong>Session Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Session Management</span>
<span class="nv">SESSION_COOKIE_NAME</span><span class="o">=</span><span class="s2">&quot;blast_radius_session&quot;</span>
<span class="nv">SESSION_COOKIE_SECURE</span><span class="o">=</span><span class="nb">true</span><span class="w">  </span><span class="c1"># HTTPS only</span>
<span class="nv">SESSION_COOKIE_HTTPONLY</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">SESSION_COOKIE_SAMESITE</span><span class="o">=</span><span class="s2">&quot;strict&quot;</span><span class="w">  </span><span class="c1"># strict, lax, none</span>
<span class="nv">SESSION_COOKIE_MAX_AGE</span><span class="o">=</span><span class="m">1800</span><span class="w">  </span><span class="c1"># 30 minutes</span>

<span class="c1"># Session Security</span>
<span class="nv">SESSION_REGENERATE_ON_LOGIN</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">SESSION_INVALIDATE_ON_LOGOUT</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">MAX_CONCURRENT_SESSIONS</span><span class="o">=</span><span class="m">3</span><span class="w">  </span><span class="c1"># Per user</span>
</pre></div>
</div>
<p><strong>Multi-Factor Authentication:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># MFA Settings</span>
<span class="nv">MFA_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">MFA_REQUIRE_FOR_ALL</span><span class="o">=</span><span class="nb">false</span><span class="w">  </span><span class="c1"># Require MFA for all users</span>
<span class="nv">MFA_REQUIRE_FOR_ADMINS</span><span class="o">=</span><span class="nb">true</span><span class="w">  </span><span class="c1"># Require MFA for admin users</span>
<span class="nv">MFA_BACKUP_CODES_COUNT</span><span class="o">=</span><span class="m">10</span>
<span class="nv">MFA_TOTP_ISSUER</span><span class="o">=</span><span class="s2">&quot;Blast-Radius Security Tool&quot;</span>
<span class="nv">MFA_TOTP_PERIOD</span><span class="o">=</span><span class="m">30</span><span class="w">  </span><span class="c1"># seconds</span>
<span class="nv">MFA_TOTP_DIGITS</span><span class="o">=</span><span class="m">6</span>
</pre></div>
</div>
</section>
<section id="encryption-settings">
<h3>Encryption Settings<a class="headerlink" href="#encryption-settings" title="Link to this heading"></a></h3>
<p><strong>Data Encryption:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Encryption Keys</span>
<span class="nv">SECRET_KEY</span><span class="o">=</span><span class="s2">&quot;your-super-secret-key-for-general-encryption&quot;</span>
<span class="nv">FIELD_ENCRYPTION_KEY</span><span class="o">=</span><span class="s2">&quot;your-field-level-encryption-key&quot;</span>

<span class="c1"># Encryption Settings</span>
<span class="nv">ENCRYPTION_ALGORITHM</span><span class="o">=</span><span class="s2">&quot;AES-256-GCM&quot;</span>
<span class="nv">PASSWORD_HASH_ALGORITHM</span><span class="o">=</span><span class="s2">&quot;bcrypt&quot;</span>
<span class="nv">PASSWORD_HASH_ROUNDS</span><span class="o">=</span><span class="m">12</span>
</pre></div>
</div>
<p><strong>SSL/TLS Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># SSL Settings</span>
<span class="nv">SSL_CERT_PATH</span><span class="o">=</span><span class="s2">&quot;/path/to/ssl/cert.pem&quot;</span>
<span class="nv">SSL_KEY_PATH</span><span class="o">=</span><span class="s2">&quot;/path/to/ssl/key.pem&quot;</span>
<span class="nv">SSL_CA_CERT_PATH</span><span class="o">=</span><span class="s2">&quot;/path/to/ssl/ca-cert.pem&quot;</span>
<span class="nv">SSL_VERIFY_MODE</span><span class="o">=</span><span class="s2">&quot;CERT_REQUIRED&quot;</span><span class="w">  </span><span class="c1"># CERT_NONE, CERT_OPTIONAL, CERT_REQUIRED</span>
</pre></div>
</div>
</section>
<section id="access-control-settings">
<h3>Access Control Settings<a class="headerlink" href="#access-control-settings" title="Link to this heading"></a></h3>
<p><strong>CORS Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Cross-Origin Resource Sharing</span>
<span class="nv">CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;https://your-domain.com&quot;, &quot;https://app.your-domain.com&quot;]&#39;</span>
<span class="nv">CORS_ALLOW_CREDENTIALS</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">CORS_ALLOW_METHODS</span><span class="o">=</span><span class="s1">&#39;[&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;OPTIONS&quot;]&#39;</span>
<span class="nv">CORS_ALLOW_HEADERS</span><span class="o">=</span><span class="s1">&#39;[&quot;*&quot;]&#39;</span>
<span class="nv">CORS_MAX_AGE</span><span class="o">=</span><span class="m">86400</span><span class="w">  </span><span class="c1"># 24 hours</span>
</pre></div>
</div>
<p><strong>Rate Limiting:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Rate Limiting</span>
<span class="nv">RATE_LIMIT_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">RATE_LIMIT_REQUESTS_PER_MINUTE</span><span class="o">=</span><span class="m">100</span>
<span class="nv">RATE_LIMIT_BURST</span><span class="o">=</span><span class="m">20</span>
<span class="nv">RATE_LIMIT_STORAGE</span><span class="o">=</span><span class="s2">&quot;redis&quot;</span><span class="w">  </span><span class="c1"># memory, redis</span>

<span class="c1"># API-specific rate limits</span>
<span class="nv">AUTH_RATE_LIMIT</span><span class="o">=</span><span class="m">10</span><span class="w">  </span><span class="c1"># Login attempts per minute</span>
<span class="nv">API_RATE_LIMIT</span><span class="o">=</span><span class="m">1000</span><span class="w">  </span><span class="c1"># API calls per minute</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-configuration">
<h2>Integration Configuration<a class="headerlink" href="#integration-configuration" title="Link to this heading"></a></h2>
<section id="cloud-provider-settings">
<h3>Cloud Provider Settings<a class="headerlink" href="#cloud-provider-settings" title="Link to this heading"></a></h3>
<p><strong>AWS Integration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># AWS Configuration</span>
<span class="nv">AWS_ACCESS_KEY_ID</span><span class="o">=</span><span class="s2">&quot;your-aws-access-key&quot;</span>
<span class="nv">AWS_SECRET_ACCESS_KEY</span><span class="o">=</span><span class="s2">&quot;your-aws-secret-key&quot;</span>
<span class="nv">AWS_DEFAULT_REGION</span><span class="o">=</span><span class="s2">&quot;us-east-1&quot;</span>
<span class="nv">AWS_SESSION_TOKEN</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="w">  </span><span class="c1"># For temporary credentials</span>

<span class="c1"># AWS Services</span>
<span class="nv">AWS_ENABLE_EC2</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AWS_ENABLE_S3</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AWS_ENABLE_RDS</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AWS_ENABLE_IAM</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AWS_ENABLE_VPC</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># AWS Polling Settings</span>
<span class="nv">AWS_POLL_INTERVAL</span><span class="o">=</span><span class="m">300</span><span class="w">  </span><span class="c1"># 5 minutes</span>
<span class="nv">AWS_MAX_RETRIES</span><span class="o">=</span><span class="m">3</span>
<span class="nv">AWS_RETRY_DELAY</span><span class="o">=</span><span class="m">5</span>
</pre></div>
</div>
<p><strong>Azure Integration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Azure Configuration</span>
<span class="nv">AZURE_CLIENT_ID</span><span class="o">=</span><span class="s2">&quot;your-azure-client-id&quot;</span>
<span class="nv">AZURE_CLIENT_SECRET</span><span class="o">=</span><span class="s2">&quot;your-azure-client-secret&quot;</span>
<span class="nv">AZURE_TENANT_ID</span><span class="o">=</span><span class="s2">&quot;your-azure-tenant-id&quot;</span>
<span class="nv">AZURE_SUBSCRIPTION_ID</span><span class="o">=</span><span class="s2">&quot;your-azure-subscription-id&quot;</span>

<span class="c1"># Azure Services</span>
<span class="nv">AZURE_ENABLE_COMPUTE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AZURE_ENABLE_STORAGE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AZURE_ENABLE_NETWORK</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AZURE_ENABLE_SECURITY</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># Azure Polling Settings</span>
<span class="nv">AZURE_POLL_INTERVAL</span><span class="o">=</span><span class="m">300</span><span class="w">  </span><span class="c1"># 5 minutes</span>
<span class="nv">AZURE_MAX_RETRIES</span><span class="o">=</span><span class="m">3</span>
</pre></div>
</div>
<p><strong>Google Cloud Platform:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># GCP Configuration</span>
<span class="nv">GOOGLE_APPLICATION_CREDENTIALS</span><span class="o">=</span><span class="s2">&quot;/path/to/service-account.json&quot;</span>
<span class="nv">GCP_PROJECT_ID</span><span class="o">=</span><span class="s2">&quot;your-gcp-project-id&quot;</span>
<span class="nv">GCP_DEFAULT_REGION</span><span class="o">=</span><span class="s2">&quot;us-central1&quot;</span>

<span class="c1"># GCP Services</span>
<span class="nv">GCP_ENABLE_COMPUTE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">GCP_ENABLE_STORAGE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">GCP_ENABLE_NETWORK</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">GCP_ENABLE_IAM</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># GCP Polling Settings</span>
<span class="nv">GCP_POLL_INTERVAL</span><span class="o">=</span><span class="m">300</span><span class="w">  </span><span class="c1"># 5 minutes</span>
</pre></div>
</div>
</section>
<section id="servicenow-integration">
<h3>ServiceNow Integration<a class="headerlink" href="#servicenow-integration" title="Link to this heading"></a></h3>
<p><strong>ServiceNow CMDB:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># ServiceNow Configuration</span>
<span class="nv">SERVICENOW_INSTANCE</span><span class="o">=</span><span class="s2">&quot;your-instance.service-now.com&quot;</span>
<span class="nv">SERVICENOW_USERNAME</span><span class="o">=</span><span class="s2">&quot;integration-user&quot;</span>
<span class="nv">SERVICENOW_PASSWORD</span><span class="o">=</span><span class="s2">&quot;integration-password&quot;</span>
<span class="nv">SERVICENOW_API_VERSION</span><span class="o">=</span><span class="s2">&quot;v1&quot;</span>
<span class="nv">SERVICENOW_TIMEOUT</span><span class="o">=</span><span class="m">30</span>

<span class="c1"># CMDB Sync Settings</span>
<span class="nv">SERVICENOW_SYNC_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">SERVICENOW_SYNC_INTERVAL</span><span class="o">=</span><span class="m">3600</span><span class="w">  </span><span class="c1"># 1 hour</span>
<span class="nv">SERVICENOW_BATCH_SIZE</span><span class="o">=</span><span class="m">100</span>

<span class="c1"># Incident Management</span>
<span class="nv">SERVICENOW_CREATE_INCIDENTS</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">SERVICENOW_INCIDENT_CATEGORY</span><span class="o">=</span><span class="s2">&quot;Security&quot;</span>
<span class="nv">SERVICENOW_INCIDENT_SUBCATEGORY</span><span class="o">=</span><span class="s2">&quot;Attack Path&quot;</span>
</pre></div>
</div>
</section>
<section id="threat-intelligence-settings">
<h3>Threat Intelligence Settings<a class="headerlink" href="#threat-intelligence-settings" title="Link to this heading"></a></h3>
<p><strong>STIX/TAXII Configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Threat Intelligence</span>
<span class="nv">THREAT_INTEL_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">THREAT_INTEL_UPDATE_INTERVAL</span><span class="o">=</span><span class="m">3600</span><span class="w">  </span><span class="c1"># 1 hour</span>

<span class="c1"># TAXII Server Configuration</span>
<span class="nv">TAXII_SERVER_URL</span><span class="o">=</span><span class="s2">&quot;https://your-taxii-server.com/taxii2/&quot;</span>
<span class="nv">TAXII_USERNAME</span><span class="o">=</span><span class="s2">&quot;taxii-user&quot;</span>
<span class="nv">TAXII_PASSWORD</span><span class="o">=</span><span class="s2">&quot;taxii-password&quot;</span>
<span class="nv">TAXII_COLLECTION_ID</span><span class="o">=</span><span class="s2">&quot;your-collection-id&quot;</span>

<span class="c1"># IOC Processing</span>
<span class="nv">IOC_RETENTION_DAYS</span><span class="o">=</span><span class="m">90</span>
<span class="nv">IOC_CONFIDENCE_THRESHOLD</span><span class="o">=</span><span class="m">75</span>
<span class="nv">IOC_AUTO_CORRELATION</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
</section>
</section>
<section id="monitoring-and-logging">
<h2>Monitoring and Logging<a class="headerlink" href="#monitoring-and-logging" title="Link to this heading"></a></h2>
<section id="logging-configuration">
<h3>Logging Configuration<a class="headerlink" href="#logging-configuration" title="Link to this heading"></a></h3>
<p><strong>Application Logging:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Logging Settings</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;info&quot;</span><span class="w">  </span><span class="c1"># debug, info, warning, error, critical</span>
<span class="nv">LOG_FORMAT</span><span class="o">=</span><span class="s2">&quot;json&quot;</span><span class="w">  </span><span class="c1"># text, json</span>
<span class="nv">LOG_FILE_PATH</span><span class="o">=</span><span class="s2">&quot;/var/log/blast-radius/app.log&quot;</span>
<span class="nv">LOG_MAX_SIZE</span><span class="o">=</span><span class="s2">&quot;100MB&quot;</span>
<span class="nv">LOG_BACKUP_COUNT</span><span class="o">=</span><span class="m">10</span>

<span class="c1"># Structured Logging</span>
<span class="nv">LOG_INCLUDE_TIMESTAMP</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">LOG_INCLUDE_LEVEL</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">LOG_INCLUDE_LOGGER_NAME</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">LOG_INCLUDE_THREAD_ID</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
<p><strong>Audit Logging:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Audit Configuration</span>
<span class="nv">AUDIT_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AUDIT_LOG_PATH</span><span class="o">=</span><span class="s2">&quot;/var/log/blast-radius/audit.log&quot;</span>
<span class="nv">AUDIT_LOG_ALL_REQUESTS</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">AUDIT_LOG_SENSITIVE_DATA</span><span class="o">=</span><span class="nb">false</span>

<span class="c1"># Audit Events</span>
<span class="nv">AUDIT_LOGIN_EVENTS</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AUDIT_PERMISSION_CHANGES</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AUDIT_DATA_ACCESS</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AUDIT_CONFIGURATION_CHANGES</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
</section>
<section id="monitoring-settings">
<h3>Monitoring Settings<a class="headerlink" href="#monitoring-settings" title="Link to this heading"></a></h3>
<p><strong>Health Checks:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Health Check Configuration</span>
<span class="nv">HEALTH_CHECK_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">HEALTH_CHECK_INTERVAL</span><span class="o">=</span><span class="m">30</span><span class="w">  </span><span class="c1"># seconds</span>
<span class="nv">HEALTH_CHECK_TIMEOUT</span><span class="o">=</span><span class="m">10</span><span class="w">  </span><span class="c1"># seconds</span>

<span class="c1"># Component Health Checks</span>
<span class="nv">HEALTH_CHECK_DATABASE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">HEALTH_CHECK_REDIS</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">HEALTH_CHECK_NEO4J</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">HEALTH_CHECK_EXTERNAL_APIS</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
<p><strong>Metrics Collection:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Metrics Configuration</span>
<span class="nv">METRICS_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">METRICS_ENDPOINT</span><span class="o">=</span><span class="s2">&quot;/metrics&quot;</span>
<span class="nv">METRICS_INCLUDE_SYSTEM</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">METRICS_INCLUDE_APPLICATION</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># Prometheus Integration</span>
<span class="nv">PROMETHEUS_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">PROMETHEUS_PORT</span><span class="o">=</span><span class="m">9090</span>
<span class="nv">PROMETHEUS_NAMESPACE</span><span class="o">=</span><span class="s2">&quot;blast_radius&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="environment-specific-configuration">
<h2>Environment-Specific Configuration<a class="headerlink" href="#environment-specific-configuration" title="Link to this heading"></a></h2>
<section id="development-environment">
<h3>Development Environment<a class="headerlink" href="#development-environment" title="Link to this heading"></a></h3>
<p><strong>.env.development:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">DEBUG</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;debug&quot;</span>
<span class="nv">DATABASE_ECHO</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;http://localhost:3000&quot;]&#39;</span>
<span class="nv">DOCS_URL</span><span class="o">=</span><span class="s2">&quot;/docs&quot;</span>
<span class="nv">REDOC_URL</span><span class="o">=</span><span class="s2">&quot;/redoc&quot;</span>

<span class="c1"># Relaxed security for development</span>
<span class="nv">JWT_EXPIRE_MINUTES</span><span class="o">=</span><span class="m">480</span><span class="w">  </span><span class="c1"># 8 hours</span>
<span class="nv">SESSION_COOKIE_SECURE</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">MFA_REQUIRE_FOR_ALL</span><span class="o">=</span><span class="nb">false</span>
</pre></div>
</div>
</section>
<section id="staging-environment">
<h3>Staging Environment<a class="headerlink" href="#staging-environment" title="Link to this heading"></a></h3>
<p><strong>.env.staging:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;info&quot;</span>
<span class="nv">DATABASE_ECHO</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;https://staging.yourapp.com&quot;]&#39;</span>
<span class="nv">DOCS_URL</span><span class="o">=</span><span class="s2">&quot;/docs&quot;</span><span class="w">  </span><span class="c1"># Keep docs in staging</span>

<span class="c1"># Moderate security for staging</span>
<span class="nv">JWT_EXPIRE_MINUTES</span><span class="o">=</span><span class="m">60</span>
<span class="nv">SESSION_COOKIE_SECURE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">MFA_REQUIRE_FOR_ADMINS</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
</section>
<section id="production-environment">
<h3>Production Environment<a class="headerlink" href="#production-environment" title="Link to this heading"></a></h3>
<p><strong>.env.production:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;warning&quot;</span>
<span class="nv">DATABASE_ECHO</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;https://yourapp.com&quot;]&#39;</span>
<span class="nv">DOCS_URL</span><span class="o">=</span>null<span class="w">  </span><span class="c1"># Disable docs in production</span>
<span class="nv">REDOC_URL</span><span class="o">=</span>null

<span class="c1"># Maximum security for production</span>
<span class="nv">SESSION_COOKIE_SECURE</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">MFA_REQUIRE_FOR_ALL</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">RATE_LIMIT_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">AUDIT_ENABLED</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
</section>
</section>
<section id="configuration-validation">
<h2>Configuration Validation<a class="headerlink" href="#configuration-validation" title="Link to this heading"></a></h2>
<p>The application automatically validates all configuration settings on startup. Invalid configurations will prevent the application from starting and display detailed error messages.</p>
<p><strong>Common Validation Errors:</strong></p>
<ul class="simple">
<li><p>Invalid database connection strings</p></li>
<li><p>Missing required encryption keys</p></li>
<li><p>Invalid JWT configuration</p></li>
<li><p>Incorrect cloud provider credentials</p></li>
<li><p>Invalid SSL certificate paths</p></li>
</ul>
<p><strong>Configuration Testing:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Test configuration</span>
<span class="nb">cd</span><span class="w"> </span>backend
python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>validate-config

<span class="c1"># Test specific components</span>
python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>test-database
python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>test-redis
python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>test-integrations
</pre></div>
</div>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="security-best-practices">
<h3>Security Best Practices<a class="headerlink" href="#security-best-practices" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Use strong, unique encryption keys</strong> for each environment</p></li>
<li><p><strong>Enable MFA</strong> for all administrative accounts</p></li>
<li><p><strong>Regularly rotate</strong> JWT secrets and encryption keys</p></li>
<li><p><strong>Use environment variables</strong> instead of hardcoded secrets</p></li>
<li><p><strong>Enable audit logging</strong> for all production environments</p></li>
<li><p><strong>Implement proper SSL/TLS</strong> configuration</p></li>
<li><p><strong>Use least privilege</strong> access for service accounts</p></li>
</ol>
</section>
<section id="performance-best-practices">
<h3>Performance Best Practices<a class="headerlink" href="#performance-best-practices" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Tune database connection pools</strong> based on load</p></li>
<li><p><strong>Configure appropriate cache TTL</strong> values</p></li>
<li><p><strong>Enable compression</strong> for API responses</p></li>
<li><p><strong>Use CDN</strong> for static assets</p></li>
<li><p><strong>Monitor and adjust</strong> rate limits based on usage</p></li>
<li><p><strong>Implement proper indexing</strong> for database queries</p></li>
</ol>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<p>For configuration-related issues, check:</p>
<ol class="arabic simple">
<li><p><strong>Application logs</strong> for validation errors</p></li>
<li><p><strong>Environment variable</strong> syntax and values</p></li>
<li><p><strong>File permissions</strong> for configuration files</p></li>
<li><p><strong>Network connectivity</strong> to external services</p></li>
<li><p><strong>SSL certificate</strong> validity and paths</p></li>
</ol>
<p>For detailed troubleshooting, see <span class="xref std std-doc">troubleshooting/common-issues</span>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Configuration changes may require application restart to take effect.
Some settings can be updated through the admin interface without restart.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="installation.html" class="btn btn-neutral float-left" title="Installation Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="quick-start-guide.html" class="btn btn-neutral float-right" title="Quick Start Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>