

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Frequently Asked Questions (FAQ) &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="prev" title="Product Requirements Document (PRD)" href="../technical/product-requirements.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Frequently Asked Questions (FAQ)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#general-questions">General Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#what-is-the-blast-radius-security-tool">What is the Blast-Radius Security Tool?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#who-should-use-this-tool">Who should use this tool?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#what-are-the-system-requirements">What are the system requirements?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-install-the-blast-radius-security-tool">How do I install the Blast-Radius Security Tool?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#what-are-the-default-login-credentials">What are the default login credentials?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-reset-the-admin-password">How do I reset the admin password?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#why-can-t-i-access-the-web-interface">Why can’t I access the web interface?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration-and-usage">Configuration and Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-configure-cloud-provider-integrations">How do I configure cloud provider integrations?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-add-new-users">How do I add new users?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-enable-multi-factor-authentication-mfa">How do I enable Multi-Factor Authentication (MFA)?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#how-does-attack-path-analysis-work">How does attack path analysis work?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-long-does-attack-path-analysis-take">How long does attack path analysis take?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#why-am-i-not-seeing-any-attack-paths">Why am I not seeing any attack paths?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#can-i-customize-risk-scoring">Can I customize risk scoring?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-and-troubleshooting">Performance and Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#why-is-the-platform-running-slowly">Why is the platform running slowly?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-backup-my-data">How do I backup my data?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-update-to-a-new-version">How do I update to a new version?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-and-api">Integration and API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-integrate-with-my-siem">How do I integrate with my SIEM?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#can-i-use-the-api-for-automation">Can I use the API for automation?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-get-api-credentials">How do I get API credentials?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-and-compliance">Security and Compliance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#is-my-data-secure">Is my data secure?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-configure-audit-logging">How do I configure audit logging?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#can-i-run-this-in-an-air-gapped-environment">Can I run this in an air-gapped environment?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#support-and-community">Support and Community</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#how-do-i-get-help">How do I get help?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#is-there-a-community-or-forum">Is there a community or forum?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#how-can-i-contribute">How can I contribute?</a></li>
<li class="toctree-l3"><a class="reference internal" href="#what-s-the-roadmap-for-future-features">What’s the roadmap for future features?</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Frequently Asked Questions (FAQ)</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/troubleshooting/faq.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="frequently-asked-questions-faq">
<h1>Frequently Asked Questions (FAQ)<a class="headerlink" href="#frequently-asked-questions-faq" title="Link to this heading"></a></h1>
<p>This document answers the most commonly asked questions about the Blast-Radius Security Tool.</p>
<section id="general-questions">
<h2>General Questions<a class="headerlink" href="#general-questions" title="Link to this heading"></a></h2>
<section id="what-is-the-blast-radius-security-tool">
<h3>What is the Blast-Radius Security Tool?<a class="headerlink" href="#what-is-the-blast-radius-security-tool" title="Link to this heading"></a></h3>
<p>The Blast-Radius Security Tool is a comprehensive security platform designed for purple teams, SOC operators, security architects, and red teamers. It provides real-time attack path analysis, multi-cloud integration, and deep ServiceNow CMDB integration to enable proactive security posture management and incident response.</p>
<p><strong>Key Features:</strong></p>
<ul class="simple">
<li><p>Real-time attack path analysis with 5-degree mapping</p></li>
<li><p>Multi-cloud integration (AWS, Azure, GCP)</p></li>
<li><p>Role-based dashboards for different security roles</p></li>
<li><p>Threat intelligence integration with STIX/TAXII</p></li>
<li><p>Automated remediation workflows</p></li>
<li><p>ServiceNow CMDB bi-directional synchronization</p></li>
</ul>
</section>
<section id="who-should-use-this-tool">
<h3>Who should use this tool?<a class="headerlink" href="#who-should-use-this-tool" title="Link to this heading"></a></h3>
<p>The platform is designed for various security professionals:</p>
<ul class="simple">
<li><p><strong>SOC Operators</strong>: Real-time monitoring and incident response</p></li>
<li><p><strong>Security Architects</strong>: Risk assessment and security design</p></li>
<li><p><strong>Red Team Members</strong>: Attack simulation and path discovery</p></li>
<li><p><strong>Purple Team Members</strong>: Collaborative security testing and validation</p></li>
<li><p><strong>Administrators</strong>: Platform management and configuration</p></li>
</ul>
</section>
<section id="what-are-the-system-requirements">
<h3>What are the system requirements?<a class="headerlink" href="#what-are-the-system-requirements" title="Link to this heading"></a></h3>
<p><strong>Minimum Requirements:</strong></p>
<ul class="simple">
<li><p>8GB RAM (16GB+ recommended for production)</p></li>
<li><p>4 CPU cores (8+ cores recommended)</p></li>
<li><p>50GB storage (100GB+ recommended)</p></li>
<li><p>Modern web browser (Chrome 90+, Firefox 88+, Safari 14+)</p></li>
</ul>
<p><strong>Software Requirements:</strong></p>
<ul class="simple">
<li><p>Docker and Docker Compose</p></li>
<li><p>Python 3.11+ (for development)</p></li>
<li><p>Node.js 18+ (for frontend development)</p></li>
<li><p>PostgreSQL 15+, Redis 7+, Neo4j 5.15+</p></li>
</ul>
</section>
</section>
<section id="installation-and-setup">
<h2>Installation and Setup<a class="headerlink" href="#installation-and-setup" title="Link to this heading"></a></h2>
<section id="how-do-i-install-the-blast-radius-security-tool">
<h3>How do I install the Blast-Radius Security Tool?<a class="headerlink" href="#how-do-i-install-the-blast-radius-security-tool" title="Link to this heading"></a></h3>
<p>The quickest way is using Docker:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/blast-radius.git
<span class="nb">cd</span><span class="w"> </span>blast-radius

<span class="c1"># Start all services</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Access the platform</span>
open<span class="w"> </span>http://localhost:3000
</pre></div>
</div>
<p>For detailed installation instructions, see the <a class="reference internal" href="../installation.html"><span class="doc">Installation Guide</span></a> guide.</p>
</section>
<section id="what-are-the-default-login-credentials">
<h3>What are the default login credentials?<a class="headerlink" href="#what-are-the-default-login-credentials" title="Link to this heading"></a></h3>
<p><strong>Default Credentials:</strong></p>
<ul class="simple">
<li><p><strong>Email</strong>: <a class="reference external" href="mailto:admin&#37;&#52;&#48;blastradius&#46;local">admin<span>&#64;</span>blastradius<span>&#46;</span>local</a></p></li>
<li><p><strong>Password</strong>: BlastRadius2024!</p></li>
</ul>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><strong>Security Notice</strong>: Change the default password immediately after first login!</p>
</div>
</section>
<section id="how-do-i-reset-the-admin-password">
<h3>How do I reset the admin password?<a class="headerlink" href="#how-do-i-reset-the-admin-password" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reset admin password</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>backend<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>reset-admin-password

<span class="c1"># Or create a new admin user</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>backend<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>create-admin<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--email<span class="w"> </span><EMAIL><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--password<span class="w"> </span><span class="s2">&quot;NewSecurePassword123!&quot;</span>
</pre></div>
</div>
</section>
<section id="why-can-t-i-access-the-web-interface">
<h3>Why can’t I access the web interface?<a class="headerlink" href="#why-can-t-i-access-the-web-interface" title="Link to this heading"></a></h3>
<p><strong>Common Solutions:</strong></p>
<ol class="arabic">
<li><p><strong>Check if services are running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>ps
</pre></div>
</div>
</li>
<li><p><strong>Verify port availability</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>netstat<span class="w"> </span>-tulpn<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>:3000
</pre></div>
</div>
</li>
<li><p><strong>Check firewall settings</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>sudo<span class="w"> </span>ufw<span class="w"> </span>allow<span class="w"> </span><span class="m">3000</span>
sudo<span class="w"> </span>ufw<span class="w"> </span>allow<span class="w"> </span><span class="m">8000</span>
</pre></div>
</div>
</li>
<li><p><strong>Review application logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>logs<span class="w"> </span>frontend
docker-compose<span class="w"> </span>logs<span class="w"> </span>backend
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="configuration-and-usage">
<h2>Configuration and Usage<a class="headerlink" href="#configuration-and-usage" title="Link to this heading"></a></h2>
<section id="how-do-i-configure-cloud-provider-integrations">
<h3>How do I configure cloud provider integrations?<a class="headerlink" href="#how-do-i-configure-cloud-provider-integrations" title="Link to this heading"></a></h3>
<p><strong>AWS Integration:</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Integrations” → “Cloud Providers” → “AWS”</p></li>
<li><p>Enter your AWS credentials:</p>
<ul class="simple">
<li><p>Access Key ID</p></li>
<li><p>Secret Access Key</p></li>
<li><p>Default Region</p></li>
</ul>
</li>
<li><p>Test the connection and start asset discovery</p></li>
</ol>
<p><strong>Azure Integration:</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Integrations” → “Cloud Providers” → “Azure”</p></li>
<li><p>Enter your Azure credentials:</p>
<ul class="simple">
<li><p>Client ID</p></li>
<li><p>Client Secret</p></li>
<li><p>Tenant ID</p></li>
<li><p>Subscription ID</p></li>
</ul>
</li>
</ol>
<p><strong>Required Permissions:</strong></p>
<ul class="simple">
<li><p>AWS: EC2, IAM, VPC read permissions</p></li>
<li><p>Azure: Reader role on subscription</p></li>
<li><p>GCP: Compute Viewer, Security Reviewer roles</p></li>
</ul>
</section>
<section id="how-do-i-add-new-users">
<h3>How do I add new users?<a class="headerlink" href="#how-do-i-add-new-users" title="Link to this heading"></a></h3>
<p><strong>Via Web Interface:</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Administration” → “Users”</p></li>
<li><p>Click “Add User”</p></li>
<li><p>Fill in user details and assign role</p></li>
<li><p>Send invitation email to user</p></li>
</ol>
<p><strong>Via Command Line:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>backend<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>create-user<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--email<span class="w"> </span><EMAIL><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--first-name<span class="w"> </span><span class="s2">&quot;John&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--last-name<span class="w"> </span><span class="s2">&quot;Doe&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--role<span class="w"> </span><span class="s2">&quot;soc_operator&quot;</span>
</pre></div>
</div>
</section>
<section id="how-do-i-enable-multi-factor-authentication-mfa">
<h3>How do I enable Multi-Factor Authentication (MFA)?<a class="headerlink" href="#how-do-i-enable-multi-factor-authentication-mfa" title="Link to this heading"></a></h3>
<p><strong>For Individual Users:</strong></p>
<ol class="arabic simple">
<li><p>Go to “Profile Settings” → “Security”</p></li>
<li><p>Click “Enable MFA”</p></li>
<li><p>Scan QR code with authenticator app</p></li>
<li><p>Enter verification code</p></li>
<li><p>Save backup codes securely</p></li>
</ol>
<p><strong>For All Users (Admin):</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Administration” → “Security Settings”</p></li>
<li><p>Enable “Require MFA for all users”</p></li>
<li><p>Set grace period for existing users</p></li>
</ol>
</section>
</section>
<section id="attack-path-analysis">
<h2>Attack Path Analysis<a class="headerlink" href="#attack-path-analysis" title="Link to this heading"></a></h2>
<section id="how-does-attack-path-analysis-work">
<h3>How does attack path analysis work?<a class="headerlink" href="#how-does-attack-path-analysis-work" title="Link to this heading"></a></h3>
<p>The attack path analysis engine uses graph algorithms to:</p>
<ol class="arabic simple">
<li><p><strong>Map Assets</strong>: Discover and map all assets and their relationships</p></li>
<li><p><strong>Identify Vulnerabilities</strong>: Correlate vulnerability data with assets</p></li>
<li><p><strong>Calculate Paths</strong>: Use graph traversal to find potential attack routes</p></li>
<li><p><strong>Score Risk</strong>: Apply MITRE ATT&amp;CK framework for risk scoring</p></li>
<li><p><strong>Visualize Results</strong>: Present interactive graphs and detailed reports</p></li>
</ol>
<p>The analysis considers:</p>
<ul class="simple">
<li><p>Asset relationships and dependencies</p></li>
<li><p>Vulnerability exploitability and impact</p></li>
<li><p>Security control effectiveness</p></li>
<li><p>Network topology and access controls</p></li>
</ul>
</section>
<section id="how-long-does-attack-path-analysis-take">
<h3>How long does attack path analysis take?<a class="headerlink" href="#how-long-does-attack-path-analysis-take" title="Link to this heading"></a></h3>
<p><strong>Typical Analysis Times:</strong></p>
<ul class="simple">
<li><p><strong>Small Environment</strong> (&lt; 1,000 assets): 5-15 seconds</p></li>
<li><p><strong>Medium Environment</strong> (1,000-10,000 assets): 15-60 seconds</p></li>
<li><p><strong>Large Environment</strong> (10,000+ assets): 1-5 minutes</p></li>
</ul>
<p><strong>Factors Affecting Performance:</strong></p>
<ul class="simple">
<li><p>Number of assets and relationships</p></li>
<li><p>Complexity of network topology</p></li>
<li><p>Analysis depth (number of hops)</p></li>
<li><p>System resources (CPU, memory)</p></li>
</ul>
</section>
<section id="why-am-i-not-seeing-any-attack-paths">
<h3>Why am I not seeing any attack paths?<a class="headerlink" href="#why-am-i-not-seeing-any-attack-paths" title="Link to this heading"></a></h3>
<p><strong>Common Causes:</strong></p>
<ol class="arabic simple">
<li><p><strong>Insufficient Asset Data</strong>: Ensure asset discovery is complete</p></li>
<li><p><strong>No Vulnerabilities</strong>: Check vulnerability data sources</p></li>
<li><p><strong>Restrictive Filters</strong>: Review analysis parameters and filters</p></li>
<li><p><strong>Network Segmentation</strong>: Strong segmentation may limit paths</p></li>
<li><p><strong>Analysis Scope</strong>: Verify source and target configurations</p></li>
</ol>
<p><strong>Troubleshooting Steps:</strong></p>
<ol class="arabic simple">
<li><p>Check asset inventory completeness</p></li>
<li><p>Verify vulnerability data is current</p></li>
<li><p>Adjust analysis parameters (increase hops, change filters)</p></li>
<li><p>Review network topology and relationships</p></li>
</ol>
</section>
<section id="can-i-customize-risk-scoring">
<h3>Can I customize risk scoring?<a class="headerlink" href="#can-i-customize-risk-scoring" title="Link to this heading"></a></h3>
<p>Yes, risk scoring can be customized:</p>
<p><strong>Available Customizations:</strong></p>
<ul class="simple">
<li><p><strong>Vulnerability Scoring</strong>: Adjust CVSS score weighting</p></li>
<li><p><strong>Asset Criticality</strong>: Define business impact scores</p></li>
<li><p><strong>Control Effectiveness</strong>: Set security control ratings</p></li>
<li><p><strong>Threat Intelligence</strong>: Incorporate threat actor capabilities</p></li>
</ul>
<p><strong>Configuration Location:</strong></p>
<p>Navigate to “Administration” → “Risk Configuration” to modify scoring parameters.</p>
</section>
</section>
<section id="performance-and-troubleshooting">
<h2>Performance and Troubleshooting<a class="headerlink" href="#performance-and-troubleshooting" title="Link to this heading"></a></h2>
<section id="why-is-the-platform-running-slowly">
<h3>Why is the platform running slowly?<a class="headerlink" href="#why-is-the-platform-running-slowly" title="Link to this heading"></a></h3>
<p><strong>Common Performance Issues:</strong></p>
<ol class="arabic simple">
<li><p><strong>Insufficient Resources</strong>: Increase RAM and CPU allocation</p></li>
<li><p><strong>Database Performance</strong>: Optimize queries and indexing</p></li>
<li><p><strong>Large Datasets</strong>: Consider data archival and cleanup</p></li>
<li><p><strong>Network Latency</strong>: Check network connectivity and bandwidth</p></li>
</ol>
<p><strong>Performance Optimization:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check resource usage</span>
docker<span class="w"> </span>stats

<span class="c1"># Increase memory limits in docker-compose.yml</span>
<span class="c1"># Clear cache</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>redis<span class="w"> </span>redis-cli<span class="w"> </span>FLUSHALL

<span class="c1"># Restart services</span>
docker-compose<span class="w"> </span>restart
</pre></div>
</div>
</section>
<section id="how-do-i-backup-my-data">
<h3>How do I backup my data?<a class="headerlink" href="#how-do-i-backup-my-data" title="Link to this heading"></a></h3>
<p><strong>Database Backup:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># PostgreSQL backup</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>postgres<span class="w"> </span>pg_dump<span class="w"> </span>-U<span class="w"> </span>blast_user<span class="w"> </span>blast_radius<span class="w"> </span>&gt;<span class="w"> </span>backup.sql

<span class="c1"># Neo4j backup</span>
docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>neo4j<span class="w"> </span>neo4j-admin<span class="w"> </span>dump<span class="w"> </span>--database<span class="o">=</span>neo4j<span class="w"> </span>--to<span class="o">=</span>/backups/
</pre></div>
</div>
<p><strong>Configuration Backup:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Backup configuration files</span>
cp<span class="w"> </span>.env<span class="w"> </span>.env.backup
cp<span class="w"> </span>docker-compose.yml<span class="w"> </span>docker-compose.yml.backup
</pre></div>
</div>
<p><strong>Automated Backup:</strong></p>
<p>Set up automated backups using cron jobs or backup tools. See the <a class="reference internal" href="../user-guides/administrators.html"><span class="doc">Administrators Guide</span></a> guide for detailed backup procedures.</p>
</section>
<section id="how-do-i-update-to-a-new-version">
<h3>How do I update to a new version?<a class="headerlink" href="#how-do-i-update-to-a-new-version" title="Link to this heading"></a></h3>
<p><strong>Update Process:</strong></p>
<ol class="arabic simple">
<li><p><strong>Backup Data</strong>: Create full backup before updating</p></li>
<li><p><strong>Stop Services</strong>: <code class="docutils literal notranslate"><span class="pre">docker-compose</span> <span class="pre">down</span></code></p></li>
<li><p><strong>Update Code</strong>: <code class="docutils literal notranslate"><span class="pre">git</span> <span class="pre">pull</span> <span class="pre">origin</span> <span class="pre">main</span></code></p></li>
<li><p><strong>Update Images</strong>: <code class="docutils literal notranslate"><span class="pre">docker-compose</span> <span class="pre">pull</span></code></p></li>
<li><p><strong>Start Services</strong>: <code class="docutils literal notranslate"><span class="pre">docker-compose</span> <span class="pre">up</span> <span class="pre">-d</span></code></p></li>
<li><p><strong>Verify Update</strong>: Check version and functionality</p></li>
</ol>
<p><strong>Migration Notes:</strong></p>
<p>Some updates may require database migrations. Check the <span class="xref std std-doc">../releases/changelog</span> for specific update instructions.</p>
</section>
</section>
<section id="integration-and-api">
<h2>Integration and API<a class="headerlink" href="#integration-and-api" title="Link to this heading"></a></h2>
<section id="how-do-i-integrate-with-my-siem">
<h3>How do I integrate with my SIEM?<a class="headerlink" href="#how-do-i-integrate-with-my-siem" title="Link to this heading"></a></h3>
<p><strong>Log Forwarding:</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Integrations” → “SIEM”</p></li>
<li><p>Configure log forwarding settings:</p>
<ul class="simple">
<li><p>SIEM endpoint URL</p></li>
<li><p>Authentication credentials</p></li>
<li><p>Log format (CEF, LEEF, JSON)</p></li>
<li><p>Event filters</p></li>
</ul>
</li>
</ol>
<p><strong>API Integration:</strong></p>
<p>Use the REST API to pull data into your SIEM:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get security events</span>
curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer &lt;token&gt;&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span><span class="s2">&quot;https://api.blastradius.com/api/v1/events&quot;</span>
</pre></div>
</div>
</section>
<section id="can-i-use-the-api-for-automation">
<h3>Can I use the API for automation?<a class="headerlink" href="#can-i-use-the-api-for-automation" title="Link to this heading"></a></h3>
<p>Yes, the platform provides a comprehensive REST API:</p>
<p><strong>API Features:</strong></p>
<ul class="simple">
<li><p>Complete CRUD operations for all resources</p></li>
<li><p>Webhook support for real-time notifications</p></li>
<li><p>Rate limiting and authentication</p></li>
<li><p>OpenAPI 3.0 specification</p></li>
<li><p>Interactive documentation at <code class="docutils literal notranslate"><span class="pre">/docs</span></code></p></li>
</ul>
<p><strong>Example Automation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>

<span class="c1"># Trigger attack path analysis</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;https://api.blastradius.com/api/v1/analysis&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer &lt;token&gt;&quot;</span><span class="p">},</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;source&quot;</span><span class="p">:</span> <span class="s2">&quot;external&quot;</span><span class="p">,</span>
        <span class="s2">&quot;target&quot;</span><span class="p">:</span> <span class="s2">&quot;crown_jewels&quot;</span><span class="p">,</span>
        <span class="s2">&quot;max_hops&quot;</span><span class="p">:</span> <span class="mi">5</span>
    <span class="p">}</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="how-do-i-get-api-credentials">
<h3>How do I get API credentials?<a class="headerlink" href="#how-do-i-get-api-credentials" title="Link to this heading"></a></h3>
<p><strong>API Token Generation:</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Profile Settings” → “API Tokens”</p></li>
<li><p>Click “Generate New Token”</p></li>
<li><p>Set token permissions and expiration</p></li>
<li><p>Copy and securely store the token</p></li>
</ol>
<p><strong>Service Account Tokens:</strong></p>
<p>For automation, create dedicated service accounts with API-only access.</p>
</section>
</section>
<section id="security-and-compliance">
<h2>Security and Compliance<a class="headerlink" href="#security-and-compliance" title="Link to this heading"></a></h2>
<section id="is-my-data-secure">
<h3>Is my data secure?<a class="headerlink" href="#is-my-data-secure" title="Link to this heading"></a></h3>
<p><strong>Security Measures:</strong></p>
<ul class="simple">
<li><p><strong>Encryption</strong>: AES-256 for data at rest, TLS 1.3 for data in transit</p></li>
<li><p><strong>Authentication</strong>: Multi-factor authentication required</p></li>
<li><p><strong>Authorization</strong>: Role-based access control with fine-grained permissions</p></li>
<li><p><strong>Audit Logging</strong>: Comprehensive logging of all user actions</p></li>
<li><p><strong>Network Security</strong>: Secure network architecture with segmentation</p></li>
</ul>
<p><strong>Compliance:</strong></p>
<ul class="simple">
<li><p>SOC 2 Type II compliance ready</p></li>
<li><p>GDPR compliant data handling</p></li>
<li><p>ISO 27001 security controls alignment</p></li>
<li><p>Regular security assessments and penetration testing</p></li>
</ul>
</section>
<section id="how-do-i-configure-audit-logging">
<h3>How do I configure audit logging?<a class="headerlink" href="#how-do-i-configure-audit-logging" title="Link to this heading"></a></h3>
<p><strong>Audit Configuration:</strong></p>
<ol class="arabic simple">
<li><p>Navigate to “Administration” → “Audit Settings”</p></li>
<li><p>Enable audit logging for required events:</p>
<ul class="simple">
<li><p>User authentication and authorization</p></li>
<li><p>Data access and modifications</p></li>
<li><p>Configuration changes</p></li>
<li><p>Administrative actions</p></li>
</ul>
</li>
<li><p>Configure log retention and archival policies</p></li>
</ol>
<p><strong>Log Access:</strong></p>
<p>Audit logs are available through:</p>
<ul class="simple">
<li><p>Web interface: “Administration” → “Audit Logs”</p></li>
<li><p>API endpoint: <code class="docutils literal notranslate"><span class="pre">/api/v1/audit/logs</span></code></p></li>
<li><p>Log files: <code class="docutils literal notranslate"><span class="pre">/var/log/blast-radius/audit.log</span></code></p></li>
</ul>
</section>
<section id="can-i-run-this-in-an-air-gapped-environment">
<h3>Can I run this in an air-gapped environment?<a class="headerlink" href="#can-i-run-this-in-an-air-gapped-environment" title="Link to this heading"></a></h3>
<p>Yes, with some limitations:</p>
<p><strong>Requirements:</strong></p>
<ul class="simple">
<li><p>All container images must be pre-loaded</p></li>
<li><p>Threat intelligence feeds require manual updates</p></li>
<li><p>Cloud integrations will not work</p></li>
<li><p>External API integrations must be disabled</p></li>
</ul>
<p><strong>Setup Process:</strong></p>
<ol class="arabic simple">
<li><p>Download all required container images</p></li>
<li><p>Transfer to air-gapped environment</p></li>
<li><p>Load images: <code class="docutils literal notranslate"><span class="pre">docker</span> <span class="pre">load</span> <span class="pre">&lt;</span> <span class="pre">images.tar</span></code></p></li>
<li><p>Configure for offline operation</p></li>
<li><p>Set up manual threat intelligence updates</p></li>
</ol>
</section>
</section>
<section id="support-and-community">
<h2>Support and Community<a class="headerlink" href="#support-and-community" title="Link to this heading"></a></h2>
<section id="how-do-i-get-help">
<h3>How do I get help?<a class="headerlink" href="#how-do-i-get-help" title="Link to this heading"></a></h3>
<p><strong>Support Channels:</strong></p>
<ol class="arabic simple">
<li><p><strong>Documentation</strong>: This comprehensive guide</p></li>
<li><p><strong>GitHub Issues</strong>: <a class="reference external" href="https://github.com/forkrul/blast-radius/issues">https://github.com/forkrul/blast-radius/issues</a></p></li>
<li><p><strong>Community Forum</strong>: <a class="reference external" href="https://community.blastradius.security">https://community.blastradius.security</a></p></li>
<li><p><strong>Enterprise Support</strong>: <a class="reference external" href="mailto:enterprise&#37;&#52;&#48;blastradius&#46;security">enterprise<span>&#64;</span>blastradius<span>&#46;</span>security</a></p></li>
</ol>
<p><strong>When Reporting Issues:</strong></p>
<p>Include:</p>
<ul class="simple">
<li><p>System information and configuration</p></li>
<li><p>Detailed error messages and logs</p></li>
<li><p>Steps to reproduce the issue</p></li>
<li><p>Expected vs. actual behavior</p></li>
</ul>
</section>
<section id="is-there-a-community-or-forum">
<h3>Is there a community or forum?<a class="headerlink" href="#is-there-a-community-or-forum" title="Link to this heading"></a></h3>
<p>Yes! Join our community:</p>
<ul class="simple">
<li><p><strong>GitHub Discussions</strong>: Feature requests and general discussion</p></li>
<li><p><strong>Community Forum</strong>: User discussions and knowledge sharing</p></li>
<li><p><strong>Discord Server</strong>: Real-time chat and support</p></li>
<li><p><strong>Stack Overflow</strong>: Technical questions (tag: blast-radius)</p></li>
</ul>
</section>
<section id="how-can-i-contribute">
<h3>How can I contribute?<a class="headerlink" href="#how-can-i-contribute" title="Link to this heading"></a></h3>
<p><strong>Ways to Contribute:</strong></p>
<ul class="simple">
<li><p><strong>Bug Reports</strong>: Report issues and bugs</p></li>
<li><p><strong>Feature Requests</strong>: Suggest new features and improvements</p></li>
<li><p><strong>Code Contributions</strong>: Submit pull requests</p></li>
<li><p><strong>Documentation</strong>: Improve documentation and guides</p></li>
<li><p><strong>Community Support</strong>: Help other users in forums</p></li>
</ul>
<p>See the <span class="xref std std-doc">../development/contributing</span> guide for detailed contribution guidelines.</p>
</section>
<section id="what-s-the-roadmap-for-future-features">
<h3>What’s the roadmap for future features?<a class="headerlink" href="#what-s-the-roadmap-for-future-features" title="Link to this heading"></a></h3>
<p><strong>Upcoming Features:</strong></p>
<ul class="simple">
<li><p><strong>Version 1.1</strong>: Enhanced threat intelligence and automated remediation</p></li>
<li><p><strong>Version 1.2</strong>: Advanced analytics and enterprise features</p></li>
<li><p><strong>Version 2.0</strong>: AI-powered security and extended integrations</p></li>
</ul>
<p>See the <span class="xref std std-doc">../releases/roadmap</span> for detailed feature plans and timelines.</p>
<p>For additional questions not covered here, please check the comprehensive documentation or reach out through our support channels.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../technical/product-requirements.html" class="btn btn-neutral float-left" title="Product Requirements Document (PRD)" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>