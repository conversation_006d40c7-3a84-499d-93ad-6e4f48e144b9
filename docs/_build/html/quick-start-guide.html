

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quick Start Guide &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f65ed55f" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="User Guides" href="user-guides/index.html" />
    <link rel="prev" title="Configuration Guide" href="configuration.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="technical/index.html">Technical Documentation</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Quick Start Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/quick-start-guide.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="quick-start-guide">
<h1>Quick Start Guide<a class="headerlink" href="#quick-start-guide" title="Link to this heading"></a></h1>
<p>Welcome to the Blast-Radius Security Tool! This guide will help you get up and running quickly, from initial setup to performing your first attack path analysis.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Blast-Radius Security Tool is designed to provide immediate value for security teams. This quick start guide covers:</p>
<ul class="simple">
<li><p><strong>5-minute setup</strong> using Docker</p></li>
<li><p><strong>Initial configuration</strong> and user setup</p></li>
<li><p><strong>First attack path analysis</strong></p></li>
<li><p><strong>Basic dashboard navigation</strong></p></li>
<li><p><strong>Essential security features</strong></p></li>
</ul>
</section>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<p>Before starting, ensure you have:</p>
<ul class="simple">
<li><p><strong>Docker and Docker Compose</strong> installed</p></li>
<li><p><strong>8GB+ RAM</strong> available</p></li>
<li><p><strong>Internet connection</strong> for downloading images</p></li>
<li><p><strong>Modern web browser</strong> (Chrome, Firefox, Safari, Edge)</p></li>
</ul>
</section>
<section id="quick-installation">
<h2>Quick Installation<a class="headerlink" href="#quick-installation" title="Link to this heading"></a></h2>
<section id="step-1-get-the-code">
<h3>Step 1: Get the Code<a class="headerlink" href="#step-1-get-the-code" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/blast-radius.git
<span class="nb">cd</span><span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
<section id="step-2-start-the-platform">
<h3>Step 2: Start the Platform<a class="headerlink" href="#step-2-start-the-platform" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start all services (this may take 2-3 minutes on first run)</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Check that all services are running</span>
docker-compose<span class="w"> </span>ps
</pre></div>
</div>
<p>You should see all services in “Up” status:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>NAME                    STATUS
blast-radius-backend    Up
blast-radius-frontend   Up
blast-radius-postgres   Up
blast-radius-redis      Up
blast-radius-neo4j      Up
</pre></div>
</div>
</section>
<section id="step-3-access-the-platform">
<h3>Step 3: Access the Platform<a class="headerlink" href="#step-3-access-the-platform" title="Link to this heading"></a></h3>
<p>Open your web browser and navigate to:</p>
<ul class="simple">
<li><p><strong>Main Dashboard</strong>: <a class="reference external" href="http://localhost:3000">http://localhost:3000</a></p></li>
<li><p><strong>API Documentation</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
</ul>
<p><strong>Default Login Credentials:</strong></p>
<ul class="simple">
<li><p><strong>Email</strong>: <a class="reference external" href="mailto:admin&#37;&#52;&#48;blastradius&#46;local">admin<span>&#64;</span>blastradius<span>&#46;</span>local</a></p></li>
<li><p><strong>Password</strong>: BlastRadius2024!</p></li>
</ul>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><strong>Security Notice</strong>: Change the default password immediately after first login!</p>
</div>
</section>
</section>
<section id="first-login-and-setup">
<h2>First Login and Setup<a class="headerlink" href="#first-login-and-setup" title="Link to this heading"></a></h2>
<section id="initial-login">
<h3>Initial Login<a class="headerlink" href="#initial-login" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Navigate to <a class="reference external" href="http://localhost:3000">http://localhost:3000</a></p></li>
<li><p>Enter the default credentials</p></li>
<li><p>Click “Sign In”</p></li>
<li><p>You’ll be prompted to change your password</p></li>
</ol>
<a class="reference internal image-reference" href="_static/screenshots/login-screen.png"><img alt="Login Screen" class="align-center" src="_static/screenshots/login-screen.png" style="width: 600px;" />
</a>
</section>
<section id="password-change">
<h3>Password Change<a class="headerlink" href="#password-change" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Enter a strong new password (minimum 12 characters)</p></li>
<li><p>Confirm the password</p></li>
<li><p>Click “Update Password”</p></li>
<li><p>You’ll be redirected to the main dashboard</p></li>
</ol>
</section>
<section id="dashboard-overview">
<h3>Dashboard Overview<a class="headerlink" href="#dashboard-overview" title="Link to this heading"></a></h3>
<p>After login, you’ll see the main dashboard with:</p>
<ul class="simple">
<li><p><strong>Navigation Menu</strong> - Access to all platform features</p></li>
<li><p><strong>Quick Stats</strong> - Overview of your security posture</p></li>
<li><p><strong>Recent Activity</strong> - Latest security events and alerts</p></li>
<li><p><strong>Action Items</strong> - Tasks requiring attention</p></li>
</ul>
<a class="reference internal image-reference" href="_static/screenshots/main-dashboard.png"><img alt="Main Dashboard" class="align-center" src="_static/screenshots/main-dashboard.png" style="width: 800px;" />
</a>
</section>
</section>
<section id="basic-configuration">
<h2>Basic Configuration<a class="headerlink" href="#basic-configuration" title="Link to this heading"></a></h2>
<section id="user-profile-setup">
<h3>User Profile Setup<a class="headerlink" href="#user-profile-setup" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Click your profile icon in the top-right corner</p></li>
<li><p>Select “Profile Settings”</p></li>
<li><p>Update your information:</p>
<ul class="simple">
<li><p><strong>Full Name</strong></p></li>
<li><p><strong>Email Address</strong></p></li>
<li><p><strong>Time Zone</strong></p></li>
<li><p><strong>Notification Preferences</strong></p></li>
</ul>
</li>
<li><p>Click “Save Changes”</p></li>
</ol>
</section>
<section id="multi-factor-authentication-recommended">
<h3>Multi-Factor Authentication (Recommended)<a class="headerlink" href="#multi-factor-authentication-recommended" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Go to “Profile Settings” → “Security”</p></li>
<li><p>Click “Enable MFA”</p></li>
<li><p>Scan the QR code with your authenticator app</p></li>
<li><p>Enter the verification code</p></li>
<li><p>Save your backup codes in a secure location</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>We recommend using Google Authenticator, Authy, or Microsoft Authenticator.</p>
</div>
</section>
</section>
<section id="your-first-attack-path-analysis">
<h2>Your First Attack Path Analysis<a class="headerlink" href="#your-first-attack-path-analysis" title="Link to this heading"></a></h2>
<section id="step-1-add-sample-assets">
<h3>Step 1: Add Sample Assets<a class="headerlink" href="#step-1-add-sample-assets" title="Link to this heading"></a></h3>
<p>For this quick start, we’ll use sample data:</p>
<ol class="arabic simple">
<li><p>Navigate to “Assets” → “Discovery”</p></li>
<li><p>Click “Load Sample Data”</p></li>
<li><p>Select “Small Enterprise Environment”</p></li>
<li><p>Click “Import”</p></li>
</ol>
<p>This creates a sample environment with:</p>
<ul class="simple">
<li><p><strong>50 servers</strong> (web, database, domain controllers)</p></li>
<li><p><strong>200 user accounts</strong> (including privileged accounts)</p></li>
<li><p><strong>Network segments</strong> (DMZ, internal, management)</p></li>
<li><p><strong>Security controls</strong> (firewalls, antivirus, monitoring)</p></li>
</ul>
</section>
<section id="step-2-run-attack-path-analysis">
<h3>Step 2: Run Attack Path Analysis<a class="headerlink" href="#step-2-run-attack-path-analysis" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Navigate to “Analysis” → “Attack Paths”</p></li>
<li><p>Click “New Analysis”</p></li>
<li><p>Configure the analysis:</p>
<ul class="simple">
<li><p><strong>Source</strong>: Select “External Attacker”</p></li>
<li><p><strong>Target</strong>: Select “Domain Controller”</p></li>
<li><p><strong>Max Hops</strong>: 5</p></li>
<li><p><strong>Analysis Type</strong>: “Comprehensive”</p></li>
</ul>
</li>
<li><p>Click “Start Analysis”</p></li>
</ol>
<p>The analysis will complete in 10-30 seconds.</p>
</section>
<section id="step-3-view-results">
<h3>Step 3: View Results<a class="headerlink" href="#step-3-view-results" title="Link to this heading"></a></h3>
<p>The results page shows:</p>
<ul class="simple">
<li><p><strong>Attack Path Visualization</strong> - Interactive graph of potential paths</p></li>
<li><p><strong>Risk Assessment</strong> - Severity and likelihood scores</p></li>
<li><p><strong>Recommended Actions</strong> - Prioritized remediation steps</p></li>
<li><p><strong>Detailed Report</strong> - Technical analysis and evidence</p></li>
</ul>
<a class="reference internal image-reference" href="_static/screenshots/attack-path-results.png"><img alt="Attack Path Analysis Results" class="align-center" src="_static/screenshots/attack-path-results.png" style="width: 800px;" />
</a>
</section>
<section id="understanding-the-visualization">
<h3>Understanding the Visualization<a class="headerlink" href="#understanding-the-visualization" title="Link to this heading"></a></h3>
<p>The attack path graph uses:</p>
<ul class="simple">
<li><p><strong>Green Nodes</strong> - Secure assets with good controls</p></li>
<li><p><strong>Yellow Nodes</strong> - Assets with moderate risk</p></li>
<li><p><strong>Red Nodes</strong> - High-risk assets requiring attention</p></li>
<li><p><strong>Arrows</strong> - Potential attack vectors between assets</p></li>
<li><p><strong>Thick Lines</strong> - High-probability attack paths</p></li>
</ul>
<p>Interactive Features:</p>
<ul class="simple">
<li><p><strong>Zoom and Pan</strong> - Navigate large graphs</p></li>
<li><p><strong>Click Nodes</strong> - View detailed asset information</p></li>
<li><p><strong>Filter Paths</strong> - Focus on specific risk levels</p></li>
<li><p><strong>Export</strong> - Save results as PDF or image</p></li>
</ul>
</section>
</section>
<section id="essential-features-tour">
<h2>Essential Features Tour<a class="headerlink" href="#essential-features-tour" title="Link to this heading"></a></h2>
<section id="real-time-monitoring">
<h3>Real-Time Monitoring<a class="headerlink" href="#real-time-monitoring" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Navigate to “Monitoring” → “Live Dashboard”</p></li>
<li><p>View real-time security events and alerts</p></li>
<li><p>Configure alert thresholds and notifications</p></li>
</ol>
<p>Key monitoring features:</p>
<ul class="simple">
<li><p><strong>Security Event Stream</strong> - Live feed of security events</p></li>
<li><p><strong>Attack Path Changes</strong> - Real-time updates to risk posture</p></li>
<li><p><strong>System Health</strong> - Platform and integration status</p></li>
<li><p><strong>Performance Metrics</strong> - Response times and throughput</p></li>
</ul>
</section>
<section id="asset-management">
<h3>Asset Management<a class="headerlink" href="#asset-management" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Navigate to “Assets” → “Inventory”</p></li>
<li><p>Browse your asset inventory</p></li>
<li><p>View asset details and relationships</p></li>
</ol>
<p>Asset management features:</p>
<ul class="simple">
<li><p><strong>Automated Discovery</strong> - Cloud and on-premises assets</p></li>
<li><p><strong>Relationship Mapping</strong> - Dependencies and connections</p></li>
<li><p><strong>Configuration Tracking</strong> - Changes and drift detection</p></li>
<li><p><strong>Risk Scoring</strong> - Individual asset risk assessment</p></li>
</ul>
</section>
<section id="user-and-role-management">
<h3>User and Role Management<a class="headerlink" href="#user-and-role-management" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Navigate to “Administration” → “Users”</p></li>
<li><p>Create additional user accounts</p></li>
<li><p>Assign appropriate roles</p></li>
</ol>
<p>Available roles:</p>
<ul class="simple">
<li><p><strong>SOC Operator</strong> - Monitoring and incident response</p></li>
<li><p><strong>Security Architect</strong> - Risk assessment and design</p></li>
<li><p><strong>Red Team Member</strong> - Attack simulation and testing</p></li>
<li><p><strong>Purple Team Member</strong> - Collaborative security testing</p></li>
<li><p><strong>Administrator</strong> - Full platform access</p></li>
</ul>
</section>
<section id="threat-intelligence">
<h3>Threat Intelligence<a class="headerlink" href="#threat-intelligence" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Navigate to “Intelligence” → “Threat Feeds”</p></li>
<li><p>Configure threat intelligence sources</p></li>
<li><p>View IOC correlations and threat actor information</p></li>
</ol>
</section>
</section>
<section id="integration-setup">
<h2>Integration Setup<a class="headerlink" href="#integration-setup" title="Link to this heading"></a></h2>
<section id="cloud-provider-integration">
<h3>Cloud Provider Integration<a class="headerlink" href="#cloud-provider-integration" title="Link to this heading"></a></h3>
<p>To connect your cloud environments:</p>
<ol class="arabic simple">
<li><p>Navigate to “Integrations” → “Cloud Providers”</p></li>
<li><p>Select your cloud provider (AWS, Azure, GCP)</p></li>
<li><p>Follow the setup wizard to configure API access</p></li>
<li><p>Test the connection and start asset discovery</p></li>
</ol>
</section>
<section id="servicenow-integration">
<h3>ServiceNow Integration<a class="headerlink" href="#servicenow-integration" title="Link to this heading"></a></h3>
<p>To integrate with ServiceNow CMDB:</p>
<ol class="arabic simple">
<li><p>Navigate to “Integrations” → “ServiceNow”</p></li>
<li><p>Enter your ServiceNow instance details</p></li>
<li><p>Configure field mappings</p></li>
<li><p>Enable bi-directional synchronization</p></li>
</ol>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>Now that you have the basics working, consider these next steps:</p>
<section id="immediate-actions">
<h3>Immediate Actions<a class="headerlink" href="#immediate-actions" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Change default passwords</strong> for all accounts</p></li>
<li><p><strong>Enable MFA</strong> for all users</p></li>
<li><p><strong>Configure real integrations</strong> with your cloud providers</p></li>
<li><p><strong>Set up proper SSL certificates</strong> for production use</p></li>
<li><p><strong>Configure backup procedures</strong> for your data</p></li>
</ol>
</section>
<section id="learning-resources">
<h3>Learning Resources<a class="headerlink" href="#learning-resources" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>User Guides</strong> - Role-specific documentation:</p>
<ul>
<li><p><a class="reference internal" href="user-guides/soc-operators.html"><span class="doc">SOC Operators Guide</span></a></p></li>
<li><p><a class="reference internal" href="user-guides/security-architects.html"><span class="doc">Security Architects Guide</span></a></p></li>
<li><p><a class="reference internal" href="user-guides/red-team-members.html"><span class="doc">Red Team Members Guide</span></a></p></li>
<li><p><a class="reference internal" href="user-guides/purple-team-members.html"><span class="doc">Purple Team Members Guide</span></a></p></li>
</ul>
</li>
<li><p><strong>Technical Documentation</strong>:</p>
<ul>
<li><p><span class="xref std std-doc">technical/architecture</span></p></li>
<li><p><a class="reference internal" href="api/authentication.html"><span class="doc">Authentication API</span></a></p></li>
<li><p><span class="xref std std-doc">security/best-practices</span></p></li>
</ul>
</li>
<li><p><strong>Use Case Guides</strong>:</p>
<ul>
<li><p><span class="xref std std-doc">use-cases/attack-path-analysis</span></p></li>
<li><p><span class="xref std std-doc">use-cases/threat-intelligence</span></p></li>
<li><p><span class="xref std std-doc">use-cases/multi-cloud-integration</span></p></li>
</ul>
</li>
</ul>
</section>
<section id="advanced-configuration">
<h3>Advanced Configuration<a class="headerlink" href="#advanced-configuration" title="Link to this heading"></a></h3>
<p>For production deployments:</p>
<ol class="arabic simple">
<li><p><strong>Review</strong> <span class="xref std std-doc">security/best-practices</span></p></li>
<li><p><strong>Configure</strong> <span class="xref std std-doc">technical/monitoring</span> and alerting</p></li>
<li><p><strong>Set up</strong> <span class="xref std std-doc">technical/deployment</span> for high availability</p></li>
<li><p><strong>Implement</strong> proper backup and disaster recovery</p></li>
</ol>
</section>
</section>
<section id="common-quick-start-issues">
<h2>Common Quick Start Issues<a class="headerlink" href="#common-quick-start-issues" title="Link to this heading"></a></h2>
<section id="platform-won-t-start">
<h3>Platform Won’t Start<a class="headerlink" href="#platform-won-t-start" title="Link to this heading"></a></h3>
<p><strong>Symptoms</strong>: Docker containers fail to start or exit immediately</p>
<p><strong>Solutions</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check system resources</span>
docker<span class="w"> </span>system<span class="w"> </span>df
docker<span class="w"> </span>system<span class="w"> </span>prune<span class="w">  </span><span class="c1"># If low on space</span>

<span class="c1"># Check logs</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>backend
docker-compose<span class="w"> </span>logs<span class="w"> </span>frontend

<span class="c1"># Restart services</span>
docker-compose<span class="w"> </span>down
docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</section>
<section id="can-t-access-web-interface">
<h3>Can’t Access Web Interface<a class="headerlink" href="#can-t-access-web-interface" title="Link to this heading"></a></h3>
<p><strong>Symptoms</strong>: Browser shows “connection refused” or timeout</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check if services are running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>ps
</pre></div>
</div>
</li>
<li><p><strong>Verify port availability</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>netstat<span class="w"> </span>-tulpn<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>:3000
netstat<span class="w"> </span>-tulpn<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>:8000
</pre></div>
</div>
</li>
<li><p><strong>Check firewall settings</strong> (if applicable)</p></li>
</ol>
</section>
<section id="login-issues">
<h3>Login Issues<a class="headerlink" href="#login-issues" title="Link to this heading"></a></h3>
<p><strong>Symptoms</strong>: Invalid credentials or login failures</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Verify default credentials</strong> are correct</p></li>
<li><p><strong>Reset admin password</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>backend<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>app.cli<span class="w"> </span>reset-admin-password
</pre></div>
</div>
</li>
<li><p><strong>Check application logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>logs<span class="w"> </span>backend<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>-i<span class="w"> </span>auth
</pre></div>
</div>
</li>
</ol>
</section>
<section id="performance-issues">
<h3>Performance Issues<a class="headerlink" href="#performance-issues" title="Link to this heading"></a></h3>
<p><strong>Symptoms</strong>: Slow response times or timeouts</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Increase system resources</strong> (RAM, CPU)</p></li>
<li><p><strong>Check database performance</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>postgres<span class="w"> </span>pg_stat_activity
</pre></div>
</div>
</li>
<li><p><strong>Monitor resource usage</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>stats
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="getting-help">
<h2>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<p>If you encounter issues not covered here:</p>
<ol class="arabic simple">
<li><p><strong>Check the logs</strong> for error messages</p></li>
<li><p><strong>Review</strong> <span class="xref std std-doc">troubleshooting/common-issues</span></p></li>
<li><p><strong>Search existing issues</strong> on GitHub</p></li>
<li><p><strong>Create a new issue</strong> with detailed information</p></li>
</ol>
<p><strong>Support Channels</strong>:</p>
<ul class="simple">
<li><p><strong>GitHub Issues</strong>: <a class="reference external" href="https://github.com/forkrul/blast-radius/issues">https://github.com/forkrul/blast-radius/issues</a></p></li>
<li><p><strong>Documentation</strong>: This comprehensive guide</p></li>
<li><p><strong>Community Forum</strong>: <a class="reference external" href="https://community.blastradius.security">https://community.blastradius.security</a></p></li>
</ul>
<p><strong>Enterprise Support</strong>:</p>
<p>For enterprise customers, contact <a class="reference external" href="mailto:enterprise&#37;&#52;&#48;blastradius&#46;security">enterprise<span>&#64;</span>blastradius<span>&#46;</span>security</a> for priority support and professional services.</p>
</section>
<section id="congratulations">
<h2>Congratulations!<a class="headerlink" href="#congratulations" title="Link to this heading"></a></h2>
<p>You’ve successfully set up the Blast-Radius Security Tool and performed your first attack path analysis. You’re now ready to explore the platform’s advanced features and integrate it with your existing security infrastructure.</p>
<p>The platform provides powerful capabilities for understanding and mitigating security risks across your environment. Take time to explore the different user guides and configure the platform to meet your specific security requirements.</p>
<div class="admonition tip">
<p class="admonition-title">Tip</p>
<p><strong>Pro Tip</strong>: Start with small, focused analyses and gradually expand to larger environments as you become more familiar with the platform’s capabilities.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="configuration.html" class="btn btn-neutral float-left" title="Configuration Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="user-guides/index.html" class="btn btn-neutral float-right" title="User Guides" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>