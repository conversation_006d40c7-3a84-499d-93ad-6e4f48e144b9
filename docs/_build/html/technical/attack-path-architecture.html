

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Attack Path Analysis Architecture &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script type="module" src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.esm.min.mjs"></script>
      <script type="module" src="https://cdn.jsdelivr.net/npm/@mermaid-js/layout-elk@0.1.4/dist/mermaid-layout-elk.esm.min.mjs"></script>
      <script type="module">
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#2980B9',
        primaryTextColor: '#ffffff',
        primaryBorderColor: '#1f5f99',
        lineColor: '#34495e',
        sectionBkgColor: '#ecf0f1',
        altSectionBkgColor: '#bdc3c7',
        gridColor: '#95a5a6',
        secondaryColor: '#3498db',
        tertiaryColor: '#e74c3c'
    },
    flowchart: {
        nodeSpacing: 50,
        rankSpacing: 50,
        curve: 'basis'
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    },
    gantt: {
        titleTopMargin: 25,
        barHeight: 20,
        fontFamily: '"Open Sans", sans-serif',
        fontSize: 11,
        gridLineStartPadding: 35,
        bottomPadding: 25,
        leftPadding: 75,
        rightPadding: 50
    }
});
</script>
      <script src="https://cdn.jsdelivr.net/npm/d3@7.9.0/dist/d3.min.js"></script>
      <script type="module">
import mermaid from "https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.esm.min.mjs";
window.addEventListener("load", () => mermaid.run());
</script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Database Design and Schema" href="database-design.html" />
    <link rel="prev" title="Technical Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Technical Documentation</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#architecture-and-design">Architecture and Design</a><ul class="current">
<li class="toctree-l3 current"><a class="reference internal" href="index.html#system-architecture">System Architecture</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Attack Path Analysis Architecture</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#database-design">Database Design</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#graph-analysis-engine">Graph Analysis Engine</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#product-requirements-document">Product Requirements Document</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#performance-and-operations">Performance and Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#development-and-integration">Development and Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#data-models-and-schemas">Data Models and Schemas</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-quality-assurance">Testing and Quality Assurance</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#contributing-and-development">Contributing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Technical Documentation</a></li>
      <li class="breadcrumb-item active">Attack Path Analysis Architecture</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/technical/attack-path-architecture.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="attack-path-analysis-architecture">
<h1>Attack Path Analysis Architecture<a class="headerlink" href="#attack-path-analysis-architecture" title="Link to this heading"></a></h1>
<p>This document provides a comprehensive technical overview of the attack path analysis engine architecture, including graph processing, algorithms, and performance optimizations.</p>
<section id="system-overview">
<h2>System Overview<a class="headerlink" href="#system-overview" title="Link to this heading"></a></h2>
<p>The attack path analysis engine is built on a multi-layered architecture designed for high performance, scalability, and extensibility:</p>
<pre  class="mermaid">
        graph TB
    subgraph &quot;Client Layer&quot;
        WebUI[Web Interface]
        MobileApp[Mobile App]
        CLI[Command Line Interface]
        SDKs[Python/JS/Go SDKs]
    end

    subgraph &quot;API Gateway&quot;
        Gateway[API Gateway]
        Auth[Authentication]
        RateLimit[Rate Limiting]
        LoadBalancer[Load Balancer]
    end

    subgraph &quot;Application Layer&quot;
        subgraph &quot;Core Services&quot;
            AttackPathAPI[Attack Path API]
            AssetAPI[Asset Management API]
            UserAPI[User Management API]
            DiscoveryAPI[Discovery API]
        end

        subgraph &quot;Analysis Services&quot;
            GraphEngine[Graph Engine]
            AttackAnalyzer[Attack Path Analyzer]
            BlastRadius[Blast Radius Calculator]
            MitreMapper[MITRE ATT&amp;CK Mapper]
        end

        subgraph &quot;Background Services&quot;
            TaskQueue[Task Queue]
            Scheduler[Job Scheduler]
            CacheManager[Cache Manager]
            MetricsCollector[Metrics Collector]
        end
    end

    subgraph &quot;Data Layer&quot;
        subgraph &quot;Primary Storage&quot;
            PostgreSQL[(PostgreSQL Database)]
            Redis[(Redis Cache)]
        end

        subgraph &quot;External Integrations&quot;
            CloudAPIs[Cloud Provider APIs]
            SIEM[SIEM Systems]
            ThreatIntel[Threat Intelligence]
            CMDB[Configuration Management DB]
        end
    end

    subgraph &quot;Infrastructure&quot;
        Monitoring[Monitoring &amp; Alerting]
        Logging[Centralized Logging]
        Backup[Backup &amp; Recovery]
        Security[Security Controls]
    end

    WebUI --&gt; Gateway
    MobileApp --&gt; Gateway
    CLI --&gt; Gateway
    SDKs --&gt; Gateway

    Gateway --&gt; Auth
    Gateway --&gt; RateLimit
    Gateway --&gt; LoadBalancer

    LoadBalancer --&gt; AttackPathAPI
    LoadBalancer --&gt; AssetAPI
    LoadBalancer --&gt; UserAPI
    LoadBalancer --&gt; DiscoveryAPI

    AttackPathAPI --&gt; GraphEngine
    AttackPathAPI --&gt; AttackAnalyzer
    AttackPathAPI --&gt; BlastRadius
    AttackPathAPI --&gt; MitreMapper

    GraphEngine --&gt; PostgreSQL
    GraphEngine --&gt; Redis
    AttackAnalyzer --&gt; TaskQueue
    BlastRadius --&gt; CacheManager

    TaskQueue --&gt; Scheduler
    CacheManager --&gt; Redis
    MetricsCollector --&gt; Monitoring

    AssetAPI --&gt; CloudAPIs
    DiscoveryAPI --&gt; SIEM
    AttackAnalyzer --&gt; ThreatIntel
    AssetAPI --&gt; CMDB

    PostgreSQL --&gt; Backup
    Redis --&gt; Backup
    </pre></section>
<section id="core-components">
<h2>Core Components<a class="headerlink" href="#core-components" title="Link to this heading"></a></h2>
<section id="graphengine">
<h3>GraphEngine<a class="headerlink" href="#graphengine" title="Link to this heading"></a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">GraphEngine</span></code> is the core component responsible for graph processing and attack path discovery.</p>
<p><strong>Key Features:</strong></p>
<ul class="simple">
<li><p><strong>NetworkX Integration</strong>: Leverages NetworkX for high-performance graph algorithms</p></li>
<li><p><strong>Weighted Relationships</strong>: Models security controls as edge weights</p></li>
<li><p><strong>Intelligent Caching</strong>: LRU cache for path and blast radius results</p></li>
<li><p><strong>Parallel Processing</strong>: Multi-threaded analysis for improved performance</p></li>
<li><p><strong>Memory Optimization</strong>: Efficient graph storage and operations</p></li>
</ul>
<p><strong>Class Structure:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">GraphEngine</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">max_workers</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">4</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="n">nx</span><span class="o">.</span><span class="n">DiGraph</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">asset_metadata</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">relationship_weights</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">executor</span> <span class="o">=</span> <span class="n">ThreadPoolExecutor</span><span class="p">(</span><span class="n">max_workers</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">path_cache</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">blast_radius_cache</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">find_attack_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">target</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">AttackPath</span><span class="p">]</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">calculate_blast_radius</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BlastRadiusResult</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">add_asset</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">asset_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">asset_data</span><span class="p">:</span> <span class="n">Dict</span><span class="p">)</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">add_relationship</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">target</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">rel_data</span><span class="p">:</span> <span class="n">Dict</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="attackpathanalyzer">
<h3>AttackPathAnalyzer<a class="headerlink" href="#attackpathanalyzer" title="Link to this heading"></a></h3>
<p>The <code class="docutils literal notranslate"><span class="pre">AttackPathAnalyzer</span></code> provides high-level analysis capabilities with MITRE ATT&amp;CK integration.</p>
<p><strong>Key Features:</strong></p>
<ul class="simple">
<li><p><strong>MITRE ATT&amp;CK Mapping</strong>: Complete framework integration with 12 tactics</p></li>
<li><p><strong>Scenario Modeling</strong>: Complex attack scenario creation and analysis</p></li>
<li><p><strong>Risk Assessment</strong>: Multi-factor risk scoring methodology</p></li>
<li><p><strong>Database Integration</strong>: Seamless integration with asset database</p></li>
<li><p><strong>Export Capabilities</strong>: Multiple format support for analysis results</p></li>
</ul>
<p><strong>Class Structure:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">AttackPathAnalyzer</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">db</span><span class="p">:</span> <span class="n">Session</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">db</span> <span class="o">=</span> <span class="n">db</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">graph_engine</span> <span class="o">=</span> <span class="n">GraphEngine</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mitre_mappings</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_load_mitre_mappings</span><span class="p">()</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">analyze_attack_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">targets</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">AttackPath</span><span class="p">]</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_attack_scenario</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">actor</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">entries</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">objectives</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">AttackScenario</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_mitre_attack_mapping</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="n">AttackPath</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span>
</pre></div>
</div>
</section>
</section>
<section id="graph-algorithms">
<h2>Graph Algorithms<a class="headerlink" href="#graph-algorithms" title="Link to this heading"></a></h2>
<section id="path-discovery-algorithm">
<h3>Path Discovery Algorithm<a class="headerlink" href="#path-discovery-algorithm" title="Link to this heading"></a></h3>
<p>The engine uses a modified breadth-first search with weighted edges for attack path discovery:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">find_attack_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">target</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">max_length</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Modified BFS with weighted edges and security control consideration.</span>

<span class="sd">    Algorithm:</span>
<span class="sd">    1. Initialize queue with source node</span>
<span class="sd">    2. For each node, explore neighbors based on edge weights</span>
<span class="sd">    3. Apply security control penalties to path likelihood</span>
<span class="sd">    4. Prune paths exceeding max_length or low probability</span>
<span class="sd">    5. Return sorted paths by criticality score</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">paths</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="n">queue</span> <span class="o">=</span> <span class="p">[(</span><span class="n">source</span><span class="p">,</span> <span class="p">[</span><span class="n">source</span><span class="p">],</span> <span class="mf">1.0</span><span class="p">)]</span>  <span class="c1"># (node, path, likelihood)</span>

    <span class="k">while</span> <span class="n">queue</span><span class="p">:</span>
        <span class="n">current</span><span class="p">,</span> <span class="n">path</span><span class="p">,</span> <span class="n">likelihood</span> <span class="o">=</span> <span class="n">queue</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">current</span> <span class="o">==</span> <span class="n">target</span><span class="p">:</span>
            <span class="n">paths</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">path</span><span class="p">,</span> <span class="n">likelihood</span><span class="p">))</span>
            <span class="k">continue</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">path</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="n">max_length</span><span class="p">:</span>
            <span class="k">continue</span>

        <span class="k">for</span> <span class="n">neighbor</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="o">.</span><span class="n">successors</span><span class="p">(</span><span class="n">current</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">neighbor</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">path</span><span class="p">:</span>  <span class="c1"># Avoid cycles</span>
                <span class="n">edge_data</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="o">.</span><span class="n">get_edge_data</span><span class="p">(</span><span class="n">current</span><span class="p">,</span> <span class="n">neighbor</span><span class="p">)</span>
                <span class="n">edge_likelihood</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_calculate_edge_likelihood</span><span class="p">(</span><span class="n">edge_data</span><span class="p">)</span>
                <span class="n">new_likelihood</span> <span class="o">=</span> <span class="n">likelihood</span> <span class="o">*</span> <span class="n">edge_likelihood</span>

                <span class="k">if</span> <span class="n">new_likelihood</span> <span class="o">&gt;</span> <span class="mf">0.01</span><span class="p">:</span>  <span class="c1"># Prune low-probability paths</span>
                    <span class="n">queue</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">neighbor</span><span class="p">,</span> <span class="n">path</span> <span class="o">+</span> <span class="p">[</span><span class="n">neighbor</span><span class="p">],</span> <span class="n">new_likelihood</span><span class="p">))</span>

    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_rank_paths</span><span class="p">(</span><span class="n">paths</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="blast-radius-algorithm">
<h3>Blast Radius Algorithm<a class="headerlink" href="#blast-radius-algorithm" title="Link to this heading"></a></h3>
<p>Blast radius calculation uses degree-based propagation analysis:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">calculate_blast_radius</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">max_degrees</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">BlastRadiusResult</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Degree-based blast radius calculation with impact assessment.</span>

<span class="sd">    Algorithm:</span>
<span class="sd">    1. Start with compromised source asset</span>
<span class="sd">    2. For each degree (0 to max_degrees):</span>
<span class="sd">       - Find all assets at current degree</span>
<span class="sd">       - Calculate impact score for each asset</span>
<span class="sd">       - Determine propagation to next degree</span>
<span class="sd">    3. Aggregate impact by degree and asset type</span>
<span class="sd">    4. Calculate financial and compliance impact</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">affected_assets</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
    <span class="n">impact_by_degree</span> <span class="o">=</span> <span class="p">{}</span>
    <span class="n">current_level</span> <span class="o">=</span> <span class="p">{</span><span class="n">source</span><span class="p">}</span>
    <span class="n">visited</span> <span class="o">=</span> <span class="p">{</span><span class="n">source</span><span class="p">}</span>

    <span class="k">for</span> <span class="n">degree</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">max_degrees</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">current_level</span><span class="p">:</span>
            <span class="k">break</span>

        <span class="n">impact_by_degree</span><span class="p">[</span><span class="n">degree</span><span class="p">]</span> <span class="o">=</span> <span class="n">current_level</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
        <span class="n">affected_assets</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">current_level</span><span class="p">)</span>

        <span class="c1"># Calculate next level</span>
        <span class="n">next_level</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
        <span class="k">for</span> <span class="n">asset</span> <span class="ow">in</span> <span class="n">current_level</span><span class="p">:</span>
            <span class="n">neighbors</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="o">.</span><span class="n">successors</span><span class="p">(</span><span class="n">asset</span><span class="p">))</span> <span class="o">|</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="o">.</span><span class="n">predecessors</span><span class="p">(</span><span class="n">asset</span><span class="p">))</span>
            <span class="k">for</span> <span class="n">neighbor</span> <span class="ow">in</span> <span class="n">neighbors</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">neighbor</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">visited</span><span class="p">:</span>
                    <span class="n">propagation_probability</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_calculate_propagation_probability</span><span class="p">(</span><span class="n">asset</span><span class="p">,</span> <span class="n">neighbor</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">propagation_probability</span> <span class="o">&gt;</span> <span class="mf">0.3</span><span class="p">:</span>  <span class="c1"># Threshold for propagation</span>
                        <span class="n">next_level</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">neighbor</span><span class="p">)</span>
                        <span class="n">visited</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">neighbor</span><span class="p">)</span>

        <span class="n">current_level</span> <span class="o">=</span> <span class="n">next_level</span>

    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_calculate_impact_metrics</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">affected_assets</span><span class="p">,</span> <span class="n">impact_by_degree</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="risk-scoring-methodology">
<h2>Risk Scoring Methodology<a class="headerlink" href="#risk-scoring-methodology" title="Link to this heading"></a></h2>
<section id="multi-factor-risk-assessment">
<h3>Multi-Factor Risk Assessment<a class="headerlink" href="#multi-factor-risk-assessment" title="Link to this heading"></a></h3>
<p>The risk scoring system uses multiple weighted factors:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">calculate_path_risk_score</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">path_nodes</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span> <span class="n">path_edges</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]])</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Multi-factor risk assessment for attack paths.</span>

<span class="sd">    Factors:</span>
<span class="sd">    - Asset Risk Scores (40%): Individual asset risk levels</span>
<span class="sd">    - Path Complexity (20%): Length and difficulty</span>
<span class="sd">    - Security Controls (30%): Effectiveness of controls</span>
<span class="sd">    - Business Impact (10%): Criticality of target assets</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Asset risk component (40%)</span>
    <span class="n">asset_risks</span> <span class="o">=</span> <span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">asset_metadata</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">node</span><span class="p">,</span> <span class="p">{})</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;risk_score&quot;</span><span class="p">,</span> <span class="mi">50</span><span class="p">)</span> <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">path_nodes</span><span class="p">]</span>
    <span class="n">weights</span> <span class="o">=</span> <span class="p">[</span><span class="n">i</span> <span class="o">+</span> <span class="mi">1</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">asset_risks</span><span class="p">))]</span>  <span class="c1"># Later assets weighted higher</span>
    <span class="n">weighted_asset_risk</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">risk</span> <span class="o">*</span> <span class="n">weight</span> <span class="k">for</span> <span class="n">risk</span><span class="p">,</span> <span class="n">weight</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">asset_risks</span><span class="p">,</span> <span class="n">weights</span><span class="p">))</span> <span class="o">/</span> <span class="nb">sum</span><span class="p">(</span><span class="n">weights</span><span class="p">)</span>

    <span class="c1"># Path complexity component (20%)</span>
    <span class="n">complexity_factor</span> <span class="o">=</span> <span class="mf">1.0</span> <span class="o">+</span> <span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">path_nodes</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="mf">0.1</span>

    <span class="c1"># Security controls component (30%)</span>
    <span class="n">security_factor</span> <span class="o">=</span> <span class="mf">1.0</span>
    <span class="k">for</span> <span class="n">source</span><span class="p">,</span> <span class="n">target</span> <span class="ow">in</span> <span class="n">path_edges</span><span class="p">:</span>
        <span class="n">edge_data</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="o">.</span><span class="n">get_edge_data</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">edge_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;encrypted&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
            <span class="n">security_factor</span> <span class="o">*=</span> <span class="mf">0.9</span>
        <span class="k">if</span> <span class="n">edge_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;authenticated&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
            <span class="n">security_factor</span> <span class="o">*=</span> <span class="mf">0.9</span>
        <span class="k">if</span> <span class="n">edge_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;monitored&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
            <span class="n">security_factor</span> <span class="o">*=</span> <span class="mf">0.8</span>

    <span class="c1"># Business impact component (10%)</span>
    <span class="n">target_asset</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">asset_metadata</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">path_nodes</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">],</span> <span class="p">{})</span>
    <span class="n">business_multiplier</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;low&quot;</span><span class="p">:</span> <span class="mf">0.5</span><span class="p">,</span> <span class="s2">&quot;medium&quot;</span><span class="p">:</span> <span class="mf">1.0</span><span class="p">,</span> <span class="s2">&quot;high&quot;</span><span class="p">:</span> <span class="mf">1.5</span><span class="p">,</span> <span class="s2">&quot;critical&quot;</span><span class="p">:</span> <span class="mf">2.0</span>
    <span class="p">}</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">target_asset</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;business_criticality&quot;</span><span class="p">,</span> <span class="s2">&quot;medium&quot;</span><span class="p">),</span> <span class="mf">1.0</span><span class="p">)</span>

    <span class="c1"># Combine factors</span>
    <span class="n">risk_score</span> <span class="o">=</span> <span class="p">(</span>
        <span class="n">weighted_asset_risk</span> <span class="o">*</span> <span class="mf">0.4</span> <span class="o">*</span> <span class="n">complexity_factor</span> <span class="o">*</span> <span class="mf">0.2</span> <span class="o">*</span>
        <span class="n">security_factor</span> <span class="o">*</span> <span class="mf">0.3</span> <span class="o">*</span> <span class="n">business_multiplier</span> <span class="o">*</span> <span class="mf">0.1</span>
    <span class="p">)</span>

    <span class="k">return</span> <span class="nb">min</span><span class="p">(</span><span class="mf">100.0</span><span class="p">,</span> <span class="n">risk_score</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="mitre-att-ck-integration">
<h2>MITRE ATT&amp;CK Integration<a class="headerlink" href="#mitre-att-ck-integration" title="Link to this heading"></a></h2>
<section id="framework-mapping">
<h3>Framework Mapping<a class="headerlink" href="#framework-mapping" title="Link to this heading"></a></h3>
<p>The system includes comprehensive MITRE ATT&amp;CK framework integration:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">MitreAttackMapping</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">technique_mappings</span> <span class="o">=</span> <span class="p">{</span>
            <span class="n">AttackTechnique</span><span class="o">.</span><span class="n">INITIAL_ACCESS</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;technique_id&quot;</span><span class="p">:</span> <span class="s2">&quot;TA0001&quot;</span><span class="p">,</span>
                <span class="s2">&quot;technique_name&quot;</span><span class="p">:</span> <span class="s2">&quot;Initial Access&quot;</span><span class="p">,</span>
                <span class="s2">&quot;tactic&quot;</span><span class="p">:</span> <span class="s2">&quot;Initial Access&quot;</span><span class="p">,</span>
                <span class="s2">&quot;platforms&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Windows&quot;</span><span class="p">,</span> <span class="s2">&quot;Linux&quot;</span><span class="p">,</span> <span class="s2">&quot;macOS&quot;</span><span class="p">,</span> <span class="s2">&quot;Cloud&quot;</span><span class="p">],</span>
                <span class="s2">&quot;data_sources&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Network Traffic&quot;</span><span class="p">,</span> <span class="s2">&quot;Authentication Logs&quot;</span><span class="p">],</span>
                <span class="s2">&quot;mitigations&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Network Segmentation&quot;</span><span class="p">,</span> <span class="s2">&quot;Multi-factor Authentication&quot;</span><span class="p">],</span>
                <span class="s2">&quot;detection_methods&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Anomaly Detection&quot;</span><span class="p">,</span> <span class="s2">&quot;Behavioral Analysis&quot;</span><span class="p">]</span>
            <span class="p">},</span>
            <span class="c1"># ... additional mappings for all 12 tactics</span>
        <span class="p">}</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">map_attack_path</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">attack_path</span><span class="p">:</span> <span class="n">AttackPath</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Map attack path to MITRE ATT&amp;CK techniques.&quot;&quot;&quot;</span>
        <span class="n">mapping</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;tactics&quot;</span><span class="p">:</span> <span class="p">[],</span> <span class="s2">&quot;techniques&quot;</span><span class="p">:</span> <span class="p">[],</span> <span class="s2">&quot;mitigations&quot;</span><span class="p">:</span> <span class="p">[],</span> <span class="s2">&quot;detection_methods&quot;</span><span class="p">:</span> <span class="p">[]}</span>

        <span class="k">for</span> <span class="n">technique</span> <span class="ow">in</span> <span class="n">attack_path</span><span class="o">.</span><span class="n">attack_techniques</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">technique</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">technique_mappings</span><span class="p">:</span>
                <span class="n">mitre_data</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">technique_mappings</span><span class="p">[</span><span class="n">technique</span><span class="p">]</span>
                <span class="n">mapping</span><span class="p">[</span><span class="s2">&quot;tactics&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">mitre_data</span><span class="p">[</span><span class="s2">&quot;tactic&quot;</span><span class="p">])</span>
                <span class="n">mapping</span><span class="p">[</span><span class="s2">&quot;techniques&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">({</span>
                    <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="n">mitre_data</span><span class="p">[</span><span class="s2">&quot;technique_id&quot;</span><span class="p">],</span>
                    <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">mitre_data</span><span class="p">[</span><span class="s2">&quot;technique_name&quot;</span><span class="p">]</span>
                <span class="p">})</span>
                <span class="n">mapping</span><span class="p">[</span><span class="s2">&quot;mitigations&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">mitre_data</span><span class="p">[</span><span class="s2">&quot;mitigations&quot;</span><span class="p">])</span>
                <span class="n">mapping</span><span class="p">[</span><span class="s2">&quot;detection_methods&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">mitre_data</span><span class="p">[</span><span class="s2">&quot;detection_methods&quot;</span><span class="p">])</span>

        <span class="k">return</span> <span class="n">mapping</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-optimizations">
<h2>Performance Optimizations<a class="headerlink" href="#performance-optimizations" title="Link to this heading"></a></h2>
<section id="caching-strategy">
<h3>Caching Strategy<a class="headerlink" href="#caching-strategy" title="Link to this heading"></a></h3>
<p>The engine implements a multi-level caching strategy:</p>
<p><strong>Level 1: In-Memory LRU Cache</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">IntelligentCache</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">max_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1000</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">path_cache</span> <span class="o">=</span> <span class="n">LRUCache</span><span class="p">(</span><span class="n">max_size</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">blast_radius_cache</span> <span class="o">=</span> <span class="n">LRUCache</span><span class="p">(</span><span class="n">max_size</span> <span class="o">//</span> <span class="mi">2</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">metrics</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;hits&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;misses&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">}</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">get_attack_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cache_key</span><span class="p">:</span> <span class="n">Tuple</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="n">AttackPath</span><span class="p">]]:</span>
        <span class="k">if</span> <span class="n">cache_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">path_cache</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">metrics</span><span class="p">[</span><span class="s2">&quot;hits&quot;</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">path_cache</span><span class="p">[</span><span class="n">cache_key</span><span class="p">]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">metrics</span><span class="p">[</span><span class="s2">&quot;misses&quot;</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="k">return</span> <span class="kc">None</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">cache_attack_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cache_key</span><span class="p">:</span> <span class="n">Tuple</span><span class="p">,</span> <span class="n">paths</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">AttackPath</span><span class="p">]):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">path_cache</span><span class="p">[</span><span class="n">cache_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">paths</span>
</pre></div>
</div>
<p><strong>Level 2: Redis Distributed Cache</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">DistributedCache</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">redis_client</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">redis</span> <span class="o">=</span> <span class="n">redis_client</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ttl</span> <span class="o">=</span> <span class="mi">3600</span>  <span class="c1"># 1 hour TTL</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_cached_result</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">]:</span>
        <span class="n">cached</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">redis</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;attack_path:</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">cached</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">cached</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">None</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">cache_result</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">result</span><span class="p">:</span> <span class="n">Dict</span><span class="p">):</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">redis</span><span class="o">.</span><span class="n">setex</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;attack_path:</span><span class="si">{</span><span class="n">key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">ttl</span><span class="p">,</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">result</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="nb">str</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="parallel-processing">
<h3>Parallel Processing<a class="headerlink" href="#parallel-processing" title="Link to this heading"></a></h3>
<p>The engine uses parallel processing for improved performance:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">ParallelAnalyzer</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">max_workers</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">4</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">executor</span> <span class="o">=</span> <span class="n">ThreadPoolExecutor</span><span class="p">(</span><span class="n">max_workers</span><span class="o">=</span><span class="n">max_workers</span><span class="p">)</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">analyze_multiple_targets</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">targets</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">List</span><span class="p">[</span><span class="n">AttackPath</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Analyze attack paths to multiple targets in parallel.&quot;&quot;&quot;</span>
        <span class="n">loop</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">get_event_loop</span><span class="p">()</span>

        <span class="c1"># Create tasks for parallel execution</span>
        <span class="n">tasks</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">target</span> <span class="ow">in</span> <span class="n">targets</span><span class="p">:</span>
            <span class="n">task</span> <span class="o">=</span> <span class="n">loop</span><span class="o">.</span><span class="n">run_in_executor</span><span class="p">(</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">executor</span><span class="p">,</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_analyze_single_target</span><span class="p">,</span>
                <span class="n">source</span><span class="p">,</span>
                <span class="n">target</span>
            <span class="p">)</span>
            <span class="n">tasks</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">target</span><span class="p">,</span> <span class="n">task</span><span class="p">))</span>

        <span class="c1"># Wait for all tasks to complete</span>
        <span class="n">results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">target</span><span class="p">,</span> <span class="n">task</span> <span class="ow">in</span> <span class="n">tasks</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">paths</span> <span class="o">=</span> <span class="k">await</span> <span class="n">task</span>
                <span class="n">results</span><span class="p">[</span><span class="n">target</span><span class="p">]</span> <span class="o">=</span> <span class="n">paths</span>
            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Analysis failed for target </span><span class="si">{</span><span class="n">target</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                <span class="n">results</span><span class="p">[</span><span class="n">target</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">return</span> <span class="n">results</span>
</pre></div>
</div>
<p>This architecture provides a robust, scalable foundation for enterprise-grade attack path analysis with comprehensive monitoring, caching, and performance optimization.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Technical Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="database-design.html" class="btn btn-neutral float-right" title="Database Design and Schema" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>