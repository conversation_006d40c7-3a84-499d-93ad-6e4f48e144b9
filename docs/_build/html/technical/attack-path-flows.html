

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Attack Path Analysis Flows &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script type="module" src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.esm.min.mjs"></script>
      <script type="module" src="https://cdn.jsdelivr.net/npm/@mermaid-js/layout-elk@0.1.4/dist/mermaid-layout-elk.esm.min.mjs"></script>
      <script type="module">
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#2980B9',
        primaryTextColor: '#ffffff',
        primaryBorderColor: '#1f5f99',
        lineColor: '#34495e',
        sectionBkgColor: '#ecf0f1',
        altSectionBkgColor: '#bdc3c7',
        gridColor: '#95a5a6',
        secondaryColor: '#3498db',
        tertiaryColor: '#e74c3c'
    },
    flowchart: {
        nodeSpacing: 50,
        rankSpacing: 50,
        curve: 'basis'
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    },
    gantt: {
        titleTopMargin: 25,
        barHeight: 20,
        fontFamily: '"Open Sans", sans-serif',
        fontSize: 11,
        gridLineStartPadding: 35,
        bottomPadding: 25,
        leftPadding: 75,
        rightPadding: 50
    }
});
</script>
      <script src="https://cdn.jsdelivr.net/npm/d3@7.9.0/dist/d3.min.js"></script>
      <script type="module">
import mermaid from "https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.esm.min.mjs";
window.addEventListener("load", () => mermaid.run());
</script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Product Requirements Document (PRD)" href="product-requirements.html" />
    <link rel="prev" title="Database Design and Schema" href="database-design.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Technical Documentation</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#architecture-and-design">Architecture and Design</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#database-design">Database Design</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#graph-analysis-engine">Graph Analysis Engine</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Attack Path Analysis Flows</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#product-requirements-document">Product Requirements Document</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#performance-and-operations">Performance and Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#development-and-integration">Development and Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#data-models-and-schemas">Data Models and Schemas</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-quality-assurance">Testing and Quality Assurance</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#contributing-and-development">Contributing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Technical Documentation</a></li>
      <li class="breadcrumb-item active">Attack Path Analysis Flows</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/technical/attack-path-flows.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="attack-path-analysis-flows">
<h1>Attack Path Analysis Flows<a class="headerlink" href="#attack-path-analysis-flows" title="Link to this heading"></a></h1>
<p>This document provides comprehensive flow diagrams and decision trees for the attack path analysis engine, illustrating the complete workflow from asset discovery to threat mitigation.</p>
<section id="attack-path-discovery-flow">
<h2>Attack Path Discovery Flow<a class="headerlink" href="#attack-path-discovery-flow" title="Link to this heading"></a></h2>
<p>The following diagram shows the complete attack path discovery process:</p>
<pre  class="mermaid">
        flowchart TD
    A[Start Attack Path Analysis] --&gt; B{Asset Discovery Complete?}
    B --&gt;|No| C[Trigger Asset Discovery]
    C --&gt; D[Scan Network Infrastructure]
    D --&gt; E[Collect Asset Metadata]
    E --&gt; F[Map Asset Relationships]
    F --&gt; B

    B --&gt;|Yes| G[Initialize Graph Engine]
    G --&gt; H[Load Asset Data into Graph]
    H --&gt; I[Calculate Edge Weights]
    I --&gt; J[Validate Graph Connectivity]

    J --&gt; K{Source Asset Exists?}
    K --&gt;|No| L[Return Asset Not Found Error]
    K --&gt;|Yes| M{Target Assets Specified?}

    M --&gt;|No| N[Identify High-Value Targets]
    N --&gt; O[Filter by Business Criticality]
    O --&gt; P[Filter by Data Classification]
    P --&gt; Q[Create Target Asset List]
    Q --&gt; R[Start Path Discovery]

    M --&gt;|Yes| S[Validate Target Assets]
    S --&gt; T{All Targets Valid?}
    T --&gt;|No| U[Return Invalid Target Error]
    T --&gt;|Yes| R

    R --&gt; V[Initialize Pathfinding Algorithm]
    V --&gt; W[Set Maximum Path Length]
    W --&gt; X[Set Maximum Paths per Target]
    X --&gt; Y[Begin Breadth-First Search]

    Y --&gt; Z{Path Found?}
    Z --&gt;|No| AA[Check Next Target]
    Z --&gt;|Yes| BB[Calculate Path Risk Score]
    BB --&gt; CC[Calculate Path Likelihood]
    CC --&gt; DD[Identify Attack Techniques]
    DD --&gt; EE[Map to MITRE ATT&amp;CK]
    EE --&gt; FF[Calculate Criticality Score]
    FF --&gt; GG[Store Path in Results]
    GG --&gt; AA

    AA --&gt; HH{More Targets?}
    HH --&gt;|Yes| Y
    HH --&gt;|No| II[Sort Paths by Criticality]
    II --&gt; JJ[Apply Result Limits]
    JJ --&gt; KK[Cache Results]
    KK --&gt; LL[Return Attack Paths]

    L --&gt; MM[End]
    U --&gt; MM
    LL --&gt; MM
    </pre></section>
<section id="blast-radius-calculation-flow">
<h2>Blast Radius Calculation Flow<a class="headerlink" href="#blast-radius-calculation-flow" title="Link to this heading"></a></h2>
<p>The blast radius calculation process follows this comprehensive workflow:</p>
<pre  class="mermaid">
        flowchart TD
    A[Start Blast Radius Calculation] --&gt; B{Source Asset Valid?}
    B --&gt;|No| C[Return Asset Not Found Error]
    B --&gt;|Yes| D[Initialize Blast Radius Engine]

    D --&gt; E[Set Maximum Degrees]
    E --&gt; F[Initialize Impact Tracking]
    F --&gt; G[Create Affected Assets Set]
    G --&gt; H[Set Current Degree = 0]
    H --&gt; I[Add Source Asset to Current Level]

    I --&gt; J{Current Level Empty?}
    J --&gt;|Yes| K[Calculate Total Impact]
    J --&gt;|No| L[Process Current Level Assets]

    L --&gt; M[For Each Asset in Current Level]
    M --&gt; N[Get Asset Neighbors]
    N --&gt; O[Calculate Propagation Probability]
    O --&gt; P{Propagation Likely?}

    P --&gt;|No| Q[Skip to Next Asset]
    P --&gt;|Yes| R[Add to Next Level]
    R --&gt; S[Calculate Asset Impact Score]
    S --&gt; T[Update Impact by Degree]
    T --&gt; U[Check Asset Criticality]
    U --&gt; V{Asset Critical?}

    V --&gt;|Yes| W[Add to Critical Assets]
    V --&gt;|No| X[Check Data Classification]
    X --&gt; Y{Contains Sensitive Data?}
    Y --&gt;|Yes| Z[Add to Data Assets]
    Y --&gt;|No| AA[Continue Processing]

    W --&gt; AA
    Z --&gt; AA
    AA --&gt; Q
    Q --&gt; BB{More Assets in Level?}
    BB --&gt;|Yes| M
    BB --&gt;|No| CC[Increment Degree]
    CC --&gt; DD{Degree &lt; Max Degrees?}
    DD --&gt;|Yes| EE[Set Current Level = Next Level]
    EE --&gt; FF[Clear Next Level]
    FF --&gt; J
    DD --&gt;|No| K

    K --&gt; GG[Calculate Financial Impact]
    GG --&gt; HH[Estimate Recovery Time]
    HH --&gt; II[Assess Compliance Impact]
    II --&gt; JJ[Calculate Service Disruption]
    JJ --&gt; KK[Generate Blast Radius Result]
    KK --&gt; LL[Cache Result]
    LL --&gt; MM[Return Blast Radius]

    C --&gt; NN[End]
    MM --&gt; NN
    </pre></section>
<section id="attack-scenario-creation-flow">
<h2>Attack Scenario Creation Flow<a class="headerlink" href="#attack-scenario-creation-flow" title="Link to this heading"></a></h2>
<p>The attack scenario modeling process:</p>
<pre  class="mermaid">
        flowchart TD
    A[Start Attack Scenario Creation] --&gt; B[Validate Input Parameters]
    B --&gt; C{Entry Points Valid?}
    C --&gt;|No| D[Return Invalid Entry Points Error]
    C --&gt;|Yes| E{Objectives Valid?}
    E --&gt;|No| F[Return Invalid Objectives Error]
    E --&gt;|Yes| G[Initialize Scenario Builder]

    G --&gt; H[Create Scenario ID]
    H --&gt; I[Set Threat Actor Profile]
    I --&gt; J[Initialize Attack Path Collection]
    J --&gt; K[For Each Entry Point]

    K --&gt; L[For Each Objective]
    L --&gt; M[Find Attack Paths]
    M --&gt; N{Paths Found?}
    N --&gt;|No| O[Log No Path Warning]
    N --&gt;|Yes| P[Add Paths to Collection]

    O --&gt; Q{More Objectives?}
    P --&gt; Q
    Q --&gt;|Yes| L
    Q --&gt;|No| R{More Entry Points?}
    R --&gt;|Yes| K
    R --&gt;|No| S[Analyze Path Collection]

    S --&gt; T[Calculate Scenario Risk Score]
    T --&gt; U[Determine Overall Likelihood]
    U --&gt; V[Calculate Total Impact Score]
    V --&gt; W[Estimate Attack Duration]
    W --&gt; X[Identify Required Resources]
    X --&gt; Y[Calculate Detection Probability]
    Y --&gt; Z[Generate Mitigation Strategies]
    Z --&gt; AA[Determine Criticality Level]
    AA --&gt; BB[Create Scenario Object]
    BB --&gt; CC[Cache Scenario]
    CC --&gt; DD[Return Attack Scenario]

    D --&gt; EE[End]
    F --&gt; EE
    DD --&gt; EE
    </pre></section>
<section id="mitre-att-ck-mapping-decision-tree">
<h2>MITRE ATT&amp;CK Mapping Decision Tree<a class="headerlink" href="#mitre-att-ck-mapping-decision-tree" title="Link to this heading"></a></h2>
<p>The decision process for mapping attack paths to MITRE ATT&amp;CK techniques:</p>
<pre  class="mermaid">
        flowchart TD
    A[Start MITRE Mapping] --&gt; B[Analyze Attack Path]
    B --&gt; C[Examine Source Asset Type]
    C --&gt; D{Public Facing?}
    D --&gt;|Yes| E[Add Initial Access - T1190]
    D --&gt;|No| F{Internal Asset?}
    F --&gt;|Yes| G[Add Initial Access - T1078]
    F --&gt;|No| H[Add Initial Access - T1566]

    E --&gt; I[Analyze Path Length]
    G --&gt; I
    H --&gt; I
    I --&gt; J{Path Length &gt; 1?}
    J --&gt;|No| K[Single Hop Attack]
    J --&gt;|Yes| L[Multi-Hop Attack]

    K --&gt; M[Check Target Asset Type]
    L --&gt; N[Add Lateral Movement - T1021]
    N --&gt; O[Check Privilege Requirements]
    O --&gt; P{Requires Privilege Escalation?}
    P --&gt;|Yes| Q[Add Privilege Escalation - T1068]
    P --&gt;|No| R[Continue Analysis]

    Q --&gt; R
    R --&gt; S[Analyze Asset Relationships]
    S --&gt; T{Credential Access Required?}
    T --&gt;|Yes| U[Add Credential Access - T1003]
    T --&gt;|No| V[Check Discovery Requirements]

    U --&gt; V
    V --&gt; W{Network Discovery Required?}
    W --&gt;|Yes| X[Add Discovery - T1018]
    W --&gt;|No| Y[Check Target Type]

    X --&gt; Y
    M --&gt; Y
    Y --&gt; Z{Target is Database?}
    Z --&gt;|Yes| AA[Add Collection - T1005]
    Z --&gt;|No| BB{Target is Critical System?}
    BB --&gt;|Yes| CC[Add Impact - T1485]
    BB --&gt;|No| DD[Add Impact - T1499]

    AA --&gt; EE[Check Data Exfiltration]
    CC --&gt; EE
    DD --&gt; EE
    EE --&gt; FF{Data Exfiltration Likely?}
    FF --&gt;|Yes| GG[Add Exfiltration - T1041]
    FF --&gt;|No| HH[Finalize Mapping]

    GG --&gt; HH
    HH --&gt; II[Generate Mitigation Recommendations]
    II --&gt; JJ[Generate Detection Methods]
    JJ --&gt; KK[Return MITRE Mapping]
    KK --&gt; LL[End]
    </pre></section>
<section id="risk-scoring-decision-flow">
<h2>Risk Scoring Decision Flow<a class="headerlink" href="#risk-scoring-decision-flow" title="Link to this heading"></a></h2>
<p>The comprehensive risk scoring algorithm:</p>
<pre  class="mermaid">
        flowchart TD
    A[Start Risk Scoring] --&gt; B[Initialize Score Components]
    B --&gt; C[Calculate Asset Risk Component]
    C --&gt; D[Get Source Asset Risk Score]
    D --&gt; E[Get Target Asset Risk Score]
    E --&gt; F[Calculate Path Asset Scores]
    F --&gt; G[Apply Distance Weighting]
    G --&gt; H[Asset Risk = Weighted Average]

    H --&gt; I[Calculate Path Complexity Component]
    I --&gt; J[Path Length Factor = Length * 0.1]
    J --&gt; K[Relationship Complexity Factor]
    K --&gt; L[Protocol Security Factor]
    L --&gt; M[Complexity Score = Combined Factors]

    M --&gt; N[Calculate Security Controls Component]
    N --&gt; O[For Each Path Edge]
    O --&gt; P{Encrypted Connection?}
    P --&gt;|Yes| Q[Apply Encryption Bonus]
    P --&gt;|No| R{Authenticated Connection?}

    Q --&gt; R
    R --&gt;|Yes| S[Apply Authentication Bonus]
    R --&gt;|No| T{Monitored Connection?}

    S --&gt; T
    T --&gt;|Yes| U[Apply Monitoring Bonus]
    T --&gt;|No| V[Continue to Next Edge]

    U --&gt; V
    V --&gt; W{More Edges?}
    W --&gt;|Yes| O
    W --&gt;|No| X[Calculate Controls Score]

    X --&gt; Y[Calculate Business Impact Component]
    Y --&gt; Z[Get Target Business Criticality]
    Z --&gt; AA{Critical Asset?}
    AA --&gt;|Yes| BB[Impact Multiplier = 2.0]
    AA --&gt;|No| CC{High Value Asset?}
    CC --&gt;|Yes| DD[Impact Multiplier = 1.5]
    CC --&gt;|No| EE{Medium Value Asset?}
    EE --&gt;|Yes| FF[Impact Multiplier = 1.0]
    EE --&gt;|No| GG[Impact Multiplier = 0.5]

    BB --&gt; HH[Combine All Components]
    DD --&gt; HH
    FF --&gt; HH
    GG --&gt; HH

    HH --&gt; II[Risk Score = Asset Risk * 0.4]
    II --&gt; JJ[+ Complexity * 0.2]
    JJ --&gt; KK[+ Security Controls * 0.3]
    KK --&gt; LL[+ Business Impact * 0.1]
    LL --&gt; MM[Normalize to 0-100 Scale]
    MM --&gt; NN[Return Risk Score]
    NN --&gt; OO[End]
    </pre></section>
<section id="caching-strategy-decision-flow">
<h2>Caching Strategy Decision Flow<a class="headerlink" href="#caching-strategy-decision-flow" title="Link to this heading"></a></h2>
<p>The intelligent caching system workflow:</p>
<pre  class="mermaid">
        flowchart TD
    A[Receive Analysis Request] --&gt; B[Generate Cache Key]
    B --&gt; C[Check L1 Cache - Memory]
    C --&gt; D{Cache Hit?}
    D --&gt;|Yes| E[Update Access Time]
    E --&gt; F[Return Cached Result]

    D --&gt;|No| G[Check L2 Cache - Redis]
    G --&gt; H{Cache Hit?}
    H --&gt;|Yes| I[Load into L1 Cache]
    I --&gt; J[Return Cached Result]

    H --&gt;|No| K[Execute Analysis]
    K --&gt; L[Generate Results]
    L --&gt; M[Calculate Result Size]
    M --&gt; N{Size &lt; Cache Limit?}
    N --&gt;|No| O[Return Results Without Caching]
    N --&gt;|Yes| P[Store in L1 Cache]
    P --&gt; Q[Store in L2 Cache]
    Q --&gt; R[Set TTL Based on Analysis Type]
    R --&gt; S{Attack Path Analysis?}
    S --&gt;|Yes| T[TTL = 1 Hour]
    S --&gt;|No| U{Blast Radius Analysis?}
    U --&gt;|Yes| V[TTL = 30 Minutes]
    U --&gt;|No| W[TTL = 15 Minutes]

    T --&gt; X[Return Results]
    V --&gt; X
    W --&gt; X
    F --&gt; Y[End]
    J --&gt; Y
    O --&gt; Y
    X --&gt; Y
    </pre></section>
<section id="error-handling-and-recovery-flow">
<h2>Error Handling and Recovery Flow<a class="headerlink" href="#error-handling-and-recovery-flow" title="Link to this heading"></a></h2>
<p>Comprehensive error handling workflow:</p>
<pre  class="mermaid">
        flowchart TD
    A[Analysis Request Received] --&gt; B[Validate Input Parameters]
    B --&gt; C{Parameters Valid?}
    C --&gt;|No| D[Return 400 Bad Request]
    C --&gt;|Yes| E[Check Authentication]
    E --&gt; F{User Authenticated?}
    F --&gt;|No| G[Return 401 Unauthorized]
    F --&gt;|Yes| H[Check Authorization]
    H --&gt; I{User Authorized?}
    I --&gt;|No| J[Return 403 Forbidden]
    I --&gt;|Yes| K[Check Rate Limits]
    K --&gt; L{Within Rate Limit?}
    L --&gt;|No| M[Return 429 Too Many Requests]
    L --&gt;|Yes| N[Start Analysis]

    N --&gt; O[Try Analysis Execution]
    O --&gt; P{Analysis Successful?}
    P --&gt;|Yes| Q[Return Results]
    P --&gt;|No| R[Analyze Error Type]

    R --&gt; S{Timeout Error?}
    S --&gt;|Yes| T[Return 504 Gateway Timeout]
    S --&gt;|No| U{Resource Not Found?}
    U --&gt;|Yes| V[Return 404 Not Found]
    U --&gt;|No| W{Database Error?}
    W --&gt;|Yes| X[Log Database Error]
    X --&gt; Y[Return 500 Internal Server Error]
    W --&gt;|No| Z{Graph Processing Error?}
    Z --&gt;|Yes| AA[Log Graph Error]
    AA --&gt; BB[Return 422 Unprocessable Entity]
    Z --&gt;|No| CC[Log Unknown Error]
    CC --&gt; DD[Return 500 Internal Server Error]

    D --&gt; EE[Log Request Error]
    G --&gt; EE
    J --&gt; EE
    M --&gt; EE
    T --&gt; EE
    V --&gt; EE
    Y --&gt; EE
    BB --&gt; EE
    DD --&gt; EE
    Q --&gt; FF[Log Successful Request]

    EE --&gt; GG[Update Error Metrics]
    FF --&gt; HH[Update Success Metrics]
    GG --&gt; II[End]
    HH --&gt; II
    </pre></section>
<section id="performance-optimization-flow">
<h2>Performance Optimization Flow<a class="headerlink" href="#performance-optimization-flow" title="Link to this heading"></a></h2>
<p>The performance optimization decision process:</p>
<pre  class="mermaid">
        flowchart TD
    A[Monitor Analysis Performance] --&gt; B[Check Response Time]
    B --&gt; C{Response Time &gt; Threshold?}
    C --&gt;|No| D[Continue Monitoring]
    C --&gt;|Yes| E[Analyze Performance Bottleneck]

    E --&gt; F{High CPU Usage?}
    F --&gt;|Yes| G[Check Graph Size]
    G --&gt; H{Graph Too Large?}
    H --&gt;|Yes| I[Implement Graph Partitioning]
    H --&gt;|No| J[Increase Worker Threads]

    F --&gt;|No| K{High Memory Usage?}
    K --&gt;|Yes| L[Check Cache Size]
    L --&gt; M{Cache Too Large?}
    M --&gt;|Yes| N[Reduce Cache Size]
    M --&gt;|No| O[Optimize Graph Storage]

    K --&gt;|No| P{High Database Load?}
    P --&gt;|Yes| Q[Check Query Performance]
    Q --&gt; R{Slow Queries Detected?}
    R --&gt;|Yes| S[Optimize Database Indexes]
    R --&gt;|No| T[Implement Connection Pooling]

    P --&gt;|No| U{High Network Latency?}
    U --&gt;|Yes| V[Implement Result Compression]
    U --&gt;|No| W[Profile Application Code]

    I --&gt; X[Test Performance Improvement]
    J --&gt; X
    N --&gt; X
    O --&gt; X
    S --&gt; X
    T --&gt; X
    V --&gt; X
    W --&gt; X

    X --&gt; Y{Performance Improved?}
    Y --&gt;|Yes| Z[Deploy Optimization]
    Y --&gt;|No| AA[Try Alternative Optimization]

    Z --&gt; D
    AA --&gt; E
    D --&gt; BB[End Monitoring Cycle]
    </pre><p>These comprehensive flow diagrams provide detailed visualization of all major processes in the attack path analysis engine, from initial request processing through error handling and performance optimization.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="database-design.html" class="btn btn-neutral float-left" title="Database Design and Schema" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="product-requirements.html" class="btn btn-neutral float-right" title="Product Requirements Document (PRD)" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>