

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Database Design and Schema &mdash; Blast-Radius Security Tool v1.0.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f65ed55f" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script type="module" src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.esm.min.mjs"></script>
      <script type="module" src="https://cdn.jsdelivr.net/npm/@mermaid-js/layout-elk@0.1.4/dist/mermaid-layout-elk.esm.min.mjs"></script>
      <script type="module">
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#2980B9',
        primaryTextColor: '#ffffff',
        primaryBorderColor: '#1f5f99',
        lineColor: '#34495e',
        sectionBkgColor: '#ecf0f1',
        altSectionBkgColor: '#bdc3c7',
        gridColor: '#95a5a6',
        secondaryColor: '#3498db',
        tertiaryColor: '#e74c3c'
    },
    flowchart: {
        nodeSpacing: 50,
        rankSpacing: 50,
        curve: 'basis'
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    },
    gantt: {
        titleTopMargin: 25,
        barHeight: 20,
        fontFamily: '"Open Sans", sans-serif',
        fontSize: 11,
        gridLineStartPadding: 35,
        bottomPadding: 25,
        leftPadding: 75,
        rightPadding: 50
    }
});
</script>
      <script src="https://cdn.jsdelivr.net/npm/d3@7.9.0/dist/d3.min.js"></script>
      <script type="module">
import mermaid from "https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.esm.min.mjs";
window.addEventListener("load", () => mermaid.run());
</script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Attack Path Analysis Flows" href="attack-path-flows.html" />
    <link rel="prev" title="Attack Path Analysis Architecture" href="attack-path-architecture.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Technical Documentation</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#architecture-and-design">Architecture and Design</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html#database-design">Database Design</a><ul class="current">
<li class="toctree-l4 current"><a class="current reference internal" href="#">Database Design and Schema</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="index.html#graph-analysis-engine">Graph Analysis Engine</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#product-requirements-document">Product Requirements Document</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#performance-and-operations">Performance and Operations</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#development-and-integration">Development and Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#data-models-and-schemas">Data Models and Schemas</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing-and-quality-assurance">Testing and Quality Assurance</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#contributing-and-development">Contributing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#support-and-resources">Support and Resources</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Technical Documentation</a></li>
      <li class="breadcrumb-item active">Database Design and Schema</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/technical/database-design.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="database-design-and-schema">
<h1>Database Design and Schema<a class="headerlink" href="#database-design-and-schema" title="Link to this heading"></a></h1>
<p>This document provides comprehensive documentation of the Blast-Radius Security Tool database design, including entity relationships, schema definitions, and optimization strategies.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The database is designed using PostgreSQL with a focus on:</p>
<ul class="simple">
<li><p><strong>Scalability</strong>: Support for enterprise-scale asset inventories</p></li>
<li><p><strong>Performance</strong>: Optimized for real-time queries and analysis</p></li>
<li><p><strong>Audit Compliance</strong>: Complete audit trails for regulatory compliance</p></li>
<li><p><strong>Data Integrity</strong>: Comprehensive constraints and validation</p></li>
<li><p><strong>Flexibility</strong>: Extensible schema for diverse asset types</p></li>
</ul>
</section>
<section id="database-architecture">
<h2>Database Architecture<a class="headerlink" href="#database-architecture" title="Link to this heading"></a></h2>
<pre  class="mermaid">
        graph TB
    subgraph &quot;Core Tables&quot;
        Users[Users]
        Assets[Assets]
        AssetTypes[Asset Types]
        Providers[Providers]
        Environments[Environments]
    end

    subgraph &quot;Relationship Tables&quot;
        AssetRelationships[Asset Relationships]
        AssetDependencies[Asset Dependencies]
        AssetCommunications[Asset Communications]
    end

    subgraph &quot;Extended Asset Data&quot;
        AssetVulnerabilities[Asset Vulnerabilities]
        AssetCompliance[Asset Compliance]
        AssetMetrics[Asset Metrics]
        AssetConfigurations[Asset Configurations]
    end

    subgraph &quot;Discovery &amp; Analysis&quot;
        DiscoveryJobs[Discovery Jobs]
        DiscoverySources[Discovery Sources]
        AttackPaths[Attack Paths]
        BlastRadius[Blast Radius Results]
    end

    subgraph &quot;Audit &amp; Security&quot;
        AuditLogs[Audit Logs]
        UserSessions[User Sessions]
        APIKeys[API Keys]
        DataRetention[Data Retention Policies]
    end

    Users --&gt; Assets
    Assets --&gt; AssetTypes
    Assets --&gt; Providers
    Assets --&gt; Environments
    Assets --&gt; AssetRelationships
    Assets --&gt; AssetVulnerabilities
    Assets --&gt; AssetCompliance
    Assets --&gt; AssetMetrics
    Assets --&gt; AssetConfigurations
    Assets --&gt; AttackPaths
    DiscoveryJobs --&gt; Assets
    DiscoverySources --&gt; DiscoveryJobs
    Users --&gt; AuditLogs
    Users --&gt; UserSessions
    Users --&gt; APIKeys
    </pre></section>
<section id="core-entity-relationship-diagram">
<h2>Core Entity Relationship Diagram<a class="headerlink" href="#core-entity-relationship-diagram" title="Link to this heading"></a></h2>
<pre  class="mermaid">
        erDiagram
    USERS {
        uuid id PK
        string username UK
        string email UK
        string password_hash
        string first_name
        string last_name
        enum role
        boolean is_active
        boolean is_verified
        timestamp created_at
        timestamp updated_at
        timestamp last_login
    }

    ASSETS {
        uuid id PK
        string name
        enum asset_type FK
        enum provider FK
        string environment FK
        string ip_addresses
        jsonb configuration
        jsonb properties
        float risk_score
        enum status
        boolean is_deleted
        timestamp deleted_at
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
    }

    ASSET_TYPES {
        string value PK
        string display_name
        string description
        jsonb default_properties
        boolean is_active
    }

    PROVIDERS {
        string value PK
        string display_name
        string description
        jsonb configuration_schema
        boolean is_active
    }

    ENVIRONMENTS {
        string name PK
        string description
        enum criticality_level
        jsonb configuration
        boolean is_active
    }

    ASSET_RELATIONSHIPS {
        uuid id PK
        uuid source_asset_id FK
        uuid target_asset_id FK
        enum relationship_type
        string protocol
        integer port
        enum direction
        string description
        float confidence
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    ASSET_VULNERABILITIES {
        uuid id PK
        uuid asset_id FK
        string vulnerability_id
        string title
        text description
        enum severity
        float cvss_score
        string cve_id
        jsonb details
        enum status
        timestamp discovered_at
        timestamp updated_at
    }

    ASSET_COMPLIANCE {
        uuid id PK
        uuid asset_id FK
        string framework
        string control_id
        string control_name
        enum compliance_status
        text findings
        timestamp assessed_at
        timestamp next_assessment
    }

    DISCOVERY_JOBS {
        uuid id PK
        string name
        enum job_type
        jsonb configuration
        enum status
        timestamp started_at
        timestamp completed_at
        integer assets_discovered
        integer assets_updated
        text error_message
        uuid created_by FK
    }

    AUDIT_LOGS {
        uuid id PK
        uuid user_id FK
        string action
        string resource_type
        uuid resource_id
        jsonb old_values
        jsonb new_values
        string ip_address
        string user_agent
        timestamp created_at
    }

    USERS ||--o{ ASSETS : creates
    USERS ||--o{ DISCOVERY_JOBS : initiates
    USERS ||--o{ AUDIT_LOGS : generates
    ASSETS ||--o{ ASSET_RELATIONSHIPS : source
    ASSETS ||--o{ ASSET_RELATIONSHIPS : target
    ASSETS ||--o{ ASSET_VULNERABILITIES : has
    ASSETS ||--o{ ASSET_COMPLIANCE : assessed
    ASSETS }o--|| ASSET_TYPES : classified_as
    ASSETS }o--|| PROVIDERS : hosted_on
    ASSETS }o--|| ENVIRONMENTS : deployed_in
    </pre></section>
<section id="attack-path-analysis-schema">
<h2>Attack Path Analysis Schema<a class="headerlink" href="#attack-path-analysis-schema" title="Link to this heading"></a></h2>
<pre  class="mermaid">
        erDiagram
    ATTACK_PATHS {
        uuid id PK
        string path_id UK
        uuid source_asset_id FK
        uuid target_asset_id FK
        jsonb path_nodes
        jsonb path_edges
        enum path_type
        jsonb attack_techniques
        float risk_score
        float likelihood
        float impact_score
        integer blast_radius
        integer estimated_time
        jsonb required_privileges
        float detection_difficulty
        float mitigation_cost
        integer path_length
        float criticality_score
        timestamp created_at
        timestamp updated_at
    }

    ATTACK_SCENARIOS {
        uuid id PK
        string scenario_id UK
        string name
        text description
        string threat_actor
        jsonb entry_points
        jsonb objectives
        float total_risk_score
        float likelihood
        float impact_score
        integer estimated_duration
        jsonb required_resources
        float detection_probability
        jsonb mitigation_strategies
        enum criticality_level
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
    }

    SCENARIO_PATHS {
        uuid id PK
        uuid scenario_id FK
        uuid attack_path_id FK
        integer sequence_order
        timestamp created_at
    }

    BLAST_RADIUS_RESULTS {
        uuid id PK
        uuid source_asset_id FK
        jsonb affected_assets
        jsonb impact_by_degree
        float total_impact_score
        jsonb critical_assets_affected
        jsonb data_assets_affected
        float service_disruption_score
        float financial_impact
        jsonb compliance_impact
        integer recovery_time_estimate
        integer max_degrees
        timestamp calculated_at
        timestamp expires_at
    }

    MITRE_ATTACK_MAPPINGS {
        uuid id PK
        string technique_id UK
        string technique_name
        string tactic
        jsonb platforms
        jsonb data_sources
        jsonb mitigations
        jsonb detection_methods
        text description
        boolean is_active
    }

    ATTACK_PATHS }o--|| ASSETS : source
    ATTACK_PATHS }o--|| ASSETS : target
    ATTACK_SCENARIOS ||--o{ SCENARIO_PATHS : contains
    SCENARIO_PATHS }o--|| ATTACK_PATHS : includes
    BLAST_RADIUS_RESULTS }o--|| ASSETS : originates_from
    ATTACK_SCENARIOS }o--|| USERS : created_by
    </pre></section>
<section id="table-definitions">
<h2>Table Definitions<a class="headerlink" href="#table-definitions" title="Link to this heading"></a></h2>
<section id="core-tables">
<h3>Core Tables<a class="headerlink" href="#core-tables" title="Link to this heading"></a></h3>
<p><strong>Users Table</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">users</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">gen_random_uuid</span><span class="p">(),</span>
<span class="w">    </span><span class="n">username</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span><span class="w"> </span><span class="k">UNIQUE</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">email</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">UNIQUE</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">password_hash</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">first_name</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="n">last_name</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="k">role</span><span class="w"> </span><span class="n">user_role_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;analyst&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">is_active</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">true</span><span class="p">,</span>
<span class="w">    </span><span class="n">is_verified</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">false</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">last_login</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="p">,</span>

<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">users_username_length</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="k">length</span><span class="p">(</span><span class="n">username</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">3</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">users_email_format</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">email</span><span class="w"> </span><span class="o">~*</span><span class="w"> </span><span class="s1">&#39;^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$&#39;</span><span class="p">)</span>
<span class="p">);</span>
</pre></div>
</div>
<p><strong>Assets Table</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">assets</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">gen_random_uuid</span><span class="p">(),</span>
<span class="w">    </span><span class="n">name</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">asset_type</span><span class="w"> </span><span class="n">asset_type_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">provider</span><span class="w"> </span><span class="n">provider_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">environment</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">ip_addresses</span><span class="w"> </span><span class="n">INET</span><span class="p">[],</span>
<span class="w">    </span><span class="n">configuration</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;{}&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">properties</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;{}&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">risk_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="n">asset_status_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;active&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">is_deleted</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">false</span><span class="p">,</span>
<span class="w">    </span><span class="n">deleted_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_by</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">users</span><span class="p">(</span><span class="n">id</span><span class="p">),</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>

<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">assets_risk_score_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">risk_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">risk_score</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">assets_soft_delete</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span>
<span class="w">        </span><span class="p">(</span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">deleted_at</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span><span class="w"> </span><span class="k">OR</span>
<span class="w">        </span><span class="p">(</span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">true</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">deleted_at</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="p">);</span>
</pre></div>
</div>
<p><strong>Asset Relationships Table</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">asset_relationships</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">gen_random_uuid</span><span class="p">(),</span>
<span class="w">    </span><span class="n">source_asset_id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">id</span><span class="p">)</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">DELETE</span><span class="w"> </span><span class="k">CASCADE</span><span class="p">,</span>
<span class="w">    </span><span class="n">target_asset_id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">id</span><span class="p">)</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="k">DELETE</span><span class="w"> </span><span class="k">CASCADE</span><span class="p">,</span>
<span class="w">    </span><span class="n">relationship_type</span><span class="w"> </span><span class="n">relationship_type_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">protocol</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">20</span><span class="p">),</span>
<span class="w">    </span><span class="n">port</span><span class="w"> </span><span class="nb">INTEGER</span><span class="p">,</span>
<span class="w">    </span><span class="n">direction</span><span class="w"> </span><span class="n">direction_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;outbound&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">description</span><span class="w"> </span><span class="nb">TEXT</span><span class="p">,</span>
<span class="w">    </span><span class="n">confidence</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">1</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">is_active</span><span class="w"> </span><span class="nb">BOOLEAN</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">true</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>

<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">asset_relationships_no_self_reference</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">source_asset_id</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">target_asset_id</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">asset_relationships_confidence_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">confidence</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">confidence</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">asset_relationships_port_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">port</span><span class="w"> </span><span class="k">IS</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="p">(</span><span class="n">port</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">1</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">port</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">65535</span><span class="p">)),</span>
<span class="w">    </span><span class="k">UNIQUE</span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">,</span><span class="w"> </span><span class="n">target_asset_id</span><span class="p">,</span><span class="w"> </span><span class="n">relationship_type</span><span class="p">,</span><span class="w"> </span><span class="n">protocol</span><span class="p">,</span><span class="w"> </span><span class="n">port</span><span class="p">)</span>
<span class="p">);</span>
</pre></div>
</div>
</section>
<section id="attack-path-analysis-tables">
<h3>Attack Path Analysis Tables<a class="headerlink" href="#attack-path-analysis-tables" title="Link to this heading"></a></h3>
<p><strong>Attack Paths Table</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">attack_paths</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">gen_random_uuid</span><span class="p">(),</span>
<span class="w">    </span><span class="n">path_id</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">UNIQUE</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">source_asset_id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">id</span><span class="p">),</span>
<span class="w">    </span><span class="n">target_asset_id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">id</span><span class="p">),</span>
<span class="w">    </span><span class="n">path_nodes</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">path_edges</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">path_type</span><span class="w"> </span><span class="n">path_type_enum</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">attack_techniques</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;[]&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">risk_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">likelihood</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">impact_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">blast_radius</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">estimated_time</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">required_privileges</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;[]&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">detection_difficulty</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="n">mitigation_cost</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">path_length</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">criticality_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">GENERATED</span><span class="w"> </span><span class="n">ALWAYS</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">        </span><span class="p">(</span><span class="n">risk_score</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">4</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">impact_score</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">4</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="p">(</span><span class="mi">100</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="n">detection_difficulty</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">100</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">2</span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span><span class="w"> </span><span class="n">STORED</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>

<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_paths_risk_score_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">risk_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">risk_score</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_paths_likelihood_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">likelihood</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">likelihood</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_paths_impact_score_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">impact_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">impact_score</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_paths_detection_difficulty_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">detection_difficulty</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">detection_difficulty</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_paths_path_length_positive</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">path_length</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span>
<span class="p">);</span>
</pre></div>
</div>
<p><strong>Attack Scenarios Table</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">attack_scenarios</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">gen_random_uuid</span><span class="p">(),</span>
<span class="w">    </span><span class="n">scenario_id</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">UNIQUE</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">name</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">description</span><span class="w"> </span><span class="nb">TEXT</span><span class="p">,</span>
<span class="w">    </span><span class="n">threat_actor</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">entry_points</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">objectives</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">total_risk_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">likelihood</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">impact_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">estimated_duration</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">required_resources</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;[]&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">detection_probability</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">.</span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="n">mitigation_strategies</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="s1">&#39;[]&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="n">criticality_level</span><span class="w"> </span><span class="n">criticality_level_enum</span><span class="w"> </span><span class="k">GENERATED</span><span class="w"> </span><span class="n">ALWAYS</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="p">(</span>
<span class="w">        </span><span class="k">CASE</span>
<span class="w">            </span><span class="k">WHEN</span><span class="w"> </span><span class="n">total_risk_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">80</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">&#39;CRITICAL&#39;</span>
<span class="w">            </span><span class="k">WHEN</span><span class="w"> </span><span class="n">total_risk_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">60</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">&#39;HIGH&#39;</span>
<span class="w">            </span><span class="k">WHEN</span><span class="w"> </span><span class="n">total_risk_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">40</span><span class="w"> </span><span class="k">THEN</span><span class="w"> </span><span class="s1">&#39;MEDIUM&#39;</span>
<span class="w">            </span><span class="k">ELSE</span><span class="w"> </span><span class="s1">&#39;LOW&#39;</span>
<span class="w">        </span><span class="k">END</span>
<span class="w">    </span><span class="p">)</span><span class="w"> </span><span class="n">STORED</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_by</span><span class="w"> </span><span class="n">UUID</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">users</span><span class="p">(</span><span class="n">id</span><span class="p">),</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>

<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_scenarios_risk_score_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">total_risk_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">total_risk_score</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_scenarios_likelihood_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">likelihood</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">likelihood</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_scenarios_impact_score_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">impact_score</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">impact_score</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">100</span><span class="p">),</span>
<span class="w">    </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">attack_scenarios_detection_probability_range</span><span class="w"> </span><span class="k">CHECK</span><span class="w"> </span><span class="p">(</span><span class="n">detection_probability</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="n">detection_probability</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span>
<span class="p">);</span>
</pre></div>
</div>
</section>
</section>
<section id="enumeration-types">
<h2>Enumeration Types<a class="headerlink" href="#enumeration-types" title="Link to this heading"></a></h2>
<p><strong>Asset Types</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TYPE</span><span class="w"> </span><span class="n">asset_type_enum</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">ENUM</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="s1">&#39;server&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;database&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;workstation&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;network_device&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;security_device&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;storage&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;application&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;container&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;virtual_machine&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;cloud_service&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;iot_device&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;mobile_device&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;printer&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;scanner&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;phone_system&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;building_system&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;industrial_control&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;medical_device&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;vehicle&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;software&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;firmware&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;operating_system&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;middleware&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;api&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;web_service&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;microservice&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;lambda_function&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;kubernetes_pod&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;docker_container&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;load_balancer&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;firewall&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;ids_ips&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;waf&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;proxy&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;vpn&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;router&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;switch&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;wireless_access_point&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;domain_controller&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;certificate_authority&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;backup_system&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;monitoring_system&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;logging_system&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;siem&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;soar&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;vulnerability_scanner&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;patch_management&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;endpoint_protection&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;email_security&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;web_security&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;cloud_security&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;identity_provider&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;mfa_system&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;privileged_access&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;secrets_management&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;encryption_system&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;key_management&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;data_loss_prevention&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;threat_intelligence&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;sandbox&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;deception_technology&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;network_segmentation&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;zero_trust&#39;</span>
<span class="p">);</span>
</pre></div>
</div>
<p><strong>Relationship Types</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TYPE</span><span class="w"> </span><span class="n">relationship_type_enum</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">ENUM</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="s1">&#39;communicates_with&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;depends_on&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;manages&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;accesses&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;hosts&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;contains&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;monitors&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;protects&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;authenticates&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;authorizes&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;encrypts&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;backs_up&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;replicates&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;load_balances&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;proxies&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;routes&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;switches&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;bridges&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;tunnels&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;vpn_connects&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;federates&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;trusts&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;delegates&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;inherits&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;extends&#39;</span>
<span class="p">);</span>
</pre></div>
</div>
<p><strong>Attack Path Types</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TYPE</span><span class="w"> </span><span class="n">path_type_enum</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">ENUM</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="s1">&#39;direct&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;lateral_movement&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;privilege_escalation&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;data_exfiltration&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;supply_chain&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;insider_threat&#39;</span>
<span class="p">);</span>
</pre></div>
</div>
</section>
<section id="indexes-and-performance-optimization">
<h2>Indexes and Performance Optimization<a class="headerlink" href="#indexes-and-performance-optimization" title="Link to this heading"></a></h2>
<p><strong>Primary Indexes</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Core asset indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_type_provider</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">asset_type</span><span class="p">,</span><span class="w"> </span><span class="n">provider</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_environment_status</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">status</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_risk_score</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">risk_score</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_created_at</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">created_at</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_soft_delete</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">is_deleted</span><span class="p">,</span><span class="w"> </span><span class="n">deleted_at</span><span class="p">);</span>

<span class="c1">-- Asset relationship indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_asset_relationships_source</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_asset_relationships_target</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">(</span><span class="n">target_asset_id</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_asset_relationships_type</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">(</span><span class="n">relationship_type</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_asset_relationships_active</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">(</span><span class="n">is_active</span><span class="p">);</span>

<span class="c1">-- Attack path indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_source_target</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">,</span><span class="w"> </span><span class="n">target_asset_id</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_risk_score</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">risk_score</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_criticality</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">criticality_score</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_path_type</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">path_type</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_created_at</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">created_at</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>JSONB Indexes</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Asset configuration indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_config_gin</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">GIN</span><span class="p">(</span><span class="n">configuration</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_properties_gin</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">GIN</span><span class="p">(</span><span class="n">properties</span><span class="p">);</span>

<span class="c1">-- Attack path technique indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_techniques_gin</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">GIN</span><span class="p">(</span><span class="n">attack_techniques</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_nodes_gin</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">GIN</span><span class="p">(</span><span class="n">path_nodes</span><span class="p">);</span>

<span class="c1">-- Attack scenario indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_scenarios_entry_points_gin</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_scenarios</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">GIN</span><span class="p">(</span><span class="n">entry_points</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_scenarios_objectives_gin</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_scenarios</span><span class="w"> </span><span class="k">USING</span><span class="w"> </span><span class="n">GIN</span><span class="p">(</span><span class="n">objectives</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Composite Indexes</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Multi-column indexes for common queries</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_assets_type_env_status</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">asset_type</span><span class="p">,</span><span class="w"> </span><span class="n">environment</span><span class="p">,</span><span class="w"> </span><span class="n">status</span><span class="p">)</span>
<span class="w">    </span><span class="k">WHERE</span><span class="w"> </span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="p">;</span>

<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_relationships_source_type_active</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">,</span><span class="w"> </span><span class="n">relationship_type</span><span class="p">,</span><span class="w"> </span><span class="n">is_active</span><span class="p">);</span>

<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_source_risk_criticality</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">,</span><span class="w"> </span><span class="n">risk_score</span><span class="w"> </span><span class="k">DESC</span><span class="p">,</span><span class="w"> </span><span class="n">criticality_score</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="data-retention-and-archival">
<h2>Data Retention and Archival<a class="headerlink" href="#data-retention-and-archival" title="Link to this heading"></a></h2>
<p><strong>Soft Delete Implementation</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Soft delete trigger function</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">soft_delete_asset</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="k">TRIGGER</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="err">$$</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">IF</span><span class="w"> </span><span class="k">NEW</span><span class="p">.</span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">true</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="k">OLD</span><span class="p">.</span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">NEW</span><span class="p">.</span><span class="n">deleted_at</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">;</span>
<span class="w">    </span><span class="k">ELSIF</span><span class="w"> </span><span class="k">NEW</span><span class="p">.</span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="w"> </span><span class="k">AND</span><span class="w"> </span><span class="k">OLD</span><span class="p">.</span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">true</span><span class="w"> </span><span class="k">THEN</span>
<span class="w">        </span><span class="k">NEW</span><span class="p">.</span><span class="n">deleted_at</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">NULL</span><span class="p">;</span>
<span class="w">    </span><span class="k">END</span><span class="w"> </span><span class="k">IF</span><span class="p">;</span>
<span class="w">    </span><span class="k">RETURN</span><span class="w"> </span><span class="k">NEW</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
<span class="err">$$</span><span class="w"> </span><span class="k">LANGUAGE</span><span class="w"> </span><span class="n">plpgsql</span><span class="p">;</span>

<span class="c1">-- Apply soft delete trigger</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">TRIGGER</span><span class="w"> </span><span class="n">trigger_soft_delete_asset</span>
<span class="w">    </span><span class="k">BEFORE</span><span class="w"> </span><span class="k">UPDATE</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">assets</span>
<span class="w">    </span><span class="k">FOR</span><span class="w"> </span><span class="k">EACH</span><span class="w"> </span><span class="k">ROW</span>
<span class="w">    </span><span class="k">EXECUTE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">soft_delete_asset</span><span class="p">();</span>
</pre></div>
</div>
<p><strong>Data Retention Policies</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Audit log retention (90 days)</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">cleanup_audit_logs</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="n">void</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="err">$$</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DELETE</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">audit_logs</span>
<span class="w">    </span><span class="k">WHERE</span><span class="w"> </span><span class="n">created_at</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nb">INTERVAL</span><span class="w"> </span><span class="s1">&#39;90 days&#39;</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
<span class="err">$$</span><span class="w"> </span><span class="k">LANGUAGE</span><span class="w"> </span><span class="n">plpgsql</span><span class="p">;</span>

<span class="c1">-- Attack path cache cleanup (30 days)</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">cleanup_attack_paths</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="n">void</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="err">$$</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">DELETE</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">attack_paths</span>
<span class="w">    </span><span class="k">WHERE</span><span class="w"> </span><span class="n">created_at</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nb">INTERVAL</span><span class="w"> </span><span class="s1">&#39;30 days&#39;</span>
<span class="w">    </span><span class="k">AND</span><span class="w"> </span><span class="n">path_id</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">IN</span><span class="w"> </span><span class="p">(</span>
<span class="w">        </span><span class="k">SELECT</span><span class="w"> </span><span class="k">DISTINCT</span><span class="w"> </span><span class="k">unnest</span><span class="p">(</span><span class="n">string_to_array</span><span class="p">(</span><span class="n">path_nodes</span><span class="p">::</span><span class="nb">text</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;,&#39;</span><span class="p">))</span>
<span class="w">        </span><span class="k">FROM</span><span class="w"> </span><span class="n">attack_scenarios</span>
<span class="w">        </span><span class="k">WHERE</span><span class="w"> </span><span class="n">created_at</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nb">INTERVAL</span><span class="w"> </span><span class="s1">&#39;7 days&#39;</span>
<span class="w">    </span><span class="p">);</span>
<span class="k">END</span><span class="p">;</span>
<span class="err">$$</span><span class="w"> </span><span class="k">LANGUAGE</span><span class="w"> </span><span class="n">plpgsql</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="database-maintenance">
<h2>Database Maintenance<a class="headerlink" href="#database-maintenance" title="Link to this heading"></a></h2>
<p><strong>Vacuum and Analyze Schedule</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Automated maintenance procedures</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">maintenance_vacuum_analyze</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="n">void</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="err">$$</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="c1">-- Vacuum and analyze high-traffic tables</span>
<span class="w">    </span><span class="k">VACUUM</span><span class="w"> </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">assets</span><span class="p">;</span>
<span class="w">    </span><span class="k">VACUUM</span><span class="w"> </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">;</span>
<span class="w">    </span><span class="k">VACUUM</span><span class="w"> </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">;</span>
<span class="w">    </span><span class="k">VACUUM</span><span class="w"> </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">audit_logs</span><span class="p">;</span>

<span class="w">    </span><span class="c1">-- Update table statistics</span>
<span class="w">    </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">assets</span><span class="p">;</span>
<span class="w">    </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">asset_relationships</span><span class="p">;</span>
<span class="w">    </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">;</span>
<span class="w">    </span><span class="k">ANALYZE</span><span class="w"> </span><span class="n">attack_scenarios</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
<span class="err">$$</span><span class="w"> </span><span class="k">LANGUAGE</span><span class="w"> </span><span class="n">plpgsql</span><span class="p">;</span>
</pre></div>
</div>
<p><strong>Index Maintenance</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Reindex heavily used indexes</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">OR</span><span class="w"> </span><span class="k">REPLACE</span><span class="w"> </span><span class="k">FUNCTION</span><span class="w"> </span><span class="n">maintenance_reindex</span><span class="p">()</span>
<span class="k">RETURNS</span><span class="w"> </span><span class="n">void</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="err">$$</span>
<span class="k">BEGIN</span>
<span class="w">    </span><span class="k">REINDEX</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_assets_type_provider</span><span class="p">;</span>
<span class="w">    </span><span class="k">REINDEX</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_asset_relationships_source</span><span class="p">;</span>
<span class="w">    </span><span class="k">REINDEX</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_attack_paths_source_target</span><span class="p">;</span>
<span class="w">    </span><span class="k">REINDEX</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_attack_paths_criticality</span><span class="p">;</span>
<span class="k">END</span><span class="p">;</span>
<span class="err">$$</span><span class="w"> </span><span class="k">LANGUAGE</span><span class="w"> </span><span class="n">plpgsql</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="performance-monitoring">
<h2>Performance Monitoring<a class="headerlink" href="#performance-monitoring" title="Link to this heading"></a></h2>
<p><strong>Query Performance Views</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Slow query monitoring</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">VIEW</span><span class="w"> </span><span class="n">slow_queries</span><span class="w"> </span><span class="k">AS</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="n">query</span><span class="p">,</span>
<span class="w">    </span><span class="n">calls</span><span class="p">,</span>
<span class="w">    </span><span class="n">total_time</span><span class="p">,</span>
<span class="w">    </span><span class="n">mean_time</span><span class="p">,</span>
<span class="w">    </span><span class="k">rows</span><span class="p">,</span>
<span class="w">    </span><span class="mi">100</span><span class="p">.</span><span class="mi">0</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">shared_blks_hit</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="k">nullif</span><span class="p">(</span><span class="n">shared_blks_hit</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">shared_blks_read</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">hit_percent</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">pg_stat_statements</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">mean_time</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">100</span><span class="w">  </span><span class="c1">-- Queries taking more than 100ms on average</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">mean_time</span><span class="w"> </span><span class="k">DESC</span><span class="p">;</span>

<span class="c1">-- Table size monitoring</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">VIEW</span><span class="w"> </span><span class="n">table_sizes</span><span class="w"> </span><span class="k">AS</span>
<span class="k">SELECT</span>
<span class="w">    </span><span class="n">schemaname</span><span class="p">,</span>
<span class="w">    </span><span class="n">tablename</span><span class="p">,</span>
<span class="w">    </span><span class="n">pg_size_pretty</span><span class="p">(</span><span class="n">pg_total_relation_size</span><span class="p">(</span><span class="n">schemaname</span><span class="o">||</span><span class="s1">&#39;.&#39;</span><span class="o">||</span><span class="n">tablename</span><span class="p">))</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="k">size</span><span class="p">,</span>
<span class="w">    </span><span class="n">pg_total_relation_size</span><span class="p">(</span><span class="n">schemaname</span><span class="o">||</span><span class="s1">&#39;.&#39;</span><span class="o">||</span><span class="n">tablename</span><span class="p">)</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="n">size_bytes</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">pg_tables</span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">schemaname</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;public&#39;</span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">size_bytes</span><span class="w"> </span><span class="k">DESC</span><span class="p">;</span>
</pre></div>
</div>
<p>This comprehensive database design provides a robust foundation for the Blast-Radius Security Tool with optimized performance, comprehensive audit trails, and scalable architecture for enterprise deployments.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="attack-path-architecture.html" class="btn btn-neutral float-left" title="Attack Path Analysis Architecture" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="attack-path-flows.html" class="btn btn-neutral float-right" title="Attack Path Analysis Flows" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Blast-Radius Security Tool Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>