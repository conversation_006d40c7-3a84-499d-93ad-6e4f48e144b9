#!/bin/bash

# Blast-Radius Documentation Build Script
# This script builds the Sphinx documentation with Mermaid support

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${DOCS_DIR}/_build"
SOURCE_DIR="${DOCS_DIR}"
REQUIREMENTS_FILE="${DOCS_DIR}/requirements.txt"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed."
        exit 1
    fi
    log_info "Python 3 found: $(python3 --version)"
}

# Check if pip is available
check_pip() {
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 is required but not installed."
        exit 1
    fi
    log_info "pip3 found: $(pip3 --version)"
}

# Install requirements
install_requirements() {
    log_info "Installing documentation requirements..."
    if [ -f "$REQUIREMENTS_FILE" ]; then
        pip3 install -r "$REQUIREMENTS_FILE"
        log_success "Requirements installed successfully"
    else
        log_warning "Requirements file not found: $REQUIREMENTS_FILE"
    fi
}

# Clean build directory
clean_build() {
    log_info "Cleaning build directory..."
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        log_success "Build directory cleaned"
    else
        log_info "Build directory doesn't exist, skipping clean"
    fi
}

# Build HTML documentation
build_html() {
    log_info "Building HTML documentation..."
    cd "$DOCS_DIR"
    
    # Use sphinx-build directly for better control
    sphinx-build -b html -d "${BUILD_DIR}/doctrees" "$SOURCE_DIR" "${BUILD_DIR}/html"
    
    if [ $? -eq 0 ]; then
        log_success "HTML documentation built successfully"
        log_info "Documentation available at: ${BUILD_DIR}/html/index.html"
    else
        log_error "Failed to build HTML documentation"
        exit 1
    fi
}

# Build PDF documentation (optional)
build_pdf() {
    log_info "Building PDF documentation..."
    cd "$DOCS_DIR"
    
    if command -v rst2pdf &> /dev/null; then
        sphinx-build -b pdf "$SOURCE_DIR" "${BUILD_DIR}/pdf"
        if [ $? -eq 0 ]; then
            log_success "PDF documentation built successfully"
        else
            log_warning "Failed to build PDF documentation"
        fi
    else
        log_warning "rst2pdf not found, skipping PDF build"
    fi
}

# Check for broken links
check_links() {
    log_info "Checking for broken links..."
    cd "$DOCS_DIR"
    
    sphinx-build -b linkcheck "$SOURCE_DIR" "${BUILD_DIR}/linkcheck"
    
    if [ $? -eq 0 ]; then
        log_success "Link check completed"
    else
        log_warning "Some links may be broken, check the linkcheck report"
    fi
}

# Generate coverage report
generate_coverage() {
    log_info "Generating documentation coverage report..."
    cd "$DOCS_DIR"
    
    sphinx-build -b coverage "$SOURCE_DIR" "${BUILD_DIR}/coverage"
    
    if [ $? -eq 0 ]; then
        log_success "Coverage report generated"
        log_info "Coverage report available at: ${BUILD_DIR}/coverage/python.txt"
    else
        log_warning "Failed to generate coverage report"
    fi
}

# Start development server
serve_docs() {
    local port=${1:-8000}
    log_info "Starting documentation server on port $port..."
    
    if [ -d "${BUILD_DIR}/html" ]; then
        cd "${BUILD_DIR}/html"
        python3 -m http.server "$port"
    else
        log_error "HTML documentation not found. Run build first."
        exit 1
    fi
}

# Start live reload server
serve_live() {
    local port=${1:-8080}
    log_info "Starting live reload server on port $port..."
    
    if command -v sphinx-autobuild &> /dev/null; then
        cd "$DOCS_DIR"
        sphinx-autobuild -b html "$SOURCE_DIR" "${BUILD_DIR}/html" --host 0.0.0.0 --port "$port"
    else
        log_error "sphinx-autobuild not found. Install it with: pip install sphinx-autobuild"
        exit 1
    fi
}

# Docker build
docker_build() {
    log_info "Building documentation with Docker..."
    cd "$DOCS_DIR"
    
    if command -v docker &> /dev/null; then
        docker-compose build docs-builder
        docker-compose run --rm docs-builder
        log_success "Docker build completed"
    else
        log_error "Docker not found. Please install Docker to use this option."
        exit 1
    fi
}

# Docker serve
docker_serve() {
    log_info "Starting documentation server with Docker..."
    cd "$DOCS_DIR"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up docs
    else
        log_error "Docker Compose not found. Please install Docker Compose to use this option."
        exit 1
    fi
}

# Show help
show_help() {
    cat << EOF
Blast-Radius Documentation Build Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    build           Build HTML documentation (default)
    clean           Clean build directory
    pdf             Build PDF documentation
    linkcheck       Check for broken links
    coverage        Generate coverage report
    serve [PORT]    Serve documentation (default port: 8000)
    live [PORT]     Start live reload server (default port: 8080)
    docker-build    Build with Docker
    docker-serve    Serve with Docker
    all             Build all formats and run checks
    help            Show this help message

Examples:
    $0                      # Build HTML documentation
    $0 build                # Build HTML documentation
    $0 clean build          # Clean and build
    $0 serve 9000           # Serve on port 9000
    $0 live                 # Start live reload server
    $0 all                  # Build everything and run checks
    $0 docker-serve         # Serve with Docker

Environment Variables:
    DOCS_PORT              # Default port for serving (default: 8000)
    DOCS_LIVE_PORT         # Default port for live reload (default: 8080)
    SKIP_REQUIREMENTS      # Skip requirements installation (set to 1)

EOF
}

# Main execution
main() {
    local command=${1:-build}
    
    case "$command" in
        "build")
            check_python
            check_pip
            [ "${SKIP_REQUIREMENTS:-0}" != "1" ] && install_requirements
            build_html
            ;;
        "clean")
            clean_build
            ;;
        "pdf")
            check_python
            check_pip
            [ "${SKIP_REQUIREMENTS:-0}" != "1" ] && install_requirements
            build_pdf
            ;;
        "linkcheck")
            check_python
            check_pip
            [ "${SKIP_REQUIREMENTS:-0}" != "1" ] && install_requirements
            check_links
            ;;
        "coverage")
            check_python
            check_pip
            [ "${SKIP_REQUIREMENTS:-0}" != "1" ] && install_requirements
            generate_coverage
            ;;
        "serve")
            serve_docs "${2:-${DOCS_PORT:-8000}}"
            ;;
        "live")
            check_python
            check_pip
            [ "${SKIP_REQUIREMENTS:-0}" != "1" ] && install_requirements
            serve_live "${2:-${DOCS_LIVE_PORT:-8080}}"
            ;;
        "docker-build")
            docker_build
            ;;
        "docker-serve")
            docker_serve
            ;;
        "all")
            check_python
            check_pip
            [ "${SKIP_REQUIREMENTS:-0}" != "1" ] && install_requirements
            clean_build
            build_html
            build_pdf
            check_links
            generate_coverage
            log_success "All documentation tasks completed!"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
