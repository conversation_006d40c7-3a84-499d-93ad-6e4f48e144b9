events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    server {
        listen 80;
        server_name docs.blastradius.localhost localhost;
        root /usr/share/nginx/html;
        index index.html portal.html;

        # Enable directory browsing for development
        autoindex on;
        autoindex_exact_size off;
        autoindex_localtime on;

        # Main documentation location
        location / {
            try_files $uri $uri/ $uri.html /index.html;
            
            # Cache static assets
            location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }

        # API documentation
        location /api/ {
            try_files $uri $uri/ $uri.html /api/index.html;
        }

        # User guides
        location /user-guides/ {
            try_files $uri $uri/ $uri.html /user-guides/index.html;
        }

        # Technical documentation
        location /technical/ {
            try_files $uri $uri/ $uri.html /technical/index.html;
        }

        # Handle Sphinx search
        location /search.html {
            try_files $uri /search.html;
        }

        # Handle Sphinx static files
        location /_static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Handle Sphinx source files
        location /_sources/ {
            expires 1d;
            add_header Cache-Control "public";
        }

        # Mermaid.js support
        location /mermaid/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Documentation portal
        location /portal {
            try_files /portal.html =404;
        }

        # Redirect common paths
        location = /docs {
            return 301 /;
        }

        location = /documentation {
            return 301 /;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;

        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }

        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }

        # Security: Deny access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Security: Deny access to backup files
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }

    # Additional server block for development
    server {
        listen 80;
        server_name dev.docs.blastradius.localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Development-specific settings
        add_header X-Development-Mode "true" always;
        
        # Disable caching in development
        location / {
            try_files $uri $uri/ $uri.html /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Enable CORS for development
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
        }
    }
}
