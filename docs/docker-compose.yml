version: '3.8'

services:
  docs:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    volumes:
      - .:/docs
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    environment:
      - DOCS_ENV=development
    restart: unless-stopped
    networks:
      - docs-network

  docs-builder:
    build:
      context: .
      dockerfile: Dockerfile.builder
    volumes:
      - .:/docs
      - ./_build:/docs/_build
    working_dir: /docs
    command: >
      sh -c "
        pip install -r requirements.txt &&
        make clean &&
        make html &&
        echo 'Documentation built successfully!'
      "
    networks:
      - docs-network

  docs-dev:
    build:
      context: .
      dockerfile: Dockerfile.builder
    ports:
      - "8081:8081"
    volumes:
      - .:/docs
    working_dir: /docs
    command: >
      sh -c "
        pip install -r requirements.txt &&
        pip install sphinx-autobuild &&
        sphinx-autobuild -b html . _build/html --host 0.0.0.0 --port 8081 --watch .
      "
    networks:
      - docs-network

networks:
  docs-network:
    driver: bridge

volumes:
  docs-build:
    driver: local
