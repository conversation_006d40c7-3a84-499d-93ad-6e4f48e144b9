/* Custom CSS for Blast-Radius Security Tool Documentation */

/* Brand colors */
:root {
    --blast-primary: #2980B9;
    --blast-secondary: #E74C3C;
    --blast-accent: #F39C12;
    --blast-success: #27AE60;
    --blast-warning: #F39C12;
    --blast-danger: #E74C3C;
    --blast-dark: #2C3E50;
    --blast-light: #ECF0F1;
}

/* Custom roles styling */
.user-role {
    background-color: var(--blast-primary);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 0.9em;
}

.api-endpoint {
    background-color: var(--blast-success);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

.permission {
    background-color: var(--blast-warning);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 0.9em;
}

/* Enhanced admonitions */
.admonition.security {
    border-left: 4px solid var(--blast-danger);
    background-color: #fdf2f2;
}

.admonition.security .admonition-title {
    background-color: var(--blast-danger);
    color: white;
}

.admonition.performance {
    border-left: 4px solid var(--blast-accent);
    background-color: #fef9e7;
}

.admonition.performance .admonition-title {
    background-color: var(--blast-accent);
    color: white;
}

/* Code blocks enhancement */
.highlight {
    border-radius: 5px;
    border: 1px solid #e1e4e5;
}

.highlight pre {
    padding: 12px;
    line-height: 1.4;
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    border: 1px solid #e1e4e5;
    margin: 1em 0;
}

table.docutils th {
    background-color: var(--blast-primary);
    color: white;
    padding: 8px 12px;
    text-align: left;
}

table.docutils td {
    padding: 8px 12px;
    border-bottom: 1px solid #e1e4e5;
}

table.docutils tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Navigation enhancements */
.wy-nav-side {
    background: linear-gradient(180deg, var(--blast-dark) 0%, #34495e 100%);
}

.wy-menu-vertical a {
    color: #bdc3c7;
}

.wy-menu-vertical a:hover {
    background-color: var(--blast-primary);
    color: white;
}

.wy-menu-vertical li.current > a {
    background-color: var(--blast-primary);
    border-right: 3px solid var(--blast-accent);
}

/* Header styling */
.wy-nav-top {
    background-color: var(--blast-primary);
}

/* Search box styling */
.wy-side-nav-search {
    background-color: var(--blast-dark);
}

.wy-side-nav-search input[type=text] {
    border: 1px solid var(--blast-primary);
    border-radius: 4px;
}

/* Content area enhancements */
.wy-nav-content {
    max-width: 1200px;
}

/* Custom badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary {
    color: #fff;
    background-color: var(--blast-primary);
}

.badge-secondary {
    color: #fff;
    background-color: var(--blast-secondary);
}

.badge-success {
    color: #fff;
    background-color: var(--blast-success);
}

.badge-warning {
    color: #212529;
    background-color: var(--blast-warning);
}

.badge-danger {
    color: #fff;
    background-color: var(--blast-danger);
}

/* Responsive design */
@media (max-width: 768px) {
    .wy-nav-content {
        margin-left: 0;
    }
    
    .wy-nav-side {
        left: -300px;
    }
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
}
