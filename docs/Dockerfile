# Multi-stage build for Sphinx documentation
FROM python:3.11-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /docs

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy documentation source
COPY . .

# Build documentation
RUN make clean && make html

# Production stage with nginx
FROM nginx:alpine

# Install Node.js for Mermaid support
RUN apk add --no-cache nodejs npm

# Copy built documentation
COPY --from=builder /docs/_build/html /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create custom index page if needed
RUN echo '<!DOCTYPE html>
<html>
<head>
    <title>Blast-Radius Security Tool Documentation</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; margin-bottom: 40px; }
        .links { display: flex; justify-content: center; gap: 20px; flex-wrap: wrap; }
        .link-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            text-decoration: none; 
            color: #333;
            min-width: 200px;
            text-align: center;
            transition: box-shadow 0.3s;
        }
        .link-card:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .link-card h3 { margin-top: 0; color: #2980B9; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Blast-Radius Security Tool</h1>
        <p>Comprehensive Documentation Portal</p>
    </div>
    <div class="links">
        <a href="/index.html" class="link-card">
            <h3>📚 Main Documentation</h3>
            <p>Complete user guides and technical documentation</p>
        </a>
        <a href="/api/index.html" class="link-card">
            <h3>🔌 API Reference</h3>
            <p>REST API documentation and examples</p>
        </a>
        <a href="/user-guides/index.html" class="link-card">
            <h3>👥 User Guides</h3>
            <p>Role-based guides for all user types</p>
        </a>
        <a href="/technical/index.html" class="link-card">
            <h3>🔧 Technical Docs</h3>
            <p>Architecture and implementation details</p>
        </a>
    </div>
</body>
</html>' > /usr/share/nginx/html/portal.html

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
