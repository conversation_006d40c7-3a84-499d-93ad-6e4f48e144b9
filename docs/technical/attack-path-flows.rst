Attack Path Analysis Flows
===========================

This document provides comprehensive flow diagrams and decision trees for the attack path analysis engine, illustrating the complete workflow from asset discovery to threat mitigation.

Attack Path Discovery Flow
--------------------------

The following diagram shows the complete attack path discovery process:

.. mermaid::

    flowchart TD
        A[Start Attack Path Analysis] --> B{Asset Discovery Complete?}
        B -->|No| C[Trigger Asset Discovery]
        C --> D[Scan Network Infrastructure]
        D --> E[Collect Asset Metadata]
        E --> F[Map Asset Relationships]
        F --> B
        
        B -->|Yes| G[Initialize Graph Engine]
        G --> H[Load Asset Data into Graph]
        H --> I[Calculate Edge Weights]
        I --> J[Validate Graph Connectivity]
        
        J --> K{Source Asset Exists?}
        K -->|No| L[Return Asset Not Found Error]
        K -->|Yes| M{Target Assets Specified?}
        
        M -->|No| N[Identify High-Value Targets]
        N --> O[Filter by Business Criticality]
        O --> P[Filter by Data Classification]
        P --> Q[Create Target Asset List]
        Q --> R[Start Path Discovery]
        
        M -->|Yes| S[Validate Target Assets]
        S --> T{All Targets Valid?}
        T -->|No| U[Return Invalid Target Error]
        T -->|Yes| R
        
        R --> V[Initialize Pathfinding Algorithm]
        V --> W[Set Maximum Path Length]
        W --> X[Set Maximum Paths per Target]
        X --> Y[Begin Breadth-First Search]
        
        Y --> Z{Path Found?}
        Z -->|No| AA[Check Next Target]
        Z -->|Yes| BB[Calculate Path Risk Score]
        BB --> CC[Calculate Path Likelihood]
        CC --> DD[Identify Attack Techniques]
        DD --> EE[Map to MITRE ATT&CK]
        EE --> FF[Calculate Criticality Score]
        FF --> GG[Store Path in Results]
        GG --> AA
        
        AA --> HH{More Targets?}
        HH -->|Yes| Y
        HH -->|No| II[Sort Paths by Criticality]
        II --> JJ[Apply Result Limits]
        JJ --> KK[Cache Results]
        KK --> LL[Return Attack Paths]
        
        L --> MM[End]
        U --> MM
        LL --> MM

Blast Radius Calculation Flow
-----------------------------

The blast radius calculation process follows this comprehensive workflow:

.. mermaid::

    flowchart TD
        A[Start Blast Radius Calculation] --> B{Source Asset Valid?}
        B -->|No| C[Return Asset Not Found Error]
        B -->|Yes| D[Initialize Blast Radius Engine]
        
        D --> E[Set Maximum Degrees]
        E --> F[Initialize Impact Tracking]
        F --> G[Create Affected Assets Set]
        G --> H[Set Current Degree = 0]
        H --> I[Add Source Asset to Current Level]
        
        I --> J{Current Level Empty?}
        J -->|Yes| K[Calculate Total Impact]
        J -->|No| L[Process Current Level Assets]
        
        L --> M[For Each Asset in Current Level]
        M --> N[Get Asset Neighbors]
        N --> O[Calculate Propagation Probability]
        O --> P{Propagation Likely?}
        
        P -->|No| Q[Skip to Next Asset]
        P -->|Yes| R[Add to Next Level]
        R --> S[Calculate Asset Impact Score]
        S --> T[Update Impact by Degree]
        T --> U[Check Asset Criticality]
        U --> V{Asset Critical?}
        
        V -->|Yes| W[Add to Critical Assets]
        V -->|No| X[Check Data Classification]
        X --> Y{Contains Sensitive Data?}
        Y -->|Yes| Z[Add to Data Assets]
        Y -->|No| AA[Continue Processing]
        
        W --> AA
        Z --> AA
        AA --> Q
        Q --> BB{More Assets in Level?}
        BB -->|Yes| M
        BB -->|No| CC[Increment Degree]
        CC --> DD{Degree < Max Degrees?}
        DD -->|Yes| EE[Set Current Level = Next Level]
        EE --> FF[Clear Next Level]
        FF --> J
        DD -->|No| K
        
        K --> GG[Calculate Financial Impact]
        GG --> HH[Estimate Recovery Time]
        HH --> II[Assess Compliance Impact]
        II --> JJ[Calculate Service Disruption]
        JJ --> KK[Generate Blast Radius Result]
        KK --> LL[Cache Result]
        LL --> MM[Return Blast Radius]
        
        C --> NN[End]
        MM --> NN

Attack Scenario Creation Flow
-----------------------------

The attack scenario modeling process:

.. mermaid::

    flowchart TD
        A[Start Attack Scenario Creation] --> B[Validate Input Parameters]
        B --> C{Entry Points Valid?}
        C -->|No| D[Return Invalid Entry Points Error]
        C -->|Yes| E{Objectives Valid?}
        E -->|No| F[Return Invalid Objectives Error]
        E -->|Yes| G[Initialize Scenario Builder]
        
        G --> H[Create Scenario ID]
        H --> I[Set Threat Actor Profile]
        I --> J[Initialize Attack Path Collection]
        J --> K[For Each Entry Point]
        
        K --> L[For Each Objective]
        L --> M[Find Attack Paths]
        M --> N{Paths Found?}
        N -->|No| O[Log No Path Warning]
        N -->|Yes| P[Add Paths to Collection]
        
        O --> Q{More Objectives?}
        P --> Q
        Q -->|Yes| L
        Q -->|No| R{More Entry Points?}
        R -->|Yes| K
        R -->|No| S[Analyze Path Collection]
        
        S --> T[Calculate Scenario Risk Score]
        T --> U[Determine Overall Likelihood]
        U --> V[Calculate Total Impact Score]
        V --> W[Estimate Attack Duration]
        W --> X[Identify Required Resources]
        X --> Y[Calculate Detection Probability]
        Y --> Z[Generate Mitigation Strategies]
        Z --> AA[Determine Criticality Level]
        AA --> BB[Create Scenario Object]
        BB --> CC[Cache Scenario]
        CC --> DD[Return Attack Scenario]
        
        D --> EE[End]
        F --> EE
        DD --> EE

MITRE ATT&CK Mapping Decision Tree
----------------------------------

The decision process for mapping attack paths to MITRE ATT&CK techniques:

.. mermaid::

    flowchart TD
        A[Start MITRE Mapping] --> B[Analyze Attack Path]
        B --> C[Examine Source Asset Type]
        C --> D{Public Facing?}
        D -->|Yes| E[Add Initial Access - T1190]
        D -->|No| F{Internal Asset?}
        F -->|Yes| G[Add Initial Access - T1078]
        F -->|No| H[Add Initial Access - T1566]
        
        E --> I[Analyze Path Length]
        G --> I
        H --> I
        I --> J{Path Length > 1?}
        J -->|No| K[Single Hop Attack]
        J -->|Yes| L[Multi-Hop Attack]
        
        K --> M[Check Target Asset Type]
        L --> N[Add Lateral Movement - T1021]
        N --> O[Check Privilege Requirements]
        O --> P{Requires Privilege Escalation?}
        P -->|Yes| Q[Add Privilege Escalation - T1068]
        P -->|No| R[Continue Analysis]
        
        Q --> R
        R --> S[Analyze Asset Relationships]
        S --> T{Credential Access Required?}
        T -->|Yes| U[Add Credential Access - T1003]
        T -->|No| V[Check Discovery Requirements]
        
        U --> V
        V --> W{Network Discovery Required?}
        W -->|Yes| X[Add Discovery - T1018]
        W -->|No| Y[Check Target Type]
        
        X --> Y
        M --> Y
        Y --> Z{Target is Database?}
        Z -->|Yes| AA[Add Collection - T1005]
        Z -->|No| BB{Target is Critical System?}
        BB -->|Yes| CC[Add Impact - T1485]
        BB -->|No| DD[Add Impact - T1499]
        
        AA --> EE[Check Data Exfiltration]
        CC --> EE
        DD --> EE
        EE --> FF{Data Exfiltration Likely?}
        FF -->|Yes| GG[Add Exfiltration - T1041]
        FF -->|No| HH[Finalize Mapping]
        
        GG --> HH
        HH --> II[Generate Mitigation Recommendations]
        II --> JJ[Generate Detection Methods]
        JJ --> KK[Return MITRE Mapping]
        KK --> LL[End]

Risk Scoring Decision Flow
--------------------------

The comprehensive risk scoring algorithm:

.. mermaid::

    flowchart TD
        A[Start Risk Scoring] --> B[Initialize Score Components]
        B --> C[Calculate Asset Risk Component]
        C --> D[Get Source Asset Risk Score]
        D --> E[Get Target Asset Risk Score]
        E --> F[Calculate Path Asset Scores]
        F --> G[Apply Distance Weighting]
        G --> H[Asset Risk = Weighted Average]
        
        H --> I[Calculate Path Complexity Component]
        I --> J[Path Length Factor = Length * 0.1]
        J --> K[Relationship Complexity Factor]
        K --> L[Protocol Security Factor]
        L --> M[Complexity Score = Combined Factors]
        
        M --> N[Calculate Security Controls Component]
        N --> O[For Each Path Edge]
        O --> P{Encrypted Connection?}
        P -->|Yes| Q[Apply Encryption Bonus]
        P -->|No| R{Authenticated Connection?}
        
        Q --> R
        R -->|Yes| S[Apply Authentication Bonus]
        R -->|No| T{Monitored Connection?}
        
        S --> T
        T -->|Yes| U[Apply Monitoring Bonus]
        T -->|No| V[Continue to Next Edge]
        
        U --> V
        V --> W{More Edges?}
        W -->|Yes| O
        W -->|No| X[Calculate Controls Score]
        
        X --> Y[Calculate Business Impact Component]
        Y --> Z[Get Target Business Criticality]
        Z --> AA{Critical Asset?}
        AA -->|Yes| BB[Impact Multiplier = 2.0]
        AA -->|No| CC{High Value Asset?}
        CC -->|Yes| DD[Impact Multiplier = 1.5]
        CC -->|No| EE{Medium Value Asset?}
        EE -->|Yes| FF[Impact Multiplier = 1.0]
        EE -->|No| GG[Impact Multiplier = 0.5]
        
        BB --> HH[Combine All Components]
        DD --> HH
        FF --> HH
        GG --> HH
        
        HH --> II[Risk Score = Asset Risk * 0.4]
        II --> JJ[+ Complexity * 0.2]
        JJ --> KK[+ Security Controls * 0.3]
        KK --> LL[+ Business Impact * 0.1]
        LL --> MM[Normalize to 0-100 Scale]
        MM --> NN[Return Risk Score]
        NN --> OO[End]

Caching Strategy Decision Flow
------------------------------

The intelligent caching system workflow:

.. mermaid::

    flowchart TD
        A[Receive Analysis Request] --> B[Generate Cache Key]
        B --> C[Check L1 Cache - Memory]
        C --> D{Cache Hit?}
        D -->|Yes| E[Update Access Time]
        E --> F[Return Cached Result]
        
        D -->|No| G[Check L2 Cache - Redis]
        G --> H{Cache Hit?}
        H -->|Yes| I[Load into L1 Cache]
        I --> J[Return Cached Result]
        
        H -->|No| K[Execute Analysis]
        K --> L[Generate Results]
        L --> M[Calculate Result Size]
        M --> N{Size < Cache Limit?}
        N -->|No| O[Return Results Without Caching]
        N -->|Yes| P[Store in L1 Cache]
        P --> Q[Store in L2 Cache]
        Q --> R[Set TTL Based on Analysis Type]
        R --> S{Attack Path Analysis?}
        S -->|Yes| T[TTL = 1 Hour]
        S -->|No| U{Blast Radius Analysis?}
        U -->|Yes| V[TTL = 30 Minutes]
        U -->|No| W[TTL = 15 Minutes]
        
        T --> X[Return Results]
        V --> X
        W --> X
        F --> Y[End]
        J --> Y
        O --> Y
        X --> Y

Error Handling and Recovery Flow
--------------------------------

Comprehensive error handling workflow:

.. mermaid::

    flowchart TD
        A[Analysis Request Received] --> B[Validate Input Parameters]
        B --> C{Parameters Valid?}
        C -->|No| D[Return 400 Bad Request]
        C -->|Yes| E[Check Authentication]
        E --> F{User Authenticated?}
        F -->|No| G[Return 401 Unauthorized]
        F -->|Yes| H[Check Authorization]
        H --> I{User Authorized?}
        I -->|No| J[Return 403 Forbidden]
        I -->|Yes| K[Check Rate Limits]
        K --> L{Within Rate Limit?}
        L -->|No| M[Return 429 Too Many Requests]
        L -->|Yes| N[Start Analysis]
        
        N --> O[Try Analysis Execution]
        O --> P{Analysis Successful?}
        P -->|Yes| Q[Return Results]
        P -->|No| R[Analyze Error Type]
        
        R --> S{Timeout Error?}
        S -->|Yes| T[Return 504 Gateway Timeout]
        S -->|No| U{Resource Not Found?}
        U -->|Yes| V[Return 404 Not Found]
        U -->|No| W{Database Error?}
        W -->|Yes| X[Log Database Error]
        X --> Y[Return 500 Internal Server Error]
        W -->|No| Z{Graph Processing Error?}
        Z -->|Yes| AA[Log Graph Error]
        AA --> BB[Return 422 Unprocessable Entity]
        Z -->|No| CC[Log Unknown Error]
        CC --> DD[Return 500 Internal Server Error]
        
        D --> EE[Log Request Error]
        G --> EE
        J --> EE
        M --> EE
        T --> EE
        V --> EE
        Y --> EE
        BB --> EE
        DD --> EE
        Q --> FF[Log Successful Request]
        
        EE --> GG[Update Error Metrics]
        FF --> HH[Update Success Metrics]
        GG --> II[End]
        HH --> II

Performance Optimization Flow
-----------------------------

The performance optimization decision process:

.. mermaid::

    flowchart TD
        A[Monitor Analysis Performance] --> B[Check Response Time]
        B --> C{Response Time > Threshold?}
        C -->|No| D[Continue Monitoring]
        C -->|Yes| E[Analyze Performance Bottleneck]
        
        E --> F{High CPU Usage?}
        F -->|Yes| G[Check Graph Size]
        G --> H{Graph Too Large?}
        H -->|Yes| I[Implement Graph Partitioning]
        H -->|No| J[Increase Worker Threads]
        
        F -->|No| K{High Memory Usage?}
        K -->|Yes| L[Check Cache Size]
        L --> M{Cache Too Large?}
        M -->|Yes| N[Reduce Cache Size]
        M -->|No| O[Optimize Graph Storage]
        
        K -->|No| P{High Database Load?}
        P -->|Yes| Q[Check Query Performance]
        Q --> R{Slow Queries Detected?}
        R -->|Yes| S[Optimize Database Indexes]
        R -->|No| T[Implement Connection Pooling]
        
        P -->|No| U{High Network Latency?}
        U -->|Yes| V[Implement Result Compression]
        U -->|No| W[Profile Application Code]
        
        I --> X[Test Performance Improvement]
        J --> X
        N --> X
        O --> X
        S --> X
        T --> X
        V --> X
        W --> X
        
        X --> Y{Performance Improved?}
        Y -->|Yes| Z[Deploy Optimization]
        Y -->|No| AA[Try Alternative Optimization]
        
        Z --> D
        AA --> E
        D --> BB[End Monitoring Cycle]

These comprehensive flow diagrams provide detailed visualization of all major processes in the attack path analysis engine, from initial request processing through error handling and performance optimization.
