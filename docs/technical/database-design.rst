Database Design and Schema
==========================

This document provides comprehensive documentation of the Blast-Radius Security Tool database design, including entity relationships, schema definitions, and optimization strategies.

Overview
--------

The database is designed using PostgreSQL with a focus on:

* **Scalability**: Support for enterprise-scale asset inventories
* **Performance**: Optimized for real-time queries and analysis
* **Audit Compliance**: Complete audit trails for regulatory compliance
* **Data Integrity**: Comprehensive constraints and validation
* **Flexibility**: Extensible schema for diverse asset types

Database Architecture
---------------------

.. mermaid::

    graph TB
        subgraph "Core Tables"
            Users[Users]
            Assets[Assets]
            AssetTypes[Asset Types]
            Providers[Providers]
            Environments[Environments]
        end
        
        subgraph "Relationship Tables"
            AssetRelationships[Asset Relationships]
            AssetDependencies[Asset Dependencies]
            AssetCommunications[Asset Communications]
        end
        
        subgraph "Extended Asset Data"
            AssetVulnerabilities[Asset Vulnerabilities]
            AssetCompliance[Asset Compliance]
            AssetMetrics[Asset Metrics]
            AssetConfigurations[Asset Configurations]
        end
        
        subgraph "Discovery & Analysis"
            DiscoveryJobs[Discovery Jobs]
            DiscoverySources[Discovery Sources]
            AttackPaths[Attack Paths]
            BlastRadius[Blast Radius Results]
        end
        
        subgraph "Audit & Security"
            AuditLogs[Audit Logs]
            UserSessions[User Sessions]
            APIKeys[API Keys]
            DataRetention[Data Retention Policies]
        end
        
        Users --> Assets
        Assets --> AssetTypes
        Assets --> Providers
        Assets --> Environments
        Assets --> AssetRelationships
        Assets --> AssetVulnerabilities
        Assets --> AssetCompliance
        Assets --> AssetMetrics
        Assets --> AssetConfigurations
        Assets --> AttackPaths
        DiscoveryJobs --> Assets
        DiscoverySources --> DiscoveryJobs
        Users --> AuditLogs
        Users --> UserSessions
        Users --> APIKeys

Core Entity Relationship Diagram
---------------------------------

.. mermaid::

    erDiagram
        USERS {
            uuid id PK
            string username UK
            string email UK
            string password_hash
            string first_name
            string last_name
            enum role
            boolean is_active
            boolean is_verified
            timestamp created_at
            timestamp updated_at
            timestamp last_login
        }
        
        ASSETS {
            uuid id PK
            string name
            enum asset_type FK
            enum provider FK
            string environment FK
            string ip_addresses
            jsonb configuration
            jsonb properties
            float risk_score
            enum status
            boolean is_deleted
            timestamp deleted_at
            uuid created_by FK
            timestamp created_at
            timestamp updated_at
        }
        
        ASSET_TYPES {
            string value PK
            string display_name
            string description
            jsonb default_properties
            boolean is_active
        }
        
        PROVIDERS {
            string value PK
            string display_name
            string description
            jsonb configuration_schema
            boolean is_active
        }
        
        ENVIRONMENTS {
            string name PK
            string description
            enum criticality_level
            jsonb configuration
            boolean is_active
        }
        
        ASSET_RELATIONSHIPS {
            uuid id PK
            uuid source_asset_id FK
            uuid target_asset_id FK
            enum relationship_type
            string protocol
            integer port
            enum direction
            string description
            float confidence
            boolean is_active
            timestamp created_at
            timestamp updated_at
        }
        
        ASSET_VULNERABILITIES {
            uuid id PK
            uuid asset_id FK
            string vulnerability_id
            string title
            text description
            enum severity
            float cvss_score
            string cve_id
            jsonb details
            enum status
            timestamp discovered_at
            timestamp updated_at
        }
        
        ASSET_COMPLIANCE {
            uuid id PK
            uuid asset_id FK
            string framework
            string control_id
            string control_name
            enum compliance_status
            text findings
            timestamp assessed_at
            timestamp next_assessment
        }
        
        DISCOVERY_JOBS {
            uuid id PK
            string name
            enum job_type
            jsonb configuration
            enum status
            timestamp started_at
            timestamp completed_at
            integer assets_discovered
            integer assets_updated
            text error_message
            uuid created_by FK
        }
        
        AUDIT_LOGS {
            uuid id PK
            uuid user_id FK
            string action
            string resource_type
            uuid resource_id
            jsonb old_values
            jsonb new_values
            string ip_address
            string user_agent
            timestamp created_at
        }
        
        USERS ||--o{ ASSETS : creates
        USERS ||--o{ DISCOVERY_JOBS : initiates
        USERS ||--o{ AUDIT_LOGS : generates
        ASSETS ||--o{ ASSET_RELATIONSHIPS : source
        ASSETS ||--o{ ASSET_RELATIONSHIPS : target
        ASSETS ||--o{ ASSET_VULNERABILITIES : has
        ASSETS ||--o{ ASSET_COMPLIANCE : assessed
        ASSETS }o--|| ASSET_TYPES : classified_as
        ASSETS }o--|| PROVIDERS : hosted_on
        ASSETS }o--|| ENVIRONMENTS : deployed_in

Attack Path Analysis Schema
---------------------------

.. mermaid::

    erDiagram
        ATTACK_PATHS {
            uuid id PK
            string path_id UK
            uuid source_asset_id FK
            uuid target_asset_id FK
            jsonb path_nodes
            jsonb path_edges
            enum path_type
            jsonb attack_techniques
            float risk_score
            float likelihood
            float impact_score
            integer blast_radius
            integer estimated_time
            jsonb required_privileges
            float detection_difficulty
            float mitigation_cost
            integer path_length
            float criticality_score
            timestamp created_at
            timestamp updated_at
        }
        
        ATTACK_SCENARIOS {
            uuid id PK
            string scenario_id UK
            string name
            text description
            string threat_actor
            jsonb entry_points
            jsonb objectives
            float total_risk_score
            float likelihood
            float impact_score
            integer estimated_duration
            jsonb required_resources
            float detection_probability
            jsonb mitigation_strategies
            enum criticality_level
            uuid created_by FK
            timestamp created_at
            timestamp updated_at
        }
        
        SCENARIO_PATHS {
            uuid id PK
            uuid scenario_id FK
            uuid attack_path_id FK
            integer sequence_order
            timestamp created_at
        }
        
        BLAST_RADIUS_RESULTS {
            uuid id PK
            uuid source_asset_id FK
            jsonb affected_assets
            jsonb impact_by_degree
            float total_impact_score
            jsonb critical_assets_affected
            jsonb data_assets_affected
            float service_disruption_score
            float financial_impact
            jsonb compliance_impact
            integer recovery_time_estimate
            integer max_degrees
            timestamp calculated_at
            timestamp expires_at
        }
        
        MITRE_ATTACK_MAPPINGS {
            uuid id PK
            string technique_id UK
            string technique_name
            string tactic
            jsonb platforms
            jsonb data_sources
            jsonb mitigations
            jsonb detection_methods
            text description
            boolean is_active
        }
        
        ATTACK_PATHS }o--|| ASSETS : source
        ATTACK_PATHS }o--|| ASSETS : target
        ATTACK_SCENARIOS ||--o{ SCENARIO_PATHS : contains
        SCENARIO_PATHS }o--|| ATTACK_PATHS : includes
        BLAST_RADIUS_RESULTS }o--|| ASSETS : originates_from
        ATTACK_SCENARIOS }o--|| USERS : created_by

Table Definitions
-----------------

Core Tables
~~~~~~~~~~~

**Users Table**

.. code-block:: sql

    CREATE TABLE users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        role user_role_enum NOT NULL DEFAULT 'analyst',
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_verified BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP WITH TIME ZONE,
        
        CONSTRAINT users_username_length CHECK (length(username) >= 3),
        CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
    );

**Assets Table**

.. code-block:: sql

    CREATE TABLE assets (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        asset_type asset_type_enum NOT NULL,
        provider provider_enum NOT NULL,
        environment VARCHAR(50) NOT NULL,
        ip_addresses INET[],
        configuration JSONB DEFAULT '{}',
        properties JSONB DEFAULT '{}',
        risk_score DECIMAL(5,2) DEFAULT 0.0,
        status asset_status_enum NOT NULL DEFAULT 'active',
        is_deleted BOOLEAN NOT NULL DEFAULT false,
        deleted_at TIMESTAMP WITH TIME ZONE,
        created_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT assets_risk_score_range CHECK (risk_score >= 0 AND risk_score <= 100),
        CONSTRAINT assets_soft_delete CHECK (
            (is_deleted = false AND deleted_at IS NULL) OR 
            (is_deleted = true AND deleted_at IS NOT NULL)
        )
    );

**Asset Relationships Table**

.. code-block:: sql

    CREATE TABLE asset_relationships (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        source_asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
        target_asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
        relationship_type relationship_type_enum NOT NULL,
        protocol VARCHAR(20),
        port INTEGER,
        direction direction_enum NOT NULL DEFAULT 'outbound',
        description TEXT,
        confidence DECIMAL(3,2) DEFAULT 1.0,
        is_active BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT asset_relationships_no_self_reference CHECK (source_asset_id != target_asset_id),
        CONSTRAINT asset_relationships_confidence_range CHECK (confidence >= 0 AND confidence <= 1),
        CONSTRAINT asset_relationships_port_range CHECK (port IS NULL OR (port >= 1 AND port <= 65535)),
        UNIQUE(source_asset_id, target_asset_id, relationship_type, protocol, port)
    );

Attack Path Analysis Tables
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Attack Paths Table**

.. code-block:: sql

    CREATE TABLE attack_paths (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        path_id VARCHAR(255) UNIQUE NOT NULL,
        source_asset_id UUID NOT NULL REFERENCES assets(id),
        target_asset_id UUID NOT NULL REFERENCES assets(id),
        path_nodes JSONB NOT NULL,
        path_edges JSONB NOT NULL,
        path_type path_type_enum NOT NULL,
        attack_techniques JSONB NOT NULL DEFAULT '[]',
        risk_score DECIMAL(5,2) NOT NULL,
        likelihood DECIMAL(3,2) NOT NULL,
        impact_score DECIMAL(5,2) NOT NULL,
        blast_radius INTEGER NOT NULL DEFAULT 0,
        estimated_time INTEGER NOT NULL DEFAULT 0,
        required_privileges JSONB NOT NULL DEFAULT '[]',
        detection_difficulty DECIMAL(3,2) NOT NULL DEFAULT 0.5,
        mitigation_cost DECIMAL(10,2) NOT NULL DEFAULT 0.0,
        path_length INTEGER NOT NULL,
        criticality_score DECIMAL(5,2) GENERATED ALWAYS AS (
            (risk_score * 0.4 + impact_score * 0.4 + (100 - detection_difficulty * 100) * 0.2)
        ) STORED,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT attack_paths_risk_score_range CHECK (risk_score >= 0 AND risk_score <= 100),
        CONSTRAINT attack_paths_likelihood_range CHECK (likelihood >= 0 AND likelihood <= 1),
        CONSTRAINT attack_paths_impact_score_range CHECK (impact_score >= 0 AND impact_score <= 100),
        CONSTRAINT attack_paths_detection_difficulty_range CHECK (detection_difficulty >= 0 AND detection_difficulty <= 1),
        CONSTRAINT attack_paths_path_length_positive CHECK (path_length > 0)
    );

**Attack Scenarios Table**

.. code-block:: sql

    CREATE TABLE attack_scenarios (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        scenario_id VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        threat_actor VARCHAR(255) NOT NULL,
        entry_points JSONB NOT NULL,
        objectives JSONB NOT NULL,
        total_risk_score DECIMAL(5,2) NOT NULL,
        likelihood DECIMAL(3,2) NOT NULL,
        impact_score DECIMAL(5,2) NOT NULL,
        estimated_duration INTEGER NOT NULL DEFAULT 0,
        required_resources JSONB NOT NULL DEFAULT '[]',
        detection_probability DECIMAL(3,2) NOT NULL DEFAULT 0.5,
        mitigation_strategies JSONB NOT NULL DEFAULT '[]',
        criticality_level criticality_level_enum GENERATED ALWAYS AS (
            CASE 
                WHEN total_risk_score >= 80 THEN 'CRITICAL'
                WHEN total_risk_score >= 60 THEN 'HIGH'
                WHEN total_risk_score >= 40 THEN 'MEDIUM'
                ELSE 'LOW'
            END
        ) STORED,
        created_by UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT attack_scenarios_risk_score_range CHECK (total_risk_score >= 0 AND total_risk_score <= 100),
        CONSTRAINT attack_scenarios_likelihood_range CHECK (likelihood >= 0 AND likelihood <= 1),
        CONSTRAINT attack_scenarios_impact_score_range CHECK (impact_score >= 0 AND impact_score <= 100),
        CONSTRAINT attack_scenarios_detection_probability_range CHECK (detection_probability >= 0 AND detection_probability <= 1)
    );

Enumeration Types
-----------------

**Asset Types**

.. code-block:: sql

    CREATE TYPE asset_type_enum AS ENUM (
        'server', 'database', 'workstation', 'network_device', 'security_device',
        'storage', 'application', 'container', 'virtual_machine', 'cloud_service',
        'iot_device', 'mobile_device', 'printer', 'scanner', 'phone_system',
        'building_system', 'industrial_control', 'medical_device', 'vehicle',
        'software', 'firmware', 'operating_system', 'middleware', 'api',
        'web_service', 'microservice', 'lambda_function', 'kubernetes_pod',
        'docker_container', 'load_balancer', 'firewall', 'ids_ips', 'waf',
        'proxy', 'vpn', 'router', 'switch', 'wireless_access_point',
        'domain_controller', 'certificate_authority', 'backup_system',
        'monitoring_system', 'logging_system', 'siem', 'soar', 'vulnerability_scanner',
        'patch_management', 'endpoint_protection', 'email_security',
        'web_security', 'cloud_security', 'identity_provider', 'mfa_system',
        'privileged_access', 'secrets_management', 'encryption_system',
        'key_management', 'data_loss_prevention', 'threat_intelligence',
        'sandbox', 'deception_technology', 'network_segmentation', 'zero_trust'
    );

**Relationship Types**

.. code-block:: sql

    CREATE TYPE relationship_type_enum AS ENUM (
        'communicates_with', 'depends_on', 'manages', 'accesses', 'hosts',
        'contains', 'monitors', 'protects', 'authenticates', 'authorizes',
        'encrypts', 'backs_up', 'replicates', 'load_balances', 'proxies',
        'routes', 'switches', 'bridges', 'tunnels', 'vpn_connects',
        'federates', 'trusts', 'delegates', 'inherits', 'extends'
    );

**Attack Path Types**

.. code-block:: sql

    CREATE TYPE path_type_enum AS ENUM (
        'direct', 'lateral_movement', 'privilege_escalation', 'data_exfiltration',
        'supply_chain', 'insider_threat'
    );

Indexes and Performance Optimization
------------------------------------

**Primary Indexes**

.. code-block:: sql

    -- Core asset indexes
    CREATE INDEX idx_assets_type_provider ON assets(asset_type, provider);
    CREATE INDEX idx_assets_environment_status ON assets(environment, status);
    CREATE INDEX idx_assets_risk_score ON assets(risk_score DESC);
    CREATE INDEX idx_assets_created_at ON assets(created_at DESC);
    CREATE INDEX idx_assets_soft_delete ON assets(is_deleted, deleted_at);
    
    -- Asset relationship indexes
    CREATE INDEX idx_asset_relationships_source ON asset_relationships(source_asset_id);
    CREATE INDEX idx_asset_relationships_target ON asset_relationships(target_asset_id);
    CREATE INDEX idx_asset_relationships_type ON asset_relationships(relationship_type);
    CREATE INDEX idx_asset_relationships_active ON asset_relationships(is_active);
    
    -- Attack path indexes
    CREATE INDEX idx_attack_paths_source_target ON attack_paths(source_asset_id, target_asset_id);
    CREATE INDEX idx_attack_paths_risk_score ON attack_paths(risk_score DESC);
    CREATE INDEX idx_attack_paths_criticality ON attack_paths(criticality_score DESC);
    CREATE INDEX idx_attack_paths_path_type ON attack_paths(path_type);
    CREATE INDEX idx_attack_paths_created_at ON attack_paths(created_at DESC);

**JSONB Indexes**

.. code-block:: sql

    -- Asset configuration indexes
    CREATE INDEX idx_assets_config_gin ON assets USING GIN(configuration);
    CREATE INDEX idx_assets_properties_gin ON assets USING GIN(properties);
    
    -- Attack path technique indexes
    CREATE INDEX idx_attack_paths_techniques_gin ON attack_paths USING GIN(attack_techniques);
    CREATE INDEX idx_attack_paths_nodes_gin ON attack_paths USING GIN(path_nodes);
    
    -- Attack scenario indexes
    CREATE INDEX idx_attack_scenarios_entry_points_gin ON attack_scenarios USING GIN(entry_points);
    CREATE INDEX idx_attack_scenarios_objectives_gin ON attack_scenarios USING GIN(objectives);

**Composite Indexes**

.. code-block:: sql

    -- Multi-column indexes for common queries
    CREATE INDEX idx_assets_type_env_status ON assets(asset_type, environment, status) 
        WHERE is_deleted = false;
    
    CREATE INDEX idx_relationships_source_type_active ON asset_relationships(source_asset_id, relationship_type, is_active);
    
    CREATE INDEX idx_attack_paths_source_risk_criticality ON attack_paths(source_asset_id, risk_score DESC, criticality_score DESC);

Data Retention and Archival
----------------------------

**Soft Delete Implementation**

.. code-block:: sql

    -- Soft delete trigger function
    CREATE OR REPLACE FUNCTION soft_delete_asset()
    RETURNS TRIGGER AS $$
    BEGIN
        IF NEW.is_deleted = true AND OLD.is_deleted = false THEN
            NEW.deleted_at = CURRENT_TIMESTAMP;
        ELSIF NEW.is_deleted = false AND OLD.is_deleted = true THEN
            NEW.deleted_at = NULL;
        END IF;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    
    -- Apply soft delete trigger
    CREATE TRIGGER trigger_soft_delete_asset
        BEFORE UPDATE ON assets
        FOR EACH ROW
        EXECUTE FUNCTION soft_delete_asset();

**Data Retention Policies**

.. code-block:: sql

    -- Audit log retention (90 days)
    CREATE OR REPLACE FUNCTION cleanup_audit_logs()
    RETURNS void AS $$
    BEGIN
        DELETE FROM audit_logs 
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    END;
    $$ LANGUAGE plpgsql;
    
    -- Attack path cache cleanup (30 days)
    CREATE OR REPLACE FUNCTION cleanup_attack_paths()
    RETURNS void AS $$
    BEGIN
        DELETE FROM attack_paths 
        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days'
        AND path_id NOT IN (
            SELECT DISTINCT unnest(string_to_array(path_nodes::text, ','))
            FROM attack_scenarios
            WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '7 days'
        );
    END;
    $$ LANGUAGE plpgsql;

Database Maintenance
--------------------

**Vacuum and Analyze Schedule**

.. code-block:: sql

    -- Automated maintenance procedures
    CREATE OR REPLACE FUNCTION maintenance_vacuum_analyze()
    RETURNS void AS $$
    BEGIN
        -- Vacuum and analyze high-traffic tables
        VACUUM ANALYZE assets;
        VACUUM ANALYZE asset_relationships;
        VACUUM ANALYZE attack_paths;
        VACUUM ANALYZE audit_logs;
        
        -- Update table statistics
        ANALYZE assets;
        ANALYZE asset_relationships;
        ANALYZE attack_paths;
        ANALYZE attack_scenarios;
    END;
    $$ LANGUAGE plpgsql;

**Index Maintenance**

.. code-block:: sql

    -- Reindex heavily used indexes
    CREATE OR REPLACE FUNCTION maintenance_reindex()
    RETURNS void AS $$
    BEGIN
        REINDEX INDEX CONCURRENTLY idx_assets_type_provider;
        REINDEX INDEX CONCURRENTLY idx_asset_relationships_source;
        REINDEX INDEX CONCURRENTLY idx_attack_paths_source_target;
        REINDEX INDEX CONCURRENTLY idx_attack_paths_criticality;
    END;
    $$ LANGUAGE plpgsql;

Performance Monitoring
----------------------

**Query Performance Views**

.. code-block:: sql

    -- Slow query monitoring
    CREATE VIEW slow_queries AS
    SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
    FROM pg_stat_statements
    WHERE mean_time > 100  -- Queries taking more than 100ms on average
    ORDER BY mean_time DESC;
    
    -- Table size monitoring
    CREATE VIEW table_sizes AS
    SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
        pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
    FROM pg_tables
    WHERE schemaname = 'public'
    ORDER BY size_bytes DESC;

This comprehensive database design provides a robust foundation for the Blast-Radius Security Tool with optimized performance, comprehensive audit trails, and scalable architecture for enterprise deployments.
