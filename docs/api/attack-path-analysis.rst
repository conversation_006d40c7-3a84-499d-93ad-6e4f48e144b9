Attack Path Analysis API
========================

The Attack Path Analysis API provides comprehensive graph-based security analysis capabilities, including attack path discovery, blast radius calculation, and MITRE ATT&CK framework integration.

Overview
--------

The attack path analysis engine uses advanced graph algorithms to:

* Discover multi-hop attack paths through your infrastructure
* Calculate blast radius from compromised assets
* Model complex attack scenarios with threat actor profiling
* Integrate with MITRE ATT&CK framework for standardized threat modeling
* Provide risk scoring and mitigation recommendations

Base URL
--------

All attack path analysis endpoints are available under::

    /api/v1/attack-paths/

Authentication
--------------

All endpoints require authentication using Bearer tokens::

    Authorization: Bearer <your-jwt-token>

Endpoints
---------

Analyze Attack Paths
~~~~~~~~~~~~~~~~~~~~~

**POST** ``/api/v1/attack-paths/analyze``

Discover attack paths from source to target assets.

**Request Body:**

.. code-block:: json

    {
        "source_asset_id": "web_server_001",
        "target_asset_ids": ["database_001", "backup_server_001"],
        "max_path_length": 5,
        "max_paths_per_target": 3
    }

**Parameters:**

* ``source_asset_id`` (string, required): Source asset ID for attack path analysis
* ``target_asset_ids`` (array, optional): Target asset IDs. If null, analyzes all high-value targets
* ``max_path_length`` (integer, optional): Maximum path length to analyze (1-10, default: 5)
* ``max_paths_per_target`` (integer, optional): Maximum paths per target (1-20, default: 5)

**Response:**

.. code-block:: json

    [
        {
            "path_id": "path_001_web_server_001_database_001",
            "source_asset_id": "web_server_001",
            "target_asset_id": "database_001",
            "path_nodes": ["web_server_001", "app_server_001", "database_001"],
            "path_type": "lateral_movement",
            "attack_techniques": ["initial_access", "lateral_movement", "impact"],
            "risk_score": 85.5,
            "likelihood": 0.75,
            "impact_score": 92.0,
            "blast_radius": 15,
            "estimated_time": 120,
            "required_privileges": ["user", "admin"],
            "detection_difficulty": 0.6,
            "mitigation_cost": 15000.0,
            "path_length": 3,
            "criticality_score": 87.2,
            "criticality_level": "HIGH"
        }
    ]

Calculate Blast Radius
~~~~~~~~~~~~~~~~~~~~~~~

**POST** ``/api/v1/attack-paths/blast-radius``

Calculate the blast radius and impact from a compromised asset.

**Request Body:**

.. code-block:: json

    {
        "source_asset_id": "app_server_001",
        "max_degrees": 5
    }

**Parameters:**

* ``source_asset_id`` (string, required): Source asset ID for blast radius calculation
* ``max_degrees`` (integer, optional): Maximum degrees of separation (1-10, default: 5)

**Response:**

.. code-block:: json

    {
        "source_asset_id": "app_server_001",
        "affected_assets": ["app_server_001", "database_001", "backup_server_001", "web_server_001"],
        "impact_by_degree": {
            "0": ["app_server_001"],
            "1": ["database_001", "web_server_001"],
            "2": ["backup_server_001"]
        },
        "total_impact_score": 285.5,
        "critical_assets_affected": ["database_001"],
        "data_assets_affected": ["database_001", "backup_server_001"],
        "service_disruption_score": 75.0,
        "financial_impact": 125000.0,
        "compliance_impact": ["GDPR", "SOX"],
        "recovery_time_estimate": 24
    }

Create Attack Scenario
~~~~~~~~~~~~~~~~~~~~~~~

**POST** ``/api/v1/attack-paths/scenarios``

Create and analyze a comprehensive attack scenario.

**Request Body:**

.. code-block:: json

    {
        "scenario_name": "APT Financial Data Exfiltration",
        "threat_actor": "APT29 (Cozy Bear)",
        "entry_points": ["internet_gateway", "admin_workstation"],
        "objectives": ["database_001", "backup_server_001"],
        "description": "Advanced persistent threat targeting financial data"
    }

**Response:**

.. code-block:: json

    {
        "scenario_id": "scenario_1639234567",
        "name": "APT Financial Data Exfiltration",
        "description": "Advanced persistent threat targeting financial data",
        "threat_actor": "APT29 (Cozy Bear)",
        "attack_paths": [
            {
                "path_id": "path_001",
                "source_asset_id": "internet_gateway",
                "target_asset_id": "database_001",
                "risk_score": 88.5,
                "criticality_level": "CRITICAL"
            }
        ],
        "total_risk_score": 88.5,
        "likelihood": 0.8,
        "impact_score": 95.0,
        "estimated_duration": 6,
        "required_resources": ["network_access", "credential_harvesting_tools"],
        "detection_probability": 0.4,
        "mitigation_strategies": ["Network Segmentation", "Multi-factor Authentication"],
        "criticality_level": "CRITICAL",
        "created_at": "2024-12-11T22:30:00Z"
    }

Get Attack Scenario
~~~~~~~~~~~~~~~~~~~

**GET** ``/api/v1/attack-paths/scenarios/{scenario_id}``

Retrieve a specific attack scenario by ID.

**Parameters:**

* ``scenario_id`` (string, required): Scenario ID to retrieve

**Response:** Same as Create Attack Scenario response.

MITRE ATT&CK Mapping
~~~~~~~~~~~~~~~~~~~~~

**GET** ``/api/v1/attack-paths/mitre-mapping/{path_id}``

Get MITRE ATT&CK framework mapping for an attack path.

**Parameters:**

* ``path_id`` (string, required): Attack path ID

**Response:**

.. code-block:: json

    {
        "tactics": ["Initial Access", "Lateral Movement", "Impact"],
        "techniques": [
            {
                "id": "TA0001",
                "name": "Initial Access",
                "description": "Techniques used to gain an initial foothold within a network"
            },
            {
                "id": "TA0008",
                "name": "Lateral Movement",
                "description": "Techniques that adversaries use to enter and control remote systems"
            }
        ],
        "mitigations": [
            "Network Segmentation",
            "Multi-factor Authentication",
            "Privileged Account Management"
        ],
        "detection_methods": [
            "Network Monitoring",
            "Authentication Analysis",
            "Behavioral Analysis"
        ]
    }

Graph Statistics
~~~~~~~~~~~~~~~~

**GET** ``/api/v1/attack-paths/graph/statistics``

Get comprehensive graph statistics and performance metrics.

**Response:**

.. code-block:: json

    {
        "timestamp": "2024-12-11T22:30:00Z",
        "statistics": {
            "nodes": 1250,
            "edges": 3420,
            "density": 0.0044,
            "is_connected": true,
            "number_of_components": 1,
            "average_clustering": 0.15,
            "performance_metrics": {
                "total_nodes": 1250,
                "total_edges": 3420,
                "last_analysis_time": 0.85,
                "cache_hits": 145,
                "cache_misses": 23
            },
            "top_degree_centrality": [
                ["app_server_001", 0.25],
                ["database_001", 0.18]
            ]
        },
        "message": "Graph statistics retrieved successfully"
    }

Export Analysis Results
~~~~~~~~~~~~~~~~~~~~~~~

**GET** ``/api/v1/attack-paths/export``

Export analysis results in various formats.

**Query Parameters:**

* ``format`` (string, optional): Export format (default: "json")

**Response:**

.. code-block:: json

    {
        "format": "json",
        "data": "{\"scenarios\": [...], \"graph_statistics\": {...}}",
        "timestamp": "2024-12-11T22:30:00Z",
        "message": "Analysis results exported successfully"
    }

Refresh Graph Data
~~~~~~~~~~~~~~~~~~

**POST** ``/api/v1/attack-paths/graph/refresh``

Refresh graph data from the database (background task).

**Response:**

.. code-block:: json

    {
        "message": "Graph refresh initiated",
        "timestamp": "2024-12-11T22:30:00Z"
    }

Clear Analysis Cache
~~~~~~~~~~~~~~~~~~~~

**DELETE** ``/api/v1/attack-paths/cache``

Clear all analysis caches to free memory.

**Response:**

.. code-block:: json

    {
        "message": "Analysis cache cleared successfully",
        "timestamp": "2024-12-11T22:30:00Z"
    }

Error Responses
---------------

All endpoints return standard HTTP status codes and error responses:

**400 Bad Request:**

.. code-block:: json

    {
        "detail": "Invalid request parameters"
    }

**404 Not Found:**

.. code-block:: json

    {
        "detail": "Asset not found"
    }

**423 Locked:**

.. code-block:: json

    {
        "detail": "Asset is currently locked for analysis"
    }

**500 Internal Server Error:**

.. code-block:: json

    {
        "detail": "Attack path analysis failed: internal error"
    }

Rate Limiting
-------------

Attack path analysis endpoints are rate limited to prevent abuse:

* **Standard users**: 10 requests per minute
* **Premium users**: 60 requests per minute
* **Enterprise users**: 300 requests per minute

Rate limit headers are included in responses::

    X-RateLimit-Limit: 60
    X-RateLimit-Remaining: 45
    X-RateLimit-Reset: 1639234567

Performance Considerations
--------------------------

* **Path Analysis**: Typical response time <1 second for graphs with <1000 assets
* **Blast Radius**: Response time scales with max_degrees parameter
* **Large Graphs**: Consider using smaller max_path_length for better performance
* **Caching**: Repeated queries benefit from intelligent caching system

Best Practices
--------------

1. **Start Small**: Begin with smaller max_path_length values and increase as needed
2. **Use Caching**: Leverage the caching system for repeated analysis
3. **Monitor Performance**: Use graph statistics endpoint to monitor performance
4. **Batch Operations**: Group related analysis requests when possible
5. **Clear Cache**: Periodically clear cache to free memory in long-running systems

Examples
--------

**Python Example:**

.. code-block:: python

    import requests

    # Analyze attack paths
    response = requests.post(
        "https://api.blast-radius.com/api/v1/attack-paths/analyze",
        headers={"Authorization": "Bearer your-token"},
        json={
            "source_asset_id": "web_server_001",
            "target_asset_ids": ["database_001"],
            "max_path_length": 5
        }
    )
    
    paths = response.json()
    for path in paths:
        print(f"Path: {' → '.join(path['path_nodes'])}")
        print(f"Risk Score: {path['risk_score']}")

**cURL Example:**

.. code-block:: bash

    curl -X POST "https://api.blast-radius.com/api/v1/attack-paths/blast-radius" \
      -H "Authorization: Bearer your-token" \
      -H "Content-Type: application/json" \
      -d '{
        "source_asset_id": "compromised_server",
        "max_degrees": 3
      }'
