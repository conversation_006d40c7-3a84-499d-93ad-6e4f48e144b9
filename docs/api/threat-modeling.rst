Threat Modeling API Reference
==============================

This document provides comprehensive API reference for the threat modeling and quantitative risk assessment features of the Blast-Radius Security Tool.

.. note::
   The Threat Modeling API provides enterprise-grade threat analysis capabilities with 
   statistical modeling, threat actor simulation, and regulatory compliance assessment.

Overview
--------

The Threat Modeling API enables:

* **Threat Actor Simulation**: Model attacks from known threat actors
* **Quantitative Risk Assessment**: Mathematical risk calculation with business impact
* **Attack Success Probability**: Statistical modeling of attack likelihood
* **Financial Impact Assessment**: Monetary loss calculation and recovery costs
* **Compliance Impact Analysis**: Regulatory violation identification
* **Mitigation Strategy Generation**: AI-driven security control recommendations

Base URL
--------

All API endpoints are relative to the base URL:

.. code-block:: text

    https://api.blast-radius.com/api/v1/threat-modeling

Authentication
--------------

All requests require authentication using Bearer tokens:

.. code-block:: bash

    curl -H "Authorization: Bearer YOUR_API_TOKEN" \
         -H "Content-Type: application/json" \
         https://api.blast-radius.com/api/v1/threat-modeling/

Threat Actor Simulation
-----------------------

Simulate Attack
~~~~~~~~~~~~~~

Simulate an attack from a specific threat actor against target assets.

.. http:post:: /simulate

   **Request Body:**

   .. code-block:: json

      {
        "threat_actor_id": "APT29",
        "target_assets": ["database_001", "web_server_001"],
        "scenario_name": "Q4 Risk Assessment",
        "simulation_parameters": {
          "max_attack_duration_hours": 168,
          "include_insider_threat": true,
          "consider_supply_chain": true,
          "regulatory_context": ["GDPR", "SOX"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "simulation_id": "sim_12345",
        "threat_actor_id": "APT29",
        "scenario_name": "Q4 Risk Assessment",
        "target_assets": ["database_001", "web_server_001"],
        "success_probability": 0.75,
        "detection_probability": 0.35,
        "estimated_time_to_compromise": 48.5,
        "financial_impact": 2500000.0,
        "compliance_impact": {
          "affected_regulations": ["GDPR", "SOX"],
          "violation_probability": 0.8,
          "potential_fines": 5000000.0
        },
        "attack_paths": [
          {
            "path_id": "path_001",
            "source_asset": "internet",
            "target_asset": "database_001",
            "attack_techniques": ["T1566", "T1078", "T1055"],
            "success_probability": 0.7,
            "detection_probability": 0.4
          }
        ],
        "created_at": "2024-01-15T10:30:00Z"
      }

   :statuscode 200: Simulation completed successfully
   :statuscode 400: Invalid request parameters
   :statuscode 401: Authentication required
   :statuscode 404: Threat actor or assets not found
   :statuscode 500: Internal server error

Get Simulation Results
~~~~~~~~~~~~~~~~~~~~~

Retrieve results from a previously run simulation.

.. http:get:: /simulations/(simulation_id)

   **Response:**

   .. code-block:: json

      {
        "simulation_id": "sim_12345",
        "status": "completed",
        "threat_actor_id": "APT29",
        "scenario_name": "Q4 Risk Assessment",
        "results": {
          "success_probability": 0.75,
          "detection_probability": 0.35,
          "estimated_time_to_compromise": 48.5,
          "financial_impact": 2500000.0,
          "detailed_analysis": {
            "entry_points": ["web_server_001"],
            "attack_vectors": ["phishing", "credential_stuffing"],
            "critical_vulnerabilities": ["CVE-2023-1234"],
            "security_gaps": ["insufficient_mfa", "weak_monitoring"]
          }
        }
      }

   :statuscode 200: Simulation results retrieved
   :statuscode 404: Simulation not found

List Threat Actors
~~~~~~~~~~~~~~~~~~

Get available threat actor profiles.

.. http:get:: /threat-actors

   **Query Parameters:**

   * ``sophistication_level`` (optional) - Filter by sophistication level (0.0-1.0)
   * ``motivation`` (optional) - Filter by primary motivation
   * ``sector`` (optional) - Filter by target sector

   **Response:**

   .. code-block:: json

      {
        "threat_actors": [
          {
            "actor_id": "APT29",
            "name": "Cozy Bear",
            "sophistication_level": 0.9,
            "resource_level": 0.8,
            "persistence_level": 0.85,
            "primary_motivation": "espionage",
            "geographic_origin": "Russia",
            "target_sectors": ["government", "technology", "healthcare"],
            "preferred_techniques": ["T1566", "T1078", "T1055"],
            "known_tools": ["Cobalt Strike", "PowerShell Empire"],
            "attribution_confidence": 0.8
          }
        ]
      }

Risk Assessment
---------------

Assess Risk
~~~~~~~~~~

Perform comprehensive risk assessment for specified assets.

.. http:post:: /risk-assessment

   **Request Body:**

   .. code-block:: json

      {
        "asset_ids": ["database_001", "web_server_001"],
        "threat_context": {
          "threat_actors": ["APT29", "APT28"],
          "time_horizon_days": 365,
          "business_context": {
            "annual_revenue": 100000000,
            "customer_count": 50000,
            "data_sensitivity": "PII",
            "regulatory_requirements": ["GDPR", "HIPAA"]
          }
        }
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "risk_67890",
        "overall_risk_score": 0.75,
        "risk_level": "high",
        "asset_risk_scores": {
          "database_001": 0.9,
          "web_server_001": 0.6
        },
        "threat_landscape_score": 0.8,
        "vulnerability_score": 0.7,
        "compliance_risk_score": 0.65,
        "financial_impact_estimate": {
          "expected_loss": 1500000.0,
          "worst_case_loss": 5000000.0,
          "confidence_interval": [800000.0, 3200000.0]
        },
        "recommendations": [
          {
            "priority": "high",
            "category": "access_control",
            "description": "Implement multi-factor authentication",
            "risk_reduction": 0.3,
            "estimated_cost": 50000.0,
            "implementation_time_days": 30
          }
        ]
      }

Financial Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~

Calculate detailed financial impact of potential security incidents.

.. http:post:: /financial-impact

   **Request Body:**

   .. code-block:: json

      {
        "affected_assets": ["database_001", "payment_system"],
        "attack_scenario": "data_breach",
        "business_context": {
          "annual_revenue": 100000000,
          "customer_count": 50000,
          "industry": "financial_services",
          "geographic_regions": ["US", "EU"],
          "data_types": ["PII", "financial_data"],
          "regulatory_requirements": ["GDPR", "PCI_DSS", "SOX"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "fin_impact_123",
        "total_impact": 3750000.0,
        "impact_breakdown": {
          "direct_costs": {
            "incident_response": 150000.0,
            "forensic_investigation": 100000.0,
            "system_recovery": 200000.0,
            "legal_fees": 300000.0
          },
          "indirect_costs": {
            "business_disruption": 800000.0,
            "customer_churn": 1200000.0,
            "reputation_damage": 500000.0,
            "competitive_disadvantage": 300000.0
          },
          "regulatory_costs": {
            "gdpr_fines": 200000.0,
            "pci_penalties": 100000.0,
            "sox_compliance": 50000.0
          }
        },
        "recovery_timeline": {
          "immediate_response": "0-24 hours",
          "containment": "1-3 days",
          "investigation": "1-4 weeks",
          "full_recovery": "2-6 months"
        }
      }

Compliance Impact Analysis
-------------------------

GDPR Impact Assessment
~~~~~~~~~~~~~~~~~~~~~

Assess potential GDPR compliance violations and penalties.

.. http:post:: /compliance/gdpr

   **Request Body:**

   .. code-block:: json

      {
        "affected_assets": ["customer_database", "web_application"],
        "data_types": ["personal_data", "sensitive_personal_data"],
        "data_subjects_affected": 10000,
        "breach_scenario": "unauthorized_access",
        "data_controller_info": {
          "annual_turnover": 50000000,
          "is_public_authority": false,
          "main_activity": "data_processing"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "gdpr_123",
        "violation_probability": 0.85,
        "breach_notification_required": true,
        "data_subject_notification_required": true,
        "potential_fines": {
          "administrative_fine_range": {
            "min": 200000.0,
            "max": 1000000.0
          },
          "turnover_based_fine": {
            "percentage": 0.04,
            "amount": 2000000.0
          },
          "likely_fine": 500000.0
        },
        "notification_timeline": {
          "supervisory_authority": "72 hours",
          "data_subjects": "without undue delay"
        },
        "compliance_requirements": [
          "Breach notification to supervisory authority",
          "Data subject notification",
          "Impact assessment documentation",
          "Remedial measures implementation"
        ]
      }

HIPAA Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~

Assess potential HIPAA compliance violations for healthcare data.

.. http:post:: /compliance/hipaa

   **Request Body:**

   .. code-block:: json

      {
        "affected_assets": ["patient_records", "billing_system"],
        "phi_records_affected": 5000,
        "breach_type": "unauthorized_disclosure",
        "covered_entity_type": "healthcare_provider",
        "safeguards_in_place": ["encryption", "access_controls", "audit_logs"]
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "hipaa_456",
        "violation_probability": 0.7,
        "breach_notification_required": true,
        "ocr_reporting_required": true,
        "patient_notification_required": true,
        "potential_penalties": {
          "civil_monetary_penalties": {
            "tier_1": 100000.0,
            "tier_2": 250000.0,
            "tier_3": 500000.0,
            "tier_4": 1500000.0
          },
          "likely_penalty": 250000.0
        },
        "notification_timeline": {
          "hhs_ocr": "60 days",
          "affected_individuals": "60 days",
          "media_notification": "if_required"
        }
      }

Mitigation Strategies
--------------------

Generate Mitigations
~~~~~~~~~~~~~~~~~~~

Generate AI-driven mitigation strategies based on risk assessment.

.. http:post:: /mitigations/generate

   **Request Body:**

   .. code-block:: json

      {
        "simulation_id": "sim_12345",
        "risk_assessment_id": "risk_67890",
        "constraints": {
          "budget_limit": 500000.0,
          "implementation_timeline_days": 90,
          "risk_tolerance": "medium",
          "compliance_requirements": ["GDPR", "SOX"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "mitigation_plan_id": "plan_789",
        "total_cost": 450000.0,
        "total_risk_reduction": 0.65,
        "implementation_timeline": 85,
        "recommendations": [
          {
            "control_id": "ctrl_001",
            "control_name": "Multi-Factor Authentication",
            "category": "access_control",
            "mitre_techniques_addressed": ["T1078", "T1110"],
            "risk_reduction": 0.3,
            "implementation_cost": 75000.0,
            "implementation_days": 30,
            "roi": 4.2,
            "priority": "high",
            "description": "Implement MFA for all privileged accounts",
            "implementation_steps": [
              "Select MFA solution",
              "Deploy MFA infrastructure",
              "Configure user accounts",
              "Train users",
              "Monitor adoption"
            ]
          }
        ]
      }

Continuous Monitoring
--------------------

Setup Monitoring
~~~~~~~~~~~~~~~

Configure continuous threat monitoring for ongoing risk assessment.

.. http:post:: /monitoring/setup

   **Request Body:**

   .. code-block:: json

      {
        "monitoring_name": "Critical Assets Monitoring",
        "threat_actors": ["APT29", "APT28", "FIN7"],
        "target_assets": ["database_001", "web_server_001"],
        "monitoring_frequency": "daily",
        "alert_thresholds": {
          "risk_score_increase": 0.1,
          "new_attack_paths": 5,
          "compliance_risk_change": 0.05,
          "threat_intelligence_updates": true
        },
        "notification_settings": {
          "email_alerts": ["<EMAIL>"],
          "webhook_url": "https://company.com/security-webhook",
          "slack_channel": "#security-alerts"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "monitoring_id": "mon_456",
        "status": "active",
        "next_assessment": "2024-01-16T10:30:00Z",
        "baseline_risk_score": 0.65,
        "monitored_assets": ["database_001", "web_server_001"],
        "alert_configuration": {
          "enabled": true,
          "thresholds_configured": 4,
          "notification_channels": 3
        }
      }

Error Handling
--------------

The API uses standard HTTP status codes and returns detailed error information:

.. code-block:: json

   {
     "error": {
       "code": "INVALID_THREAT_ACTOR",
       "message": "The specified threat actor 'UNKNOWN_APT' is not available",
       "details": {
         "available_actors": ["APT29", "APT28", "FIN7", "INSIDER_THREAT"],
         "suggestion": "Use /threat-actors endpoint to list available actors"
       }
     }
   }

**Common Error Codes:**

* ``INVALID_THREAT_ACTOR`` - Unknown threat actor specified
* ``ASSET_NOT_FOUND`` - One or more assets not found
* ``INSUFFICIENT_PERMISSIONS`` - User lacks required permissions
* ``SIMULATION_IN_PROGRESS`` - Simulation already running
* ``RATE_LIMIT_EXCEEDED`` - Too many requests
* ``INVALID_PARAMETERS`` - Request validation failed

Rate Limiting
-------------

API requests are rate limited to ensure fair usage:

* **Standard Users**: 100 requests per hour
* **Premium Users**: 1000 requests per hour
* **Enterprise Users**: 10000 requests per hour

Rate limit headers are included in responses:

.. code-block:: text

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1642248000

For detailed examples and SDK usage, see the :doc:`../user-guides/threat-modeling` guide.
