Authentication API
==================

The Blast-Radius Security Tool provides comprehensive authentication and authorization capabilities through a RESTful API. This document covers all authentication-related endpoints, security models, and integration patterns.

Overview
--------

The authentication system supports:

* **JWT-based authentication** with refresh tokens
* **Multi-factor authentication** (MFA) with TOTP
* **Single Sign-On (SSO)** integration with SAML and OIDC
* **Role-based access control** (RBAC) with fine-grained permissions
* **Session management** with configurable timeouts
* **Audit logging** for all authentication events

Authentication Flow
-------------------

Standard Authentication Flow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Login Request**: Client submits credentials to ``/api/v1/auth/login``
2. **Credential Validation**: Server validates username/password
3. **MFA Challenge** (if enabled): Server requests MFA token
4. **Token Generation**: Server generates JWT access and refresh tokens
5. **Token Response**: Server returns tokens and user information
6. **API Access**: Client includes access token in subsequent requests
7. **Token Refresh**: Client uses refresh token to obtain new access tokens

API Endpoints
-------------

Authentication Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~

Login
^^^^^

**POST** ``/api/v1/auth/login``

Authenticate user with username/password credentials.

**Request Body:**

.. code-block:: json

   {
     "email": "<EMAIL>",
     "password": "securepassword123",
     "mfa_token": "123456",
     "remember_me": false
   }

**Response (200 OK):**

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 1800,
     "user": {
       "id": "123e4567-e89b-12d3-a456-426614174000",
       "email": "<EMAIL>",
       "first_name": "John",
       "last_name": "Doe",
       "role": "soc_operator",
       "permissions": ["view_security_events", "investigate_incidents"],
       "mfa_enabled": true,
       "last_login": "2024-01-15T10:30:00Z"
     }
   }

Logout
^^^^^^

**POST** ``/api/v1/auth/logout``

Invalidate current session and tokens.

**Headers:**
``Authorization: Bearer <access_token>``

**Response (200 OK):**

.. code-block:: json

   {
     "message": "Successfully logged out"
   }

Token Refresh
^^^^^^^^^^^^^

**POST** ``/api/v1/auth/refresh``

Obtain new access token using refresh token.

**Request Body:**

.. code-block:: json

   {
     "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   }

**Response (200 OK):**

.. code-block:: json

   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "expires_in": 1800
   }

Password Management
~~~~~~~~~~~~~~~~~~~

Change Password
^^^^^^^^^^^^^^^

**POST** ``/api/v1/auth/change-password``

Change user's password (requires current password).

**Headers:**
``Authorization: Bearer <access_token>``

**Request Body:**

.. code-block:: json

   {
     "current_password": "oldpassword123",
     "new_password": "newpassword456",
     "confirm_password": "newpassword456"
   }

**Response (200 OK):**

.. code-block:: json

   {
     "message": "Password changed successfully"
   }

Multi-Factor Authentication
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Enable MFA
^^^^^^^^^^^

**POST** ``/api/v1/auth/mfa/enable``

Enable MFA for current user.

**Headers:**
``Authorization: Bearer <access_token>``

**Response (200 OK):**

.. code-block:: json

   {
     "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
     "secret": "JBSWY3DPEHPK3PXP",
     "backup_codes": [
       "12345678",
       "87654321",
       "11223344"
     ]
   }

Security and Authorization
--------------------------

JWT Token Structure
~~~~~~~~~~~~~~~~~~~

**Access Token Claims:**

.. code-block:: json

   {
     "sub": "123e4567-e89b-12d3-a456-426614174000",
     "email": "<EMAIL>",
     "role": "soc_operator",
     "permissions": ["view_security_events", "investigate_incidents"],
     "session_id": "sess_123456789",
     "iat": 1705312200,
     "exp": **********,
     "iss": "blast-radius-security-tool",
     "aud": "blast-radius-users"
   }

Permission System
~~~~~~~~~~~~~~~~~

**Available Permissions:**

* ``view_security_events`` - View security events and alerts
* ``investigate_incidents`` - Access incident investigation tools
* ``update_incident_status`` - Modify incident status and notes
* ``view_attack_paths`` - Access attack path visualizations
* ``generate_reports`` - Create and export reports
* ``manage_users`` - Create and manage user accounts
* ``configure_system`` - Modify system settings
* ``manage_integrations`` - Configure external integrations

**Role-Permission Mapping:**

.. code-block:: json

   {
     "soc_operator": [
       "view_security_events",
       "investigate_incidents",
       "update_incident_status",
       "view_attack_paths",
       "generate_reports"
     ],
     "security_architect": [
       "view_security_events",
       "view_attack_paths",
       "generate_reports",
       "assess_risks",
       "design_security_controls"
     ],
     "administrator": [
       "manage_users",
       "configure_system",
       "manage_integrations",
       "view_audit_logs"
     ]
   }

Integration Examples
--------------------

JavaScript/TypeScript
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: typescript

   class AuthService {
     private baseUrl = 'https://api.blastradius.com/api/v1/auth';
     
     async login(email: string, password: string, mfaToken?: string) {
       const response = await fetch(`${this.baseUrl}/login`, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
         },
         body: JSON.stringify({
           email,
           password,
           mfa_token: mfaToken,
         }),
       });
       
       if (!response.ok) {
         throw new Error('Login failed');
       }
       
       const data = await response.json();
       localStorage.setItem('access_token', data.access_token);
       localStorage.setItem('refresh_token', data.refresh_token);
       
       return data;
     }
   }

Python
~~~~~~

.. code-block:: python

   import requests
   from typing import Optional, Dict, Any
   
   class AuthClient:
       def __init__(self, base_url: str):
           self.base_url = f"{base_url}/api/v1/auth"
           self.access_token: Optional[str] = None
           self.refresh_token: Optional[str] = None
       
       def login(self, email: str, password: str, mfa_token: Optional[str] = None) -> Dict[str, Any]:
           data = {
               "email": email,
               "password": password,
           }
           if mfa_token:
               data["mfa_token"] = mfa_token
           
           response = requests.post(f"{self.base_url}/login", json=data)
           response.raise_for_status()
           
           result = response.json()
           self.access_token = result["access_token"]
           self.refresh_token = result["refresh_token"]
           
           return result

Best Practices
--------------

Security Recommendations
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Always use HTTPS** in production environments
2. **Store tokens securely** (httpOnly cookies or secure storage)
3. **Implement token refresh** before expiration
4. **Enable MFA** for all users, especially administrators
5. **Monitor authentication events** for suspicious activity
6. **Implement proper session management** with timeouts
7. **Use strong password policies** and regular rotation
8. **Log all authentication events** for audit purposes

For more information about specific authentication scenarios and advanced configurations, see the :doc:`../security/access-control` and :doc:`../user-guides/administrators` documentation.
