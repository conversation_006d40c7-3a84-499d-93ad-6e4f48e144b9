Asset Management API
====================

Comprehensive API reference for asset management operations in the Blast-Radius Security Tool, including asset discovery, inventory management, and metadata operations.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Asset Management API provides complete control over asset lifecycle:

* **Asset Discovery** - Automated discovery across cloud and on-premises environments
* **Inventory Management** - CRUD operations for asset records
* **Metadata Management** - Custom attributes and tagging
* **Relationship Mapping** - Asset dependencies and connections
* **Bulk Operations** - Efficient batch processing for large environments

Base URL
--------

All asset management endpoints are available under:

.. code-block:: text

   https://your-domain.com/api/v1/assets

Authentication
--------------

All endpoints require authentication via JWT token:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        https://your-domain.com/api/v1/assets

Asset CRUD Operations
---------------------

List Assets
~~~~~~~~~~~

**Endpoint:** ``GET /api/v1/assets``

**Description:** Retrieve a paginated list of assets with optional filtering.

**Parameters:**

.. list-table:: Query Parameters
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - ``page``
     - integer
     - No
     - Page number (default: 1)
   * - ``limit``
     - integer
     - No
     - Items per page (default: 50, max: 1000)
   * - ``asset_type``
     - string
     - No
     - Filter by asset type
   * - ``environment``
     - string
     - No
     - Filter by environment
   * - ``is_active``
     - boolean
     - No
     - Filter by active status
   * - ``search``
     - string
     - No
     - Search in name, description, IP
   * - ``tags``
     - string
     - No
     - Comma-separated tag filters
   * - ``sort``
     - string
     - No
     - Sort field (name, created_at, risk_score)
   * - ``order``
     - string
     - No
     - Sort order (asc, desc)

**Example Request:**

.. code-block:: bash

   curl -X GET \
     "https://your-domain.com/api/v1/assets?asset_type=server&environment=production&page=1&limit=20" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "assets": [
         {
           "id": 1,
           "name": "web-server-01",
           "asset_type": "server",
           "ip_address": "************",
           "hostname": "web01.company.com",
           "environment": "production",
           "operating_system": "Ubuntu 20.04",
           "is_active": true,
           "risk_score": 7.5,
           "last_seen": "2024-01-15T10:30:00Z",
           "created_at": "2024-01-01T00:00:00Z",
           "updated_at": "2024-01-15T10:30:00Z",
           "tags": ["web", "frontend", "critical"],
           "metadata": {
             "cpu_cores": 4,
             "memory_gb": 16,
             "disk_gb": 100
           },
           "discovery_source": "aws_ec2",
           "compliance_status": "compliant"
         }
       ],
       "pagination": {
         "page": 1,
         "limit": 20,
         "total": 1247,
         "pages": 63,
         "has_next": true,
         "has_prev": false
       }
     },
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z",
       "version": "1.0.0"
     }
   }

Get Asset
~~~~~~~~~

**Endpoint:** ``GET /api/v1/assets/{asset_id}``

**Description:** Retrieve detailed information about a specific asset.

**Path Parameters:**

.. list-table:: Path Parameters
   :header-rows: 1
   :widths: 20 15 65

   * - Parameter
     - Type
     - Description
   * - ``asset_id``
     - integer
     - Unique asset identifier

**Query Parameters:**

.. list-table:: Query Parameters
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - ``include_relationships``
     - boolean
     - No
     - Include asset relationships (default: false)
   * - ``include_vulnerabilities``
     - boolean
     - No
     - Include vulnerability data (default: false)
   * - ``include_attack_paths``
     - boolean
     - No
     - Include attack path summary (default: false)

**Example Request:**

.. code-block:: bash

   curl -X GET \
     "https://your-domain.com/api/v1/assets/1?include_relationships=true&include_vulnerabilities=true" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": 1,
       "name": "web-server-01",
       "asset_type": "server",
       "ip_address": "************",
       "hostname": "web01.company.com",
       "environment": "production",
       "operating_system": "Ubuntu 20.04",
       "is_active": true,
       "risk_score": 7.5,
       "last_seen": "2024-01-15T10:30:00Z",
       "created_at": "2024-01-01T00:00:00Z",
       "updated_at": "2024-01-15T10:30:00Z",
       "tags": ["web", "frontend", "critical"],
       "metadata": {
         "cpu_cores": 4,
         "memory_gb": 16,
         "disk_gb": 100,
         "aws_instance_id": "i-1234567890abcdef0",
         "aws_region": "us-east-1"
       },
       "discovery_source": "aws_ec2",
       "compliance_status": "compliant",
       "relationships": [
         {
           "target_asset_id": 2,
           "target_asset_name": "database-01",
           "relationship_type": "connects_to",
           "port": 3306,
           "protocol": "tcp"
         }
       ],
       "vulnerabilities": [
         {
           "cve_id": "CVE-2023-1234",
           "severity": "high",
           "score": 8.5,
           "description": "Remote code execution vulnerability",
           "status": "open"
         }
       ]
     }
   }

Create Asset
~~~~~~~~~~~~

**Endpoint:** ``POST /api/v1/assets``

**Description:** Create a new asset record.

**Request Body:**

.. code-block:: json

   {
     "name": "new-server-01",
     "asset_type": "server",
     "ip_address": "************",
     "hostname": "newserver.company.com",
     "environment": "staging",
     "operating_system": "Ubuntu 22.04",
     "tags": ["web", "staging"],
     "metadata": {
       "cpu_cores": 2,
       "memory_gb": 8,
       "disk_gb": 50
     }
   }

**Example Request:**

.. code-block:: bash

   curl -X POST \
     "https://your-domain.com/api/v1/assets" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "new-server-01",
       "asset_type": "server",
       "ip_address": "************",
       "hostname": "newserver.company.com",
       "environment": "staging"
     }'

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": 1248,
       "name": "new-server-01",
       "asset_type": "server",
       "ip_address": "************",
       "hostname": "newserver.company.com",
       "environment": "staging",
       "operating_system": "Ubuntu 22.04",
       "is_active": true,
       "risk_score": 0.0,
       "last_seen": "2024-01-15T10:30:00Z",
       "created_at": "2024-01-15T10:30:00Z",
       "updated_at": "2024-01-15T10:30:00Z",
       "tags": ["web", "staging"],
       "metadata": {
         "cpu_cores": 2,
         "memory_gb": 8,
         "disk_gb": 50
       },
       "discovery_source": "manual",
       "compliance_status": "unknown"
     }
   }

Update Asset
~~~~~~~~~~~~

**Endpoint:** ``PUT /api/v1/assets/{asset_id}``

**Description:** Update an existing asset record.

**Path Parameters:**

.. list-table:: Path Parameters
   :header-rows: 1
   :widths: 20 15 65

   * - Parameter
     - Type
     - Description
   * - ``asset_id``
     - integer
     - Unique asset identifier

**Request Body:** Same as Create Asset, all fields optional.

**Example Request:**

.. code-block:: bash

   curl -X PUT \
     "https://your-domain.com/api/v1/assets/1248" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "environment": "production",
       "tags": ["web", "frontend", "production"]
     }'

Delete Asset
~~~~~~~~~~~~

**Endpoint:** ``DELETE /api/v1/assets/{asset_id}``

**Description:** Delete an asset record (soft delete by default).

**Path Parameters:**

.. list-table:: Path Parameters
   :header-rows: 1
   :widths: 20 15 65

   * - Parameter
     - Type
     - Description
   * - ``asset_id``
     - integer
     - Unique asset identifier

**Query Parameters:**

.. list-table:: Query Parameters
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - ``hard_delete``
     - boolean
     - No
     - Permanently delete (default: false)

**Example Request:**

.. code-block:: bash

   curl -X DELETE \
     "https://your-domain.com/api/v1/assets/1248" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "message": "Asset deleted successfully",
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z"
     }
   }

Asset Discovery
---------------

Start Discovery
~~~~~~~~~~~~~~~

**Endpoint:** ``POST /api/v1/assets/discovery/start``

**Description:** Start an asset discovery job.

**Request Body:**

.. code-block:: json

   {
     "discovery_type": "network",
     "targets": ["***********/24", "10.0.0.0/16"],
     "discovery_options": {
       "port_scan": true,
       "service_detection": true,
       "os_detection": true,
       "timeout": 300
     },
     "tags": ["discovered", "network-scan"]
   }

**Discovery Types:**

.. list-table:: Discovery Types
   :header-rows: 1
   :widths: 20 80

   * - Type
     - Description
   * - ``network``
     - Network-based discovery using Nmap
   * - ``aws``
     - AWS cloud provider discovery
   * - ``azure``
     - Azure cloud provider discovery
   * - ``gcp``
     - Google Cloud Platform discovery
   * - ``api``
     - API endpoint discovery
   * - ``agent``
     - Agent-based discovery

**Example Request:**

.. code-block:: bash

   curl -X POST \
     "https://your-domain.com/api/v1/assets/discovery/start" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "discovery_type": "aws",
       "targets": ["us-east-1", "us-west-2"],
       "discovery_options": {
         "services": ["ec2", "s3", "rds"],
         "include_stopped": false
       }
     }'

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "job_id": "disc_1234567890",
       "status": "started",
       "discovery_type": "aws",
       "targets": ["us-east-1", "us-west-2"],
       "estimated_duration": 600,
       "created_at": "2024-01-15T10:30:00Z"
     }
   }

Discovery Status
~~~~~~~~~~~~~~~~

**Endpoint:** ``GET /api/v1/assets/discovery/status/{job_id}``

**Description:** Get the status of a discovery job.

**Path Parameters:**

.. list-table:: Path Parameters
   :header-rows: 1
   :widths: 20 15 65

   * - Parameter
     - Type
     - Description
   * - ``job_id``
     - string
     - Discovery job identifier

**Example Request:**

.. code-block:: bash

   curl -X GET \
     "https://your-domain.com/api/v1/assets/discovery/status/disc_1234567890" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "job_id": "disc_1234567890",
       "status": "running",
       "progress": 65,
       "assets_discovered": 127,
       "targets_completed": 1,
       "targets_total": 2,
       "started_at": "2024-01-15T10:30:00Z",
       "estimated_completion": "2024-01-15T10:40:00Z",
       "current_target": "us-west-2",
       "errors": []
     }
   }

Discovery Results
~~~~~~~~~~~~~~~~~

**Endpoint:** ``GET /api/v1/assets/discovery/results/{job_id}``

**Description:** Get the results of a completed discovery job.

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "job_id": "disc_1234567890",
       "status": "completed",
       "summary": {
         "total_assets": 156,
         "new_assets": 23,
         "updated_assets": 133,
         "errors": 2
       },
       "assets": [
         {
           "id": 1249,
           "name": "discovered-server-01",
           "asset_type": "server",
           "ip_address": "************",
           "discovery_source": "aws_ec2",
           "is_new": true
         }
       ],
       "errors": [
         {
           "target": "*************",
           "error": "Connection timeout",
           "timestamp": "2024-01-15T10:35:00Z"
         }
       ]
     }
   }

Bulk Operations
---------------

Bulk Import
~~~~~~~~~~~

**Endpoint:** ``POST /api/v1/assets/bulk/import``

**Description:** Import multiple assets from CSV or JSON.

**Request Body (JSON):**

.. code-block:: json

   {
     "format": "json",
     "data": [
       {
         "name": "bulk-server-01",
         "asset_type": "server",
         "ip_address": "************"
       },
       {
         "name": "bulk-server-02",
         "asset_type": "server",
         "ip_address": "************"
       }
     ],
     "options": {
       "update_existing": true,
       "match_field": "ip_address"
     }
   }

**Request Body (CSV Upload):**

.. code-block:: bash

   curl -X POST \
     "https://your-domain.com/api/v1/assets/bulk/import" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -F "file=@assets.csv" \
     -F "format=csv" \
     -F "options={\"update_existing\": true}"

Bulk Update
~~~~~~~~~~~

**Endpoint:** ``PUT /api/v1/assets/bulk/update``

**Description:** Update multiple assets based on filters.

**Request Body:**

.. code-block:: json

   {
     "filters": {
       "asset_type": "server",
       "environment": "staging"
     },
     "updates": {
       "tags": ["staging", "updated"],
       "metadata": {
         "last_patched": "2024-01-15"
       }
     }
   }

Asset Relationships
-------------------

List Relationships
~~~~~~~~~~~~~~~~~~

**Endpoint:** ``GET /api/v1/assets/{asset_id}/relationships``

**Description:** Get all relationships for an asset.

**Example Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "asset_id": 1,
       "relationships": [
         {
           "id": 101,
           "target_asset_id": 2,
           "target_asset_name": "database-01",
           "relationship_type": "connects_to",
           "port": 3306,
           "protocol": "tcp",
           "confidence": 0.95,
           "created_at": "2024-01-15T10:30:00Z"
         }
       ]
     }
   }

Create Relationship
~~~~~~~~~~~~~~~~~~~

**Endpoint:** ``POST /api/v1/assets/{asset_id}/relationships``

**Description:** Create a new asset relationship.

**Request Body:**

.. code-block:: json

   {
     "target_asset_id": 3,
     "relationship_type": "depends_on",
     "port": 443,
     "protocol": "https",
     "metadata": {
       "service": "api",
       "criticality": "high"
     }
   }

Asset Tags and Metadata
-----------------------

Manage Tags
~~~~~~~~~~~

**Endpoint:** ``POST /api/v1/assets/{asset_id}/tags``

**Description:** Add tags to an asset.

**Request Body:**

.. code-block:: json

   {
     "tags": ["production", "critical", "web-tier"]
   }

**Endpoint:** ``DELETE /api/v1/assets/{asset_id}/tags``

**Description:** Remove tags from an asset.

**Request Body:**

.. code-block:: json

   {
     "tags": ["staging"]
   }

Update Metadata
~~~~~~~~~~~~~~~

**Endpoint:** ``PUT /api/v1/assets/{asset_id}/metadata``

**Description:** Update asset metadata.

**Request Body:**

.. code-block:: json

   {
     "metadata": {
       "owner": "security-team",
       "cost_center": "IT-001",
       "backup_schedule": "daily",
       "maintenance_window": "Sunday 2-4 AM"
     }
   }

Error Handling
--------------

Error Response Format
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "success": false,
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid asset type provided",
       "details": {
         "field": "asset_type",
         "allowed_values": ["server", "database", "workstation", "network_device"]
       }
     },
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z",
       "request_id": "req_1234567890"
     }
   }

Common Error Codes
~~~~~~~~~~~~~~~~~~

.. list-table:: Error Codes
   :header-rows: 1
   :widths: 25 75

   * - Code
     - Description
   * - ``VALIDATION_ERROR``
     - Input validation failed
   * - ``ASSET_NOT_FOUND``
     - Asset does not exist
   * - ``DUPLICATE_ASSET``
     - Asset already exists
   * - ``PERMISSION_DENIED``
     - Insufficient permissions
   * - ``DISCOVERY_FAILED``
     - Asset discovery job failed
   * - ``BULK_OPERATION_FAILED``
     - Bulk operation encountered errors

Rate Limiting
-------------

The Asset Management API implements rate limiting:

* **Standard users:** 100 requests per minute
* **Premium users:** 1000 requests per minute
* **Bulk operations:** 10 requests per minute

Rate limit headers are included in responses:

.. code-block:: text

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1642248000

SDK Examples
------------

Python SDK
~~~~~~~~~~

.. code-block:: python

   from blast_radius_sdk import BlastRadiusClient
   
   client = BlastRadiusClient(
       base_url="https://your-domain.com",
       api_token="your-jwt-token"
   )
   
   # List assets
   assets = client.assets.list(
       asset_type="server",
       environment="production",
       limit=50
   )
   
   # Create asset
   new_asset = client.assets.create({
       "name": "new-server",
       "asset_type": "server",
       "ip_address": "*************"
   })
   
   # Start discovery
   job = client.assets.start_discovery(
       discovery_type="aws",
       targets=["us-east-1"]
   )

JavaScript SDK
~~~~~~~~~~~~~~

.. code-block:: javascript

   import { BlastRadiusClient } from '@blast-radius/sdk';
   
   const client = new BlastRadiusClient({
     baseUrl: 'https://your-domain.com',
     apiToken: 'your-jwt-token'
   });
   
   // List assets
   const assets = await client.assets.list({
     assetType: 'server',
     environment: 'production',
     limit: 50
   });
   
   // Create asset
   const newAsset = await client.assets.create({
     name: 'new-server',
     assetType: 'server',
     ipAddress: '*************'
   });

.. note::
   For complete API documentation with interactive examples, visit the
   Swagger UI at ``https://your-domain.com/docs`` when the application is running.
