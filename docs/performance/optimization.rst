Performance Optimization
========================

Comprehensive guide to optimizing the Blast-Radius Security Tool for maximum performance, covering database tuning, application optimization, and infrastructure scaling.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Performance optimization ensures the Blast-Radius Security Tool can handle enterprise-scale workloads efficiently:

* **Sub-second response times** for critical operations
* **High throughput** for concurrent users and analysis requests
* **Efficient resource utilization** to minimize infrastructure costs
* **Scalable architecture** that grows with your organization
* **Optimized algorithms** for complex security analysis

Optimization Strategy
---------------------

Performance Methodology
~~~~~~~~~~~~~~~~~~~~~~~~

**1. Measure First**
- Establish baseline performance metrics
- Identify bottlenecks through profiling
- Set realistic performance targets
- Monitor continuously

**2. Optimize Systematically**
- Database queries and indexes
- Application code and algorithms
- Caching strategies
- Infrastructure configuration

**3. Validate Improvements**
- Benchmark before and after changes
- Load test under realistic conditions
- Monitor production performance
- Iterate based on results

**Performance Targets:**

.. list-table:: Performance Targets by Operation
   :header-rows: 1
   :widths: 40 20 20 20

   * - Operation
     - Target Time
     - Throughput
     - Concurrency
   * - **API Response (simple)**
     - <200ms
     - 1000 req/sec
     - 100 users
   * - **Attack Path Analysis**
     - <2 seconds
     - 50 analyses/min
     - 20 concurrent
   * - **Asset Discovery**
     - <5 minutes
     - 1000 assets/min
     - 10 concurrent
   * - **MITRE Correlation**
     - <500ms
     - 200 req/sec
     - 50 concurrent

Database Optimization
---------------------

PostgreSQL Tuning
~~~~~~~~~~~~~~~~~~

**Memory Configuration:**

.. code-block:: sql

   -- postgresql.conf optimizations
   
   -- Memory settings (for 16GB RAM server)
   shared_buffers = 4GB                    -- 25% of total RAM
   effective_cache_size = 12GB             -- 75% of total RAM
   work_mem = 256MB                        -- Per operation memory
   maintenance_work_mem = 1GB              -- Maintenance operations
   
   -- Connection settings
   max_connections = 200                   -- Adjust based on load
   
   -- Query planner settings
   random_page_cost = 1.1                  -- SSD optimization
   effective_io_concurrency = 200          -- SSD concurrent I/O
   
   -- Write-ahead logging
   wal_buffers = 64MB
   checkpoint_completion_target = 0.9
   max_wal_size = 4GB
   min_wal_size = 1GB
   
   -- Background writer
   bgwriter_delay = 200ms
   bgwriter_lru_maxpages = 100
   bgwriter_lru_multiplier = 2.0

**Index Optimization:**

.. code-block:: sql

   -- Critical indexes for performance
   
   -- Assets table indexes
   CREATE INDEX CONCURRENTLY idx_assets_type_active 
   ON assets(asset_type, is_active) WHERE is_active = true;
   
   CREATE INDEX CONCURRENTLY idx_assets_ip_address 
   ON assets USING hash(ip_address);
   
   CREATE INDEX CONCURRENTLY idx_assets_created_at 
   ON assets(created_at DESC);
   
   -- Attack paths indexes
   CREATE INDEX CONCURRENTLY idx_attack_paths_source_target 
   ON attack_paths(source_asset_id, target_asset_id);
   
   CREATE INDEX CONCURRENTLY idx_attack_paths_risk_score 
   ON attack_paths(risk_score DESC) WHERE risk_score > 5.0;
   
   -- MITRE techniques indexes
   CREATE INDEX CONCURRENTLY idx_mitre_techniques_tactic 
   ON mitre_techniques(tactic_id);
   
   CREATE INDEX CONCURRENTLY idx_mitre_techniques_search 
   ON mitre_techniques USING gin(to_tsvector('english', name || ' ' || description));
   
   -- Composite indexes for common queries
   CREATE INDEX CONCURRENTLY idx_assets_discovery_composite 
   ON assets(asset_type, environment, is_active, last_seen DESC);

**Query Optimization:**

.. code-block:: sql

   -- Optimized query examples
   
   -- Before: Slow query
   SELECT * FROM assets 
   WHERE asset_type = 'server' 
   AND created_at > NOW() - INTERVAL '30 days'
   ORDER BY created_at DESC;
   
   -- After: Optimized query
   SELECT id, name, asset_type, ip_address, created_at 
   FROM assets 
   WHERE asset_type = 'server' 
   AND is_active = true
   AND created_at > NOW() - INTERVAL '30 days'
   ORDER BY created_at DESC
   LIMIT 100;
   
   -- Use EXPLAIN ANALYZE to verify performance
   EXPLAIN (ANALYZE, BUFFERS) 
   SELECT id, name FROM assets WHERE asset_type = 'server';

**Connection Pooling:**

.. code-block:: python

   # SQLAlchemy connection pool optimization
   from sqlalchemy import create_engine
   from sqlalchemy.pool import QueuePool
   
   engine = create_engine(
       DATABASE_URL,
       poolclass=QueuePool,
       pool_size=20,              # Base number of connections
       max_overflow=30,           # Additional connections under load
       pool_timeout=30,           # Timeout for getting connection
       pool_recycle=3600,         # Recycle connections every hour
       pool_pre_ping=True,        # Validate connections before use
       echo=False                 # Disable SQL logging in production
   )

Redis Optimization
~~~~~~~~~~~~~~~~~~

**Memory Configuration:**

.. code-block:: bash

   # redis.conf optimizations
   
   # Memory management
   maxmemory 8gb
   maxmemory-policy allkeys-lru
   
   # Persistence (adjust for performance vs durability)
   save 900 1
   save 300 10
   save 60 10000
   
   # Network optimization
   tcp-keepalive 60
   timeout 300
   
   # Performance settings
   hash-max-ziplist-entries 512
   hash-max-ziplist-value 64
   list-max-ziplist-size -2
   set-max-intset-entries 512

**Caching Strategy:**

.. code-block:: python

   # Intelligent caching implementation
   import redis
   import json
   from typing import Optional, Any
   
   class CacheManager:
       def __init__(self, redis_client: redis.Redis):
           self.redis = redis_client
           self.default_ttl = 3600  # 1 hour
       
       def get_or_set(self, key: str, fetch_func, ttl: Optional[int] = None) -> Any:
           """Get from cache or fetch and cache the result."""
           cached_value = self.redis.get(key)
           
           if cached_value:
               return json.loads(cached_value)
           
           # Fetch fresh data
           fresh_value = fetch_func()
           
           # Cache the result
           self.redis.setex(
               key, 
               ttl or self.default_ttl, 
               json.dumps(fresh_value, default=str)
           )
           
           return fresh_value
       
       def invalidate_pattern(self, pattern: str):
           """Invalidate all keys matching pattern."""
           keys = self.redis.keys(pattern)
           if keys:
               self.redis.delete(*keys)
   
   # Usage in services
   cache = CacheManager(redis_client)
   
   def get_attack_paths(source_id: int, target_id: int):
       cache_key = f"attack_paths:{source_id}:{target_id}"
       
       return cache.get_or_set(
           cache_key,
           lambda: expensive_attack_path_calculation(source_id, target_id),
           ttl=1800  # 30 minutes
       )

Neo4j Optimization
~~~~~~~~~~~~~~~~~~

**Memory and Performance:**

.. code-block:: bash

   # neo4j.conf optimizations
   
   # Memory settings
   dbms.memory.heap.initial_size=4g
   dbms.memory.heap.max_size=8g
   dbms.memory.pagecache.size=4g
   
   # Query performance
   dbms.query_cache_size=1000
   dbms.query.timeout=60s
   
   # Connection pooling
   dbms.connector.bolt.thread_pool_max_size=400
   dbms.connector.bolt.thread_pool_keep_alive=5m

**Cypher Query Optimization:**

.. code-block:: cypher

   // Before: Inefficient query
   MATCH (source:Asset)-[*1..5]-(target:Asset)
   WHERE source.id = $sourceId AND target.id = $targetId
   RETURN path;
   
   // After: Optimized query with constraints
   MATCH path = (source:Asset)-[*1..5]-(target:Asset)
   WHERE source.id = $sourceId 
   AND target.id = $targetId
   AND ALL(n IN nodes(path) WHERE n.is_active = true)
   RETURN path
   LIMIT 100;
   
   // Use indexes for better performance
   CREATE INDEX asset_id_index FOR (a:Asset) ON (a.id);
   CREATE INDEX asset_type_index FOR (a:Asset) ON (a.type);

Application Optimization
------------------------

Code-Level Optimizations
~~~~~~~~~~~~~~~~~~~~~~~~

**Async Programming:**

.. code-block:: python

   import asyncio
   import aiohttp
   from typing import List
   
   class AsyncAssetDiscovery:
       def __init__(self):
           self.session = None
       
       async def __aenter__(self):
           self.session = aiohttp.ClientSession()
           return self
       
       async def __aexit__(self, exc_type, exc_val, exc_tb):
           await self.session.close()
       
       async def discover_asset(self, ip: str) -> dict:
           """Discover single asset asynchronously."""
           async with self.session.get(f"http://{ip}/api/info") as response:
               if response.status == 200:
                   return await response.json()
               return {"ip": ip, "status": "unreachable"}
       
       async def discover_assets_batch(self, ips: List[str]) -> List[dict]:
           """Discover multiple assets concurrently."""
           tasks = [self.discover_asset(ip) for ip in ips]
           return await asyncio.gather(*tasks, return_exceptions=True)
   
   # Usage
   async def main():
       ips = ["***********", "***********", "***********"]
       
       async with AsyncAssetDiscovery() as discovery:
           results = await discovery.discover_assets_batch(ips)
           print(f"Discovered {len(results)} assets")

**Efficient Data Processing:**

.. code-block:: python

   import pandas as pd
   from typing import Iterator, List
   
   class EfficientDataProcessor:
       @staticmethod
       def process_assets_chunked(assets: Iterator, chunk_size: int = 1000):
           """Process assets in chunks to manage memory usage."""
           chunk = []
           
           for asset in assets:
               chunk.append(asset)
               
               if len(chunk) >= chunk_size:
                   yield chunk
                   chunk = []
           
           if chunk:  # Process remaining items
               yield chunk
       
       @staticmethod
       def bulk_risk_calculation(assets: List[dict]) -> pd.DataFrame:
           """Efficient bulk risk calculation using pandas."""
           df = pd.DataFrame(assets)
           
           # Vectorized operations are much faster
           df['base_risk'] = df['vulnerability_count'] * 0.3
           df['exposure_risk'] = df['external_exposure'].astype(int) * 2.0
           df['criticality_multiplier'] = df['criticality'].map({
               'low': 1.0, 'medium': 1.5, 'high': 2.0, 'critical': 3.0
           })
           
           df['total_risk'] = (df['base_risk'] + df['exposure_risk']) * df['criticality_multiplier']
           
           return df[['asset_id', 'total_risk']]

**Memory Management:**

.. code-block:: python

   import gc
   import weakref
   from typing import Dict, Any
   
   class MemoryEfficientCache:
       def __init__(self, max_size: int = 1000):
           self._cache: Dict[str, Any] = {}
           self._access_order: List[str] = []
           self.max_size = max_size
       
       def get(self, key: str) -> Any:
           if key in self._cache:
               # Move to end (most recently used)
               self._access_order.remove(key)
               self._access_order.append(key)
               return self._cache[key]
           return None
       
       def set(self, key: str, value: Any):
           if key in self._cache:
               self._access_order.remove(key)
           elif len(self._cache) >= self.max_size:
               # Remove least recently used
               lru_key = self._access_order.pop(0)
               del self._cache[lru_key]
           
           self._cache[key] = value
           self._access_order.append(key)
       
       def clear(self):
           self._cache.clear()
           self._access_order.clear()
           gc.collect()  # Force garbage collection

Algorithm Optimization
~~~~~~~~~~~~~~~~~~~~~~

**Graph Algorithm Optimization:**

.. code-block:: python

   import networkx as nx
   from typing import List, Tuple, Set
   
   class OptimizedAttackPathFinder:
       def __init__(self, graph: nx.DiGraph):
           self.graph = graph
           self._precompute_shortest_paths()
       
       def _precompute_shortest_paths(self):
           """Precompute shortest paths for frequently accessed nodes."""
           # Identify high-degree nodes (likely to be in many paths)
           high_degree_nodes = [
               node for node, degree in self.graph.degree() 
               if degree > 10
           ]
           
           self.shortest_paths = {}
           for node in high_degree_nodes:
               self.shortest_paths[node] = nx.single_source_shortest_path(
                   self.graph, node, cutoff=5
               )
       
       def find_attack_paths_optimized(
           self, 
           source: int, 
           target: int, 
           max_paths: int = 10
       ) -> List[List[int]]:
           """Find attack paths with optimizations."""
           
           # Quick check: direct connection
           if self.graph.has_edge(source, target):
               return [[source, target]]
           
           # Use precomputed paths if available
           if source in self.shortest_paths:
               if target in self.shortest_paths[source]:
                   return [self.shortest_paths[source][target]]
           
           # Bidirectional search for efficiency
           try:
               paths = list(nx.all_simple_paths(
                   self.graph, source, target, cutoff=5
               ))
               
               # Sort by length and risk score
               paths.sort(key=lambda p: (len(p), self._calculate_path_risk(p)))
               
               return paths[:max_paths]
           except nx.NetworkXNoPath:
               return []
       
       def _calculate_path_risk(self, path: List[int]) -> float:
           """Calculate risk score for a path."""
           total_risk = 0.0
           for i in range(len(path) - 1):
               edge_data = self.graph.get_edge_data(path[i], path[i + 1])
               total_risk += edge_data.get('risk_weight', 1.0)
           return total_risk

Caching Strategies
------------------

Multi-Level Caching
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from functools import lru_cache
   import time
   
   class MultiLevelCache:
       def __init__(self, redis_client):
           self.redis = redis_client
           self.l1_cache = {}  # In-memory cache
           self.l1_max_size = 1000
           self.l1_ttl = 300  # 5 minutes
           
       @lru_cache(maxsize=100)
       def get_mitre_technique(self, technique_id: str) -> dict:
           """L0 cache: Function-level LRU cache."""
           return self._fetch_mitre_technique(technique_id)
       
       def get_attack_analysis(self, source_id: int, target_id: int) -> dict:
           """L1 cache: Application-level cache."""
           cache_key = f"analysis:{source_id}:{target_id}"
           
           # Check L1 cache (in-memory)
           if cache_key in self.l1_cache:
               cached_data, timestamp = self.l1_cache[cache_key]
               if time.time() - timestamp < self.l1_ttl:
                   return cached_data
               else:
                   del self.l1_cache[cache_key]
           
           # Check L2 cache (Redis)
           cached_result = self.redis.get(cache_key)
           if cached_result:
               result = json.loads(cached_result)
               # Store in L1 cache
               self._store_l1(cache_key, result)
               return result
           
           # Fetch fresh data
           result = self._perform_attack_analysis(source_id, target_id)
           
           # Store in both caches
           self._store_l1(cache_key, result)
           self.redis.setex(cache_key, 3600, json.dumps(result, default=str))
           
           return result
       
       def _store_l1(self, key: str, value: dict):
           """Store in L1 cache with size management."""
           if len(self.l1_cache) >= self.l1_max_size:
               # Remove oldest entry
               oldest_key = min(self.l1_cache.keys(), 
                              key=lambda k: self.l1_cache[k][1])
               del self.l1_cache[oldest_key]
           
           self.l1_cache[key] = (value, time.time())

Infrastructure Optimization
----------------------------

Load Balancing
~~~~~~~~~~~~~~

**Nginx Configuration:**

.. code-block:: nginx

   upstream backend_servers {
       least_conn;
       server backend-1:8000 weight=3 max_fails=3 fail_timeout=30s;
       server backend-2:8000 weight=3 max_fails=3 fail_timeout=30s;
       server backend-3:8000 weight=2 max_fails=3 fail_timeout=30s;
       server backend-4:8000 weight=2 max_fails=3 fail_timeout=30s;
       
       # Health check
       keepalive 32;
   }
   
   server {
       listen 80;
       server_name blast-radius.example.com;
       
       # Performance optimizations
       client_max_body_size 10M;
       client_body_timeout 60s;
       client_header_timeout 60s;
       keepalive_timeout 65s;
       send_timeout 60s;
       
       # Compression
       gzip on;
       gzip_vary on;
       gzip_min_length 1024;
       gzip_types
           text/plain
           text/css
           text/xml
           text/javascript
           application/json
           application/javascript
           application/xml+rss
           application/atom+xml;
       
       location / {
           proxy_pass http://backend_servers;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           
           # Performance settings
           proxy_buffering on;
           proxy_buffer_size 128k;
           proxy_buffers 4 256k;
           proxy_busy_buffers_size 256k;
           proxy_connect_timeout 60s;
           proxy_send_timeout 60s;
           proxy_read_timeout 60s;
       }
       
       # Static file caching
       location /static/ {
           expires 1y;
           add_header Cache-Control "public, immutable";
           access_log off;
       }
   }

Container Optimization
~~~~~~~~~~~~~~~~~~~~~~

**Docker Configuration:**

.. code-block:: dockerfile

   # Multi-stage build for smaller images
   FROM python:3.11-slim as builder
   
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install --no-cache-dir --user -r requirements.txt
   
   FROM python:3.11-slim
   
   # Performance optimizations
   ENV PYTHONUNBUFFERED=1
   ENV PYTHONDONTWRITEBYTECODE=1
   ENV PYTHONHASHSEED=random
   
   # Copy only necessary files
   COPY --from=builder /root/.local /root/.local
   COPY app/ /app/
   
   WORKDIR /app
   
   # Health check
   HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
       CMD curl -f http://localhost:8000/health || exit 1
   
   # Resource limits
   USER 1000:1000
   
   CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", \
        "--worker-class", "uvicorn.workers.UvicornWorker", \
        "--max-requests", "1000", "--max-requests-jitter", "100", \
        "app.main:app"]

**Docker Compose Optimization:**

.. code-block:: yaml

   version: '3.8'
   
   services:
     backend:
       build: .
       deploy:
         resources:
           limits:
             cpus: '2.0'
             memory: 4G
           reservations:
             cpus: '1.0'
             memory: 2G
         restart_policy:
           condition: on-failure
           delay: 5s
           max_attempts: 3
       environment:
         - WORKERS=4
         - MAX_REQUESTS=1000
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
         interval: 30s
         timeout: 10s
         retries: 3
         start_period: 40s

Monitoring and Profiling
-------------------------

Performance Monitoring
~~~~~~~~~~~~~~~~~~~~~~

**Application Metrics:**

.. code-block:: python

   import time
   import psutil
   from prometheus_client import Counter, Histogram, Gauge
   
   # Metrics collection
   REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
   REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
   MEMORY_USAGE = Gauge('memory_usage_bytes', 'Memory usage in bytes')
   
   def monitor_performance(func):
       """Decorator to monitor function performance."""
       def wrapper(*args, **kwargs):
           start_time = time.time()
           
           try:
               result = func(*args, **kwargs)
               REQUEST_COUNT.labels(method='POST', endpoint=func.__name__).inc()
               return result
           finally:
               duration = time.time() - start_time
               REQUEST_DURATION.observe(duration)
               MEMORY_USAGE.set(psutil.Process().memory_info().rss)
       
       return wrapper

**Database Performance Monitoring:**

.. code-block:: sql

   -- Monitor slow queries
   SELECT 
       query,
       mean_time,
       calls,
       total_time,
       (total_time / calls) as avg_time_per_call
   FROM pg_stat_statements 
   WHERE mean_time > 1000  -- Queries taking more than 1 second
   ORDER BY mean_time DESC
   LIMIT 20;
   
   -- Monitor database connections
   SELECT 
       state,
       count(*) as connection_count
   FROM pg_stat_activity 
   GROUP BY state;
   
   -- Monitor index usage
   SELECT 
       schemaname,
       tablename,
       indexname,
       idx_scan,
       idx_tup_read,
       idx_tup_fetch
   FROM pg_stat_user_indexes
   WHERE idx_scan = 0  -- Unused indexes
   ORDER BY schemaname, tablename;

Profiling Tools
~~~~~~~~~~~~~~~

**Python Profiling:**

.. code-block:: python

   import cProfile
   import pstats
   from functools import wraps
   
   def profile_function(func):
       """Decorator to profile function execution."""
       @wraps(func)
       def wrapper(*args, **kwargs):
           profiler = cProfile.Profile()
           profiler.enable()
           
           try:
               result = func(*args, **kwargs)
               return result
           finally:
               profiler.disable()
               stats = pstats.Stats(profiler)
               stats.sort_stats('cumulative')
               stats.print_stats(20)  # Top 20 functions
       
       return wrapper
   
   # Usage
   @profile_function
   def expensive_attack_analysis():
       # Complex analysis code
       pass

Best Practices
--------------

Optimization Guidelines
~~~~~~~~~~~~~~~~~~~~~~~

1. **Measure Before Optimizing** - Profile to identify real bottlenecks
2. **Optimize the Right Things** - Focus on high-impact improvements
3. **Test Performance Changes** - Validate improvements with benchmarks
4. **Monitor Continuously** - Track performance in production
5. **Document Optimizations** - Record what was changed and why

Common Pitfalls
~~~~~~~~~~~~~~~

1. **Premature Optimization** - Don't optimize without evidence
2. **Over-Caching** - Too much caching can cause memory issues
3. **Ignoring Database** - Database is often the bottleneck
4. **Not Testing at Scale** - Test with realistic data volumes
5. **Forgetting Monitoring** - Set up monitoring before optimization

Next Steps
----------

For ongoing performance optimization:

1. **Implement monitoring** - Set up comprehensive performance monitoring
2. **Establish baselines** - Measure current performance
3. **Identify bottlenecks** - Use profiling to find slow components
4. **Apply optimizations** - Implement targeted improvements
5. **Validate results** - Measure improvement and monitor production

.. note::
   Performance optimization is an iterative process. Start with the biggest
   bottlenecks and work your way down. Always measure the impact of changes.
