Attack Path Analysis Use Cases
===============================

Comprehensive guide to attack path analysis capabilities in the Blast-Radius Security Tool, covering threat modeling, risk assessment, and security validation scenarios.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Attack path analysis is the core capability of the Blast-Radius Security Tool, providing:

* **Graph-based Analysis** - Visualize complex attack scenarios across your infrastructure
* **MITRE ATT&CK Integration** - Map attack paths to real-world threat techniques
* **Risk Quantification** - Calculate blast radius and impact assessment
* **Threat Modeling** - Proactive security analysis and validation
* **Compliance Support** - Evidence for security frameworks and audits

Key Capabilities
----------------

Analysis Types
~~~~~~~~~~~~~~

**1. Point-to-Point Analysis**
- Analyze attack paths between specific assets
- Identify shortest and highest-risk paths
- Calculate blast radius from compromised assets

**2. Multi-Target Analysis**
- Analyze paths from one source to multiple targets
- Identify critical chokepoints and vulnerabilities
- Prioritize security controls and mitigations

**3. Lateral Movement Analysis**
- Map potential lateral movement scenarios
- Identify privilege escalation opportunities
- Analyze network segmentation effectiveness

**4. Crown Jewel Protection**
- Analyze paths to critical business assets
- Validate security controls around sensitive data
- Assess impact of potential breaches

Use Case Scenarios
------------------

Scenario 1: Red Team Attack Simulation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Red team needs to identify realistic attack paths for penetration testing and security validation exercises.

**Implementation:**

.. code-block:: python

   # Red team attack path discovery
   from app.services.attack_path_service import AttackPathService
   from app.models.asset import Asset
   
   # Initialize attack path service
   attack_service = AttackPathService()
   
   # Define attack scenario
   external_asset = Asset.get_by_name("external-web-server")
   crown_jewel = Asset.get_by_name("customer-database")
   
   # Analyze attack paths
   attack_paths = attack_service.analyze_attack_paths(
       source=external_asset,
       target=crown_jewel,
       max_depth=8,
       include_mitre=True,
       attack_types=["lateral_movement", "privilege_escalation"]
   )
   
   # Generate red team report
   red_team_report = attack_service.generate_red_team_report(
       attack_paths=attack_paths,
       include_techniques=True,
       include_tools=True,
       format="detailed"
   )

**Expected Results:**

.. list-table:: Red Team Analysis Results
   :header-rows: 1
   :widths: 30 25 25 20

   * - Attack Path
     - Risk Score
     - MITRE Techniques
     - Estimated Time
   * - **Web → App → DB**
     - 8.5/10
     - T1190, T1068, T1003
     - 2-4 hours
   * - **Web → File → Domain → DB**
     - 7.2/10
     - T1190, T1021, T1078
     - 4-8 hours
   * - **Web → Email → Workstation → DB**
     - 6.8/10
     - T1566, T1204, T1055
     - 1-2 days

**Red Team Deliverables:**
- Detailed attack path documentation
- MITRE ATT&CK technique mapping
- Tool and exploit recommendations
- Timeline and complexity estimates
- Defensive recommendations

Scenario 2: Purple Team Validation Exercise
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Purple team collaboration to validate detection capabilities and improve security controls through realistic attack simulation.

**Implementation:**

.. code-block:: python

   # Purple team validation workflow
   from app.services.purple_team_service import PurpleTeamService
   from app.integrations.siem import SIEMIntegration
   
   purple_service = PurpleTeamService()
   siem = SIEMIntegration()
   
   # Define validation scenario
   validation_scenario = {
       "name": "Lateral Movement Detection",
       "source_asset": "compromised-workstation",
       "target_assets": ["file-server", "domain-controller"],
       "techniques": ["T1021.001", "T1078.002", "T1003.001"],
       "detection_rules": ["rule_001", "rule_002", "rule_003"]
   }
   
   # Execute purple team exercise
   exercise_results = purple_service.execute_validation(
       scenario=validation_scenario,
       simulate_attacks=True,
       monitor_detections=True,
       duration_hours=4
   )
   
   # Analyze detection effectiveness
   detection_analysis = purple_service.analyze_detection_coverage(
       attack_paths=exercise_results.attack_paths,
       siem_alerts=siem.get_alerts(timeframe="4h"),
       detection_rules=validation_scenario["detection_rules"]
   )

**Purple Team Metrics:**

.. list-table:: Detection Effectiveness Analysis
   :header-rows: 1
   :widths: 25 20 20 35

   * - MITRE Technique
     - Simulated
     - Detected
     - Detection Quality
   * - **T1021.001 (RDP)**
     - ✅ Yes
     - ✅ Yes
     - High confidence, low false positives
   * - **T1078.002 (Domain Accounts)**
     - ✅ Yes
     - ⚠️ Partial
     - Medium confidence, needs tuning
   * - **T1003.001 (LSASS)**
     - ✅ Yes
     - ❌ No
     - Missing detection rule

**Collaboration Workflow:**

.. code-block:: python

   # Purple team collaboration platform
   collaboration_results = purple_service.generate_collaboration_report(
       red_team_findings=exercise_results.attack_paths,
       blue_team_detections=detection_analysis,
       recommendations={
           "immediate": [
               "Deploy LSASS monitoring rule",
               "Tune domain account detection thresholds"
           ],
           "short_term": [
               "Implement network segmentation",
               "Deploy additional endpoint monitoring"
           ],
           "long_term": [
               "Zero trust architecture implementation",
               "Advanced behavioral analytics"
           ]
       }
   )

Scenario 3: Compliance Risk Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Organization needs to demonstrate security controls effectiveness for SOC 2, PCI DSS, and ISO 27001 compliance audits.

**Implementation:**

.. code-block:: python

   # Compliance-focused attack path analysis
   from app.services.compliance_service import ComplianceService
   from app.models.compliance_framework import ComplianceFramework
   
   compliance_service = ComplianceService()
   
   # Define compliance scope
   pci_scope = compliance_service.get_compliance_scope("PCI_DSS")
   cardholder_data_assets = pci_scope.get_assets_by_classification("cardholder_data")
   
   # Analyze attack paths to sensitive data
   compliance_analysis = compliance_service.analyze_compliance_risk(
       framework="PCI_DSS",
       sensitive_assets=cardholder_data_assets,
       include_network_segmentation=True,
       include_access_controls=True,
       include_monitoring=True
   )
   
   # Generate compliance report
   pci_report = compliance_service.generate_compliance_report(
       framework="PCI_DSS",
       analysis_results=compliance_analysis,
       include_evidence=True,
       include_recommendations=True
   )

**Compliance Analysis Results:**

.. list-table:: PCI DSS Compliance Assessment
   :header-rows: 1
   :widths: 30 25 25 20

   * - Control Area
     - Risk Level
     - Attack Paths
     - Compliance Status
   * - **Network Segmentation**
     - Low
     - 2 paths found
     - ✅ Compliant
   * - **Access Controls**
     - Medium
     - 5 paths found
     - ⚠️ Needs improvement
   * - **Monitoring & Logging**
     - High
     - 8 paths found
     - ❌ Non-compliant

**Compliance Deliverables:**
- Executive summary with risk ratings
- Detailed technical findings
- Control effectiveness assessment
- Remediation roadmap with priorities
- Evidence package for auditors

Scenario 4: Incident Response Planning
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Security team needs to prepare incident response playbooks based on realistic attack scenarios and potential blast radius.

**Implementation:**

.. code-block:: python

   # Incident response scenario planning
   from app.services.incident_response_service import IncidentResponseService
   from app.models.incident_scenario import IncidentScenario
   
   ir_service = IncidentResponseService()
   
   # Define incident scenarios
   ransomware_scenario = IncidentScenario(
       name="Ransomware Attack",
       initial_compromise="email-server",
       attack_objectives=["data_encryption", "data_exfiltration"],
       threat_actor_profile="financially_motivated"
   )
   
   # Analyze potential blast radius
   blast_radius_analysis = ir_service.analyze_blast_radius(
       scenario=ransomware_scenario,
       include_business_impact=True,
       include_recovery_time=True,
       include_financial_impact=True
   )
   
   # Generate incident response playbook
   ir_playbook = ir_service.generate_playbook(
       scenario=ransomware_scenario,
       blast_radius=blast_radius_analysis,
       include_containment_steps=True,
       include_communication_plan=True,
       include_recovery_procedures=True
   )

**Incident Response Analysis:**

.. list-table:: Blast Radius Assessment
   :header-rows: 1
   :widths: 25 25 25 25

   * - Asset Category
     - Assets at Risk
     - Business Impact
     - Recovery Time
   * - **Critical Systems**
     - 12 assets
     - High
     - 24-48 hours
   * - **User Workstations**
     - 450 assets
     - Medium
     - 4-8 hours
   * - **File Servers**
     - 8 assets
     - High
     - 12-24 hours
   * - **Backup Systems**
     - 3 assets
     - Critical
     - 1-2 hours

**Playbook Components:**
- Threat detection indicators
- Containment procedures by asset type
- Communication templates
- Recovery prioritization matrix
- Lessons learned framework

Scenario 5: Zero Trust Architecture Planning
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Organization is implementing zero trust architecture and needs to understand current trust boundaries and attack paths.

**Implementation:**

.. code-block:: python

   # Zero trust analysis
   from app.services.zero_trust_service import ZeroTrustService
   from app.models.trust_boundary import TrustBoundary
   
   zt_service = ZeroTrustService()
   
   # Analyze current trust boundaries
   current_boundaries = zt_service.identify_trust_boundaries(
       include_network_segments=True,
       include_identity_boundaries=True,
       include_application_boundaries=True
   )
   
   # Analyze attack paths across trust boundaries
   boundary_analysis = zt_service.analyze_boundary_crossings(
       trust_boundaries=current_boundaries,
       include_privilege_escalation=True,
       include_lateral_movement=True
   )
   
   # Generate zero trust roadmap
   zt_roadmap = zt_service.generate_zero_trust_roadmap(
       current_state=boundary_analysis,
       target_maturity="advanced",
       implementation_phases=4,
       include_technology_recommendations=True
   )

**Zero Trust Analysis Results:**

.. list-table:: Trust Boundary Analysis
   :header-rows: 1
   :widths: 30 25 25 20

   * - Trust Boundary
     - Current State
     - Attack Paths
     - ZT Priority
   * - **Network Perimeter**
     - Traditional firewall
     - 15 paths
     - High
   * - **Identity Systems**
     - AD-based
     - 8 paths
     - Critical
   * - **Application Access**
     - VPN-based
     - 12 paths
     - High
   * - **Data Access**
     - Role-based
     - 6 paths
     - Medium

Advanced Analysis Features
--------------------------

Risk Scoring and Prioritization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Multi-Factor Risk Assessment:**

.. code-block:: python

   # Advanced risk scoring
   risk_factors = {
       "asset_criticality": 0.3,      # Business importance
       "vulnerability_severity": 0.25, # Technical risk
       "exposure_level": 0.2,          # Attack surface
       "control_effectiveness": 0.15,   # Security controls
       "threat_likelihood": 0.1         # Threat intelligence
   }
   
   def calculate_path_risk(attack_path, risk_factors):
       total_risk = 0.0
       
       for factor, weight in risk_factors.items():
           factor_score = attack_path.get_factor_score(factor)
           total_risk += factor_score * weight
       
       return min(total_risk, 10.0)  # Cap at 10.0

**Risk Prioritization Matrix:**

.. list-table:: Risk Prioritization Framework
   :header-rows: 1
   :widths: 20 20 20 20 20

   * - Risk Score
     - Priority
     - Response Time
     - Resources
     - Escalation
   * - **9.0-10.0**
     - Critical
     - Immediate
     - All available
     - C-level
   * - **7.0-8.9**
     - High
     - 24 hours
     - Dedicated team
     - Security leadership
   * - **5.0-6.9**
     - Medium
     - 1 week
     - Regular team
     - Team lead
   * - **3.0-4.9**
     - Low
     - 1 month
     - As available
     - None
   * - **0.0-2.9**
     - Informational
     - Next cycle
     - Background
     - None

MITRE ATT&CK Integration
~~~~~~~~~~~~~~~~~~~~~~~~

**Technique Mapping:**

.. code-block:: python

   # MITRE ATT&CK technique correlation
   def map_attack_path_to_mitre(attack_path):
       technique_mapping = []
       
       for step in attack_path.steps:
           techniques = mitre_service.get_techniques_for_step(
               source_asset_type=step.source.asset_type,
               target_asset_type=step.target.asset_type,
               attack_vector=step.attack_vector,
               privileges_required=step.privileges_required
           )
           
           technique_mapping.append({
               "step": step.id,
               "techniques": techniques,
               "tactic": techniques[0].tactic if techniques else None,
               "confidence": step.confidence_score
           })
       
       return technique_mapping

**Threat Actor Profiling:**

.. code-block:: python

   # Threat actor analysis
   def analyze_threat_actor_fit(attack_path, threat_actors):
       actor_scores = {}
       
       for actor in threat_actors:
           score = 0.0
           
           # Check technique overlap
           actor_techniques = set(actor.techniques)
           path_techniques = set(attack_path.mitre_techniques)
           technique_overlap = len(actor_techniques & path_techniques)
           
           # Check targeting preferences
           if attack_path.target_sector in actor.target_sectors:
               score += 2.0
           
           # Check sophistication level
           if attack_path.complexity_score <= actor.sophistication_level:
               score += 1.0
           
           actor_scores[actor.name] = score + technique_overlap
       
       return sorted(actor_scores.items(), key=lambda x: x[1], reverse=True)

Integration and Automation
--------------------------

SIEM Integration
~~~~~~~~~~~~~~~

.. code-block:: python

   # Automated SIEM rule generation
   def generate_detection_rules(attack_paths):
       detection_rules = []
       
       for path in attack_paths:
           for technique in path.mitre_techniques:
               rule = {
                   "name": f"Detect {technique.name}",
                   "technique_id": technique.id,
                   "severity": path.risk_score,
                   "query": technique.detection_query,
                   "false_positive_rate": technique.fp_rate,
                   "data_sources": technique.data_sources
               }
               detection_rules.append(rule)
       
       return detection_rules

SOAR Integration
~~~~~~~~~~~~~~~

.. code-block:: python

   # Automated response workflows
   def create_response_playbook(attack_path):
       playbook_steps = []
       
       for step in attack_path.steps:
           if step.risk_score > 7.0:
               playbook_steps.extend([
                   f"Isolate {step.source.name}",
                   f"Monitor {step.target.name}",
                   f"Alert security team",
                   f"Collect forensic evidence"
               ])
       
       return {
           "name": f"Response to {attack_path.name}",
           "trigger": f"Detection of {attack_path.initial_technique}",
           "steps": playbook_steps,
           "escalation": "<EMAIL>"
       }

Best Practices
--------------

Analysis Guidelines
~~~~~~~~~~~~~~~~~~

1. **Start with Crown Jewels** - Focus on protecting most critical assets
2. **Regular Analysis** - Run analysis after infrastructure changes
3. **Validate Results** - Cross-check with penetration testing
4. **Update Models** - Keep asset relationships current
5. **Collaborate** - Share results with relevant teams

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Limit Depth** - Use appropriate max_depth for analysis scope
2. **Filter Assets** - Focus on active and relevant assets
3. **Cache Results** - Cache frequently accessed analysis results
4. **Parallel Processing** - Use async analysis for multiple targets
5. **Monitor Resources** - Track analysis performance and resource usage

Next Steps
----------

For comprehensive attack path analysis:

1. **Define Use Cases** - Identify specific analysis scenarios for your organization
2. **Configure Assets** - Ensure complete and accurate asset inventory
3. **Validate Relationships** - Verify asset connections and dependencies
4. **Integrate Tools** - Connect with SIEM, SOAR, and other security tools
5. **Train Teams** - Educate security teams on analysis capabilities and interpretation

.. note::
   Attack path analysis is most effective when combined with threat intelligence,
   vulnerability management, and continuous monitoring. Regular analysis helps
   maintain an accurate understanding of your security posture.
