Asset Discovery Use Cases
==========================

Comprehensive guide to asset discovery capabilities in the Blast-Radius Security Tool, covering multi-cloud, on-premises, and hybrid environments.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Asset discovery is the foundation of effective security analysis. The Blast-Radius Security Tool provides comprehensive asset discovery capabilities across:

* **Multi-cloud environments** (AWS, Azure, GCP)
* **On-premises infrastructure** (network scanning, agent-based)
* **Hybrid environments** (cloud + on-premises)
* **API endpoints** (REST, GraphQL, SOAP)
* **Container environments** (Docker, Kubernetes)

Discovery Methods
-----------------

Cloud Provider Discovery
~~~~~~~~~~~~~~~~~~~~~~~~

**AWS Asset Discovery:**

.. code-block:: python

   # Configure AWS discovery
   from app.services.discovery import AWSDiscoveryService
   
   # Initialize discovery service
   aws_discovery = AWSDiscoveryService(
       access_key_id="your-access-key",
       secret_access_key="your-secret-key",
       regions=["us-east-1", "us-west-2", "eu-west-1"]
   )
   
   # Discover EC2 instances
   ec2_instances = aws_discovery.discover_ec2_instances()
   
   # Discover S3 buckets
   s3_buckets = aws_discovery.discover_s3_buckets()
   
   # Discover RDS databases
   rds_instances = aws_discovery.discover_rds_instances()

**Supported AWS Services:**

.. list-table:: AWS Service Coverage
   :header-rows: 1
   :widths: 30 30 40

   * - Service Category
     - Services
     - Discovery Details
   * - **Compute**
     - EC2, Lambda, ECS, EKS
     - Instances, functions, containers
   * - **Storage**
     - S3, EBS, EFS
     - Buckets, volumes, file systems
   * - **Database**
     - RDS, DynamoDB, ElastiCache
     - Instances, tables, clusters
   * - **Network**
     - VPC, ELB, CloudFront
     - Networks, load balancers, CDN
   * - **Security**
     - IAM, KMS, Secrets Manager
     - Users, keys, secrets

**Azure Asset Discovery:**

.. code-block:: bash

   # Configure Azure discovery via CLI
   blast-radius discovery azure \
     --client-id "your-client-id" \
     --client-secret "your-client-secret" \
     --tenant-id "your-tenant-id" \
     --subscription-id "your-subscription-id" \
     --resource-groups "rg-production,rg-staging"

**Google Cloud Discovery:**

.. code-block:: bash

   # Configure GCP discovery
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
   
   blast-radius discovery gcp \
     --project-id "your-project-id" \
     --regions "us-central1,us-east1" \
     --services "compute,storage,sql"

Network-Based Discovery
~~~~~~~~~~~~~~~~~~~~~~~

**Network Scanning Configuration:**

.. code-block:: yaml

   # discovery-config.yaml
   network_discovery:
     enabled: true
     scan_ranges:
       - "10.0.0.0/8"
       - "**********/12"
       - "***********/16"
     
     scan_options:
       port_scan: true
       service_detection: true
       os_detection: true
       vulnerability_scan: false
     
     nmap_options:
       - "-sS"  # SYN scan
       - "-O"   # OS detection
       - "-sV"  # Service version detection
       - "-T4"  # Timing template
     
     exclusions:
       - "***********"      # Router
       - "10.0.0.0/24"      # Management network

**Advanced Network Discovery:**

.. code-block:: python

   # Custom network discovery
   from app.services.discovery import NetworkDiscoveryService
   
   network_discovery = NetworkDiscoveryService()
   
   # Discover assets in specific subnet
   assets = network_discovery.scan_subnet(
       subnet="***********/24",
       ports=[22, 80, 443, 3389],
       timeout=30
   )
   
   # Passive discovery via network monitoring
   passive_assets = network_discovery.passive_discovery(
       interface="eth0",
       duration=3600  # 1 hour
   )

API Discovery
~~~~~~~~~~~~~

**REST API Discovery:**

.. code-block:: python

   # API endpoint discovery
   from app.services.discovery import APIDiscoveryService
   
   api_discovery = APIDiscoveryService()
   
   # Discover APIs using multiple methods
   discovered_apis = api_discovery.discover_apis(
       target="https://api.example.com",
       methods=["swagger", "openapi", "bruteforce"],
       wordlists=["/opt/wordlists/api-endpoints.txt"]
   )
   
   # Analyze API security
   for api in discovered_apis:
       security_analysis = api_discovery.analyze_api_security(api)
       print(f"API: {api.endpoint}, Security Score: {security_analysis.score}")

**Integration with API Discovery Tools:**

.. code-block:: bash

   # Akto integration
   blast-radius discovery api \
     --tool akto \
     --target "https://api.example.com" \
     --auth-token "your-api-token"
   
   # Kiterunner integration
   blast-radius discovery api \
     --tool kiterunner \
     --target "https://api.example.com" \
     --wordlist "/opt/wordlists/routes.txt"

Use Case Scenarios
------------------

Scenario 1: Multi-Cloud Asset Inventory
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Large enterprise with assets across AWS, Azure, and on-premises infrastructure needs comprehensive asset visibility.

**Implementation:**

.. code-block:: python

   # Multi-cloud discovery orchestration
   from app.services.discovery import MultiCloudDiscoveryOrchestrator
   
   orchestrator = MultiCloudDiscoveryOrchestrator()
   
   # Configure cloud providers
   orchestrator.add_provider("aws", {
       "access_key_id": "aws-key",
       "secret_access_key": "aws-secret",
       "regions": ["us-east-1", "us-west-2"]
   })
   
   orchestrator.add_provider("azure", {
       "client_id": "azure-client-id",
       "client_secret": "azure-secret",
       "tenant_id": "azure-tenant"
   })
   
   # Add on-premises networks
   orchestrator.add_network_range("10.0.0.0/8")
   orchestrator.add_network_range("**********/12")
   
   # Run comprehensive discovery
   discovery_job = orchestrator.start_discovery()
   
   # Monitor progress
   while not discovery_job.is_complete():
       status = discovery_job.get_status()
       print(f"Progress: {status.progress}%, Assets found: {status.assets_discovered}")
       time.sleep(30)

**Expected Results:**

.. list-table:: Discovery Results Example
   :header-rows: 1
   :widths: 25 25 25 25

   * - Environment
     - Assets Found
     - Asset Types
     - Discovery Time
   * - **AWS**
     - 1,247 assets
     - EC2, S3, RDS, Lambda
     - 15 minutes
   * - **Azure**
     - 892 assets
     - VMs, Storage, SQL
     - 12 minutes
   * - **On-Premises**
     - 2,156 assets
     - Servers, Workstations
     - 45 minutes
   * - **Total**
     - 4,295 assets
     - 58 asset types
     - 72 minutes

Scenario 2: Continuous Asset Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Security team needs real-time visibility into infrastructure changes for compliance and security monitoring.

**Implementation:**

.. code-block:: yaml

   # continuous-monitoring.yaml
   monitoring:
     enabled: true
     interval: 300  # 5 minutes
     
     triggers:
       - new_asset_discovered
       - asset_configuration_changed
       - asset_removed
       - security_posture_changed
     
     notifications:
       - type: webhook
         url: "https://your-siem.com/webhook"
       - type: email
         recipients: ["<EMAIL>"]
       - type: slack
         channel: "#security-alerts"
     
     change_detection:
       track_configuration: true
       track_relationships: true
       track_vulnerabilities: true
       baseline_comparison: true

**Monitoring Dashboard:**

.. code-block:: python

   # Real-time monitoring dashboard
   from app.services.monitoring import AssetMonitoringService
   
   monitoring = AssetMonitoringService()
   
   # Set up real-time alerts
   monitoring.create_alert_rule(
       name="New High-Risk Asset",
       condition="risk_score > 8.0 AND asset_age < 3600",
       actions=["email", "slack", "ticket"]
   )
   
   monitoring.create_alert_rule(
       name="Asset Configuration Drift",
       condition="configuration_changed AND baseline_deviation > 0.7",
       actions=["email", "audit_log"]
   )
   
   # Start monitoring
   monitoring.start_continuous_monitoring()

Scenario 3: Compliance Asset Reporting
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Business Context:**
Organization needs to generate compliance reports showing asset inventory for SOC 2, PCI DSS, and GDPR audits.

**Implementation:**

.. code-block:: python

   # Compliance reporting
   from app.services.compliance import ComplianceReportingService
   
   compliance = ComplianceReportingService()
   
   # Generate SOC 2 asset report
   soc2_report = compliance.generate_asset_report(
       framework="SOC2",
       scope=["production", "staging"],
       include_controls=True,
       format="pdf"
   )
   
   # Generate PCI DSS cardholder data environment report
   pci_report = compliance.generate_asset_report(
       framework="PCI_DSS",
       scope=["cardholder_data_environment"],
       include_network_segmentation=True,
       format="excel"
   )
   
   # Generate GDPR data processing inventory
   gdpr_report = compliance.generate_asset_report(
       framework="GDPR",
       scope=["data_processing_systems"],
       include_data_flows=True,
       format="json"
   )

**Report Contents:**

.. list-table:: Compliance Report Elements
   :header-rows: 1
   :widths: 30 35 35

   * - Framework
     - Required Information
     - Additional Details
   * - **SOC 2**
     - Asset inventory, controls, access
     - Change management, monitoring
   * - **PCI DSS**
     - CHD systems, network segmentation
     - Vulnerability scans, access logs
   * - **GDPR**
     - Data processing systems, flows
     - Data retention, privacy controls

Advanced Discovery Features
---------------------------

Agent-Based Discovery
~~~~~~~~~~~~~~~~~~~~~

**Lightweight Agent Deployment:**

.. code-block:: bash

   # Deploy discovery agent
   curl -sSL https://releases.blast-radius.com/agent/install.sh | bash
   
   # Configure agent
   blast-radius-agent configure \
     --server "https://your-blast-radius.com" \
     --token "agent-registration-token" \
     --tags "environment=production,team=security"
   
   # Start agent
   systemctl start blast-radius-agent
   systemctl enable blast-radius-agent

**Agent Capabilities:**

* **System Information:** OS, hardware, installed software
* **Network Configuration:** Interfaces, routes, firewall rules
* **Security Controls:** Antivirus, EDR, monitoring agents
* **Compliance Data:** Patch levels, configurations, certificates

Container Discovery
~~~~~~~~~~~~~~~~~~~

**Kubernetes Integration:**

.. code-block:: yaml

   # kubernetes-discovery.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: blast-radius-discovery
   data:
     config.yaml: |
       kubernetes:
         enabled: true
         namespaces: ["default", "production", "staging"]
         resources:
           - pods
           - services
           - deployments
           - configmaps
           - secrets
         
         security_scanning:
           image_vulnerabilities: true
           misconfigurations: true
           rbac_analysis: true

**Docker Discovery:**

.. code-block:: python

   # Docker container discovery
   from app.services.discovery import ContainerDiscoveryService
   
   container_discovery = ContainerDiscoveryService()
   
   # Discover running containers
   containers = container_discovery.discover_docker_containers()
   
   # Analyze container security
   for container in containers:
       security_analysis = container_discovery.analyze_container_security(container)
       if security_analysis.vulnerabilities:
           print(f"Container {container.name} has {len(security_analysis.vulnerabilities)} vulnerabilities")

Discovery Automation
--------------------

Scheduled Discovery
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Automated discovery scheduling
   from app.services.scheduler import DiscoveryScheduler
   
   scheduler = DiscoveryScheduler()
   
   # Schedule daily full discovery
   scheduler.schedule_discovery(
       name="daily_full_discovery",
       cron="0 2 * * *",  # 2 AM daily
       discovery_type="full",
       targets=["all_clouds", "all_networks"]
   )
   
   # Schedule hourly incremental discovery
   scheduler.schedule_discovery(
       name="hourly_incremental",
       cron="0 * * * *",  # Every hour
       discovery_type="incremental",
       targets=["aws", "azure"]
   )

Integration with External Tools
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**CMDB Synchronization:**

.. code-block:: python

   # ServiceNow CMDB integration
   from app.integrations.servicenow import ServiceNowCMDBSync
   
   cmdb_sync = ServiceNowCMDBSync(
       instance="your-instance.service-now.com",
       username="integration-user",
       password="password"
   )
   
   # Sync discovered assets to CMDB
   sync_results = cmdb_sync.sync_assets(
       asset_filter={"environment": "production"},
       update_existing=True,
       create_new=True
   )

**SIEM Integration:**

.. code-block:: python

   # Send discovery events to SIEM
   from app.integrations.siem import SIEMIntegration
   
   siem = SIEMIntegration(
       siem_type="splunk",
       endpoint="https://your-splunk.com:8088",
       token="your-hec-token"
   )
   
   # Send asset discovery events
   siem.send_discovery_events(
       events=discovery_results,
       source="blast-radius-discovery",
       sourcetype="asset_discovery"
   )

Best Practices
--------------

Discovery Strategy
~~~~~~~~~~~~~~~~~~

1. **Start Small:** Begin with a limited scope and expand gradually
2. **Prioritize Critical Assets:** Focus on production and sensitive systems first
3. **Regular Scheduling:** Implement continuous discovery for dynamic environments
4. **Validation:** Verify discovery results with known asset inventories
5. **Documentation:** Maintain clear documentation of discovery configurations

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Parallel Processing:** Use concurrent discovery for multiple targets
2. **Rate Limiting:** Respect API limits and network constraints
3. **Caching:** Cache results to avoid redundant discoveries
4. **Filtering:** Use filters to focus on relevant assets
5. **Monitoring:** Track discovery performance and optimize accordingly

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~~

1. **Least Privilege:** Use minimal required permissions for discovery
2. **Credential Management:** Securely store and rotate discovery credentials
3. **Network Segmentation:** Consider network access requirements
4. **Audit Logging:** Log all discovery activities for compliance
5. **Data Protection:** Encrypt discovery data in transit and at rest

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Discovery Timeouts:**

.. code-block:: bash

   # Increase timeout settings
   blast-radius config set discovery.timeout 300
   
   # Check network connectivity
   blast-radius discovery test-connectivity --target "aws"

**Permission Errors:**

.. code-block:: bash

   # Validate cloud credentials
   blast-radius discovery validate-credentials --provider aws
   
   # Check required permissions
   blast-radius discovery check-permissions --provider azure

**Performance Issues:**

.. code-block:: bash

   # Monitor discovery performance
   blast-radius discovery monitor --job-id "discovery-job-123"
   
   # Optimize discovery settings
   blast-radius config set discovery.parallel_workers 4
   blast-radius config set discovery.batch_size 100

Next Steps
----------

After implementing asset discovery:

1. **Configure Attack Path Analysis:** Use discovered assets for security analysis
2. **Set Up Monitoring:** Implement continuous asset monitoring
3. **Integrate with SIEM:** Send discovery data to security tools
4. **Generate Reports:** Create compliance and inventory reports
5. **Optimize Performance:** Fine-tune discovery settings for your environment

.. note::
   Asset discovery is an ongoing process. Regularly review and update your
   discovery configurations to ensure comprehensive coverage of your environment.
