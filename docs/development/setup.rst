Development Environment Setup
===============================

Complete guide for setting up a development environment for the Blast-Radius Security Tool, including all dependencies, tools, and configurations needed for effective development.

.. contents:: Table of Contents
   :local:
   :depth: 2

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

**Operating System:**
* Linux (Ubuntu 20.04+ recommended)
* macOS 10.15+
* Windows 10+ with WSL2

**Hardware Requirements:**
* **CPU:** 4+ cores (8+ recommended)
* **RAM:** 16GB minimum (32GB recommended)
* **Storage:** 100GB+ free space (SSD recommended)
* **Network:** Stable internet connection

Required Software
~~~~~~~~~~~~~~~~~

**Core Development Tools:**

.. code-block:: bash

   # Git (version control)
   git --version  # Should be 2.30+
   
   # Python (backend development)
   python3.11 --version  # Python 3.11 required
   
   # Node.js (frontend development)
   node --version  # Node.js 18+ required
   npm --version   # npm 8+ required
   
   # Docker (containerization)
   docker --version         # Docker 20.10+
   docker-compose --version # Docker Compose 2.0+

**Database Systems:**

.. code-block:: bash

   # PostgreSQL (primary database)
   psql --version  # PostgreSQL 15+
   
   # Redis (caching and sessions)
   redis-cli --version  # Redis 7.0+
   
   # Neo4j (graph database)
   # Will be installed via Docker

Installation Guide
------------------

Step 1: Clone Repository
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Clone the main repository
   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius
   
   # Set up git hooks
   git config core.hooksPath .githooks
   chmod +x .githooks/*

Step 2: Backend Setup
~~~~~~~~~~~~~~~~~~~~~

**Python Environment:**

.. code-block:: bash

   # Navigate to backend directory
   cd backend
   
   # Create virtual environment
   python3.11 -m venv venv
   
   # Activate virtual environment
   source venv/bin/activate  # Linux/macOS
   # OR
   venv\Scripts\activate     # Windows
   
   # Upgrade pip
   pip install --upgrade pip setuptools wheel

**Install Dependencies:**

.. code-block:: bash

   # Install production dependencies
   pip install -r requirements.txt
   
   # Install development dependencies
   pip install -r requirements-dev.txt
   
   # Install testing dependencies
   pip install -r requirements-test.txt
   
   # Install the application in development mode
   pip install -e .

**Environment Configuration:**

.. code-block:: bash

   # Copy environment template
   cp .env.example .env.development
   
   # Edit configuration
   nano .env.development

**Sample Development Configuration:**

.. code-block:: bash

   # .env.development
   
   # Application
   PROJECT_NAME="Blast-Radius Security Tool (Dev)"
   ENVIRONMENT="development"
   DEBUG=true
   LOG_LEVEL="debug"
   
   # Database URLs
   DATABASE_URL="postgresql://blast_dev:blast_dev@localhost:5432/blast_radius_dev"
   REDIS_URL="redis://localhost:6379/0"
   NEO4J_URI="bolt://localhost:7687"
   NEO4J_USER="neo4j"
   NEO4J_PASSWORD="blast_dev"
   
   # Security (development keys - change for production)
   SECRET_KEY="dev-secret-key-change-in-production"
   JWT_SECRET_KEY="dev-jwt-secret-key-change-in-production"
   
   # Development settings
   CORS_ORIGINS='["http://localhost:3000", "http://127.0.0.1:3000"]'
   DOCS_URL="/docs"
   REDOC_URL="/redoc"

Step 3: Database Setup
~~~~~~~~~~~~~~~~~~~~~~

**Start Database Services:**

.. code-block:: bash

   # Start databases using Docker Compose
   docker-compose -f docker-compose.dev.yml up -d postgresql redis neo4j
   
   # Wait for services to be ready
   docker-compose -f docker-compose.dev.yml logs -f

**Initialize Databases:**

.. code-block:: bash

   # Run database migrations
   alembic upgrade head
   
   # Create initial admin user
   python -m app.cli create-admin \
     --email <EMAIL> \
     --password DevPassword123! \
     --first-name Admin \
     --last-name User
   
   # Load sample data (optional)
   python -m app.cli load-sample-data

Step 4: Frontend Setup
~~~~~~~~~~~~~~~~~~~~~~

**Node.js Environment:**

.. code-block:: bash

   # Navigate to frontend directory
   cd ../frontend
   
   # Install dependencies
   npm install
   
   # Or use yarn if preferred
   yarn install

**Environment Configuration:**

.. code-block:: bash

   # Copy environment template
   cp .env.example .env.development.local
   
   # Edit configuration
   nano .env.development.local

**Sample Frontend Configuration:**

.. code-block:: bash

   # .env.development.local
   
   # API Configuration
   REACT_APP_API_URL=http://localhost:8000
   REACT_APP_API_VERSION=v1
   
   # Development settings
   REACT_APP_ENVIRONMENT=development
   REACT_APP_DEBUG=true
   
   # Feature flags
   REACT_APP_ENABLE_MOCK_DATA=true
   REACT_APP_ENABLE_DEBUG_TOOLS=true

Step 5: Development Tools Setup
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Code Editor Configuration (VS Code):**

.. code-block:: json

   // .vscode/settings.json
   {
     "python.defaultInterpreterPath": "./backend/venv/bin/python",
     "python.linting.enabled": true,
     "python.linting.pylintEnabled": false,
     "python.linting.flake8Enabled": true,
     "python.linting.banditEnabled": true,
     "python.formatting.provider": "black",
     "python.sortImports.args": ["--profile", "black"],
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.organizeImports": true
     },
     "files.exclude": {
       "**/__pycache__": true,
       "**/*.pyc": true,
       "**/node_modules": true,
       "**/build": true,
       "**/dist": true
     }
   }

**Recommended VS Code Extensions:**

.. code-block:: json

   // .vscode/extensions.json
   {
     "recommendations": [
       "ms-python.python",
       "ms-python.flake8",
       "ms-python.black-formatter",
       "ms-python.isort",
       "bradlc.vscode-tailwindcss",
       "esbenp.prettier-vscode",
       "ms-vscode.vscode-typescript-next",
       "ms-vscode.vscode-json",
       "redhat.vscode-yaml",
       "ms-vscode-remote.remote-containers"
     ]
   }

**Pre-commit Hooks:**

.. code-block:: bash

   # Install pre-commit
   pip install pre-commit
   
   # Install git hooks
   pre-commit install
   
   # Run on all files (optional)
   pre-commit run --all-files

Development Workflow
--------------------

Starting Development Services
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Backend Development:**

.. code-block:: bash

   # Terminal 1: Start backend services
   cd backend
   source venv/bin/activate
   
   # Start development server with auto-reload
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   
   # Or use the Makefile
   make run-dev

**Frontend Development:**

.. code-block:: bash

   # Terminal 2: Start frontend development server
   cd frontend
   npm start
   
   # Or use yarn
   yarn start

**Database Services:**

.. code-block:: bash

   # Terminal 3: Monitor database logs
   docker-compose -f docker-compose.dev.yml logs -f

**Access Points:**

* **Frontend:** http://localhost:3000
* **Backend API:** http://localhost:8000
* **API Documentation:** http://localhost:8000/docs
* **Database Admin:** http://localhost:5050 (pgAdmin)

Code Quality Tools
~~~~~~~~~~~~~~~~~~

**Linting and Formatting:**

.. code-block:: bash

   # Backend code quality
   cd backend
   
   # Format code
   black app/ tests/
   isort app/ tests/
   
   # Lint code
   flake8 app/ tests/
   pylint app/
   
   # Security scanning
   bandit -r app/
   safety check

**Frontend Code Quality:**

.. code-block:: bash

   # Frontend code quality
   cd frontend
   
   # Format code
   npm run format
   
   # Lint code
   npm run lint
   
   # Type checking
   npm run type-check

Testing in Development
~~~~~~~~~~~~~~~~~~~~~~

**Backend Testing:**

.. code-block:: bash

   cd backend
   
   # Run all tests
   pytest
   
   # Run with coverage
   pytest --cov=app --cov-report=html
   
   # Run specific test categories
   pytest tests/unit/          # Unit tests
   pytest tests/integration/   # Integration tests
   pytest -m "not slow"        # Skip slow tests
   
   # Run tests in watch mode
   ptw -- --testmon

**Frontend Testing:**

.. code-block:: bash

   cd frontend
   
   # Run all tests
   npm test
   
   # Run tests in watch mode
   npm test -- --watch
   
   # Run tests with coverage
   npm test -- --coverage

Database Management
~~~~~~~~~~~~~~~~~~~

**Common Database Tasks:**

.. code-block:: bash

   # Create new migration
   alembic revision --autogenerate -m "Description of changes"
   
   # Apply migrations
   alembic upgrade head
   
   # Rollback migration
   alembic downgrade -1
   
   # Reset database (development only)
   python -m app.cli reset-database --confirm
   
   # Backup database
   pg_dump blast_radius_dev > backup.sql
   
   # Restore database
   psql blast_radius_dev < backup.sql

Debugging
---------

Backend Debugging
~~~~~~~~~~~~~~~~~

**VS Code Debug Configuration:**

.. code-block:: json

   // .vscode/launch.json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Python: FastAPI",
         "type": "python",
         "request": "launch",
         "program": "${workspaceFolder}/backend/venv/bin/uvicorn",
         "args": [
           "app.main:app",
           "--reload",
           "--host",
           "0.0.0.0",
           "--port",
           "8000"
         ],
         "cwd": "${workspaceFolder}/backend",
         "env": {
           "PYTHONPATH": "${workspaceFolder}/backend"
         },
         "console": "integratedTerminal"
       }
     ]
   }

**Debug with pdb:**

.. code-block:: python

   # Add breakpoint in code
   import pdb; pdb.set_trace()
   
   # Or use ipdb for better experience
   import ipdb; ipdb.set_trace()

Frontend Debugging
~~~~~~~~~~~~~~~~~~~

**Browser DevTools:**

* **React DevTools:** Install browser extension
* **Redux DevTools:** For state management debugging
* **Network Tab:** Monitor API calls
* **Console:** Check for JavaScript errors

**VS Code Debugging:**

.. code-block:: json

   // .vscode/launch.json (add to configurations)
   {
     "name": "Launch Chrome",
     "type": "pwa-chrome",
     "request": "launch",
     "url": "http://localhost:3000",
     "webRoot": "${workspaceFolder}/frontend/src"
   }

Performance Profiling
~~~~~~~~~~~~~~~~~~~~~~

**Backend Profiling:**

.. code-block:: python

   # Profile with cProfile
   python -m cProfile -o profile.stats app/main.py
   
   # Analyze with snakeviz
   snakeviz profile.stats

**Database Query Analysis:**

.. code-block:: sql

   -- Enable query logging
   ALTER SYSTEM SET log_statement = 'all';
   SELECT pg_reload_conf();
   
   -- Analyze slow queries
   SELECT query, mean_time, calls
   FROM pg_stat_statements
   ORDER BY mean_time DESC
   LIMIT 10;

Common Development Tasks
------------------------

Adding New Features
~~~~~~~~~~~~~~~~~~~

**Backend Feature Development:**

.. code-block:: bash

   # 1. Create feature branch
   git checkout -b feature/new-feature-name
   
   # 2. Create database migration (if needed)
   alembic revision --autogenerate -m "Add new feature tables"
   
   # 3. Implement models, services, and APIs
   # 4. Write tests
   # 5. Update documentation

**Frontend Feature Development:**

.. code-block:: bash

   # 1. Create components
   # 2. Add routes
   # 3. Implement state management
   # 4. Write tests
   # 5. Update documentation

API Development
~~~~~~~~~~~~~~~

**Creating New Endpoints:**

.. code-block:: python

   # app/api/v1/endpoints/new_feature.py
   from fastapi import APIRouter, Depends
   from app.api.deps import get_current_user
   from app.schemas.new_feature import NewFeatureCreate, NewFeatureResponse
   
   router = APIRouter()
   
   @router.post("/", response_model=NewFeatureResponse)
   def create_new_feature(
       feature: NewFeatureCreate,
       current_user = Depends(get_current_user)
   ):
       # Implementation
       pass

**Testing APIs:**

.. code-block:: bash

   # Test with curl
   curl -X POST http://localhost:8000/api/v1/new-feature \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer $TOKEN" \
     -d '{"name": "test"}'
   
   # Or use HTTPie
   http POST localhost:8000/api/v1/new-feature name=test Authorization:"Bearer $TOKEN"

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Port Already in Use:**

.. code-block:: bash

   # Find process using port
   lsof -i :8000
   
   # Kill process
   kill -9 <PID>

**Database Connection Issues:**

.. code-block:: bash

   # Check if PostgreSQL is running
   docker-compose -f docker-compose.dev.yml ps postgresql
   
   # Check logs
   docker-compose -f docker-compose.dev.yml logs postgresql
   
   # Restart database
   docker-compose -f docker-compose.dev.yml restart postgresql

**Python Import Issues:**

.. code-block:: bash

   # Check Python path
   python -c "import sys; print(sys.path)"
   
   # Reinstall in development mode
   pip install -e .

**Node.js Issues:**

.. code-block:: bash

   # Clear npm cache
   npm cache clean --force
   
   # Delete node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install

Getting Help
~~~~~~~~~~~~

**Documentation:**
* API Documentation: http://localhost:8000/docs
* This developer guide
* Code comments and docstrings

**Community:**
* GitHub Issues: Report bugs and request features
* Discussions: Ask questions and share ideas

**Development Tools:**
* Use IDE debugging capabilities
* Check application logs
* Monitor database queries
* Use browser developer tools

Next Steps
----------

After setting up your development environment:

1. **Explore the codebase** - Understand the architecture
2. **Run the test suite** - Ensure everything works
3. **Make a small change** - Test your setup
4. **Read the contributing guide** - :doc:`contributing`
5. **Check coding standards** - :doc:`code-standards`

.. note::
   Keep your development environment updated by regularly pulling changes
   and updating dependencies. Use `git pull` and `pip install -r requirements-dev.txt`
   to stay current.
