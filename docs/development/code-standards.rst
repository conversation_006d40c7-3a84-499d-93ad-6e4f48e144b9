Code Standards & Style Guide
=============================

Comprehensive coding standards and style guidelines for the Blast-Radius Security Tool to ensure consistent, maintainable, and secure code across the entire project.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Code standards ensure:

* **Consistency** - Uniform code style across the project
* **Readability** - Code that is easy to understand and maintain
* **Security** - Secure coding practices and vulnerability prevention
* **Performance** - Efficient and optimized code
* **Maintainability** - Code that is easy to modify and extend

General Principles
------------------

Core Principles
~~~~~~~~~~~~~~~

1. **Clarity over Cleverness** - Write code that is easy to understand
2. **Consistency** - Follow established patterns and conventions
3. **Security First** - Consider security implications in all code
4. **Performance Awareness** - Write efficient code without premature optimization
5. **Documentation** - Document complex logic and public interfaces
6. **Testing** - Write testable code with comprehensive test coverage

SOLID Principles
~~~~~~~~~~~~~~~~

.. list-table:: SOLID Principles Application
   :header-rows: 1
   :widths: 20 80

   * - Principle
     - Application in Blast-Radius
   * - **Single Responsibility**
     - Each class/function has one reason to change
   * - **Open/Closed**
     - Open for extension, closed for modification
   * - **Liskov Substitution**
     - Derived classes must be substitutable for base classes
   * - **Interface Segregation**
     - Many specific interfaces better than one general interface
   * - **Dependency Inversion**
     - Depend on abstractions, not concretions

Python Standards
----------------

Code Style
~~~~~~~~~~

We follow **PEP 8** with these specific configurations:

.. code-block:: python

   # pyproject.toml
   [tool.black]
   line-length = 88
   target-version = ['py311']
   include = '\.pyi?$'

   [tool.isort]
   profile = "black"
   multi_line_output = 3
   line_length = 88

   [tool.flake8]
   max-line-length = 88
   extend-ignore = ["E203", "W503"]
   max-complexity = 10

**Naming Conventions:**

.. code-block:: python

   # Classes - PascalCase
   class AttackPathAnalyzer:
       pass

   # Functions and variables - snake_case
   def analyze_attack_path():
       source_asset_id = 123
       return attack_path_result

   # Constants - UPPER_SNAKE_CASE
   MAX_ATTACK_PATH_DEPTH = 10
   DEFAULT_TIMEOUT_SECONDS = 30

   # Private methods - leading underscore
   def _validate_input(self, data):
       pass

   # Protected methods - single underscore
   def _internal_method(self):
       pass

**Type Annotations:**

.. code-block:: python

   from typing import List, Dict, Optional, Union
   from app.models import Asset, AttackPath

   def analyze_attack_paths(
       source_asset: Asset,
       target_assets: List[Asset],
       max_depth: int = 10,
       include_mitre: bool = True
   ) -> List[AttackPath]:
       """Analyze attack paths between source and target assets.

       Args:
           source_asset: Starting point for analysis
           target_assets: List of potential target assets
           max_depth: Maximum path depth to explore
           include_mitre: Whether to include MITRE ATT&CK mapping

       Returns:
           List of discovered attack paths

       Raises:
           ValidationError: If input validation fails
           AnalysisError: If analysis cannot be completed
       """
       pass

**Error Handling:**

.. code-block:: python

   # Good - Specific exceptions with context
   class AttackPathError(Exception):
       """Base exception for attack path analysis."""
       pass

   class ValidationError(AttackPathError):
       """Raised when input validation fails."""
       pass

   class AnalysisError(AttackPathError):
       """Raised when analysis cannot be completed."""
       pass

   def analyze_path(source: Asset, target: Asset) -> AttackPath:
       if not source or not target:
           raise ValidationError("Source and target assets are required")

       try:
           result = perform_analysis(source, target)
           if not result:
               raise AnalysisError("No attack paths found")
           return result
       except DatabaseError as e:
           logger.error(f"Database error during analysis: {e}")
           raise AnalysisError(f"Analysis failed due to database error: {e}")

**Logging:**

.. code-block:: python

   import logging
   import structlog

   # Use structured logging
   logger = structlog.get_logger(__name__)

   def process_asset_discovery(asset_id: int) -> None:
       logger.info(
           "Starting asset discovery",
           asset_id=asset_id,
           operation="asset_discovery"
       )

       try:
           result = discover_asset(asset_id)
           logger.info(
               "Asset discovery completed",
               asset_id=asset_id,
               assets_found=len(result.assets),
               duration_ms=result.duration
           )
       except Exception as e:
           logger.error(
               "Asset discovery failed",
               asset_id=asset_id,
               error=str(e),
               error_type=type(e).__name__
           )
           raise

Security Standards
~~~~~~~~~~~~~~~~~~

**Input Validation:**

.. code-block:: python

   from pydantic import BaseModel, validator
   import re

   class AssetCreateRequest(BaseModel):
       name: str
       ip_address: str
       asset_type: str

       @validator('name')
       def validate_name(cls, v):
           if not v or len(v.strip()) == 0:
               raise ValueError('Name cannot be empty')
           if len(v) > 255:
               raise ValueError('Name too long')
           # Prevent XSS
           if re.search(r'[<>"\']', v):
               raise ValueError('Name contains invalid characters')
           return v.strip()

       @validator('ip_address')
       def validate_ip(cls, v):
           import ipaddress
           try:
               ipaddress.ip_address(v)
           except ValueError:
               raise ValueError('Invalid IP address format')
           return v

**SQL Injection Prevention:**

.. code-block:: python

   # Good - Use parameterized queries
   def get_assets_by_type(asset_type: str) -> List[Asset]:
       query = """
           SELECT id, name, ip_address, asset_type
           FROM assets
           WHERE asset_type = %s AND is_active = true
       """
       return db.execute(query, (asset_type,)).fetchall()

   # Good - Use ORM
   def get_assets_by_type_orm(asset_type: str) -> List[Asset]:
       return session.query(Asset).filter(
           Asset.asset_type == asset_type,
           Asset.is_active == True
       ).all()

**Authentication & Authorization:**

.. code-block:: python

   from functools import wraps
   from app.auth import get_current_user, check_permission

   def require_permission(permission: str):
       def decorator(func):
           @wraps(func)
           def wrapper(*args, **kwargs):
               current_user = get_current_user()
               if not check_permission(current_user, permission):
                   raise PermissionError(f"Permission '{permission}' required")
               return func(*args, **kwargs)
           return wrapper
       return decorator

   @require_permission('asset:read')
   def get_asset(asset_id: int) -> Asset:
       return Asset.get_by_id(asset_id)

JavaScript/TypeScript Standards
-------------------------------

Code Style
~~~~~~~~~~

**ESLint Configuration:**

.. code-block:: json

   {
     "extends": [
       "@typescript-eslint/recommended",
       "react-hooks/recommended",
       "prettier"
     ],
     "rules": {
       "@typescript-eslint/no-unused-vars": "error",
       "@typescript-eslint/explicit-function-return-type": "warn",
       "react-hooks/exhaustive-deps": "error",
       "prefer-const": "error",
       "no-var": "error"
     }
   }

**Naming Conventions:**

.. code-block:: typescript

   // Interfaces - PascalCase with 'I' prefix (optional)
   interface AttackPathResult {
     paths: AttackPath[];
     riskScore: number;
   }

   // Types - PascalCase
   type AssetType = 'server' | 'database' | 'workstation';

   // Components - PascalCase
   const AttackPathVisualization: React.FC<Props> = ({ data }) => {
     return <div>{/* Component content */}</div>;
   };

   // Functions and variables - camelCase
   const analyzeAttackPath = async (sourceId: number): Promise<AttackPath[]> => {
     const analysisResult = await api.analyzeAttackPath(sourceId);
     return analysisResult.paths;
   };

   // Constants - UPPER_SNAKE_CASE
   const MAX_RETRY_ATTEMPTS = 3;
   const API_BASE_URL = process.env.REACT_APP_API_URL;

**React Component Standards:**

.. code-block:: typescript

   import React, { useState, useCallback, useEffect } from 'react';
   import { AttackPath, Asset } from '../types';
   import { attackPathService } from '../services';

   interface AttackPathAnalyzerProps {
     sourceAsset: Asset;
     targetAsset: Asset;
     onAnalysisComplete: (paths: AttackPath[]) => void;
     className?: string;
   }

   export const AttackPathAnalyzer: React.FC<AttackPathAnalyzerProps> = ({
     sourceAsset,
     targetAsset,
     onAnalysisComplete,
     className = ''
   }) => {
     const [isAnalyzing, setIsAnalyzing] = useState(false);
     const [error, setError] = useState<string | null>(null);

     const handleAnalysis = useCallback(async () => {
       if (!sourceAsset || !targetAsset) {
         setError('Source and target assets are required');
         return;
       }

       setIsAnalyzing(true);
       setError(null);

       try {
         const paths = await attackPathService.analyze({
           sourceAssetId: sourceAsset.id,
           targetAssetId: targetAsset.id
         });
         onAnalysisComplete(paths);
       } catch (err) {
         const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
         setError(errorMessage);
       } finally {
         setIsAnalyzing(false);
       }
     }, [sourceAsset, targetAsset, onAnalysisComplete]);

     useEffect(() => {
       // Auto-analyze when assets change
       if (sourceAsset && targetAsset) {
         handleAnalysis();
       }
     }, [sourceAsset?.id, targetAsset?.id, handleAnalysis]);

     return (
       <div className={`attack-path-analyzer ${className}`}>
         {/* Component JSX */}
       </div>
     );
   };

**Error Handling:**

.. code-block:: typescript

   // Custom error classes
   class ApiError extends Error {
     constructor(
       message: string,
       public statusCode: number,
       public response?: any
     ) {
       super(message);
       this.name = 'ApiError';
     }
   }

   // Error boundary component
   class ErrorBoundary extends React.Component<Props, State> {
     constructor(props: Props) {
       super(props);
       this.state = { hasError: false, error: null };
     }

     static getDerivedStateFromError(error: Error): State {
       return { hasError: true, error };
     }

     componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
       console.error('Error caught by boundary:', error, errorInfo);
       // Send to error reporting service
     }

     render() {
       if (this.state.hasError) {
         return <ErrorFallback error={this.state.error} />;
       }

       return this.props.children;
     }
   }

Database Standards
------------------

Schema Design
~~~~~~~~~~~~~

**Naming Conventions:**

.. code-block:: sql

   -- Tables - plural, snake_case
   CREATE TABLE attack_paths (
       id BIGSERIAL PRIMARY KEY,
       source_asset_id BIGINT NOT NULL,
       target_asset_id BIGINT NOT NULL,
       risk_score DECIMAL(3,2) NOT NULL,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Indexes - descriptive names
   CREATE INDEX idx_attack_paths_source_asset ON attack_paths(source_asset_id);
   CREATE INDEX idx_attack_paths_risk_score ON attack_paths(risk_score DESC);

   -- Foreign keys - descriptive names
   ALTER TABLE attack_paths
   ADD CONSTRAINT fk_attack_paths_source_asset
   FOREIGN KEY (source_asset_id) REFERENCES assets(id);

**Migration Standards:**

.. code-block:: python

   """Add attack path analysis tables

   Revision ID: 001_attack_paths
   Revises: 000_initial
   Create Date: 2024-01-15 10:30:00.000000
   """

   from alembic import op
   import sqlalchemy as sa

   def upgrade():
       # Create table with all constraints
       op.create_table(
           'attack_paths',
           sa.Column('id', sa.BigInteger(), nullable=False),
           sa.Column('source_asset_id', sa.BigInteger(), nullable=False),
           sa.Column('target_asset_id', sa.BigInteger(), nullable=False),
           sa.Column('risk_score', sa.Numeric(3, 2), nullable=False),
           sa.Column('created_at', sa.DateTime(timezone=True),
                    server_default=sa.text('NOW()'), nullable=False),
           sa.Column('updated_at', sa.DateTime(timezone=True),
                    server_default=sa.text('NOW()'), nullable=False),
           sa.PrimaryKeyConstraint('id'),
           sa.ForeignKeyConstraint(['source_asset_id'], ['assets.id']),
           sa.ForeignKeyConstraint(['target_asset_id'], ['assets.id'])
       )

       # Create indexes
       op.create_index('idx_attack_paths_source_asset', 'attack_paths', ['source_asset_id'])
       op.create_index('idx_attack_paths_risk_score', 'attack_paths', ['risk_score'])

   def downgrade():
       op.drop_table('attack_paths')

API Standards
-------------

REST API Design
~~~~~~~~~~~~~~~

**URL Structure:**

.. code-block:: text

   # Good - RESTful URLs
   GET    /api/v1/assets                    # List assets
   POST   /api/v1/assets                    # Create asset
   GET    /api/v1/assets/{id}               # Get specific asset
   PUT    /api/v1/assets/{id}               # Update asset
   DELETE /api/v1/assets/{id}               # Delete asset

   # Nested resources
   GET    /api/v1/assets/{id}/attack-paths  # Get attack paths for asset
   POST   /api/v1/assets/{id}/scan          # Trigger scan for asset

   # Bad - Non-RESTful URLs
   GET    /api/v1/getAssets
   POST   /api/v1/createAsset
   GET    /api/v1/asset?action=get&id=123

**Response Format:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": 123,
       "name": "web-server-01",
       "type": "server",
       "ip_address": "*************"
     },
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z",
       "version": "1.0.0"
     }
   }

   // Error response
   {
     "success": false,
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid asset type provided",
       "details": {
         "field": "type",
         "allowed_values": ["server", "database", "workstation"]
       }
     },
     "meta": {
       "timestamp": "2024-01-15T10:30:00Z",
       "request_id": "req_123456789"
     }
   }

**Status Codes:**

.. list-table:: HTTP Status Code Usage
   :header-rows: 1
   :widths: 15 25 60

   * - Code
     - Usage
     - Example
   * - **200**
     - Successful GET, PUT
     - Asset retrieved/updated successfully
   * - **201**
     - Successful POST
     - Asset created successfully
   * - **204**
     - Successful DELETE
     - Asset deleted successfully
   * - **400**
     - Bad Request
     - Invalid input data
   * - **401**
     - Unauthorized
     - Missing or invalid authentication
   * - **403**
     - Forbidden
     - Insufficient permissions
   * - **404**
     - Not Found
     - Asset does not exist
   * - **409**
     - Conflict
     - Asset already exists
   * - **422**
     - Validation Error
     - Input validation failed
   * - **500**
     - Internal Error
     - Unexpected server error

Documentation Standards
-----------------------

Code Documentation
~~~~~~~~~~~~~~~~~~

**Python Docstrings:**

.. code-block:: python

   def analyze_attack_path(
       source_asset: Asset,
       target_asset: Asset,
       max_depth: int = 10,
       include_mitre: bool = True
   ) -> AttackPathResult:
       """Analyze attack paths between two assets.

       This function performs graph-based analysis to discover potential
       attack paths from a source asset to a target asset, optionally
       including MITRE ATT&CK technique mapping.

       Args:
           source_asset: The starting point for attack path analysis.
               Must be a valid Asset instance with an ID.
           target_asset: The target asset to reach. Must be a valid
               Asset instance with an ID.
           max_depth: Maximum number of hops to explore in the attack
               path. Higher values increase analysis time but may find
               more complex paths. Defaults to 10.
           include_mitre: Whether to include MITRE ATT&CK technique
               mapping in the results. Defaults to True.

       Returns:
           AttackPathResult containing:
               - paths: List of discovered attack paths
               - risk_scores: Risk assessment for each path
               - mitre_techniques: MITRE ATT&CK techniques (if enabled)
               - analysis_metadata: Timing and performance data

       Raises:
           ValidationError: If source_asset or target_asset is invalid
           AnalysisError: If the analysis cannot be completed
           TimeoutError: If analysis exceeds maximum time limit

       Example:
           >>> source = Asset.get_by_id(1)
           >>> target = Asset.get_by_id(2)
           >>> result = analyze_attack_path(source, target, max_depth=5)
           >>> print(f"Found {len(result.paths)} attack paths")

       Note:
           This function may take significant time for complex networks.
           Consider using async version for web applications.
       """
       pass

**TypeScript Documentation:**

.. code-block:: typescript

   /**
    * Analyzes attack paths between assets using graph algorithms.
    *
    * @param sourceAssetId - ID of the source asset
    * @param targetAssetId - ID of the target asset
    * @param options - Analysis configuration options
    * @returns Promise resolving to attack path analysis results
    *
    * @throws {ValidationError} When asset IDs are invalid
    * @throws {ApiError} When API request fails
    *
    * @example
    * ```typescript
    * const result = await analyzeAttackPath(1, 2, {
    *   maxDepth: 5,
    *   includeMitre: true
    * });
    * console.log(`Found ${result.paths.length} paths`);
    * ```
    */
   export async function analyzeAttackPath(
     sourceAssetId: number,
     targetAssetId: number,
     options: AnalysisOptions = {}
   ): Promise<AttackPathResult> {
     // Implementation
   }

Testing Standards
-----------------

Test Organization
~~~~~~~~~~~~~~~~~

.. code-block:: text

   tests/
   ├── unit/                    # Unit tests
   │   ├── services/           # Service layer tests
   │   ├── models/             # Model tests
   │   └── utils/              # Utility function tests
   ├── integration/            # Integration tests
   │   ├── api/                # API endpoint tests
   │   ├── database/           # Database integration tests
   │   └── external/           # External service tests
   ├── e2e/                    # End-to-end tests
   │   ├── user_workflows/     # Complete user workflows
   │   └── api_workflows/      # API workflow tests
   ├── performance/            # Performance tests
   └── security/               # Security tests

**Test Naming:**

.. code-block:: python

   # Good test names - describe what they test
   def test_analyze_attack_path_returns_valid_paths_for_connected_assets():
       pass

   def test_analyze_attack_path_raises_validation_error_for_invalid_assets():
       pass

   def test_analyze_attack_path_respects_max_depth_parameter():
       pass

   # Bad test names - not descriptive
   def test_attack_path():
       pass

   def test_analysis():
       pass

Quality Assurance
-----------------

Automated Checks
~~~~~~~~~~~~~~~~

**Pre-commit Hooks:**

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: https://github.com/psf/black
       rev: 23.3.0
       hooks:
         - id: black
           language_version: python3.11

     - repo: https://github.com/pycqa/isort
       rev: 5.12.0
       hooks:
         - id: isort
           args: ["--profile", "black"]

     - repo: https://github.com/pycqa/flake8
       rev: 6.0.0
       hooks:
         - id: flake8
           additional_dependencies: [flake8-docstrings]

     - repo: https://github.com/pre-commit/mirrors-mypy
       rev: v1.3.0
       hooks:
         - id: mypy
           additional_dependencies: [types-requests]

**CI/CD Quality Gates:**

.. code-block:: yaml

   # Quality requirements for merge
   quality_gates:
     code_coverage: 90%
     security_scan: pass
     performance_regression: none
     documentation_coverage: 95%
     type_coverage: 85%

Tools and Configuration
-----------------------

Development Tools
~~~~~~~~~~~~~~~~~

**Required Tools:**

.. code-block:: bash

   # Python tools
   pip install black isort flake8 mypy bandit safety

   # JavaScript/TypeScript tools
   npm install -g eslint prettier typescript

   # Documentation tools
   pip install sphinx sphinx-rtd-theme

   # Testing tools
   pip install pytest pytest-cov pytest-mock

**IDE Configuration:**

.. code-block:: json

   // VS Code settings.json
   {
     "python.defaultInterpreterPath": "./venv/bin/python",
     "python.linting.enabled": true,
     "python.linting.flake8Enabled": true,
     "python.formatting.provider": "black",
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.organizeImports": true
     }
   }

Enforcement
-----------

These standards are enforced through:

1. **Automated tools** - Linters, formatters, and type checkers
2. **CI/CD pipeline** - Quality gates in continuous integration
3. **Code review** - Manual review process for all changes
4. **Documentation** - Clear guidelines and examples
5. **Training** - Onboarding and ongoing education

For questions about these standards or suggestions for improvements, please create an issue or start a discussion in the project repository.

.. note::
   These standards are living documents that evolve with the project.
   Regular reviews ensure they remain relevant and effective.