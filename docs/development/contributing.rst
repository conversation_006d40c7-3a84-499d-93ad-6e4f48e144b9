Contributing Guide
==================

Welcome to the Blast-Radius Security Tool project! This guide will help you get started with contributing to our open-source security platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Getting Started
---------------

Ways to Contribute
~~~~~~~~~~~~~~~~~~

There are many ways to contribute to the Blast-Radius Security Tool:

* **🐛 Bug Reports** - Help us identify and fix issues
* **💡 Feature Requests** - Suggest new capabilities and improvements
* **📝 Documentation** - Improve guides, tutorials, and API documentation
* **🧪 Testing** - Write tests and improve test coverage
* **💻 Code Contributions** - Implement features and fix bugs
* **🎨 UI/UX Improvements** - Enhance user interface and experience
* **🔒 Security Reviews** - Help identify and fix security vulnerabilities
* **🌐 Translations** - Localize the platform for different languages

Before You Start
~~~~~~~~~~~~~~~~~

1. **Read the Code of Conduct** - We maintain a welcoming, inclusive community
2. **Check existing issues** - Avoid duplicate work by reviewing open issues
3. **Join discussions** - Participate in GitHub Discussions for questions
4. **Set up development environment** - Follow the :doc:`setup` guide

Contribution Workflow
---------------------

Step 1: Find or Create an Issue
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**For Bug Reports:**

.. code-block:: markdown

   ## Bug Report Template
   
   **Describe the bug**
   A clear description of what the bug is.
   
   **To Reproduce**
   Steps to reproduce the behavior:
   1. Go to '...'
   2. Click on '....'
   3. Scroll down to '....'
   4. See error
   
   **Expected behavior**
   What you expected to happen.
   
   **Screenshots**
   If applicable, add screenshots.
   
   **Environment:**
   - OS: [e.g. Ubuntu 20.04]
   - Docker version: [e.g. 20.10.7]
   - Browser: [e.g. Chrome 91.0]
   
   **Additional context**
   Any other context about the problem.

**For Feature Requests:**

.. code-block:: markdown

   ## Feature Request Template
   
   **Is your feature request related to a problem?**
   A clear description of what the problem is.
   
   **Describe the solution you'd like**
   A clear description of what you want to happen.
   
   **Describe alternatives you've considered**
   Alternative solutions or features you've considered.
   
   **Use cases**
   Specific scenarios where this feature would be valuable.
   
   **Additional context**
   Any other context, mockups, or examples.

Step 2: Fork and Clone
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Fork the repository on GitHub
   # Then clone your fork
   git clone https://github.com/YOUR_USERNAME/blast-radius.git
   cd blast-radius
   
   # Add upstream remote
   git remote add upstream https://github.com/forkrul/blast-radius.git
   
   # Verify remotes
   git remote -v

Step 3: Create a Branch
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create and switch to a new branch
   git checkout -b feature/your-feature-name
   
   # Or for bug fixes
   git checkout -b bugfix/issue-description
   
   # Push branch to your fork
   git push -u origin feature/your-feature-name

Step 4: Make Changes
~~~~~~~~~~~~~~~~~~~~

**Development Guidelines:**

1. **Follow coding standards** - See :doc:`code-standards`
2. **Write tests** - Include unit and integration tests
3. **Update documentation** - Keep docs current with changes
4. **Commit frequently** - Make small, logical commits
5. **Test thoroughly** - Ensure all tests pass

**Commit Message Format:**

.. code-block:: bash

   # Use conventional commit format
   feat(component): add new feature description
   fix(component): resolve specific issue
   docs(section): update documentation
   test(component): add test coverage
   
   # Examples
   feat(api): add real-time monitoring endpoints
   fix(auth): resolve JWT token validation issue
   docs(installation): update Docker requirements
   test(discovery): add unit tests for AWS integration

Step 5: Test Your Changes
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run the full test suite
   make test
   
   # Run specific test categories
   make test-unit
   make test-integration
   make test-security
   
   # Check code quality
   make lint
   make format
   make type-check
   
   # Test documentation build
   make docs

Step 6: Submit Pull Request
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Push your changes
   git push origin feature/your-feature-name
   
   # Create pull request via GitHub CLI
   gh pr create --title "feat: Add real-time monitoring dashboard" \
                --body "Implements real-time monitoring with WebSocket support"
   
   # Or create via GitHub web interface

**Pull Request Template:**

.. code-block:: markdown

   ## Description
   Brief description of changes and motivation.
   
   ## Type of Change
   - [ ] Bug fix (non-breaking change which fixes an issue)
   - [ ] New feature (non-breaking change which adds functionality)
   - [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
   - [ ] Documentation update
   
   ## Testing
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Manual testing completed
   - [ ] Performance impact assessed
   
   ## Documentation
   - [ ] API documentation updated
   - [ ] User guides updated
   - [ ] Technical documentation updated
   - [ ] Changelog updated
   
   ## Security
   - [ ] Security implications considered
   - [ ] No sensitive data exposed
   - [ ] Input validation implemented
   - [ ] Authentication/authorization correct
   
   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Comments added for complex code
   - [ ] No new warnings introduced
   - [ ] Tests added for new functionality

Code Standards
--------------

Python Code Style
~~~~~~~~~~~~~~~~~

We follow PEP 8 with some modifications:

.. code-block:: python

   # Good examples
   
   class AttackPathAnalyzer:
       """Analyzes attack paths between assets."""
       
       def __init__(self, graph_service: GraphService) -> None:
           self.graph_service = graph_service
           self.logger = logging.getLogger(__name__)
       
       def analyze_path(
           self, 
           source_asset: Asset, 
           target_asset: Asset,
           max_depth: int = 10
       ) -> AttackPathResult:
           """Analyze attack paths between two assets.
           
           Args:
               source_asset: Starting point for analysis
               target_asset: Target asset to reach
               max_depth: Maximum path depth to explore
               
           Returns:
               AttackPathResult containing discovered paths
               
           Raises:
               ValidationError: If assets are invalid
               AnalysisError: If analysis fails
           """
           if not self._validate_assets(source_asset, target_asset):
               raise ValidationError("Invalid assets provided")
           
           try:
               paths = self.graph_service.find_paths(
                   source=source_asset.id,
                   target=target_asset.id,
                   max_depth=max_depth
               )
               return AttackPathResult(paths=paths)
           except Exception as e:
               self.logger.error(f"Analysis failed: {e}")
               raise AnalysisError(f"Failed to analyze path: {e}")

**Key Principles:**

1. **Type hints** - Use type annotations for all functions
2. **Docstrings** - Document all public methods and classes
3. **Error handling** - Use specific exceptions with clear messages
4. **Logging** - Use structured logging for debugging
5. **Constants** - Use uppercase for constants

JavaScript/TypeScript Style
~~~~~~~~~~~~~~~~~~~~~~~~~~~

For frontend code:

.. code-block:: typescript

   // Good examples
   
   interface AttackPathProps {
     sourceAsset: Asset;
     targetAsset: Asset;
     onAnalysisComplete: (result: AttackPathResult) => void;
   }
   
   const AttackPathAnalyzer: React.FC<AttackPathProps> = ({
     sourceAsset,
     targetAsset,
     onAnalysisComplete
   }) => {
     const [isAnalyzing, setIsAnalyzing] = useState(false);
     const [error, setError] = useState<string | null>(null);
     
     const analyzeAttackPath = useCallback(async () => {
       setIsAnalyzing(true);
       setError(null);
       
       try {
         const result = await attackPathService.analyze({
           sourceAssetId: sourceAsset.id,
           targetAssetId: targetAsset.id
         });
         onAnalysisComplete(result);
       } catch (err) {
         setError(err instanceof Error ? err.message : 'Analysis failed');
       } finally {
         setIsAnalyzing(false);
       }
     }, [sourceAsset.id, targetAsset.id, onAnalysisComplete]);
     
     return (
       <div className="attack-path-analyzer">
         {/* Component JSX */}
       </div>
     );
   };

Testing Guidelines
------------------

Test Structure
~~~~~~~~~~~~~~

.. code-block:: python

   # tests/test_attack_path_service.py
   
   import pytest
   from unittest.mock import Mock, patch
   
   from app.services.attack_path_service import AttackPathService
   from app.models.asset import Asset
   from app.exceptions import ValidationError
   
   
   class TestAttackPathService:
       """Test suite for AttackPathService."""
       
       @pytest.fixture
       def mock_graph_service(self):
           """Mock graph service for testing."""
           return Mock()
       
       @pytest.fixture
       def service(self, mock_graph_service):
           """Create service instance with mocked dependencies."""
           return AttackPathService(graph_service=mock_graph_service)
       
       @pytest.fixture
       def sample_assets(self):
           """Create sample assets for testing."""
           return [
               Asset(id=1, name="web-server", type="server"),
               Asset(id=2, name="database", type="database")
           ]
       
       def test_analyze_path_success(self, service, sample_assets, mock_graph_service):
           """Test successful attack path analysis."""
           # Arrange
           source, target = sample_assets
           expected_paths = [{"path": [1, 2], "risk_score": 0.8}]
           mock_graph_service.find_paths.return_value = expected_paths
           
           # Act
           result = service.analyze_path(source, target)
           
           # Assert
           assert result.paths == expected_paths
           mock_graph_service.find_paths.assert_called_once_with(
               source=1, target=2, max_depth=10
           )
       
       def test_analyze_path_invalid_assets(self, service):
           """Test analysis with invalid assets."""
           # Arrange
           invalid_asset = Asset(id=None, name="", type="")
           valid_asset = Asset(id=1, name="server", type="server")
           
           # Act & Assert
           with pytest.raises(ValidationError, match="Invalid assets"):
               service.analyze_path(invalid_asset, valid_asset)

**Testing Best Practices:**

1. **AAA Pattern** - Arrange, Act, Assert
2. **Descriptive names** - Test names should explain what they test
3. **One assertion per test** - Focus on single behavior
4. **Use fixtures** - Reuse common test setup
5. **Mock external dependencies** - Isolate units under test

Documentation Standards
-----------------------

Documentation Types
~~~~~~~~~~~~~~~~~~~

1. **API Documentation** - OpenAPI/Swagger specifications
2. **User Guides** - Step-by-step instructions for end users
3. **Technical Documentation** - Architecture and design decisions
4. **Code Documentation** - Inline comments and docstrings
5. **Tutorials** - Learning-oriented content

Writing Guidelines
~~~~~~~~~~~~~~~~~~

**Structure:**

.. code-block:: rst

   Page Title
   ==========
   
   Brief description of the page content.
   
   .. contents:: Table of Contents
      :local:
      :depth: 2
   
   Overview
   --------
   
   High-level overview of the topic.
   
   Section Title
   -------------
   
   Detailed content with examples.
   
   Subsection
   ~~~~~~~~~~
   
   More specific information.

**Code Examples:**

.. code-block:: rst

   # Always include working code examples
   
   .. code-block:: python
   
      # Example with explanation
      from app.services import AttackPathService
      
      service = AttackPathService()
      result = service.analyze_path(source, target)
      print(f"Found {len(result.paths)} attack paths")

**Best Practices:**

1. **User-focused** - Write from the user's perspective
2. **Step-by-step** - Break complex tasks into steps
3. **Examples** - Include practical, working examples
4. **Cross-references** - Link to related documentation
5. **Keep current** - Update docs with code changes

Review Process
--------------

Code Review Checklist
~~~~~~~~~~~~~~~~~~~~~~

**Functionality:**
- [ ] Code implements intended functionality
- [ ] Edge cases are handled
- [ ] Error handling is comprehensive
- [ ] Performance is acceptable

**Code Quality:**
- [ ] Follows project coding standards
- [ ] Functions are well-documented
- [ ] Names are descriptive and clear
- [ ] Code is DRY and maintainable

**Security:**
- [ ] Input validation is implemented
- [ ] No sensitive data exposure
- [ ] Authentication/authorization correct
- [ ] SQL injection prevention

**Testing:**
- [ ] Unit tests cover new functionality
- [ ] Integration tests are updated
- [ ] Test coverage meets requirements
- [ ] Tests are meaningful and robust

**Documentation:**
- [ ] API documentation updated
- [ ] User guides reflect changes
- [ ] Code comments explain complex logic
- [ ] Changelog updated

Review Guidelines
~~~~~~~~~~~~~~~~~

**For Authors: <AUTHORS>
1. **Self-review first** - Review your own code before submitting
2. **Provide context** - Explain the reasoning behind changes
3. **Keep PRs focused** - One feature or fix per PR
4. **Respond promptly** - Address feedback quickly
5. **Be open to feedback** - Consider suggestions constructively

**For Reviewers:**
1. **Be constructive** - Provide helpful, actionable feedback
2. **Explain reasoning** - Help authors understand suggestions
3. **Focus on important issues** - Don't nitpick minor style issues
4. **Approve when ready** - Don't hold up good code unnecessarily
5. **Learn from others** - Use reviews as learning opportunities

Community Guidelines
--------------------

Code of Conduct
~~~~~~~~~~~~~~~

We are committed to providing a welcoming and inclusive environment:

1. **Be respectful** - Treat everyone with respect and kindness
2. **Be inclusive** - Welcome people of all backgrounds and experience levels
3. **Be collaborative** - Work together to solve problems
4. **Be patient** - Help others learn and grow
5. **Be professional** - Maintain professional standards in all interactions

Communication
~~~~~~~~~~~~~

**Preferred Channels:**
- **GitHub Issues** - Bug reports and feature requests
- **GitHub Discussions** - Questions and general discussion
- **Pull Requests** - Code review and technical discussion
- **Email** - Private or sensitive matters

**Communication Guidelines:**
1. **Be clear and concise** - Communicate effectively
2. **Provide context** - Give enough information for others to help
3. **Search first** - Check if your question has been answered
4. **Use appropriate channels** - Choose the right forum for your message
5. **Follow up** - Close issues when resolved

Recognition
-----------

Contributors are recognized in several ways:

1. **Contributors file** - Listed in CONTRIBUTORS.md
2. **Release notes** - Mentioned in changelog
3. **GitHub profile** - Contributions visible on GitHub
4. **Special recognition** - Outstanding contributions highlighted

Getting Help
------------

If you need help:

1. **Check documentation** - Review existing guides and tutorials
2. **Search issues** - Look for similar problems and solutions
3. **Ask questions** - Use GitHub Discussions for help
4. **Join community** - Participate in community discussions
5. **Contact maintainers** - Reach out for complex issues

**Resources:**
- **Documentation:** This comprehensive guide
- **GitHub Issues:** https://github.com/forkrul/blast-radius/issues
- **Discussions:** https://github.com/forkrul/blast-radius/discussions
- **Development Setup:** :doc:`setup`
- **Coding Standards:** :doc:`code-standards`

Thank you for contributing to the Blast-Radius Security Tool! Your contributions help make cybersecurity more accessible and effective for everyone.

.. note::
   This contributing guide is a living document. Please suggest improvements
   to help make the contribution process better for everyone.
