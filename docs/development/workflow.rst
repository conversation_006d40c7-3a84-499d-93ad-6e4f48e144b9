Development Workflow
====================

Comprehensive guide to the development workflow for the Blast-Radius Security Tool, covering branching strategies, code review processes, and release management.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Blast-Radius Security Tool follows a structured development workflow designed to ensure:

* **Code Quality:** Consistent standards and automated quality checks
* **Security:** Security-first development practices
* **Collaboration:** Efficient team collaboration and code review
* **Reliability:** Stable releases with comprehensive testing
* **Documentation:** Up-to-date documentation with every change

Branching Strategy
------------------

Git Flow Model
~~~~~~~~~~~~~~

We use a modified Git Flow branching strategy:

.. mermaid::

   gitGraph
       commit id: "Initial"
       branch develop
       checkout develop
       commit id: "Dev setup"
       branch feature/new-feature
       checkout feature/new-feature
       commit id: "Feature work"
       commit id: "Feature complete"
       checkout develop
       merge feature/new-feature
       commit id: "Merge feature"
       branch release/v1.1.0
       checkout release/v1.1.0
       commit id: "Release prep"
       checkout main
       merge release/v1.1.0
       commit id: "Release v1.1.0"
       checkout develop
       merge main

**Branch Types:**

.. list-table:: Branch Types and Purposes
   :header-rows: 1
   :widths: 20 30 50

   * - Branch Type
     - Naming Convention
     - Purpose
   * - **main**
     - `main`
     - Production-ready code, tagged releases
   * - **develop**
     - `develop`
     - Integration branch for features
   * - **feature**
     - `feature/description`
     - New features and enhancements
   * - **bugfix**
     - `bugfix/issue-description`
     - Bug fixes for develop branch
   * - **hotfix**
     - `hotfix/critical-fix`
     - Critical fixes for production
   * - **release**
     - `release/v1.2.0`
     - Release preparation and stabilization

Branch Naming Conventions
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Feature Branches:**

.. code-block:: bash

   # Good examples
   feature/real-time-monitoring-dashboard
   feature/mitre-attack-integration
   feature/multi-cloud-discovery
   feature/api-rate-limiting
   
   # Bad examples
   feature/fix
   feature/update
   feature/john-changes

**Bug Fix Branches:**

.. code-block:: bash

   # Good examples
   bugfix/asset-discovery-timeout
   bugfix/authentication-token-expiry
   bugfix/graph-analysis-memory-leak
   
   # Include issue number when available
   bugfix/issue-123-database-connection-pool

**Hotfix Branches:**

.. code-block:: bash

   # Critical production fixes
   hotfix/security-vulnerability-cve-2024-1234
   hotfix/database-connection-failure
   hotfix/authentication-bypass

Development Process
-------------------

Feature Development Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**1. Planning Phase:**

.. code-block:: bash

   # Create GitHub issue for feature
   # Define acceptance criteria
   # Estimate effort and timeline
   # Assign to milestone/sprint

**2. Branch Creation:**

.. code-block:: bash

   # Start from develop branch
   git checkout develop
   git pull origin develop
   
   # Create feature branch
   git checkout -b feature/real-time-monitoring-dashboard
   
   # Push branch to remote
   git push -u origin feature/real-time-monitoring-dashboard

**3. Development:**

.. code-block:: bash

   # Make incremental commits
   git add .
   git commit -m "feat: add real-time data streaming endpoint"
   
   # Push regularly
   git push origin feature/real-time-monitoring-dashboard
   
   # Rebase on develop regularly
   git fetch origin
   git rebase origin/develop

**4. Code Review:**

.. code-block:: bash

   # Create pull request
   gh pr create --title "feat: Real-time Monitoring Dashboard" \
                --body "Implements real-time monitoring dashboard with WebSocket support"
   
   # Address review feedback
   git add .
   git commit -m "fix: address code review feedback"
   git push origin feature/real-time-monitoring-dashboard

**5. Merge:**

.. code-block:: bash

   # Squash and merge via GitHub UI
   # Delete feature branch after merge
   git checkout develop
   git pull origin develop
   git branch -d feature/real-time-monitoring-dashboard

Commit Message Standards
~~~~~~~~~~~~~~~~~~~~~~~~

We follow the Conventional Commits specification:

**Format:**

.. code-block:: text

   <type>[optional scope]: <description>
   
   [optional body]
   
   [optional footer(s)]

**Types:**

.. list-table:: Commit Types
   :header-rows: 1
   :widths: 15 85

   * - Type
     - Description
   * - **feat**
     - New feature for the user
   * - **fix**
     - Bug fix for the user
   * - **docs**
     - Documentation changes
   * - **style**
     - Code style changes (formatting, etc.)
   * - **refactor**
     - Code refactoring without feature changes
   * - **perf**
     - Performance improvements
   * - **test**
     - Adding or updating tests
   * - **chore**
     - Build process or auxiliary tool changes
   * - **ci**
     - CI/CD configuration changes
   * - **security**
     - Security-related changes

**Examples:**

.. code-block:: bash

   # Feature commits
   feat(api): add real-time monitoring endpoints
   feat(ui): implement attack path visualization
   feat(discovery): add Azure cloud provider support
   
   # Bug fix commits
   fix(auth): resolve JWT token expiration issue
   fix(database): fix connection pool exhaustion
   fix(api): handle malformed request payloads
   
   # Documentation commits
   docs(api): add comprehensive endpoint documentation
   docs(setup): update installation requirements
   
   # Breaking changes
   feat(api)!: redesign authentication endpoints
   
   BREAKING CHANGE: Authentication endpoints now require API version header

Code Review Process
-------------------

Review Requirements
~~~~~~~~~~~~~~~~~~~

**All code changes must:**

1. **Pass automated checks** (CI/CD pipeline)
2. **Have at least one approval** from a code owner
3. **Include tests** for new functionality
4. **Update documentation** when needed
5. **Follow coding standards** and style guidelines

**Review Checklist:**

.. code-block:: markdown

   ## Code Review Checklist
   
   ### Functionality
   - [ ] Code implements the intended functionality
   - [ ] Edge cases are handled appropriately
   - [ ] Error handling is comprehensive
   - [ ] Performance considerations are addressed
   
   ### Code Quality
   - [ ] Code follows project style guidelines
   - [ ] Functions and classes are well-documented
   - [ ] Variable and function names are descriptive
   - [ ] Code is DRY (Don't Repeat Yourself)
   
   ### Security
   - [ ] Input validation is implemented
   - [ ] Authentication/authorization is correct
   - [ ] No sensitive data in logs or responses
   - [ ] SQL injection prevention measures
   
   ### Testing
   - [ ] Unit tests cover new functionality
   - [ ] Integration tests are updated
   - [ ] Test coverage meets requirements (>90%)
   - [ ] Tests are meaningful and not just for coverage
   
   ### Documentation
   - [ ] API documentation is updated
   - [ ] User guides reflect changes
   - [ ] Technical documentation is current
   - [ ] Changelog is updated

Review Process
~~~~~~~~~~~~~~

**1. Automated Checks:**

.. code-block:: yaml

   # GitHub Actions automatically run:
   - Code linting (flake8, black, isort)
   - Security scanning (bandit, safety)
   - Unit and integration tests
   - Documentation build
   - Performance benchmarks

**2. Manual Review:**

.. code-block:: bash

   # Reviewers check:
   # - Code logic and design
   # - Security implications
   # - Performance impact
   # - Documentation completeness
   # - Test coverage and quality

**3. Approval Process:**

.. code-block:: text

   Required Approvals:
   - 1 approval for minor changes (docs, tests, small fixes)
   - 2 approvals for major features
   - Security team approval for security-related changes
   - Architecture team approval for architectural changes

Testing Strategy
----------------

Test-Driven Development
~~~~~~~~~~~~~~~~~~~~~~~

**TDD Workflow:**

.. code-block:: bash

   # 1. Write failing test
   def test_attack_path_analysis():
       result = analyze_attack_path(source, target)
       assert result.paths_found > 0
       assert result.risk_score > 0.5
   
   # 2. Run test (should fail)
   pytest tests/test_attack_path.py::test_attack_path_analysis
   
   # 3. Write minimal code to pass
   def analyze_attack_path(source, target):
       return AttackPathResult(paths_found=1, risk_score=0.6)
   
   # 4. Refactor and improve
   def analyze_attack_path(source, target):
       # Actual implementation
       pass

**Testing Pyramid:**

.. code-block:: text

   ┌─────────────────┐
   │   E2E Tests     │  ← Few, slow, high confidence
   │   (5-10%)       │
   ├─────────────────┤
   │ Integration     │  ← Some, medium speed
   │ Tests (20-30%)  │
   ├─────────────────┤
   │   Unit Tests    │  ← Many, fast, focused
   │   (60-70%)      │
   └─────────────────┘

Continuous Integration
~~~~~~~~~~~~~~~~~~~~~~

**CI Pipeline Stages:**

.. code-block:: yaml

   # .github/workflows/ci.yml
   stages:
     - lint:
         - flake8 (code style)
         - black (formatting)
         - isort (import sorting)
         - mypy (type checking)
     
     - security:
         - bandit (security linting)
         - safety (dependency scanning)
         - semgrep (static analysis)
     
     - test:
         - unit tests (pytest)
         - integration tests
         - coverage reporting
     
     - build:
         - Docker image build
         - Documentation build
         - Package creation
     
     - deploy:
         - Deploy to staging
         - Run E2E tests
         - Performance benchmarks

Release Management
------------------

Release Process
~~~~~~~~~~~~~~~

**1. Release Planning:**

.. code-block:: bash

   # Create release milestone
   # Plan features and bug fixes
   # Set target release date
   # Communicate to stakeholders

**2. Release Branch Creation:**

.. code-block:: bash

   # Create release branch from develop
   git checkout develop
   git pull origin develop
   git checkout -b release/v1.2.0
   
   # Update version numbers
   # Update changelog
   # Final testing and bug fixes

**3. Release Preparation:**

.. code-block:: bash

   # Update version in all files
   sed -i 's/version = "1.1.0"/version = "1.2.0"/' pyproject.toml
   
   # Update changelog
   echo "## [1.2.0] - $(date +%Y-%m-%d)" >> CHANGELOG.md
   
   # Create release notes
   gh release create v1.2.0 --generate-notes

**4. Release Deployment:**

.. code-block:: bash

   # Merge to main
   git checkout main
   git merge release/v1.2.0
   
   # Tag release
   git tag -a v1.2.0 -m "Release version 1.2.0"
   git push origin main --tags
   
   # Merge back to develop
   git checkout develop
   git merge main

Versioning Strategy
~~~~~~~~~~~~~~~~~~~

We follow Semantic Versioning (SemVer):

.. code-block:: text

   MAJOR.MINOR.PATCH
   
   MAJOR: Breaking changes
   MINOR: New features (backward compatible)
   PATCH: Bug fixes (backward compatible)

**Examples:**

.. code-block:: bash

   # Patch release (bug fixes)
   1.1.0 → 1.1.1
   
   # Minor release (new features)
   1.1.1 → 1.2.0
   
   # Major release (breaking changes)
   1.2.0 → 2.0.0

Quality Gates
-------------

Definition of Done
~~~~~~~~~~~~~~~~~~

**Feature is considered "Done" when:**

1. **Code Complete:**
   - All acceptance criteria met
   - Code reviewed and approved
   - No critical or high-severity issues

2. **Testing Complete:**
   - Unit tests written and passing
   - Integration tests updated
   - Manual testing completed
   - Performance impact assessed

3. **Documentation Complete:**
   - API documentation updated
   - User guides updated
   - Technical documentation current
   - Changelog updated

4. **Security Review:**
   - Security implications assessed
   - Threat model updated if needed
   - Security tests passing
   - No new vulnerabilities introduced

5. **Deployment Ready:**
   - CI/CD pipeline passing
   - Staging deployment successful
   - Performance benchmarks met
   - Rollback plan documented

Automation Tools
----------------

Development Tools
~~~~~~~~~~~~~~~~~

**Code Quality:**

.. code-block:: bash

   # Pre-commit hooks
   pre-commit install
   
   # Manual quality checks
   make lint          # Run all linters
   make format        # Format code
   make type-check    # Type checking
   make security-scan # Security scanning

**Testing:**

.. code-block:: bash

   # Test commands
   make test          # Run all tests
   make test-unit     # Unit tests only
   make test-integration  # Integration tests
   make test-e2e      # End-to-end tests
   make coverage      # Coverage report

**Documentation:**

.. code-block:: bash

   # Documentation commands
   make docs          # Build documentation
   make docs-live     # Live reload server
   make docs-check    # Check for issues

Best Practices
--------------

Code Organization
~~~~~~~~~~~~~~~~~

1. **Follow project structure** - Maintain consistent file organization
2. **Use meaningful names** - Functions, variables, and files should be descriptive
3. **Keep functions small** - Single responsibility principle
4. **Document complex logic** - Add comments for non-obvious code
5. **Handle errors gracefully** - Comprehensive error handling

Collaboration
~~~~~~~~~~~~~

1. **Communicate early** - Discuss design decisions before implementation
2. **Review thoroughly** - Take time for meaningful code reviews
3. **Share knowledge** - Document decisions and learnings
4. **Be responsive** - Address review feedback promptly
5. **Help others** - Mentor junior developers and share expertise

Security
~~~~~~~~

1. **Security by design** - Consider security implications from the start
2. **Input validation** - Validate all user inputs
3. **Least privilege** - Use minimal required permissions
4. **Regular updates** - Keep dependencies current
5. **Security testing** - Include security tests in CI/CD

Next Steps
----------

For new team members:

1. **Set up development environment** - :doc:`setup`
2. **Review coding standards** - :doc:`code-standards`
3. **Understand testing approach** - :doc:`testing`
4. **Read contribution guidelines** - :doc:`contributing`
5. **Start with good first issues** - Check GitHub issues labeled "good first issue"

.. note::
   This workflow is continuously evolving. Suggest improvements through GitHub issues
   or team discussions to keep our development process efficient and effective.
