Security Testing Automation
============================

.. meta::
   :description: Comprehensive security testing automation framework for the Blast-Radius Security Tool
   :keywords: security automation, DevSecOps, CI/CD security, automated testing

Overview
--------

The Blast-Radius Security Tool implements a comprehensive security testing automation framework that integrates security testing throughout the software development lifecycle (SDLC). This approach ensures continuous security validation while maintaining development velocity and quality.

Security Automation Architecture
---------------------------------

Our security automation follows a multi-layered approach:

.. mermaid::

   graph TB
       A[Developer Workstation] --> B[Pre-commit Hooks]
       B --> C[CI/CD Pipeline]
       C --> D[Security Testing Stage]
       D --> E[Deployment Gates]
       E --> F[Production Monitoring]
       
       D --> D1[SAST Scanning]
       D --> D2[DAST Testing]
       D --> D3[Dependency Scanning]
       D --> D4[Container Security]
       D --> D5[Infrastructure Testing]
       
       E --> E1[Security Approval]
       E --> E2[Compliance Check]
       E --> E3[Risk Assessment]
       
       F --> F1[Runtime Protection]
       F --> F2[Vulnerability Monitoring]
       F --> F3[Threat Detection]

Pre-commit Security Hooks
--------------------------

Developer Workstation Security
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Git Pre-commit Hooks Configuration**:

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: https://github.com/PyCQA/bandit
       rev: '1.7.5'
       hooks:
         - id: bandit
           args: ['-r', 'app/', '--severity-level', 'medium']
           
     - repo: https://github.com/Yelp/detect-secrets
       rev: v1.4.0
       hooks:
         - id: detect-secrets
           args: ['--baseline', '.secrets.baseline']
           
     - repo: https://github.com/psf/black
       rev: 23.3.0
       hooks:
         - id: black
           language_version: python3.11
           
     - repo: https://github.com/pycqa/flake8
       rev: 6.0.0
       hooks:
         - id: flake8
           additional_dependencies: [flake8-security]

**Security Checks Performed**:

.. list-table:: Pre-commit Security Checks
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tool
     - Check Type
     - Blocking
     - Fix Available
   * - **Bandit**
     - SAST scanning
     - Yes
     - Manual
   * - **detect-secrets**
     - Secret detection
     - Yes
     - Manual
   * - **safety**
     - Dependency vulnerabilities
     - Yes
     - Automated
   * - **semgrep**
     - Custom security rules
     - Yes
     - Manual

CI/CD Security Pipeline
-----------------------

Pipeline Configuration
~~~~~~~~~~~~~~~~~~~~~~

**GitLab CI Security Pipeline**:

.. code-block:: yaml

   # .gitlab-ci.yml - Security Stage
   stages:
     - build
     - security
     - test
     - deploy
   
   variables:
     SECURITY_REPORT_FORMAT: "json"
     FAIL_ON_CRITICAL: "true"
   
   security_scan:
     stage: security
     image: python:3.11-slim
     before_script:
       - pip install bandit safety semgrep
     script:
       - bandit -r app/ -f json -o bandit-results.json || true
       - safety check --json --output safety-results.json || true
       - semgrep --config=auto --json --output=semgrep-results.json app/ || true
       - python scripts/security_scan.py --output-dir reports/security
     artifacts:
       reports:
         security: reports/security/security-summary.json
       paths:
         - reports/security/
       expire_in: 30 days
     only:
       - merge_requests
       - master
       - develop

**Container Security Scanning**:

.. code-block:: yaml

   container_security:
     stage: security
     image: aquasec/trivy:latest
     script:
       - trivy image --format json --output trivy-results.json $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
       - trivy config --format json --output trivy-config.json .
     artifacts:
       reports:
         container_scanning: trivy-results.json
       paths:
         - trivy-*.json
     dependencies:
       - build_image

**Infrastructure Security Testing**:

.. code-block:: yaml

   infrastructure_security:
     stage: security
     image: bridgecrew/checkov:latest
     script:
       - checkov -d . --framework terraform --output json --output-file checkov-results.json
       - checkov -d . --framework kubernetes --output json --output-file checkov-k8s.json
     artifacts:
       reports:
         security: checkov-results.json
       paths:
         - checkov-*.json

Security Testing Tools Integration
----------------------------------

Static Application Security Testing (SAST)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Bandit Configuration**:

.. code-block:: ini

   # bandit.yaml
   tests:
     - B101  # assert_used
     - B102  # exec_used
     - B103  # set_bad_file_permissions
     - B104  # hardcoded_bind_all_interfaces
     - B105  # hardcoded_password_string
     - B106  # hardcoded_password_funcarg
     - B107  # hardcoded_password_default
     - B108  # hardcoded_tmp_directory
     - B110  # try_except_pass
     - B112  # try_except_continue
   
   skips:
     - B105  # Hardcoded password string (for enum values)
   
   exclude_dirs:
     - /tests/
     - /venv/
     - /.git/

**Semgrep Custom Rules**:

.. code-block:: yaml

   # .semgrep.yml
   rules:
     - id: blast-radius-sql-injection
       pattern: |
         cursor.execute($QUERY, ...)
       message: "Potential SQL injection vulnerability"
       languages: [python]
       severity: ERROR
       
     - id: blast-radius-command-injection
       pattern: |
         subprocess.$FUNC($CMD, shell=True)
       message: "Command injection risk with shell=True"
       languages: [python]
       severity: WARNING
       
     - id: blast-radius-weak-crypto
       pattern: |
         hashlib.md5(...)
       message: "Weak cryptographic hash function"
       languages: [python]
       severity: ERROR

Dynamic Application Security Testing (DAST)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**OWASP ZAP Integration**:

.. code-block:: yaml

   dast_scan:
     stage: security
     image: owasp/zap2docker-stable:latest
     script:
       - mkdir -p /zap/wrk
       - zap-baseline.py -t $APPLICATION_URL -J zap-baseline-report.json
       - zap-full-scan.py -t $APPLICATION_URL -J zap-full-report.json
     artifacts:
       reports:
         dast: zap-baseline-report.json
       paths:
         - zap-*.json
     only:
       - schedules
       - master

**Custom DAST Testing**:

.. code-block:: python

   # scripts/dast_automation.py
   import requests
   import json
   from typing import List, Dict
   
   class BlastRadiusDAST:
       def __init__(self, base_url: str, auth_token: str):
           self.base_url = base_url
           self.auth_token = auth_token
           self.session = requests.Session()
           self.session.headers.update({
               'Authorization': f'Bearer {auth_token}',
               'Content-Type': 'application/json'
           })
       
       def test_authentication_bypass(self) -> Dict:
           """Test for authentication bypass vulnerabilities"""
           test_cases = [
               {'endpoint': '/api/v1/assets', 'method': 'GET'},
               {'endpoint': '/api/v1/attack-paths', 'method': 'GET'},
               {'endpoint': '/api/v1/users', 'method': 'GET'}
           ]
           
           results = []
           for case in test_cases:
               # Test without authentication
               response = requests.get(f"{self.base_url}{case['endpoint']}")
               results.append({
                   'endpoint': case['endpoint'],
                   'status_code': response.status_code,
                   'vulnerable': response.status_code == 200
               })
           
           return {'test': 'authentication_bypass', 'results': results}
       
       def test_sql_injection(self) -> Dict:
           """Test for SQL injection vulnerabilities"""
           payloads = [
               "' OR '1'='1",
               "'; DROP TABLE users; --",
               "' UNION SELECT * FROM users --"
           ]
           
           results = []
           for payload in payloads:
               response = self.session.get(
                   f"{self.base_url}/api/v1/assets",
                   params={'search': payload}
               )
               results.append({
                   'payload': payload,
                   'status_code': response.status_code,
                   'response_time': response.elapsed.total_seconds(),
                   'vulnerable': 'error' in response.text.lower()
               })
           
           return {'test': 'sql_injection', 'results': results}

Dependency Security Scanning
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Safety Configuration**:

.. code-block:: json

   {
     "safety": {
       "ignore": [],
       "continue-on-error": false,
       "full-report": true,
       "output": "json"
     }
   }

**Automated Dependency Updates**:

.. code-block:: yaml

   # .github/dependabot.yml
   version: 2
   updates:
     - package-ecosystem: "pip"
       directory: "/backend"
       schedule:
         interval: "weekly"
       reviewers:
         - "security-team"
       labels:
         - "security"
         - "dependencies"
       open-pull-requests-limit: 10

Security Quality Gates
----------------------

Deployment Gates Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Security Approval Gates**:

.. code-block:: yaml

   security_gate:
     stage: security_approval
     script:
       - python scripts/security_gate_check.py
     rules:
       - if: '$CI_COMMIT_BRANCH == "master"'
         when: manual
         allow_failure: false
       - if: '$SECURITY_CRITICAL_ISSUES > 0'
         when: manual
         allow_failure: false
     dependencies:
       - security_scan

**Risk Assessment Automation**:

.. code-block:: python

   # scripts/security_gate_check.py
   import json
   import sys
   from typing import Dict, List
   
   class SecurityGate:
       def __init__(self, reports_dir: str):
           self.reports_dir = reports_dir
           self.risk_thresholds = {
               'critical': 0,
               'high': 2,
               'medium': 10,
               'low': 50
           }
       
       def evaluate_security_posture(self) -> Dict:
           """Evaluate overall security posture"""
           bandit_results = self.load_bandit_results()
           safety_results = self.load_safety_results()
           
           total_issues = self.count_issues_by_severity(bandit_results, safety_results)
           gate_status = self.check_risk_thresholds(total_issues)
           
           return {
               'gate_status': gate_status,
               'total_issues': total_issues,
               'recommendations': self.generate_recommendations(total_issues)
           }
       
       def check_risk_thresholds(self, issues: Dict) -> str:
           """Check if issues exceed risk thresholds"""
           for severity, count in issues.items():
               if count > self.risk_thresholds.get(severity, 0):
                   return 'FAIL'
           return 'PASS'

Automated Remediation
---------------------

Auto-fix Capabilities
~~~~~~~~~~~~~~~~~~~~~

**Automated Security Fixes**:

.. code-block:: python

   # scripts/auto_remediation.py
   import ast
   import re
   from typing import List, Tuple
   
   class SecurityAutoFix:
       def __init__(self, file_path: str):
           self.file_path = file_path
           with open(file_path, 'r') as f:
               self.content = f.read()
       
       def fix_hardcoded_secrets(self) -> List[str]:
           """Automatically fix hardcoded secrets"""
           fixes = []
           
           # Replace hardcoded passwords with environment variables
           patterns = [
               (r'password\s*=\s*["\']([^"\']+)["\']', 
                r'password = os.getenv("PASSWORD", "\1")'),
               (r'api_key\s*=\s*["\']([^"\']+)["\']', 
                r'api_key = os.getenv("API_KEY", "\1")')
           ]
           
           for pattern, replacement in patterns:
               if re.search(pattern, self.content):
                   self.content = re.sub(pattern, replacement, self.content)
                   fixes.append(f"Fixed hardcoded secret: {pattern}")
           
           return fixes
       
       def fix_weak_crypto(self) -> List[str]:
           """Automatically fix weak cryptographic functions"""
           fixes = []
           
           # Replace MD5 with SHA-256
           if 'hashlib.md5' in self.content:
               self.content = self.content.replace('hashlib.md5', 'hashlib.sha256')
               fixes.append("Replaced MD5 with SHA-256")
           
           return fixes

Continuous Security Monitoring
-------------------------------

Runtime Security Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Application Performance Monitoring (APM) Integration**:

.. code-block:: python

   # app/core/security_monitoring.py
   import logging
   from datadog import DogStatsdClient
   from typing import Dict, Any
   
   class SecurityMonitor:
       def __init__(self):
           self.statsd = DogStatsdClient(host='localhost', port=8125)
           self.logger = logging.getLogger('security')
       
       def track_security_event(self, event_type: str, severity: str, metadata: Dict[str, Any]):
           """Track security events for monitoring"""
           self.statsd.increment(
               'security.event',
               tags=[f'type:{event_type}', f'severity:{severity}']
           )
           
           self.logger.warning(
               f"Security event: {event_type}",
               extra={
                   'event_type': event_type,
                   'severity': severity,
                   'metadata': metadata
               }
           )
       
       def track_vulnerability_metrics(self, scan_results: Dict):
           """Track vulnerability metrics"""
           for severity, count in scan_results.items():
               self.statsd.gauge(
                   f'security.vulnerabilities.{severity}',
                   count
               )

**Security Alerting**:

.. code-block:: yaml

   # monitoring/security-alerts.yml
   groups:
     - name: security.rules
       rules:
         - alert: CriticalVulnerabilityDetected
           expr: security_vulnerabilities_critical > 0
           for: 0m
           labels:
             severity: critical
           annotations:
             summary: "Critical security vulnerability detected"
             description: "{{ $value }} critical vulnerabilities found"
         
         - alert: SecurityScanFailure
           expr: security_scan_success == 0
           for: 5m
           labels:
             severity: warning
           annotations:
             summary: "Security scan failed"
             description: "Security scanning pipeline has failed"

Metrics and Reporting
---------------------

Security Automation Metrics
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Security Automation KPIs
   :header-rows: 1
   :widths: 40 30 30

   * - Metric
     - Current
     - Target
   * - **Scan Coverage**
     - 95%
     - >98%
   * - **False Positive Rate**
     - 8%
     - <5%
   * - **Mean Time to Detection**
     - 2 hours
     - <1 hour
   * - **Automated Fix Rate**
     - 60%
     - >80%
   * - **Pipeline Security Gate Pass Rate**
     - 92%
     - >95%

**Automated Reporting**:

.. code-block:: python

   # scripts/security_report_generator.py
   import json
   from datetime import datetime
   from jinja2 import Template
   
   class SecurityReportGenerator:
       def __init__(self, reports_dir: str):
           self.reports_dir = reports_dir
       
       def generate_executive_summary(self) -> str:
           """Generate executive security summary"""
           template = Template("""
           # Security Automation Report
           
           **Generated**: {{ timestamp }}
           
           ## Summary
           - Total Scans: {{ total_scans }}
           - Critical Issues: {{ critical_issues }}
           - High Issues: {{ high_issues }}
           - Remediation Rate: {{ remediation_rate }}%
           
           ## Trends
           - 30-day vulnerability trend: {{ trend }}
           - Security posture: {{ posture }}
           """)
           
           return template.render(
               timestamp=datetime.now().isoformat(),
               total_scans=self.get_total_scans(),
               critical_issues=self.get_critical_issues(),
               high_issues=self.get_high_issues(),
               remediation_rate=self.get_remediation_rate(),
               trend=self.get_vulnerability_trend(),
               posture=self.get_security_posture()
           )

Best Practices and Guidelines
-----------------------------

Security Automation Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Shift-Left Security**: Integrate security testing early in the development process
2. **Fail Fast**: Configure security gates to fail builds on critical issues
3. **Continuous Monitoring**: Implement runtime security monitoring and alerting
4. **Automated Remediation**: Automate fixes for common security issues
5. **Regular Updates**: Keep security tools and rules updated
6. **Metrics-Driven**: Use metrics to continuously improve security automation

Developer Guidelines
~~~~~~~~~~~~~~~~~~~~

**Security Testing Checklist**:

- [ ] Pre-commit hooks configured and passing
- [ ] SAST scans clean or issues addressed
- [ ] Dependencies scanned for vulnerabilities
- [ ] Security unit tests written and passing
- [ ] Code review includes security considerations
- [ ] Documentation updated for security changes

**Security Automation Integration**:

- [ ] CI/CD pipeline includes security stages
- [ ] Security gates configured appropriately
- [ ] Monitoring and alerting set up
- [ ] Automated remediation where possible
- [ ] Regular security automation reviews

Conclusion
----------

The Blast-Radius Security Tool security automation framework provides comprehensive, continuous security testing throughout the development lifecycle. By integrating security testing into every stage of development and deployment, we ensure robust security while maintaining development velocity.

.. note::
   Security automation is continuously evolving. This framework is reviewed and updated quarterly to incorporate new tools, techniques, and threat intelligence.

.. seealso::
   - :doc:`static-analysis`
   - :doc:`dynamic-testing`
   - :doc:`../reviews/security-assessment`
   - :doc:`../procedures/security-review-process`
