Dynamic Application Security Testing (DAST)
==========================================

This document provides comprehensive information about the Dynamic Application Security Testing (DAST) implementation for the Blast-Radius Security Tool. DAST tools test running applications to identify security vulnerabilities through simulated attacks.

Overview
--------

Dynamic Application Security Testing complements our static analysis by testing the application in its runtime environment. DAST tools interact with the application as an attacker would, identifying vulnerabilities that may not be apparent in source code analysis.

DAST Benefits
~~~~~~~~~~~~~

* **Runtime Vulnerability Detection**: Identify issues that only manifest during execution
* **Real-World Attack Simulation**: Test how the application responds to actual attacks
* **Configuration Testing**: Validate security configurations and deployments
* **Business Logic Testing**: Test complex application workflows and logic
* **Environment-Specific Issues**: Identify vulnerabilities specific to deployment environments

DAST Tools and Implementation
-----------------------------

Primary DAST Tools
~~~~~~~~~~~~~~~~~~

**Custom API Security Tests**

Our primary DAST implementation consists of comprehensive API security tests built specifically for the Blast-Radius Security Tool.

*Implementation:*

.. code-block:: python

   # tests/security/test_api_security.py
   import pytest
   from fastapi.testclient import TestClient
   from app.main import app
   
   client = TestClient(app)
   
   class TestAPISecurityDAST:
       """Comprehensive API security testing."""
       
       def test_sql_injection_attacks(self):
           """Test SQL injection prevention."""
           sql_payloads = [
               "'; DROP TABLE users; --",
               "' OR '1'='1",
               "'; INSERT INTO users VALUES ('hacker', 'password'); --",
               "' UNION SELECT * FROM sensitive_table --"
           ]
           
           for payload in sql_payloads:
               response = client.post("/api/v1/auth/login", json={
                   "username": payload,
                   "password": "test"
               })
               # Should not succeed or expose database errors
               assert response.status_code in [400, 401, 422]
               assert "database" not in response.text.lower()
               assert "sql" not in response.text.lower()
       
       def test_xss_prevention(self):
           """Test XSS attack prevention."""
           xss_payloads = [
               "<script>alert('XSS')</script>",
               "javascript:alert('XSS')",
               "<img src=x onerror=alert('XSS')>",
               "<svg onload=alert('XSS')>",
               "';alert('XSS');//"
           ]
           
           for payload in xss_payloads:
               response = client.post("/api/v1/users", json={
                   "username": "testuser",
                   "email": "<EMAIL>",
                   "full_name": payload
               })
               
               if response.status_code == 201:
                   # If accepted, should be sanitized
                   assert payload not in response.text
                   assert "<script>" not in response.text.lower()
       
       def test_authentication_bypass(self):
           """Test authentication bypass attempts."""
           bypass_attempts = [
               {"Authorization": "Bearer invalid-token"},
               {"Authorization": "Bearer "},
               {"Authorization": "Basic dGVzdDp0ZXN0"},
               {"Authorization": "Bearer eyJhbGciOiJIUzI1NiJ9.invalid.signature"},
               {}  # No authorization header
           ]
           
           for headers in bypass_attempts:
               response = client.get("/api/v1/users/me", headers=headers)
               assert response.status_code == 401

**OWASP ZAP Integration**

OWASP ZAP (Zed Attack Proxy) provides automated web application security scanning.

*Configuration:*

.. code-block:: yaml

   # zap-baseline-scan.yml
   zap:
     baseline_scan:
       target: "http://localhost:8000"
       rules:
         - id: "10021"  # X-Content-Type-Options header missing
           action: "IGNORE"
           reason: "Handled by reverse proxy"
       
       context:
         name: "blast-radius-context"
         urls:
           - "http://localhost:8000/api/v1/*"
         authentication:
           method: "form"
           login_url: "http://localhost:8000/api/v1/auth/login"
           username_field: "username"
           password_field: "password"

*Usage:*

.. code-block:: bash

   # Run ZAP baseline scan
   docker run -t owasp/zap2docker-stable zap-baseline.py \
     -t http://localhost:8000 \
     -J zap-baseline-report.json \
     -r zap-baseline-report.html

**Nuclei Vulnerability Scanner**

Nuclei provides fast vulnerability scanning with community-maintained templates.

*Configuration:*

.. code-block:: yaml

   # nuclei-config.yml
   templates:
     - cves/
     - vulnerabilities/
     - security-misconfiguration/
     - default-logins/
   
   severity:
     - critical
     - high
     - medium
   
   output: nuclei-results.json
   json: true

*Usage:*

.. code-block:: bash

   # Run Nuclei scan
   nuclei -u http://localhost:8000 \
     -t nuclei-templates/ \
     -severity critical,high,medium \
     -json -o nuclei-results.json

DAST Test Categories
-------------------

Authentication Security Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestAuthenticationSecurity:
       """Test authentication mechanisms."""
       
       def test_brute_force_protection(self):
           """Test brute force attack protection."""
           # Attempt multiple failed logins
           for i in range(10):
               response = client.post("/api/v1/auth/login", json={
                   "username": "testuser",
                   "password": f"wrong_password_{i}"
               })
               
           # Should implement rate limiting or account lockout
           response = client.post("/api/v1/auth/login", json={
               "username": "testuser",
               "password": "wrong_password"
           })
           
           assert response.status_code in [429, 423]  # Rate limited or locked
       
       def test_session_fixation(self):
           """Test session fixation prevention."""
           # Get initial session
           response1 = client.get("/api/v1/health")
           session1 = response1.cookies.get("session_id")
           
           # Login with credentials
           login_response = client.post("/api/v1/auth/login", json={
               "username": "testuser",
               "password": "password123"
           })
           
           if login_response.status_code == 200:
               # Session should change after login
               session2 = login_response.cookies.get("session_id")
               assert session1 != session2

Authorization Security Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestAuthorizationSecurity:
       """Test authorization and access controls."""
       
       def test_privilege_escalation(self):
           """Test privilege escalation prevention."""
           # Login as regular user
           user_token = self._get_user_token("regular_user", "password")
           
           # Attempt to access admin endpoints
           admin_endpoints = [
               "/api/v1/admin/users",
               "/api/v1/admin/settings",
               "/api/v1/admin/logs"
           ]
           
           for endpoint in admin_endpoints:
               response = client.get(endpoint, headers={
                   "Authorization": f"Bearer {user_token}"
               })
               assert response.status_code == 403
       
       def test_horizontal_privilege_escalation(self):
           """Test access to other users' data."""
           # Login as user1
           user1_token = self._get_user_token("user1", "password")
           
           # Attempt to access user2's data
           response = client.get("/api/v1/users/user2/profile", headers={
               "Authorization": f"Bearer {user1_token}"
           })
           
           assert response.status_code in [403, 404]

Input Validation Tests
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestInputValidation:
       """Test input validation and sanitization."""
       
       def test_command_injection(self):
           """Test command injection prevention."""
           command_payloads = [
               "; ls -la",
               "| cat /etc/passwd",
               "&& rm -rf /",
               "`whoami`",
               "$(id)"
           ]
           
           for payload in command_payloads:
               response = client.post("/api/v1/assets/scan", json={
                   "target": f"192.168.1.1{payload}",
                   "scan_type": "network"
               })
               
               # Should reject malicious input
               assert response.status_code in [400, 422]
       
       def test_path_traversal(self):
           """Test path traversal prevention."""
           traversal_payloads = [
               "../../../etc/passwd",
               "..\\..\\..\\windows\\system32\\config\\sam",
               "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
               "....//....//....//etc/passwd"
           ]
           
           for payload in traversal_payloads:
               response = client.get(f"/api/v1/files/{payload}")
               assert response.status_code in [400, 403, 404]

Business Logic Tests
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestBusinessLogic:
       """Test business logic security."""
       
       def test_race_conditions(self):
           """Test race condition vulnerabilities."""
           import threading
           import time
           
           results = []
           
           def concurrent_request():
               response = client.post("/api/v1/assets", json={
                   "name": "test_asset",
                   "type": "server"
               })
               results.append(response.status_code)
           
           # Create multiple concurrent requests
           threads = []
           for _ in range(10):
               thread = threading.Thread(target=concurrent_request)
               threads.append(thread)
               thread.start()
           
           for thread in threads:
               thread.join()
           
           # Should handle concurrent requests properly
           success_count = sum(1 for status in results if status == 201)
           assert success_count <= 1  # Only one should succeed
       
       def test_workflow_bypass(self):
           """Test workflow bypass attempts."""
           # Attempt to skip required workflow steps
           response = client.post("/api/v1/assessments/finalize", json={
               "assessment_id": "new_assessment"
           })
           
           # Should require proper workflow completion
           assert response.status_code in [400, 403, 422]

DAST Automation and CI/CD Integration
-------------------------------------

Automated DAST Pipeline
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # .github/workflows/dast.yml
   name: Dynamic Security Testing
   
   on:
     push:
       branches: [ main, master ]
     pull_request:
       branches: [ main, master ]
   
   jobs:
     dast:
       runs-on: ubuntu-latest
       
       services:
         postgres:
           image: postgres:15
           env:
             POSTGRES_PASSWORD: postgres
           options: >-
             --health-cmd pg_isready
             --health-interval 10s
             --health-timeout 5s
             --health-retries 5
       
       steps:
       - uses: actions/checkout@v4
       
       - name: Set up Python
         uses: actions/setup-python@v4
         with:
           python-version: '3.11'
       
       - name: Install dependencies
         run: |
           pip install -r requirements.txt
           pip install -r requirements-test.txt
       
       - name: Start application
         run: |
           uvicorn app.main:app --host 0.0.0.0 --port 8000 &
           sleep 10
       
       - name: Run API Security Tests
         run: |
           pytest tests/security/test_api_security.py -v --tb=short
       
       - name: Run ZAP Baseline Scan
         run: |
           docker run -t owasp/zap2docker-stable zap-baseline.py \
             -t http://localhost:8000 \
             -J zap-results.json \
             -r zap-report.html
       
       - name: Upload DAST Results
         uses: actions/upload-artifact@v3
         with:
           name: dast-results
           path: |
             zap-results.json
             zap-report.html

Makefile Integration
~~~~~~~~~~~~~~~~~~~

.. code-block:: makefile

   # DAST testing targets
   .PHONY: dast-test api-security-test zap-scan nuclei-scan
   
   dast-test: api-security-test zap-scan
   	@echo "Dynamic security testing complete"
   
   api-security-test:
   	@echo "Running API security tests..."
   	$(VENV_BIN)/pytest tests/security/test_api_security.py -v
   
   zap-scan:
   	@echo "Running ZAP baseline scan..."
   	@mkdir -p reports
   	docker run -t owasp/zap2docker-stable zap-baseline.py \
   		-t http://localhost:8000 \
   		-J reports/zap-results.json \
   		-r reports/zap-report.html
   
   nuclei-scan:
   	@echo "Running Nuclei vulnerability scan..."
   	@mkdir -p reports
   	nuclei -u http://localhost:8000 \
   		-t nuclei-templates/ \
   		-json -o reports/nuclei-results.json

DAST Results Analysis
--------------------

Vulnerability Assessment
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # scripts/analyze_dast_results.py
   import json
   from typing import Dict, List, Any
   
   class DASTResultsAnalyzer:
       def __init__(self):
           self.vulnerabilities = []
           self.false_positives = []
       
       def analyze_zap_results(self, results_file: str):
           """Analyze ZAP scan results."""
           with open(results_file, 'r') as f:
               data = json.load(f)
           
           for alert in data.get('site', [{}])[0].get('alerts', []):
               vulnerability = {
                   'tool': 'zap',
                   'name': alert['name'],
                   'risk': alert['riskdesc'],
                   'confidence': alert['confidence'],
                   'description': alert['desc'],
                   'solution': alert['solution'],
                   'instances': len(alert['instances'])
               }
               
               if self._is_false_positive(vulnerability):
                   self.false_positives.append(vulnerability)
               else:
                   self.vulnerabilities.append(vulnerability)
       
       def analyze_nuclei_results(self, results_file: str):
           """Analyze Nuclei scan results."""
           with open(results_file, 'r') as f:
               for line in f:
                   if line.strip():
                       result = json.loads(line)
                       vulnerability = {
                           'tool': 'nuclei',
                           'template': result['template-id'],
                           'name': result['info']['name'],
                           'severity': result['info']['severity'],
                           'url': result['matched-at'],
                           'description': result['info'].get('description', '')
                       }
                       self.vulnerabilities.append(vulnerability)
       
       def generate_report(self) -> Dict[str, Any]:
           """Generate comprehensive DAST report."""
           return {
               'summary': {
                   'total_vulnerabilities': len(self.vulnerabilities),
                   'by_severity': self._count_by_severity(),
                   'by_tool': self._count_by_tool(),
                   'false_positives': len(self.false_positives)
               },
               'vulnerabilities': self.vulnerabilities,
               'recommendations': self._generate_recommendations()
           }

Performance Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Performance monitoring during DAST
   import time
   import psutil
   from prometheus_client import Histogram, Counter
   
   # DAST performance metrics
   dast_scan_duration = Histogram('dast_scan_duration_seconds', 'DAST scan duration', ['tool'])
   dast_requests_total = Counter('dast_requests_total', 'Total DAST requests', ['tool', 'status'])
   
   class DASTPerformanceMonitor:
       def __init__(self):
           self.start_time = None
           self.request_count = 0
       
       def start_monitoring(self):
           """Start performance monitoring."""
           self.start_time = time.time()
           self.initial_cpu = psutil.cpu_percent()
           self.initial_memory = psutil.virtual_memory().percent
       
       def record_request(self, tool: str, status_code: int):
           """Record DAST request."""
           self.request_count += 1
           dast_requests_total.labels(tool=tool, status=status_code).inc()
       
       def end_monitoring(self, tool: str):
           """End monitoring and record metrics."""
           duration = time.time() - self.start_time
           dast_scan_duration.labels(tool=tool).observe(duration)
           
           return {
               'duration': duration,
               'requests': self.request_count,
               'cpu_impact': psutil.cpu_percent() - self.initial_cpu,
               'memory_impact': psutil.virtual_memory().percent - self.initial_memory
           }

DAST Best Practices
------------------

Test Environment Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Dedicated Test Environments**
   - Isolated testing environments
   - Production-like configurations
   - Controlled test data sets
   - Network isolation and monitoring

**Environment Preparation**
   - Clean state before testing
   - Consistent configuration
   - Monitoring and logging enabled
   - Backup and recovery procedures

Test Data Management
~~~~~~~~~~~~~~~~~~~

**Synthetic Test Data**
   - Realistic but non-sensitive data
   - Comprehensive test scenarios
   - Edge cases and boundary conditions
   - Data privacy compliance

**Test User Accounts**
   - Multiple privilege levels
   - Dedicated test accounts
   - Secure credential management
   - Regular account rotation

Continuous Improvement
~~~~~~~~~~~~~~~~~~~~~

**Regular Tool Updates**
   - Keep DAST tools current
   - Update vulnerability signatures
   - Evaluate new testing techniques
   - Monitor tool effectiveness

**False Positive Management**
   - Regular false positive review
   - Suppression rule maintenance
   - Tool tuning and configuration
   - Feedback to tool vendors

Conclusion
----------

Dynamic Application Security Testing provides critical runtime security validation for the Blast-Radius Security Tool. Through comprehensive API testing, automated scanning, and continuous integration, DAST ensures that our application maintains strong security posture in production environments.

For additional security testing information:

- :doc:`static-analysis` - Static Application Security Testing
- :doc:`penetration-testing` - Penetration testing procedures
- :doc:`security-automation` - Security test automation
- :doc:`overview` - Security testing overview
