Static Application Security Testing (SAST)
==========================================

This document provides detailed information about the Static Application Security Testing (SAST) implementation for the Blast-Radius Security Tool. SAST tools analyze source code to identify security vulnerabilities without executing the application.

Overview
--------

Static Application Security Testing is a critical component of our security testing strategy. It enables early detection of security vulnerabilities during the development process, allowing for cost-effective remediation before deployment.

SAST Benefits
~~~~~~~~~~~~~

* **Early Detection**: Identify vulnerabilities during development
* **Cost-Effective**: Fix issues before they reach production
* **Comprehensive Coverage**: Analyze all code paths and branches
* **Automated Integration**: Seamless CI/CD pipeline integration
* **Developer Education**: Help developers learn secure coding practices

SAST Tools and Implementation
-----------------------------

Primary SAST Tools
~~~~~~~~~~~~~~~~~~

**Bandit - Python Security Linter**

Bandit is our primary Python security analysis tool, specifically designed to find common security issues in Python code.

*Configuration:*

.. code-block:: yaml

   # .bandit
   exclude_dirs:
     - tests
     - venv
     - .venv
   
   skips:
     - B101  # Skip assert_used test
   
   tests:
     - B102  # exec_used
     - B103  # set_bad_file_permissions
     - B104  # hardcoded_bind_all_interfaces
     - B105  # hardcoded_password_string
     - B106  # hardcoded_password_funcarg
     - B107  # hardcoded_password_default

*Usage:*

.. code-block:: bash

   # Run Bandit scan
   bandit -r app/ -f json -o reports/bandit-results.json
   
   # Run with specific confidence level
   bandit -r app/ -i -l
   
   # Generate HTML report
   bandit -r app/ -f html -o reports/bandit-report.html

**Semgrep - Multi-Language Static Analysis**

Semgrep provides advanced pattern-based static analysis with custom rule support.

*Configuration:*

.. code-block:: yaml

   # .semgrep.yml
   rules:
     - id: hardcoded-secret
       pattern: |
         password = "..."
       message: Hardcoded password detected
       severity: ERROR
       languages: [python]
     
     - id: sql-injection
       pattern: |
         execute($QUERY + $VAR)
       message: Potential SQL injection
       severity: ERROR
       languages: [python]

*Usage:*

.. code-block:: bash

   # Run Semgrep with auto rules
   semgrep --config=auto app/
   
   # Run with custom rules
   semgrep --config=.semgrep.yml app/
   
   # Generate JSON output
   semgrep --config=auto --json --output=reports/semgrep-results.json app/

**CodeQL - Semantic Code Analysis**

GitHub's CodeQL provides deep semantic analysis of code to identify complex security vulnerabilities.

*Configuration:*

.. code-block:: yaml

   # .github/workflows/codeql-analysis.yml
   name: "CodeQL"
   
   on:
     push:
       branches: [ main, master ]
     pull_request:
       branches: [ main, master ]
   
   jobs:
     analyze:
       name: Analyze
       runs-on: ubuntu-latest
       
       strategy:
         fail-fast: false
         matrix:
           language: [ 'python' ]
       
       steps:
       - name: Checkout repository
         uses: actions/checkout@v4
       
       - name: Initialize CodeQL
         uses: github/codeql-action/init@v2
         with:
           languages: ${{ matrix.language }}
           queries: security-extended
       
       - name: Perform CodeQL Analysis
         uses: github/codeql-action/analyze@v2

**SonarQube - Code Quality and Security**

SonarQube provides comprehensive code quality analysis including security vulnerability detection.

*Configuration:*

.. code-block:: properties

   # sonar-project.properties
   sonar.projectKey=blast-radius-security-tool
   sonar.projectName=Blast-Radius Security Tool
   sonar.projectVersion=1.0
   
   sonar.sources=app
   sonar.tests=tests
   sonar.python.coverage.reportPaths=reports/coverage.xml
   
   sonar.security.hotspots.enabled=true
   sonar.security.review.enabled=true

SAST Integration in CI/CD
-------------------------

GitHub Actions Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # .github/workflows/security.yml
   name: Security Analysis
   
   on:
     push:
       branches: [ main, master ]
     pull_request:
       branches: [ main, master ]
   
   jobs:
     sast:
       runs-on: ubuntu-latest
       
       steps:
       - uses: actions/checkout@v4
       
       - name: Set up Python
         uses: actions/setup-python@v4
         with:
           python-version: '3.11'
       
       - name: Install dependencies
         run: |
           pip install bandit semgrep safety
       
       - name: Run Bandit
         run: |
           bandit -r app/ -f json -o bandit-results.json
         continue-on-error: true
       
       - name: Run Semgrep
         run: |
           semgrep --config=auto --json --output=semgrep-results.json app/
         continue-on-error: true
       
       - name: Upload results
         uses: actions/upload-artifact@v3
         with:
           name: sast-results
           path: |
             bandit-results.json
             semgrep-results.json

Makefile Integration
~~~~~~~~~~~~~~~~~~~

.. code-block:: makefile

   # Security testing targets
   .PHONY: security-scan sast-scan bandit-scan semgrep-scan
   
   security-scan: sast-scan dependency-check secrets-scan
   
   sast-scan: bandit-scan semgrep-scan
   	@echo "Static analysis complete"
   
   bandit-scan:
   	@echo "Running Bandit security scan..."
   	@mkdir -p reports
   	$(VENV_BIN)/bandit -r app/ -f json -o reports/bandit-results.json
   	$(VENV_BIN)/bandit -r app/ -f txt -o reports/bandit-report.txt
   
   semgrep-scan:
   	@echo "Running Semgrep analysis..."
   	@mkdir -p reports
   	$(VENV_BIN)/semgrep --config=auto --json --output=reports/semgrep-results.json app/

SAST Rule Configuration
-----------------------

Custom Security Rules
~~~~~~~~~~~~~~~~~~~~~

**Bandit Custom Rules**

.. code-block:: python

   # custom_bandit_rules.py
   import bandit
   from bandit.core import test_properties
   
   @test_properties.test_id('B999')
   @test_properties.checks('Call')
   def check_custom_crypto(context):
       """Check for weak cryptographic functions."""
       weak_crypto = ['md5', 'sha1', 'des', 'rc4']
       
       if context.call_function_name_qual in weak_crypto:
           return bandit.Issue(
               severity=bandit.HIGH,
               confidence=bandit.HIGH,
               text="Use of weak cryptographic function",
               lineno=context.node.lineno,
           )

**Semgrep Custom Rules**

.. code-block:: yaml

   # custom-security-rules.yml
   rules:
     - id: hardcoded-jwt-secret
       pattern: |
         jwt.encode($PAYLOAD, "...", algorithm="...")
       message: Hardcoded JWT secret detected
       severity: ERROR
       languages: [python]
       
     - id: unsafe-deserialization
       pattern: |
         pickle.loads($DATA)
       message: Unsafe deserialization with pickle
       severity: ERROR
       languages: [python]
       
     - id: command-injection
       pattern: |
         os.system($CMD)
       message: Potential command injection
       severity: ERROR
       languages: [python]

SAST Results Analysis
--------------------

Vulnerability Classification
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Critical Vulnerabilities**
   - SQL injection vulnerabilities
   - Remote code execution risks
   - Authentication bypass issues
   - Hardcoded secrets and credentials

**High Vulnerabilities**
   - Cross-site scripting (XSS) risks
   - Insecure cryptographic implementations
   - Path traversal vulnerabilities
   - Privilege escalation issues

**Medium Vulnerabilities**
   - Information disclosure risks
   - Weak session management
   - Insecure configurations
   - Input validation issues

**Low Vulnerabilities**
   - Code quality issues with security implications
   - Minor configuration weaknesses
   - Informational security findings

Results Processing
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # scripts/process_sast_results.py
   import json
   from typing import Dict, List, Any
   
   class SASTResultsProcessor:
       def __init__(self):
           self.results = {
               'critical': [],
               'high': [],
               'medium': [],
               'low': []
           }
       
       def process_bandit_results(self, results_file: str):
           """Process Bandit JSON results."""
           with open(results_file, 'r') as f:
               data = json.load(f)
           
           for result in data.get('results', []):
               severity = self._map_bandit_severity(result['issue_severity'])
               self.results[severity].append({
                   'tool': 'bandit',
                   'type': result['test_name'],
                   'file': result['filename'],
                   'line': result['line_number'],
                   'message': result['issue_text'],
                   'confidence': result['issue_confidence']
               })
       
       def process_semgrep_results(self, results_file: str):
           """Process Semgrep JSON results."""
           with open(results_file, 'r') as f:
               data = json.load(f)
           
           for result in data.get('results', []):
               severity = self._map_semgrep_severity(result['extra']['severity'])
               self.results[severity].append({
                   'tool': 'semgrep',
                   'type': result['check_id'],
                   'file': result['path'],
                   'line': result['start']['line'],
                   'message': result['extra']['message']
               })
       
       def generate_summary(self) -> Dict[str, Any]:
           """Generate summary of SAST results."""
           return {
               'total_issues': sum(len(issues) for issues in self.results.values()),
               'by_severity': {
                   severity: len(issues) 
                   for severity, issues in self.results.items()
               },
               'critical_files': self._get_files_with_critical_issues(),
               'recommendations': self._generate_recommendations()
           }

False Positive Management
~~~~~~~~~~~~~~~~~~~~~~~~

**Suppression Configuration**

.. code-block:: python

   # Security issue suppressions
   # Format: # nosec B<test_id> <justification>
   
   def get_database_password():
       # This is a test password, not used in production
       return "test_password"  # nosec B105 test credential
   
   def bind_development_server():
       # Development server binding - not used in production
       app.run(host="0.0.0.0", port=8000)  # nosec B104 dev only

**Review Process**
   1. Automated SAST scan execution
   2. Results triage and classification
   3. False positive identification and suppression
   4. True positive validation and prioritization
   5. Remediation planning and tracking

SAST Metrics and Reporting
--------------------------

Key Metrics
~~~~~~~~~~

**Coverage Metrics**
   - Lines of code analyzed
   - File coverage percentage
   - Rule coverage by category
   - Scan execution time

**Quality Metrics**
   - Total vulnerabilities found
   - Vulnerabilities by severity
   - False positive rate
   - Time to remediation

**Trend Metrics**
   - Vulnerability trends over time
   - New vs. resolved issues
   - Security debt accumulation
   - Developer training effectiveness

Reporting Dashboard
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # SAST metrics collection
   from prometheus_client import Counter, Histogram, Gauge
   
   # SAST execution metrics
   sast_scans_total = Counter('sast_scans_total', 'Total SAST scans', ['tool', 'status'])
   sast_scan_duration = Histogram('sast_scan_duration_seconds', 'SAST scan duration', ['tool'])
   
   # Vulnerability metrics
   vulnerabilities_found = Counter('vulnerabilities_found_total', 'Vulnerabilities found', ['tool', 'severity'])
   vulnerabilities_current = Gauge('vulnerabilities_current', 'Current vulnerabilities', ['severity'])
   
   # Quality metrics
   false_positives = Counter('false_positives_total', 'False positives', ['tool'])
   time_to_fix = Histogram('vulnerability_fix_time_hours', 'Time to fix vulnerabilities', ['severity'])

Best Practices
--------------

SAST Implementation Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Tool Selection**
   - Use multiple complementary tools
   - Choose tools appropriate for your technology stack
   - Consider both open-source and commercial options
   - Evaluate tool accuracy and false positive rates

**Configuration Management**
   - Maintain centralized rule configurations
   - Regularly update rule sets and tool versions
   - Document suppression decisions and rationale
   - Version control all configuration files

**Integration Strategy**
   - Integrate early in the development process
   - Provide fast feedback to developers
   - Balance security and development velocity
   - Automate result processing and reporting

**Developer Training**
   - Educate developers on common vulnerabilities
   - Provide secure coding guidelines and examples
   - Offer training on SAST tool usage
   - Create feedback loops for continuous learning

Conclusion
----------

Static Application Security Testing is a fundamental component of our comprehensive security testing strategy. Through proper tool selection, configuration, and integration, SAST enables early detection and remediation of security vulnerabilities, contributing to the overall security posture of the Blast-Radius Security Tool.

For additional security testing information:

- :doc:`dynamic-testing` - Dynamic Application Security Testing
- :doc:`penetration-testing` - Penetration testing procedures
- :doc:`security-automation` - Security test automation
- :doc:`overview` - Security testing overview
