Common Issues & Troubleshooting
=================================

Comprehensive troubleshooting guide for the Blast-Radius Security Tool, covering installation, configuration, performance, and operational issues.

.. contents:: Table of Contents
   :local:
   :depth: 2

Quick Diagnostics
-----------------

Health Check Commands
~~~~~~~~~~~~~~~~~~~~~

**System Health Check:**

.. code-block:: bash

   # Check overall system health
   curl http://localhost:8000/health
   
   # Detailed health check
   curl http://localhost:8000/health/detailed
   
   # Check specific components
   curl http://localhost:8000/health/database
   curl http://localhost:8000/health/redis
   curl http://localhost:8000/health/neo4j

**Service Status Check:**

.. code-block:: bash

   # Docker services
   docker-compose ps
   
   # Check logs
   docker-compose logs backend
   docker-compose logs frontend
   docker-compose logs postgresql
   
   # Resource usage
   docker stats

**Application Diagnostics:**

.. code-block:: bash

   # Backend diagnostics
   cd backend
   python -m app.cli diagnose
   
   # Test database connections
   python -m app.cli test-connections
   
   # Validate configuration
   python -m app.cli validate-config

Installation Issues
-------------------

Docker Installation Problems
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Docker services won't start**

*Symptoms:*
- `docker-compose up` fails
- Services exit immediately
- Port binding errors

*Solutions:*

.. code-block:: bash

   # Check Docker daemon
   sudo systemctl status docker
   sudo systemctl start docker
   
   # Check available ports
   netstat -tulpn | grep -E ':(3000|8000|5432|6379|7687)'
   
   # Kill processes using required ports
   sudo lsof -ti:8000 | xargs sudo kill -9
   
   # Clean Docker environment
   docker system prune -f
   docker-compose down -v
   docker-compose up -d

**Issue: Out of disk space**

*Symptoms:*
- "No space left on device" errors
- Docker build failures
- Database write errors

*Solutions:*

.. code-block:: bash

   # Check disk usage
   df -h
   docker system df
   
   # Clean Docker resources
   docker system prune -a -f
   docker volume prune -f
   
   # Remove unused images
   docker image prune -a -f

Database Connection Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: PostgreSQL connection refused**

*Symptoms:*
- "Connection refused" errors
- Backend fails to start
- Database health check fails

*Solutions:*

.. code-block:: bash

   # Check PostgreSQL container
   docker-compose logs postgresql
   
   # Verify PostgreSQL is running
   docker-compose exec postgresql pg_isready
   
   # Test connection manually
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "SELECT 1;"
   
   # Reset database
   docker-compose down
   docker volume rm blast-radius_postgres_data
   docker-compose up -d postgresql

**Issue: Redis connection errors**

*Symptoms:*
- Session management failures
- Cache errors
- "Connection to Redis failed"

*Solutions:*

.. code-block:: bash

   # Check Redis container
   docker-compose logs redis
   
   # Test Redis connection
   docker-compose exec redis redis-cli ping
   
   # Check Redis configuration
   docker-compose exec redis redis-cli config get "*"
   
   # Restart Redis
   docker-compose restart redis

**Issue: Neo4j authentication failures**

*Symptoms:*
- Graph database connection errors
- Attack path analysis failures
- "Authentication failed" errors

*Solutions:*

.. code-block:: bash

   # Check Neo4j logs
   docker-compose logs neo4j
   
   # Reset Neo4j password
   docker-compose exec neo4j cypher-shell -u neo4j -p neo4j "ALTER USER neo4j SET PASSWORD 'blast_neo4j'"
   
   # Verify connection
   docker-compose exec neo4j cypher-shell -u neo4j -p blast_neo4j "RETURN 1"

Configuration Issues
--------------------

Environment Variable Problems
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Configuration not loading**

*Symptoms:*
- Default values being used
- Environment-specific settings ignored
- Configuration validation errors

*Solutions:*

.. code-block:: bash

   # Check environment file
   cat .env
   
   # Validate environment variables
   cd backend
   python -c "from app.core.config import settings; print(settings.dict())"
   
   # Check for syntax errors
   python -m app.cli validate-config
   
   # Reload configuration
   docker-compose restart backend

**Issue: Secret key errors**

*Symptoms:*
- JWT token validation failures
- Session errors
- "Invalid secret key" messages

*Solutions:*

.. code-block:: bash

   # Generate new secret keys
   python -c "import secrets; print(secrets.token_urlsafe(32))"
   
   # Update environment file
   echo "SECRET_KEY=$(python -c 'import secrets; print(secrets.token_urlsafe(32))')" >> .env
   echo "JWT_SECRET_KEY=$(python -c 'import secrets; print(secrets.token_urlsafe(32))')" >> .env
   
   # Restart services
   docker-compose restart backend

SSL/TLS Certificate Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: SSL certificate errors**

*Symptoms:*
- HTTPS connection failures
- Certificate validation errors
- "SSL handshake failed" messages

*Solutions:*

.. code-block:: bash

   # Check certificate validity
   openssl x509 -in /path/to/cert.pem -text -noout
   
   # Verify certificate chain
   openssl verify -CAfile /path/to/ca.pem /path/to/cert.pem
   
   # Test SSL connection
   openssl s_client -connect your-domain.com:443
   
   # Regenerate self-signed certificate (development only)
   openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

Performance Issues
------------------

Slow Response Times
~~~~~~~~~~~~~~~~~~~

**Issue: API endpoints responding slowly**

*Symptoms:*
- Response times > 5 seconds
- Timeout errors
- High CPU usage

*Diagnostic Steps:*

.. code-block:: bash

   # Check system resources
   docker stats
   
   # Monitor database performance
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "
   SELECT query, mean_time, calls, total_time 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC 
   LIMIT 10;"
   
   # Check slow queries
   docker-compose logs backend | grep "slow query"
   
   # Monitor Redis performance
   docker-compose exec redis redis-cli --latency

*Solutions:*

.. code-block:: bash

   # Increase worker processes
   echo "WORKERS=8" >> .env
   docker-compose restart backend
   
   # Optimize database
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "VACUUM ANALYZE;"
   
   # Clear Redis cache
   docker-compose exec redis redis-cli FLUSHALL
   
   # Restart services
   docker-compose restart

**Issue: Attack path analysis taking too long**

*Symptoms:*
- Analysis timeouts
- High memory usage during analysis
- "Analysis failed" errors

*Solutions:*

.. code-block:: bash

   # Reduce analysis depth
   echo "MAX_ATTACK_PATH_DEPTH=5" >> .env
   
   # Increase timeout
   echo "ATTACK_PATH_TIMEOUT=300" >> .env
   
   # Optimize Neo4j
   docker-compose exec neo4j cypher-shell -u neo4j -p blast_neo4j "
   CALL db.indexes();
   CALL db.constraints();"
   
   # Restart backend
   docker-compose restart backend

Memory Issues
~~~~~~~~~~~~~

**Issue: Out of memory errors**

*Symptoms:*
- "Out of memory" errors
- Services being killed
- Swap usage high

*Solutions:*

.. code-block:: bash

   # Check memory usage
   free -h
   docker stats --no-stream
   
   # Increase Docker memory limits
   # Edit docker-compose.yml
   services:
     backend:
       mem_limit: 4g
     postgresql:
       mem_limit: 2g
   
   # Optimize PostgreSQL memory
   docker-compose exec postgresql psql -U postgres -c "
   ALTER SYSTEM SET shared_buffers = '1GB';
   ALTER SYSTEM SET work_mem = '256MB';
   SELECT pg_reload_conf();"
   
   # Restart services
   docker-compose restart

Authentication & Authorization Issues
-------------------------------------

Login Problems
~~~~~~~~~~~~~~

**Issue: Cannot login with default credentials**

*Symptoms:*
- "Invalid credentials" error
- Login page redirects back
- Authentication failures in logs

*Solutions:*

.. code-block:: bash

   # Reset admin password
   cd backend
   docker-compose exec backend python -m app.cli reset-admin-password
   
   # Create new admin user
   docker-compose exec backend python -m app.cli create-admin \
     --email <EMAIL> \
     --password "NewPassword123!" \
     --first-name Admin \
     --last-name User
   
   # Check user exists
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "SELECT email, is_active FROM users;"

**Issue: JWT token errors**

*Symptoms:*
- "Token expired" errors
- "Invalid token" messages
- Frequent re-authentication required

*Solutions:*

.. code-block:: bash

   # Check JWT configuration
   cd backend
   python -c "from app.core.config import settings; print(f'JWT expires in: {settings.JWT_EXPIRE_MINUTES} minutes')"
   
   # Increase token lifetime (development only)
   echo "JWT_EXPIRE_MINUTES=480" >> .env
   
   # Clear browser cache and cookies
   # Restart backend
   docker-compose restart backend

Permission Errors
~~~~~~~~~~~~~~~~~

**Issue: "Access denied" errors**

*Symptoms:*
- Users cannot access certain features
- "Insufficient permissions" messages
- Role-based access not working

*Solutions:*

.. code-block:: bash

   # Check user roles
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "
   SELECT u.email, r.name as role 
   FROM users u 
   JOIN user_roles ur ON u.id = ur.user_id 
   JOIN roles r ON ur.role_id = r.id;"
   
   # Assign role to user
   docker-compose exec backend python -m app.cli assign-role \
     --email <EMAIL> \
     --role "Security Architect"
   
   # Check permissions
   docker-compose exec backend python -m app.cli list-permissions \
     --email <EMAIL>

Integration Issues
------------------

Cloud Provider Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: AWS discovery not working**

*Symptoms:*
- "Access denied" errors
- No assets discovered
- AWS API errors in logs

*Solutions:*

.. code-block:: bash

   # Validate AWS credentials
   aws sts get-caller-identity
   
   # Test AWS permissions
   aws ec2 describe-instances --max-items 1
   aws s3 ls
   
   # Check IAM permissions
   aws iam simulate-principal-policy \
     --policy-source-arn arn:aws:iam::************:user/blast-radius \
     --action-names ec2:DescribeInstances \
     --resource-arns "*"
   
   # Update credentials in environment
   echo "AWS_ACCESS_KEY_ID=your-key" >> .env
   echo "AWS_SECRET_ACCESS_KEY=your-secret" >> .env
   docker-compose restart backend

**Issue: Azure integration failures**

*Symptoms:*
- Authentication errors
- "Subscription not found" errors
- Azure API timeouts

*Solutions:*

.. code-block:: bash

   # Test Azure CLI authentication
   az login
   az account show
   
   # Validate service principal
   az ad sp show --id your-client-id
   
   # Test Azure permissions
   az vm list --output table
   az storage account list --output table
   
   # Update Azure configuration
   echo "AZURE_CLIENT_ID=your-client-id" >> .env
   echo "AZURE_CLIENT_SECRET=your-secret" >> .env
   echo "AZURE_TENANT_ID=your-tenant-id" >> .env

Network Connectivity Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Network discovery not finding assets**

*Symptoms:*
- Empty discovery results
- Network timeouts
- "Host unreachable" errors

*Solutions:*

.. code-block:: bash

   # Test network connectivity
   ping ***********
   nmap -sn ***********/24
   
   # Check firewall rules
   sudo iptables -L
   
   # Test from container
   docker-compose exec backend ping ***********
   docker-compose exec backend nmap -sn ***********/24
   
   # Update network configuration
   echo "NETWORK_DISCOVERY_TIMEOUT=60" >> .env
   echo "NETWORK_DISCOVERY_RETRIES=3" >> .env

Frontend Issues
---------------

UI Not Loading
~~~~~~~~~~~~~~

**Issue: Blank page or loading errors**

*Symptoms:*
- White screen
- JavaScript errors in console
- "Failed to load resource" errors

*Solutions:*

.. code-block:: bash

   # Check frontend logs
   docker-compose logs frontend
   
   # Check if backend is accessible
   curl http://localhost:8000/health
   
   # Clear browser cache
   # Hard refresh (Ctrl+F5)
   
   # Restart frontend
   docker-compose restart frontend
   
   # Check for CORS issues
   curl -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: X-Requested-With" \
        -X OPTIONS \
        http://localhost:8000/api/v1/health

**Issue: API calls failing**

*Symptoms:*
- "Network error" messages
- 404 or 500 errors
- CORS errors in browser console

*Solutions:*

.. code-block:: bash

   # Check API endpoint
   curl http://localhost:8000/api/v1/health
   
   # Check CORS configuration
   echo 'CORS_ORIGINS=["http://localhost:3000"]' >> .env
   
   # Verify API documentation
   open http://localhost:8000/docs
   
   # Check backend logs for errors
   docker-compose logs backend | grep ERROR

Data Issues
-----------

Missing or Incorrect Data
~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Assets not appearing in dashboard**

*Symptoms:*
- Empty asset inventory
- Discovery completed but no results
- Data inconsistencies

*Solutions:*

.. code-block:: bash

   # Check database for assets
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "SELECT COUNT(*) FROM assets;"
   
   # Check discovery job status
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "SELECT * FROM discovery_jobs ORDER BY created_at DESC LIMIT 5;"
   
   # Manually trigger discovery
   docker-compose exec backend python -m app.cli discovery run --target all
   
   # Check for data corruption
   docker-compose exec postgresql psql -U blast_user -d blast_radius -c "SELECT pg_stat_database.*, pg_database.datname FROM pg_stat_database JOIN pg_database ON pg_stat_database.datid = pg_database.oid WHERE datname = 'blast_radius';"

**Issue: Attack path analysis returns no results**

*Symptoms:*
- "No paths found" message
- Analysis completes quickly
- Graph appears empty

*Solutions:*

.. code-block:: bash

   # Check asset relationships
   docker-compose exec neo4j cypher-shell -u neo4j -p blast_neo4j "MATCH (n)-[r]->(m) RETURN count(r) as relationship_count;"
   
   # Verify assets in Neo4j
   docker-compose exec neo4j cypher-shell -u neo4j -p blast_neo4j "MATCH (n:Asset) RETURN count(n) as asset_count;"
   
   # Rebuild graph relationships
   docker-compose exec backend python -m app.cli graph rebuild
   
   # Check for isolated assets
   docker-compose exec neo4j cypher-shell -u neo4j -p blast_neo4j "MATCH (n:Asset) WHERE NOT (n)--() RETURN n.name as isolated_assets;"

Getting Additional Help
-----------------------

Log Collection
~~~~~~~~~~~~~~

**Collect comprehensive logs for support:**

.. code-block:: bash

   # Create support bundle
   mkdir blast-radius-support-$(date +%Y%m%d-%H%M%S)
   cd blast-radius-support-$(date +%Y%m%d-%H%M%S)
   
   # Collect logs
   docker-compose logs > docker-compose.log
   docker-compose logs backend > backend.log
   docker-compose logs frontend > frontend.log
   docker-compose logs postgresql > postgresql.log
   docker-compose logs redis > redis.log
   docker-compose logs neo4j > neo4j.log
   
   # Collect configuration
   cp ../.env environment.txt
   docker-compose config > docker-compose-config.yaml
   
   # Collect system info
   docker version > docker-info.txt
   docker-compose version >> docker-info.txt
   docker stats --no-stream > docker-stats.txt
   
   # Create archive
   cd ..
   tar -czf blast-radius-support-$(date +%Y%m%d-%H%M%S).tar.gz blast-radius-support-$(date +%Y%m%d-%H%M%S)/

Support Channels
~~~~~~~~~~~~~~~~

**Before contacting support:**

1. **Check this troubleshooting guide**
2. **Search existing GitHub issues**
3. **Collect logs and system information**
4. **Try the suggested solutions**
5. **Document steps to reproduce the issue**

**Contact Information:**

* **GitHub Issues:** https://github.com/forkrul/blast-radius/issues
* **Documentation:** This comprehensive guide
* **Community Forum:** https://community.blastradius.security
* **Enterprise Support:** <EMAIL>

**When creating a support request, include:**

* **Environment details** (OS, Docker version, etc.)
* **Steps to reproduce** the issue
* **Expected vs actual behavior**
* **Log files** and error messages
* **Configuration details** (sanitized)

.. note::
   Most issues can be resolved by following this troubleshooting guide.
   If you continue to experience problems, don't hesitate to reach out for help.
