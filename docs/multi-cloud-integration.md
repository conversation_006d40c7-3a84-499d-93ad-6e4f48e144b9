# Multi-Cloud Integration Guide

## Overview

The Blast-Radius Security Tool provides comprehensive multi-cloud asset discovery and security analysis across AWS, Azure, and Google Cloud Platform (GCP). This guide covers the implementation, configuration, and usage of the multi-cloud integration features.

## Architecture

### Discovery Engine Architecture

```mermaid
graph TB
    subgraph "Discovery Orchestrator"
        A[Discovery Orchestrator] --> B[Cloud Discovery Engine]
        A --> C[API Discovery Engine]
        A --> D[Network Discovery Engine]
    end
    
    subgraph "Cloud Providers"
        B --> E[AWS Discovery Service]
        B --> F[Azure Discovery Service]
        B --> G[GCP Discovery Service]
    end
    
    subgraph "Asset Processing"
        E --> H[Asset Service]
        F --> H
        G --> H
        H --> I[Graph Engine]
        I --> J[Risk Assessment]
    end
    
    subgraph "Storage"
        H --> K[PostgreSQL Database]
        I --> L[NetworkX Graph]
    end
```

### Supported Cloud Services

#### AWS Services
- **Compute**: EC2 Instances, Lambda Functions
- **Storage**: S3 Buckets, EBS Volumes
- **Database**: RDS Instances, DynamoDB Tables
- **Network**: VPCs, Security Groups, Load Balancers
- **Container**: ECS Clusters, EKS Clusters

#### Azure Services
- **Compute**: Virtual Machines, Function Apps
- **Storage**: Storage Accounts, Blob Containers
- **Database**: SQL Databases, Cosmos DB
- **Network**: Virtual Networks, Network Security Groups, Load Balancers
- **Container**: Container Instances, AKS Clusters
- **Web**: App Services, API Management

#### GCP Services
- **Compute**: Compute Engine Instances, Cloud Functions
- **Storage**: Cloud Storage Buckets, Persistent Disks
- **Database**: Cloud SQL, Firestore
- **Network**: VPC Networks, Cloud Load Balancing
- **Container**: GKE Clusters, Cloud Run

## Configuration

### AWS Configuration

```json
{
  "provider": "aws",
  "access_key_id": "AKIA...",
  "secret_access_key": "...",
  "regions": ["us-east-1", "us-west-2", "eu-west-1"],
  "services": ["ec2", "s3", "rds", "lambda", "ecs"],
  "assume_role_arn": "arn:aws:iam::********9012:role/BlastRadiusRole"
}
```

### Azure Configuration

```json
{
  "provider": "azure",
  "subscription_id": "********-1234-1234-1234-********9012",
  "client_id": "app-client-id",
  "client_secret": "app-client-secret",
  "tenant_id": "tenant-id",
  "resource_groups": ["production-rg", "staging-rg"],
  "regions": ["eastus", "westus2", "northeurope"],
  "services": ["compute", "storage", "sql", "web", "containers", "network"]
}
```

### GCP Configuration

```json
{
  "provider": "gcp",
  "project_id": "my-project-12345",
  "credentials_path": "/path/to/service-account.json",
  "regions": ["us-central1", "us-east1", "europe-west1"],
  "services": ["compute", "storage", "sql", "containers", "functions"]
}
```

## Installation and Setup

### Prerequisites

#### AWS SDK
```bash
pip install boto3 botocore
```

#### Azure SDK
```bash
pip install azure-identity azure-mgmt-resource azure-mgmt-compute \
            azure-mgmt-storage azure-mgmt-sql azure-mgmt-web \
            azure-mgmt-containerservice azure-mgmt-network
```

#### GCP SDK
```bash
pip install google-cloud-compute google-cloud-storage \
            google-cloud-sql google-cloud-container \
            google-cloud-asset
```

### Authentication Setup

#### AWS Authentication
1. **IAM User with Access Keys**:
   ```bash
   aws configure
   ```

2. **IAM Role with AssumeRole**:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": "arn:aws:iam::ACCOUNT:user/blast-radius-user"
         },
         "Action": "sts:AssumeRole"
       }
     ]
   }
   ```

3. **Required IAM Permissions**:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "ec2:Describe*",
           "s3:ListAllMyBuckets",
           "s3:GetBucketLocation",
           "rds:Describe*",
           "lambda:List*",
           "ecs:Describe*",
           "eks:Describe*"
         ],
         "Resource": "*"
       }
     ]
   }
   ```

#### Azure Authentication
1. **Service Principal**:
   ```bash
   az ad sp create-for-rbac --name "blast-radius-sp" \
     --role "Reader" \
     --scopes "/subscriptions/SUBSCRIPTION_ID"
   ```

2. **Required Azure Permissions**:
   - Reader role on subscription or resource groups
   - Additional permissions for specific services:
     - `Microsoft.Compute/virtualMachines/read`
     - `Microsoft.Storage/storageAccounts/read`
     - `Microsoft.Sql/servers/read`
     - `Microsoft.Web/sites/read`

#### GCP Authentication
1. **Service Account**:
   ```bash
   gcloud iam service-accounts create blast-radius-sa \
     --display-name="Blast Radius Service Account"
   ```

2. **Required GCP Permissions**:
   ```bash
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member="serviceAccount:blast-radius-sa@PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/compute.viewer"
   
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member="serviceAccount:blast-radius-sa@PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/storage.objectViewer"
   ```

3. **Download Service Account Key**:
   ```bash
   gcloud iam service-accounts keys create key.json \
     --iam-account=blast-radius-sa@PROJECT_ID.iam.gserviceaccount.com
   ```

## Usage

### API Endpoints

#### Start AWS Discovery
```bash
curl -X POST "http://localhost:8000/api/v1/discovery/cloud/aws" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "access_key_id": "AKIA...",
    "secret_access_key": "...",
    "regions": ["us-east-1", "us-west-2"],
    "services": ["ec2", "s3", "rds"]
  }'
```

#### Start Azure Discovery
```bash
curl -X POST "http://localhost:8000/api/v1/discovery/cloud/azure" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "subscription_id": "********-1234-1234-1234-********9012",
    "client_id": "app-client-id",
    "client_secret": "app-client-secret",
    "tenant_id": "tenant-id",
    "services": ["compute", "storage", "sql"]
  }'
```

#### Start GCP Discovery
```bash
curl -X POST "http://localhost:8000/api/v1/discovery/cloud/gcp" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "my-project-12345",
    "credentials_path": "/path/to/service-account.json",
    "services": ["compute", "storage", "sql"]
  }'
```

### Python SDK Usage

```python
from app.services.discovery.cloud_discovery import CloudDiscoveryEngine
from app.services.discovery_orchestrator import DiscoveryOrchestrator

# AWS Discovery
aws_config = {
    "provider": "aws",
    "access_key_id": "AKIA...",
    "secret_access_key": "...",
    "regions": ["us-east-1"]
}

aws_engine = CloudDiscoveryEngine(aws_config)
aws_result = await aws_engine.discover()

# Azure Discovery
azure_config = {
    "provider": "azure",
    "subscription_id": "********-1234-1234-1234-********9012"
}

azure_engine = CloudDiscoveryEngine(azure_config)
azure_result = await azure_engine.discover()

# GCP Discovery
gcp_config = {
    "provider": "gcp",
    "project_id": "my-project-12345"
}

gcp_engine = CloudDiscoveryEngine(gcp_config)
gcp_result = await gcp_engine.discover()
```

## Asset Types and Risk Assessment

### Asset Type Mapping

| Cloud Service | Asset Type | Risk Factors |
|---------------|------------|--------------|
| EC2/VM/Compute Engine | `CLOUD_INSTANCE` | Public IP, OS type, size, environment |
| S3/Storage/Cloud Storage | `STORAGE` | Public access, encryption, versioning |
| RDS/SQL/Cloud SQL | `DATABASE` | Public access, version, backup config |
| Lambda/Functions | `APPLICATION` | Runtime, permissions, triggers |
| ECS/AKS/GKE | `CONTAINER` | Public access, version, node count |

### Risk Score Calculation

Risk scores are calculated based on multiple factors:

1. **Base Risk by Asset Type**:
   - Database: 50 points
   - Application: 45 points
   - Cloud Instance: 40 points
   - Container: 35 points
   - Storage: 30 points

2. **Environment Modifiers**:
   - Production: +25 points
   - Staging: +15 points
   - Development: +5 points

3. **Exposure Modifiers**:
   - Public IP: +20 points
   - Public access: +25 points
   - No encryption: +15 points

4. **Configuration Modifiers**:
   - Outdated versions: +10-15 points
   - Weak security settings: +10-25 points
   - No backups: +10 points

### Risk Levels

- **Critical (80-100)**: Immediate attention required
- **High (60-79)**: High priority remediation
- **Medium (40-59)**: Moderate risk, monitor closely
- **Low (0-39)**: Acceptable risk level

## Monitoring and Alerting

### Discovery Job Monitoring

```python
# Get discovery job status
job_status = await orchestrator.get_job_status(job_id)

# Monitor active jobs
active_jobs = orchestrator.get_active_jobs()

# Get discovery statistics
stats = await orchestrator.get_discovery_statistics()
```

### Error Handling

The discovery engines implement comprehensive error handling:

1. **Authentication Errors**: Invalid credentials or permissions
2. **Network Errors**: Connectivity issues or timeouts
3. **Rate Limiting**: API throttling and retry logic
4. **Resource Errors**: Missing or inaccessible resources

### Logging

Discovery operations are logged with structured logging:

```python
import logging

logger = logging.getLogger("discovery")
logger.info("Starting AWS discovery", extra={
    "provider": "aws",
    "regions": ["us-east-1"],
    "job_id": str(job_id)
})
```

## Performance Optimization

### Concurrent Discovery

Discovery operations can be run concurrently across:
- Multiple cloud providers
- Multiple regions within a provider
- Multiple services within a region

### Caching and Incremental Updates

- Asset data is cached to avoid redundant API calls
- Incremental discovery updates only changed assets
- Relationship graphs are updated efficiently using NetworkX

### Resource Limits

Configure appropriate limits to prevent resource exhaustion:

```python
discovery_config = {
    "max_concurrent_requests": 10,
    "request_timeout": 30,
    "batch_size": 100,
    "max_retries": 3
}
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Verify credentials and permissions
   - Check token expiration
   - Validate service principal configuration

2. **Discovery Timeouts**:
   - Increase timeout values
   - Reduce batch sizes
   - Check network connectivity

3. **Missing Assets**:
   - Verify service permissions
   - Check region configuration
   - Review resource filters

4. **High Memory Usage**:
   - Reduce concurrent operations
   - Implement pagination
   - Clear asset caches

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger("discovery").setLevel(logging.DEBUG)
```

## Security Considerations

### Credential Management

- Store credentials securely using environment variables or secret management systems
- Rotate credentials regularly
- Use least-privilege access principles
- Implement credential encryption at rest

### Network Security

- Use VPN or private endpoints when possible
- Implement IP whitelisting for API access
- Monitor discovery traffic for anomalies

### Data Protection

- Encrypt discovery data in transit and at rest
- Implement data retention policies
- Anonymize sensitive asset information
- Audit discovery operations

## Future Enhancements

### Planned Features

1. **Additional Cloud Providers**:
   - Oracle Cloud Infrastructure (OCI)
   - IBM Cloud
   - Alibaba Cloud

2. **Enhanced Discovery**:
   - Kubernetes cluster deep scanning
   - Serverless function analysis
   - Container image vulnerability scanning

3. **Advanced Analytics**:
   - Machine learning-based risk assessment
   - Anomaly detection for asset changes
   - Predictive security analytics

4. **Integration Improvements**:
   - Real-time streaming discovery
   - Webhook-based change notifications
   - GraphQL API support
