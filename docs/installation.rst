Installation Guide
==================

This comprehensive guide will walk you through installing and setting up the Blast-Radius Security Tool for different deployment scenarios.

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

**Minimum Requirements:**

* **Operating System**: Linux (Ubuntu 20.04+, CentOS 8+), macOS 10.15+, or Windows 10+
* **Memory**: 8GB RAM (16GB+ recommended for production)
* **Storage**: 50GB free space (100GB+ recommended for production)
* **CPU**: 4 cores (8+ cores recommended for production)
* **Network**: Internet connection for downloading dependencies

**Production Requirements:**

* **Memory**: 32GB+ RAM for large-scale deployments
* **Storage**: 500GB+ SSD storage with backup capabilities
* **CPU**: 16+ cores for optimal performance
* **Network**: High-bandwidth connection for real-time data processing

Software Dependencies
~~~~~~~~~~~~~~~~~~~~~

**Backend Requirements:**

* **Python**: 3.11 or higher
* **PostgreSQL**: 15.0 or higher
* **Redis**: 7.0 or higher
* **Neo4j**: 5.15 or higher (for graph analysis)

**Frontend Requirements:**

* **Node.js**: 18.0 or higher
* **npm**: 8.0 or higher (or yarn 1.22+)

**Infrastructure Requirements:**

* **Docker**: 20.10 or higher
* **Docker Compose**: 2.0 or higher
* **Git**: Latest version

Installation Methods
--------------------

Method 1: Quick Start with Docker (Recommended)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This is the fastest way to get Blast-Radius running for evaluation and development.

**Step 1: Clone the Repository**

.. code-block:: bash

   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius

**Step 2: Start the Platform**

.. code-block:: bash

   # Start all services with one command
   docker-compose up -d

**Step 3: Access the Platform**

* **Web Dashboard**: http://localhost:3000
* **API Documentation**: http://localhost:8000/docs
* **Monitoring**: http://localhost:3001

**Default Credentials:**

* **Username**: <EMAIL>
* **Password**: BlastRadius2024!

.. warning::
   Change the default credentials immediately after first login.

Method 2: Development Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This method is recommended for development and customization.

**Step 1: Clone and Setup Backend**

.. code-block:: bash

   git clone https://github.com/forkrul/blast-radius.git
   cd blast-radius/backend
   
   # Create virtual environment
   python3.11 -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   
   # Install dependencies
   make install-dev
   
   # Copy environment configuration
   cp .env.example .env

**Step 2: Configure Environment**

Edit the `.env` file with your settings:

.. code-block:: bash

   # Database Configuration
   DATABASE_URL="postgresql://blast_user:blast_pass@localhost:5432/blast_radius"
   
   # Redis Configuration
   REDIS_URL="redis://localhost:6379/0"
   
   # Neo4j Configuration
   NEO4J_URI="bolt://localhost:7687"
   NEO4J_USER="neo4j"
   NEO4J_PASSWORD="blast_neo4j"
   
   # Security Configuration
   SECRET_KEY="your-super-secret-key-here"
   JWT_SECRET_KEY="your-jwt-secret-key-here"

**Step 3: Setup Databases**

.. code-block:: bash

   # Start database services
   docker-compose up -d postgresql redis neo4j
   
   # Run database migrations
   make migrate-upgrade
   
   # Create initial admin user
   make create-admin-user

**Step 4: Setup Frontend**

.. code-block:: bash

   cd ../frontend
   
   # Install dependencies
   npm install
   
   # Start development server
   npm start

**Step 5: Start Backend**

.. code-block:: bash

   cd ../backend
   make run-dev

Method 3: Production Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This method is for production deployments with high availability and security.

**Step 1: Prepare Production Environment**

.. code-block:: bash

   # Create dedicated user
   sudo useradd -m -s /bin/bash blastradius
   sudo usermod -aG docker blastradius
   
   # Create application directories
   sudo mkdir -p /opt/blast-radius/{data,logs,config,backups}
   sudo chown -R blastradius:blastradius /opt/blast-radius

**Step 2: Clone and Configure**

.. code-block:: bash

   sudo -u blastradius git clone https://github.com/forkrul/blast-radius.git /opt/blast-radius/app
   cd /opt/blast-radius/app
   
   # Copy production configuration
   sudo -u blastradius cp .env.production .env
   
   # Edit configuration for your environment
   sudo -u blastradius nano .env

**Step 3: Setup SSL Certificates**

.. code-block:: bash

   # Using Let's Encrypt (recommended)
   sudo apt install certbot
   sudo certbot certonly --standalone -d your-domain.com
   
   # Copy certificates to application directory
   sudo cp /etc/letsencrypt/live/your-domain.com/*.pem /opt/blast-radius/config/

**Step 4: Deploy with Docker Compose**

.. code-block:: bash

   # Start production services
   sudo -u blastradius docker-compose -f docker-compose.prod.yml up -d
   
   # Verify deployment
   sudo -u blastradius docker-compose -f docker-compose.prod.yml ps

Configuration
-------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

**Core Application Settings:**

.. code-block:: bash

   # Application Identity
   PROJECT_NAME="Blast-Radius Security Tool"
   VERSION="1.0.0"
   ENVIRONMENT="production"  # development, staging, production
   DEBUG=false
   LOG_LEVEL="info"
   
   # Server Configuration
   HOST="0.0.0.0"
   PORT=8000
   WORKERS=4

**Database Configuration:**

.. code-block:: bash

   # PostgreSQL
   DATABASE_URL="********************************/dbname"
   DATABASE_POOL_SIZE=20
   DATABASE_MAX_OVERFLOW=30
   
   # Redis
   REDIS_URL="redis://host:6379/0"
   REDIS_POOL_SIZE=10
   
   # Neo4j
   NEO4J_URI="bolt://host:7687"
   NEO4J_USER="neo4j"
   NEO4J_PASSWORD="password"

**Security Configuration:**

.. code-block:: bash

   # Encryption Keys
   SECRET_KEY="your-256-bit-secret-key"
   JWT_SECRET_KEY="your-jwt-secret-key"
   JWT_ALGORITHM="HS256"
   JWT_EXPIRE_MINUTES=30
   
   # Session Configuration
   SESSION_COOKIE_SECURE=true
   SESSION_COOKIE_HTTPONLY=true
   SESSION_COOKIE_SAMESITE="strict"
   
   # CORS Configuration
   CORS_ORIGINS='["https://your-domain.com"]'

Initial Setup
-------------

Creating Admin User
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Using the CLI tool
   cd backend
   python -m app.cli create-admin \
     --email <EMAIL> \
     --password "SecurePassword123!" \
     --first-name "Admin" \
     --last-name "User"

Configuring Integrations
~~~~~~~~~~~~~~~~~~~~~~~~

**Cloud Provider Setup:**

.. code-block:: bash

   # AWS Configuration
   AWS_ACCESS_KEY_ID="your-access-key"
   AWS_SECRET_ACCESS_KEY="your-secret-key"
   AWS_DEFAULT_REGION="us-east-1"
   
   # Azure Configuration
   AZURE_CLIENT_ID="your-client-id"
   AZURE_CLIENT_SECRET="your-client-secret"
   AZURE_TENANT_ID="your-tenant-id"
   
   # GCP Configuration
   GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

**ServiceNow Integration:**

.. code-block:: bash

   SERVICENOW_INSTANCE="your-instance.service-now.com"
   SERVICENOW_USERNAME="integration-user"
   SERVICENOW_PASSWORD="integration-password"
   SERVICENOW_API_VERSION="v1"

Verification
------------

Health Checks
~~~~~~~~~~~~~

.. code-block:: bash

   # Check application health
   curl http://localhost:8000/health
   
   # Check database connectivity
   curl http://localhost:8000/health/db
   
   # Check all services
   curl http://localhost:8000/health/detailed

Performance Testing
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run basic performance tests
   cd backend
   make test-performance
   
   # Load testing (requires additional setup)
   make test-load

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Database Connection Issues:**

.. code-block:: bash

   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Test connection
   psql -h localhost -U blast_user -d blast_radius -c "SELECT 1;"

**Redis Connection Issues:**

.. code-block:: bash

   # Check Redis status
   sudo systemctl status redis
   
   # Test connection
   redis-cli ping

**Neo4j Connection Issues:**

.. code-block:: bash

   # Check Neo4j status
   sudo systemctl status neo4j
   
   # Test connection
   cypher-shell -u neo4j -p password "RETURN 1;"

**Port Conflicts:**

.. code-block:: bash

   # Check what's using port 8000
   sudo netstat -tulpn | grep :8000
   
   # Kill process if needed
   sudo kill -9 <PID>

Getting Help
~~~~~~~~~~~~

If you encounter issues during installation:

1. Check the :doc:`troubleshooting/common-issues` guide
2. Review application logs in `/opt/blast-radius/logs/`
3. Search existing issues on `GitHub <https://github.com/forkrul/blast-radius/issues>`_
4. Create a new issue with detailed error information

Next Steps
----------

After successful installation:

1. Read the :doc:`quick-start-guide` for initial setup
2. Configure :doc:`user-guides/administrators` settings
3. Set up user roles in :doc:`security/access-control`
4. Review :doc:`security/best-practices` for production deployments

.. note::
   For enterprise deployments, consider consulting with our professional services team
   for customized installation and configuration assistance.
