Testing & Quality Assurance
============================

Comprehensive testing documentation for the Blast-Radius Security Tool, covering all aspects of quality assurance from unit tests to security validation.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Blast-Radius Security Tool maintains enterprise-grade quality through:

* **95%+ test coverage** across all components
* **Multi-layered testing strategy** from unit to integration
* **Automated security testing** with SAST/DAST tools
* **Performance testing** for scalability validation
* **Continuous integration** with automated quality gates

Testing Philosophy
------------------

**Quality Principles:**

1. **Test Early, Test Often** - Shift-left testing approach
2. **Comprehensive Coverage** - Unit, integration, and end-to-end tests
3. **Security First** - Security testing integrated throughout
4. **Performance Validation** - Load and stress testing
5. **Automated Quality Gates** - No manual testing bottlenecks

**Testing Pyramid:**

.. mermaid::

   graph TB
       A[End-to-End Tests] --> B[Integration Tests]
       B --> C[Unit Tests]
       
       A1[UI Tests<br/>API Tests<br/>Security Tests] --> A
       B1[Service Integration<br/>Database Tests<br/>External API Tests] --> B
       C1[Function Tests<br/>Class Tests<br/>Module Tests] --> C
       
       style A fill:#ff9999
       style B fill:#ffcc99
       style C fill:#99ff99

Test Categories
---------------

Unit Testing
~~~~~~~~~~~~

**Coverage Requirements:**

* **Minimum Coverage:** 90% for all modules
* **Critical Components:** 95% coverage required
* **New Code:** 100% coverage for new features

**Testing Framework:**

.. code-block:: python

   # pytest configuration
   # pytest.ini
   [tool:pytest]
   testpaths = tests
   python_files = test_*.py
   python_classes = Test*
   python_functions = test_*
   addopts = 
       --cov=app
       --cov-report=html
       --cov-report=term-missing
       --cov-fail-under=90
       --strict-markers
       --disable-warnings

**Example Unit Test:**

.. code-block:: python

   # tests/test_attack_path_service.py
   import pytest
   from app.services.attack_path_service import AttackPathService
   from app.models.asset import Asset
   
   class TestAttackPathService:
       
       @pytest.fixture
       def service(self):
           return AttackPathService()
       
       @pytest.fixture
       def sample_assets(self):
           return [
               Asset(id=1, name="web-server", type="server"),
               Asset(id=2, name="database", type="database")
           ]
       
       def test_find_attack_paths_success(self, service, sample_assets):
           # Arrange
           source = sample_assets[0]
           target = sample_assets[1]
           
           # Act
           paths = service.find_attack_paths(source, target, max_depth=5)
           
           # Assert
           assert len(paths) > 0
           assert all(path.source_id == source.id for path in paths)
           assert all(path.target_id == target.id for path in paths)
       
       def test_find_attack_paths_no_path_exists(self, service):
           # Test edge case where no path exists
           source = Asset(id=1, name="isolated", type="server")
           target = Asset(id=2, name="unreachable", type="server")
           
           paths = service.find_attack_paths(source, target)
           assert len(paths) == 0

Integration Testing
~~~~~~~~~~~~~~~~~~~

**Database Integration Tests:**

.. code-block:: python

   # tests/integration/test_database_integration.py
   import pytest
   from sqlalchemy import create_engine
   from app.db.session import SessionLocal
   from app.models.asset import Asset
   
   @pytest.mark.integration
   class TestDatabaseIntegration:
       
       def test_asset_crud_operations(self, db_session):
           # Create
           asset = Asset(name="test-server", type="server")
           db_session.add(asset)
           db_session.commit()
           
           # Read
           retrieved = db_session.query(Asset).filter_by(name="test-server").first()
           assert retrieved is not None
           assert retrieved.name == "test-server"
           
           # Update
           retrieved.description = "Updated description"
           db_session.commit()
           
           # Delete
           db_session.delete(retrieved)
           db_session.commit()
           
           # Verify deletion
           deleted = db_session.query(Asset).filter_by(name="test-server").first()
           assert deleted is None

**API Integration Tests:**

.. code-block:: python

   # tests/integration/test_api_integration.py
   import pytest
   from fastapi.testclient import TestClient
   from app.main import app
   
   @pytest.mark.integration
   class TestAPIIntegration:
       
       @pytest.fixture
       def client(self):
           return TestClient(app)
       
       @pytest.fixture
       def auth_headers(self, client):
           # Login and get JWT token
           response = client.post("/api/v1/auth/login", json={
               "email": "<EMAIL>",
               "password": "testpassword"
           })
           token = response.json()["access_token"]
           return {"Authorization": f"Bearer {token}"}
       
       def test_asset_discovery_workflow(self, client, auth_headers):
           # Start discovery
           response = client.post(
               "/api/v1/discovery/start",
               json={"target": "192.168.1.0/24"},
               headers=auth_headers
           )
           assert response.status_code == 200
           job_id = response.json()["job_id"]
           
           # Check status
           response = client.get(f"/api/v1/discovery/status/{job_id}", headers=auth_headers)
           assert response.status_code == 200
           assert response.json()["status"] in ["running", "completed"]

End-to-End Testing
~~~~~~~~~~~~~~~~~~

**Browser Automation Tests:**

.. code-block:: python

   # tests/e2e/test_user_workflows.py
   import pytest
   from selenium import webdriver
   from selenium.webdriver.common.by import By
   from selenium.webdriver.support.ui import WebDriverWait
   from selenium.webdriver.support import expected_conditions as EC
   
   @pytest.mark.e2e
   class TestUserWorkflows:
       
       @pytest.fixture
       def driver(self):
           options = webdriver.ChromeOptions()
           options.add_argument("--headless")
           driver = webdriver.Chrome(options=options)
           yield driver
           driver.quit()
       
       def test_complete_attack_path_analysis(self, driver):
           # Login
           driver.get("http://localhost:3000/login")
           driver.find_element(By.ID, "email").send_keys("<EMAIL>")
           driver.find_element(By.ID, "password").send_keys("password")
           driver.find_element(By.ID, "login-button").click()
           
           # Navigate to attack path analysis
           WebDriverWait(driver, 10).until(
               EC.element_to_be_clickable((By.ID, "attack-paths-menu"))
           ).click()
           
           # Configure analysis
           source_dropdown = driver.find_element(By.ID, "source-asset")
           source_dropdown.click()
           driver.find_element(By.XPATH, "//option[text()='Web Server']").click()
           
           target_dropdown = driver.find_element(By.ID, "target-asset")
           target_dropdown.click()
           driver.find_element(By.XPATH, "//option[text()='Database']").click()
           
           # Start analysis
           driver.find_element(By.ID, "start-analysis").click()
           
           # Wait for results
           WebDriverWait(driver, 30).until(
               EC.presence_of_element_located((By.ID, "analysis-results"))
           )
           
           # Verify results
           results = driver.find_element(By.ID, "analysis-results")
           assert "Attack paths found" in results.text

Security Testing
~~~~~~~~~~~~~~~~

**SAST (Static Application Security Testing):**

.. code-block:: bash

   # Security testing with multiple tools
   
   # Bandit - Python security linter
   bandit -r app/ -f json -o reports/bandit-results.json
   
   # Semgrep - Multi-language static analysis
   semgrep --config=auto --json --output=reports/semgrep-results.json app/
   
   # Safety - Python dependency vulnerability scanner
   safety check --json --output reports/safety-results.json

**DAST (Dynamic Application Security Testing):**

.. code-block:: python

   # tests/security/test_api_security.py
   import pytest
   import requests
   from app.core.config import settings
   
   @pytest.mark.security
   class TestAPISecurity:
       
       def test_sql_injection_protection(self):
           # Test SQL injection attempts
           malicious_payloads = [
               "'; DROP TABLE users; --",
               "1' OR '1'='1",
               "admin'/*",
               "1; SELECT * FROM users"
           ]
           
           for payload in malicious_payloads:
               response = requests.get(
                   f"{settings.API_V1_STR}/assets",
                   params={"search": payload}
               )
               # Should not return 500 or expose database errors
               assert response.status_code != 500
               assert "database" not in response.text.lower()
               assert "sql" not in response.text.lower()
       
       def test_xss_protection(self):
           # Test XSS attempts
           xss_payloads = [
               "<script>alert('xss')</script>",
               "javascript:alert('xss')",
               "<img src=x onerror=alert('xss')>"
           ]
           
           for payload in xss_payloads:
               response = requests.post(
                   f"{settings.API_V1_STR}/assets",
                   json={"name": payload, "type": "server"}
               )
               # Should sanitize input
               if response.status_code == 200:
                   assert "<script>" not in response.text
                   assert "javascript:" not in response.text

Performance Testing
~~~~~~~~~~~~~~~~~~~

**Load Testing Configuration:**

.. code-block:: yaml

   # locustfile.py
   from locust import HttpUser, task, between
   
   class BlastRadiusUser(HttpUser):
       wait_time = between(1, 3)
       
       def on_start(self):
           # Login
           response = self.client.post("/api/v1/auth/login", json={
               "email": "<EMAIL>",
               "password": "testpassword"
           })
           self.token = response.json()["access_token"]
           self.headers = {"Authorization": f"Bearer {self.token}"}
       
       @task(3)
       def view_assets(self):
           self.client.get("/api/v1/assets", headers=self.headers)
       
       @task(2)
       def view_attack_paths(self):
           self.client.get("/api/v1/attack-paths", headers=self.headers)
       
       @task(1)
       def run_analysis(self):
           self.client.post("/api/v1/analysis/attack-paths", 
               json={
                   "source_asset_id": 1,
                   "target_asset_id": 2,
                   "max_depth": 5
               },
               headers=self.headers
           )

Test Automation
---------------

CI/CD Pipeline
~~~~~~~~~~~~~~

**GitHub Actions Workflow:**

.. code-block:: yaml

   # .github/workflows/test.yml
   name: Test Suite
   
   on:
     push:
       branches: [ main, develop ]
     pull_request:
       branches: [ main ]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       
       services:
         postgres:
           image: postgres:15
           env:
             POSTGRES_PASSWORD: postgres
           options: >-
             --health-cmd pg_isready
             --health-interval 10s
             --health-timeout 5s
             --health-retries 5
         
         redis:
           image: redis:7
           options: >-
             --health-cmd "redis-cli ping"
             --health-interval 10s
             --health-timeout 5s
             --health-retries 5
       
       steps:
       - uses: actions/checkout@v3
       
       - name: Set up Python
         uses: actions/setup-python@v4
         with:
           python-version: '3.11'
       
       - name: Install dependencies
         run: |
           pip install -r requirements-test.txt
       
       - name: Run unit tests
         run: |
           pytest tests/unit/ --cov=app --cov-report=xml
       
       - name: Run integration tests
         run: |
           pytest tests/integration/ --cov=app --cov-append --cov-report=xml
       
       - name: Run security tests
         run: |
           bandit -r app/ -f json -o bandit-report.json
           safety check --json --output safety-report.json
       
       - name: Upload coverage
         uses: codecov/codecov-action@v3
         with:
           file: ./coverage.xml

Quality Gates
~~~~~~~~~~~~~

**Pre-commit Hooks:**

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: https://github.com/pre-commit/pre-commit-hooks
       rev: v4.4.0
       hooks:
         - id: trailing-whitespace
         - id: end-of-file-fixer
         - id: check-yaml
         - id: check-added-large-files
     
     - repo: https://github.com/psf/black
       rev: 23.3.0
       hooks:
         - id: black
     
     - repo: https://github.com/pycqa/isort
       rev: 5.12.0
       hooks:
         - id: isort
     
     - repo: https://github.com/pycqa/flake8
       rev: 6.0.0
       hooks:
         - id: flake8
     
     - repo: https://github.com/PyCQA/bandit
       rev: 1.7.5
       hooks:
         - id: bandit
           args: ['-r', 'app/']

Test Data Management
--------------------

Fixtures and Factories
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/factories.py
   import factory
   from app.models.asset import Asset
   from app.models.user import User
   
   class AssetFactory(factory.Factory):
       class Meta:
           model = Asset
       
       name = factory.Sequence(lambda n: f"asset-{n}")
       type = factory.Iterator(["server", "database", "workstation"])
       ip_address = factory.Faker("ipv4")
       description = factory.Faker("text", max_nb_chars=200)
   
   class UserFactory(factory.Factory):
       class Meta:
           model = User
       
       email = factory.Faker("email")
       first_name = factory.Faker("first_name")
       last_name = factory.Faker("last_name")
       is_active = True

Test Environment Setup
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # conftest.py
   import pytest
   from sqlalchemy import create_engine
   from sqlalchemy.orm import sessionmaker
   from app.db.base import Base
   from app.core.config import settings
   
   @pytest.fixture(scope="session")
   def engine():
       return create_engine(settings.TEST_DATABASE_URL)
   
   @pytest.fixture(scope="session")
   def tables(engine):
       Base.metadata.create_all(engine)
       yield
       Base.metadata.drop_all(engine)
   
   @pytest.fixture
   def db_session(engine, tables):
       connection = engine.connect()
       transaction = connection.begin()
       session = sessionmaker(bind=connection)()
       
       yield session
       
       session.close()
       transaction.rollback()
       connection.close()

Running Tests
-------------

Local Development
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Install test dependencies
   pip install -r requirements-test.txt
   
   # Run all tests
   make test
   
   # Run specific test categories
   make test-unit          # Unit tests only
   make test-integration   # Integration tests only
   make test-e2e          # End-to-end tests only
   make test-security     # Security tests only
   make test-performance  # Performance tests only
   
   # Run with coverage
   make test-coverage
   
   # Run specific test file
   pytest tests/test_attack_path_service.py -v
   
   # Run tests matching pattern
   pytest -k "test_attack_path" -v

Continuous Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # CI test commands
   
   # Fast feedback loop (unit tests)
   pytest tests/unit/ --maxfail=1 --tb=short
   
   # Full test suite
   pytest tests/ --cov=app --cov-report=html --cov-report=term
   
   # Security testing
   bandit -r app/ -ll
   safety check
   
   # Performance baseline
   locust --headless --users 100 --spawn-rate 10 --run-time 60s

Test Reporting
--------------

Coverage Reports
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Generate coverage report
   pytest --cov=app --cov-report=html --cov-report=term-missing
   
   # View HTML report
   open htmlcov/index.html

Quality Metrics
~~~~~~~~~~~~~~~

**Key Quality Indicators:**

* **Test Coverage:** >90% overall, >95% for critical components
* **Test Execution Time:** <5 minutes for full suite
* **Security Scan Results:** Zero high/critical vulnerabilities
* **Performance Benchmarks:** Meet defined SLA requirements

Best Practices
--------------

Test Writing Guidelines
~~~~~~~~~~~~~~~~~~~~~~~

1. **Follow AAA Pattern** - Arrange, Act, Assert
2. **Use Descriptive Names** - Test names should explain what they test
3. **Test One Thing** - Each test should verify one specific behavior
4. **Use Fixtures** - Reuse common test setup
5. **Mock External Dependencies** - Isolate units under test
6. **Test Edge Cases** - Include boundary conditions and error cases

Maintenance
~~~~~~~~~~~

1. **Regular Test Review** - Remove obsolete tests
2. **Update Test Data** - Keep test fixtures current
3. **Monitor Test Performance** - Optimize slow tests
4. **Review Coverage** - Identify untested code paths
5. **Update Dependencies** - Keep testing tools current

Next Steps
----------

For detailed testing guidance:

1. Review :doc:`unit-tests` for comprehensive unit testing
2. Set up :doc:`integration-tests` for service validation
3. Implement :doc:`security-tests` for vulnerability scanning
4. Configure :doc:`performance-tests` for load validation

.. note::
   Testing is a continuous process. Regularly review and improve your test suite
   to maintain high quality and catch issues early.
