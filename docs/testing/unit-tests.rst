Unit Testing Guide
===================

Comprehensive guide to unit testing in the Blast-Radius Security Tool, covering testing strategies, frameworks, and best practices for ensuring code quality and reliability.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Unit testing forms the foundation of our testing strategy, providing:

* **Fast Feedback** - Quick validation of code changes
* **Regression Prevention** - Catch bugs before they reach production
* **Documentation** - Tests serve as living documentation
* **Design Validation** - Ensure code is well-designed and testable
* **Confidence** - Enable safe refactoring and feature development

Testing Framework
-----------------

pytest Configuration
~~~~~~~~~~~~~~~~~~~~

We use **pytest** as our primary testing framework:

.. code-block:: python

   # pytest.ini
   [tool:pytest]
   testpaths = tests
   python_files = test_*.py
   python_classes = Test*
   python_functions = test_*
   addopts = 
       --cov=app
       --cov-report=html
       --cov-report=term-missing
       --cov-fail-under=90
       --strict-markers
       --disable-warnings
   
   markers =
       unit: Unit tests
       integration: Integration tests
       slow: Slow running tests
       security: Security-related tests
       performance: Performance tests

**Key Features:**
- Automatic test discovery
- Powerful fixtures system
- Parametrized testing
- Coverage reporting
- Custom markers for test categorization

Test Structure
~~~~~~~~~~~~~~

**Directory Organization:**

.. code-block:: text

   tests/
   ├── unit/
   │   ├── conftest.py              # Shared fixtures
   │   ├── services/
   │   │   ├── test_attack_path_service.py
   │   │   ├── test_asset_service.py
   │   │   └── test_mitre_service.py
   │   ├── models/
   │   │   ├── test_asset.py
   │   │   ├── test_attack_path.py
   │   │   └── test_user.py
   │   ├── api/
   │   │   ├── test_auth_endpoints.py
   │   │   ├── test_asset_endpoints.py
   │   │   └── test_analysis_endpoints.py
   │   └── utils/
   │       ├── test_validators.py
   │       └── test_helpers.py
   └── fixtures/
       ├── sample_assets.json
       └── sample_attack_paths.json

**Test File Naming:**
- Test files: `test_<module_name>.py`
- Test classes: `Test<ClassName>`
- Test methods: `test_<functionality>_<expected_result>`

Writing Unit Tests
------------------

Basic Test Structure
~~~~~~~~~~~~~~~~~~~~

**AAA Pattern (Arrange, Act, Assert):**

.. code-block:: python

   import pytest
   from app.services.attack_path_service import AttackPathService
   from app.models.asset import Asset
   from app.exceptions import ValidationError
   
   class TestAttackPathService:
       """Test suite for AttackPathService."""
       
       def test_analyze_path_success(self):
           """Test successful attack path analysis."""
           # Arrange
           service = AttackPathService()
           source = Asset(id=1, name="web-server", type="server")
           target = Asset(id=2, name="database", type="database")
           
           # Act
           result = service.analyze_path(source, target)
           
           # Assert
           assert result is not None
           assert len(result.paths) > 0
           assert result.risk_score > 0.0
           assert result.risk_score <= 10.0
       
       def test_analyze_path_invalid_source_raises_validation_error(self):
           """Test that invalid source asset raises ValidationError."""
           # Arrange
           service = AttackPathService()
           invalid_source = Asset(id=None, name="", type="")
           valid_target = Asset(id=2, name="database", type="database")
           
           # Act & Assert
           with pytest.raises(ValidationError, match="Invalid source asset"):
               service.analyze_path(invalid_source, valid_target)

Fixtures and Test Data
~~~~~~~~~~~~~~~~~~~~~~

**Shared Fixtures:**

.. code-block:: python

   # tests/unit/conftest.py
   import pytest
   from app.models.asset import Asset
   from app.models.user import User
   from app.services.attack_path_service import AttackPathService
   
   @pytest.fixture
   def sample_assets():
       """Create sample assets for testing."""
       return [
           Asset(id=1, name="web-server-01", type="server", ip_address="***********0"),
           Asset(id=2, name="database-01", type="database", ip_address="************"),
           Asset(id=3, name="workstation-01", type="workstation", ip_address="************")
       ]
   
   @pytest.fixture
   def admin_user():
       """Create admin user for testing."""
       return User(
           id=1,
           email="<EMAIL>",
           first_name="Admin",
           last_name="User",
           is_active=True,
           is_superuser=True
       )
   
   @pytest.fixture
   def attack_path_service():
       """Create AttackPathService instance."""
       return AttackPathService()
   
   @pytest.fixture
   def mock_graph_service():
       """Mock graph service for isolated testing."""
       from unittest.mock import Mock
       mock = Mock()
       mock.find_paths.return_value = [
           {"path": [1, 2], "risk_score": 7.5},
           {"path": [1, 3, 2], "risk_score": 6.2}
       ]
       return mock

**Factory Pattern for Test Data:**

.. code-block:: python

   # tests/factories.py
   import factory
   from app.models.asset import Asset
   from app.models.user import User
   
   class AssetFactory(factory.Factory):
       class Meta:
           model = Asset
       
       name = factory.Sequence(lambda n: f"asset-{n}")
       type = factory.Iterator(["server", "database", "workstation"])
       ip_address = factory.Faker("ipv4")
       description = factory.Faker("text", max_nb_chars=200)
       is_active = True
   
   class UserFactory(factory.Factory):
       class Meta:
           model = User
       
       email = factory.Faker("email")
       first_name = factory.Faker("first_name")
       last_name = factory.Faker("last_name")
       is_active = True
       is_superuser = False
   
   # Usage in tests
   def test_asset_creation():
       asset = AssetFactory()
       assert asset.name.startswith("asset-")
       assert asset.type in ["server", "database", "workstation"]

Mocking and Patching
~~~~~~~~~~~~~~~~~~~~

**Mocking External Dependencies:**

.. code-block:: python

   import pytest
   from unittest.mock import Mock, patch, MagicMock
   from app.services.asset_discovery_service import AssetDiscoveryService
   
   class TestAssetDiscoveryService:
       
       @patch('app.services.asset_discovery_service.boto3.client')
       def test_discover_aws_assets_success(self, mock_boto_client):
           """Test successful AWS asset discovery."""
           # Arrange
           mock_ec2 = Mock()
           mock_boto_client.return_value = mock_ec2
           mock_ec2.describe_instances.return_value = {
               'Reservations': [{
                   'Instances': [{
                       'InstanceId': 'i-1234567890abcdef0',
                       'InstanceType': 't2.micro',
                       'State': {'Name': 'running'},
                       'PrivateIpAddress': '*********'
                   }]
               }]
           }
           
           service = AssetDiscoveryService()
           
           # Act
           assets = service.discover_aws_assets(region='us-east-1')
           
           # Assert
           assert len(assets) == 1
           assert assets[0].name == 'i-1234567890abcdef0'
           assert assets[0].ip_address == '*********'
           mock_ec2.describe_instances.assert_called_once()
       
       @patch('app.services.asset_discovery_service.requests.get')
       def test_discover_api_endpoints_handles_timeout(self, mock_get):
           """Test API discovery handles timeout gracefully."""
           # Arrange
           mock_get.side_effect = requests.Timeout("Request timed out")
           service = AssetDiscoveryService()
           
           # Act
           result = service.discover_api_endpoints("https://api.example.com")
           
           # Assert
           assert result.success is False
           assert "timeout" in result.error_message.lower()

**Context Managers for Testing:**

.. code-block:: python

   import pytest
   from unittest.mock import patch
   
   def test_database_connection_failure():
       """Test handling of database connection failures."""
       with patch('app.db.session.SessionLocal') as mock_session:
           mock_session.side_effect = ConnectionError("Database unavailable")
           
           service = AssetService()
           
           with pytest.raises(DatabaseError):
               service.get_asset(1)

Parametrized Testing
~~~~~~~~~~~~~~~~~~~~

**Testing Multiple Scenarios:**

.. code-block:: python

   import pytest
   from app.utils.validators import validate_ip_address
   
   class TestIPValidation:
       
       @pytest.mark.parametrize("ip_address,expected", [
           ("***********", True),
           ("********", True),
           ("**********", True),
           ("***************", True),
           ("0.0.0.0", True),
           ("192.168.1.256", False),  # Invalid octet
           ("192.168.1", False),      # Incomplete
           ("not.an.ip.address", False),  # Non-numeric
           ("", False),               # Empty string
           (None, False),             # None value
       ])
       def test_validate_ip_address(self, ip_address, expected):
           """Test IP address validation with various inputs."""
           result = validate_ip_address(ip_address)
           assert result == expected
       
       @pytest.mark.parametrize("asset_type,is_valid", [
           ("server", True),
           ("database", True),
           ("workstation", True),
           ("network_device", True),
           ("invalid_type", False),
           ("", False),
           (None, False),
       ])
       def test_asset_type_validation(self, asset_type, is_valid):
           """Test asset type validation."""
           from app.utils.validators import validate_asset_type
           
           if is_valid:
               assert validate_asset_type(asset_type) is True
           else:
               with pytest.raises(ValidationError):
                   validate_asset_type(asset_type)

Testing Async Code
~~~~~~~~~~~~~~~~~~

**Async Test Functions:**

.. code-block:: python

   import pytest
   import asyncio
   from app.services.async_analysis_service import AsyncAnalysisService
   
   class TestAsyncAnalysisService:
       
       @pytest.mark.asyncio
       async def test_async_attack_path_analysis(self):
           """Test asynchronous attack path analysis."""
           service = AsyncAnalysisService()
           
           result = await service.analyze_attack_path_async(
               source_id=1,
               target_id=2
           )
           
           assert result is not None
           assert len(result.paths) > 0
       
       @pytest.mark.asyncio
       async def test_concurrent_analysis_requests(self):
           """Test handling of concurrent analysis requests."""
           service = AsyncAnalysisService()
           
           # Create multiple concurrent requests
           tasks = [
               service.analyze_attack_path_async(1, 2),
               service.analyze_attack_path_async(1, 3),
               service.analyze_attack_path_async(2, 3)
           ]
           
           results = await asyncio.gather(*tasks)
           
           assert len(results) == 3
           assert all(result is not None for result in results)

Exception Testing
~~~~~~~~~~~~~~~~~

**Testing Error Conditions:**

.. code-block:: python

   import pytest
   from app.services.mitre_service import MitreService
   from app.exceptions import MitreDataError, ValidationError
   
   class TestMitreServiceExceptions:
       
       def test_get_technique_invalid_id_raises_validation_error(self):
           """Test that invalid technique ID raises ValidationError."""
           service = MitreService()
           
           with pytest.raises(ValidationError) as exc_info:
               service.get_technique("INVALID_ID")
           
           assert "Invalid technique ID format" in str(exc_info.value)
       
       def test_update_mitre_data_network_error_raises_mitre_data_error(self):
           """Test network error during MITRE data update."""
           with patch('requests.get') as mock_get:
               mock_get.side_effect = requests.ConnectionError("Network error")
               
               service = MitreService()
               
               with pytest.raises(MitreDataError) as exc_info:
                   service.update_mitre_data()
               
               assert "Failed to fetch MITRE data" in str(exc_info.value)
       
       def test_multiple_exception_types(self):
           """Test that function can raise different exception types."""
           service = MitreService()
           
           # Test ValidationError
           with pytest.raises(ValidationError):
               service.validate_technique_data(None)
           
           # Test MitreDataError
           with pytest.raises(MitreDataError):
               service.parse_invalid_mitre_data("invalid json")

Performance Testing
~~~~~~~~~~~~~~~~~~~

**Testing Performance Requirements:**

.. code-block:: python

   import pytest
   import time
   from app.services.attack_path_service import AttackPathService
   
   class TestAttackPathPerformance:
       
       @pytest.mark.performance
       def test_attack_path_analysis_performance(self, sample_assets):
           """Test that attack path analysis completes within time limit."""
           service = AttackPathService()
           source, target = sample_assets[0], sample_assets[1]
           
           start_time = time.time()
           result = service.analyze_path(source, target)
           end_time = time.time()
           
           analysis_time = end_time - start_time
           
           assert result is not None
           assert analysis_time < 5.0  # Should complete within 5 seconds
       
       @pytest.mark.performance
       def test_bulk_analysis_performance(self, sample_assets):
           """Test performance of bulk attack path analysis."""
           service = AttackPathService()
           
           start_time = time.time()
           results = service.analyze_bulk_paths(sample_assets[:10])
           end_time = time.time()
           
           analysis_time = end_time - start_time
           
           assert len(results) == 10
           assert analysis_time < 30.0  # Bulk analysis within 30 seconds

Coverage and Quality
--------------------

Coverage Requirements
~~~~~~~~~~~~~~~~~~~~~

**Coverage Targets:**

.. list-table:: Coverage Requirements by Component
   :header-rows: 1
   :widths: 30 25 45

   * - Component
     - Minimum Coverage
     - Notes
   * - **Services**
     - 95%
     - Core business logic
   * - **Models**
     - 90%
     - Data models and validation
   * - **API Endpoints**
     - 90%
     - Request/response handling
   * - **Utilities**
     - 95%
     - Helper functions
   * - **Overall**
     - 90%
     - Project-wide minimum

**Coverage Configuration:**

.. code-block:: python

   # .coveragerc
   [run]
   source = app
   omit = 
       */migrations/*
       */tests/*
       */venv/*
       */env/*
       app/main.py
   
   [report]
   exclude_lines =
       pragma: no cover
       def __repr__
       raise AssertionError
       raise NotImplementedError
       if __name__ == .__main__.:
   
   [html]
   directory = htmlcov

**Coverage Commands:**

.. code-block:: bash

   # Run tests with coverage
   pytest --cov=app --cov-report=html --cov-report=term
   
   # Coverage report
   coverage report -m
   
   # HTML coverage report
   coverage html
   open htmlcov/index.html

Test Quality Metrics
~~~~~~~~~~~~~~~~~~~~~

**Quality Indicators:**

1. **Test Coverage** - Percentage of code covered by tests
2. **Test Execution Time** - How fast tests run
3. **Test Reliability** - Consistency of test results
4. **Test Maintainability** - Ease of updating tests
5. **Test Readability** - Clarity of test intent

**Quality Commands:**

.. code-block:: bash

   # Run quality checks
   pytest --cov=app --cov-fail-under=90
   pytest --durations=10  # Show slowest tests
   pytest --tb=short      # Concise error output
   pytest -v              # Verbose output

Best Practices
--------------

Test Design Principles
~~~~~~~~~~~~~~~~~~~~~~

1. **Independent Tests** - Tests should not depend on each other
2. **Deterministic** - Tests should produce consistent results
3. **Fast Execution** - Unit tests should run quickly
4. **Clear Intent** - Test names should describe what they test
5. **Minimal Setup** - Use fixtures to reduce test setup code

**Good Test Examples:**

.. code-block:: python

   # Good - Clear, focused, independent
   def test_asset_validation_rejects_empty_name():
       """Test that asset validation rejects empty name."""
       with pytest.raises(ValidationError, match="Name cannot be empty"):
           Asset(name="", type="server", ip_address="***********")
   
   def test_asset_validation_accepts_valid_data():
       """Test that asset validation accepts valid data."""
       asset = Asset(name="web-server", type="server", ip_address="***********")
       assert asset.name == "web-server"
       assert asset.is_valid()

**Avoid These Patterns:**

.. code-block:: python

   # Bad - Testing multiple things
   def test_asset():
       asset = Asset(name="test", type="server", ip_address="***********")
       assert asset.name == "test"  # Testing name
       assert asset.is_valid()      # Testing validation
       asset.save()                 # Testing persistence
       assert asset.id is not None # Testing ID assignment
   
   # Bad - Unclear test name
   def test_asset_stuff():
       pass
   
   # Bad - Dependent on external state
   def test_get_asset():
       asset = Asset.get_by_id(1)  # Assumes asset with ID 1 exists
       assert asset is not None

Common Testing Patterns
~~~~~~~~~~~~~~~~~~~~~~~

**Testing Validation Logic:**

.. code-block:: python

   def test_email_validation():
       """Test email validation logic."""
       valid_emails = [
           "<EMAIL>",
           "<EMAIL>",
           "<EMAIL>"
       ]
       
       invalid_emails = [
           "invalid-email",
           "@domain.com",
           "user@",
           "",
           None
       ]
       
       for email in valid_emails:
           assert validate_email(email), f"Should accept valid email: {email}"
       
       for email in invalid_emails:
           assert not validate_email(email), f"Should reject invalid email: {email}"

**Testing State Changes:**

.. code-block:: python

   def test_asset_status_transitions():
       """Test valid asset status transitions."""
       asset = Asset(name="test", type="server", status="active")
       
       # Test valid transition
       asset.set_status("inactive")
       assert asset.status == "inactive"
       
       # Test invalid transition
       with pytest.raises(InvalidTransitionError):
           asset.set_status("deleted")  # Can't go directly from inactive to deleted

Running Tests
-------------

Test Execution
~~~~~~~~~~~~~~

**Basic Commands:**

.. code-block:: bash

   # Run all unit tests
   pytest tests/unit/
   
   # Run specific test file
   pytest tests/unit/services/test_attack_path_service.py
   
   # Run specific test method
   pytest tests/unit/services/test_attack_path_service.py::TestAttackPathService::test_analyze_path_success
   
   # Run tests matching pattern
   pytest -k "test_validation"
   
   # Run tests with specific marker
   pytest -m "not slow"

**Advanced Options:**

.. code-block:: bash

   # Parallel execution
   pytest -n auto  # Use all CPU cores
   pytest -n 4     # Use 4 processes
   
   # Stop on first failure
   pytest -x
   
   # Show local variables on failure
   pytest -l
   
   # Verbose output
   pytest -v
   
   # Quiet output
   pytest -q

Continuous Integration
~~~~~~~~~~~~~~~~~~~~~~

**GitHub Actions Configuration:**

.. code-block:: yaml

   name: Unit Tests
   
   on: [push, pull_request]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       
       steps:
       - uses: actions/checkout@v3
       
       - name: Set up Python
         uses: actions/setup-python@v4
         with:
           python-version: '3.11'
       
       - name: Install dependencies
         run: |
           pip install -r requirements-test.txt
       
       - name: Run unit tests
         run: |
           pytest tests/unit/ --cov=app --cov-report=xml
       
       - name: Upload coverage
         uses: codecov/codecov-action@v3
         with:
           file: ./coverage.xml

Debugging Tests
~~~~~~~~~~~~~~~

**Debugging Techniques:**

.. code-block:: python

   # Add print statements for debugging
   def test_debug_example():
       result = some_function()
       print(f"Debug: result = {result}")  # Will show with -s flag
       assert result == expected
   
   # Use pdb for interactive debugging
   def test_with_debugger():
       import pdb; pdb.set_trace()
       result = some_function()
       assert result == expected
   
   # Use pytest fixtures for debugging
   @pytest.fixture
   def debug_mode():
       import logging
       logging.basicConfig(level=logging.DEBUG)

**Running with Debug Output:**

.. code-block:: bash

   # Show print statements
   pytest -s
   
   # Show debug logging
   pytest --log-cli-level=DEBUG
   
   # Drop into debugger on failure
   pytest --pdb

Next Steps
----------

For comprehensive testing:

1. **Write unit tests** for all new functionality
2. **Maintain high coverage** (>90% for critical components)
3. **Run tests frequently** during development
4. **Review test quality** during code reviews
5. **Update tests** when requirements change

.. note::
   Unit tests are the foundation of software quality. Invest time in writing
   good tests - they will save time and prevent bugs in the long run.
