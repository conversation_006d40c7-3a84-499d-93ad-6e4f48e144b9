# Blast-Radius Security Tool - Local Development Makefile
# Simplified commands for local CI/CD and development workflow

.PHONY: help install dev test security build deploy clean status logs shell

# Default target
.DEFAULT_GOAL := help

# Configuration
COMPOSE_FILE := docker-compose.local.yml
BACKEND_DIR := backend
FRONTEND_DIR := frontend
SCRIPTS_DIR := scripts

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

# Help target
help: ## Show this help message
	@echo "$(BLUE)Blast-Radius Security Tool - Local Development$(NC)"
	@echo ""
	@echo "$(GREEN)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)Examples:$(NC)"
	@echo "  make install     # Set up development environment"
	@echo "  make dev         # Start development environment"
	@echo "  make test        # Run all tests"
	@echo "  make security    # Run security scans"
	@echo "  make deploy      # Full CI/CD pipeline"

# Installation and setup
install: ## Install dependencies and set up development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@chmod +x $(SCRIPTS_DIR)/local-cicd.sh
	@cd $(BACKEND_DIR) && python3 -m venv venv
	@cd $(BACKEND_DIR) && . venv/bin/activate && pip install -r requirements.txt -r requirements-test.txt
	@cd $(FRONTEND_DIR) && npm ci
	@echo "$(GREEN)Development environment ready!$(NC)"

# Development
dev: ## Start development environment (databases + backend)
	@echo "$(BLUE)Starting development environment...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) up -d postgresql redis neo4j
	@echo "$(YELLOW)Waiting for databases to be ready...$(NC)"
	@sleep 15
	@cd $(BACKEND_DIR) && . venv/bin/activate && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
	@echo "$(GREEN)Development environment started!$(NC)"
	@echo "API: http://localhost:8000"
	@echo "Docs: http://localhost:8000/docs"

dev-full: ## Start full development environment with frontend
	@echo "$(BLUE)Starting full development environment...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) up -d
	@echo "$(GREEN)Full environment started!$(NC)"
	@echo "Frontend: http://localhost:3000"
	@echo "Backend: http://localhost:8000"
	@echo "Docs: http://localhost:8000/docs"

# Testing
test: ## Run all tests
	@echo "$(BLUE)Running all tests...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh test

test-backend: ## Run backend tests only
	@echo "$(BLUE)Running backend tests...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) up -d postgresql redis
	@sleep 10
	@cd $(BACKEND_DIR) && . venv/bin/activate && python -m pytest tests/ -v
	@docker-compose -f $(COMPOSE_FILE) down

test-frontend: ## Run frontend tests only
	@echo "$(BLUE)Running frontend tests...$(NC)"
	@cd $(FRONTEND_DIR) && npm run test:unit

test-watch: ## Run tests in watch mode
	@echo "$(BLUE)Running tests in watch mode...$(NC)"
	@cd $(BACKEND_DIR) && . venv/bin/activate && python -m pytest tests/ -v --tb=short -f

# Security
security: ## Run security scans
	@echo "$(BLUE)Running security scans...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh security

security-backend: ## Run backend security scan
	@echo "$(BLUE)Running backend security scan...$(NC)"
	@cd $(BACKEND_DIR) && . venv/bin/activate && bandit -r app/ -ll
	@cd $(BACKEND_DIR) && . venv/bin/activate && safety scan

security-frontend: ## Run frontend security audit
	@echo "$(BLUE)Running frontend security audit...$(NC)"
	@cd $(FRONTEND_DIR) && npm audit --audit-level=moderate

# Building
build: ## Build all containers
	@echo "$(BLUE)Building containers...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh build

build-backend: ## Build backend container only
	@echo "$(BLUE)Building backend container...$(NC)"
	@docker build -t blast-radius-backend:latest $(BACKEND_DIR)

build-frontend: ## Build frontend container only
	@echo "$(BLUE)Building frontend container...$(NC)"
	@docker build -t blast-radius-frontend:latest $(FRONTEND_DIR)

# Deployment
deploy: ## Run full CI/CD pipeline and deploy
	@echo "$(BLUE)Running full CI/CD pipeline...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh

deploy-fast: ## Deploy without tests (faster iteration)
	@echo "$(BLUE)Fast deployment (skipping tests)...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh --skip-tests

deploy-local: ## Deploy to local environment only
	@echo "$(BLUE)Deploying to local environment...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh deploy

# Monitoring
status: ## Show status of all services
	@echo "$(BLUE)Service Status:$(NC)"
	@docker-compose -f $(COMPOSE_FILE) ps

logs: ## Show logs from all services
	@docker-compose -f $(COMPOSE_FILE) logs -f

logs-backend: ## Show backend logs
	@docker-compose -f $(COMPOSE_FILE) logs -f backend

logs-db: ## Show database logs
	@docker-compose -f $(COMPOSE_FILE) logs -f postgresql

health: ## Check application health
	@echo "$(BLUE)Checking application health...$(NC)"
	@curl -f http://localhost:8000/health && echo "$(GREEN)Backend: Healthy$(NC)" || echo "$(RED)Backend: Unhealthy$(NC)"
	@curl -f http://localhost:3000 >/dev/null 2>&1 && echo "$(GREEN)Frontend: Healthy$(NC)" || echo "$(YELLOW)Frontend: Not running$(NC)"

# Development utilities
shell: ## Open shell in backend container
	@docker-compose -f $(COMPOSE_FILE) exec backend bash

shell-db: ## Open PostgreSQL shell
	@docker-compose -f $(COMPOSE_FILE) exec postgresql psql -U blastradius -d blastradius

shell-redis: ## Open Redis shell
	@docker-compose -f $(COMPOSE_FILE) exec redis redis-cli -a redis_local_dev_2024

shell-neo4j: ## Open Neo4j shell
	@docker-compose -f $(COMPOSE_FILE) exec neo4j cypher-shell -u neo4j -p neo4j_local_dev_2024

# Database management
db-reset: ## Reset database (WARNING: destroys all data)
	@echo "$(RED)Resetting database (this will destroy all data)...$(NC)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose -f $(COMPOSE_FILE) down -v; \
		docker-compose -f $(COMPOSE_FILE) up -d postgresql redis neo4j; \
		echo "$(GREEN)Database reset complete$(NC)"; \
	else \
		echo "$(YELLOW)Database reset cancelled$(NC)"; \
	fi

db-backup: ## Backup database
	@echo "$(BLUE)Creating database backup...$(NC)"
	@mkdir -p backups
	@docker-compose -f $(COMPOSE_FILE) exec -T postgresql pg_dump -U blastradius blastradius > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Database backup created in backups/$(NC)"

db-restore: ## Restore database from backup (requires BACKUP_FILE variable)
	@echo "$(BLUE)Restoring database from $(BACKUP_FILE)...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) exec -T postgresql psql -U blastradius -d blastradius < $(BACKUP_FILE)
	@echo "$(GREEN)Database restored$(NC)"

# Cleanup
clean: ## Clean up containers, images, and volumes
	@echo "$(BLUE)Cleaning up...$(NC)"
	@$(SCRIPTS_DIR)/local-cicd.sh clean

clean-containers: ## Stop and remove containers
	@echo "$(BLUE)Stopping and removing containers...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) down

clean-volumes: ## Remove all volumes (WARNING: destroys all data)
	@echo "$(RED)Removing all volumes (this will destroy all data)...$(NC)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose -f $(COMPOSE_FILE) down -v; \
		echo "$(GREEN)Volumes removed$(NC)"; \
	else \
		echo "$(YELLOW)Volume removal cancelled$(NC)"; \
	fi

clean-images: ## Remove built images
	@echo "$(BLUE)Removing built images...$(NC)"
	@docker rmi blast-radius-backend:latest blast-radius-frontend:latest 2>/dev/null || true

clean-all: ## Complete cleanup (containers, volumes, images)
	@echo "$(RED)Complete cleanup (WARNING: destroys all data and images)...$(NC)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		$(MAKE) clean; \
		echo "$(GREEN)Complete cleanup finished$(NC)"; \
	else \
		echo "$(YELLOW)Cleanup cancelled$(NC)"; \
	fi

# Monitoring and debugging
monitor: ## Start monitoring stack (Prometheus + Grafana)
	@echo "$(BLUE)Starting comprehensive monitoring stack...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh start

monitor-setup: ## Set up complete monitoring stack
	@echo "$(BLUE)Setting up monitoring stack...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh setup

monitor-status: ## Show monitoring services status
	@echo "$(BLUE)Checking monitoring status...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh status

monitor-stop: ## Stop monitoring services
	@echo "$(BLUE)Stopping monitoring services...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh stop

monitor-clean: ## Clean monitoring data
	@echo "$(BLUE)Cleaning monitoring data...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh clean

dashboards: ## Import Grafana dashboards
	@echo "$(BLUE)Importing Grafana dashboards...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh dashboards

alerts-test: ## Test alert configuration
	@echo "$(BLUE)Testing alert configuration...$(NC)"
	@$(SCRIPTS_DIR)/setup-monitoring.sh alerts

debug: ## Start in debug mode with verbose logging
	@echo "$(BLUE)Starting in debug mode...$(NC)"
	@DEBUG=true LOG_LEVEL=debug $(MAKE) dev

# Performance testing
perf-test: ## Run performance tests
	@echo "$(BLUE)Running performance tests...$(NC)"
	@cd tests/performance && python -m locust -f locustfile.py --host=http://localhost:8000 --headless -u 10 -r 2 -t 60s

# Documentation
docs: ## Generate and serve documentation
	@echo "$(BLUE)Generating documentation...$(NC)"
	@cd docs && python -m http.server 8080 &
	@echo "$(GREEN)Documentation available at: http://localhost:8080$(NC)"

# Git helpers
git-hooks: ## Install git hooks for development
	@echo "$(BLUE)Installing git hooks...$(NC)"
	@cp scripts/git-hooks/pre-commit .git/hooks/
	@chmod +x .git/hooks/pre-commit
	@echo "$(GREEN)Git hooks installed$(NC)"

# Quick commands for common workflows
quick-test: ## Quick test (security + backend tests)
	@$(MAKE) security-backend test-backend

quick-deploy: ## Quick deploy (build + deploy without full tests)
	@$(MAKE) build deploy-fast

# Environment info
info: ## Show environment information
	@echo "$(BLUE)Environment Information:$(NC)"
	@echo "Docker: $(shell docker --version)"
	@echo "Docker Compose: $(shell docker-compose --version)"
	@echo "Python: $(shell python3 --version)"
	@echo "Node.js: $(shell node --version)"
	@echo "NPM: $(shell npm --version)"
	@echo ""
	@echo "$(BLUE)Project Status:$(NC)"
	@$(MAKE) status
