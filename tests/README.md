# Blast-Radius Security Tool - Test Suite

This directory contains comprehensive tests for the Blast-Radius Security Tool, covering all major functionality including attack path analysis, threat modeling, security management, and API endpoints.

## 📁 Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Shared pytest fixtures and configuration
├── pytest.ini                 # Pytest configuration
├── README.md                   # This file
├── run_tests.py                # Comprehensive test runner script
├── unit/                       # Unit tests for individual components
│   ├── test_attack_path_analyzer.py
│   ├── test_threat_modeling_service.py
│   └── ...
├── integration/                # Integration tests for component interactions
│   ├── test_attack_path_integration.py
│   └── ...
├── api/                        # API endpoint tests
│   ├── test_attack_path_api.py
│   └── ...
├── security/                   # Security-focused tests
│   ├── test_attack_path_security.py
│   └── ...
├── performance/                # Performance and scalability tests
│   ├── test_attack_path_performance.py
│   └── ...
├── data/                       # Test data files
└── fixtures/                   # Test fixtures and mock data
```

## 🚀 Running Tests

### Quick Start

```bash
# Run all tests
python tests/run_tests.py --all

# Run specific test categories
python tests/run_tests.py --unit
python tests/run_tests.py --api --security

# Run smoke tests (quick validation)
python tests/run_tests.py --smoke
```

### Test Categories

- **Unit Tests** (`--unit`): Individual component testing
- **Integration Tests** (`--integration`): Component interaction testing
- **API Tests** (`--api`): REST endpoint validation
- **Security Tests** (`--security`): Security vulnerability testing
- **Performance Tests** (`--performance`): Load and scalability testing

### Advanced Options

```bash
# Verbose output
python tests/run_tests.py --unit --verbose

# Stop on first failure
python tests/run_tests.py --all --stop-on-failure

# Run tests matching keyword
python tests/run_tests.py --unit -k "attack_path"

# Skip slow tests
python tests/run_tests.py --all --skip-slow

# Generate coverage report
python tests/run_tests.py --coverage

# Clean test artifacts
python tests/run_tests.py --clean
```

## 📊 Test Coverage

- **Minimum Coverage**: 80%
- **Unit Tests**: 90%+ coverage expected
- **Integration Tests**: Focus on workflow coverage
- **API Tests**: 100% endpoint coverage

## 🔧 Test Configuration

Tests are configured via `pytest.ini` and use shared fixtures from `conftest.py`. Environment variables:

```bash
export TEST_DATABASE_URL="sqlite:///./test.db"
export TEST_REDIS_URL="redis://localhost:6379/1"
```

## 📈 Quality Metrics

- **Test Execution Time**: <5 minutes for unit tests, <30 minutes for full suite
- **Test Reliability**: 99%+ pass rate for stable tests
- **Security Coverage**: 100% of security controls tested
- **Performance Benchmarks**: Sub-second response time validation

For detailed testing procedures and examples, refer to the documentation in `docs/testing/`.
