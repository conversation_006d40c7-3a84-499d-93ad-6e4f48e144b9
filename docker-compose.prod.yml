# Production-like override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

version: '3.8'

services:
  # Nginx reverse proxy with SSL
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # Backend API with production settings
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - RELOAD=false
      - CORS_ORIGINS=["https://localhost:8443"]
      - DATABASE_URL=postgresql://blast_radius_prod:${POSTGRES_PASSWORD}@postgresql:5432/blast_radius_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - NEO4J_URL=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - REFRESH_TOKEN_EXPIRE_DAYS=7
    expose:
      - "8000"
    command: >
      sh -c "
        echo '🚀 Starting Blast-Radius API in production mode...' &&
        gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 --access-logfile - --error-logfile -
      "
    depends_on:
      - postgresql
      - redis
      - neo4j
    networks:
      - blast-radius-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend with production build
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    environment:
      - REACT_APP_API_URL=https://localhost:8443/api
      - REACT_APP_ENV=production
    expose:
      - "80"
    depends_on:
      - backend
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # PostgreSQL with production settings
  postgresql:
    environment:
      - POSTGRES_DB=blast_radius_prod
      - POSTGRES_USER=blast_radius_prod
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./postgresql/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./postgresql/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    expose:
      - "5432"
    networks:
      - blast-radius-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U blast_radius_prod -d blast_radius_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis with production settings
  redis:
    command: >
      redis-server 
      --appendonly yes 
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_prod_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    expose:
      - "6379"
    networks:
      - blast-radius-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Neo4j with production settings
  neo4j:
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD}
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_whitelist=apoc.*
      - NEO4J_dbms_logs_query_enabled=true
      - NEO4J_dbms_logs_query_threshold=1s
    volumes:
      - neo4j_prod_data:/data
      - neo4j_prod_logs:/logs
      - ./neo4j/neo4j.conf:/var/lib/neo4j/conf/neo4j.conf:ro
    expose:
      - "7474"
      - "7687"
    networks:
      - blast-radius-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "${NEO4J_PASSWORD}", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    expose:
      - "9090"
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    expose:
      - "3000"
    depends_on:
      - prometheus
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    expose:
      - "9100"
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # Elasticsearch for logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    expose:
      - "9200"
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
    depends_on:
      - elasticsearch
    networks:
      - blast-radius-prod
    restart: unless-stopped

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    expose:
      - "5601"
    depends_on:
      - elasticsearch
    networks:
      - blast-radius-prod
    restart: unless-stopped

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  neo4j_prod_data:
    driver: local
  neo4j_prod_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  blast-radius-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
