# Blast-Radius Security Tool Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=production

# Debug mode (true/false) - NEVER set to true in production
DEBUG=false

# Logging level (debug, info, warning, error, critical)
LOG_LEVEL=info

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Secret key for JWT tokens - MUST be changed in production
# Generate with: openssl rand -hex 32
SECRET_KEY=change-this-to-a-secure-random-key-in-production

# JWT token expiration settings
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Password policy
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# Account security
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION_MINUTES=30

# Multi-Factor Authentication
MFA_ISSUER_NAME="Blast-Radius Security Tool"
MFA_REQUIRE_FOR_ALL=false
MFA_REQUIRE_FOR_ADMIN=true

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# PostgreSQL Database Password
DB_PASSWORD=blast_radius_secure_password_2024

# Full database URL (auto-constructed from above)
# DATABASE_URL=postgresql://blastradius:${DB_PASSWORD}@postgresql:5432/blastradius

# Database connection pool settings
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# =============================================================================
# REDIS SETTINGS
# =============================================================================

# Redis Password
REDIS_PASSWORD=redis_secure_password_2024

# Full Redis URL (auto-constructed from above)
# REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0

# =============================================================================
# CORS SETTINGS
# =============================================================================

# Allowed origins for CORS (JSON array format)
CORS_ORIGINS=["https://blastradius.local","https://api.blastradius.local"]

# =============================================================================
# EMAIL SETTINGS (Optional)
# =============================================================================

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# Email settings
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Blast-Radius Security Tool"

# Email features
SEND_WELCOME_EMAILS=true
SEND_PASSWORD_RESET_EMAILS=true
SEND_SECURITY_ALERTS=true

# =============================================================================
# MONITORING SETTINGS (Optional)
# =============================================================================

# Grafana admin password
GRAFANA_PASSWORD=admin_secure_password_2024

# Prometheus settings
PROMETHEUS_ENABLED=true

# =============================================================================
# SSL/TLS SETTINGS (Production)
# =============================================================================

# Let's Encrypt email for SSL certificates
LETSENCRYPT_EMAIL=<EMAIL>

# Domain names
DOMAIN_NAME=blastradius.local
API_DOMAIN_NAME=api.blastradius.local

# =============================================================================
# DEVELOPMENT SETTINGS (Development Only)
# =============================================================================

# Development overrides (uncomment for development)
# ENVIRONMENT=development
# DEBUG=true
# LOG_LEVEL=debug
# CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]
# SESSION_COOKIE_SECURE=false
# MFA_REQUIRE_FOR_ALL=false

# =============================================================================
# BACKUP SETTINGS (Optional)
# =============================================================================

# Backup configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Worker processes (adjust based on CPU cores)
WORKERS=4

# Request timeout
REQUEST_TIMEOUT_SECONDS=30

# Cache TTL
CACHE_TTL_SECONDS=300

# =============================================================================
# FEATURE FLAGS (Optional)
# =============================================================================

# Enable/disable features
FEATURE_API_DOCS=false  # Disable in production
FEATURE_METRICS=true
FEATURE_AUDIT_LOGGING=true
FEATURE_RATE_LIMITING=true

# =============================================================================
# INTEGRATION SETTINGS (Optional)
# =============================================================================

# External service integrations
# Add your integration settings here as needed

# Example: Slack notifications
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
# SLACK_CHANNEL=#security-alerts

# Example: Microsoft Teams notifications
# TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/...

# =============================================================================
# CUSTOM SETTINGS
# =============================================================================

# Add any custom environment variables your deployment needs
