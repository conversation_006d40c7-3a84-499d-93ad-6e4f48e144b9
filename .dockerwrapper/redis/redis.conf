# Redis configuration for Blast-Radius Security Tool
# Optimized for security and performance

# Network
bind 0.0.0.0
port 6379
protected-mode yes

# General
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Security
requirepass blastradius_redis_secure_password_2024
rename-command FL<PERSON><PERSON><PERSON> ""
rename-command <PERSON><PERSON><PERSON>LL ""
rename-command <PERSON>EY<PERSON> ""
rename-command <PERSON><PERSON><PERSON> "CONFIG_b1a5tr4d1u5"
rename-command DEBUG ""
rename-command EVAL ""
rename-command SHUTDOWN "SHUTDOWN_b1a5tr4d1u5"

# Memory Management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Append Only File
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitoring
latency-monitor-threshold 100

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client Query Buffer
client-query-buffer-limit 1gb

# Protocol
tcp-keepalive 300
tcp-backlog 511

# Timeouts
timeout 0

# Threading (Redis 6.0+)
io-threads 4
io-threads-do-reads yes

# TLS (disabled, handled by Traefik)
tls-port 0

# Modules (if needed)
# loadmodule /path/to/module.so
