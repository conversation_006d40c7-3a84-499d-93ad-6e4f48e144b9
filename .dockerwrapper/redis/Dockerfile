FROM redis:7-alpine

# Copy custom configuration
COPY redis.conf /usr/local/etc/redis/redis.conf

# Create redis user and set permissions
RUN addgroup -g 999 redis && \
    adduser -D -s /bin/sh -u 999 -G redis redis && \
    mkdir -p /data && \
    chown -R redis:redis /data && \
    chown -R redis:redis /usr/local/etc/redis/

# Switch to redis user
USER redis

# Expose port
EXPOSE 6379

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD redis-cli ping || exit 1

# Start Redis with custom configuration
CMD ["redis-server", "/usr/local/etc/redis/redis.conf"]

# Labels
LABEL maintainer="Blast-Radius Security Tool"
LABEL description="Redis cache and session store for Blast-Radius"
LABEL version="1.0"
