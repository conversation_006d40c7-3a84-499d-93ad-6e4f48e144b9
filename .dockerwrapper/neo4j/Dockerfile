FROM neo4j:5.15-community

# Set environment variables
ENV NEO4J_AUTH=neo4j/blastradius_neo4j_secure_password_2024
ENV NEO4J_PLUGINS=["apoc", "graph-data-science"]
ENV NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
ENV NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*

# Copy custom configuration
COPY neo4j.conf /var/lib/neo4j/conf/neo4j.conf

# Install APOC and GDS plugins
RUN wget -O /var/lib/neo4j/plugins/apoc-5.15.0-core.jar \
    https://github.com/neo4j-contrib/neo4j-apoc-procedures/releases/download/5.15.0/apoc-5.15.0-core.jar && \
    wget -O /var/lib/neo4j/plugins/neo4j-graph-data-science-2.5.8.jar \
    https://github.com/neo4j/graph-data-science/releases/download/2.5.8/neo4j-graph-data-science-2.5.8.jar

# Set proper permissions
RUN chown -R neo4j:neo4j /var/lib/neo4j/plugins/ && \
    chmod 644 /var/lib/neo4j/plugins/*.jar

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD cypher-shell -u neo4j -p blastradius_neo4j_secure_password_2024 "RETURN 1" || exit 1

# Labels
LABEL maintainer="Blast-Radius Security Tool"
LABEL description="Neo4j graph database for Blast-Radius attack path analysis"
LABEL version="1.0"
