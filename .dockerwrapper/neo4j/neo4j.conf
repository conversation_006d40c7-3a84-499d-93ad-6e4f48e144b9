# Neo4j configuration for Blast-Radius Security Tool
# Optimized for graph analysis and security

# Network
server.default_listen_address=0.0.0.0
server.bolt.listen_address=0.0.0.0:7687
server.http.listen_address=0.0.0.0:7474
server.https.listen_address=0.0.0.0:7473

# Security
server.bolt.tls_level=DISABLED
server.http.enabled=true
server.https.enabled=false
dbms.security.auth_enabled=true

# Memory Settings
server.memory.heap.initial_size=1G
server.memory.heap.max_size=2G
server.memory.pagecache.size=1G

# Transaction Settings
db.transaction.timeout=60s
db.transaction.bookmark_ready_timeout=30s
db.transaction.concurrent.maximum=1000

# Query Settings
db.query.cache_size=1000
db.query.cache_ttl=60s
cypher.default_language_version=5
cypher.hints_error=false
cypher.lenient_create_relationship=false

# Logging
server.logs.user.level=INFO
server.logs.security.level=INFO
server.logs.http.enabled=true
server.logs.gc.enabled=true
server.logs.gc.options=-Xloggc:{{server.directories.logs}}/gc.log -XX:+UseG1GC -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=20m -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintPromotionFailure -XX:+UseGCLogFileRotation

# Performance
db.checkpoint.interval.time=15m
db.checkpoint.interval.tx=100000
db.relationship_grouping_threshold=100

# Procedures and Functions
dbms.security.procedures.unrestricted=apoc.*,gds.*
dbms.security.procedures.allowlist=apoc.*,gds.*

# APOC Configuration
apoc.export.file.enabled=true
apoc.import.file.enabled=true
apoc.import.file.use_neo4j_config=true

# Graph Data Science Configuration
gds.enterprise.license_file=

# Metrics
server.metrics.enabled=true
server.metrics.jvm.enabled=true
server.metrics.bolt.messages_received.enabled=true
server.metrics.bolt.messages_started.enabled=true
server.metrics.bolt.messages_done.enabled=true
server.metrics.bolt.messages_failed.enabled=true
server.metrics.bolt.accumulated_processing_time.enabled=true
server.metrics.bolt.accumulated_queue_time.enabled=true

# Directories
server.directories.data=/data
server.directories.logs=/logs
server.directories.lib=/var/lib/neo4j/lib
server.directories.plugins=/var/lib/neo4j/plugins

# JVM Settings
server.jvm.additional=-XX:+UseG1GC
server.jvm.additional=-XX:+UnlockExperimentalVMOptions
server.jvm.additional=-XX:+TrustFinalNonStaticFields
server.jvm.additional=-XX:+DisableExplicitGC
server.jvm.additional=-XX:MaxInlineLevel=15
server.jvm.additional=-Djdk.nio.maxCachedBufferSize=262144
server.jvm.additional=-Dio.netty.tryReflectionSetAccessible=true
server.jvm.additional=-Djdk.tls.ephemeralDHKeySize=2048
server.jvm.additional=-Djdk.tls.rejectClientInitiatedRenegotiation=true
server.jvm.additional=-Dunsupported.dbms.udc.source=docker
