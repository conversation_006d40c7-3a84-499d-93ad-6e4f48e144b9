-- Initialize Blast-Radius Security Tool Database
-- This script sets up the initial database structure

-- Create application database and user
CREATE DATABASE blastradius;
CREATE DATABASE blastradius_test;

-- Create application user with limited privileges
CREATE USER blastradius WITH ENCRYPTED PASSWORD 'blastradius_secure_password_2024';
CREATE USER blastradius_test WITH ENCRYPTED PASSWORD 'blastradius_test_password_2024';

-- Grant privileges
GRANT CONNECT ON DATABASE blastradius TO blastradius;
GRANT CONNECT ON DATABASE blastradius_test TO blastradius_test;

-- Connect to application database
\c blastradius;

-- Create schema for application
CREATE SCHEMA IF NOT EXISTS blastradius_app;
GRANT USAGE ON SCHEMA blastradius_app TO blastradius;
GRANT CREATE ON SCHEMA blastradius_app TO blastradius;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create soft-delete function for all tables
CREATE OR REPLACE FUNCTION blastradius_app.soft_delete_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        UPDATE blastradius_app.deleted_records 
        SET deleted_at = NOW(), 
            deleted_data = row_to_json(OLD)
        WHERE table_name = TG_TABLE_NAME AND record_id = OLD.id;
        
        IF NOT FOUND THEN
            INSERT INTO blastradius_app.deleted_records (table_name, record_id, deleted_at, deleted_data)
            VALUES (TG_TABLE_NAME, OLD.id, NOW(), row_to_json(OLD));
        END IF;
        
        RETURN NULL; -- Prevent actual deletion
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create deleted records tracking table
CREATE TABLE IF NOT EXISTS blastradius_app.deleted_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(255) NOT NULL,
    record_id UUID NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for deleted records
CREATE INDEX IF NOT EXISTS idx_deleted_records_table_name ON blastradius_app.deleted_records(table_name);
CREATE INDEX IF NOT EXISTS idx_deleted_records_record_id ON blastradius_app.deleted_records(record_id);
CREATE INDEX IF NOT EXISTS idx_deleted_records_deleted_at ON blastradius_app.deleted_records(deleted_at);

-- Set up test database
\c blastradius_test;

CREATE SCHEMA IF NOT EXISTS blastradius_app;
GRANT USAGE ON SCHEMA blastradius_app TO blastradius_test;
GRANT CREATE ON SCHEMA blastradius_app TO blastradius_test;

-- Enable required extensions for test database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create the same soft-delete infrastructure for test database
CREATE OR REPLACE FUNCTION blastradius_app.soft_delete_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        UPDATE blastradius_app.deleted_records 
        SET deleted_at = NOW(), 
            deleted_data = row_to_json(OLD)
        WHERE table_name = TG_TABLE_NAME AND record_id = OLD.id;
        
        IF NOT FOUND THEN
            INSERT INTO blastradius_app.deleted_records (table_name, record_id, deleted_at, deleted_data)
            VALUES (TG_TABLE_NAME, OLD.id, NOW(), row_to_json(OLD));
        END IF;
        
        RETURN NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TABLE IF NOT EXISTS blastradius_app.deleted_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(255) NOT NULL,
    record_id UUID NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_deleted_records_table_name ON blastradius_app.deleted_records(table_name);
CREATE INDEX IF NOT EXISTS idx_deleted_records_record_id ON blastradius_app.deleted_records(record_id);
CREATE INDEX IF NOT EXISTS idx_deleted_records_deleted_at ON blastradius_app.deleted_records(deleted_at);
