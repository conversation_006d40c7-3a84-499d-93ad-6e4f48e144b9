FROM postgres:15-alpine

# Install additional extensions
RUN apk add --no-cache postgresql-contrib

# Copy custom configuration
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Set custom configuration
CMD ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD pg_isready -U ${POSTGRES_USER:-blastradius} -d ${POSTGRES_DB:-blastradius}

# Labels
LABEL maintainer="Blast-Radius Security Tool"
LABEL description="PostgreSQL database for Blast-Radius with soft-delete support"
LABEL version="1.0"
