# PostgreSQL Client Authentication Configuration File
# For Blast-Radius Security Tool

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     scram-sha-256

# IPv4 local connections:
host    all             all             127.0.0.1/32            scram-sha-256

# IPv6 local connections:
host    all             all             ::1/128                 scram-sha-256

# Docker network connections
host    all             all             **********/12           scram-sha-256
host    all             all             ***********/16          scram-sha-256
host    all             all             10.0.0.0/8              scram-sha-256

# Blast-Radius network connections
host    blastradius     blastradius     blastradius_network     scram-sha-256
host    blastradius_test blastradius_test blastradius_network   scram-sha-256

# Replication connections
host    replication     all             **********/12           scram-sha-256
