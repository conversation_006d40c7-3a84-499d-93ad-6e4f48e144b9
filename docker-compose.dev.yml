# Development override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: '3.8'

services:
  # Backend API with development settings
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    volumes:
      - ./backend:/app
      - /app/venv  # Prevent overwriting venv
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - RELOAD=true
      - CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
    ports:
      - "8000:8000"
      - "5678:5678"  # Debug port
    command: >
      sh -c "
        echo '🚀 Starting Blast-Radius API in development mode...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
      "
    depends_on:
      - postgresql
      - redis
      - neo4j
    networks:
      - blast-radius-dev

  # Frontend with hot reload
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    volumes:
      - ./frontend:/app
      - /app/node_modules  # Prevent overwriting node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ENV=development
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    command: >
      sh -c "
        echo '🎨 Starting Blast-Radius Frontend in development mode...' &&
        npm start
      "
    depends_on:
      - backend
    networks:
      - blast-radius-dev

  # PostgreSQL with development settings
  postgresql:
    environment:
      - POSTGRES_DB=blast_radius_dev
      - POSTGRES_USER=blast_radius_dev
      - POSTGRES_PASSWORD=dev_password_123
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/scripts/dev-init.sql:/docker-entrypoint-initdb.d/01-dev-init.sql
    ports:
      - "5432:5432"
    networks:
      - blast-radius-dev

  # Redis with development settings
  redis:
    command: redis-server --appendonly yes --requirepass dev_redis_123
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - blast-radius-dev

  # Neo4j with development settings
  neo4j:
    environment:
      - NEO4J_AUTH=neo4j/dev_neo4j_123
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=1G
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_whitelist=apoc.*
    volumes:
      - neo4j_dev_data:/data
      - neo4j_dev_logs:/logs
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    networks:
      - blast-radius-dev

  # Development tools container
  dev-tools:
    image: python:3.11-slim
    volumes:
      - ./backend:/app
      - ./scripts:/scripts
    working_dir: /app
    environment:
      - PYTHONPATH=/app
    command: >
      sh -c "
        echo '🛠️ Development tools container ready...' &&
        pip install -r requirements-dev.txt &&
        tail -f /dev/null
      "
    networks:
      - blast-radius-dev

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - blast-radius-dev

  # Adminer for database management
  adminer:
    image: adminer:latest
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgresql
    depends_on:
      - postgresql
    networks:
      - blast-radius-dev

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379:0:dev_redis_123
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - blast-radius-dev

  # Documentation server
  docs:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    volumes:
      - ./backend:/app
    working_dir: /app
    command: >
      sh -c "
        echo '📚 Starting documentation server...' &&
        pip install sphinx sphinx-rtd-theme &&
        cd docs &&
        make html &&
        python -m http.server 8082 --directory _build/html
      "
    ports:
      - "8082:8082"
    networks:
      - blast-radius-dev

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  neo4j_dev_data:
    driver: local
  neo4j_dev_logs:
    driver: local

networks:
  blast-radius-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
