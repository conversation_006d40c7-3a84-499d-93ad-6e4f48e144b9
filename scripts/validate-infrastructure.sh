#!/bin/bash
# Blast-Radius Security Tool - Infrastructure Validation Script
# Comprehensive validation of infrastructure configurations and deployments

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
ENVIRONMENT=${ENVIRONMENT:-"local"}
VALIDATE_TERRAFORM=${VALIDATE_TERRAFORM:-"true"}
VALIDATE_KUBERNETES=${VALIDATE_KUBERNETES:-"true"}
VALIDATE_DOCKER=${VALIDATE_DOCKER:-"true"}
VALIDATE_MONITORING=${VALIDATE_MONITORING:-"true"}

# Validation results
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Help function
show_help() {
    cat << EOF
Infrastructure Validation Script

Usage: $0 [OPTIONS] [COMPONENT]

COMPONENTS:
    all                  Validate all components (default)
    terraform            Validate Terraform configurations
    kubernetes           Validate Kubernetes manifests
    docker               Validate Docker configurations
    monitoring           Validate monitoring configurations
    security             Validate security configurations

OPTIONS:
    -h, --help          Show this help
    -e, --env ENV       Environment (local, dev, staging, prod)
    --skip-terraform    Skip Terraform validation
    --skip-kubernetes   Skip Kubernetes validation
    --skip-docker       Skip Docker validation
    --skip-monitoring   Skip monitoring validation

EXAMPLES:
    $0                  # Validate all components
    $0 terraform        # Validate Terraform only
    $0 --env prod       # Validate for production environment

EOF
}

# Parse arguments
COMPONENT="all"
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help) show_help; exit 0 ;;
        -e|--env) ENVIRONMENT="$2"; shift 2 ;;
        --skip-terraform) VALIDATE_TERRAFORM="false"; shift ;;
        --skip-kubernetes) VALIDATE_KUBERNETES="false"; shift ;;
        --skip-docker) VALIDATE_DOCKER="false"; shift ;;
        --skip-monitoring) VALIDATE_MONITORING="false"; shift ;;
        all|terraform|kubernetes|docker|monitoring|security) COMPONENT="$1"; shift ;;
        *) log_error "Unknown option: $1"; show_help; exit 1 ;;
    esac
done

# Increment error counter
add_error() {
    ((VALIDATION_ERRORS++))
    log_error "$1"
}

# Increment warning counter
add_warning() {
    ((VALIDATION_WARNINGS++))
    log_warning "$1"
}

# Check if command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        add_error "Required command not found: $1"
        return 1
    fi
    return 0
}

# Validate Terraform configurations
validate_terraform() {
    log_info "Validating Terraform configurations"
    
    if [[ "$VALIDATE_TERRAFORM" != "true" ]]; then
        log_info "Skipping Terraform validation"
        return 0
    fi
    
    if ! check_command "terraform"; then
        return 1
    fi
    
    local terraform_dir="${PROJECT_ROOT}/infrastructure/terraform"
    
    if [[ ! -d "$terraform_dir" ]]; then
        add_warning "Terraform directory not found: $terraform_dir"
        return 0
    fi
    
    # Check for Terraform files
    if ! find "$terraform_dir" -name "*.tf" | grep -q .; then
        add_warning "No Terraform files found in $terraform_dir"
        return 0
    fi
    
    # Validate format
    log_info "Checking Terraform format"
    if ! terraform fmt -check -recursive "$terraform_dir" &> /dev/null; then
        add_error "Terraform files are not properly formatted"
    else
        log_success "Terraform format validation passed"
    fi
    
    # Validate syntax for each module
    for module_dir in "$terraform_dir"/modules/*/; do
        if [[ -d "$module_dir" ]]; then
            local module_name=$(basename "$module_dir")
            log_info "Validating Terraform module: $module_name"
            
            cd "$module_dir"
            if terraform init -backend=false &> /dev/null; then
                if terraform validate &> /dev/null; then
                    log_success "Module $module_name validation passed"
                else
                    add_error "Module $module_name validation failed"
                fi
            else
                add_error "Module $module_name initialization failed"
            fi
        fi
    done
    
    # Validate environment configurations
    for env_dir in "$terraform_dir"/environments/*/; do
        if [[ -d "$env_dir" ]]; then
            local env_name=$(basename "$env_dir")
            log_info "Validating Terraform environment: $env_name"
            
            cd "$env_dir"
            if [[ -f "main.tf" ]]; then
                if terraform init -backend=false &> /dev/null; then
                    if terraform validate &> /dev/null; then
                        log_success "Environment $env_name validation passed"
                    else
                        add_error "Environment $env_name validation failed"
                    fi
                else
                    add_error "Environment $env_name initialization failed"
                fi
            else
                add_warning "No main.tf found in environment $env_name"
            fi
        fi
    done
    
    cd "$PROJECT_ROOT"
    log_success "Terraform validation completed"
}

# Validate Kubernetes configurations
validate_kubernetes() {
    log_info "Validating Kubernetes configurations"
    
    if [[ "$VALIDATE_KUBERNETES" != "true" ]]; then
        log_info "Skipping Kubernetes validation"
        return 0
    fi
    
    if ! check_command "kubectl"; then
        return 1
    fi
    
    local k8s_dir="${PROJECT_ROOT}/infrastructure/kubernetes"
    
    if [[ ! -d "$k8s_dir" ]]; then
        add_warning "Kubernetes directory not found: $k8s_dir"
        return 0
    fi
    
    # Validate YAML syntax
    log_info "Validating Kubernetes YAML syntax"
    find "$k8s_dir" -name "*.yaml" -o -name "*.yml" | while read -r file; do
        if ! kubectl apply --dry-run=client -f "$file" &> /dev/null; then
            add_error "Invalid Kubernetes manifest: $file"
        fi
    done
    
    # Check for required manifests
    local required_manifests=("namespace.yaml" "deployment.yaml" "service.yaml")
    for manifest in "${required_manifests[@]}"; do
        if ! find "$k8s_dir" -name "$manifest" | grep -q .; then
            add_warning "Required manifest not found: $manifest"
        fi
    done
    
    log_success "Kubernetes validation completed"
}

# Validate Docker configurations
validate_docker() {
    log_info "Validating Docker configurations"
    
    if [[ "$VALIDATE_DOCKER" != "true" ]]; then
        log_info "Skipping Docker validation"
        return 0
    fi
    
    if ! check_command "docker"; then
        return 1
    fi
    
    if ! check_command "docker-compose"; then
        return 1
    fi
    
    # Validate Docker Compose files
    local compose_files=("docker-compose.yml" "docker-compose.local.yml")
    for compose_file in "${compose_files[@]}"; do
        if [[ -f "${PROJECT_ROOT}/$compose_file" ]]; then
            log_info "Validating Docker Compose file: $compose_file"
            if docker-compose -f "${PROJECT_ROOT}/$compose_file" config &> /dev/null; then
                log_success "Docker Compose file $compose_file is valid"
            else
                add_error "Invalid Docker Compose file: $compose_file"
            fi
        fi
    done
    
    # Validate Dockerfiles
    find "$PROJECT_ROOT" -name "Dockerfile" | while read -r dockerfile; do
        local dir=$(dirname "$dockerfile")
        local service=$(basename "$dir")
        log_info "Validating Dockerfile for $service"
        
        # Check Dockerfile syntax (basic validation)
        if [[ -s "$dockerfile" ]]; then
            if grep -q "FROM" "$dockerfile"; then
                log_success "Dockerfile for $service has valid structure"
            else
                add_error "Dockerfile for $service missing FROM instruction"
            fi
        else
            add_error "Empty Dockerfile found: $dockerfile"
        fi
    done
    
    log_success "Docker validation completed"
}

# Validate monitoring configurations
validate_monitoring() {
    log_info "Validating monitoring configurations"
    
    if [[ "$VALIDATE_MONITORING" != "true" ]]; then
        log_info "Skipping monitoring validation"
        return 0
    fi
    
    local monitoring_dir="${PROJECT_ROOT}/monitoring"
    
    if [[ ! -d "$monitoring_dir" ]]; then
        add_warning "Monitoring directory not found: $monitoring_dir"
        return 0
    fi
    
    # Validate Prometheus configuration
    local prometheus_config="${monitoring_dir}/prometheus/local.yml"
    if [[ -f "$prometheus_config" ]]; then
        log_info "Validating Prometheus configuration"
        # Basic YAML syntax check
        if python3 -c "import yaml; yaml.safe_load(open('$prometheus_config'))" 2> /dev/null; then
            log_success "Prometheus configuration is valid YAML"
        else
            add_error "Invalid Prometheus configuration YAML"
        fi
    else
        add_warning "Prometheus configuration not found"
    fi
    
    # Validate Grafana configurations
    local grafana_dir="${monitoring_dir}/grafana"
    if [[ -d "$grafana_dir" ]]; then
        log_info "Validating Grafana configurations"
        
        # Check datasources
        if [[ -f "${grafana_dir}/datasources/prometheus.yml" ]]; then
            log_success "Grafana datasources configuration found"
        else
            add_warning "Grafana datasources configuration not found"
        fi
        
        # Check dashboards
        if find "${grafana_dir}/dashboards" -name "*.json" | grep -q .; then
            log_success "Grafana dashboards found"
        else
            add_warning "No Grafana dashboards found"
        fi
    fi
    
    # Validate Alertmanager configuration
    local alertmanager_config="${monitoring_dir}/alertmanager/local.yml"
    if [[ -f "$alertmanager_config" ]]; then
        log_info "Validating Alertmanager configuration"
        if python3 -c "import yaml; yaml.safe_load(open('$alertmanager_config'))" 2> /dev/null; then
            log_success "Alertmanager configuration is valid YAML"
        else
            add_error "Invalid Alertmanager configuration YAML"
        fi
    else
        add_warning "Alertmanager configuration not found"
    fi
    
    log_success "Monitoring validation completed"
}

# Validate security configurations
validate_security() {
    log_info "Validating security configurations"
    
    # Check for security-related files
    local security_files=(
        "backend/requirements.txt"
        "frontend/package.json"
        ".gitignore"
        "backend/.env.example"
    )
    
    for file in "${security_files[@]}"; do
        if [[ -f "${PROJECT_ROOT}/$file" ]]; then
            log_success "Security file found: $file"
        else
            add_warning "Security file not found: $file"
        fi
    done
    
    # Check for sensitive data in git
    log_info "Checking for sensitive data patterns"
    if git -C "$PROJECT_ROOT" log --all --full-history -- "*password*" "*secret*" "*key*" | grep -q .; then
        add_warning "Potential sensitive data found in git history"
    fi
    
    # Check Docker security
    find "$PROJECT_ROOT" -name "Dockerfile" | while read -r dockerfile; do
        if grep -q "USER root" "$dockerfile"; then
            add_warning "Dockerfile runs as root: $dockerfile"
        fi
    done
    
    log_success "Security validation completed"
}

# Generate validation report
generate_report() {
    log_info "Generating validation report"
    
    echo ""
    echo "=================================="
    echo "Infrastructure Validation Report"
    echo "=================================="
    echo "Environment: $ENVIRONMENT"
    echo "Timestamp: $(date)"
    echo ""
    
    if [[ $VALIDATION_ERRORS -eq 0 && $VALIDATION_WARNINGS -eq 0 ]]; then
        echo -e "${GREEN}✅ All validations passed successfully!${NC}"
    else
        echo -e "${YELLOW}⚠️  Validation Summary:${NC}"
        echo -e "   Errors: ${RED}$VALIDATION_ERRORS${NC}"
        echo -e "   Warnings: ${YELLOW}$VALIDATION_WARNINGS${NC}"
    fi
    
    echo ""
    
    if [[ $VALIDATION_ERRORS -gt 0 ]]; then
        echo -e "${RED}❌ Validation failed with $VALIDATION_ERRORS error(s)${NC}"
        return 1
    elif [[ $VALIDATION_WARNINGS -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Validation completed with $VALIDATION_WARNINGS warning(s)${NC}"
        return 0
    else
        echo -e "${GREEN}✅ All validations passed!${NC}"
        return 0
    fi
}

# Main execution
main() {
    log_info "Starting infrastructure validation for environment: $ENVIRONMENT"
    
    case $COMPONENT in
        all)
            validate_terraform
            validate_kubernetes
            validate_docker
            validate_monitoring
            validate_security
            ;;
        terraform)
            validate_terraform
            ;;
        kubernetes)
            validate_kubernetes
            ;;
        docker)
            validate_docker
            ;;
        monitoring)
            validate_monitoring
            ;;
        security)
            validate_security
            ;;
        *)
            log_error "Unknown component: $COMPONENT"
            exit 1
            ;;
    esac
    
    generate_report
}

# Run main function
main
