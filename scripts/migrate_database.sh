#!/bin/bash
# Database Migration Script for Blast-Radius Security Tool
# This script helps manage database migrations safely

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"

echo "🗄️  Blast-Radius Database Migration Tool"
echo "========================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check database connection
check_database_connection() {
    echo "🔍 Checking database connection..."
    
    cd "$BACKEND_DIR"
    
    # Check if we can connect to the database
    python3 -c "
import os
import sys
from sqlalchemy import create_engine, text
from app.core.config import settings

try:
    engine = create_engine(settings.DATABASE_URL)
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
    sys.exit(1)
"
}

# Function to backup database
backup_database() {
    echo "💾 Creating database backup..."
    
    # Extract database info from DATABASE_URL
    DB_URL=$(python3 -c "from app.core.config import settings; print(settings.DATABASE_URL)")
    
    # Parse DATABASE_URL to extract components
    DB_HOST=$(echo "$DB_URL" | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo "$DB_URL" | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    DB_NAME=$(echo "$DB_URL" | sed -n 's/.*\/\([^?]*\).*/\1/p')
    DB_USER=$(echo "$DB_URL" | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
    
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    echo "📋 Database details:"
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  Database: $DB_NAME"
    echo "  User: $DB_USER"
    
    if command_exists pg_dump; then
        echo "🔄 Creating PostgreSQL backup..."
        PGPASSWORD="$DB_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"
        echo "✅ Backup created: $BACKUP_FILE"
    else
        echo "⚠️  pg_dump not found. Please create a manual backup before proceeding."
        read -p "Continue without backup? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Function to show current migration status
show_migration_status() {
    echo "📊 Current migration status:"
    
    cd "$BACKEND_DIR"
    
    python3 -c "
import sys
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.environment import EnvironmentContext
from sqlalchemy import create_engine
from app.core.config import settings

try:
    alembic_cfg = Config('alembic.ini')
    script = ScriptDirectory.from_config(alembic_cfg)
    
    # Get current revision
    engine = create_engine(settings.DATABASE_URL)
    with engine.connect() as connection:
        context = EnvironmentContext(alembic_cfg, script)
        context.configure(connection=connection)
        current_rev = context.get_current_revision()
    
    # Get head revision
    head_rev = script.get_current_head()
    
    print(f'Current revision: {current_rev or \"None\"}')
    print(f'Head revision: {head_rev}')
    
    if current_rev == head_rev:
        print('✅ Database is up to date')
    else:
        print('⚠️  Database needs migration')
        
    # Show pending migrations
    revisions = list(script.walk_revisions(head_rev, current_rev))
    if len(revisions) > 1:
        print(f'📋 Pending migrations ({len(revisions)-1}):')
        for rev in reversed(revisions[1:]):
            print(f'  - {rev.revision}: {rev.doc}')
    
except Exception as e:
    print(f'❌ Error checking migration status: {e}')
    sys.exit(1)
"
}

# Function to run migrations
run_migrations() {
    echo "🚀 Running database migrations..."
    
    cd "$BACKEND_DIR"
    
    # Run alembic upgrade
    python3 -c "
from alembic.config import Config
from alembic import command
import sys

try:
    alembic_cfg = Config('alembic.ini')
    command.upgrade(alembic_cfg, 'head')
    print('✅ Migrations completed successfully')
except Exception as e:
    print(f'❌ Migration failed: {e}')
    sys.exit(1)
"
}

# Function to rollback migrations
rollback_migrations() {
    echo "⏪ Rolling back migrations..."
    
    if [ -z "$1" ]; then
        echo "❌ Please specify the target revision"
        echo "Usage: $0 rollback <revision>"
        exit 1
    fi
    
    TARGET_REVISION="$1"
    
    cd "$BACKEND_DIR"
    
    echo "⚠️  WARNING: This will rollback the database to revision: $TARGET_REVISION"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Rollback cancelled"
        exit 1
    fi
    
    python3 -c "
from alembic.config import Config
from alembic import command
import sys

try:
    alembic_cfg = Config('alembic.ini')
    command.downgrade(alembic_cfg, '$TARGET_REVISION')
    print('✅ Rollback completed successfully')
except Exception as e:
    print(f'❌ Rollback failed: {e}')
    sys.exit(1)
"
}

# Function to generate new migration
generate_migration() {
    echo "📝 Generating new migration..."
    
    if [ -z "$1" ]; then
        echo "❌ Please specify a migration message"
        echo "Usage: $0 generate \"migration message\""
        exit 1
    fi
    
    MESSAGE="$1"
    
    cd "$BACKEND_DIR"
    
    python3 -c "
from alembic.config import Config
from alembic import command
import sys

try:
    alembic_cfg = Config('alembic.ini')
    command.revision(alembic_cfg, message='$MESSAGE', autogenerate=True)
    print('✅ Migration generated successfully')
except Exception as e:
    print(f'❌ Migration generation failed: {e}')
    sys.exit(1)
"
}

# Function to validate migrations
validate_migrations() {
    echo "✅ Validating migrations..."
    
    cd "$BACKEND_DIR"
    
    python3 -c "
from alembic.config import Config
from alembic.script import ScriptDirectory
from alembic.runtime.environment import EnvironmentContext
from sqlalchemy import create_engine
from app.core.config import settings
import sys

try:
    alembic_cfg = Config('alembic.ini')
    script = ScriptDirectory.from_config(alembic_cfg)
    
    # Check for duplicate revision numbers
    revisions = list(script.walk_revisions())
    revision_ids = [rev.revision for rev in revisions]
    
    if len(revision_ids) != len(set(revision_ids)):
        print('❌ Duplicate revision IDs found')
        sys.exit(1)
    
    # Check for broken dependencies
    for revision in revisions:
        if revision.down_revision:
            if revision.down_revision not in revision_ids:
                print(f'❌ Broken dependency: {revision.revision} depends on missing {revision.down_revision}')
                sys.exit(1)
    
    print('✅ All migrations are valid')
    
except Exception as e:
    print(f'❌ Migration validation failed: {e}')
    sys.exit(1)
"
}

# Function to show migration history
show_history() {
    echo "📚 Migration history:"
    
    cd "$BACKEND_DIR"
    
    python3 -c "
from alembic.config import Config
from alembic import command

try:
    alembic_cfg = Config('alembic.ini')
    command.history(alembic_cfg, verbose=True)
except Exception as e:
    print(f'❌ Error showing history: {e}')
"
}

# Function to initialize database
init_database() {
    echo "🏗️  Initializing database..."
    
    cd "$BACKEND_DIR"
    
    # Create database tables
    python3 -c "
from app.db.base import Base
from app.core.config import settings
from sqlalchemy import create_engine

try:
    engine = create_engine(settings.DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    print('✅ Database tables created')
except Exception as e:
    print(f'❌ Database initialization failed: {e}')
    import sys
    sys.exit(1)
"
    
    # Stamp with current migration
    python3 -c "
from alembic.config import Config
from alembic import command

try:
    alembic_cfg = Config('alembic.ini')
    command.stamp(alembic_cfg, 'head')
    print('✅ Database stamped with current migration')
except Exception as e:
    print(f'❌ Database stamping failed: {e}')
"
}

# Main script logic
case "${1:-help}" in
    "status")
        check_database_connection
        show_migration_status
        ;;
    "migrate")
        check_database_connection
        backup_database
        run_migrations
        show_migration_status
        ;;
    "rollback")
        check_database_connection
        backup_database
        rollback_migrations "$2"
        show_migration_status
        ;;
    "generate")
        generate_migration "$2"
        ;;
    "validate")
        validate_migrations
        ;;
    "history")
        show_history
        ;;
    "init")
        check_database_connection
        init_database
        ;;
    "backup")
        backup_database
        ;;
    "help"|*)
        echo "Usage: $0 {status|migrate|rollback|generate|validate|history|init|backup}"
        echo ""
        echo "Commands:"
        echo "  status              Show current migration status"
        echo "  migrate             Run pending migrations (with backup)"
        echo "  rollback <revision> Rollback to specific revision (with backup)"
        echo "  generate <message>  Generate new migration"
        echo "  validate            Validate migration files"
        echo "  history             Show migration history"
        echo "  init                Initialize database and stamp with current migration"
        echo "  backup              Create database backup"
        echo ""
        echo "Examples:"
        echo "  $0 status                           # Check migration status"
        echo "  $0 migrate                          # Run all pending migrations"
        echo "  $0 rollback 001_robust_assets       # Rollback to specific revision"
        echo "  $0 generate \"Add new feature\"       # Generate new migration"
        ;;
esac

echo "🎉 Database migration script completed"
