#!/bin/bash
# Security Update Script for Blast-Radius Security Tool
# This script helps identify and update vulnerable dependencies

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"

echo "🛡️  Blast-Radius Security Update Tool"
echo "======================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install security tools
install_security_tools() {
    echo "🔧 Installing security scanning tools..."
    
    # Install Python security tools
    pip install --upgrade safety bandit pip-audit
    
    # Install Node.js security tools if npm is available
    if command_exists npm; then
        npm install -g audit-ci
    fi
    
    echo "✅ Security tools installed"
}

# Function to check for vulnerabilities
check_vulnerabilities() {
    echo "🔍 Checking for known vulnerabilities..."
    
    cd "$BACKEND_DIR"
    
    # Check main requirements
    if [ -f "requirements.txt" ]; then
        echo "📋 Checking requirements.txt..."
        safety check --requirements requirements.txt || echo "⚠️  Vulnerabilities found in requirements.txt"
    fi
    
    # Check discovery requirements
    if [ -f "requirements-discovery.txt" ]; then
        echo "📋 Checking requirements-discovery.txt..."
        safety check --requirements requirements-discovery.txt || echo "⚠️  Vulnerabilities found in requirements-discovery.txt"
    fi
    
    # Run pip-audit
    echo "🔍 Running pip-audit..."
    pip-audit || echo "⚠️  Issues found by pip-audit"
}

# Function to check outdated packages
check_outdated() {
    echo "📦 Checking for outdated packages..."
    
    cd "$BACKEND_DIR"
    
    echo "Current outdated packages:"
    pip list --outdated || echo "No outdated packages found"
}

# Function to run code security scan
run_code_scan() {
    echo "🔍 Running code security scan with Bandit..."
    
    cd "$BACKEND_DIR"
    
    # Run bandit on the codebase
    bandit -r app/ -ll -f json -o bandit-report.json || echo "⚠️  Security issues found by Bandit"
    
    if [ -f "bandit-report.json" ]; then
        echo "📄 Bandit report saved to bandit-report.json"
        
        # Show summary
        echo "📊 Bandit Summary:"
        python3 -c "
import json
try:
    with open('bandit-report.json', 'r') as f:
        data = json.load(f)
    print(f'  High severity issues: {len([r for r in data.get(\"results\", []) if r.get(\"issue_severity\") == \"HIGH\"])}')
    print(f'  Medium severity issues: {len([r for r in data.get(\"results\", []) if r.get(\"issue_severity\") == \"MEDIUM\"])}')
    print(f'  Low severity issues: {len([r for r in data.get(\"results\", []) if r.get(\"issue_severity\") == \"LOW\"])}')
except Exception as e:
    print(f'Error reading bandit report: {e}')
"
    fi
}

# Function to update packages
update_packages() {
    echo "🔄 Updating packages to latest secure versions..."
    
    cd "$BACKEND_DIR"
    
    # Backup current requirements
    cp requirements.txt requirements.txt.backup
    if [ -f "requirements-discovery.txt" ]; then
        cp requirements-discovery.txt requirements-discovery.txt.backup
    fi
    
    echo "💾 Backed up current requirements files"
    
    # Update pip itself first
    pip install --upgrade pip setuptools wheel
    
    # Install updated requirements
    pip install --upgrade -r requirements.txt
    
    if [ -f "requirements-discovery.txt" ]; then
        pip install --upgrade -r requirements-discovery.txt
    fi
    
    echo "✅ Packages updated"
}

# Function to generate security report
generate_report() {
    echo "📄 Generating security report..."
    
    cd "$BACKEND_DIR"
    
    REPORT_FILE="security-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$REPORT_FILE" << EOF
# Security Report for Blast-Radius
Generated: $(date)

## System Information
- Python Version: $(python3 --version)
- Pip Version: $(pip --version)
- Project Root: $PROJECT_ROOT

## Package Versions
\`\`\`
$(pip list)
\`\`\`

## Outdated Packages
\`\`\`
$(pip list --outdated || echo "No outdated packages")
\`\`\`

## Security Scan Results

### Safety Check
\`\`\`
$(safety check --requirements requirements.txt 2>&1 || echo "Vulnerabilities found")
\`\`\`

### Pip Audit
\`\`\`
$(pip-audit 2>&1 || echo "Issues found")
\`\`\`

### Bandit Code Scan
$(if [ -f "bandit-report.json" ]; then echo "See bandit-report.json for detailed results"; else echo "Bandit scan not run"; fi)

## Recommendations
1. Update all outdated packages, especially security-critical ones
2. Review and fix any high-severity Bandit issues
3. Monitor for new vulnerabilities regularly
4. Consider using dependabot for automated updates

EOF

    echo "📄 Security report saved to $REPORT_FILE"
}

# Main script logic
case "${1:-help}" in
    "install-tools")
        install_security_tools
        ;;
    "check")
        check_vulnerabilities
        check_outdated
        ;;
    "scan")
        check_vulnerabilities
        check_outdated
        run_code_scan
        ;;
    "update")
        update_packages
        ;;
    "report")
        check_vulnerabilities
        check_outdated
        run_code_scan
        generate_report
        ;;
    "full")
        install_security_tools
        check_vulnerabilities
        check_outdated
        run_code_scan
        generate_report
        ;;
    "help"|*)
        echo "Usage: $0 {install-tools|check|scan|update|report|full}"
        echo ""
        echo "Commands:"
        echo "  install-tools  Install security scanning tools"
        echo "  check         Check for vulnerabilities and outdated packages"
        echo "  scan          Run comprehensive security scan"
        echo "  update        Update packages to latest versions"
        echo "  report        Generate comprehensive security report"
        echo "  full          Run all security checks and generate report"
        echo ""
        echo "Examples:"
        echo "  $0 install-tools  # Install security tools"
        echo "  $0 check         # Quick vulnerability check"
        echo "  $0 full          # Complete security audit"
        ;;
esac

echo "🎉 Security update script completed"
