#!/bin/bash
# Blast-Radius Security Tool - Monitoring Stack Setup
# Comprehensive monitoring and observability setup script

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
MONITORING_DIR="${PROJECT_ROOT}/monitoring"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
ENVIRONMENT=${ENVIRONMENT:-"local"}
ENABLE_ALERTS=${ENABLE_ALERTS:-"true"}
ENABLE_GRAFANA=${ENABLE_GRAFANA:-"true"}
GRAFANA_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-"admin_local_dev"}

# Help function
show_help() {
    cat << EOF
Blast-Radius Monitoring Setup

Usage: $0 [OPTIONS] [COMMAND]

COMMANDS:
    setup                 Set up complete monitoring stack
    start                 Start monitoring services
    stop                  Stop monitoring services
    status                Show monitoring services status
    dashboards            Import Grafana dashboards
    alerts                Test alert configuration
    clean                 Clean monitoring data

OPTIONS:
    -h, --help           Show this help
    -e, --env ENV        Environment (local, dev, staging)
    --no-alerts          Disable alerting
    --no-grafana         Disable Grafana
    --password PASS      Set Grafana admin password

EXAMPLES:
    $0 setup             # Full monitoring setup
    $0 start             # Start monitoring stack
    $0 dashboards        # Import dashboards only
    $0 --no-alerts setup # Setup without alerting

EOF
}

# Parse arguments
COMMAND="setup"
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help) show_help; exit 0 ;;
        -e|--env) ENVIRONMENT="$2"; shift 2 ;;
        --no-alerts) ENABLE_ALERTS="false"; shift ;;
        --no-grafana) ENABLE_GRAFANA="false"; shift ;;
        --password) GRAFANA_ADMIN_PASSWORD="$2"; shift 2 ;;
        setup|start|stop|status|dashboards|alerts|clean) COMMAND="$1"; shift ;;
        *) log_error "Unknown option: $1"; show_help; exit 1 ;;
    esac
done

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies"
    
    local deps=("docker" "docker-compose" "curl" "jq")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "Required dependency not found: $dep"
            exit 1
        fi
    done
    
    log_success "All dependencies available"
}

# Setup monitoring directories
setup_directories() {
    log_info "Setting up monitoring directories"
    
    mkdir -p "${MONITORING_DIR}"/{prometheus,grafana/{dashboards,datasources},alertmanager}
    mkdir -p "${PROJECT_ROOT}/data"/{prometheus,grafana,alertmanager}
    
    # Set proper permissions
    chmod 755 "${MONITORING_DIR}"/{prometheus,grafana,alertmanager}
    
    log_success "Monitoring directories created"
}

# Validate configuration files
validate_configs() {
    log_info "Validating monitoring configurations"
    
    # Check Prometheus config
    if [[ -f "${MONITORING_DIR}/prometheus/local.yml" ]]; then
        log_success "Prometheus configuration found"
    else
        log_error "Prometheus configuration missing"
        exit 1
    fi
    
    # Check Grafana datasources
    if [[ -f "${MONITORING_DIR}/grafana/datasources/prometheus.yml" ]]; then
        log_success "Grafana datasources configuration found"
    else
        log_warning "Grafana datasources configuration missing"
    fi
    
    # Check alerting configuration
    if [[ "$ENABLE_ALERTS" == "true" ]]; then
        if [[ -f "${MONITORING_DIR}/alertmanager/local.yml" ]]; then
            log_success "Alertmanager configuration found"
        else
            log_warning "Alertmanager configuration missing"
        fi
    fi
    
    log_success "Configuration validation completed"
}

# Start monitoring services
start_monitoring() {
    log_info "Starting monitoring services"
    
    cd "${PROJECT_ROOT}"
    
    # Start core application first
    log_info "Starting core application services"
    docker-compose -f docker-compose.local.yml up -d postgresql redis neo4j backend
    
    # Wait for core services
    log_info "Waiting for core services to be ready"
    sleep 30
    
    # Start monitoring stack
    log_info "Starting monitoring stack"
    docker-compose -f docker-compose.local.yml --profile monitoring up -d
    
    # Wait for monitoring services
    log_info "Waiting for monitoring services to be ready"
    sleep 20
    
    # Verify services
    verify_services
    
    log_success "Monitoring services started"
}

# Stop monitoring services
stop_monitoring() {
    log_info "Stopping monitoring services"
    
    cd "${PROJECT_ROOT}"
    docker-compose -f docker-compose.local.yml --profile monitoring down
    
    log_success "Monitoring services stopped"
}

# Check service status
check_status() {
    log_info "Checking monitoring services status"
    
    cd "${PROJECT_ROOT}"
    
    # Check Docker services
    echo -e "\n${BLUE}Docker Services:${NC}"
    docker-compose -f docker-compose.local.yml --profile monitoring ps
    
    # Check service health
    echo -e "\n${BLUE}Service Health:${NC}"
    
    # Prometheus
    if curl -f http://localhost:9090/-/healthy &> /dev/null; then
        log_success "Prometheus: Healthy"
    else
        log_error "Prometheus: Unhealthy"
    fi
    
    # Grafana
    if [[ "$ENABLE_GRAFANA" == "true" ]]; then
        if curl -f http://localhost:3001/api/health &> /dev/null; then
            log_success "Grafana: Healthy"
        else
            log_error "Grafana: Unhealthy"
        fi
    fi
    
    # Alertmanager
    if [[ "$ENABLE_ALERTS" == "true" ]]; then
        if curl -f http://localhost:9093/-/healthy &> /dev/null; then
            log_success "Alertmanager: Healthy"
        else
            log_error "Alertmanager: Unhealthy"
        fi
    fi
    
    # Application
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "Blast-Radius Backend: Healthy"
    else
        log_error "Blast-Radius Backend: Unhealthy"
    fi
}

# Verify services are running
verify_services() {
    log_info "Verifying monitoring services"
    
    local max_attempts=30
    local attempt=1
    
    # Check Prometheus
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:9090/-/healthy &> /dev/null; then
            log_success "Prometheus is ready"
            break
        fi
        log_info "Waiting for Prometheus... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "Prometheus failed to start"
        return 1
    fi
    
    # Check Grafana
    if [[ "$ENABLE_GRAFANA" == "true" ]]; then
        attempt=1
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f http://localhost:3001/api/health &> /dev/null; then
                log_success "Grafana is ready"
                break
            fi
            log_info "Waiting for Grafana... (attempt $attempt/$max_attempts)"
            sleep 10
            ((attempt++))
        done
        
        if [[ $attempt -gt $max_attempts ]]; then
            log_error "Grafana failed to start"
            return 1
        fi
    fi
    
    log_success "All monitoring services verified"
}

# Import Grafana dashboards
import_dashboards() {
    log_info "Importing Grafana dashboards"
    
    if [[ "$ENABLE_GRAFANA" != "true" ]]; then
        log_warning "Grafana disabled, skipping dashboard import"
        return 0
    fi
    
    # Wait for Grafana to be ready
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3001/api/health &> /dev/null; then
            break
        fi
        log_info "Waiting for Grafana... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "Grafana not available for dashboard import"
        return 1
    fi
    
    # Import dashboards
    local dashboard_dir="${MONITORING_DIR}/grafana/dashboards"
    if [[ -d "$dashboard_dir" ]]; then
        for dashboard in "$dashboard_dir"/*.json; do
            if [[ -f "$dashboard" ]]; then
                log_info "Importing dashboard: $(basename "$dashboard")"
                curl -X POST \
                    -H "Content-Type: application/json" \
                    -d @"$dashboard" \
                    "http://admin:${GRAFANA_ADMIN_PASSWORD}@localhost:3001/api/dashboards/db" \
                    &> /dev/null || log_warning "Failed to import $(basename "$dashboard")"
            fi
        done
    fi
    
    log_success "Dashboard import completed"
}

# Test alert configuration
test_alerts() {
    log_info "Testing alert configuration"
    
    if [[ "$ENABLE_ALERTS" != "true" ]]; then
        log_warning "Alerting disabled, skipping alert tests"
        return 0
    fi
    
    # Check Alertmanager
    if ! curl -f http://localhost:9093/-/healthy &> /dev/null; then
        log_error "Alertmanager not available"
        return 1
    fi
    
    # Send test alert
    log_info "Sending test alert"
    curl -X POST http://localhost:9093/api/v1/alerts \
        -H "Content-Type: application/json" \
        -d '[{
            "labels": {
                "alertname": "TestAlert",
                "severity": "warning",
                "service": "blast-radius",
                "instance": "test"
            },
            "annotations": {
                "summary": "Test alert from monitoring setup",
                "description": "This is a test alert to verify alerting configuration"
            },
            "startsAt": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
            "endsAt": "'$(date -u -d '+5 minutes' +%Y-%m-%dT%H:%M:%S.%3NZ)'"
        }]' &> /dev/null
    
    log_success "Test alert sent"
}

# Clean monitoring data
clean_monitoring() {
    log_info "Cleaning monitoring data"
    
    cd "${PROJECT_ROOT}"
    
    # Stop services
    docker-compose -f docker-compose.local.yml --profile monitoring down
    
    # Remove volumes
    docker volume rm blast-radius-prometheus-local blast-radius-grafana-local blast-radius-alertmanager-local 2>/dev/null || true
    
    # Clean data directories
    rm -rf "${PROJECT_ROOT}/data"/{prometheus,grafana,alertmanager}/*
    
    log_success "Monitoring data cleaned"
}

# Display monitoring URLs
show_urls() {
    log_info "Monitoring services are available at:"
    echo ""
    echo -e "${GREEN}Prometheus:${NC}    http://localhost:9090"
    echo -e "${GREEN}Grafana:${NC}       http://localhost:3001 (admin/${GRAFANA_ADMIN_PASSWORD})"
    echo -e "${GREEN}Alertmanager:${NC}  http://localhost:9093"
    echo -e "${GREEN}Application:${NC}   http://localhost:8000"
    echo -e "${GREEN}API Docs:${NC}      http://localhost:8000/docs"
    echo ""
}

# Main execution
main() {
    log_info "Starting monitoring setup for environment: ${ENVIRONMENT}"
    
    case $COMMAND in
        setup)
            check_dependencies
            setup_directories
            validate_configs
            start_monitoring
            import_dashboards
            if [[ "$ENABLE_ALERTS" == "true" ]]; then
                test_alerts
            fi
            show_urls
            ;;
        start)
            start_monitoring
            show_urls
            ;;
        stop)
            stop_monitoring
            ;;
        status)
            check_status
            ;;
        dashboards)
            import_dashboards
            ;;
        alerts)
            test_alerts
            ;;
        clean)
            clean_monitoring
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            exit 1
            ;;
    esac
    
    log_success "Monitoring setup completed successfully"
}

# Run main function
main
