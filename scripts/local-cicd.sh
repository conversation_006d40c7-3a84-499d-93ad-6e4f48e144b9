#!/bin/bash
# Blast-Radius Security Tool - Local CI/CD Pipeline
# Comprehensive local development and deployment pipeline

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BUILD_DIR="${PROJECT_ROOT}/build"
REPORTS_DIR="${PROJECT_ROOT}/reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
ENVIRONMENT=${ENVIRONMENT:-"local"}
SKIP_TESTS=${SKIP_TESTS:-"false"}
SKIP_SECURITY=${SKIP_SECURITY:-"false"}
DEPLOY=${DEPLOY:-"true"}

# Help function
show_help() {
    cat << EOF
Blast-Radius Local CI/CD Pipeline

Usage: $0 [OPTIONS] [COMMAND]

COMMANDS:
    full                   Run complete pipeline (default)
    security              Run security scans only
    test                  Run all tests
    build                 Build containers
    deploy                Deploy locally
    clean                 Clean up artifacts

OPTIONS:
    -h, --help            Show this help
    -e, --env ENV         Environment (local, dev, staging)
    --skip-tests          Skip testing stages
    --skip-security       Skip security scanning
    --no-deploy           Skip deployment

EXAMPLES:
    $0                    # Full pipeline
    $0 security           # Security scan only
    $0 --skip-tests deploy # Deploy without tests
    $0 clean              # Clean artifacts

EOF
}

# Parse arguments
COMMAND="full"
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help) show_help; exit 0 ;;
        -e|--env) ENVIRONMENT="$2"; shift 2 ;;
        --skip-tests) SKIP_TESTS="true"; shift ;;
        --skip-security) SKIP_SECURITY="true"; shift ;;
        --no-deploy) DEPLOY="false"; shift ;;
        full|security|test|build|deploy|clean) COMMAND="$1"; shift ;;
        *) log_error "Unknown option: $1"; show_help; exit 1 ;;
    esac
done

# Setup environment
setup_environment() {
    log_info "Setting up environment for ${ENVIRONMENT}"
    
    mkdir -p "${BUILD_DIR}" "${REPORTS_DIR}"
    
    # Check dependencies
    local deps=("docker" "docker-compose" "python3" "node" "npm")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "Required dependency not found: $dep"
            exit 1
        fi
    done
    
    # Python environment
    cd "${PROJECT_ROOT}/backend"
    if [[ ! -d "venv" ]]; then
        log_info "Creating Python virtual environment"
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install -q -r requirements.txt
    pip install -q -r requirements-test.txt
    
    # Node.js environment
    cd "${PROJECT_ROOT}/frontend"
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing Node.js dependencies"
        npm ci --silent
    fi
    
    log_success "Environment setup completed"
}

# Security scanning
run_security_scan() {
    log_info "Running security scans"
    
    cd "${PROJECT_ROOT}/backend"
    source venv/bin/activate
    
    # Install security tools
    pip install -q bandit safety
    
    # Bandit scan
    log_info "Running Bandit security scan"
    if bandit -r app/ -ll; then
        log_success "Bandit scan passed"
    else
        log_warning "Bandit found security issues"
    fi
    
    # Safety scan
    log_info "Running Safety dependency scan"
    if safety scan; then
        log_success "Safety scan passed"
    else
        log_warning "Safety found vulnerable dependencies"
    fi
    
    # Frontend audit
    log_info "Running frontend security audit"
    cd "${PROJECT_ROOT}/frontend"
    if npm audit --audit-level=moderate; then
        log_success "Frontend audit passed"
    else
        log_warning "Frontend audit found issues"
    fi
    
    log_success "Security scans completed"
}

# Run tests
run_tests() {
    log_info "Running test suite"
    
    # Backend tests
    cd "${PROJECT_ROOT}/backend"
    source venv/bin/activate
    
    log_info "Starting test services"
    docker-compose -f ../docker-compose.yml up -d postgresql redis
    sleep 10
    
    export TESTING=true
    export DATABASE_URL="postgresql://blastradius:blastradius_secure_password_2024@localhost:5432/blastradius"
    export REDIS_URL="redis://localhost:6379/0"
    
    log_info "Running backend tests"
    if python -m pytest tests/ -v --tb=short; then
        log_success "Backend tests passed"
    else
        log_error "Backend tests failed"
        docker-compose -f ../docker-compose.yml down
        exit 1
    fi
    
    docker-compose -f ../docker-compose.yml down
    
    # Frontend tests
    cd "${PROJECT_ROOT}/frontend"
    log_info "Running frontend tests"
    if npm run test:unit; then
        log_success "Frontend tests passed"
    else
        log_error "Frontend tests failed"
        exit 1
    fi
    
    log_success "All tests completed"
}

# Build containers
build_containers() {
    log_info "Building containers"
    
    cd "${PROJECT_ROOT}"
    
    # Build backend
    log_info "Building backend image"
    if docker build -t blast-radius-backend:latest ./backend; then
        log_success "Backend image built"
    else
        log_error "Backend build failed"
        exit 1
    fi
    
    # Build frontend (if Dockerfile exists)
    if [[ -f "frontend/Dockerfile" ]]; then
        log_info "Building frontend image"
        if docker build -t blast-radius-frontend:latest ./frontend; then
            log_success "Frontend image built"
        else
            log_error "Frontend build failed"
            exit 1
        fi
    fi
    
    log_success "Container building completed"
}

# Deploy locally
deploy_local() {
    log_info "Deploying to local environment"
    
    cd "${PROJECT_ROOT}"
    
    # Stop existing deployment
    docker-compose down || true
    
    # Start services
    log_info "Starting services"
    if docker-compose up -d; then
        log_success "Services started"
    else
        log_error "Deployment failed"
        exit 1
    fi
    
    # Wait for services
    log_info "Waiting for services to be ready"
    sleep 30
    
    # Health check
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "Application is healthy"
            break
        fi
        log_info "Waiting for application... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "Application health check failed"
        docker-compose logs backend
        exit 1
    fi
    
    log_success "Local deployment completed"
    log_info "Application: http://localhost:8000"
    log_info "API Docs: http://localhost:8000/docs"
}

# Clean up
clean_artifacts() {
    log_info "Cleaning up artifacts"
    
    cd "${PROJECT_ROOT}"
    
    # Stop containers
    docker-compose down || true
    
    # Remove images
    docker rmi blast-radius-backend:latest blast-radius-frontend:latest 2>/dev/null || true
    
    # Clean build directories
    rm -rf "${BUILD_DIR}" "${REPORTS_DIR}"
    rm -rf backend/venv backend/.pytest_cache backend/htmlcov
    rm -rf frontend/node_modules frontend/dist
    
    # Docker cleanup
    docker system prune -f || true
    
    log_success "Cleanup completed"
}

# Main execution
main() {
    local start_time=$(date +%s)
    
    log_info "Starting Blast-Radius Local CI/CD Pipeline"
    log_info "Command: ${COMMAND}, Environment: ${ENVIRONMENT}"
    
    case $COMMAND in
        full)
            setup_environment
            [[ "$SKIP_SECURITY" != "true" ]] && run_security_scan
            [[ "$SKIP_TESTS" != "true" ]] && run_tests
            build_containers
            [[ "$DEPLOY" == "true" ]] && deploy_local
            ;;
        security)
            setup_environment
            run_security_scan
            ;;
        test)
            setup_environment
            run_tests
            ;;
        build)
            setup_environment
            build_containers
            ;;
        deploy)
            deploy_local
            ;;
        clean)
            clean_artifacts
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            exit 1
            ;;
    esac
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "Pipeline completed in ${duration} seconds"
}

# Trap for cleanup on exit
trap 'log_info "Pipeline interrupted"' INT TERM

# Run main function
main
